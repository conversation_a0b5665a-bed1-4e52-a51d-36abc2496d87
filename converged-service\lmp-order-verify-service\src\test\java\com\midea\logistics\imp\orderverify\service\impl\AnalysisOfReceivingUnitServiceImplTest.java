package com.midea.logistics.imp.orderverify.service.impl;

import com.annto.framework.easyjunit.disable.apollo.EasyJunitDisableApollo;
import com.annto.framework.easyjunit.mariadb.EasyJunitMariadb;
import com.annto.framework.easyjunit.redis.EasyJunitRedis;
import com.annto.framework.easyjunit.rocketmq.EasyJunitRocketMQ;
import com.midea.logistics.cache.manager.MidCustomerControlManager;
import com.midea.logistics.domain.mdm.domain.MidCustomerControl;
import com.midea.logistics.imp.orderverify.LmpOrderVerifyService;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.otp.enums.CommonMdmEnum;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.thread.ThreadLocals;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ClassName: AnalysisOfReceivingUnitServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2024/7/12 11:14
 */
@EasyJunitDisableApollo
@EasyJunitRocketMQ
@EasyJunitMariadb
@EasyJunitRedis
@ActiveProfiles("local")
@TestPropertySource(locations = "classpath:local.properties")
@SpringBootTest(classes = LmpOrderVerifyService.class)
class AnalysisOfReceivingUnitServiceImplTest {

    @Autowired
    private AnalysisOfReceivingUnitServiceImpl analysisOfReceivingUnitServiceImpl;

    @MockBean
    private MidCustomerControlManager midCustomerControlManager;

    @MockBean
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @Test
    public void cwShtOrderAnalysis1() {
        CustomerOrderInfoExt customerOrderInfo = new CustomerOrderInfoExt();
        customerOrderInfo.setCustomerOrderNo("customerOrderInfo");
        customerOrderInfo.setUpperTargeWhCode("SGC50A1110");
        customerOrderInfo.setSiteCode("6735543378");
        customerOrderInfo.setTargetSiteCode(null);
        customerOrderInfo.setTargetCustomerCode(null);

        MidCustomerControl midCustomerControl = new MidCustomerControl();
        midCustomerControl.setAnnCustomerCode("********");
        midCustomerControl.setStatus(Integer.parseInt(CommonMdmEnum.ENABLE.getValue()));
        Mockito.when(midCustomerControlManager.getMidCustControlCache(Mockito.anyString(),Mockito.anyString())).thenReturn(midCustomerControl);

        Mockito.doNothing().when(lmpOrderFlowHelper).updateCustomerOrderInfo(Mockito.any(), Mockito.anyString());

        analysisOfReceivingUnitServiceImpl.cwShtOrderAnalysis(customerOrderInfo);

        Assertions.assertEquals(customerOrderInfo.getSiteCode(), customerOrderInfo.getTargetSiteCode());
        Assertions.assertEquals(midCustomerControl.getAnnCustomerCode(), customerOrderInfo.getTargetCustomerCode());

        FlowListenerParam flowListenerParam = (FlowListenerParam) ThreadLocals.get("flow");
        Assertions.assertEquals(FlowStatus.SUCCESS, flowListenerParam.getFlowStatus());
        Assertions.assertEquals("获取收货单位成功,收货客户:" + midCustomerControl.getAnnCustomerCode() + "收货平台:" + customerOrderInfo.getSiteCode(), flowListenerParam.getErrorMsg());
    }

    @Test
    public void cwShtOrderAnalysis2() {
        CustomerOrderInfoExt customerOrderInfo = new CustomerOrderInfoExt();
        customerOrderInfo.setCustomerOrderNo("customerOrderInfo");
        customerOrderInfo.setUpperTargeWhCode("SGC50A1110");
        customerOrderInfo.setSiteCode("6735543378");
        customerOrderInfo.setTargetSiteCode(null);
        customerOrderInfo.setTargetCustomerCode(null);

        MidCustomerControl midCustomerControl = new MidCustomerControl();
        midCustomerControl.setAnnCustomerCode("********");
        midCustomerControl.setStatus(Integer.parseInt(CommonMdmEnum.DISABLE.getValue()));
        Mockito.when(midCustomerControlManager.getMidCustControlCache(Mockito.anyString(),Mockito.anyString())).thenReturn(midCustomerControl);

        Mockito.doNothing().when(lmpOrderFlowHelper).updateCustomerOrderInfo(Mockito.any(), Mockito.anyString());

        String errorMsg = "";
        String code = "0";
        try{
            analysisOfReceivingUnitServiceImpl.cwShtOrderAnalysis(customerOrderInfo);
        }catch (Exception e){
            if(e instanceof BusinessException){
                BusinessException bue = (BusinessException)e;
                errorMsg =  bue.getMessage();
                code = bue.getCode();
            }
        }
        Assertions.assertEquals("1",code);
        Assertions.assertEquals("云仓OU订单，解析目标客户失败，" + customerOrderInfo.getUpperTargeWhCode(),errorMsg);
    }

    @Test
    public void cwShtOrderAnalysis3() {
        CustomerOrderInfoExt customerOrderInfo = new CustomerOrderInfoExt();
        customerOrderInfo.setCustomerOrderNo("customerOrderInfo");
        customerOrderInfo.setUpperTargeWhCode("SGC50A1110");
        customerOrderInfo.setSiteCode("6735543378");
        customerOrderInfo.setTargetSiteCode(null);
        customerOrderInfo.setTargetCustomerCode(null);

        MidCustomerControl midCustomerControl = new MidCustomerControl();
        midCustomerControl.setStatus(Integer.parseInt(CommonMdmEnum.ENABLE.getValue()));
        Mockito.when(midCustomerControlManager.getMidCustControlCache(Mockito.anyString(),Mockito.anyString())).thenReturn(midCustomerControl);

        Mockito.doNothing().when(lmpOrderFlowHelper).updateCustomerOrderInfo(Mockito.any(), Mockito.anyString());

        String errorMsg = "";
        String code = "0";
        try{
            analysisOfReceivingUnitServiceImpl.cwShtOrderAnalysis(customerOrderInfo);
        }catch (Exception e){
            if(e instanceof BusinessException){
                BusinessException bue = (BusinessException)e;
                errorMsg =  bue.getMessage();
                code = bue.getCode();
            }
        }
        Assertions.assertEquals("1",code);
        Assertions.assertEquals("云仓OU订单，解析目标客户失败，" + customerOrderInfo.getUpperTargeWhCode(),errorMsg);
    }

    @Test
    public void cwShtOrderAnalysis4() {
        CustomerOrderInfoExt customerOrderInfo = new CustomerOrderInfoExt();
        customerOrderInfo.setCustomerOrderNo("customerOrderInfo");
        customerOrderInfo.setUpperTargeWhCode("SGC50A1110");
        customerOrderInfo.setSiteCode("6735543378");
        customerOrderInfo.setTargetSiteCode(null);
        customerOrderInfo.setTargetCustomerCode(null);

        Mockito.when(midCustomerControlManager.getMidCustControlCache(Mockito.anyString(),Mockito.anyString())).thenReturn(null);

        Mockito.doNothing().when(lmpOrderFlowHelper).updateCustomerOrderInfo(Mockito.any(), Mockito.anyString());

        String errorMsg = "";
        String code = "0";
        try{
            analysisOfReceivingUnitServiceImpl.cwShtOrderAnalysis(customerOrderInfo);
        }catch (Exception e){
            if(e instanceof BusinessException){
                BusinessException bue = (BusinessException)e;
                errorMsg =  bue.getMessage();
                code = bue.getCode();
            }
        }
        Assertions.assertEquals("1",code);
        Assertions.assertEquals("云仓OU订单，解析目标客户失败，" + customerOrderInfo.getUpperTargeWhCode(),errorMsg);
    }

}