package com.midea.logistics.otp.task.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.midea.logistics.otp.task.mapper.common.TaskAddressMapper;
import com.midea.logistics.otp.task.mapper.custom.TaskAddressExtMapper;
import com.midea.logistics.otp.task.service.ITaskAddressService;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.PageResponse;
/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: TaskAddressServiceImpl
* Author: lindq2
* Date: 2019-6-28 10:56:27
* Description:TaskAddressServiceImpl类
*/
@Service
public class TaskAddressServiceImpl implements ITaskAddressService {


    private static final Logger logger = LoggerFactory.getLogger( TaskAddressServiceImpl.class );

    @Autowired
    private TaskAddressMapper taskAddressMapper;
    @Autowired
    private TaskAddressExtMapper taskAddressExtMapper;


    @Override
    @Transactional(readOnly = false)
    public Integer saveTaskAddress(TaskAddress taskAddress) {

        if( null == taskAddress) {
            logger.warn("save taskAddress, but taskAddress is null...");
            return 0;
            }

        return taskAddressMapper.save(taskAddress);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteTaskAddress(TaskAddress taskAddress) {

        if( null == taskAddress || null == taskAddress.getId()) {
            logger.warn("delete taskAddress, but taskAddress is null  or taskAddress id is null...");
            return 0;
        }

        return taskAddressMapper.deleteById(taskAddress);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateTaskAddress(TaskAddress taskAddress) {

        if( null == taskAddress || null == taskAddress.getId()) {
            logger.warn("update taskAddress, but taskAddress is null  or taskAddress id is null...");
            return 0;
        }

        return taskAddressMapper.updateById(taskAddress);
    }

    @Override
    public TaskAddress selectOneTaskAddress(TaskAddress taskAddress) {
        if( taskAddress == null) {
            logger.warn("select taskAddress one, but taskAddress is null ...");
            return null;
        }
        taskAddress = taskAddressMapper.selectOne( taskAddress );
        return taskAddress;
    }

    @Override
    public PageResponse<TaskAddress> selectTaskAddressPage(TaskAddress taskAddress) {
        PageResponse<TaskAddress> pagingData = new PageResponse<>();

        if( null == taskAddress ) {
            logger.warn("select taskAddress page, but taskAddress is null...");
            return pagingData;
        }

        Integer queryCount = taskAddressMapper.selectByIndexCount( taskAddress );
        if( null != queryCount && queryCount <= 0 ) {
            logger.info("select taskAddress page , but count {} == 0 ...",queryCount);
            return pagingData;
        }

        List<TaskAddress> taskAddresss =  selectTaskAddressByIndex( taskAddress );
        pagingData.init(taskAddress.getPageNo(),taskAddress.getPageSize(),queryCount,taskAddresss);
        return pagingData;
    }

    @Override
    public List<TaskAddress> selectTaskAddressByIndex(TaskAddress taskAddress) {
        List<TaskAddress> taskAddresss = new ArrayList<>();
        if( taskAddress == null) {
            logger.warn("select taskAddress by index, but taskAddress is null ...");
            return taskAddresss;
        }

        taskAddresss = taskAddressMapper.selectByIndex( taskAddress );

        return taskAddresss;
    }

    @Override
    public int countTaskAddressByIndex(TaskAddress taskAddress) {
        int count = 0;
        if( taskAddress == null) {
            logger.warn("count taskAddress by index, but taskAddress is null ...");
            return count;
        }

        count = taskAddressMapper.selectByIndexCount( taskAddress );

        return count;
    }

    @Override
    public Integer insertBatch(List<TaskAddress> taskAddresss) {
        if (CollectionUtils.isEmpty(taskAddresss)) {
            logger.warn("insertBatch taskAddresss, but taskAddresss is null ...");
            return 0;
        }
        return taskAddressMapper.insertBatch(taskAddresss);
    }

    @Override
    public Integer batchUpdate(List<TaskAddress> taskAddresss) {
        if (CollectionUtils.isEmpty(taskAddresss)) {
            logger.warn("batchUpdate taskAddresss, but taskAddresss is null ...");
            return 0;
        }
        return taskAddressMapper.batchUpdate(taskAddresss);
    }

    @Override
    public Integer deleteBatch(List<TaskAddress> taskAddresss) {
        if (CollectionUtils.isEmpty(taskAddresss)) {
            logger.warn("deleteBatch taskAddresss, but taskAddresss is null ...");
            return 0;
        }
        return taskAddressMapper.deleteBatch(taskAddresss);
    }

    @Override
    public Integer batchUpdateNull(List<TaskAddress> taskAddresss){
        if (CollectionUtils.isEmpty(taskAddresss)) {
            logger.warn("batchUpdate taskAddresss, but taskAddresss is null ...");
            return 0;
        }
        return taskAddressExtMapper.batchUpdateNull(taskAddresss);
    }

    @Override
    public Integer batchUpdateByTaskNo(List<TaskAddress> taskAddresss) {
        if (CollectionUtils.isEmpty(taskAddresss)) {
            logger.warn("batchUpdate taskAddresss, but taskAddresss is null ...");
            return 0;
        }
        return taskAddressExtMapper.batchUpdateByTaskNo(taskAddresss);
    }

    @Override
    public List<TaskAddress> searchTaskAddressListByTaskNos(List<String> taskNos) {
        if (ToolUtils.isEmpty(taskNos)) {
            return new ArrayList<>();
        }
        return taskAddressMapper.searchTaskAddressListByTaskNos(taskNos);
    }


    @Override
    public List<TaskAddress> searchLastTaskAddressListByOrderNos(List<String> orderNos) {
        if (ToolUtils.isEmpty(orderNos)) {
            return new ArrayList<>();
        }
        return taskAddressExtMapper.searchLastTaskAddressListByOrderNos(orderNos);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Integer batchUpdateCanSetEmptyByTaskNo(List<TaskAddress> taskAddresss) {
        if (CollectionUtils.isEmpty(taskAddresss)) {
            logger.warn("batchUpdate taskAddresss, but taskAddresss is null ...");
            return 0;
        }
        return taskAddressExtMapper.batchUpdateCanSetEmptyByTaskNo(taskAddresss);
    }
}