package com.midea.logistics.otp.task.service.impl;

import com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord;
import com.midea.logistics.otp.task.domain.request.WarehouseCloseRecordReq;
import com.midea.logistics.otp.task.mapper.common.TaskMapper;
import com.midea.logistics.otp.task.mapper.common.WarehouseCloseRecordMapper;
import com.midea.logistics.otp.task.service.ITaskAddressService;
import com.midea.logistics.otp.task.service.IWarehouseCloseRecordService;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.auth.bean.UserInfo;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.thread.ThreadLocals;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.sdk.helper.MideaAuthUserHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: WarehouseCloseRecordServiceImpl
* Author: dongxy31
* Date: 2024-6-11 10:44:53
* Description:WarehouseCloseRecordServiceImpl类
*/
@Service
public class WarehouseCloseRecordServiceImpl implements IWarehouseCloseRecordService {

    private static final Logger logger = LoggerFactory.getLogger( WarehouseCloseRecordServiceImpl.class );

    @Autowired
    private WarehouseCloseRecordMapper warehouseCloseRecordMapper;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private ISsoService iSsoService;

    @Override
    @Transactional(readOnly = false)
    public Integer saveWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord) {

        if( null == warehouseCloseRecord) {
            logger.warn("save warehouseCloseRecord, but warehouseCloseRecord is null...");
            return 0;
            }
        WarehouseCloseRecord warehouseCloseRecordreq = new WarehouseCloseRecord();
        warehouseCloseRecordreq.setCustomerCode(warehouseCloseRecord.getCustomerCode());
        warehouseCloseRecordreq.setSiteCode(warehouseCloseRecord.getSiteCode());
        warehouseCloseRecordreq.setWhCode(warehouseCloseRecord.getWhCode());
        WarehouseCloseRecord warehouseCloseRecord1 = warehouseCloseRecordMapper.selectOne(warehouseCloseRecordreq);
        if(warehouseCloseRecord1 !=null ) {
            throw BusinessException.fail("已存在，重新维护！");
        }

        UserInfo userInfo = (UserInfo) iSsoService.getUserInfo(iSsoService.getUserCode());
        warehouseCloseRecord.setCreateUserCode(iSsoService.getUserCode());
        warehouseCloseRecord.setUpdateUserCode(iSsoService.getUserCode());
        if (userInfo != null) {
            warehouseCloseRecord.setCreateUserName(userInfo.getUserName());
            warehouseCloseRecord.setUpdateUserName(userInfo.getUserName());
        }
        return warehouseCloseRecordMapper.save(warehouseCloseRecord);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord) {

        if( null == warehouseCloseRecord || null == warehouseCloseRecord.getId()) {
            logger.warn("delete warehouseCloseRecord, but warehouseCloseRecord is null  or warehouseCloseRecord id is null...");
            return 0;
        }
        UserInfo userInfo = (UserInfo) iSsoService.getUserInfo(iSsoService.getUserCode());
        warehouseCloseRecord.setUpdateUserCode(iSsoService.getUserCode());
        if (userInfo != null) {
            warehouseCloseRecord.setUpdateUserName(userInfo.getUserName());
        }

        return warehouseCloseRecordMapper.deleteById(warehouseCloseRecord);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord) {

        if( null == warehouseCloseRecord || null == warehouseCloseRecord.getId()) {
            logger.warn("update warehouseCloseRecord, but warehouseCloseRecord is null  or warehouseCloseRecord id is null...");
            return 0;
        }
        UserInfo userInfo = (UserInfo) iSsoService.getUserInfo(iSsoService.getUserCode());
        warehouseCloseRecord.setUpdateUserCode(iSsoService.getUserCode());
        if (userInfo != null) {
            warehouseCloseRecord.setUpdateUserName(userInfo.getUserName());
        }

        return warehouseCloseRecordMapper.updateById(warehouseCloseRecord);
    }

    @Override
    public WarehouseCloseRecord selectOneWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord) {
        if( warehouseCloseRecord == null) {
            logger.warn("select warehouseCloseRecord one, but warehouseCloseRecord is null ...");
            return null;
        }
        warehouseCloseRecord = warehouseCloseRecordMapper.selectOne( warehouseCloseRecord );
        return warehouseCloseRecord;
    }

    @Override
    public PageResponse<WarehouseCloseRecord> selectWarehouseCloseRecordPage(WarehouseCloseRecord warehouseCloseRecord) {
        PageResponse<WarehouseCloseRecord> pagingData = new PageResponse<>();

        if( null == warehouseCloseRecord ) {
            logger.warn("select warehouseCloseRecord page, but warehouseCloseRecord is null...");
            return pagingData;
        }

        warehouseCloseRecord.setCustomerName(null);
        warehouseCloseRecord.setSiteName(null);
        warehouseCloseRecord.setWhName(null);

        Integer queryCount = warehouseCloseRecordMapper.selectByIndexCount( warehouseCloseRecord );
        if( null != queryCount && queryCount <= 0 ) {
            logger.info("select warehouseCloseRecord page , but count {} == 0 ...",queryCount);
            pagingData.init(warehouseCloseRecord.getPageNo(),warehouseCloseRecord.getPageSize(),queryCount,null);
            return pagingData;
        }

        List<WarehouseCloseRecord> warehouseCloseRecords =  selectWarehouseCloseRecordByIndex( warehouseCloseRecord );
        pagingData.init(warehouseCloseRecord.getPageNo(),warehouseCloseRecord.getPageSize(),queryCount,warehouseCloseRecords);
        return pagingData;
    }

    @Override
    public List<WarehouseCloseRecord> selectWarehouseCloseRecordByIndex(WarehouseCloseRecord warehouseCloseRecord) {
        List<WarehouseCloseRecord> warehouseCloseRecords = new ArrayList<>();
        if( warehouseCloseRecord == null) {
            logger.warn("select warehouseCloseRecord by index, but warehouseCloseRecord is null ...");
            return warehouseCloseRecords;
        }

        warehouseCloseRecords = warehouseCloseRecordMapper.selectByIndex( warehouseCloseRecord );

        return warehouseCloseRecords;
    }

    @Override
    public int countWarehouseCloseRecordByIndex(WarehouseCloseRecord warehouseCloseRecord) {
        int count = 0;
        if( warehouseCloseRecord == null) {
            logger.warn("count warehouseCloseRecord by index, but warehouseCloseRecord is null ...");
            return count;
        }

        count = warehouseCloseRecordMapper.selectByIndexCount( warehouseCloseRecord );

        return count;
    }

    @Override
    public Integer insertBatch(List<WarehouseCloseRecord> warehouseCloseRecords) {
        MDC.remove("traceId");
        ThreadLocals.put("tenantCode", "annto");

        if (CollectionUtils.isEmpty(warehouseCloseRecords)) {
            logger.warn("insertBatch warehouseCloseRecords, but warehouseCloseRecords is null ...");
            return 0;
        }

        warehouseCloseRecords.forEach(warehouseCloseRecord -> {
            WarehouseCloseRecord warehouseCloseRecordreq = new WarehouseCloseRecord();
            warehouseCloseRecordreq.setCustomerCode(warehouseCloseRecord.getCustomerCode());
            warehouseCloseRecordreq.setSiteCode(warehouseCloseRecord.getSiteCode());
            warehouseCloseRecordreq.setWhCode(warehouseCloseRecord.getWhCode());
            WarehouseCloseRecord warehouseCloseRecord1 = warehouseCloseRecordMapper.selectOne(warehouseCloseRecordreq);
            if(warehouseCloseRecord1 !=null ) {
                throw BusinessException.fail("已存在相同的数据，重新维护！");
            }

            UserInfo userInfo = (UserInfo) iSsoService.getUserInfo(iSsoService.getUserCode());
            warehouseCloseRecord.setCreateUserCode(iSsoService.getUserCode());
            warehouseCloseRecord.setUpdateUserCode(iSsoService.getUserCode());
            if (userInfo != null) {
                warehouseCloseRecord.setCreateUserName(userInfo.getUserName());
                warehouseCloseRecord.setUpdateUserName(userInfo.getUserName());
            }



        });
        return warehouseCloseRecordMapper.insertBatch(warehouseCloseRecords);
    }

    @Override
    public Integer batchUpdate(List<WarehouseCloseRecord> warehouseCloseRecords) {
        MDC.remove("traceId");
        ThreadLocals.put("tenantCode", "annto");
        if (CollectionUtils.isEmpty(warehouseCloseRecords)) {
            logger.warn("batchUpdate warehouseCloseRecords, but warehouseCloseRecords is null ...");
            return 0;
        }
        warehouseCloseRecords.forEach(warehouseCloseRecord -> {
            WarehouseCloseRecord warehouseCloseRecordreq = new WarehouseCloseRecord();
            warehouseCloseRecordreq.setCustomerCode(warehouseCloseRecord.getCustomerCode());
            warehouseCloseRecordreq.setSiteCode(warehouseCloseRecord.getSiteCode());
            warehouseCloseRecordreq.setWhCode(warehouseCloseRecord.getWhCode());
            warehouseCloseRecordreq.setEnableFlag(warehouseCloseRecord.getEnableFlag());
            WarehouseCloseRecord warehouseCloseRecord1 = warehouseCloseRecordMapper.selectOne(warehouseCloseRecordreq);
            if(warehouseCloseRecord1 !=null ) {
                throw BusinessException.fail("已存在相同的数据,不能修改！");
            }
            UserInfo userInfo = (UserInfo) iSsoService.getUserInfo(iSsoService.getUserCode());
            warehouseCloseRecord.setUpdateUserCode(iSsoService.getUserCode());
            if (userInfo != null) {
                warehouseCloseRecord.setUpdateUserName(userInfo.getUserName());
            }
        });

        return warehouseCloseRecordMapper.batchUpdate(warehouseCloseRecords);
    }

    @Override
    public Integer batchUpdateTask(List<WarehouseCloseRecord> warehouseCloseRecords) {
        if (CollectionUtils.isEmpty(warehouseCloseRecords)) {
            logger.warn("batchUpdate warehouseCloseRecords, but warehouseCloseRecords is null ...");
            return 0;
        }

        return warehouseCloseRecordMapper.batchUpdate(warehouseCloseRecords);
    }

    @Override
    public Integer deleteBatch(List<WarehouseCloseRecord> warehouseCloseRecords) {
        if (CollectionUtils.isEmpty(warehouseCloseRecords)) {
            logger.warn("deleteBatch warehouseCloseRecords, but warehouseCloseRecords is null ...");
            return 0;
        }
        warehouseCloseRecords.forEach(warehouseCloseRecord -> {
            UserInfo userInfo = (UserInfo) iSsoService.getUserInfo(iSsoService.getUserCode());
            warehouseCloseRecord.setUpdateUserCode(iSsoService.getUserCode());
            if (userInfo != null) {
                warehouseCloseRecord.setUpdateUserName(userInfo.getUserName());
            }
        });

        return warehouseCloseRecordMapper.deleteBatch(warehouseCloseRecords);
    }

    @Override
    public List<WarehouseCloseRecord> queryListByUpdate(WarehouseCloseRecordReq warehouseCloseRecord) {

        List<WarehouseCloseRecord> warehouseCloseRecords = new ArrayList<>();
        if( warehouseCloseRecord == null) {
            logger.warn("select warehouseCloseRecord by index, but warehouseCloseRecord is null ...");
            return warehouseCloseRecords;
        }

        warehouseCloseRecords = warehouseCloseRecordMapper.queryListByUpdate( warehouseCloseRecord );

        return warehouseCloseRecords;
    }

    @Override
    public List<WarehouseCloseRecord> queryList(WarehouseCloseRecordReq warehouseCloseRecord) {

        List<WarehouseCloseRecord> warehouseCloseRecords = new ArrayList<>();
        if( warehouseCloseRecord == null) {
            logger.warn("select warehouseCloseRecord by index, but warehouseCloseRecord is null ...");
            return warehouseCloseRecords;
        }

        warehouseCloseRecords = warehouseCloseRecordMapper.queryList( warehouseCloseRecord );

        return warehouseCloseRecords;
    }

}