<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.DeliveryRuleExtMapper">

    <sql id="searchFieldsSql">
        `delete_flag` AS deleteFlag,
        `volume` AS volume,
        `order_type` AS orderType,
        `update_user_code` AS updateUserCode,
        `site_code` AS siteCode,
        `create_time` AS createTime,
        `create_user_code` AS createUserCode,
        `qty` AS qty,
        `customer_code` AS customerCode,
        `update_time` AS updateTime,
        `id` AS id,
        `version` AS version
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="volume !=null">
            and `volume` =#{volume}
        </if>
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="qty !=null">
            and `qty` =#{qty}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="volume != null">
                `volume` = #{volume},
            </if>
            <if test="orderType !=null and orderType != ''">
                `order_type` = #{orderType},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="siteCode !=null and siteCode != ''">
                `site_code` = #{siteCode},
            </if>
            <if test="qty != null">
                `qty` = #{qty},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                `customer_code` = #{customerCode},
            </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from delivery_rule t
        where
        `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.DeliveryRule">
        select
        <include refid="searchFieldsSql"/>
        from delivery_rule t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.DeliveryRule">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from delivery_rule t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.DeliveryRule">
        select
        <include refid="searchFieldsSql"/>
        from delivery_rule t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        delivery_rule t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>

    <update id="deleteById">
        update
        delivery_rule t
        set `delete_flag`=1
        where
        `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.DeliveryRule" useGeneratedKeys="true"
            keyProperty="id">
        insert into delivery_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="volume != null">
                `volume`,
            </if>

            <if test="orderType !=null and orderType != ''">
                `order_type`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="siteCode !=null and siteCode != ''">
                `site_code`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="qty != null">
                `qty`,
            </if>

            <if test="customerCode !=null and customerCode != ''">
                `customer_code`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="volume != null">
                #{volume},
            </if>
            <if test="orderType !=null and orderType != ''">
                #{orderType},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="siteCode !=null and siteCode != ''">
                #{siteCode},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="qty != null">
                #{qty},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                #{customerCode},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `volume`,
            `order_type`,
            `update_user_code`,
            `site_code`,
            `create_user_code`,
            `qty`,
            `customer_code`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.volume},
            #{item.orderType},
            #{item.updateUserCode},
            #{item.siteCode},
            #{item.createUserCode},
            #{item.qty},
            #{item.customerCode},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        delivery_rule
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.volume != null">
                `volume` = #{item.volume},
            </if>
            <if test="item.orderType !=null and item.orderType != ''">
                `order_type` = #{item.orderType},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.siteCode !=null and item.siteCode != ''">
                `site_code` = #{item.siteCode},
            </if>
            <if test="item.qty != null">
                `qty` = #{item.qty},
            </if>
            <if test="item.customerCode !=null and item.customerCode != ''">
                `customer_code` = #{item.customerCode},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.id !=null">
                and `id` =#{item.id}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE delivery_rule
            <include refid="setBatchFieldsSql"/>
            where
            <include refid="setBatchWhereFields"/>
        </foreach>

    </update>

    <update id="deleteBatchById" parameterType="list">
        update
        delivery_rule d
        set `delete_flag`=1
        where
        `id` in
        <foreach collection="array" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>


</mapper>