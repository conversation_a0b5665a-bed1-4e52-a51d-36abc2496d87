<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-wms-ti" name="子订单流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1149caj</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_1pcuews</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_1szv1rd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_195th7r" name="生成任务">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="task" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/sentTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0aatv0u</bpmn:incoming>
      <bpmn:outgoing>Flow_1pcuews</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:incoming>Flow_1szv1rd</bpmn:incoming>
      <bpmn:outgoing>Flow_0aatv0u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1pcuews" sourceRef="Activity_195th7r" targetRef="Event_14d5skw" />
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_1149caj</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Activity_1fv890g">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1szv1rd" sourceRef="Activity_087r4hi" targetRef="Activity_1fv890g" />
    <bpmn:sequenceFlow id="Flow_0aatv0u" sourceRef="Activity_1fv890g" targetRef="Activity_195th7r" />
    <bpmn:sequenceFlow id="Flow_1149caj" sourceRef="StartEvent_1" targetRef="Gateway_0mzkgmp" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-wms-ti">
      <bpmndi:BPMNEdge id="Flow_1149caj_di" bpmnElement="Flow_1149caj">
        <di:waypoint x="188" y="177" />
        <di:waypoint x="295" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aatv0u_di" bpmnElement="Flow_0aatv0u">
        <di:waypoint x="660" y="177" />
        <di:waypoint x="850" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1szv1rd_di" bpmnElement="Flow_1szv1rd">
        <di:waypoint x="490" y="177" />
        <di:waypoint x="560" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="320" y="152" />
        <di:waypoint x="320" y="110" />
        <di:waypoint x="610" y="110" />
        <di:waypoint x="610" y="137" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="321" y="122" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="345" y="177" />
        <di:waypoint x="390" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="349" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pcuews_di" bpmnElement="Flow_1pcuews">
        <di:waypoint x="950" y="177" />
        <di:waypoint x="1072" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="1072" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1079" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="390" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1aomcsq_di" bpmnElement="Activity_195th7r">
        <dc:Bounds x="850" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="560" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="295" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="307" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
