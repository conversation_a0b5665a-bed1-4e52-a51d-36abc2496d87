package com.midea.logistics.otp.orderverify.service;

import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;

/**
 * <AUTHOR>
 */
public interface AssignWarehouseService {


    /**
     * 自动分仓
     *
     * @param orderNo
     * @return
     */
    FlowListenerParam automatic(CustomerOrderInfoExt dto);


}
