package com.midea.logistics.otp.ttx.service;

import com.midea.logistics.otp.ttx.inventory.domain.request.ReceiptDataTTXRequest;
import com.mideaframework.core.web.JsonResponse;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: IReceiptInventoryTTXService
 * Author: caizt
 * Date: 2019-8-22 8:45:52
 * Description:TTX-库存入库单导入
 */
public interface IReceiptInventoryTTXService {

public JsonResponse importReceiptData(List<ReceiptDataTTXRequest> requests);

}
