<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-gsc" name="父订单流程-gsc" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_08mw2j3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0d15yei" name="商品确认">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mappingOfc" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1oyv8yd</bpmn:incoming>
      <bpmn:outgoing>Flow_1ngf0qe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1oyv8yd" sourceRef="Activity_1k6ho42" targetRef="Activity_0d15yei" />
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08mw2j3</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyv8yd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0nccbee" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="receive" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ngf0qe</bpmn:incoming>
      <bpmn:outgoing>Flow_0hb6030</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/addressResolving" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k9k1b7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wi0xbe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1pyopys</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wi0xbe" sourceRef="Activity_1i377su" targetRef="Activity_0j0kz71" />
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Event_09zbrsb" />
    <bpmn:serviceTask id="Activity_07ox4pt" name="统配计费解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/businessCategoryAndTcFlag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0pp7125</bpmn:incoming>
      <bpmn:outgoing>Flow_1pyopys</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_08mw2j3" sourceRef="StartEvent_1" targetRef="Activity_1k6ho42" />
    <bpmn:exclusiveGateway id="Gateway_04dmfyf" name="是否地址解析" default="Flow_191u9lk">
      <bpmn:incoming>Flow_0hb6030</bpmn:incoming>
      <bpmn:outgoing>Flow_1k9k1b7</bpmn:outgoing>
      <bpmn:outgoing>Flow_191u9lk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1k9k1b7" name="yes" sourceRef="Gateway_04dmfyf" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and (((customerOrderInfo.deliveryType!=null)=false or (customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT")) and (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO") and (customerOrderInfo.orderSource!=null and customerOrderInfo.orderSource!="HANDLE")))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_191u9lk" name="no" sourceRef="Gateway_04dmfyf" targetRef="Activity_0j0kz71" />
    <bpmn:sequenceFlow id="Flow_1ngf0qe" sourceRef="Activity_0d15yei" targetRef="Activity_0nccbee" />
    <bpmn:sequenceFlow id="Flow_0hb6030" sourceRef="Activity_0nccbee" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_1pyopys" sourceRef="Activity_07ox4pt" targetRef="Activity_0uruim6" />
    <bpmn:serviceTask id="Activity_0j0kz71" name="标准产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/standardProductAnalysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wi0xbe</bpmn:incoming>
      <bpmn:incoming>Flow_191u9lk</bpmn:incoming>
      <bpmn:outgoing>Flow_0pp7125</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0pp7125" sourceRef="Activity_0j0kz71" targetRef="Activity_07ox4pt" />
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-gsc">
      <bpmndi:BPMNEdge id="Flow_1pyopys_di" bpmnElement="Flow_1pyopys">
        <di:waypoint x="1240" y="380" />
        <di:waypoint x="1240" y="420" />
        <di:waypoint x="970" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hb6030_di" bpmnElement="Flow_0hb6030">
        <di:waypoint x="740" y="120" />
        <di:waypoint x="825" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ngf0qe_di" bpmnElement="Flow_1ngf0qe">
        <di:waypoint x="550" y="120" />
        <di:waypoint x="640" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191u9lk_di" bpmnElement="Flow_191u9lk">
        <di:waypoint x="850" y="145" />
        <di:waypoint x="850" y="220" />
        <di:waypoint x="1190" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1022" y="198" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9k1b7_di" bpmnElement="Flow_1k9k1b7">
        <di:waypoint x="875" y="120" />
        <di:waypoint x="1000" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="894" y="103" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08mw2j3_di" bpmnElement="Flow_08mw2j3">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="270" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="870" y="420" />
        <di:waypoint x="768" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wi0xbe_di" bpmnElement="Flow_1wi0xbe">
        <di:waypoint x="1100" y="120" />
        <di:waypoint x="1240" y="120" />
        <di:waypoint x="1240" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyv8yd_di" bpmnElement="Flow_1oyv8yd">
        <di:waypoint x="370" y="120" />
        <di:waypoint x="450" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pp7125_di" bpmnElement="Flow_0pp7125">
        <di:waypoint x="1240" y="260" />
        <di:waypoint x="1240" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="143" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xzxgxd_di" bpmnElement="Activity_0d15yei">
        <dc:Bounds x="450" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="732" y="402" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="739" y="378" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="270" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g3mnwv_di" bpmnElement="Activity_0nccbee">
        <dc:Bounds x="640" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="1000" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="870" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04dmfyf_di" bpmnElement="Gateway_04dmfyf" isMarkerVisible="true">
        <dc:Bounds x="825" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="867" y="83" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w1igdz_di" bpmnElement="Activity_07ox4pt">
        <dc:Bounds x="1190" y="300" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0j0kz71_di" bpmnElement="Activity_0j0kz71">
        <dc:Bounds x="1190" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>