package com.midea.logistics.imp.orderverify;

import com.mideaframework.core.config.properties.AppProperties;
import com.mideaframework.transactionservice.mq.MqCilentsScan;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;


@EnableEncryptableProperties
@EnableFeignClients(value = {
        "com.midea.logistics.otp.order.common.fegin",
        "com.midea.logistics.otp.common.feign",
        "com.midea.logistics.logisticsbopsdk",
        "com.midea.logistics.cache",
})
@MqCilentsScan(basePackages = "com.midea.logistics.imp.orderverify.mq")
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@ComponentScan(basePackages = {"com.midea.logistics.*", "com.mideaframework.*"})
public class LmpOrderVerifyService {

    public static void main(String[] args) {
        ConfigurableApplicationContext run = SpringApplication.run(LmpOrderVerifyService.class, args);
        AppProperties.setEnv(run);
    }

    @Bean
    public RestTemplate restTemplate(){
        return new RestTemplate();
    }
}
