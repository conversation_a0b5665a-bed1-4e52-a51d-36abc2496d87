<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskItemMapper">

    <update id="batchUpdateQty">
    <foreach collection="list" item="item" separator=";">
        UPDATE task_item
        <set>
            `version` = `version` + 1 ,
            <if test="item.planQty != null">
                `plan_qty`  = ifnull(`plan_qty`, 0) - #{item.planQty},
            </if>
            <if test="item.actQty != null">
                `act_qty`  = ifnull(`act_qty`, 0) + #{item.actQty},
            </if>
            <if test="item.cancleQty != null">
                `cancle_qty`  = ifnull(`cancle_qty`, 0) + #{item.cancleQty},
            </if>
            <if test="item.splitQty != null">
                `split_qty`  = ifnull(`split_qty`, 0) + #{item.splitQty},
            </if>
        </set>
        where
        `id` = #{item.id}
        <if test="item.actQty != null">
            and plan_qty >= ifnull(`act_qty`, 0) + #{item.actQty}
        </if>
    </foreach>
    </update>

    <select id="listByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.TaskItem">
        select
        <include refid="searchFieldsSql"/>
        from task_item t where t.task_no in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="sumActQtyByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.TaskItem">
        select
        task_no as taskNo,
        sum(act_qty) as actQty
        from task_item
        <where>
            task_no in
            <foreach collection="TaskNos" item="TaskNo" separator="," open="(" close=")">
                #{TaskNo}
            </foreach>
            and  act_qty > 0
            and  delete_flag=0
        </where>
        group by task_no
    </select>

    <select id="querySplitTasksByOrderNo" resultType="com.midea.logistics.otp.task.domain.bean.TaskItem">
        select
        <include refid="searchFieldsSql"/>
        from
        task_item ti
        where
        ti.task_no in(
        select
        t.task_no
        from
        task t
        where
        t.order_no = #{orderNo}
        and t.task_type = 'DO-SPLIT'
        and t.delete_flag = 0)
        and ti.delete_flag = 0
    </select>

</mapper>