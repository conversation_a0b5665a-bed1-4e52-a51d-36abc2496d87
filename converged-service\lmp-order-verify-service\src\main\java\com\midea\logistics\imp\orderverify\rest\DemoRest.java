package com.midea.logistics.imp.orderverify.rest;

import com.mideaframework.core.web.JsonResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DemoRest {
    private Logger logger = LoggerFactory.getLogger(DemoRest.class);

    @GetMapping("/lmp/verify/demo")
    public JsonResponse testSearch2() {
        return JsonResponse.success("success");
    }


    @PostMapping("/lmp/verify/demoPost")
    public JsonResponse demoPost() {
        return JsonResponse.success("success");
    }

}
