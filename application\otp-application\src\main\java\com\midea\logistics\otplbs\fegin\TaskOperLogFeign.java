package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.task.domain.bean.TaskOperLog;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskOperLogFeign
 * Author: fangwb
 * Date: 2019年5月29日 下午2:07:41
 * Description:任务操作日志 feign
 */
@FeignClient(value = "logistics-otp-task-service")
public interface TaskOperLogFeign {

    @RequestMapping(value = "/taskOperLog", method = RequestMethod.POST)
    JsonResponse create(@RequestBody TaskOperLog taskOperLog);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskOperLog/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskOperLog/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody TaskOperLog taskOperLog);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskOperLog/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/taskOperLogs", method = RequestMethod.POST)
    JsonResponse search(@RequestBody TaskOperLog taskOperLog);
}
