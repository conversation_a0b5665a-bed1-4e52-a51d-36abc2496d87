package com.midea.logistics.imp.orderverify.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.midea.logistics.imp.orderverify.helper.LmpDeliveryTypeHelper;
import com.midea.logistics.imp.orderverify.service.DeliveryTypeResetService;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @author: zhouhl
 * @date: 2023-7-11 11:13
 * @Description:客户需要根据配置设置配送方式
 */

@Service
public class DeliveryTypeResetServiceImpl implements DeliveryTypeResetService{
    @Autowired
    private LmpDeliveryTypeHelper lmpDeliveryTypeHelper;


    @Override
    @ZeebeFlow("RESET_DELIVERY_TYPE")
    public CustomerOrderInfoExt resetDeliveryType(CustomerOrderInfoExt customerOrderInfo) {
        if(customerOrderInfo == null || StringUtils.isEmpty(customerOrderInfo.getOrderNo())) {
            throw BusinessException.fail("订单号为空");
        }
        String sourceSystem = customerOrderInfo.getSourceSystem();
        String deliveryType = customerOrderInfo.getDeliveryType();
        String orderType = customerOrderInfo.getOrderType();
        List<String> sourceSystems = Arrays.asList(SourceSystem.XINLONG.getKey());
        List<String> orderTypes = Arrays.asList(OrderType.PO.getKey(), OrderType.RI.getKey());
        boolean isReset = sourceSystems.contains(sourceSystem) && orderTypes.contains(orderType) && DeliveryType.DELIVERY.getKey().equals(deliveryType);
        if (!isReset){
            FlowListenerParam.success("");
            return customerOrderInfo;
        }
        String customerCode = customerOrderInfo.getCustomerCode();
        String siteCode = customerOrderInfo.getSiteCode();
        String whCode = customerOrderInfo.getWhCode();
        String businessMode = customerOrderInfo.getBusinessMode();
        CustomerOrderAddress customerOrderAddress = customerOrderInfo.getCustomerOrderAddress();
        if (StringUtils.isAnyEmpty(customerCode,siteCode,whCode,businessMode) || ObjectUtil.isEmpty(customerOrderAddress)){
            FlowListenerParam.success("客户·平台·仓库·业务模式有空值,不设置配送方式");
            return customerOrderInfo;
        }

        ShippingTypeRule deliveryRule = lmpDeliveryTypeHelper.getDeliveryRule(customerOrderInfo, customerOrderAddress, businessMode, true);
        if (null != deliveryRule && !StringUtils.isEmpty(deliveryRule.getDeliveryType())){
            customerOrderInfo.setDeliveryType(deliveryRule.getDeliveryType());
        }
        FlowListenerParam.success("");
        return customerOrderInfo;
    }
}
