package com.midea.logistics.otp.order.common.fegin.iflow;

import com.midea.logistics.iflow.feign.IIflowFeign;
import com.midea.logistics.iflow.mbmp.domain.dto.ReqCreateDraftDto;
import com.midea.logistics.iflow.mbmp.domain.dto.RespCreateDraftDto;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "logistics-iflow-service")
public interface IflowFeign extends IIflowFeign {
    /**
     * 创建流程
     * @param reqCreateDraftDto
     * @return
     */
    @PostMapping("/createDraftProcess")
    JsonResponse<RespCreateDraftDto> createAndApproveProcess(@RequestBody ReqCreateDraftDto reqCreateDraftDto);

}
