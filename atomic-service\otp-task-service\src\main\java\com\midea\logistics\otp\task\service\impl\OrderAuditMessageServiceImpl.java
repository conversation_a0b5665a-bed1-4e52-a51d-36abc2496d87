package com.midea.logistics.otp.task.service.impl;

import com.midea.logistics.otp.task.domain.bean.OrderAuditMessage;
import com.midea.logistics.otp.task.mapper.common.OrderAuditMessageMapper;
import com.midea.logistics.otp.task.service.IOrderAuditMessageService;
import com.mideaframework.core.web.PageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: OrderAuditMessageServiceImpl
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:OrderAuditMessageServiceImpl类
 */
@Service
public class OrderAuditMessageServiceImpl implements IOrderAuditMessageService {


    private static final Logger logger = LoggerFactory.getLogger(OrderAuditMessageServiceImpl.class);

    @Autowired
    private OrderAuditMessageMapper orderAuditMessageMapper;


    @Override
    @Transactional(readOnly = false)
    public Integer saveOrderAuditMessage(OrderAuditMessage orderAuditMessage) {

        if (null == orderAuditMessage) {
            logger.warn("save orderAuditMessage, but orderAuditMessage is null...");
            return 0;
        }

        return orderAuditMessageMapper.save(orderAuditMessage);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteOrderAuditMessage(OrderAuditMessage orderAuditMessage) {

        if (null == orderAuditMessage || null == orderAuditMessage.getId()) {
            logger.warn("delete orderAuditMessage, but orderAuditMessage is null  or orderAuditMessage id is null...");
            return 0;
        }

        return orderAuditMessageMapper.deleteById(orderAuditMessage);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateOrderAuditMessage(OrderAuditMessage orderAuditMessage) {

        if (null == orderAuditMessage || null == orderAuditMessage.getId()) {
            logger.warn("update orderAuditMessage, but orderAuditMessage is null  or orderAuditMessage id is null...");
            return 0;
        }

        return orderAuditMessageMapper.updateById(orderAuditMessage);
    }

    @Override
    public OrderAuditMessage selectOneOrderAuditMessage(OrderAuditMessage orderAuditMessage) {
        if (orderAuditMessage == null) {
            logger.warn("select orderAuditMessage one, but orderAuditMessage is null ...");
            return null;
        }
        orderAuditMessage = orderAuditMessageMapper.selectOne(orderAuditMessage);
        return orderAuditMessage;
    }

    @Override
    public PageResponse<OrderAuditMessage> selectOrderAuditMessagePage(OrderAuditMessage orderAuditMessage) {
        PageResponse<OrderAuditMessage> pagingData = new PageResponse<>();

        if (null == orderAuditMessage) {
            logger.warn("select orderAuditMessage page, but orderAuditMessage is null...");
            return pagingData;
        }

        Integer queryCount = orderAuditMessageMapper.selectByIndexCount(orderAuditMessage);
        if (null != queryCount && queryCount <= 0) {
            logger.info("select orderAuditMessage page , but count {} == 0 ...", queryCount);
            return pagingData;
        }

        List<OrderAuditMessage> orderAuditMessages = selectOrderAuditMessageByIndex(orderAuditMessage);
        pagingData.init(orderAuditMessage.getPageNo(), orderAuditMessage.getPageSize(), queryCount, orderAuditMessages);
        return pagingData;
    }

    @Override
    public List<OrderAuditMessage> selectOrderAuditMessageByIndex(OrderAuditMessage orderAuditMessage) {
        List<OrderAuditMessage> orderAuditMessages = new ArrayList<>();
        if (orderAuditMessage == null) {
            logger.warn("select orderAuditMessage by index, but orderAuditMessage is null ...");
            return orderAuditMessages;
        }

        orderAuditMessages = orderAuditMessageMapper.selectByIndex(orderAuditMessage);

        return orderAuditMessages;
    }

    @Override
    public int countOrderAuditMessageByIndex(OrderAuditMessage orderAuditMessage) {
        int count = 0;
        if (orderAuditMessage == null) {
            logger.warn("count orderAuditMessage by index, but orderAuditMessage is null ...");
            return count;
        }

        count = orderAuditMessageMapper.selectByIndexCount(orderAuditMessage);

        return count;
    }

    @Override
    public Integer insertBatch(List<OrderAuditMessage> orderAuditMessages) {
        if (CollectionUtils.isEmpty(orderAuditMessages)) {
            logger.warn("insertBatch orderAuditMessages, but orderAuditMessages is null ...");
            return 0;
        }
        return orderAuditMessageMapper.insertBatch(orderAuditMessages);
    }

    @Override
    public Integer batchUpdate(List<OrderAuditMessage> orderAuditMessages) {
        if (CollectionUtils.isEmpty(orderAuditMessages)) {
            logger.warn("batchUpdate orderAuditMessages, but orderAuditMessages is null ...");
            return 0;
        }
        return orderAuditMessageMapper.batchUpdate(orderAuditMessages);
    }

    @Override
    public Integer deleteBatch(List<OrderAuditMessage> orderAuditMessages) {
        if (CollectionUtils.isEmpty(orderAuditMessages)) {
            logger.warn("deleteBatch orderAuditMessages, but orderAuditMessages is null ...");
            return 0;
        }
        return orderAuditMessageMapper.deleteBatch(orderAuditMessages);
    }

}