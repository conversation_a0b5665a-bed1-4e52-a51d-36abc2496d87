package com.midea.logistics.otp.order.common.bean;

import com.midea.logistics.domain.mdm.domain.CdCommonMaterial;
import com.mideaframework.core.bean.TenantPagingDomain;
import lombok.Data;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName:
 * Author: caixz
 * Date: 2020-3-6 14:56
 * Description:TODO
 */
@Data
public class OrderItemSyncDto extends TenantPagingDomain {
    /**
     * 来源平台
     */
    private String sourceSystem;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 同步方式:PUSH/PULL，拉方式则需要去外部拉取，推方式保存至商品表
     */
    private String syncType;

    /**
     * 商品列表
     */
    private List<CdCommonMaterial> cdCommonMaterialModels;
}
