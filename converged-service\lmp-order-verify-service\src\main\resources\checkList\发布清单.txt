﻿1.OTP分支发版  
何：feature/heyh
罗\林：feature/2.0.1/appointment
2.物流中台分支发版
何：feature/heyh
罗\林：develop

城配订单中心：feature/lmp-order-2021
中台订单中心：feature/develop

1.代码合并：
城配订单中心：
feature/2.0.1/appointment -> 合并到 develop/test6  （已经合到lmp-order-2021）
feature/lmp-order-2021 -> 合并到 develop/test6    1
中台订单中心
feature/heyh -> feature/develop
logistics-otp-net

com.midea.logistics.imp.orderverify.service.impl.CargoRightTransferServiceImpl#cargoRightTransfer  
omsSuContractFegin.getWhCodeBySiteCodeAndReceiveAdress(siteCode, receiverDetailAddr); 有问题
orderInfoMapper.xml appoint_order_no代码合并问题

2.脚本执行 1
logistics-otp-order: all-sql.sql
logistics-otp-rule: rule-sql.sql
天猫优品-T.sql
天猫优品预约.sql
物流家具.sql

3.mq配置 1
创建PRODUCER:
LOGISTICS-LMP-ORDER-SERVICE-CONSUMER-GROUP-PROD
LOGISTICS-APPT-AGG-SERVICE-CONSUMER-GROUP-PROD
LOGISTICS-ORDER-AGG-SERVICE-CONSUMER-GROUP-PROD
创建topic:
zeebe_customer_order_topic
zeebe_customer_order_retry_topic
zeebe_order_topic
nettask_appt_topic
accept_order_topic
order_trace_topic   order_trace_tag

4.新增配置中心配置文件 1
logistics-lmp-order-verify-service-uat.yml
logistics-order-agg-service-uat.yml
logistics-appt-agg-service-uat.yml

5.容器部署配置
logistics-lmp-order-verify-service
logistics-order-agg-service
logistics-appt-agg-service
logistics-zeebe-service
logistics-zeebe引擎

6.配置devops uat发布线
logistics-lmp-order-verify-service
logistics-order-agg-service
logistics-appt-agg-service
logistics-zeebe-service
logistics-zeebe引擎

7.zeebe配置
logistics-zeebe-service配置
Camunda Operate控制台部署
ES6.8部署
流程模板上传：customerOrderInfoAll.bpmn，orderInfoAll.bpmn

8.devops应用发布
新增模块：
logistics-lmp-order-verify-service
logistics-order-agg-service
logistics-appt-agg-service
城配原有模块

9.启用审核件型计算节点，初始化数据（配置客户）
PDD:"QiMenA0018642,QiMenA0003056,ECMA0041801,ECMA0041800,ECMA0003056,ECMA0041801,QiMenA0041801"
菜鸟
UPDATE workflow_node_template  SET process_decision='{"defaultNextNodeCode":"customer_order_pieceAnalysis"}'  WHERE template_code='customer_order'  AND node_code='customer_order_audit_items'
INSERT INTO `workflow_node_template` (`remark`, `template_code`, `node_code`, `node_name`, `node_group`, `enable_flag`, `enable_rule`, `node_actuator`, `process_decision`, `node_variable`) VALUES('刘中林','customer_order','customer_order_pieceAnalysis','件型解析',NULL,'1','{"enableFlag":"1","autoLy":"1"}','{"actuatorType":"BEAN","preCondition":"com.midea.logistics.otp.orderverify.service.OrderService.customerOrderCloseCheck","actuatorPath":"com.midea.logistics.otp.orderverify.service.OrderService.pieceAnalysis"}','{"defaultNextNodeCode":"customer_order_mip_execution"}','{"orderOperateType":"PIECE_ANALYSIS"}');

业务大类解析:
UPDATE workflow_node_template  SET process_decision='{"defaultNextNodeCode":"customer_order_businessCategory"}'  WHERE template_code='customer_order'  AND node_code='customer_order_address_analysis'
INSERT INTO `workflow_node_template` (`remark`, `template_code`, `node_code`, `node_name`, `node_group`, `enable_flag`, `enable_rule`, `node_actuator`, `process_decision`, `node_variable`) VALUES('蔡小章','customer_order','customer_order_businessCategory','业务大类解析',NULL,'1','{"enableFlag":"1","autoLy":"1"}','{"actuatorType":"BEAN","preCondition":"com.midea.logistics.otp.orderverify.service.OrderService.customerOrderCloseCheck","actuatorPath":"com.midea.logistics.otp.orderverify.service.BusinessCategoryService.businessCategory"}','{"defaultNextNodeCode":"customer_order_cargo_right_transfer","skip":"DELIVERY_TYPE_ZT"}','{"orderOperateType":"BUSINESS_CATEGORY"}');



10.Job配置：无

11.DC发布

12.前端发布

13.业务数据配置初始化（应用）

14.配置接单地址
  鹊桥接单地址切换至新地址服务端

15 配置中心配置条码端到端（logistics-otp-net-atomic）
lbcs:
  loginUrl: http://api.irsnp.com/api-auth/public/exLogin
  userCode: snmi_meid1hzc0
  secrectKey: Wvo25cRDXmVWrgIC5QZtOUjC3OwJEykHrFoZNjYpsTetIJKsw9PWIn/HrZu0jSDe9oaoBxyK1vDJvcjtA1TV3kztwKpYFwuxsqeYaxxnokSJ9nZKU1bTe6ncirCHitsbuGJCa6v2+1inZAm+I0ULDDSyqR/QwAvaTvX+I4AOXYA=
  barcodeInfoUrl: http://api.irsnp.com/api-sn/lbcs/bcBarcodeSwap/saveBarcodeInfo

16配置RocketMq主題
   LOGISTICS_NET_OTP_SCAN_CODE