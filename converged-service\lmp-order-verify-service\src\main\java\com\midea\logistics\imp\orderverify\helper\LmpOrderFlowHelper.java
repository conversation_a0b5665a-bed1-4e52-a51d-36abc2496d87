package com.midea.logistics.imp.orderverify.helper;

import com.midea.logistics.imp.orderverify.bean.ZeebeParamDto;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.constant.WorkflowConstant;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.MapToBeanUtil;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.utils.thread.ThreadLocals;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ProjectName: logistics-otp
 * @Package: com.midea.logistics.otp.order.common.helper
 * @ClassName: 更新订单
 * @Author: ex_luohao2
 * @Description:
 * @Date: 2020/4/8 17:28
 * @Version: 1.0
 */
@Component
@Slf4j
public class LmpOrderFlowHelper {
    @Autowired
    private OrderFeign orderFeign;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;

    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;
    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;

    // TODO 存在订单号不是 IdPrefixEnum 前缀的情况，需要按场景写两个方法
//    public void loadingOrder(String orderNo) {
//        if (null == orderNo){
//            throw BusinessException.fail("订单号不能为空");
//        }
//        if (orderNo.startsWith(IdPrefixEnum.INNER.getValue())){
//            loadingOrder(orderNo, WorkflowConstant.CUSTOMER_ORDER);
//        }
//        if (orderNo.startsWith(IdPrefixEnum.ANNTO.getValue())){
//            loadingOrder(orderNo,  WorkflowConstant.ANNTO_ORDER);
//        }
//
//    }
    public void check(JsonResponse response,String errMsg){
        if (String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()).equals(response.getCode())){
            throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()) ,HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            throw BusinessException.fail(errMsg);
        }
    }


    /**
     * 加载订单信息
     * @param orderNo
     * @param templateCode
     */
    public void loadingOrder(String orderNo, String templateCode) {
        Object o = ThreadLocals.get(orderNo + templateCode);
        if (o != null){
            return;
        }
        //父单
        if(WorkflowConstant.CUSTOMER_ORDER.equals(templateCode)){

            CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
            customerOrderInfo.setOrderNo(orderNo);
            JsonResponse<CustomerOrderInfo> custOrderRespone = customerOrderInfoFeign.searchOne(customerOrderInfo);
            if (custOrderRespone == null) {
                throw BusinessException.fail("查询订单失败：" + orderNo);
            }
            if (!BaseCodeEnum.SUCCESS.getCode().equals(custOrderRespone.getCode())) {
                throw BusinessException.fail("查询订单失败：" + custOrderRespone.getMsg());
            }
            customerOrderInfo = custOrderRespone.data();
            if (customerOrderInfo != null) {
                ThreadLocals.put(orderNo + WorkflowConstant.CUSTOMER_ORDER, customerOrderInfo);
                List<CustomerOrderItem> items = customerOrderItemFeign.listByOrderNo(orderNo).data();
                ThreadLocals.put(orderNo + WorkflowConstant.CUSTOMER_ORDER+"items", items);
                CustomerOrderAddress address = customerOrderAddressFeign.findByOrderNo(orderNo).data();
                ThreadLocals.put(orderNo + WorkflowConstant.CUSTOMER_ORDER+"address", address);

            }
        }
        //子单
        if(WorkflowConstant.ANNTO_ORDER.equals(templateCode)){
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderNo(orderNo);
            JsonResponse<OrderInfo> orderRespone = orderFeign.selectOne(orderInfo);
            if (orderRespone == null) {
                throw BusinessException.fail("查询订单失败：" + orderNo);
            }
            if (!BaseCodeEnum.SUCCESS.getCode().equals(orderRespone.getCode())) {
                throw BusinessException.fail("查询订单失败：" + orderRespone.getMsg());
            }

            orderInfo = orderRespone.data();
            if (orderInfo != null) {
                ThreadLocals.put(orderNo + WorkflowConstant.ANNTO_ORDER, orderInfo);
                List<OrderInfoItem> items = orderInfoItemFeign.getOrderItem(orderNo).data();
                ThreadLocals.put(orderNo + WorkflowConstant.ANNTO_ORDER+"items", items);
                CustomerOrderAddress address = customerOrderAddressFeign.findByOrderNo(orderInfo.getParentOrderNo()).data();
                ThreadLocals.put(orderNo + WorkflowConstant.ANNTO_ORDER+"address", address);
            }
        }

    }


    /**
     * 客户订单==========================
     */
    public CustomerOrderInfo getCustomerOrderInfo(String orderNo){
        CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
        customerOrderInfo.setOrderNo(orderNo);
        JsonResponse<CustomerOrderInfo> custOrderRespone = customerOrderInfoFeign.searchOne(customerOrderInfo);
        if (custOrderRespone == null) {
            throw BusinessException.fail("查询订单失败：" + orderNo);
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(custOrderRespone.getCode())) {
            throw BusinessException.fail("查询订单失败：" + custOrderRespone.getMsg());
        }
        return custOrderRespone.data();
    }

    /**
     * 客户订单==========================
     */
    public CustomerOrderInfoExt getCustomerOrderInfoOnly(String orderNo){
        CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
        customerOrderInfo.setOrderNo(orderNo);
        JsonResponse<CustomerOrderInfo> custOrderRespone = customerOrderInfoFeign.searchOne(customerOrderInfo);
        if (custOrderRespone == null || null == custOrderRespone.data()) {
            throw BusinessException.fail("查询订单失败：" + orderNo);
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(custOrderRespone.getCode())) {
            throw BusinessException.fail("查询订单失败：" + custOrderRespone.getMsg());
        }

        CustomerOrderInfoExt customerOrderInfoExt = new CustomerOrderInfoExt();
        BeanUtils.copyProperties(custOrderRespone.data(), customerOrderInfoExt);

        CustomerOrderInfoExtend customerOrderInfoExtend = new CustomerOrderInfoExtend();
        customerOrderInfoExtend.setOrderNo(orderNo);
        JsonResponse<CustomerOrderInfoExtend> response = customerOrderInfoExtendFeign.selectOne(customerOrderInfoExtend);
        if (BaseCodeEnum.FAILED.getCode().equals(response.getCode())) {
            throw BusinessException.fail("查询扩展表失败！"+response.getMsg());
        }
        if(response.getData()!=null && StringUtils.isNotEmpty(response.getData().getOrderDistinctionFlag())){
            customerOrderInfoExt.setOrderDistinctionFlag(response.getData().getOrderDistinctionFlag());
        }


        return customerOrderInfoExt;
    }

    public CustomerOrderInfoExt getCustomerOrderInfoExt(String orderNo){
        if(StringUtils.isEmpty(orderNo)){
            throw BusinessException.fail("订单号为空：" + orderNo);
        }
        JsonResponse<CustomerOrderInfoExt> jsonResponseCustomerOrder = customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(orderNo);
        CustomerOrderInfoExt customerOrderInfoExt = jsonResponseCustomerOrder.data;
        if (customerOrderInfoExt == null) {
            throw BusinessException.fail("查询订单失败：" + orderNo);
        }

        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponseCustomerOrder.getCode())) {
            throw BusinessException.fail("查询订单失败：" + jsonResponseCustomerOrder.getMsg());
        }
        CustomerOrderInfoExtend extend = customerOrderInfoExt.getCustomerOrderInfoExtend();
        if(null == extend || null == extend.getId()) {
            CustomerOrderInfoExtend customerOrderInfoExtend = new CustomerOrderInfoExtend();
            customerOrderInfoExtend.setOrderNo(orderNo);
            JsonResponse<CustomerOrderInfoExtend> response = customerOrderInfoExtendFeign.selectOne(customerOrderInfoExtend);
            if (BaseCodeEnum.FAILED.getCode().equals(response.getCode())) {
                throw BusinessException.fail("查询扩展表失败！" + response.getMsg());
            }
            customerOrderInfoExt = jsonResponseCustomerOrder.data();
            customerOrderInfoExt.setCustomerOrderInfoExtend(response.getData());
            if (response.getData() != null && StringUtils.isNotEmpty(response.getData().getOrderDistinctionFlag())) {
                customerOrderInfoExt.setOrderDistinctionFlag(response.getData().getOrderDistinctionFlag());
            }
        }


        return customerOrderInfoExt;

    }

    public OrderInfoExt getOrderInfoExt(String orderNo){
        if(StringUtils.isEmpty(orderNo)){
            throw BusinessException.fail("订单号为空：" + orderNo);
        }
        JsonResponse<OrderInfo> jsonResponse1 = orderInfoFeign.selectByOrderNo(orderNo);
        OrderInfo orderInfo = jsonResponse1.data;

        if (null == orderInfo) {
            throw BusinessException.fail("触发子订单流程失败,订单信息为空");
        }

        JsonResponse jsonResponseOrder = orderInfoItemFeign.getOrderItem(orderNo);
        List<OrderInfoItem> orderInfoItems = (List<OrderInfoItem>) jsonResponseOrder.data;
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponseOrder.getCode())) {
            throw BusinessException.fail("查询订单明细失败：" + jsonResponseOrder.getMsg());
        }
        OrderInfoExt orderInfoExt = new OrderInfoExt();
        BeanUtils.copyProperties(orderInfo, orderInfoExt);
        orderInfoExt.setOrderInfoItems(orderInfoItems);

        return orderInfoExt;
    }

    /**
     * 获取 orderInfo 信息，同时保留扩展信息
     * @param orderNo
     * @param orderInfo
     * @return
     */
    public OrderInfoExt getOrderInfoExt(String orderNo, OrderInfoExt orderInfo){

        OrderInfoExt orderInfoExt = this.getOrderInfoExt(orderNo);

        if (orderInfo != null) {
            orderInfoExt.setOrderDistinctionFlag(orderInfo.getOrderDistinctionFlag());
        }

        return orderInfoExt;
    }

    public List<CustomerOrderInfo> listCustomerOrderInfo(List<String> orderNos){
        return orderNos.stream().map( o -> getCustomerOrderInfo(o))
            .collect(Collectors.toList());
    }

    public void updateCustomerOrderInfo(CustomerOrderInfo customerOrderInfo, String msg){
        if (ToolUtils.isEmpty(customerOrderInfo)){
            return;
        }
//        CustomerOrderInfo source = getCustomerOrderInfo(customerOrderInfo.getOrderNo());
//        source.setVersion(null);
//        source.setId(null);
//        CustomerOrderInfo target = (CustomerOrderInfo)CompareHelper.compareFieldsForObjiect(source, customerOrderInfo,null);
        customerOrderInfo.setVersion(0L);
        JsonResponse<Integer> update = customerOrderInfoFeign.update(customerOrderInfo.getId(), customerOrderInfo);
        if (update == null) {
            throw BusinessException.fail(msg + ",更新订单失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail(msg + ",更新订单失败：" + update.getMsg());
        }
//        CompareHelper.copyPropertiesIgnoreNull(customerOrderInfo, source);
//        source.setVersion(target.getVersion()+1);
//        source.setId(target.getId());
//        ThreadLocals.put(customerOrderInfo.getOrderNo() + WorkflowConstant.CUSTOMER_ORDER,source);
    }


    public void updateCanSetEmptyCustomerOrderInfo(CustomerOrderInfo customerOrderInfo){
//        if (ToolUtils.isEmpty(customerOrderInfo)){
//            return;
//        }
//        CustomerOrderInfo source = getCustomerOrderInfo(customerOrderInfo.getOrderNo());
//        source.setVersion(null);
//        source.setId(null);
//        CustomerOrderInfo target = (CustomerOrderInfo)CompareHelper.compareFieldsForObjiect(source, customerOrderInfo,null);
        customerOrderInfo.setVersion(0L);
        JsonResponse<Integer> update = customerOrderInfoFeign.updateByIdCanSetEmpty(customerOrderInfo.getId(), customerOrderInfo);
        if (update == null) {
            throw BusinessException.fail("更新订单失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail("更新订单失败：" + update.getMsg());
        }
//        CompareHelper.copyPropertiesIgnoreNull(customerOrderInfo, source);
//        source.setVersion(target.getVersion()+1);
//        source.setId(target.getId());
//        ThreadLocals.put(customerOrderInfo.getOrderNo() + WorkflowConstant.CUSTOMER_ORDER,source);
    }
    /**
     * 客户订单地址==========================
     */
    public CustomerOrderAddress getCustomerOrderAddress(String orderNo){
        return customerOrderAddressFeign.findByOrderNo(orderNo).data();
    }

    public List<CustomerOrderAddress> listCustomerOrderAddress(List<String> orderNos){
        return orderNos.stream().map( o -> (CustomerOrderAddress) ThreadLocals.get(o + WorkflowConstant.CUSTOMER_ORDER+"address"))
            .collect(Collectors.toList());
    }

    public void updateCustomerOrderAddress(CustomerOrderAddress customerOrderAddress){
        if (ToolUtils.isEmpty(customerOrderAddress)){
            return;
        }

        customerOrderAddress.setVersion(0L);
        JsonResponse update = customerOrderAddressFeign.update(customerOrderAddress.getId(), customerOrderAddress);
        if (update == null) {
            throw BusinessException.fail("更新订单地址失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail("更新订单地址失败：" + update.getMsg());
        }

    }

    /**
     * 客户订单商品==========================
     */

    public List<CustomerOrderItem> listCustomerOrderItem(String orderNo){
        return customerOrderItemFeign.listByOrderNo(orderNo).data();
    }

    public void updateCustomerOrderItem(List<CustomerOrderItem> items){
        if (ToolUtils.isEmpty(items)){
            return;
        }
        JsonResponse update = customerOrderItemFeign.batchCreateOrUpdate(items);
        if (update == null) {
            throw BusinessException.fail("更新订单商品失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail("更新订单商品失败：" + update.getMsg());
        }
//        items.stream().forEach(i-> i.setVersion(i.getVersion()+1));
//        ThreadLocals.put(items.get(0).getOrderNo() + WorkflowConstant.CUSTOMER_ORDER+"items", items);
    }

    /**
     * 子订单==========================
     */
    public OrderInfo getOrderInfo(String orderNo){
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo(orderNo);
        JsonResponse<OrderInfo> orderRespone = orderFeign.selectOne(orderInfo);
        if (orderRespone == null) {
            throw BusinessException.fail("查询订单失败：" + orderNo);
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(orderRespone.getCode())) {
            throw BusinessException.fail("查询订单失败：" + orderRespone.getMsg());
        }

        return orderRespone.data();
    }

    /**
     * 子订单==========================
     */
    public OrderInfoExt getOrderInfoOnly(String orderNo){
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo(orderNo);
        JsonResponse<OrderInfo> orderRespone = orderFeign.selectOne(orderInfo);
        if (orderRespone == null || null == orderRespone.data()) {
            throw BusinessException.fail("查询订单失败：" + orderNo);
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(orderRespone.getCode())) {
            throw BusinessException.fail("查询订单失败：" + orderRespone.getMsg());
        }

        OrderInfoExt orderInfoExt = new OrderInfoExt();
        BeanUtils.copyProperties(orderRespone.data(), orderInfoExt);
        return orderInfoExt;
    }

    public List<OrderInfo> listOrderInfo(List<String> orderNos){
        return orderNos.stream().map( o -> (OrderInfo) ThreadLocals.get(o + WorkflowConstant.ANNTO_ORDER))
            .collect(Collectors.toList());
    }

    // TODO 【已修改】 1. 这里要换一个方法，把   orderInfo 不是空的 cp 到 source， 把 source 放 ThreadLocals，而不是能 compareFieldsForObjiect 出来的差异信息
    // TODO 2. ThreadLocals 需要在审核结束之后清除，线程是重用的
    public void updateOrderInfo(OrderInfo orderInfo, String msg){
        if (ToolUtils.isEmpty(orderInfo)){
            return;
        }
//        OrderInfo source = getOrderInfo(orderInfo.getOrderNo());
//        source.setVersion(null);
//        source.setId(null);
//        OrderInfo target = (OrderInfo)CompareHelper.compareFieldsForObjiect(source, orderInfo,null);
        orderInfo.setVersion(0L);
        JsonResponse update = orderFeign.update(orderInfo.getId(), orderInfo);
        if (update == null) {
            throw BusinessException.fail(msg + ",更新订单失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail(msg + ",更新订单失败：" + update.getMsg());
        }
//        CompareHelper.copyPropertiesIgnoreNull(orderInfo, source);
//        source.setVersion(target.getVersion()+1);
//        source.setId(target.getId());
//        ThreadLocals.put(orderInfo.getOrderNo() + WorkflowConstant.ANNTO_ORDER,source);
    }

    /**
     * 客户订单商品==========================
     */

    public List<OrderInfoItem> listOrderInfoItem(String orderNo){
        return orderInfoItemFeign.getOrderItem(orderNo).data();
    }

    public void updateOrderInfoItem(List<OrderInfoItem> items){
        if (ToolUtils.isEmpty(items)){
            return;
        }
        JsonResponse update = orderInfoItemFeign.batchCreateOrUpdate(items);
        if (update == null) {
            throw BusinessException.fail("更新订单商品失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail("更新订单商品失败：" + update.getMsg());
        }
//        items.stream().forEach(i-> i.setVersion(i.getVersion()+1));
//        ThreadLocals.put(items.get(0).getOrderNo() + WorkflowConstant.ANNTO_ORDER+"items", items);
    }

    public void clear(String workflowTicket){
        if (StringUtils.isBlank(workflowTicket)){
            return;
        }
        ThreadLocals.remove(workflowTicket + WorkflowConstant.CUSTOMER_ORDER);
        ThreadLocals.remove(workflowTicket + WorkflowConstant.CUSTOMER_ORDER+"items");
        ThreadLocals.remove(workflowTicket + WorkflowConstant.CUSTOMER_ORDER+"address");
        ThreadLocals.remove(workflowTicket + WorkflowConstant.ANNTO_ORDER);
        ThreadLocals.remove(workflowTicket + WorkflowConstant.ANNTO_ORDER+"items");
        ThreadLocals.remove(workflowTicket + WorkflowConstant.ANNTO_ORDER+"address");
    }


    public Map<String, Object> getZeebeResponseParam(Object obj){
        ZeebeParamDto zeebeParamDto = new ZeebeParamDto();
        BeanUtils.copyProperties(obj,zeebeParamDto);
        Map<String, Object> paramMap = new HashMap<>();
        try {
            paramMap = MapToBeanUtil.convertBean2Map(zeebeParamDto);

        }catch (Exception e){
            log.error(e.getMessage());
        }

        return paramMap;
    }
}
