package com.midea.logistics.otp.orderverify.start;

import java.util.UUID;

import com.midea.logistics.otp.common.helper.PreFlowEndHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.common.service.FlowService;
import com.midea.logistics.otp.enums.OrderOperateType;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.OrderLogHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.mq.producer.ExpressOrderFlowProducer;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.orderverify.service.*;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目名称：logistics-otp
 * 功能说明：快递流程帮助类
 * <AUTHOR>
 * @createtime 2021/10/4 15:18
 */
@Component
@Slf4j
public class ExpressFlowStart implements FlowService {
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private CustomerOrderInfoMapping customerOrderInfoMapping;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private CargoRightTransferService cargoRightTransferService;
    @Autowired
    private ExpressAddressService addressService;
    @Autowired
    private AssignWarehouseService separateWarehouseService;
    @Autowired
    private ExpressOrderFlowProducer expressOrderFlowProducer;
    @Autowired
    private OrderLogHelper orderLogHelper;
    @Autowired
    private BusinessCategoryService businessCategoryService;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private StandardProductAnalysisService standardProductAnalysisService;
    @Autowired
    private PreFlowEndHelper preFlowEndHelper;
    /**
     * 启动流程
     * @param flowTemplateCode
     * @param flowTicket
     * @return
     */
    public Integer startFlow(String parentOrderNo) {

        boolean lock = redisLockHelper.tryLock(parentOrderNo);
        if (!lock){
            log.error("====>ExpressFlowStart ticket {} faild to get lock!", parentOrderNo);
            return 1;
        }
        log.info("====>ExpressFlowStart ticket {} would be run!", parentOrderNo);
        Thread.currentThread().setName("hand_"+parentOrderNo+ "_" +  UUID.randomUUID().toString().replace("-",""));

        CustomerOrderInfoExt customerOrderInfoExt = orderverifyHelper.getCustomerOrderInfoExt(parentOrderNo);
        this.action(customerOrderInfoExt);

        return 1;
    }

    public Integer startFlow(CustomerOrderInfo customerOrderInfo) {

        boolean lock = redisLockHelper.tryLock(customerOrderInfo.getOrderNo());
        if (!lock){
            log.error("====>ExpressFlowStart ticket {} faild to get lock!", customerOrderInfo.getOrderNo());
            return 1;
        }
        log.info("====>ExpressFlowStart ticket {} would be run!", customerOrderInfo.getOrderNo());
        Thread.currentThread().setName("hand_"+customerOrderInfo.getOrderNo()+ "_" +  UUID.randomUUID().toString().replace("-",""));

        CustomerOrderInfoExt customerOrderInfoExt = orderverifyHelper.getCustomerOrderInfoExt(customerOrderInfo);
        this.action(customerOrderInfoExt);

        return 1;
    }

    @Override
    public void action(CustomerOrderInfoExt customerOrderInfoExt) {
        try {
            //1.订单映射
            customerOrderInfoMapping.mappingCustomerOrderInfo(customerOrderInfoExt);

            //2.订单审核
            cargoRightTransferService.auditItemsByFlow(customerOrderInfoExt);

            //3.地址映射
            addressService.addressMapping(customerOrderInfoExt);

            //4.业务大类解析
            //2023年7月3日10:48:48 杨顺怀：停止‘业务大类’的解析
//            businessCategoryService.businessCategory(customerOrderInfoExt);

            //4.标准产品解析
            standardProductAnalysisService.standardProductAnalysis(customerOrderInfoExt);

            //5.分仓
            FlowListenerParam automatic = separateWarehouseService.automatic(customerOrderInfoExt);
            if (FlowStatus.HOLD == automatic.getFlowStatus()){
                orderLogHelper.saveLog(customerOrderInfoExt, OrderOperateType.getOrderOperateType(automatic.getNodeCode()), CommonConstant.STRING_FLAG_YES,automatic.getErrorMsg());
                return;
            }

            //父单审核流程后置处理
            preFlowEndHelper.handleFlowEndPreCustomerOrderInfo(customerOrderInfoExt, customerOrderInfoExt.getCustomerOrderInfoExtend(), customerOrderInfoExt.getOrderNo(), customerOrderInfoExt.getCustomerOrderAddress());
    
            //清除小电快递流程redis节点缓存
            redisHelper.expireExpressFlowNode(customerOrderInfoExt.getOrderNo());

        } catch (Exception e) {
            log.error("ExpressFlowStart error e:{}",e);
            e.printStackTrace();
        }finally {
            //清理线程
            orderFlowHelper.clear(customerOrderInfoExt.getOrderNo());
            log.warn("clearOrderCache-> orderNo:{}",customerOrderInfoExt.getOrderNo());
        }
    }
}
