<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-toms" name="子订单流程-toms" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1x5qry3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_02zzxtp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
          <zeebe:output source="=responseBody.deliveryType" target="orderInfo.deliveryType" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1x5qry3</bpmn:incoming>
      <bpmn:outgoing>Flow_0829vy2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_10qm32l</bpmn:incoming>
      <bpmn:outgoing>Flow_0qr8gl4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_09m8o30" name="网点解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="nethttp" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisNetRange" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0829vy2</bpmn:incoming>
      <bpmn:outgoing>Flow_054qlvp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0hvdqty" name="计费相关">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/anyBmsInfo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0qr8gl4</bpmn:incoming>
      <bpmn:outgoing>Flow_10mwatt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1kkl3ix" name="推送查单系统,bms,net,task">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bmsLotsNetTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_10mwatt</bpmn:incoming>
      <bpmn:outgoing>Flow_02zzxtp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02zzxtp" sourceRef="Activity_1kkl3ix" targetRef="Event_14d5skw" />
    <bpmn:sequenceFlow id="Flow_1x5qry3" sourceRef="StartEvent_1" targetRef="Activity_087r4hi" />
    <bpmn:sequenceFlow id="Flow_0829vy2" sourceRef="Activity_087r4hi" targetRef="Activity_09m8o30" />
    <bpmn:sequenceFlow id="Flow_0qr8gl4" sourceRef="Activity_1fv890g" targetRef="Activity_0hvdqty" />
    <bpmn:sequenceFlow id="Flow_10mwatt" sourceRef="Activity_0hvdqty" targetRef="Activity_1kkl3ix" />
    <bpmn:sequenceFlow id="Flow_054qlvp" sourceRef="Activity_09m8o30" targetRef="Activity_00hamdf" />
    <bpmn:serviceTask id="Activity_00hamdf" name="电商分类解析1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/ecommerceCategoriesAnalysisTag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_054qlvp</bpmn:incoming>
      <bpmn:outgoing>Flow_10qm32l</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_10qm32l" sourceRef="Activity_00hamdf" targetRef="Activity_1fv890g" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-toms">
      <bpmndi:BPMNEdge id="Flow_10qm32l_di" bpmnElement="Flow_10qm32l">
        <di:waypoint x="620" y="117" />
        <di:waypoint x="650" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_054qlvp_di" bpmnElement="Flow_054qlvp">
        <di:waypoint x="480" y="117" />
        <di:waypoint x="520" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10mwatt_di" bpmnElement="Flow_10mwatt">
        <di:waypoint x="900" y="117" />
        <di:waypoint x="960" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qr8gl4_di" bpmnElement="Flow_0qr8gl4">
        <di:waypoint x="750" y="117" />
        <di:waypoint x="800" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0829vy2_di" bpmnElement="Flow_0829vy2">
        <di:waypoint x="320" y="117" />
        <di:waypoint x="380" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x5qry3_di" bpmnElement="Flow_1x5qry3">
        <di:waypoint x="172" y="117" />
        <di:waypoint x="220" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02zzxtp_di" bpmnElement="Flow_02zzxtp">
        <di:waypoint x="1060" y="117" />
        <di:waypoint x="1102" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="136" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="143" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="1102" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1109" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="220" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="650" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09m8o30_di" bpmnElement="Activity_09m8o30">
        <dc:Bounds x="380" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0hvdqty_di" bpmnElement="Activity_0hvdqty">
        <dc:Bounds x="800" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kkl3ix_di" bpmnElement="Activity_1kkl3ix">
        <dc:Bounds x="960" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_00hamdf_di" bpmnElement="Activity_00hamdf">
        <dc:Bounds x="520" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
