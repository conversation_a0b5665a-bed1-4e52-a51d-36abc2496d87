<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all-ofc" name="父订单流程-ofc" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_08mw2j3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0d15yei" name="商品确认1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mapping/byOrderNo" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0hjv9lz</bpmn:incoming>
      <bpmn:outgoing>Flow_0echylv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0echylv" sourceRef="Activity_0d15yei" targetRef="Gateway_1wccxzd" />
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1oyv8yd" sourceRef="Activity_1k6ho42" targetRef="Activity_0ur58zi" />
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping/byOrderNo" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
          <zeebe:output source="=responseBody.orderType" target="customerOrderInfo.orderType" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08mw2j3</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyv8yd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0nccbee" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="receive" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1uewv0z</bpmn:incoming>
      <bpmn:outgoing>Flow_07qlm7g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_119zmlg" name="仓间调拨解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/allocationParse/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="allocationParse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1n6udmd</bpmn:incoming>
      <bpmn:outgoing>Flow_0v8tmtg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/addressResolving/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k9k1b7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wi0xbe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kptmf2" name="货权转移校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/cargo/right/transfer/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p7943l</bpmn:incoming>
      <bpmn:outgoing>Flow_1wuusbt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0kj1gz5</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1g2qvk8" name="是否仓间调拨" default="Flow_1odmwry">
      <bpmn:incoming>Flow_07qlm7g</bpmn:incoming>
      <bpmn:incoming>Flow_1eh4qu3</bpmn:incoming>
      <bpmn:outgoing>Flow_1n6udmd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1odmwry</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07qlm7g" sourceRef="Activity_0nccbee" targetRef="Gateway_1g2qvk8" />
    <bpmn:sequenceFlow id="Flow_1n6udmd" name="yes" sourceRef="Gateway_1g2qvk8" targetRef="Activity_119zmlg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO") or (customerOrderInfo.sourceSystem="TTX" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AO")) or (customerOrderInfo.sourceSystem="OFC" and customerOrderInfo.orderType="AO"))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_04dmfyf" name="是否地址解析" default="Flow_191u9lk">
      <bpmn:incoming>Flow_1odmwry</bpmn:incoming>
      <bpmn:incoming>Flow_0v8tmtg</bpmn:incoming>
      <bpmn:outgoing>Flow_191u9lk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k9k1b7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1odmwry" sourceRef="Gateway_1g2qvk8" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_0v8tmtg" sourceRef="Activity_119zmlg" targetRef="Gateway_04dmfyf" />
    <bpmn:exclusiveGateway id="Gateway_0fo5mo8" name="是否货权转移" default="Flow_1o83mga">
      <bpmn:incoming>Flow_1np6vg1</bpmn:incoming>
      <bpmn:outgoing>Flow_1o83mga</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p7943l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_191u9lk" name="no" sourceRef="Gateway_04dmfyf" targetRef="Activity_07ox4pt" />
    <bpmn:sequenceFlow id="Flow_1wi0xbe" sourceRef="Activity_1i377su" targetRef="Activity_07ox4pt" />
    <bpmn:exclusiveGateway id="Gateway_1b565p2" name="是否分仓拆单">
      <bpmn:incoming>Flow_1o83mga</bpmn:incoming>
      <bpmn:incoming>Flow_1wuusbt</bpmn:incoming>
      <bpmn:outgoing>Flow_0kj1gz5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o83mga" name="no" sourceRef="Gateway_0fo5mo8" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_1k9k1b7" name="yes" sourceRef="Gateway_04dmfyf" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and (((customerOrderInfo.deliveryType!=null)=false or (customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT")) and (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO") and (customerOrderInfo.orderSource!=null and customerOrderInfo.orderSource!="HANDLE")))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1p7943l" name="yes" sourceRef="Gateway_0fo5mo8" targetRef="Activity_0kptmf2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CAINIAOPO","CCSJDPI","CCSPO","CCSPI","CIMSPO","CIMSAI","CIMSPI","CIMSRI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wuusbt" sourceRef="Activity_0kptmf2" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_0kj1gz5" name="no" sourceRef="Gateway_1b565p2" targetRef="Activity_0uruim6" />
    <bpmn:serviceTask id="Activity_0ur58zi" name="订单信息校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/orderInfoConfirm/byOrderNo" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="orderInfoConfirm" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1oyv8yd</bpmn:incoming>
      <bpmn:outgoing>Flow_0hjv9lz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1wccxzd" name="B2C" default="Flow_0bcco7j">
      <bpmn:incoming>Flow_0echylv</bpmn:incoming>
      <bpmn:outgoing>Flow_1xpxeay</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bcco7j</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xpxeay" name="yes" sourceRef="Gateway_1wccxzd" targetRef="Activity_039ag6m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo.businessMode="B2C")=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_039ag6m" name="件型计算">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="pieceAnalysis" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/pieceAnalysisOld/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xpxeay</bpmn:incoming>
      <bpmn:outgoing>Flow_0dxxyux</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Event_09zbrsb" />
    <bpmn:exclusiveGateway id="Gateway_1yfd9e1" name="是否走收货单位解析" default="Flow_1eh4qu3">
      <bpmn:incoming>Flow_0dxxyux</bpmn:incoming>
      <bpmn:incoming>Flow_0bcco7j</bpmn:incoming>
      <bpmn:outgoing>Flow_1uewv0z</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eh4qu3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1uewv0z" name="yes" sourceRef="Gateway_1yfd9e1" targetRef="Activity_0nccbee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CIMSPO","CCSPO","CAINIAOAO","CAINIAOYS","CIMSRI","OFCPO","OFCAO"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1eh4qu3" name="no" sourceRef="Gateway_1yfd9e1" targetRef="Gateway_1g2qvk8" />
    <bpmn:serviceTask id="Activity_07ox4pt" name="业务大类解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/businessCategory/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wi0xbe</bpmn:incoming>
      <bpmn:incoming>Flow_191u9lk</bpmn:incoming>
      <bpmn:outgoing>Flow_1np6vg1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_08mw2j3" sourceRef="StartEvent_1" targetRef="Activity_1k6ho42" />
    <bpmn:sequenceFlow id="Flow_0hjv9lz" sourceRef="Activity_0ur58zi" targetRef="Activity_0d15yei" />
    <bpmn:sequenceFlow id="Flow_0dxxyux" sourceRef="Activity_039ag6m" targetRef="Gateway_1yfd9e1" />
    <bpmn:sequenceFlow id="Flow_0bcco7j" sourceRef="Gateway_1wccxzd" targetRef="Gateway_1yfd9e1" />
    <bpmn:sequenceFlow id="Flow_1np6vg1" sourceRef="Activity_07ox4pt" targetRef="Gateway_0fo5mo8" />
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all-ofc">
      <bpmndi:BPMNEdge id="Flow_1np6vg1_di" bpmnElement="Flow_1np6vg1">
        <di:waypoint x="1790" y="390" />
        <di:waypoint x="1790" y="470" />
        <di:waypoint x="1555" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bcco7j_di" bpmnElement="Flow_0bcco7j">
        <di:waypoint x="840" y="230" />
        <di:waypoint x="1126" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dxxyux_di" bpmnElement="Flow_0dxxyux">
        <di:waypoint x="891" y="194" />
        <di:waypoint x="891" y="210" />
        <di:waypoint x="1146" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hjv9lz_di" bpmnElement="Flow_0hjv9lz">
        <di:waypoint x="500" y="230" />
        <di:waypoint x="656" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08mw2j3_di" bpmnElement="Flow_08mw2j3">
        <di:waypoint x="158" y="230" />
        <di:waypoint x="257" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eh4qu3_di" bpmnElement="Flow_1eh4qu3">
        <di:waypoint x="1151" y="205" />
        <di:waypoint x="1151" y="150" />
        <di:waypoint x="1360" y="150" />
        <di:waypoint x="1360" y="225" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1249" y="132" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uewv0z_di" bpmnElement="Flow_1uewv0z">
        <di:waypoint x="1176" y="230" />
        <di:waypoint x="1218" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1189" y="207" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="1110" y="470" />
        <di:waypoint x="988" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xpxeay_di" bpmnElement="Flow_1xpxeay">
        <di:waypoint x="815" y="205" />
        <di:waypoint x="815" y="154" />
        <di:waypoint x="841" y="154" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="822" y="173" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kj1gz5_di" bpmnElement="Flow_0kj1gz5">
        <di:waypoint x="1315" y="470" />
        <di:waypoint x="1210" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1293" y="513" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wuusbt_di" bpmnElement="Flow_1wuusbt">
        <di:waypoint x="1379" y="620" />
        <di:waypoint x="1340" y="620" />
        <di:waypoint x="1340" y="495" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p7943l_di" bpmnElement="Flow_1p7943l">
        <di:waypoint x="1530" y="495" />
        <di:waypoint x="1530" y="620" />
        <di:waypoint x="1479" y="620" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1537" y="557" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9k1b7_di" bpmnElement="Flow_1k9k1b7">
        <di:waypoint x="1555" y="230" />
        <di:waypoint x="1740" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1587" y="213" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o83mga_di" bpmnElement="Flow_1o83mga">
        <di:waypoint x="1505" y="470" />
        <di:waypoint x="1365" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1442" y="452" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wi0xbe_di" bpmnElement="Flow_1wi0xbe">
        <di:waypoint x="1790" y="270" />
        <di:waypoint x="1790" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191u9lk_di" bpmnElement="Flow_191u9lk">
        <di:waypoint x="1530" y="255" />
        <di:waypoint x="1530" y="338" />
        <di:waypoint x="1740" y="338" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1634" y="316" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v8tmtg_di" bpmnElement="Flow_0v8tmtg">
        <di:waypoint x="1511" y="151" />
        <di:waypoint x="1530" y="151" />
        <di:waypoint x="1530" y="205" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1odmwry_di" bpmnElement="Flow_1odmwry">
        <di:waypoint x="1405" y="230" />
        <di:waypoint x="1505" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n6udmd_di" bpmnElement="Flow_1n6udmd">
        <di:waypoint x="1380" y="205" />
        <di:waypoint x="1380" y="151" />
        <di:waypoint x="1411" y="151" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1385" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07qlm7g_di" bpmnElement="Flow_07qlm7g">
        <di:waypoint x="1318" y="230" />
        <di:waypoint x="1355" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyv8yd_di" bpmnElement="Flow_1oyv8yd">
        <di:waypoint x="357" y="230" />
        <di:waypoint x="400" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0echylv_di" bpmnElement="Flow_0echylv">
        <di:waypoint x="756" y="230" />
        <di:waypoint x="790" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="122" y="212" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="129" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xzxgxd_di" bpmnElement="Activity_0d15yei">
        <dc:Bounds x="656" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="952" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="959" y="428" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="257" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g3mnwv_di" bpmnElement="Activity_0nccbee">
        <dc:Bounds x="1218" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s5niqa_di" bpmnElement="Activity_119zmlg">
        <dc:Bounds x="1411" y="111" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="1740" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07zfya5_di" bpmnElement="Activity_0kptmf2">
        <dc:Bounds x="1379" y="580" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="1110" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g2qvk8_di" bpmnElement="Gateway_1g2qvk8" isMarkerVisible="true">
        <dc:Bounds x="1355" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1350" y="262" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04dmfyf_di" bpmnElement="Gateway_04dmfyf" isMarkerVisible="true">
        <dc:Bounds x="1505" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1538" y="203" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fo5mo8_di" bpmnElement="Gateway_0fo5mo8" isMarkerVisible="true">
        <dc:Bounds x="1505" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1497.5" y="421" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b565p2_di" bpmnElement="Gateway_1b565p2" isMarkerVisible="true">
        <dc:Bounds x="1315" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1309" y="424" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0wx9b29_di" bpmnElement="Activity_0ur58zi">
        <dc:Bounds x="400" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1wccxzd_di" bpmnElement="Gateway_1wccxzd" isMarkerVisible="true">
        <dc:Bounds x="790" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="804" y="262" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ztbyvv_di" bpmnElement="Activity_039ag6m">
        <dc:Bounds x="841" y="114" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yfd9e1_di" bpmnElement="Gateway_1yfd9e1" isMarkerVisible="true">
        <dc:Bounds x="1126" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1114" y="262" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w1igdz_di" bpmnElement="Activity_07ox4pt">
        <dc:Bounds x="1740" y="310" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
