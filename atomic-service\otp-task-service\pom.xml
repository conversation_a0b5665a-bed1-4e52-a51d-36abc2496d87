<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.midea.logistics.otp</groupId>
        <artifactId>atomic-service</artifactId>
        <version>1.0.1</version>
    </parent>

    <groupId>com.midea.logistics.otp</groupId>
    <artifactId>otp-task-service</artifactId>
    <version>1.0.1</version>

    <properties>
    </properties>

    <dependencies>
        <!--
        <dependency>
            <groupId>com.mideaframework</groupId>
            <artifactId>midea-dts-seata-common</artifactId>
            <version>${midea-dts.seata.version}</version>
        </dependency>
        -->
    </dependencies>

</project>
