<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.CustomerSoldToConfigExtMapper">

    <sql id="searchFieldsSql">
            `order_type` AS  orderType,
            `source_system` AS  sourceSystem,
            `create_user_code` AS  createUserCode,
            `customer_code` AS  customerCode,
            `update_time` AS  updateTime,
            `sold_to_name` AS  soldToName,
            `remark` AS  remark,
            `version` AS  version,
            `business_classification` AS  businessClassification,
            `customer_name` AS  customerName,
            `delete_flag` AS  deleteFlag,
            `enabled_falg` AS  enabledFalg,
            `update_user_code` AS  updateUserCode,
            `create_time` AS  createTime,
            `sold_to_code` AS  soldToCode,
            `id` AS  id
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="orderType !=null and orderType != ''">
                        and `order_type` =#{orderType}
                    </if>
                    <if test="sourceSystem !=null and sourceSystem != ''">
                        and `source_system` =#{sourceSystem}
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        and `customer_code` =#{customerCode}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="soldToName !=null and soldToName != ''">
                        and `sold_to_name` =#{soldToName}
                    </if>
                    <if test="remark !=null">
                        and `remark` =#{remark}
                    </if>
                    <if test="businessClassification !=null and businessClassification != ''">
                        and `business_classification` =#{businessClassification}
                    </if>
<!--                    <if test="customerName !=null and customerName != ''">
                        and `customer_name` =#{customerName}
                    </if>-->
                    <if test="enabledFalg !=null">
                        and `enabled_falg` =#{enabledFalg}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="soldToCode !=null and soldToCode != ''">
                        and `sold_to_code` =#{soldToCode}
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
    </sql>


    <select id="selectConfig" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerSoldToConfig">
        select
        <include refid="searchFieldsSql"/>
        from customer_sold_to_config
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">${orderByType}</if>
        </if>
        limit 1
    </select>

    <!--查询批量参数查询集合 这个查询只是为了前端增改查重 所以必填字段直接判断-->
    <select id="searchListForInsertOrUpdateByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerSoldToConfig">
        select
        <include refid="searchFieldsSql"/>
        from customer_sold_to_config
        where delete_flag=0
        and source_system in
        <foreach collection="sourceSystems" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and sold_to_code in
        <foreach collection="soldToCodes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

    </select>

</mapper>