package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialNoAndConstractNoSyncDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialOrderSyncItemDto;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * Author:   zhangjh
 * Date:     2019-07-01 17:20
 * Description:
 */
public interface ITaskSynFinancialNoService {

    public void synFinancialNo(List<FinancialOrderSyncItemDto> itemDtoList);

    void synContractNo(FinancialNoAndConstractNoSyncDto financialNoAndConstractNoSyncDto);

}
