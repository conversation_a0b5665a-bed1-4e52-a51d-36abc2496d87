<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.TcRuleMapper">

    <update id="batchUpdateCanSetEmpty">
        <foreach collection="list" item="item" separator=";">
            UPDATE tc_rule
            <include refid="setBatchFieldsSqlCanSetEmpty"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

</mapper>