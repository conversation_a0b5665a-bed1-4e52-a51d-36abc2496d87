package com.midea.logistics.otp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.*;
import com.midea.logistics.domain.mdm.domain.*;
import com.midea.logistics.dtc.dto.CustSkuInfo;
import com.midea.logistics.dtc.dto.MidCdWhSkuDto;
import com.midea.logistics.logisticsbopsdk.bean.ItemSync;
import com.midea.logistics.logisticsbopsdk.bean.QueryItemParam;
import com.midea.logistics.logisticsbopsdk.service.OtpService;
import com.midea.logistics.otp.common.bean.ExceptionInfoDto;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.enums.ExceptionInfoEnum;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbShippersFeign;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.helper.FlowRuleHelper;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderItemFeign;
import com.midea.logistics.otp.order.common.fegin.DtcServiceFeign;
import com.midea.logistics.otp.order.common.fegin.OmsCdCommonMaterialFegin;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.OrderItemSyncHelper;
import com.midea.logistics.otp.order.common.helper.OrderMappingHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.service.CNCustSkuService;
import com.midea.logistics.otp.order.common.service.CenterInvService;
import com.midea.logistics.otp.order.converged.domain.request.OrderItemRequest;
import com.midea.logistics.otp.order.converged.domain.response.MaterialLuggageRelation;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.request.CustomerOrderInfoUpdateRequest;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.CargoRightTransferService;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * Author:   zhangjh
 * Date:     2019-06-25 10:51
 * Description: 货权转移
 */
@Component
@Slf4j
public class CargoRightTransferServiceImpl implements CargoRightTransferService {

    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private CenterInvService centerInvService;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;
    @Autowired
    private OmsCdCommonMaterialFegin omsCdCommonMaterialFegin;
    @Autowired
    private CdCommonMaterialManager cdCommonMaterialManager;
    @Autowired
    private EbMaterialGroupManager ebMaterialGroupManager;
    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private FlowRuleHelper flowRuleHelper;
    @Autowired
    private MidB2cGoodstypeControlManager midB2cGoodstypeControlManager;
    @Autowired
    private CdWhBomDetailManager cdWhBomDetailManager;
    @Autowired
    private DtcServiceFeign dtcServiceFeign;
    @Autowired
    private OtpService otpService;
    @Autowired
    private OrderItemSyncHelper orderItemSyncHelper;
    @Autowired
    private MidB2cGoodsControlManager midB2cGoodsControlManager;
    @Autowired
    private CdCommonMaterialCustManager cdCommonMaterialCustManager;
    @Autowired
    private OrderMappingHelper orderMappingHelper;
    @Autowired
    private MidB2cCustomerControlManager midB2cCustomerControlManager;

    @Autowired
    private EbShippersFeign ebShippersFeign;
    @Autowired
    private CNCustSkuService cnCustSkuService;
    @Autowired
    private RedisHelper redisHelper;
    @Override
    @ExpressFlow(node = OrderOperateType.ORDER_CONFIRM)
    public FlowListenerParam  auditItemsByFlow(CustomerOrderInfoExt customerOrderInfoExt) throws BusinessException {

        //查询客户订单
        CustomerOrderInfo customerOrderInfo = customerOrderInfoExt;
        CustomerOrderInfo oldData = new CustomerOrderInfo();
        BeanUtils.copyProperties(customerOrderInfoExt, oldData);

        //订单明细映射
        List<CustomerOrderItem> customerOrderItems = customerOrderInfoExt.getCustomerOrderItems();
        List<CustomerOrderItem> customerOrderItemList = new ArrayList<>();
        List<CustomerOrderItem> customerBomItemList = new ArrayList<>();
        List<CustomerOrderItem> deleteItems = new ArrayList<>();
        List<CustomerOrderItem> allItems = new ArrayList<>();

        if (CollectionUtils.isEmpty(customerOrderItems)) {
            throw BusinessException.fail("订单无商品: " + customerOrderInfo.getOrderNo());
        }

        String sourceSys = customerOrderInfoExt.getSourceSystem();
        String upperCustCode = customerOrderInfoExt.getUpperCustomerCode();
        StringBuffer errorMsg = new StringBuffer();

        ExceptionInfoDto exceptionInfoDto = new ExceptionInfoDto();

        //订单明细映射
        getBomList(customerOrderInfoExt, customerOrderItems, customerOrderItemList, customerBomItemList, deleteItems, errorMsg);

        Boolean isGoodsMapFlag = customerOrderInfoExt.getIsGoodsMapFlag();
        if(null == isGoodsMapFlag) {
            MidB2cCustomerControl midB2cCustomerControl = midB2cCustomerControlManager.getMidB2cCustControlCache(upperCustCode, sourceSys);
            if (midB2cCustomerControl == null || StringUtils.isEmpty(midB2cCustomerControl.getAnnCustomerCode())) {
                log.warn("upperCustCode:{} is not mapping in midB2cCustomer mapping config.......", upperCustCode);
                //异常处理
                errorMsg.append("对照表找不到对应客户映射主数据. 上游客户编码:" + upperCustCode);
                throw BusinessException.fail("对照表找不到对应客户映射主数据. 上游客户编码:" + upperCustCode);
            }
            isGoodsMapFlag = midB2cCustomerControl.getGoodsMapFlag().intValue() == 0 ? true : false;
        }

        //非bom商品编码对照 、商品状态对照
        for (CustomerOrderItem customerOrderItem : customerOrderItemList) {
            try {
                innerMaterialForCMapping(customerOrderInfoExt, errorMsg, sourceSys, upperCustCode, isGoodsMapFlag, customerOrderItem);
            } catch (BusinessException be) {
                exceptionInfoDto.addExceptionMap(be.getMessage());
            }

        }

        //bom商品状态映射
        for (CustomerOrderItem customerOrderItem : customerBomItemList) {
            //内部商品状态、商品主数据映射为内部商品
            try {
                innerMaterialForCMapping(customerOrderInfoExt, errorMsg, sourceSys, upperCustCode, isGoodsMapFlag, customerOrderItem);
            } catch (BusinessException be) {
                exceptionInfoDto.addExceptionMap(be.getMessage());
            }
        }

        //校验是否存在报错，统一抛出去
        exceptionInfoDto.checkResult();

        //奇门根据散件获取套件编码
        if(SourceSystem.QIMEN.getKey().equals(sourceSys)){
            orderMappingHelper.getBomInfoBySubCode(customerOrderInfoExt,customerOrderItemList);
        }

        maxItems(allItems, customerBomItemList, deleteItems, customerOrderItemList);


        //货值
        BigDecimal orderValue = BigDecimal.ZERO;

        // * 总体积
        BigDecimal totalVolume = BigDecimal.ZERO;
        // * 总毛重
        BigDecimal totalGrossWeight = BigDecimal.ZERO;

        //计划件数
        BigDecimal totalPlanQty = BigDecimal.ZERO;
        // * 总净重
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        //总包件数
        BigDecimal totalPkgQty = BigDecimal.ZERO;

        //需要删除的明细
        ArrayList<CustomerOrderItem> deleteItem = new ArrayList<CustomerOrderItem>();

        // 2020-12-23 家居订单不校验单体毛重、单体体积、体积、毛重
        boolean HLFlag = ProjectClassifyEnum.HL.getKey().equals(customerOrderInfo.getProjectClassify());
        // 2021-07-20 虚拟商品不需要重新设置体积毛重,以输入值为准
        boolean cdcmFictitiousFlag = ProjectClassifyEnum.CDCM_FICTITIOUS.getKey().equals(customerOrderInfo.getProjectClassify());
        // 虚拟商品（取订单体积重量）
        boolean virtualItemFlag = orderverifyHelper.checkVirtual(customerOrderInfo);

        List<String> failItem = Lists.newArrayList();
        for (CustomerOrderItem item : allItems) {
            //判断订单明细中安得商品编码是否为空
            CdCommonMaterial cdCommonMaterial = null;
            if (null != item.getItemCode()) {
                //有安得商品编码，用安得编码查询出计费等商品信息
                cdCommonMaterial = cdCommonMaterialManager.getCdCommonMaterialCache(item.getItemCode());
            } else {
                //无安得编码，根据customerCode,coustmerItemCode 遍历查询出计费等商品信息
                OrderItemRequest orderItemRequest = new OrderItemRequest();
                orderItemRequest.setCustomerCode(customerOrderInfo.getCustomerCode());
                orderItemRequest.setCustomerItemCode(CommonConstant.urlEncode(item.getCustomerItemCode()));
                JsonResponse<CdCommonMaterial> cdCommonMaterialResponse = omsCdCommonMaterialFegin.getCdCommonMaterialByCustomerCodeAndCustomerItemCode(orderItemRequest);
                cdCommonMaterial = cdCommonMaterialResponse.data;
            }
            if (item.getPlanQty() == null || BigDecimal.ZERO.compareTo(item.getPlanQty()) >= 0){
                deleteItem.add(item);
            }
            //计算订单
            totalPlanQty = totalPlanQty.add(item.getPlanQty());
            if (null == cdCommonMaterial) {
                //zhm:若没有客户商品编码，取上游商品编码
                failItem.add(StringUtils.isNotBlank(item.getCustomerItemCode()) ? item.getCustomerItemCode() : item.getUpperItemCode());
                continue;
            }
            //商品编码
            item.setItemCode(cdCommonMaterial.getCdcmMaterialNo());
            //商品名称
            item.setItemName(cdCommonMaterial.getCdcmNameCn());
            //商品品类
            item.setItemClass(cdCommonMaterial.getCdcmTypeCode());
            //获取事业部实体
            if (StringUtils.isNotEmpty(cdCommonMaterial.getCdcmTypeCode())) {
                EbMaterialGroup ebMaterialGroup = ebMaterialGroupManager.getEbMaterialGroupCache(cdCommonMaterial.getCdcmTypeCode());
                if (ebMaterialGroup != null) {
                    item.setBuCode(ebMaterialGroup.getEbcuBuCode());
                    item.setItemClassName(ebMaterialGroup.getEbmgNameCn());
                }
            }
            //商品件型
            item.setItemSize(cdCommonMaterial.getCdcmSizeIdentification());

            //订单明细有一个需要安装，则订单就需要安装
            if (null != item.getInstallFlag() && 1 == (item.getInstallFlag())) {
                customerOrderInfo.setInstallFlag(1);
            }

            //包件数计算
            if (null != item.getPkgQty()) {
                totalPkgQty = totalPkgQty.add(item.getPkgQty());
            }

            //2020-6-3 14:09:39 昌泰： 由于手工单可以修改重量和体积，因此这里有的话，不设置，没有的话按照原来的逻辑
            BigDecimal volume = item.getVolume();
            if (null != volume && volume.compareTo(BigDecimal.ZERO) > 0 ) {
                log.info("{}不设置体积", customerOrderInfo.getCustomerOrderNo());
            }else{
                // 体积
                item.setVolume(cdCommonMaterial.getCdcmCube() == null ? null : BigDecimal.valueOf(cdCommonMaterial.getCdcmCube()));
            }

            // 单台体积
            item.setSingleVolume(StringUtils.isBlank(cdCommonMaterial.getUserdefined16()) ? null : new BigDecimal(cdCommonMaterial.getUserdefined16()));
            // 体积相互赋值
            if (item.getVolume() == null || BigDecimal.ZERO.compareTo(item.getVolume()) == 0){ item.setVolume(item.getSingleVolume()); }
            if (item.getSingleVolume() == null || BigDecimal.ZERO.compareTo(item.getSingleVolume()) == 0){ item.setSingleVolume(item.getVolume()); }

            //家居体积为0不用取基础数据 手工单
            if ((HLFlag || cdcmFictitiousFlag) && null != volume && BigDecimal.ZERO.compareTo(volume) == 0 && SourceSystem.HANDLE.getKey().equals(customerOrderInfo.getSourceSystem())) {
                item.setVolume(volume);
            }

            // 净重
            item.setNetWeight(cdCommonMaterial.getCdcmNetWeight() == null ? null : BigDecimal.valueOf(cdCommonMaterial.getCdcmNetWeight()));

            //2020-6-3 14:09:39 昌泰： 由于手工单可以修改重量和体积，因此这里有的话，不设置，没有的话按照原来的逻辑
            BigDecimal grossWeight = item.getGrossWeight();
            if (null != grossWeight && grossWeight.compareTo(BigDecimal.ZERO) > 0 ) {
                log.info("{}不设置重量", customerOrderInfo.getCustomerOrderNo());
            }else{
                // 毛重
                item.setGrossWeight(cdCommonMaterial.getCdcmWeight() == null ? null : BigDecimal.valueOf(cdCommonMaterial.getCdcmWeight()));
            }

            // 单台毛重
            item.setSingleWeight(StringUtils.isBlank(cdCommonMaterial.getUserdefined17()) ? null : new BigDecimal(cdCommonMaterial.getUserdefined17()));

            // 重量相互赋值
            BigDecimal weight = item.getNetWeight();
            if (weight == null || BigDecimal.ZERO.compareTo(weight) == 0) { weight = item.getGrossWeight();}
            if (weight == null || BigDecimal.ZERO.compareTo(weight) == 0) { weight = item.getSingleWeight();}

            if (item.getNetWeight() == null || BigDecimal.ZERO.compareTo(item.getNetWeight()) == 0){ item.setNetWeight(weight);}
            if (item.getGrossWeight() == null || BigDecimal.ZERO.compareTo(item.getGrossWeight()) == 0){ item.setGrossWeight(weight);}
            if (item.getSingleWeight() == null || BigDecimal.ZERO.compareTo(item.getSingleWeight()) == 0){ item.setSingleWeight(weight);}

            //家居毛重为0不用取基础数据 手工单
            if ((HLFlag || cdcmFictitiousFlag) && null != grossWeight && BigDecimal.ZERO.compareTo(grossWeight) == 0 && SourceSystem.HANDLE.getKey().equals(customerOrderInfo.getSourceSystem())) {
                item.setGrossWeight(grossWeight);
            }

            //cdcm_Measure_Standard 重泡货标识
            //cdcm_Specification_Unit 规格单位
            //cdcm_Product_Specification 规格单位值
            item.setSpecificationUnit(cdCommonMaterial.getCdcmSpecificationUnit());
            item.setProductSpecification(cdCommonMaterial.getCdcmProductSpecification());
            item.setMeasureStandard(cdCommonMaterial.getCdcmMeasureStandard());
            //计算订单


            //2020-6-3 14:09:39 昌泰： 由于手工单可以修改重量和体积，因此这里有的话，直接用修改后的，没有的话按照去基础数据的
            if (item.getTotalVolume() != null) {
                totalVolume = totalVolume.add(item.getTotalVolume());
            } else if (null != item.getVolume() && item.getVolume().compareTo(BigDecimal.ZERO) > 0 ) {
                BigDecimal volumeTemp = item.getPlanQty().multiply(item.getVolume());
                totalVolume = totalVolume.add(volumeTemp);
            }else{
                if (cdCommonMaterial.getCdcmCube() != null) {
                    BigDecimal volumeTemp = item.getPlanQty().multiply(BigDecimal.valueOf(cdCommonMaterial.getCdcmCube()));
                    totalVolume = totalVolume.add(volumeTemp);
                }
            }

            //2020-6-3 14:09:39 昌泰：  由于手工单可以修改重量和体积，因此这里有的话，直接用修改后的，没有的话按照去基础数据的
            if (item.getTotalGrossWeight() != null) {
                totalGrossWeight = totalGrossWeight.add(item.getTotalGrossWeight());
            } else if (null != item.getGrossWeight() && item.getGrossWeight().compareTo(BigDecimal.ZERO) > 0 ) {
                BigDecimal grossWeightTemp = item.getPlanQty().multiply(item.getGrossWeight());
                totalGrossWeight = totalGrossWeight.add(grossWeightTemp);
            }else{
                if (cdCommonMaterial.getCdcmCube() != null && cdCommonMaterial.getCdcmWeight() != null) {
                    BigDecimal grossWeightTemp = item.getPlanQty().multiply(BigDecimal.valueOf(cdCommonMaterial.getCdcmWeight()));
                    totalGrossWeight = totalGrossWeight.add(grossWeightTemp);
                }
            }

            //2021-7-8 10:35:27 如果数据字典配置虚拟商品的话,且头表有总重量/总体积 取上游下单的数据
            if (virtualItemFlag){
                if (null != customerOrderInfo.getTotalGrossWeight()) {
                    totalGrossWeight = customerOrderInfo.getTotalGrossWeight();
                }
                if (null != customerOrderInfo.getTotalVolume()) {
                    totalVolume = customerOrderInfo.getTotalVolume();
                }
            }

            //2020-6-5 18:11:07 昌泰：加上总净重
            if (null != item.getNetWeight()) {
                BigDecimal netWeightTemp = item.getPlanQty().multiply(item.getNetWeight());
                totalNetWeight = totalNetWeight.add(netWeightTemp);
            }


            //国际条形码
            item.setBarcode69(cdCommonMaterial.getCdcmBarcode69());

            //计费组1~5
            item.setMaterialGroup1(cdCommonMaterial.getCdcmMaterialGroup1());
            item.setMaterialGroup2(cdCommonMaterial.getCdcmMaterialGroup2());
            item.setMaterialGroup3(cdCommonMaterial.getCdcmMaterialGroup3());
            item.setMaterialGroup4(cdCommonMaterial.getCdcmMaterialGroup4());
            item.setMaterialGroup5(cdCommonMaterial.getCdcmMaterialGroup5());
            //计费组7~8
            item.setMaterialGroup7(cdCommonMaterial.getCdcmMaterialGroup7());
            item.setMaterialGroup8(cdCommonMaterial.getCdcmMaterialGroup8());

        }


        //2020-4-1 15:53:53 昌泰：如果有一个商品计划数量为0，则delete_flag置为1，并判断是否所有商品delete_falg都被置为1，是则冲销。
        if(!CollectionUtils.isEmpty(deleteItem)){
            //删除明细
            if (deleteItem.size() >= allItems.size()){
                customerOrderItemFeign.deleteBatch(deleteItem);
                //更新为冲销
                customerOrderInfo.setOrderStatus(OrderStatus.CANCEL.getKey());
                customerOrderInfo.setExcuteStatus(OrderStatus.CANCEL.getKey());
                customerOrderInfo.setInterceptFlag(CustomerOrderStatus.CANCEL.getKey());
                customerOrderInfoFeign.update(customerOrderInfo.getId(), customerOrderInfo);
                throw BusinessException.fail("此订单全部商品计划数量为0,已冲销");
            //直接内存删除
            }else{
                for (CustomerOrderItem del : deleteItem) {
                    allItems.remove(del);
                }
            }
        }

        if (ToolUtils.isNotEmpty(failItem)){
            throw BusinessException.fail(String.format("商品校验失败,商品编码:%s", JSON.toJSONString(failItem)));
        }

        if (BigDecimal.ZERO.compareTo(totalPlanQty) >= 0){
            throw BusinessException.fail(String.format("商品数量应该大于0:%s", customerOrderInfo.getOrderNo()));
        }

        //家居订单，如果传了总体积总重量则用主表的值，不用计算
        //虚拟商品
        if (HLFlag || cdcmFictitiousFlag) {
            if (null != customerOrderInfo.getTotalGrossWeight()) {
                totalGrossWeight = customerOrderInfo.getTotalGrossWeight();
            }
            if (null != customerOrderInfo.getTotalVolume()) {
                totalVolume = customerOrderInfo.getTotalVolume();
            }
            if (null != customerOrderInfo.getTotalPkgQty()) {
                totalPkgQty = customerOrderInfo.getTotalPkgQty();
            }
        }

        //计算后的总件数
        customerOrderInfo.setTotalQty(totalPlanQty);
        //总体积
        customerOrderInfo.setTotalVolume(totalVolume);
        //总毛重
        customerOrderInfo.setTotalGrossWeight(totalGrossWeight);
        //总净重
        customerOrderInfo.setTotalNetWeight(totalNetWeight);
        //总包件数
        customerOrderInfo.setTotalPkgQty(totalPkgQty);

        //费标准规则判断
        if(StringUtils.isEmpty(customerOrderInfo.getFreightBasis())){
            //3.有重量有体积 体积/重量(转换为吨)<=3.5重货，否则轻货
            if(totalGrossWeight.compareTo(BigDecimal.ZERO)>0 && totalVolume.compareTo(BigDecimal.ZERO)>0){
               BigDecimal sub = new BigDecimal("3.5");
               log.info("totalVolume:{},totalGrossWeight:{}",totalVolume.doubleValue(),totalGrossWeight.doubleValue());
                BigDecimal tTotalGrossWeight = totalGrossWeight.divide(new BigDecimal("1000"),8, RoundingMode.HALF_UP);
                if(tTotalGrossWeight.compareTo(BigDecimal.ZERO)<=0){
                    throw BusinessException.fail("毛重["+totalGrossWeight.toString()+"]近似为0，请修改商品的毛重数值");
                }
                BigDecimal divideRs = totalVolume.divide(tTotalGrossWeight,8,RoundingMode.HALF_UP);
               if(divideRs.compareTo(sub)<=0){
                    customerOrderInfo.setFreightBasis(FreightBasisEnum.HEAVY.getKey());
                }else{
                    customerOrderInfo.setFreightBasis(FreightBasisEnum.Light.getKey());
                }
                //1.有重量无体积--重货
            }else if(totalGrossWeight.compareTo(BigDecimal.ZERO)>0 && totalVolume.compareTo(BigDecimal.ZERO)==0){
                customerOrderInfo.setFreightBasis(FreightBasisEnum.HEAVY.getKey());
                //2.无重量有体积--轻货
            }else if(totalGrossWeight.compareTo(BigDecimal.ZERO)==0 && totalVolume.compareTo(BigDecimal.ZERO)>0){
                customerOrderInfo.setFreightBasis(FreightBasisEnum.Light.getKey());
            }

        }

        //订单信息
        JsonResponse jsonResponse = updateMappingInfo(customerOrderInfo, allItems, customerBomItemList, deleteItems);
        //对更新的结果进行检测 20211213 fix
        if(!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())){
            throw BusinessException.fail("更新订单映射失败:"+JSON.toJSONString(jsonResponse));
        }
        return FlowListenerParam.success("成功");
    }


    private JsonResponse updateMappingInfo(CustomerOrderInfo customerOrderInfo, List<CustomerOrderItem> allItems, List<CustomerOrderItem> customerBomItemList, List<CustomerOrderItem> deleteItems) {
        CustomerOrderInfoUpdateRequest customerOrderInfoUpdateRequest = new CustomerOrderInfoUpdateRequest();
        if (!CollectionUtils.isEmpty(deleteItems)) {
            customerOrderInfoUpdateRequest.setCustomerOrderItemsDel(deleteItems);
        }
        //插入套件明细
        if (!CollectionUtils.isEmpty(customerBomItemList)) {
            List<CustomerOrderItem> insertList = allItems.stream().filter(a -> null == a.getId()).collect(Collectors.toList());
            customerOrderInfoUpdateRequest.setCustomerOrderItemsInsert(insertList);
        }
        //更新行号与映射
        if (!CollectionUtils.isEmpty(allItems)) {
            List<CustomerOrderItem> updatetList = allItems.stream().filter(a -> null != a.getId()).collect(Collectors.toList());
            customerOrderInfoUpdateRequest.setCustomerOrderItemsUpdate(updatetList);
        }

        //更新主表对照
        customerOrderInfo.setVersion(null);
        customerOrderInfoUpdateRequest.setCustomerOrderInfo(customerOrderInfo);
        return customerOrderInfoFeign.updateCustomerOrderInfoWithDetail(customerOrderInfoUpdateRequest);
    }


    /**
     * 整合明细
     * @param allItems
     * @param customerBomItemList
     * @param deleteItems
     * @param customerOrderItemList
     */
    private void maxItems(List<CustomerOrderItem> allItems, List<CustomerOrderItem> customerBomItemList, List<CustomerOrderItem> deleteItems, List<CustomerOrderItem> customerOrderItemList) {

        if(CollectionUtils.isEmpty(deleteItems)){
            allItems.addAll(customerOrderItemList);
        } else {
            for (CustomerOrderItem item : customerOrderItemList) {
                boolean isDel = false;
                for (CustomerOrderItem dItem : deleteItems) {
                    if (item.getId().equals(dItem.getId())) {
                        isDel = true;
                        break;
                    }
                }
                if (!isDel) {
                    allItems.add(item);
                }
            }
        }
        if(!CollectionUtils.isEmpty(customerBomItemList)) {
            allItems.addAll(customerBomItemList);
        }
    }


    @Data
    private class AnntoOrder {
        private String orderNo;
        private CustomerOrderInfo customerOrderInfo;
        private OrderInfo orderInfo;
        private List<OrderInfoItem> orderInfoItems;
        private boolean isOccupy;
        private String whCode;

        private String msg;

        public AnntoOrder(String orderNo, CustomerOrderInfo customerOrderInfo) {
            this.orderNo = orderNo;
            this.customerOrderInfo = customerOrderInfo;
        }
        public AnntoOrder(String orderNo, String whCode, CustomerOrderInfo customerOrderInfo) {
            this.whCode = whCode;
            this.orderNo = orderNo;
            this.customerOrderInfo = customerOrderInfo;
        }
        public boolean getIsOccupy(){
            return isOccupy;
        }

        public AnntoOrder invoke() {
            orderInfo = new OrderInfo();
            //根据customerOrderInfo生成orderInfo及其商品明细，orderInfo.orderNo调用生成订单号方法，获取新的订单号；先不保存
            BeanUtils.copyProperties(customerOrderInfo, orderInfo,"id");
            if (ToolUtils.isNotEmpty(whCode)){
                orderInfo.setWhCode(whCode);
            }
            orderInfo.setWhName(cdWarehouseManager.getCdWarehouseNameByWhCode(orderInfo.getWhCode()));
            //设置orderInfo.parentOrderNo = customerOrderInfo.orderNo
            String customerOrderNo = customerOrderInfo.getCustomerOrderNo();
            orderInfo.setParentOrderNo(customerOrderInfo.getOrderNo());
            String newOrderNo =  flowRuleHelper.getAnOrderNo(customerOrderInfo);
            orderInfo.setTenantCode(customerOrderInfo.getTenantCode());
            orderInfo.setOrderNo(newOrderNo);
            List<CustomerOrderItem> data = orderFlowHelper.listCustomerOrderItem(orderNo);
            orderInfoItems = Lists.newArrayList();
            //数量
            BigDecimal totalQty = BigDecimal.ZERO;
            //体积
            BigDecimal totalVolume = BigDecimal.ZERO;
            //重量
            BigDecimal totalGrossWeight = BigDecimal.ZERO;
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(data)) {
                for (CustomerOrderItem customerOrderItem : data) {
                    OrderInfoItem orderInfoItem = new OrderInfoItem();
                    BeanUtils.copyProperties(customerOrderItem, orderInfoItem,"id","cancleQty");

                    orderInfoItem.setOrderNo(newOrderNo);
                    orderInfoItem.setParentOrderNo(orderInfo.getParentOrderNo());
                    BigDecimal planQty = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(),BigDecimal.ZERO));
                    if (ToolUtils.isNotEmpty(customerOrderItem.getCancleQty())){
                        orderInfoItem.setRemark("取消的数量："+ ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(),BigDecimal.ZERO));
                    }
                    orderInfoItem.setPlanQty(planQty);
                    //计算订单
                    totalQty = totalQty.add(orderInfoItem.getPlanQty());
                    BigDecimal volumeTemp = orderInfoItem.getPlanQty().multiply(ObjectUtils.defaultIfNull(orderInfoItem.getVolume(),BigDecimal.ZERO));
                    if (orderInfoItem.getTotalVolume() != null) {
                        volumeTemp = orderInfoItem.getTotalVolume();
                    }
                    BigDecimal grossWeightTemp = orderInfoItem.getPlanQty().multiply(ObjectUtils.defaultIfNull(orderInfoItem.getGrossWeight(),BigDecimal.ZERO));
                    if (orderInfoItem.getTotalGrossWeight() != null) {
                        grossWeightTemp = orderInfoItem.getTotalGrossWeight();
                    }
                    totalVolume = totalVolume.add(volumeTemp);
                    totalGrossWeight = totalGrossWeight.add(grossWeightTemp);
                    orderInfoItems.add(orderInfoItem);

                }
            }

            //计算订单数量，体积，重量
            orderInfo.setTotalQty(totalQty);
            orderInfo.setTotalVolume(totalVolume);
            orderInfo.setTotalGrossWeight(totalGrossWeight);
            OrderInfoExt orderInfoExt = new OrderInfoExt();
            orderInfoExt.setOrderInfoItems(orderInfoItems);
            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);
            if (OrderType.RI.getKey().equals(customerOrderInfo.getOrderType())
            && JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType())) {
                orderInfoExt.setCustomerCode(orderInfoExt.getTargetCustomerCode());
            }else{
                if (InOutType.IN.getName().equals(customerOrderInfo.getInOutType())){
                    isOccupy = true;
                    return this;
                }
            }
            isOccupy = isInventoryPreemption(orderInfoExt, this);
            return this;
        }

    }



    public boolean isInventoryPreemption(OrderInfoExt orderInfoExt, AnntoOrder anntoOrder) {
//        centerInvService.proHoldInv()
        //占用库存
        JsonResponse jsonResponse = null;
        //占用库存
        try {
            jsonResponse = centerInvService.proHoldInv(orderInfoExt, CenterInvType.PLAN_OMS_OUT.getKey());
            log.info("占用的-调用中央库存--------"+jsonResponse);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            JsonResponse jsonResponse2 = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
            log.info("取消占用的---------"+jsonResponse2);
            throw e;
        }
        //占用库存
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            Object data = jsonResponse.getData();
            if (ToolUtils.isNotEmpty(data)) {
                anntoOrder.setMsg(data.toString());
            }
            JsonResponse jsonResponse2 = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
            log.info("取消占用的---------"+jsonResponse2);
        }

        return BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode());
    }



    private void validateStatus(Integer status,String code,String msg){
        if (CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(status))) {
            throw BusinessException.fail("["+msg +":{"+ code + "}]");
        }
    }


    private void innerMaterialForCMapping(CustomerOrderInfoExt customerOrderInfoExt, StringBuffer errorMsg, String sourceSys, String upperCustCode, boolean isGoodsMapFlag, CustomerOrderItem customerOrderItem) {
        String upperItemCode = customerOrderItem.getUpperItemCode();
        String upperItemStatus = customerOrderItem.getUpperItemStatus();
        //是否需要商品对照:对照中：MID_GOODS_MAP_FLAG: {0: "是", 1: "否"},套散件也需要对照
        if (isGoodsMapFlag) {
            MidB2cGoodsControl midB2cGoodsControl = midB2cGoodsControlManager.getMidB2cGoodsControlCache(upperCustCode, upperItemCode);
            if (midB2cGoodsControl == null || StringUtils.isEmpty(midB2cGoodsControl.getAnnGoodsCode())) {
                errorMsg.append("对照表找不到对应商品主数据. 商品编码:" + upperItemCode);
                throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.ITEM_NULL2.getValue(), upperCustCode, upperItemCode));
            }
            //校验状态
            validateStatus(midB2cGoodsControl.getStatus(),upperItemCode,ExceptionInfoEnum.ITEM_DROP.getValue());
            //商品编码映射
            customerOrderItem.setItemCode(midB2cGoodsControl.getAnnGoodsCode());
            customerOrderItem.setItemName(midB2cGoodsControl.getAnnGoodsName());

            CdCommonMaterial cdCommonMaterial = cdCommonMaterialManager.getCdCommonMaterialCache(midB2cGoodsControl.getAnnGoodsCode());
            //如果是商品映射，则还需要查询商品主数据做商品映射
            mappingMaterial(customerOrderInfoExt, cdCommonMaterial, customerOrderItem, upperItemCode, errorMsg, isGoodsMapFlag);
        } else {
            CdCommonMaterial cdCommonMaterial = cdCommonMaterialCustManager.getCdCommonMaterialByCustItemAndOwnerFromCache(upperItemCode, customerOrderInfoExt.getCustomerCode());
            //映射商品编码
            mappingMaterial(customerOrderInfoExt, cdCommonMaterial, customerOrderItem, upperItemCode, errorMsg, isGoodsMapFlag);
        }
        //商品状态映射
        MidB2cGoodstypeControl midB2cGoodstypeControl = midB2cGoodstypeControlManager.getMidB2cGoodstypeControlCache(sourceSys, upperItemStatus);
        if (midB2cGoodstypeControl != null && StringUtils.isNotEmpty(midB2cGoodstypeControl.getAnnGoodstypeCode())) {
            //校验状态
            validateStatus(midB2cGoodstypeControl.getStatus(),upperItemStatus, ExceptionInfoEnum.ITEM_STATUS_DROP.getValue());
            customerOrderItem.setItemStatus(midB2cGoodstypeControl.getAnnGoodstypeCode());
        } else {
            MidB2cGoodstypeControl midB2cGoodstypeControl2 = midB2cGoodstypeControlManager.getMidB2cGoodstypeControlCache(upperCustCode, upperItemStatus);
            if (midB2cGoodstypeControl2 == null || StringUtils.isEmpty(midB2cGoodstypeControl2.getAnnGoodstypeCode())) {
                errorMsg.append("找不到对应商品状态主数据. 货物状态:" + upperItemStatus);
                throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.ITEM_STATUS_NULL2.getValue(), upperCustCode, upperItemStatus));
            }
            //校验状态
            validateStatus(midB2cGoodstypeControl2.getStatus(),upperItemStatus, ExceptionInfoEnum.ITEM_STATUS_DROP.getValue());
            customerOrderItem.setItemStatus(midB2cGoodstypeControl2.getAnnGoodstypeCode());
        }
    }


    private void setItemInfo(CdWhBomDetail cdWhBomDetail, CustomerOrderItem custOrderItem, Integer lineNo) {
        //设置内部行号
        custOrderItem.setItemLineNo(lineNo);
        BigDecimal qty = cdWhBomDetail.getQty();
        BigDecimal EAQty = cdWhBomDetail.getQtyEa();
        //散件商品数量=套件数量*散件比例(一个套件包含多少该散件)
        BigDecimal sourcePlanQty = custOrderItem.getPlanQty();
        custOrderItem.setPlanQty(qty.multiply(sourcePlanQty));
        //取散件的上游商品编码
        custOrderItem.setItemSuiteCode(custOrderItem.getUpperItemCode());
        //设置上游编码为外部客户商品编码
        custOrderItem.setUpperItemCode(cdWhBomDetail.getCdcmCustMaterialNo());
        custOrderItem.setItemSuiteQty(EAQty==null?null:EAQty.intValue());
        custOrderItem.setId(null);//后面做对照
        custOrderItem.setMainItemCode(cdWhBomDetail.getParentSkuCode());
    }

    private void getBomList(CustomerOrderInfoExt customerOrderInfoExt, List<CustomerOrderItem> srcCustOrderItems, List<CustomerOrderItem> targetCustOrderItemList, List<CustomerOrderItem> customerBomItemList, List<CustomerOrderItem> deleteItems, StringBuffer errorMsg) {
        int lineNo = 1;
        ExceptionInfoDto exceptionInfoDto = new ExceptionInfoDto();
        for (CustomerOrderItem customerOrderItem : srcCustOrderItems) {
            try {
                //如果是CIMS平台，先校验商品是否套件，是套件则打上套件的标
                if(SourceSystem.CIMS.getKey().equals(customerOrderInfoExt.getSourceSystem())){
                    CdCommonMaterial cdCommonMaterial = cdCommonMaterialCustManager.getCdCommonMaterialByCustItemAndOwnerFromCache(customerOrderItem.getUpperItemCode(), customerOrderInfoExt.getCustomerCode());
                    if(cdCommonMaterial==null){
                        JsonResponse<CdCommonMaterial> jsonResp = omsCdCommonMaterialFegin.getCdCommonMaterialByCustItemCodeForCIMSAndCCS(customerOrderItem.getUpperItemCode());
                        if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResp.getCode()) && jsonResp.getData()!=null){
                            cdCommonMaterial = jsonResp.getData();
                        }
                    }
                    if(cdCommonMaterial!=null && CommonEnum.Y.getKey().equals(cdCommonMaterial.getCdcmIsParent())){
                        customerOrderItem.setSetFlag(CommonEnum.YES.getValue());
                    }
                }
                //商品对照:套散件映射,是套散件则拆散
                if (customerOrderItem.getSetFlag() != null && CommonEnum.YES.getValue().intValue() == customerOrderItem.getSetFlag().intValue()) {
                    //先查询出内部商品编码，再根据内部商品编码查找套件
                    CdCommonMaterial cdCommonMaterial = cdCommonMaterialCustManager.getCdCommonMaterialByCustItemAndOwnerFromCache(customerOrderItem.getUpperItemCode(), customerOrderInfoExt.getCustomerCode());
                    if (cdCommonMaterial == null) {
                        if(SourceSystem.CIMS.getKey().equals(customerOrderInfoExt.getSourceSystem()) || SourceSystem.CCS.getKey().equals(customerOrderInfoExt.getSourceSystem())){
                            JsonResponse<CdCommonMaterial> jsonResp = omsCdCommonMaterialFegin.getCdCommonMaterialByCustItemCodeForCIMSAndCCS(customerOrderItem.getUpperItemCode());
                            if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResp.getCode()) && jsonResp.getData()!=null){
                                cdCommonMaterial = jsonResp.getData();
                            }else{
                                throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.ITEM_NULL6.getValue(), customerOrderItem.getUpperItemCode()));
                            }
                        }else {
                            throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.ITEM_NULL6.getValue(), customerOrderItem.getUpperItemCode()));
                        }
                    }

                    List<CdWhBomDetail> cdWhBomDetails = cdWhBomDetailManager.getCdWhBomDetailCache(cdCommonMaterial.getCdcmMaterialNo());
                    if (cdWhBomDetails == null || cdWhBomDetails.isEmpty()) {
                        errorMsg.append("[找不到对应商品套件. 商品编码:" + customerOrderItem.getItemCode() + "]");
                        throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.BOM_NULL.getValue(), StringUtils.isNotBlank(customerOrderItem.getItemCode()) ? customerOrderItem.getItemCode() : customerOrderItem.getUpperItemCode()));
                    }
                    //CIMS套件数量，用于套件价格只给其中一个散件
                    int bomDetailCount = 0;
                    for (CdWhBomDetail cdWhBomDetail : cdWhBomDetails) {

                        if (cdWhBomDetail.getSubSkuCode() == null) {
                            errorMsg.append("散件编码为空.");
                            throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.BOM_SUB_NULL2.getValue(), cdCommonMaterial.getCdcmMaterialNo()));
                        }
                        if (cdWhBomDetail.getQty() == null) {
                            errorMsg.append("散件数量为空.");
                            throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.BOM_SUB_QTY_NULL.getValue(), cdCommonMaterial.getCdcmMaterialNo()));
                        }

                        CustomerOrderItem custOrderItem = new CustomerOrderItem();
                        BeanUtils.copyProperties(customerOrderItem, custOrderItem);
                        setItemInfo(cdWhBomDetail, custOrderItem, lineNo);
                        //CIMS套件数量，用于套件价格只给其中一个散件,只取价格为其中一个散件中
                        if(SourceSystem.CIMS.getKey().equals(customerOrderInfoExt.getSourceSystem()) && bomDetailCount>0){
                            custOrderItem.setPrice(BigDecimal.ZERO);
                        }
                        bomDetailCount ++;
                        customerBomItemList.add(custOrderItem);
                        lineNo++;
                    }
                    //获取套件后逻辑删除原来的商品
                    deleteItems.add(customerOrderItem);
                } else {
                    customerOrderItem.setItemLineNo(lineNo);
                    targetCustOrderItemList.add(customerOrderItem);
                    lineNo++;
                }
            } catch (BusinessException be) {
                exceptionInfoDto.addExceptionMap(be.getMessage());
            }

        }
        //校验是否存在报错，统一抛出去
        exceptionInfoDto.checkResult();
    }

    private void mappingMaterial(CustomerOrderInfoExt customerOrderInfoExt, CdCommonMaterial cdCommonMaterial, CustomerOrderItem customerOrderItem, String upperItemCode, StringBuffer errorMsg, boolean isGoodsMap) {
        if (cdCommonMaterial == null || StringUtils.isEmpty(cdCommonMaterial.getCdcmMaterialNo())) {
            //调用接口去拉取商品信息
            CdCommonMaterial syncItem = itemSync(customerOrderInfoExt, customerOrderItem);
            if (syncItem != null) {
                cdCommonMaterial = syncItem;
            } else {
                errorMsg.append("[找不到对应商品主数据. 商品编码:" + upperItemCode + "]");
                throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.ITEM_NULL.getValue(), upperItemCode));
            }
        }

        if (String.valueOf(CommonMdmEnum.DISABLE.getValue()).equals(cdCommonMaterial.getIsEnable())) {
            errorMsg.append("[商品未启用. 商品编码:" + upperItemCode + "]");
            throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.ITEM_DISABLE.getValue(), upperItemCode));

        }
        if (!CommonMdmEnum.AUDIT_PASS.getValue().equals(cdCommonMaterial.getCdcmAuditStatus())) {
            errorMsg.append("[商品未审核. 商品编码:" + upperItemCode + "]");
            throw BusinessException.fail(ExceptionInfoEnum.getExceptionMsg(ExceptionInfoEnum.ITEM_UN_AUDIT.getValue(), upperItemCode));
        }
        if (CommonConstant.Y.equals(orderverifyHelper.checkVirtualMaterial(customerOrderInfoExt, cdCommonMaterial,customerOrderItem))) {
            return;
        }
        customerOrderItem.setItemCode(cdCommonMaterial.getCdcmMaterialNo());
        customerOrderItem.setCustomerItemCode(cdCommonMaterial.getCdcmCustMaterialNo());
        customerOrderItem.setItemName(cdCommonMaterial.getCdcmNameCn());
        //设置商品长宽高重量体积等属于
        //商品品类
        customerOrderItem.setItemClass(cdCommonMaterial.getCdcmTypeCode());
        //商品件型
        customerOrderItem.setItemSize(cdCommonMaterial.getCdcmSizeIdentification());
        //毛重
        customerOrderItem.setGrossWeight(ToolUtils.isEmpty(cdCommonMaterial.getCdcmWeight() )? null : BigDecimal.valueOf(cdCommonMaterial.getCdcmWeight()));
        //净重
        customerOrderItem.setNetWeight(ToolUtils.isEmpty(cdCommonMaterial.getCdcmNetWeight() ) ? null : BigDecimal.valueOf(cdCommonMaterial.getCdcmNetWeight()));
        //体积
        customerOrderItem.setVolume(ToolUtils.isEmpty(cdCommonMaterial.getCdcmCube() )? null : BigDecimal.valueOf(cdCommonMaterial.getCdcmCube()));
        //国际条形码
        customerOrderItem.setBarcode69(cdCommonMaterial.getCdcmBarcode69());
        //计费组1~5
        customerOrderItem.setMaterialGroup1(cdCommonMaterial.getCdcmMaterialGroup1());
        customerOrderItem.setMaterialGroup2(cdCommonMaterial.getCdcmMaterialGroup2());
        customerOrderItem.setMaterialGroup3(cdCommonMaterial.getCdcmMaterialGroup3());
        customerOrderItem.setMaterialGroup4(cdCommonMaterial.getCdcmMaterialGroup4());
        customerOrderItem.setMaterialGroup5(cdCommonMaterial.getCdcmMaterialGroup5());
        //计费组7~8
        customerOrderItem.setMaterialGroup7(cdCommonMaterial.getCdcmMaterialGroup7());
        customerOrderItem.setMaterialGroup8(cdCommonMaterial.getCdcmMaterialGroup8());
        //增加单台体积重量
        customerOrderItem.setSingleVolume(ToolUtils.isEmpty(cdCommonMaterial.getUserdefined16()) ? null : new BigDecimal(cdCommonMaterial.getUserdefined16()));
        customerOrderItem.setSingleWeight(ToolUtils.isEmpty(cdCommonMaterial.getUserdefined17()) ? null : new BigDecimal(cdCommonMaterial.getUserdefined17()));
        customerOrderItem.setProductSpecification(cdCommonMaterial.getCdcmProductSpecification());
        customerOrderItem.setMeasureStandard(cdCommonMaterial.getCdcmMeasureStandard());
        customerOrderItem.setSpecificationUnit(cdCommonMaterial.getCdcmSpecificationUnit());
        customerOrderItem.setBrand(cdCommonMaterial.getCdcmBrand());
        //小件则设置箱规数量
        if(SourceSystem.CAINIAO.getKey().equals(customerOrderInfoExt.getSourceSystem()) && StringUtils.isNotEmpty(customerOrderInfoExt.getGrayFlag()) && ItemStatus.AH.getKey().equals(customerOrderInfoExt.getGrayFlag())){
            setCNPackingAmount(customerOrderInfoExt, customerOrderItem, upperItemCode);
        }
        //包装规格和包装单位
        customerOrderItem.setCdpaFormat(cdCommonMaterial.getCdcmPackageCode());
        customerOrderItem.setCdprDesc(cdCommonMaterial.getCdcmPackageUnitCode());
        //
        String fullDelivery  = cdCommonMaterial.getFullDelivery();
        if(CommonConstant.STRING_FLAG_YES.equals(fullDelivery)){
            customerOrderItem.setItemSplitTaskFlag(CommonConstant.FLAG_YES);
        }
        if (CommonConstant.FLAG_YES.equals(customerOrderItem.getItemSplitTaskFlag())){
            List<BusinessControlParamDetail> data = businessParamHelper.getBusinessControlDetailByIndex("ITEM_FCL_SPLIT_TASK", customerOrderInfoExt);
            if (org.springframework.util.CollectionUtils.isEmpty(data)) {
                log.info("单号{} 未获取到控制参数",customerOrderInfoExt.getOrderNo());
                return ;
            }
            MaterialLuggageRelation cdWhPackageRelation = this.getCdWhPackageRelation(customerOrderItem.getUpperItemCode(), customerOrderInfoExt.getCustomerCode());
            if (CommonEnum.Y.getKey().equals(cdWhPackageRelation.getCdpaIsDecimal())) {
                customerOrderItem.setPackingDecimalAmount(cdWhPackageRelation.getCdprDecimalQuantity());
            } else {
                customerOrderItem.setPackingDecimalAmount(cdWhPackageRelation.getCdprQuantity());
                customerOrderItem.setPackingAmount(cdWhPackageRelation.getCdprQuantity().intValue());
            }
        }


    }


    private void setCNPackingAmount(CustomerOrderInfoExt customerOrderInfoExt, CustomerOrderItem customerOrderItem, String upperItemCode) {
        CustSkuInfo custSkuInfo = new CustSkuInfo();
        custSkuInfo.setSender(SourceSystem.CAINIAO.getKey());
        custSkuInfo.setCustomerCode(customerOrderInfoExt.getUpperCustomerCode());
        custSkuInfo.setSkuCode(upperItemCode);

        if(redisHelper.redisCNCustSkuDC()) {
            JSONObject jsonObject = cnCustSkuService.queryCNCustSkuCode(custSkuInfo);

            if (StringUtils.isEmpty(jsonObject.getString("packingAmount"))) {
                throw BusinessException.fail("菜鸟小件箱规数量为空");
            }
            customerOrderItem.setPackingAmount(Integer.valueOf(jsonObject.getString("packingAmount")));
            return;
        }

        JsonResponse<MidCdWhSkuDto> jsonResponse =  dtcServiceFeign.queryCNCustSkuCode(custSkuInfo);
        if(jsonResponse==null || !BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode()) || null == jsonResponse.getData()){
            throw BusinessException.fail("获取菜鸟小件箱规数量失败:"+JSON.toJSONString(jsonResponse));
        }
        if(StringUtils.isEmpty(jsonResponse.getData().getPackingamount())){
            throw BusinessException.fail("菜鸟小件箱规数量为空");
        }
        customerOrderItem.setPackingAmount(Integer.valueOf(jsonResponse.getData().getPackingamount()));
    }


    private CdCommonMaterial itemSync(CustomerOrderInfoExt customerOrderInfoExt, CustomerOrderItem customerOrderItem) {
        String sourceSystem = customerOrderInfoExt.getSourceSystem();
        //非CIMS和CCS平台的不走同步接口同步基础数据
        if (!SourceSystem.CIMS.getKey().equals(sourceSystem) && !SourceSystem.CCS.getKey().equals(sourceSystem)) {
            return orderItemSyncHelper.syncItem(customerOrderInfoExt,customerOrderItem);
        }
        //同步商品前先查询商品是否存在CIMS和CCS客户组中，有则表示商品存在
        JsonResponse<CdCommonMaterial> jsonResp = omsCdCommonMaterialFegin.getCdCommonMaterialByCustItemCodeForCIMSAndCCS(customerOrderItem.getUpperItemCode());
        if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResp.getCode()) && jsonResp.getData()!=null){
            return jsonResp.getData();
        }
        CdCommonMaterial cdCommonMaterial = null;
        List<CustomerOrderItem> customerOrderItems =  customerOrderInfoExt.getCustomerOrderItems();
        for(CustomerOrderItem item:customerOrderItems){
            try{
                //先判断是否存在商品，有则不同步
                if(isExistItem(customerOrderInfoExt,item)){
                    continue;
                }
                //循环遍历同步
                QueryItemParam queryItemParam = new QueryItemParam();
                JsonResponse jsonResponse = null;
                queryItemParam.setAnntoCustomerCode(customerOrderInfoExt.getCustomerCode());
                queryItemParam.setCustomerCode(customerOrderInfoExt.getUpperCustomerCode());
                queryItemParam.setCustProdCode(item.getUpperItemCode());
                if (SourceSystem.CIMS.getKey().equals(sourceSystem)) {
                    queryItemParam.setEntityId(String.valueOf(customerOrderInfoExt.getEntityId()));
                    queryItemParam.setOwnerGroup("KH00012");
                    log.info("CIMS item sync param:{}",JSON.toJSON(queryItemParam));
                    jsonResponse = otpService.getCIMSItem(queryItemParam);

                } else {
                    log.info("CIMS item sync param:{}",JSON.toJSON(queryItemParam));
                    jsonResponse = otpService.getCCSItem(queryItemParam);
                }
                log.info("CIMS or CCS item sync response data :{}",JSON.toJSON(jsonResponse));
                if (jsonResponse!=null && BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode()) && jsonResponse.data() != null) {
                    ItemSync itemSync = (ItemSync) jsonResponse.data();
                    if ("T".equals(itemSync.getIsSuccess()) && StringUtils.isNotEmpty(itemSync.getAnnGoodsCode())) {
                        if(item.getUpperLineNo().equals(customerOrderItem.getUpperLineNo())){
                            cdCommonMaterial = cdCommonMaterialManager.getCdCommonMaterialCache(itemSync.getAnnGoodsCode());
                        }
                    } else {
                        log.error("[同步上游商品失败.:" + itemSync.getRepMsg() + "上游商品编码:" + customerOrderItem.getUpperItemCode() + "]");
                    }
                } else {
                    log.error("[同步上游商品失败. 上游商品编码:" + customerOrderItem.getUpperItemCode() + "]");
                }

            }catch (Exception e){
                log.error(e.getMessage());
            }
        }
        return cdCommonMaterial;
    }



    /**
     * 是否已存在商品
     * @param customerOrderInfoExt
     * @return
     */
    private boolean isExistItem(CustomerOrderInfoExt customerOrderInfoExt,CustomerOrderItem customerOrderItem){
        CdCommonMaterial cdCommonMaterial = cdCommonMaterialCustManager.getCdCommonMaterialByCustItemAndOwnerFromCache(customerOrderItem.getUpperItemCode(), customerOrderInfoExt.getCustomerCode());
        if(cdCommonMaterial!=null){
            return true;
        }
        JsonResponse<CdCommonMaterial> jsonResp = omsCdCommonMaterialFegin.getCdCommonMaterialByCustItemCodeForCIMSAndCCS(customerOrderItem.getUpperItemCode());
        if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResp.getCode()) && jsonResp.getData()!=null){
            return true;
        }
        return false;
    }



    /**
     * @description: MDM获取箱转换
     * @param: [cdcmCustMaterialNo, customerCode]
     * @return: java.math.BigDecimal
     * @author: 陈永培
     * @createtime: 2020/9/15 11:15
     */
    private BigDecimal getCdWhPackage(String upperItemCode, String customerCode) {
        BigDecimal packageQty = BigDecimal.ZERO;
        BigDecimal branchQty = BigDecimal.ZERO;
        JsonResponse<List<MaterialLuggageRelation>> response = ebShippersFeign.getLuggageConversion(upperItemCode, customerCode);
        if (response == null || !BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) || CollectionUtils.isEmpty(response.getData())) {
            throw BusinessException.fail("客户："+ customerCode + "客户商品编码："+upperItemCode + ",此商品为订单拆箱商品，请到LMDM包装 维护箱包关系" );
        }
        Optional<MaterialLuggageRelation> first = response.getData().stream().filter(o -> "CS".equals(o.getCdprUnit()))
            .findFirst();
        if (first.isPresent() && !(null == first.get().getCdprQuantity()) && !(
            BigDecimal.ZERO.compareTo(first.get().getCdprQuantity()) == 0)) {
            branchQty = first.get().getCdprQuantity();
        } else {
            throw BusinessException.fail("客户："+ customerCode + "客户商品编码："+upperItemCode + "查询箱包关系....【箱(CS)】的值为空,请到LMDM包装 维护箱包关系");
        }
        return branchQty;
    }

    /**
     * @description: MDM获取箱转换
     */
    private MaterialLuggageRelation getCdWhPackageRelation(String upperItemCode, String customerCode) {
        JsonResponse<List<MaterialLuggageRelation>> response = ebShippersFeign.getLuggageConversion(upperItemCode, customerCode);
        if (response == null || !BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) || CollectionUtils.isEmpty(response.getData())) {
            throw BusinessException.fail("客户："+ customerCode + "客户商品编码："+upperItemCode + ",此商品为订单拆箱商品，请到LMDM包装 维护箱包关系" );
        }
        Optional<MaterialLuggageRelation> first = response.getData().stream().filter(o -> "CS".equals(o.getCdprUnit()))
            .findFirst();
        if (first.isPresent()) {
            MaterialLuggageRelation mlRelation = first.get();
            if (CommonEnum.Y.getKey().equals(mlRelation.getCdpaIsDecimal())) {
                if (mlRelation.getCdprDecimalQuantity() != null && BigDecimal.ZERO.compareTo(mlRelation.getCdprDecimalQuantity()) < 0) {
                    return mlRelation;
                }
            } else {
                if (mlRelation.getCdprQuantity() != null && BigDecimal.ZERO.compareTo(mlRelation.getCdprQuantity()) < 0) {
                    return mlRelation;
                } 
            }
        }
        throw BusinessException.fail("客户："+ customerCode + "客户商品编码："+upperItemCode + "查询箱包关系....【箱(CS)】的值为空,请到LMDM包装 维护箱包关系");
    }
}
