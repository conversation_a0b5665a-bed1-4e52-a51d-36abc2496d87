<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.ApartRuleMapper">

    <sql id="searchFieldsSql">
        `delete_flag` AS  deleteFlag,
        `volume` AS  volume,
        `order_type` AS  orderType,
        `update_user_code` AS  updateUserCode,
        `create_time` AS  createTime,
        `source_system` AS  sourceSystem,
        `create_user_code` AS  createUserCode,
        `update_time` AS  updateTime,
        `remark` AS  remark,
        `id` AS  id,
        `version` AS  version,
        `apart_type` AS  apartType
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="volume !=null and volume != ''">
            and `volume` =#{volume}
        </if>
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and  `source_system`  =#{sourceSystem}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="apartType !=null and apartType != ''">
            and `apart_type` =#{apartType}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="volume !=null and volume != ''">
                `volume` = #{volume},
            </if>
            <if test="orderType !=null and orderType != ''">
                `order_type` = #{orderType},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="sourceSystem !=null and sourceSystem != ''">
                `source_system`  = #{sourceSystem},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="apartType !=null and apartType != ''">
                `apart_type` = #{apartType},
            </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from apart_rule t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.ApartRule">
        select
        <include refid="searchFieldsSql"/>
        from apart_rule t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.ApartRule">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from apart_rule t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.ApartRule">
        select
        <include refid="searchFieldsSql"/>
        from apart_rule t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        apart_rule t
        <include refid="setFieldsSql"/>
        where
        `id` = #{id}
    </update>

    <update id="deleteById">
        update
            apart_rule t
        set  `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.ApartRule" useGeneratedKeys="true"
            keyProperty="id">
        insert into apart_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="volume !=null and volume != ''">
                `volume`,
            </if>

            <if test="orderType !=null and orderType != ''">
                `order_type`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="sourceSystem !=null and sourceSystem != ''">
                `source_system`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="apartType !=null and apartType != ''">
                `apart_type`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="volume !=null and volume != ''">
                #{volume},
            </if>
            <if test="orderType !=null and orderType != ''">
                #{orderType},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="sourceSystem !=null and sourceSystem != ''">
                #{sourceSystem},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="apartType !=null and apartType != ''">
                #{apartType},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `volume`,
            `order_type`,
            `update_user_code`,
            `source_system`,
            `create_user_code`,
            `remark`,
            `apart_type`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.volume},
            #{item.orderType},
            #{item.updateUserCode},
            #{item.sourceSystem},
            #{item.createUserCode},
            #{item.remark},
            #{item.apartType},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        apart_rule
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.volume !=null and item.volume != ''">
                `volume` = #{item.volume},
            </if>
            <if test="item.orderType !=null and item.orderType != ''">
                `order_type` = #{item.orderType},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                `source_system`  = #{item.sourceSystem},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.apartType !=null and item.apartType != ''">
                `apart_type` = #{item.apartType},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.deleteFlag !=null">
                and `delete_flag` =#{item.deleteFlag}
            </if>
            <if test="item.volume !=null and item.volume != ''">
                and `volume` =#{item.volume}
            </if>
            <if test="item.orderType !=null and item.orderType != ''">
                and `order_type` =#{item.orderType}
            </if>
            <if test="item.createTime !=null">
                and `create_time` =#{item.createTime}
            </if>
            <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                and  `source_system`  =#{item.sourceSystem}
            </if>
            <if test="item.updateTime !=null">
                and `update_time` =#{item.updateTime}
            </if>
            <if test="item.remark !=null">
                and `remark` =#{item.remark}
            </if>
            <if test="item.version !=null">
                and `version` =#{item.version}
            </if>
            <if test="item.apartType !=null and item.apartType != ''">
                and `apart_type` =#{item.apartType}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE apart_rule
            <include refid="setBatchFieldsSql"/>
            where
            `id` = #{item.id}
        </foreach>

    </update>

    <update id="deleteBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE
            apart_rule t
            set `delete_flag`=#{item.deleteFlag}
            WHERE
            `id` = #{item.id}
        </foreach>
    </update>


</mapper>