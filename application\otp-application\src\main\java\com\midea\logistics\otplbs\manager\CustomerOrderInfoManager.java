package com.midea.logistics.otplbs.manager;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.midea.logistics.otp.report.service.domain.dto.OrderImportForm;
import com.midea.logistics.otplbs.manager.helper.OtpMessageHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.midea.logistics.cache.manager.CdOwnerGroupDetailManager;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.ControlParamManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.domain.mdm.domain.*;
import com.midea.logistics.domain.mdm.request.CdWarehouseRequest;
import com.midea.logistics.lc.file.sdk.builder.FileUploadBuilder;
import com.midea.logistics.lc.file.sdk.service.LcFileService;
import com.midea.logistics.otp.bean.dc.NewCdTsEquipmentGroup;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.ConvergedCustomerOrderFeign;
import com.midea.logistics.otp.common.feign.servicefeign.OmsCdCommonMaterialFegin;
import com.midea.logistics.otp.common.feign.servicefeign.dc.DcAtomicFeign;
import com.midea.logistics.otp.common.feign.servicefeign.dc.DcQueryFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.CdTsEquipmentGroupFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.CdWarehouseFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EsCompanyFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderItemFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.common.helper.bean.*;
import com.midea.logistics.otp.common.request.ContactsOms;
import com.midea.logistics.otp.common.utils.Assert;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.helper.CodeHelper;
import com.midea.logistics.otp.order.converged.domain.response.CustomerOrderInfoResponse;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.TmpAddress;
import com.midea.logistics.otp.order.domain.request.VagueRelationRequest;
import com.midea.logistics.otp.report.service.domain.request.CostOrderInfoImportFileRequest;
import com.midea.logistics.otplbs.fegin.CustomerOrderAddressFeign;
import com.midea.logistics.otplbs.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otplbs.fegin.OrderFeign;
import com.midea.logistics.otplbs.manager.helper.ExportOrderBusinessHelper;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.SpringContextHolder;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * @Program: logistics-otp
 * @ClassName: CustomerOrderInfoManager
 * @Author： JiaJun
 * @Date： 2019-07-30 14:10
 * @Description: 客户订单导入
 */
@Slf4j
@Component
public class CustomerOrderInfoManager {

    private static final Logger logger = LoggerFactory.getLogger(CustomerOrderInfoManager.class);



    // 仓库大小电配置缓存 MAP
    private static Map<String, Integer> WH_OUTCOLLAB_MAP = new HashMap<>();
    private static Map<String, Integer> SITE_OUTCOLLAB_MAP = new HashMap<>();


    //货值限制
    public static final String ORDER_VALUE_LIMIT="ORDER_VALUE_LIMIT";


    @Autowired
    private OrderFeign orderFeign;

    @Autowired
    private CdWarehouseManager cdWarehouseManager;

    @Autowired
    private CdTsEquipmentGroupFeign cdTsEquipmentGroupFeign;
    @Autowired
    Validatorhelper validatorhelper;

    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;

    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    private EsCompanyFeign esCompanyFeign;
    @Autowired
    private CdWarehouseFeign cdWarehouseFeign;
    @Autowired
    CdOwnerGroupDetailManager cdOwnerGroupDetailManager;
    @Autowired
    private OmsCdCommonMaterialFegin omsCdCommonMaterialFegin;

    @Autowired
    private EbCustomerManager ebCustomerManager;
    @Autowired
    private com.midea.logistics.otplbs.manager.helper.CheckCustomerAgingConfigExcel checkCustomerAgingConfigExcel;

    @Autowired
    private ControlParamManager controlParamManager;

    @Autowired
    private DcAtomicFeign dcAtomicFeign;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private CodeHelper codeHelper;
    @Autowired
    private ConvergedCustomerOrderFeign convergedCustomerOrderFeign;
    @Autowired
    private ExportOrderBusinessHelper exportOrderBusinessHelper;
    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private LcFileService lcFileService;
    @Autowired
    private ReportFeign reportFeign;
    @Autowired
    private DcQueryFeign dcQueryFeign;
    @Autowired
    private OtpMessageHelper otpMessageHelper;

    /**
     * 入库单模板导入
     *
     * @param file
     * @return
     * @throws IOException
     */
    public JsonResponse excelImport(@RequestParam("file") MultipartFile file, String inOutType) throws Exception {
        JsonResponse<Object> success = JsonResponse.success(null);
        int headerNum = 2 ;
        if (InOutType.YS.getName().equals(inOutType)){
            headerNum = 1 ;
        }
        ImportExcel importExcel = new ImportExcel(file, headerNum, 0, applicationContext);
        int lastCellNum = importExcel.getLastCellNum();
        Class clazz = OutCustomerOrderInfoExcelRequest.class;
        if (InOutType.IN.getName().equals(inOutType)){
            clazz = InCustomerOrderInfoExcelRequest.class;
            if (lastCellNum != 52){
                throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
            }
        } else if (InOutType.YS.getName().equals(inOutType)){
            clazz = YsCustomerOrderInfoExcelRequest.class;
            if (lastCellNum != 55){
                throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
            }
        } else {
            if (lastCellNum != 83){
                throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
            }
        }
        importExcel.setMaximumFractionDigits(6); //读取6位小数
        List<GroupExcelRequest> dataList = importExcel.getDataList(clazz, null);
        // logger.info("读取excel数据:{}", JSON.toJSONString(dataList));
        List<CustomerOrderInfoResponse> list = Lists.newArrayList();
        dataList = dataList.stream().filter(e -> ToolUtils.isNotEmpty(e.getCustomerOrderNo())).collect(Collectors.toList());
        Map<String, List<GroupExcelRequest>> map = dataList.stream().collect(Collectors.groupingBy(GroupExcelRequest::getCustomerOrderNo));
        Map<Integer,String> s = Maps.newTreeMap();
        HashMap<String, String> relateOrderMap = Maps.newHashMap();
        for (Map.Entry<String, List<GroupExcelRequest>> e : map.entrySet()){

            CustomerOrderInfoResponse customerOrderInfoResponse = new CustomerOrderInfoResponse();
            List<GroupExcelRequest> value = e.getValue();
            BeanUtils.copyProperties(value.get(0),customerOrderInfoResponse);
            CustomerOrderAddress address = new CustomerOrderAddress();
            BeanUtils.copyProperties(value.get(0),address);
            customerOrderInfoResponse.setAddress(address);

            //订单类型
            String orderTypeName = customerOrderInfoResponse.getOrderType();
            if (ToolUtils.isNotEmpty(orderTypeName)) {
                Optional<OrderType> first = Lists.newArrayList(OrderType.values()).stream().filter(o -> o.getValue().equals(orderTypeName)).findFirst();
                if (first.isPresent()){
                    customerOrderInfoResponse.setOrderType(first.get().getKey());
                }
            }

            // 客户集拼号校验
            if(StringUtils.isNotBlank(customerOrderInfoResponse.getConsolidationOrderNo())){
                codeHelper.checkConsolidationOrderNo(customerOrderInfoResponse.getConsolidationOrderNo());
            }

            if(InOutType.YS.getName().equals(inOutType) && CommonConstant.YES.equals(customerOrderInfoResponse.getPlanOrderFlagName())){
                //商品
                ArrayList<CustomerOrderItem> items = Lists.newArrayList();
                value.stream().forEach(
                    v->{
                        CustomerOrderItem item = new CustomerOrderItem();
                        BeanUtils.copyProperties(v, item);
                        if(ToolUtils.isEmpty(item.getCustomerItemCode())){
                            return;
                        }
                        items.add(item);
                    }
                );
                customerOrderInfoResponse.setItems(items);
                NewCustomerOrderInfoManager newCustomerOrderInfoManager = SpringContextHolder.getBean(NewCustomerOrderInfoManager.class);
                newCustomerOrderInfoManager.savePlanOrder(customerOrderInfoResponse);
                continue;
            }
            //业务类型
            String businessTypeName = customerOrderInfoResponse.getBusinessType();
            if (ToolUtils.isNotEmpty(businessTypeName)) {
                Optional<BusinessType> first = Lists.newArrayList(BusinessType.values()).stream().filter(o -> o.getValue().equals(businessTypeName)).findFirst();
                if (first.isPresent()){
                    customerOrderInfoResponse.setBusinessType(first.get().getKey());
                }
            }
            String businessModeName = customerOrderInfoResponse.getBusinessMode();
            if (ToolUtils.isNotEmpty(businessModeName)) {
                Optional<BusinessMode> first = Lists.newArrayList(BusinessMode.values()).stream().filter(o -> o.getName().equals(businessModeName)).findFirst();
                if (!first.isPresent()){
                    throw BusinessException.fail("所填业务模式不存在");
                }
            }

            //计费标准
            String freightBasisName = customerOrderInfoResponse.getFreightBasis();
            if (ToolUtils.isNotEmpty(freightBasisName)) {
                Optional<FreightBasisEnum> first = Lists.newArrayList(FreightBasisEnum.values()).stream().filter(o -> o.getValue().equals(freightBasisName)).findFirst();
                if (first.isPresent()){
                    customerOrderInfoResponse.setFreightBasis(first.get().getKey());
                }
            }
            //整车零担
            String loadTypeName = customerOrderInfoResponse.getLoadType();
            if (ToolUtils.isNotEmpty(loadTypeName)) {
                Optional<LoadType> first = Lists.newArrayList(LoadType.values()).stream().filter(o -> o.getValue().equals(loadTypeName)).findFirst();
                if (first.isPresent()){
                    customerOrderInfoResponse.setLoadType(first.get().getKey());
                }
            }
            //是否自提
            String selfMention = customerOrderInfoResponse.getSelfMention();
            customerOrderInfoResponse.setSelfMention(CommonConstant.NO.equals(selfMention) ? CommonConstant.FLAG_NO.toString() : CommonConstant.FLAG_YES.toString());

            //specimenType 上撤样
            String specimenType = customerOrderInfoResponse.getSpecimenType();
            //Y上样
            //N撤样
            if (CommonConstant.SPECIMENTYPE_N.equals(specimenType)){
                specimenType = CommonConstant.STRING_FLAG_NO;
            }
            if (CommonConstant.SPECIMENTYPE_Y.equals(specimenType)){
                specimenType = CommonConstant.STRING_FLAG_YES;
            }
            customerOrderInfoResponse.setSpecimenType(specimenType);
            //商超标识
            String scPosFlagName = customerOrderInfoResponse.getScPosFlagName();
            customerOrderInfoResponse.setScPosFlag(CommonConstant.YES.equals(scPosFlagName) ? CommonConstant.FLAG_YES : CommonConstant.FLAG_NO);

            //是否代收货款
            String deliverypayTypeName = customerOrderInfoResponse.getDeliverypayTypeName();
            if (StringUtils.isNotBlank(deliverypayTypeName)) {
                Optional<DeliverypayType> first = Lists.newArrayList(DeliverypayType.values()).stream().filter(o -> o.getValue().equals(deliverypayTypeName)).findFirst();
                if (!first.isPresent()) {
                    throw BusinessException.fail("该运费类型不存在");
                }
                customerOrderInfoResponse.setDeliverypayType(first.get().getKey());
                if (2== first.get().getKey()){

                    if (ToolUtils.isEmpty(customerOrderInfoResponse.getDeliveryAmount())) {
                        throw BusinessException.fail("到付需要填写运费金额");
                    }

                }
            }
            String collectionFlag = customerOrderInfoResponse.getCollectionFlag();
            customerOrderInfoResponse.setCollectionFlag(CommonConstant.YES.equals(collectionFlag) ? CommonConstant.STRING_FLAG_YES : CommonConstant.STRING_FLAG_NO);
            customerOrderInfoResponse.setCollectionAmount(CommonConstant.YES.equals(collectionFlag) ? customerOrderInfoResponse.getCollectionAmount() : BigDecimal.ZERO);

            //2021-03-19 进广  增加家居逻辑
            //是否家居订单  包件数不可全为空  家居订单总体积不可全为空！
            boolean hlFlag = StringUtils.isNotBlank(customerOrderInfoResponse.getHlFlagName()) && CommonConstant.YES.equals(customerOrderInfoResponse.getHlFlagName());
            if (hlFlag) customerOrderInfoResponse.setProjectClassify(ProjectClassifyEnum.HL.getKey());


            //是否模糊订单
            String planOrderFlagName = customerOrderInfoResponse.getPlanOrderFlagName();
            customerOrderInfoResponse.setPlanOrderFlag(CommonConstant.YES.equals(planOrderFlagName) ? CommonConstant.FLAG_YES : CommonConstant.FLAG_NO);
            //车型
            String equipmentName = customerOrderInfoResponse.getEquipmentName();
            customerOrderInfoResponse.setEquipmentType(queryEquipmentType(equipmentName));


            List<CustomerOrderItem> items = Lists.newArrayList();
            try{

                BigDecimal orderValue = customerOrderInfoResponse.getOrderValue();
//                if(customerOrderInfoResponse.getOrderType().equalsIgnoreCase(OrderType.YS.getKey())
//                    && null == orderValue
//                ){
//                    throw BusinessException.fail("纯运输订单货值不能为空");
//                }

                //2020-9-8 10:58:12 黄进广:手工或者导入的订单，如果货值超过T端限制（<=2400w）的则在新增页面保存时报错，货值金额必须<=2400w。导入的订单则提示：**行货值金额必须<=2400w
                ControlParam controlParam = controlParamManager.getCache(ORDER_VALUE_LIMIT);
                if (null != controlParam) {
                    String limitVaule = controlParam.getValue();
                    if (StringUtils.isNotEmpty(limitVaule) && null != orderValue ) {
                        orderValue = new BigDecimal(10000).multiply(orderValue);
                        BigDecimal limit = new BigDecimal(limitVaule.toString());
                        if (orderValue.compareTo(limit)>0) {
                            throw BusinessException.fail("货值金额必须<="+limit.divide(new BigDecimal(10000)).toString()+"万元");
                        }
                    }
                }

                if(customerOrderInfoResponse.getOrderType().equalsIgnoreCase(OrderType.YS.getKey())
                    &&CommonConstant.YES.equals(planOrderFlagName)&&
                    (customerOrderInfoResponse.getTotalVolume()==null||StringUtils.isEmpty(customerOrderInfoResponse.getTotalVolume().toPlainString()))
                    &&(customerOrderInfoResponse.getTotalGrossWeight()==null||StringUtils.isEmpty(customerOrderInfoResponse.getTotalGrossWeight().toPlainString()))
                    &&(customerOrderInfoResponse.getTotalQty()==null||StringUtils.isEmpty(customerOrderInfoResponse.getTotalQty().toPlainString()))
                    &&StringUtils.isEmpty(customerOrderInfoResponse.getEquipmentType())
                ){
                    throw BusinessException.fail("模糊订单总重量、总体积、总件数、车型不能全部为空");
                }
                if(customerOrderInfoResponse.getOrderType().equalsIgnoreCase(OrderType.YS.getKey())
                    &&(customerOrderInfoResponse.getTotalVolume()==null||StringUtils.isEmpty(customerOrderInfoResponse.getTotalVolume().toPlainString()))
                    &&(customerOrderInfoResponse.getTotalGrossWeight()==null||StringUtils.isEmpty(customerOrderInfoResponse.getTotalGrossWeight().toPlainString()))

                ){
                    throw BusinessException.fail("纯运输订单总重量、总体积不能全部为空");
                }


                //平台
                String siteName = customerOrderInfoResponse.getSiteName();
                customerOrderInfoResponse.setSiteCode(querySiteCode(siteName));
                String siteCode = customerOrderInfoResponse.getSiteCode();
                //下面可能空指针报错
                if (StringUtils.isBlank(siteCode)) {
                    throw BusinessException.fail("服务平台查询结果为空.名称：" + siteName);
                }
                String whCode = customerOrderInfoResponse.getWhCode();
                //仓库
                String whName = customerOrderInfoResponse.getWhName();
                if (ToolUtils.isNotEmpty(whCode)){
                    CdWarehouse cdWarehouseCache = cdWarehouseManager.getCdWarehouseCache(whCode);
                    if (cdWarehouseCache != null) {
                        if (!siteCode.equals(cdWarehouseCache.getSiteCode())) {
                            throw BusinessException.fail("仓库需要在对应服务平台下");
                        }
                        customerOrderInfoResponse.setWhName(cdWarehouseCache.getCdwhName());
                    }
                }else{
                    customerOrderInfoResponse.setWhCode(queryWhCode(whName));
                }
                List<String> failItems = Lists.newArrayList();
                value.stream().forEach(
                    v -> {
                        CustomerOrderItem customerOrderItem = new CustomerOrderItem();
                        BeanUtils.copyProperties(v, customerOrderItem);
                        //2021年10月8日17:33:43 徐期阳 订单导入明细备注
                        customerOrderItem.setRemark(customerOrderItem.getItemRemark());

                        if(customerOrderInfoResponse.getOrderType().equalsIgnoreCase(OrderType.YS.getKey())
                            && null == customerOrderItem.getPlanQty() &&!CommonConstant.YES.equals(planOrderFlagName)
                        ){
                            throw BusinessException.fail("纯运输订单计划数量不能为空");
                        }
                        if (CommonConstant.YES.equals(planOrderFlagName) && ToolUtils.isEmpty(customerOrderItem.getCustomerItemCode())){
                            return;
                        }
                        //商品状态  商品状态不能为空
                        String itemStatusName = customerOrderItem.getItemStatusName();
                        if (StringUtils.isBlank(itemStatusName)) {
                            throw BusinessException.fail("商品状态不能为空.客户商品编码：" + customerOrderItem.getCustomerItemCode());
                        }
                        Optional<ItemStatus> first = Lists.newArrayList(ItemStatus.values()).stream().filter(o -> o.getValue().equals(itemStatusName)).findFirst();
                        String itemStatus = first.isPresent() ? first.get().getKey() : dictHelper.getDictCodeByName(ItemStatus.DICT_CODE.getValue(), itemStatusName);
                        log.info("=====1=====客户订单号:{},状态名称:{},转换编码:{}",customerOrderInfoResponse.getCustomerOrderNo(),itemStatusName,itemStatus);
//                        String itemStatus = first.map(ItemStatus::getKey).orElseGet(() -> dictHelper.getDictVaule(ItemStatus.DICT_CODE.getValue(), itemStatusName));
                        if (StringUtils.isBlank(itemStatus)) {
                            throw BusinessException.fail("该商品状态不存在.客户商品编码：" + customerOrderItem.getCustomerItemCode() + ".商品状态：" + itemStatusName);
                        }
                        customerOrderItem.setItemStatus(itemStatus);

                        //套机编码不为空时，设置套机数量默认为1
                        if (InOutType.OUT.getName().equals(inOutType) && ToolUtils.isNotEmpty(customerOrderItem.getItemSuiteCode())){
                            customerOrderItem.setItemSuiteQty(1);
                        }
                        CdCommonMaterial commonMaterial = getCdcmMaterialNo(customerOrderInfoResponse.getCustomerCode(), customerOrderItem, inOutType);
                        if (ToolUtils.isEmpty(commonMaterial) || ToolUtils.isEmpty(commonMaterial.getCdcmMaterialNo())){
//                            failItems.add(customerOrderItem.getCustomerItemCode());
                            throw BusinessException.fail("基础数据商品为空.客户商品编码" + customerOrderItem.getCustomerItemCode());
                        }
                        customerOrderItem.setItemCode(commonMaterial.getCdcmMaterialNo());

                        boolean cdcmIsFictitiousFlag = false;
                        if (CommonConstant.STRING_FLAG_YES.equals(commonMaterial.getCdcmIsFictitious())&&!hlFlag) {
                            if (!OrderType.YS.getKey().equals(customerOrderInfoResponse.getOrderType())) throw BusinessException.fail("存在虚拟商品只能是纯运输订单");
                            cdcmIsFictitiousFlag = true;
                            customerOrderInfoResponse.setProjectClassify(ProjectClassifyEnum.CDCM_FICTITIOUS.getKey());
                        }

                        /**
                         * 家居
                         * 1. 判断安装标志
                         * 2. 计算明细总体积、总毛重
                         * 3. 非家居包件数清0
                         */
                        //家居不取基础数据 直接取excel的值
                        //虚拟商品以导入的为准
                        //重新计算体积毛重，打标
                        CustomerOrderInfoResponse customerOrderItemResponse = new CustomerOrderInfoResponse();
                        BeanUtils.copyProperties(v, customerOrderItemResponse);
                        customerOrderItem.setInstallFlag(customerOrderItemResponse.getInstallFlagName() == null || CommonConstant.NO.equals(customerOrderItemResponse.getInstallFlagName()) ? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
                        customerOrderItem.setIsParent(customerOrderItemResponse.getIsParent() == null || CommonConstant.NO.equals(customerOrderItemResponse.getIsParent()) ? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
                        customerOrderItem.setFictitiousFlag(commonMaterial.getCdcmIsFictitious() == null || CommonConstant.FLAG_N.equals(commonMaterial.getCdcmIsFictitious()) ? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
                        if (hlFlag || cdcmIsFictitiousFlag) {
                            //按导入的值计算，不取基础数据的值
                            setItemValueByImportValue(customerOrderItem, customerOrderItemResponse);
                        }
//                        if (InOutType.IN.getName().equals(inOutType)) {
//                            InCustomerOrderInfoExcelRequest inCustomerOrderInfoExcelRequest = (InCustomerOrderInfoExcelRequest) v;
//                            customerOrderItem.setInstallFlag(inCustomerOrderInfoExcelRequest.getInstallFlagName() == null || CommonConstant.NO.equals(inCustomerOrderInfoExcelRequest.getInstallFlagName()) ? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
//                            customerOrderItem.setIsParent(inCustomerOrderInfoExcelRequest.getIsParent() == null || CommonConstant.NO.equals(inCustomerOrderInfoExcelRequest.getIsParent()) ? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
//                            if (hlFlag || cdcmIsFictitiousFlag) {
//                                BigDecimal planQty = inCustomerOrderInfoExcelRequest.getPlanQty();
//                                Boolean planQtyIsOk = planQty != null && planQty.compareTo(BigDecimal.ZERO) > 0;
//                                if (null != inCustomerOrderInfoExcelRequest.getTotalVolume() && BigDecimal.ZERO.compareTo(inCustomerOrderInfoExcelRequest.getTotalVolume()) < 0) {
//                                    customerOrderItem.setTotalVolume(inCustomerOrderInfoExcelRequest.getTotalVolume());
//                                    if (planQtyIsOk) {
//                                        customerOrderItem.setVolume(inCustomerOrderInfoExcelRequest.getTotalVolume().divide(planQty, 8, RoundingMode.HALF_UP));
//                                    }
//                                } else {
//                                    customerOrderItem.setVolume(BigDecimal.ZERO);
//                                    customerOrderItem.setTotalVolume(BigDecimal.ZERO);
//                                }
//                                if (null != inCustomerOrderInfoExcelRequest.getTotalGrossWeight() && BigDecimal.ZERO.compareTo(inCustomerOrderInfoExcelRequest.getTotalGrossWeight()) < 0) {
//                                    customerOrderItem.setTotalGrossWeight(inCustomerOrderInfoExcelRequest.getTotalGrossWeight());
//                                    if (planQtyIsOk) {
//                                        customerOrderItem.setGrossWeight(inCustomerOrderInfoExcelRequest.getTotalGrossWeight().divide(planQty, 8, RoundingMode.HALF_UP));
//                                    }
//                                } else {
//                                    customerOrderItem.setGrossWeight(BigDecimal.ZERO);
//                                    customerOrderItem.setTotalGrossWeight(BigDecimal.ZERO);
//                                }
//                            }
//                        } else {
//                            OutCustomerOrderInfoExcelRequest outCustomerOrderInfoExcelRequest = (OutCustomerOrderInfoExcelRequest) v;
//                            customerOrderItem.setInstallFlag(outCustomerOrderInfoExcelRequest.getInstallFlagName() == null || CommonConstant.NO.equals(outCustomerOrderInfoExcelRequest.getInstallFlagName()) ? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
//                            customerOrderItem.setIsParent(outCustomerOrderInfoExcelRequest.getIsParent() == null || CommonConstant.NO.equals(outCustomerOrderInfoExcelRequest.getIsParent()) ? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
//                            if (hlFlag || cdcmIsFictitiousFlag) {
//                                BigDecimal planQty = outCustomerOrderInfoExcelRequest.getPlanQty();
//                                Boolean planQtyIsOk = planQty != null && planQty.compareTo(BigDecimal.ZERO) > 0;
//                                //体积
//                                if (null != outCustomerOrderInfoExcelRequest.getTotalVolume() && BigDecimal.ZERO.compareTo(outCustomerOrderInfoExcelRequest.getTotalVolume()) < 0) {
//                                    customerOrderItem.setTotalVolume(outCustomerOrderInfoExcelRequest.getTotalVolume());
//                                    if (planQtyIsOk) {
//                                        customerOrderItem.setVolume(outCustomerOrderInfoExcelRequest.getTotalVolume().divide(planQty, 8, RoundingMode.HALF_UP));
//                                    }
//                                } else if (null != outCustomerOrderInfoExcelRequest.getVolume() && BigDecimal.ZERO.compareTo(outCustomerOrderInfoExcelRequest.getVolume()) < 0) {
//                                    customerOrderItem.setVolume(outCustomerOrderInfoExcelRequest.getVolume());
//                                    if (planQtyIsOk) {
//                                        customerOrderItem.setTotalVolume(planQty.multiply(outCustomerOrderInfoExcelRequest.getVolume()));
//                                    }
//                                } else {
//                                    customerOrderItem.setVolume(BigDecimal.ZERO);
//                                    customerOrderItem.setTotalVolume(BigDecimal.ZERO);
//                                }
//                                //毛重
//                                if (null != outCustomerOrderInfoExcelRequest.getTotalGrossWeight() && BigDecimal.ZERO.compareTo(outCustomerOrderInfoExcelRequest.getTotalGrossWeight()) < 0) {
//                                    customerOrderItem.setTotalGrossWeight(outCustomerOrderInfoExcelRequest.getTotalGrossWeight());
//                                    if (planQtyIsOk) {
//                                        customerOrderItem.setGrossWeight(outCustomerOrderInfoExcelRequest.getTotalGrossWeight().divide(planQty, 8, RoundingMode.HALF_UP));
//                                    }
//                                } else if (null != outCustomerOrderInfoExcelRequest.getGrossWeight() && BigDecimal.ZERO.compareTo(outCustomerOrderInfoExcelRequest.getGrossWeight()) < 0) {
//                                    customerOrderItem.setGrossWeight(outCustomerOrderInfoExcelRequest.getGrossWeight());
//                                    if (planQtyIsOk) {
//                                        customerOrderItem.setTotalGrossWeight(planQty.multiply(outCustomerOrderInfoExcelRequest.getGrossWeight()));
//                                    }
//                                } else {
//                                    customerOrderItem.setGrossWeight(BigDecimal.ZERO);
//                                    customerOrderItem.setTotalGrossWeight(BigDecimal.ZERO);
//                                }
//                            }
//                        }
                        if (!hlFlag) customerOrderItem.setPkgQty(BigDecimal.ZERO);

                        items.add(customerOrderItem);
                    }
                );
                //计算主表总体积总毛重总包件数总包件数  家居订单不能是模糊订单
                if (hlFlag) {
                    customerOrderInfoResponse.setTotalVolume(items.stream().filter(a -> null != a.getTotalVolume()).map(CustomerOrderItem::getTotalVolume).reduce(BigDecimal.ZERO, BigDecimal::add));
                    customerOrderInfoResponse.setTotalGrossWeight(items.stream().filter(a -> null != a.getTotalGrossWeight()).map(CustomerOrderItem::getTotalGrossWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
                    customerOrderInfoResponse.setTotalPkgQty(items.stream().filter(a -> null != a.getPkgQty()).map(CustomerOrderItem::getPkgQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    if (null == customerOrderInfoResponse.getTotalPkgQty() || BigDecimal.ZERO.compareTo(customerOrderInfoResponse.getTotalPkgQty()) == 0) throw BusinessException.fail("家居订单" + customerOrderInfoResponse.getCustomerOrderNo() + "，包件数不可全为空");
                    if (null == customerOrderInfoResponse.getTotalVolume() || BigDecimal.ZERO.compareTo(customerOrderInfoResponse.getTotalVolume()) == 0) throw BusinessException.fail("家居订单" + customerOrderInfoResponse.getCustomerOrderNo() + "，总体积不可全为空");
                    if (CommonConstant.FLAG_YES.equals(customerOrderInfoResponse.getPlanOrderFlag())) throw BusinessException.fail("家居订单" + customerOrderInfoResponse.getCustomerOrderNo() + "，不能是模糊订单");
                }

                //虚拟商品 明细总体积、总毛重 不可同时为0
                if (ProjectClassifyEnum.CDCM_FICTITIOUS.getKey().equals(customerOrderInfoResponse.getProjectClassify())) {
                    items.forEach(customerOrderItem -> {
                        if (null == customerOrderItem.getTotalVolume()) customerOrderItem.setTotalVolume(BigDecimal.ZERO);
                        if (null == customerOrderInfoResponse.getTotalGrossWeight()) customerOrderItem.setTotalGrossWeight(BigDecimal.ZERO);
                        if (BigDecimal.ZERO.compareTo(customerOrderItem.getTotalVolume()) == 0 && BigDecimal.ZERO.compareTo(customerOrderItem.getTotalGrossWeight()) == 0) throw BusinessException.fail(customerOrderInfoResponse.getCustomerOrderNo() + "存在虚拟商品," + customerOrderItem.getCustomerItemCode() + "导入重量体积不同时为0或空");
                    });
                    customerOrderInfoResponse.setTotalVolume(items.stream().filter(a -> null != a.getTotalVolume()).map(CustomerOrderItem::getTotalVolume).reduce(BigDecimal.ZERO, BigDecimal::add));
                    customerOrderInfoResponse.setTotalGrossWeight(items.stream().filter(a -> null != a.getTotalGrossWeight()).map(CustomerOrderItem::getTotalGrossWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
                }

                //1导入时客户商品编码对照基础数据表 cd common material中 cdcm cust material no字段不存在
                //则提示客户商品编码不存在请重试
                //2
                if (ToolUtils.isNotEmpty(failItems)&&!CommonConstant.YES.equals(planOrderFlagName)){
                    throw BusinessException.fail("客户商品编码不存在请重试"+ JSON.toJSONString(failItems));
                }
                customerOrderInfoResponse.setOrderSource(OrderSource.HANDLE.getKey());
                customerOrderInfoResponse.setAddress(address);
                customerOrderInfoResponse.setItems(items);

                Optional<DeliveryType> firstDeliveryType = Lists.newArrayList(DeliveryType.values()).stream().filter(o -> o.getValue().equals(customerOrderInfoResponse.getDeliveryType())).findFirst();
                if (firstDeliveryType.isPresent()){
                    customerOrderInfoResponse.setDeliveryType(firstDeliveryType.get().getKey());
                }

                //2021 0302 黄进广:订单导入根据业务模式校验配送方式。B2C：YS，WAREHOUSEMATCHING，DOT，ZT，EXPRESS。B2B：NET，DELIVERY，YS，ZT，EXPRESS。如果导入校验不通过，就报错并且把B2C和B2B对应的配送方式都提示出来。
                /*if (BusinessMode.isB2C(customerOrderInfoResponse.getBusinessMode())) {
                    if (!Arrays.asList(DeliveryType.YS.getKey(), DeliveryType.WAREHOUSEMATCHING.getKey(), DeliveryType.DOT.getKey(), DeliveryType.ZT.getKey(), DeliveryType.EXPRESS.getKey()).contains(customerOrderInfoResponse.getDeliveryType())) {
                        throw BusinessException.fail("业务模式 为 B2C , 配送方式可选择：" + DeliveryType.YS.getValue() + "," + DeliveryType.WAREHOUSEMATCHING.getValue() +
                            "," + DeliveryType.DOT.getValue()  + "," + DeliveryType.ZT.getValue() + "," + DeliveryType.EXPRESS.getValue() + ".当前导入配送方式异常[" +DeliveryType.getName(customerOrderInfoResponse.getDeliveryType()) +"]");
                    }
                }
                if (BusinessMode.isB2B(customerOrderInfoResponse.getBusinessMode())) {
                    if (!Arrays.asList(DeliveryType.NET.getKey(), DeliveryType.DELIVERY.getKey(), DeliveryType.YS.getKey(), DeliveryType.ZT.getKey(), DeliveryType.EXPRESS.getKey()).contains(customerOrderInfoResponse.getDeliveryType())) {
                        throw BusinessException.fail("业务模式 为 B2B , 配送方式可选择：" + DeliveryType.NET.getValue() + "," + DeliveryType.DELIVERY.getValue() +
                            "," + DeliveryType.YS.getValue() + "," + DeliveryType.ZT.getValue() + "," + DeliveryType.EXPRESS.getValue() + ".当前导入配送方式异常[" +DeliveryType.getName(customerOrderInfoResponse.getDeliveryType()) +"]");
                    }
                }*/
                //2021 0419 王涛:订单导入 根据业务类型B2B/B2C限制导入的配送方式
                if (!InOutType.YS.getName().equals(inOutType)){
                    if (BusinessMode.isB2C(customerOrderInfoResponse.getBusinessMode())) {
                        String dictVaule = dictHelper.getDictVaule(CommonConstant.DELIVERY_TYPE_B2C, customerOrderInfoResponse.getDeliveryType());
                        if (StringUtils.isEmpty(dictVaule)) {
                            throw BusinessException.fail("当前导入B2C配送方式异常:" +DeliveryType.getName(customerOrderInfoResponse.getDeliveryType()));
                        }
                    }
                    if (BusinessMode.isB2B(customerOrderInfoResponse.getBusinessMode())) {
                        String dictVaule = dictHelper.getDictVaule(CommonConstant.DELIVERY_TYPE_B2B, customerOrderInfoResponse.getDeliveryType());
                        if (StringUtils.isEmpty(dictVaule)) {
                            throw BusinessException.fail("当前导入B2B配送方式异常:" +DeliveryType.getName(customerOrderInfoResponse.getDeliveryType()));
                        }
                    }
                }

                // 出库单检验地址
                if (InOutType.OUT.getName().equals(inOutType)){
                    check(customerOrderInfoResponse);
                }
                if (ToolUtils.isNotEmpty(customerOrderInfoResponse.getCustomerCode())) {
                    EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(customerOrderInfoResponse.getCustomerCode());
                    if ( ebCustomerCache == null) {
                        throw BusinessException.fail("客户不存在，请检查客户编码");
                    }
                    customerOrderInfoResponse.setCustomerName(ebCustomerCache.getEbcuNameCn());
                }
                if(!CollectionUtils.isEmpty(customerOrderInfoResponse.getItems())){
                    customerOrderInfoResponse.getItems().forEach(data ->{
                        if(data.getPlanQty()==null){
                            data.setPlanQty(new BigDecimal(0));
                        }
                    });
                }
                customerOrderInfoResponse.setProductAnalysisFlag(true);
                if (InOutType.YS.getName().equals(inOutType)){
                    checkYS(customerOrderInfoResponse);
                }
                JsonResponse<CustomerOrderInfoResponse> response = orderFeign.addOrUpdateCustomerOrderInfo(customerOrderInfoResponse);
                if (null == response){
                    throw BusinessException.fail("订单保存失败");
                }
                if(!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())){
                    throw BusinessException.fail(response.getMsg());
                }

                // 2022.7.7 zhs:订单保存成功后，保存关联订单信息
                CustomerOrderInfoResponse customerOrderInfo = response.data;
                CustomerOrderInfo relateCustomerOrderInfo = exportOrderBusinessHelper.searchSuitedRelateOrder(customerOrderInfoResponse);
                if (relateCustomerOrderInfo != null) {
                    relateOrderMap.put(customerOrderInfo.getOrderNo(),relateCustomerOrderInfo.getOrderNo());
                }

            } catch (BusinessException ex){
                value.stream().forEach(
                    v -> s.put(v.getRow(),"第"+ v.getRow() +"行数据异常,"+ex.getMessage())
                   // v -> s.add("第"+ v.getRow() +"行数据异常,"+ex.getMessage())
                );
                log.error(ex.getMessage(), ex);
                continue;
            }catch (Exception ep){
                value.stream().forEach(
                    v -> s.put(v.getRow(),"第"+ v.getRow() +"行数据异常," + ep.getMessage())
                   // v -> s.add("第"+ v.getRow() +"行数据异常,")
                );
                log.error(ep.getMessage() ,ep);
                continue;
            }
            list.add(customerOrderInfoResponse);
        }

        //因为存在多条订单关联同一个模糊订单的情况，所以全部订单保存完后关联订单，
        ArrayList<VagueRelationRequest> vagueRelationRequests = exportOrderBusinessHelper.getVagueRelationRequests(relateOrderMap);
        vagueRelationRequests.forEach(vagueRelationRequest -> {
            convergedCustomerOrderFeign.vagueRelation(vagueRelationRequest);
        });

        JsonResponse jsonResponse = JsonResponse.success(list);
        if (ToolUtils.isNotEmpty(s)){
            jsonResponse.setMsg(JSON.toJSONString(s.values()));
        }
        return jsonResponse;
    }


    private void checkYS(CustomerOrderInfoResponse customerOrderInfoResponse) {
        if (ToolUtils.isEmpty(customerOrderInfoResponse.getCustomerCode())){
            throw BusinessException.fail("客户编码必填");
        }
        if (ToolUtils.isEmpty(customerOrderInfoResponse.getBusinessMode())){
            throw BusinessException.fail("业务模式必填");
        }
        if (ToolUtils.isEmpty(customerOrderInfoResponse.getBusinessType())){
            throw BusinessException.fail("业务类型必填");
        }
        if (ToolUtils.isEmpty(customerOrderInfoResponse.getSelfMention())){
            throw BusinessException.fail("是否自提必填");
        }
        if (ToolUtils.isEmpty(customerOrderInfoResponse.getSiteName())){
            throw BusinessException.fail("服务平台必填");
        }

        customerOrderInfoResponse.setExpectArriveEndTime(customerOrderInfoResponse.getExpectArriveStartTime());

        CustomerOrderAddress address = customerOrderInfoResponse.getAddress();

        if (ToolUtils.isNotEmpty(customerOrderInfoResponse.getSenderUnitCode())){
            address.setSenderUnitCode(customerOrderInfoResponse.getSenderUnitCode());
            JsonResponse<PageResponse<ContactsOms>> contacts = dcAtomicFeign.contacts(customerOrderInfoResponse.getSenderUnitCode(),"annto");
            Assert.isTrue(contacts.judgeSuccess(), "获取发货联系人信息失败："+customerOrderInfoResponse.getSenderUnitCode());
            List<ContactsOms> list = contacts.data().getList();
            Assert.notEmpty(list, "联系人编码未在DC维护，请前往【DC(数据中心)-订单配置-收发货联系人】进行查询或清空该字段后填写具体联系人信息");
            ContactsOms contactsOms = list.get(0);
            address.setSenderProvinceCode(contactsOms.getProvinceCode());
            address.setSenderProvinceName(contactsOms.getProvinceName());
            address.setSenderCityCode(contactsOms.getCityCode());
            address.setSenderCityName(contactsOms.getCityName());
            address.setSenderDistrictCode(contactsOms.getDistrictCode());
            address.setSenderDistrictName(contactsOms.getDistrictName());
            address.setSenderTownCode(contactsOms.getTownCode());
            address.setSenderTownName(contactsOms.getTownName());
            address.setSenderDetailAddr(contactsOms.getDetailAddress());
            address.setSenderMobile(contactsOms.getContactMobile());
            address.setSenderTel(contactsOms.getContactMobile());
            address.setSenderName(contactsOms.getContact());
            address.setSenderUnitName(contactsOms.getContactCompanyName());
            customerOrderInfoResponse.setUpperSenderName(contactsOms.getContactCompanyName());
        }else{
            address.setSenderUnitName(customerOrderInfoResponse.getSenderUnitName());
            customerOrderInfoResponse.setUpperSenderName(customerOrderInfoResponse.getSenderUnitName());
            String sendProvince = address.getSenderProvinceName();
            String sendCity = address.getSenderCityName();
            String sendDistrict = address.getSenderDistrictName();
            String sendTown = address.getSenderTownName();
            TmpAddress tmpSender = checkCustomerAgingConfigExcel.getAddCode(sendProvince, sendCity, sendDistrict, sendTown);
            if (tmpSender != null) {
                String sendProvinceCode = tmpSender.getProvince();
                String sendCityCode = tmpSender.getCity();
                String sendDistrictCode = tmpSender.getDistrict();
                String sendTownCode = tmpSender.getTown();
                address.setSenderProvinceCode(sendProvinceCode);
                address.setSenderCityCode(sendCityCode);
                address.setSenderDistrictCode(sendDistrictCode);
                address.setSenderTownCode(sendTownCode);
                log.info("发货地{}",JSON.toJSONString(tmpSender));
            }
        }
        //校验发货
//        if(ToolUtils.isEmpty(address.getSenderProvinceCode())){
//            throw BusinessException.fail("发货省为空");
//        }
//        if(ToolUtils.isEmpty(address.getSenderCityCode())){
//            throw BusinessException.fail("发货市为空");
//        }
//        if(ToolUtils.isEmpty(address.getSenderDistrictCode())){
//            throw BusinessException.fail("发货区县为空");
//        }
//        if(ToolUtils.isEmpty(address.getSenderTownCode())){
//            throw BusinessException.fail("发货乡镇为空");
//        }
        if(ToolUtils.isEmpty(address.getSenderDetailAddr())){
            throw BusinessException.fail("发货详细地址为空");
        }
        if(ToolUtils.isEmpty(address.getSenderMobile())){
            throw BusinessException.fail("发货手机号为空");
        }
        if(ToolUtils.isEmpty(address.getSenderName())){
            throw BusinessException.fail("发货人为空");
        }



        if (ToolUtils.isNotEmpty(customerOrderInfoResponse.getReceiverUnitCode())){
            address.setReceiverUnitCode(customerOrderInfoResponse.getReceiverUnitCode());
            JsonResponse<PageResponse<ContactsOms>> contacts = dcAtomicFeign.contacts(customerOrderInfoResponse.getReceiverUnitCode(),"annto");
            log.info("checkYS->contacts orderNo:{},json:{}",customerOrderInfoResponse.getCustomerOrderNo(),JSON.toJSONString(contacts));
            Assert.isTrue(contacts.judgeSuccess(), "获取收货联系人信息失败："+customerOrderInfoResponse.getReceiverUnitCode());
            List<ContactsOms> list = contacts.data().getList();
            Assert.notEmpty(list, "联系人编码未在DC维护，请前往【DC(数据中心)-订单配置-收发货联系人】进行查询或清空该字段后填写具体联系人信息");
            ContactsOms contactsOms = list.get(0);
            address.setReceiverProvinceCode(contactsOms.getProvinceCode());
            address.setReceiverProvinceName(contactsOms.getProvinceName());
            address.setReceiverCityCode(contactsOms.getCityCode());
            address.setReceiverCityName(contactsOms.getCityName());
            address.setReceiverDistrictCode(contactsOms.getDistrictCode());
            address.setReceiverDistrictName(contactsOms.getDistrictName());
            address.setReceiverTownCode(contactsOms.getTownCode());
            address.setReceiverTownName(contactsOms.getTownName());
            address.setReceiverDetailAddr(contactsOms.getDetailAddress());
            address.setReceiverMobile(contactsOms.getContactMobile());
            address.setReceiverTel(contactsOms.getContactMobile());
            address.setReceiverName(contactsOms.getContact());
            address.setReceiverUnitName(contactsOms.getContactCompanyName());
            customerOrderInfoResponse.setUpperReceiverName(contactsOms.getContactCompanyName());
        }else{
            address.setReceiverUnitName(customerOrderInfoResponse.getReceiverUnitName());
            customerOrderInfoResponse.setUpperReceiverName(customerOrderInfoResponse.getReceiverUnitName());
            //目的地
            String receiveProvince = address.getReceiverProvinceName();
            String receiveCity = address.getReceiverCityName();
            String receiveDistrict = address.getReceiverDistrictName();
            String receiveTown = address.getReceiverTownName();
            TmpAddress tmpReceiver = checkCustomerAgingConfigExcel.getAddCode(receiveProvince, receiveCity, receiveDistrict, receiveTown);
            //收件地
            if (tmpReceiver != null) {
                String receiverProvinceCode = tmpReceiver.getProvince();
                String receiverCityCode = tmpReceiver.getCity();
                String receiverDistrictCode = tmpReceiver.getDistrict();
                String receiverTownCode = tmpReceiver.getTown();
                address.setReceiverProvinceCode(receiverProvinceCode);
                address.setReceiverCityCode(receiverCityCode);
                address.setReceiverDistrictCode(receiverDistrictCode);
                address.setReceiverTownCode(receiverTownCode);
                log.info("收货地{}",JSON.toJSONString(tmpReceiver));
            }
        }
        if (CommonConstant.YES.equals(customerOrderInfoResponse.getSelfMention())){
            return;
        }
        //校验收货
//        if(ToolUtils.isEmpty(address.getReceiverProvinceCode())){
//            throw BusinessException.fail("收货省为空");
//        }
//        if(ToolUtils.isEmpty(address.getReceiverCityCode())){
//            throw BusinessException.fail("收货市为空");
//        }
//        if(ToolUtils.isEmpty(address.getReceiverDistrictCode())){
//            throw BusinessException.fail("收货区县为空");
//        }
//        if(ToolUtils.isEmpty(address.getReceiverTownCode())){
//            throw BusinessException.fail("收货乡镇为空");
//        }
        if(ToolUtils.isEmpty(address.getReceiverDetailAddr())){
            throw BusinessException.fail("收货详细地址为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverMobile())){
            throw BusinessException.fail("收货手机号为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverName())){
            throw BusinessException.fail("收货人为空");
        }

    }


    /**
    * @description: 验证地址
    * @param: [customerOrderInfoResponse]
    * @return: void
    * @author: 陈永培
    * @createtime: 2020/6/11 10:44
    */
    private void check(CustomerOrderInfoResponse customerOrderInfoResponse) {
        if (ToolUtils.isEmpty(customerOrderInfoResponse.getOrderType())){
            throw BusinessException.fail("不存在的订单类型");
        }

        CustomerOrderAddress address = customerOrderInfoResponse.getAddress();

        //目的地
        String receiveProvince = address.getReceiverProvinceName();
        String receiveCity = address.getReceiverCityName();
        String receiveDistrict = address.getReceiverDistrictName();
        String receiveTown = address.getReceiverTownName();
        TmpAddress tmpReceiver = checkCustomerAgingConfigExcel.getAddCode(receiveProvince, receiveCity, receiveDistrict, receiveTown);

        //验证地址
        //支持订单中心批量导入模板只填写详细地址，不需要填写四级地址，订单审核解析详细地址;新建模糊订单不需要填写收货地址，在订单补录时填写
        if (!CommonConstant.FLAG_YES.equals(customerOrderInfoResponse.getPlanOrderFlag()) && StringUtils.isBlank(address.getReceiverDetailAddr())) {
            checkReceiverAddress(customerOrderInfoResponse, tmpReceiver);
        }
        //收件地
        if (tmpReceiver != null) {
            String receiverProvinceCode = tmpReceiver.getProvince();
            String receiverCityCode = tmpReceiver.getCity();
            String receiverDistrictCode = tmpReceiver.getDistrict();
            String receiverTownCode = tmpReceiver.getTown();
            address.setReceiverProvinceCode(receiverProvinceCode);
            address.setReceiverCityCode(receiverCityCode);
            address.setReceiverDistrictCode(receiverDistrictCode);
            address.setReceiverTownCode(receiverTownCode);
            log.info("收货地{}",JSON.toJSONString(tmpReceiver));
        }

        //始发地
        String sendProvince = address.getSenderProvinceName();
        String sendCity = address.getSenderCityName();
        String sendDistrict = address.getSenderDistrictName();
        String sendTown = address.getSenderTownName();
        TmpAddress tmpSender = checkCustomerAgingConfigExcel.getAddCode(sendProvince, sendCity, sendDistrict, sendTown);
        //支持订单中心批量导入模板只填写详细地址，不需要填写四级地址，订单审核解析详细地址
        if (OrderType.YS.getKey().equals(customerOrderInfoResponse.getOrderType()) && StringUtils.isBlank(address.getSenderDetailAddr())){
            boolean checkFourAddress = tmpSender.checkThridAddress();
            //需要验证四级
            if ( ! checkFourAddress){
                throw BusinessException.fail("纯运输订单存在未解析到的发货地省市区县");
            }
        }

        if (tmpSender != null) {
            String sendProvinceCode = tmpSender.getProvince();
            String sendCityCode = tmpSender.getCity();
            String sendDistrictCode = tmpSender.getDistrict();
            String sendTownCode = tmpSender.getTown();
            address.setSenderProvinceCode(sendProvinceCode);
            address.setSenderCityCode(sendCityCode);
            address.setSenderDistrictCode(sendDistrictCode);
            address.setSenderTownCode(sendTownCode);
            log.info("发货地{}",JSON.toJSONString(tmpSender));
        }
    }


    /**
     * 设置大小电标识
     *
     * @param whCode
     */
    public Integer getOutCollabWh(String whCode) {
        if (org.apache.commons.lang.StringUtils.isEmpty(whCode)) {
            throw BusinessException.fail("获取大小电标示失败,无仓库编码");
        }
        CdWarehouse cdWarehouse = cdWarehouseManager.getCdWarehouseCache(whCode);
        if (cdWarehouse == null) {
            throw BusinessException.fail("获取大小电标示失败");
        }

        if (cdWarehouse.getIsOutCollabWh() != null && CommonEnum.NO.getValue() == cdWarehouse.getIsOutCollabWh().intValue()) {
            return CommonEnum.YES.getValue();
        } else {
            return CommonEnum.NO.getValue();
        }
    }

    /**
     * @description: 验证地址
     * @param: [customerOrderInfoResponse, addressCode]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/6/9 16:45
     */
    private void checkReceiverAddress(CustomerOrderInfoResponse customerOrderInfoResponse, TmpAddress tmpAddress) {
        CustomerOrderAddress address = customerOrderInfoResponse.getAddress();
        //四级地址
        String receiveTown = address.getReceiverTownName();

        String businessMode = customerOrderInfoResponse.getBusinessMode();
        String orderType = customerOrderInfoResponse.getOrderType();
        String whCode = customerOrderInfoResponse.getWhCode();
        String deliveryType = customerOrderInfoResponse.getDeliveryType();
        // 验证三级是否成功
        boolean checkThridAddress = tmpAddress.checkThridAddress();
        // 验证四级是否成功
        boolean checkFourAddress = tmpAddress.checkFourAddress();

        if ((CommonConstant.FLAG_NO.toString().equals(customerOrderInfoResponse.getSelfMention())|| OrderType.YS.getKey().equals(customerOrderInfoResponse.getOrderType()))){

            //2020-6-9 16:43:07 泓铄： 出库订单导入时，若业务模式为B2C，订单类型为销售出库单，仓库为小电仓，则导入模板中的订单的【收货乡镇】为非必填，为空时不校验，不为空时则正常校验四级地址编码，其余逻辑不变
            if (BusinessMode.B2C.getName().equals(businessMode) && OrderType.PO.getKey().equals(orderType)) {
                Integer outCollabWh = getOutCollabWh(whCode);
                //小电 && 配送方式为空 (可能存在选了B2C，PO，小电仓，直配的场景)
                if (CommonEnum.YES.getValue().equals(outCollabWh) && null == deliveryType && StringUtils.isEmpty(receiveTown)){
                    //只需验证三级
                    if (checkThridAddress){
                        return;
                    }
                }
            }

            //2020-6-9 17:08:00 泓铄：出库订单导入时，配送方式为快递，则导入模板中的订单的【收货乡镇】为非必填，为空时不校验，不为空时则正常校验四级地址编码，其余逻辑不变
            if (DeliveryType.EXPRESS.getKey().equals(deliveryType) && StringUtils.isEmpty(receiveTown) ) {
                //只需验证三级
                if (checkThridAddress){
                    return;
                }
            }

            //需要验证四级
            if ( ! checkFourAddress){
                throw BusinessException.fail("非自提或纯运输订单存在未解析到的收货地省市区县");
            }

        }
    }





    /**
     * 设置大小电标识
     * @param customerOrderInfo
     * @param orderInfo
     * @return
     */
    public Integer getOutCollabWh(CustomerOrderInfo customerOrderInfo, OrderInfo orderInfo) {

        if(orderInfo==null && customerOrderInfo == null){
            throw BusinessException.fail("获取大小电标示失败,客户订单和子单同时为空");
        }

        String whCode = null;
        String siteCode = null;

        if(customerOrderInfo == null){
            whCode = orderInfo.getWhCode();
            siteCode = orderInfo.getSiteCode();
        }else {
            whCode = customerOrderInfo.getWhCode();
            siteCode = customerOrderInfo.getSiteCode();
        }

        // 使用仓库判断大小电
        if (StringUtils.isNotEmpty(whCode)) {
            Integer isOutCollabWh = WH_OUTCOLLAB_MAP.get(whCode);
            if (isOutCollabWh != null){
                return isOutCollabWh;
            }

            synchronized (whCode.intern()){
                CdWarehouse cdWarehouse = cdWarehouseManager.getCdWarehouseCache(whCode);
                if (cdWarehouse == null) {
                    throw BusinessException.fail("获取大小电标识失败,mdm根据仓库编码获取仓库信息为空,仓库编码:"+whCode);
                }
                if (cdWarehouse.getIsOutCollabWh() != null && CommonEnum.NO.getValue() == cdWarehouse.getIsOutCollabWh().intValue()) {
                    isOutCollabWh = CommonEnum.YES.getValue();
                } else {
                    isOutCollabWh = CommonEnum.NO.getValue();
                }
                WH_OUTCOLLAB_MAP.put(whCode, isOutCollabWh);
                return isOutCollabWh;
            }
        }


        // 使用网点编码判断是否为小电
        if(StringUtils.isEmpty(siteCode)){
            throw BusinessException.fail("获取大小电标示失败,无平台编码");
        }

        Integer isOutCollabWh = SITE_OUTCOLLAB_MAP.get(siteCode);
        if (isOutCollabWh != null){
            return isOutCollabWh;
        }
        synchronized (siteCode.intern()){
            List<CdWarehouse> cdWarehouseList =  cdWarehouseManager.getCdWarehouseListCacheFromSiteCode(siteCode);
            if(CollectionUtils.isEmpty(cdWarehouseList)){
                throw BusinessException.fail("获取大小电标示失败,根据平台编码:"+siteCode+"找不到对应仓库");
            }
            //该平台下所有仓库都是小电才是小电，否则是大电,by lijuan
            for(CdWarehouse cdWarehouse : cdWarehouseList){
                if (cdWarehouse.getIsOutCollabWh() != null && CommonEnum.NO.getValue() == cdWarehouse.getIsOutCollabWh().intValue()) {
                    //小电
                } else {
                    isOutCollabWh = CommonEnum.NO.getValue();
                    break;
                }
            }

            isOutCollabWh = isOutCollabWh == null? CommonEnum.YES.getValue(): isOutCollabWh;
            SITE_OUTCOLLAB_MAP.put(whCode, isOutCollabWh);
            return isOutCollabWh;
        }
    }




    private void customerOrderInfoImport(List<CustomerOrderInfoResponse> inInfoResponses, StringBuilder msg) {
        for (CustomerOrderInfoResponse customerOrderInfoResponse : inInfoResponses) {
            if (customerOrderInfoResponse.getCustomerOrderNo() == null) {
                return;
            }
            CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
            CustomerOrderAddress customerOrderAddress = new CustomerOrderAddress();
            CustomerOrderAddress address = customerOrderInfoResponse.getAddress();

            BeanUtils.copyProperties(customerOrderInfoResponse, customerOrderInfo);
            BeanUtils.copyProperties(address, customerOrderAddress);

            JsonResponse createOrderResponse = customerOrderInfoFeign.save(customerOrderInfo);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(createOrderResponse.getCode())) {
                msg.append("客户订单号：").append(customerOrderInfoResponse.getCustomerOrderNo())
                    .append("插入客户订单表失败,").append(createOrderResponse.getMsg()).append(",");
                continue;
            }

            JsonResponse createAddrResponse = customerOrderAddressFeign.create(customerOrderAddress);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(createAddrResponse.getCode())) {
                msg.append("客户订单号：").append(customerOrderInfoResponse.getCustomerOrderNo())
                    .append("插入客户订单地址表失败,").append(createAddrResponse.getMsg()).append(",");
                continue;
            }

            List<CustomerOrderItem> items = customerOrderInfoResponse.getItems();

            for (CustomerOrderItem customerOrderItem : items) {
                JsonResponse createItemResponse = customerOrderItemFeign.create(customerOrderItem);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(createItemResponse.getCode())) {
                    msg.append("客户订单号：").append(customerOrderInfoResponse.getCustomerOrderNo())
                        .append("插入客户订单明细表失败,").append(createItemResponse.getMsg()).append(",");
                    continue;
                }
            }
        }
    }

    private void getInInfoResponses(List<InCustomerOrderInfoImportRequest> inInfoImportList, List<CustomerOrderInfoResponse> inInfoResponses) {
        Map<String, List<CustomerOrderItem>> listMap = new HashMap<>(16);
        inInfoImportList.forEach(customerOrderInfoImport -> {
            if (customerOrderInfoImport.getCustomerOrderNo() == null) {
                return;
            }
            CustomerOrderInfoResponse customerOrderInfoResponse = new CustomerOrderInfoResponse();
            CustomerOrderAddress customerOrderAddress = new CustomerOrderAddress();
            CustomerOrderItem customerOrderItem = new CustomerOrderItem();

            BeanUtils.copyProperties(customerOrderInfoImport, customerOrderInfoResponse);
            BeanUtils.copyProperties(customerOrderInfoImport, customerOrderAddress);
            BeanUtils.copyProperties(customerOrderInfoImport, customerOrderItem);

            customerOrderInfoResponse.setAddress(customerOrderAddress);
            inInfoResponses.add(customerOrderInfoResponse);

            if (listMap.containsKey(customerOrderInfoImport.getCustomerOrderNo())) {
                listMap.get(customerOrderInfoImport.getCustomerOrderNo()).add(customerOrderItem);
            } else {
                List<CustomerOrderItem> customerOrderItems = Lists.newArrayList();
                customerOrderItems.add(customerOrderItem);
                listMap.put(customerOrderInfoImport.getCustomerOrderNo(), customerOrderItems);
            }
        });

        inInfoResponses.forEach(customerOrderInfoResponse -> {
            if (listMap.containsKey(customerOrderInfoResponse.getCustomerOrderNo())) {
                customerOrderInfoResponse.setItems(listMap.get(customerOrderInfoResponse.getCustomerOrderNo()));
            }
        });
        removeDuplicate(inInfoResponses);
    }

//    private void getOutInfoResponses(List<OutCustomerOrderInfoImport> outInfoImportList, List<CustomerOrderInfoResponse> outInfoResponses) {
//        Map<String, List<CustomerOrderItem>> listMap = new HashMap<>(16);
//        outInfoImportList.forEach(customerOrderInfoImport -> {
//
//            CustomerOrderInfoResponse customerOrderInfoResponse = new CustomerOrderInfoResponse();
//            CustomerOrderAddress customerOrderAddress = new CustomerOrderAddress();
//            CustomerOrderItem customerOrderItem = new CustomerOrderItem();
//
//            BeanUtils.copyProperties(customerOrderInfoImport, customerOrderInfoResponse);
//            BeanUtils.copyProperties(customerOrderInfoImport, customerOrderAddress);
//            BeanUtils.copyProperties(customerOrderInfoImport, customerOrderItem);
//
//            customerOrderInfoResponse.setAddress(customerOrderAddress);
//            outInfoResponses.add(customerOrderInfoResponse);
//
//            if (listMap.containsKey(customerOrderInfoImport.getCustomerOrderNo())) {
//                listMap.get(customerOrderInfoImport.getCustomerOrderNo()).add(customerOrderItem);
//            } else {
//                List<CustomerOrderItem> customerOrderItems = Lists.newArrayList();
//                customerOrderItems.add(customerOrderItem);
//                listMap.put(customerOrderInfoImport.getCustomerOrderNo(), customerOrderItems);
//            }
//        });
//
//        outInfoResponses.forEach(customerOrderInfoResponse -> {
//            if (listMap.containsKey(customerOrderInfoResponse.getCustomerOrderNo())) {
//                customerOrderInfoResponse.setItems(listMap.get(customerOrderInfoResponse.getCustomerOrderNo()));
//            }
//        });
//        removeDuplicate(outInfoResponses);
//    }

    private StringBuilder checkNotNull(List list, StringBuilder msg) {
        for (int i = 0; i < list.size(); i++) {
            List<String> validate = validatorhelper.validate(list.get(i));
            if (!CollectionUtils.isEmpty(validate)) {
                msg.append("第").append(i + 3).append("行").append(validate.get(0)).append(",");
            }
        }
        return msg;
    }

    private StringBuilder checkWhCode(List<CustomerOrderInfoResponse> list, StringBuilder msg) {
        for (CustomerOrderInfoResponse customerOrderInfoResponse : list) {
            String siteCode = customerOrderInfoResponse.getSiteCode();
            String whCode = customerOrderInfoResponse.getWhCode();
            CdWarehouse cdWarehouseCache = cdWarehouseManager.getCdWarehouseCache(whCode);
            if (cdWarehouseCache != null) {
                if (!siteCode.equals(cdWarehouseCache.getSiteCode())) {
                    msg.append("客户订单号：").append(customerOrderInfoResponse.getCustomerOrderNo()).append("仓库需要在对应服务平台下,");
                }
            } else {
                msg.append("客户订单号：").append(customerOrderInfoResponse.getCustomerOrderNo()).append("根据仓库查询基础数据失败,");
            }
        }
        return msg;
    }

    private void removeDuplicate(List<CustomerOrderInfoResponse> list) {
        LinkedHashSet<CustomerOrderInfoResponse> set = new LinkedHashSet<CustomerOrderInfoResponse>(list.size());
        set.addAll(list);
        list.clear();
        list.addAll(set);
    }

    public String querySiteCode(String siteName) {
        if(ToolUtils.isEmpty(siteName)) {
            return null;
        }
        JsonResponse<PageResponse<EsCompany>> jsonResponse = esCompanyFeign.searchByCodeAndNames(siteName);
        List<EsCompany> list = jsonResponse.data.list;
        List<EsCompany> collect = list.stream().filter(c -> c.getEscoCompanyNameCn().equals(siteName)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            return collect.get(0).getSiteCode();
        }
        EsCompany base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getSiteCode();
    }

    public String queryWhCode(String whName) {
        if(ToolUtils.isEmpty(whName)) {
            return null;
        }
        CdWarehouseRequest param = new CdWarehouseRequest();
        param.setCdwhName(whName);
        JsonResponse<PageResponse<CdWarehouse>> jsonResponse = cdWarehouseFeign.search(param);
        List<CdWarehouse> list = jsonResponse.data.list;
        CdWarehouse base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getWhCode();
    }

    private String queryEquipmentType(String equipmentName) {
        if(ToolUtils.isEmpty(equipmentName)) {
            return null;
        }
        //CdTsEquipmentGroup cdTsEquipmentGroup = new CdTsEquipmentGroup();
        //cdTsEquipmentGroup.setCdegNameCn(equipmentName);
        //JsonResponse<PageResponse<CdTsEquipmentGroup>> response = cdTsEquipmentGroupFeign.search(cdTsEquipmentGroup);
        NewCdTsEquipmentGroup cdTsEquipmentGroup = new NewCdTsEquipmentGroup();
        cdTsEquipmentGroup.setPrecisionActualVechile(equipmentName);
        cdTsEquipmentGroup.setTenantCode("annto");
        cdTsEquipmentGroup.setStatus(0);
        JsonResponse<PageResponse<NewCdTsEquipmentGroup>> response = dcQueryFeign.cdTsEquipmentGroup(cdTsEquipmentGroup);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            throw BusinessException.fail("查询车型服务异常");
        }
        if (response.getData() != null && ToolUtils.isNotEmpty(response.getData().getList())) {
            cdTsEquipmentGroup = response.getData().getList().get(0);
            if (ToolUtils.isEmpty(cdTsEquipmentGroup.getCdegCode())) {
                throw BusinessException.fail("车型名称为["+equipmentName+"]的数据对应的车型编码为空");
            }
            return cdTsEquipmentGroup.getCdegCode();
        }else {
            throw BusinessException.fail("基础数据匹配不到车型名称为["+equipmentName+"]的数据");
        }
    }

    /**
     * @description: 获取商品编码
     * @param: [customerCode, upperItemCode]
     * @return: java.lang.String
     * @author: 陈永培
     * @createtime: 2019/8/28 20:53
     */
    public String getCdcmMaterialNo(String customerCode, String upperItemCode) {
        String code;
        String cdcmMaterialNo = "";
        List<CdOwnerGroupDetail> groupDetailList = cdOwnerGroupDetailManager.getCdOwnerGroupDetailCacheByOwnerCode(customerCode);
        if (!CollectionUtils.isEmpty(groupDetailList)) {
            for (CdOwnerGroupDetail cdOwnerGroupDetail : groupDetailList) {
                String ownerGroupCode = cdOwnerGroupDetail.getOwnerGroupCode();
                JsonResponse<CdCommonMaterial> cdCommonMaterialByItemCode = omsCdCommonMaterialFegin.getCdCommonMaterialByCustItemCode(upperItemCode , ownerGroupCode );
                code = cdCommonMaterialByItemCode.getCode();
                if (BaseCodeEnum.SUCCESS.getCode().equals(code)) {
                    CdCommonMaterial data = cdCommonMaterialByItemCode.getData();
                    if (null != data){
                        cdcmMaterialNo = data.getCdcmMaterialNo();
                        break;
                    }
                }
            }
        }
        return cdcmMaterialNo;
    }

    /**
     * @description: 获取商品编码
     * @param: [customerCode, upperItemCode]
     * @return: java.lang.String
     * @author: 陈永培
     * @createtime: 2019/8/28 20:53
     */
    public CdCommonMaterial getCdcmMaterialNo(String customerCode, CustomerOrderItem orderItem, String inOutType) {
        String upperItemCode = orderItem.getCustomerItemCode();
        List<CdOwnerGroupDetail> groupDetailList = cdOwnerGroupDetailManager.getCdOwnerGroupDetailCacheByOwnerCode(customerCode);
        if (!CollectionUtils.isEmpty(groupDetailList)) {
            for (CdOwnerGroupDetail cdOwnerGroupDetail : groupDetailList) {
                String ownerGroupCode = cdOwnerGroupDetail.getOwnerGroupCode();
                JsonResponse<CdCommonMaterial> cdCommonMaterialByItemCode = omsCdCommonMaterialFegin.getCdCommonMaterialByCustItemCode(upperItemCode, ownerGroupCode);
                if (cdCommonMaterialByItemCode.judgeSuccess()){
                    CdCommonMaterial material = cdCommonMaterialByItemCode.getData();
                    if (null != material){
                        Double cdcmWeight = material.getCdcmWeight(); //基础数据单台毛重
                        Double cdcmCube = material.getCdcmCube(); //基础数据单台体积
                        BigDecimal totalGrossWeight = orderItem.getTotalGrossWeight(); //总毛重
                        BigDecimal totalVolume = orderItem.getTotalVolume(); //总体积
                        //校验用来计费自费字段是否全空
                        boolean forChargeFieldsIsAllNull = cdcmWeight == null && cdcmCube == null && totalGrossWeight == null && totalVolume == null;
                        String mustInputFieldNames = "总重量、总体积";
                        if (InOutType.OUT.getName().equals(inOutType)){ //出库导入
                            forChargeFieldsIsAllNull = forChargeFieldsIsAllNull && orderItem.getGrossWeight() == null && orderItem.getVolume() == null;
                            mustInputFieldNames += "、商品体积、商品重量";
                        }
                        if (forChargeFieldsIsAllNull) {
                            throw BusinessException.fail("商品基础数据的单台毛重和单台体积为空，" + mustInputFieldNames + "需至少填一项");
                        }

                        BigDecimal planQty = orderItem.getPlanQty();
                        Boolean planQtyIsOk = planQty != null && planQty.compareTo(BigDecimal.ZERO) > 0;
                        //优先级：基础数据单台>单行总>单行单台
                        if (cdcmWeight != null) {
                            orderItem.setGrossWeight(BigDecimal.valueOf(cdcmWeight));
                            if (planQtyIsOk) {
                                orderItem.setTotalGrossWeight(orderItem.getGrossWeight().multiply(planQty));
                            }
                        } else if (totalGrossWeight != null && planQtyIsOk) {
                            orderItem.setGrossWeight(totalGrossWeight.divide(planQty, 8, RoundingMode.HALF_UP));
                        }
                        if (cdcmCube != null) {
                            orderItem.setVolume(BigDecimal.valueOf(cdcmCube));
                            if (planQtyIsOk) {
                                orderItem.setTotalVolume(orderItem.getVolume().multiply(planQty));
                            }
                        } else if (totalVolume != null && planQtyIsOk) {
                            orderItem.setVolume(totalVolume.divide(planQty, 8, RoundingMode.HALF_UP));
                        }

                        return material;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 按导入体积总量计算
     * @param customerOrderItem
     * @param customerOrderItemResponse
     */
    private void setItemValueByImportValue(CustomerOrderItem customerOrderItem, CustomerOrderInfoResponse customerOrderItemResponse) {
        BigDecimal planQty = customerOrderItemResponse.getPlanQty();
        Boolean planQtyIsOk = planQty != null && planQty.compareTo(BigDecimal.ZERO) > 0;
        //体积
        if (null != customerOrderItemResponse.getTotalVolume() && BigDecimal.ZERO.compareTo(customerOrderItemResponse.getTotalVolume()) < 0) {
            customerOrderItem.setTotalVolume(customerOrderItemResponse.getTotalVolume());
            if (planQtyIsOk) {
                customerOrderItem.setVolume(customerOrderItemResponse.getTotalVolume().divide(planQty, 8, RoundingMode.HALF_UP));
            }
        } else if (null != customerOrderItemResponse.getVolume() && BigDecimal.ZERO.compareTo(customerOrderItemResponse.getVolume()) < 0) {
            customerOrderItem.setVolume(customerOrderItemResponse.getVolume());
            if (planQtyIsOk) {
                customerOrderItem.setTotalVolume(planQty.multiply(customerOrderItemResponse.getVolume()));
            }
        } else {
            customerOrderItem.setVolume(BigDecimal.ZERO);
            customerOrderItem.setTotalVolume(BigDecimal.ZERO);
        }
        //毛重
        if (null != customerOrderItemResponse.getTotalGrossWeight() && BigDecimal.ZERO.compareTo(customerOrderItemResponse.getTotalGrossWeight()) < 0) {
            customerOrderItem.setTotalGrossWeight(customerOrderItemResponse.getTotalGrossWeight());
            if (planQtyIsOk) {
                customerOrderItem.setGrossWeight(customerOrderItemResponse.getTotalGrossWeight().divide(planQty, 8, RoundingMode.HALF_UP));
            }
        } else if (null != customerOrderItemResponse.getGrossWeight() && BigDecimal.ZERO.compareTo(customerOrderItemResponse.getGrossWeight()) < 0) {
            customerOrderItem.setGrossWeight(customerOrderItemResponse.getGrossWeight());
            if (planQtyIsOk) {
                customerOrderItem.setTotalGrossWeight(planQty.multiply(customerOrderItemResponse.getGrossWeight()));
            }
        } else {
            customerOrderItem.setGrossWeight(BigDecimal.ZERO);
            customerOrderItem.setTotalGrossWeight(BigDecimal.ZERO);
        }
    }

    /**
     * 上传OSS
     * @param file
     * @param inOutType
     * @param importVersion
     * @return
     */
    public JsonResponse importExcel(MultipartFile file, OrderImportForm importForm) {
        String userCode = iSsoService.getUserCode();
        if (org.apache.commons.lang.StringUtils.isBlank(userCode)) {
            throw BusinessException.fail("用户未登录");
        }

        String inOutType = importForm.getInOutType();
        Integer importVersion = importForm.getImportVersion();
        String isAuto = importForm.getIsAuto();
        String anntoBatchId = importForm.getAnntoBatchId();
        String orderSourceFlag = importForm.getOrderSourceFlag();

        // 校验文件格式
        validateFile(file, inOutType);

        CostOrderInfoImportFileRequest request = new CostOrderInfoImportFileRequest();
        request.setFileName(file.getOriginalFilename());
        request.setUserCode(userCode);

        String value = redisLockHelper.getValue(CostOrderInfoStatusEnum.ORDER_REPORT_LOCK + request.getLockKey());
        if (null != value) {
            //订单中心导入的异常提示发送站内信
            importVersion = Optional.ofNullable(request.getImportVersion()).orElse(0);
            if (org.apache.commons.lang.StringUtils.equals("YQ", inOutType)) {
                throw BusinessException.fail("[" + file.getOriginalFilename() + "]系统正在导入，请稍等");
            }
            if (OrderType.isMFOrder(inOutType)) {
                //发送站内信
                otpMessageHelper.sendMessage("[" + file.getOriginalFilename() + "]系统正在导入，请稍等", "[" + file.getOriginalFilename() + "]导入结果", 1);
            }
            if (OrderType.isTFOrder(inOutType)) {
                //发送站内信
                otpMessageHelper.sendMessage("[" + file.getOriginalFilename() + "]系统正在导入，请稍等", "[" + file.getOriginalFilename() + "]导入结果", 1);
            }
            if (org.apache.commons.lang.StringUtils.equals("CCR", inOutType)) {
                throw BusinessException.fail("[" + file.getOriginalFilename() + "]系统正在导入，请稍等");
            }
            if(org.apache.commons.lang.StringUtils.equals("SC", inOutType)) {
                throw BusinessException.fail("[" + file.getOriginalFilename() + "]系统正在导入，请稍等");
            }
            if(org.apache.commons.lang.StringUtils.equals("VC", inOutType)) {
                throw BusinessException.fail("[" + file.getOriginalFilename() + "]系统正在导入，请稍等");
            }
            //代码扫描问题修复
            //if (importVersion == 2){
            //    //发送站内信
            //    otpMessageHelper.sendMessage("[" + file.getOriginalFilename() + "]系统正在导入，请稍等", "[" + file.getOriginalFilename() + "]导入结果", 1);
            //} else {
            //    //发送站内信
            //    otpMessageHelper.sendMessage("[" + file.getOriginalFilename() + "]系统正在导入，请稍等", "[" + file.getOriginalFilename() + "]导入结果", 1);
            //}
            otpMessageHelper.sendMessage("[" + file.getOriginalFilename() + "]系统正在导入，请稍等", "[" + file.getOriginalFilename() + "]导入结果", 1);
        }

        FileUploadBuilder build = FileUploadBuilder.newBuilder().file(file).bucket("c-loms").userCode(userCode).build();
        String upload = lcFileService.upload(build);
        log.info("订单导入文件url"+upload);
        if (org.apache.commons.lang.StringUtils.isBlank(upload)) {
            throw BusinessException.fail("文件上传异常");
        }
        request.setFileUrl(upload);
        request.setInOutType(inOutType);
        request.setImportVersion(importVersion);
        request.setIsAuto(isAuto);
        request.setAnntoBatchId(anntoBatchId);
        request.setOrderSourceFlag(orderSourceFlag);
        request.setOrderImportForm(importForm);
        return reportFeign.importOrder(request);
    }

    /**
     * 校验文件
     * @param file 文件
     * @param inOutType 类型
     */
    private void validateFile(MultipartFile file, String inOutType) {
        // 逾期类型只能上传xlsx|xls文件
        if (StringUtils.equals("YQ", inOutType)) {
            String originalFilename = file.getOriginalFilename();
            Assert.isTrue(StringUtils.isNotBlank(originalFilename), "文件名为空");
            String extName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            Assert.isTrue(extName.matches("xlsx|xls"), "不能识别该类型，请上传.xlsx、.xls格式的文件");
        }
    }
}
