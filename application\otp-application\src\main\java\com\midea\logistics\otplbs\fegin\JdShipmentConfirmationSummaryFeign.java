package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.dispatch.domain.bean.JdShipmentConfirmationSummary;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: JdShipmentConfirmationSummaryFeign
* Author: ex_chenty1
* Date: 2020-12-22 9:16:34
* Description:收货确认单 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface JdShipmentConfirmationSummaryFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/jdShipmentConfirmationSummary", method = RequestMethod.POST)
    JsonResponse create(@RequestBody JdShipmentConfirmationSummary jdShipmentConfirmationSummary);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/jdShipmentConfirmationSummary/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/jdShipmentConfirmationSummary/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody JdShipmentConfirmationSummary jdShipmentConfirmationSummary);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/jdShipmentConfirmationSummary/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/jdShipmentConfirmationSummarys", method = RequestMethod.POST)
    JsonResponse search(@RequestBody JdShipmentConfirmationSummary jdShipmentConfirmationSummary);
}
