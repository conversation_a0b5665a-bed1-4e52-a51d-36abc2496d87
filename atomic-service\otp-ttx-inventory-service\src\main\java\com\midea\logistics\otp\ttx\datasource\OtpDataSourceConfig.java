package com.midea.logistics.otp.ttx.datasource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2019-8-27 11:12
 */
@Configuration
@MapperScan(basePackages = "com.midea.logistics.otp.ttx.mapper.otp",
    sqlSessionFactoryRef="otpSqlSessionFactory")
public class OtpDataSourceConfig {

    @Autowired
    @Qualifier("otpDataSource")
    private DataSource otpDataSource;

    @Bean(name="otpSqlSessionFactory")
    public SqlSessionFactory otpSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(otpDataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/otp/*.xml"));
        return bean.getObject();
    }


    @Bean(name="otpSqlSessionTemplate")
    public SqlSessionTemplate otpSqlSessionTemplate(@Qualifier("otpSqlSessionFactory") SqlSessionFactory sqlSessionFactory)  {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
