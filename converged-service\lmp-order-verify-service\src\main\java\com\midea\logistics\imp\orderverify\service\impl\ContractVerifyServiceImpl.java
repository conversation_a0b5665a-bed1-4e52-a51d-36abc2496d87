package com.midea.logistics.imp.orderverify.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.domain.mdm.domain.MdmDataDictionaryDetail;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.ContractVerifyService;
import com.midea.logistics.otp.common.bean.SuContractExt;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.order.PmsFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.CustomerContractConfigFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.helper.ContractSwitchHelper;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.OmsSuContractFegin;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.converged.domain.request.OrderInfoRequest;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.dto.SuContractDto;
import com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ContractVerifyServiceImpl implements ContractVerifyService {

    @Autowired
    CustomerContractConfigFeign customerContractConfigFeign;

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @Autowired
    OmsSuContractFegin omsSuContractFegin;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private PmsFeign pmsFeign;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private ContractSwitchHelper contractSwitchHelper;
    @Autowired
    private DictHelper dictHelper;

    /** 
    * @description: !@合同校验 - 中台
    * @param: [orderInfoExt] 
    * @return: com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt 
    * @author: 陈永培
    * @createtime: 2022/7/18 14:55
    */ 
    @Override
    @ZeebeFlow("CONTRACT_VERIFY")
    public OrderInfoExt contractVerify(OrderInfoExt orderInfoExt) {

        if (StringUtils.isBlank(orderInfoExt.getSourceSystem())) {
            throw BusinessException.fail("sourceSystem 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfoExt.getBusinessMode())) {
            throw BusinessException.fail("businessMode 为空，无法确认合同状态");
        }

        // 没有 businessType，挪到这里
        if (businessHelper.isSxqjExpress(orderInfoExt)) {
            lmpOrderFlowHelper.updateOrderInfo(orderInfoExt, "更新合同信息");
            FlowListenerParam.success("不校验合同");
            return orderInfoExt;
        }

        //2020-5-7 14:18:54 阳阳： 加上非自提的判断，自提-业务类型可以为空
        if (StringUtils.isBlank(orderInfoExt.getBusinessType()) && ! (DeliveryType.ZT.getKey().equals(orderInfoExt.getDeliveryType()))) {
            throw BusinessException.fail("非自提订单，businessType 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfoExt.getOrderType())) {
            throw BusinessException.fail("orderType 为空，无法确认合同状态");
        }

        //家电单可能没有平台，挪到这
        if (OrderType.isSOOrder(orderInfoExt.getOrderType())) {
            lmpOrderFlowHelper.updateOrderInfo(orderInfoExt, "更新合同信息");
            FlowListenerParam.success("服务单不校验合同");
            return orderInfoExt;
        }

        if (StringUtils.isBlank(orderInfoExt.getCustomerCode())) {
            throw BusinessException.fail("customerCode 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfoExt.getSiteCode())) {
            throw BusinessException.fail("siteCode 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfoExt.getInOutType())) {
            throw BusinessException.fail("inOutType 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfoExt.getDeliveryType())) {
            throw BusinessException.fail("deliveryType 为空，无法确认合同状态");
        }


        SourceSystem sourceSystem = SourceSystem.getByKey(orderInfoExt.getSourceSystem());
        if (sourceSystem == null) {
            throw BusinessException.fail("系统来源类型找不到");
        }
        //gsc：云仓客户不进行合同校验,InvoiceUnitCode默认0
//        if (businessHelper.isCLoudWhCustomerFlag(orderInfoExt.getCustomerCode())) {
//            orderInfoExt.setInvoiceUnitCode(CommonConstant.N);
//            lmpOrderFlowHelper.updateOrderInfo(orderInfoExt, "更新合同信息");
//            FlowListenerParam.success("云仓客户无需合同校验");
//            return orderInfoExt;
//        }
        BusinessMode businessMode = BusinessMode.valueOf(orderInfoExt.getBusinessMode());
        DeliveryType deliveryType = DeliveryType.getDeliveryType(orderInfoExt.getDeliveryType());
        OrderType orderType = OrderType.valueOf(orderInfoExt.getOrderType());
        InOutType inOutType = InOutType.valueOf(orderInfoExt.getInOutType());
        FeeType feeType = null;
        if (StringUtils.isNotEmpty(orderInfoExt.getOrderRpFlag())) {
            feeType = FeeType.valueOf(orderInfoExt.getOrderRpFlag());
        }

        // 处理子单，是否代收 标识的修改, 和crm 合同的判断
        contractSwitchHelper.handleCollectionFlag(orderInfoExt);

        OrderInfo orderInfo = lmpOrderFlowHelper.getOrderInfo(orderInfoExt.getOrderNo());
        // MGWMS 不校验合同
        boolean noVerifyFlag = SourceSystem.isMGWMS(orderInfo.getSourceSystem());
        if (noVerifyFlag) {
            lmpOrderFlowHelper.updateOrderInfo(orderInfoExt, "更新合同信息");
            FlowListenerParam.success(SourceSystem.getName(orderInfo.getSourceSystem()) + "订单不校验合同");
            return orderInfoExt;
        }
        //由于前面的节点可能到这个节点才做保存操作，如果这里直接退出可能导致前面解析的数据没有保存上，因此这里需要保存一下
        if (orderFlowHelper.checkBaseVerifyContract(orderInfo.getParentOrderNo(), deliveryType, orderInfo.getSiteCode(), orderInfoExt.getOrderDistinctionFlag())) {
            lmpOrderFlowHelper.updateOrderInfo(orderInfoExt, "更新合同信息");
            FlowListenerParam.success("基地自提订单不校验合同");
            return orderInfoExt;
        }
        String contractCustomerCode = null;
        String contractCustomerName = null;


        // 客户作为虚拟客户，查找合同客户
        CustomerContractConfig customerContractConfig = new CustomerContractConfig();
        customerContractConfig.setVirtualCustomerCode(orderInfoExt.getCustomerCode());
        customerContractConfig.setSourceSystem(orderInfoExt.getSourceSystem());
        //1、	解析虚拟客户时，根据虚拟客户、仓库、来源系统找，若找不到则依次去掉虚拟客户、仓库继续找 2019.10.12
        // 2、	B2C的分拨单解析虚拟客户时，仓库取upperSenderCode 2019.10.12
        customerContractConfig.setWhCode(orderInfoExt.getWhCode());
        if (BusinessMode.B2C.getName().equals(orderInfoExt.getBusinessMode()) && OrderType.DO.getKey().equals(orderInfoExt.getOrderType())){
            customerContractConfig.setWhCode(orderInfoExt.getUpperSenderCode());
        }
        customerContractConfig.setUpperWhCode(orderInfoExt.getUpperWhCode());
        /**
         * 2022年7月6日17:07:28 李娟：
         * 1.来源系统+虚拟客户+仓库；查不到时，转为2来查，2查不到时，转为3
         * 2.来源系统+仓库+虚拟客户为空
         * 3.来源系统+虚拟客户+仓库为空
         * 4、来源系统+虚拟客户为空+仓库为空
         */
        JsonResponse<CustomerContractConfig> search = customerContractConfigFeign.contractVerifySearchContractConfig(customerContractConfig);
        if (!"0".equals(search.getCode())) {
            throw BusinessException.fail("查询虚拟客户合同配置失败: " + search.getMsg());
        }

        // 合同客户,是否存在, 如果存在，取合同客户
        if (search.data != null) {
            CustomerContractConfig config = search.data;
            if (StringUtils.isBlank(config.getContractCustomerCode())) {
                throw BusinessException.fail("查询到合同虚拟客户配置，但合同客户编码不存在");
            }
            contractCustomerCode = config.getContractCustomerCode();
            contractCustomerName = config.getContractCustomerName();
        }

        // 合同客户,是否存在, 如果不在，直接取订单客户
        if (contractCustomerCode == null) {
            contractCustomerCode = orderInfoExt.getCustomerCode();
            contractCustomerName = orderInfoExt.getCustomerName();
        }

        // 拿到合同会计主体
        orderInfoExt.setContractCustomerCode(contractCustomerCode);
        orderInfoExt.setContractCustomerName(contractCustomerName);

        //菜鸟上门取件合同客户写死:
        if(SourceSystem.CAINIAO.getKey().equals(orderInfoExt.getSourceSystem()) && OrderType.DP.getKey().equals(orderInfoExt.getOrderType())){
            //lj:菜鸟上门取件单的合同客户（不再是写死的），取原单的合同客户，当原单找不到时，取订单客户
            OrderInfo cnDpOriginOrderInfo = businessParamHelper.getCnDpOriginOrderInfo(orderInfoExt.getOriginOrderNo());
            if (null != cnDpOriginOrderInfo) {
                orderInfoExt.setContractCustomerCode(cnDpOriginOrderInfo.getContractCustomerCode());
                orderInfoExt.setContractCustomerName(cnDpOriginOrderInfo.getContractCustomerName());
            } else {
                orderInfoExt.setContractCustomerCode(orderInfoExt.getCustomerCode());
                orderInfoExt.setContractCustomerName(orderInfoExt.getCustomerName());
            }
        }

        List<BusinessType> verifyed = new ArrayList<>();


        // 是否纯运输,是，直接根据业务类型校验
        if (OrderType.YS == orderType) {
            FlowListenerParam flowListenerParam = contractVerifyingAndUpdate(orderInfoExt, null);
            if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                //return param;
                throw BusinessException.fail(flowListenerParam.getErrorMsg());
            }
            FlowListenerParam.success(flowListenerParam.getErrorMsg());
            return orderInfoExt;
        }
        // 是否纯运输,否, 以下都不为运输单

        // 是否入库单，入库单校验 仓储合同
        if (InOutType.IN == inOutType) {
            // 仓储合同 （businessType=仓储），强制OrderCancelHelper 【非最终状态，暂时不更新 订单信息】
            FlowListenerParam param = contractVerifying(orderInfoExt, BusinessType.STORAGE);
            if (FlowStatus.SUCCESS != param.getFlowStatus()) {
                //return param;
                throw BusinessException.fail(param.getErrorMsg());
            }
            FlowListenerParam.success(param.getErrorMsg());
            verifyed.add(BusinessType.STORAGE);
        }

        // 判断装卸,是否计费标识,是否计收入
        // 产生计费
        //2020-4-28 09:11:26 阳阳：加个判断，如果rpFlag为空，就不校验装卸合同
        if (StringUtils.isNotEmpty(orderInfoExt.getOrderRpFlag()) && (FeeType.Y == feeType || FeeType.R == feeType) ) {
            // 装卸合同 （businessType=装卸），强制 【非最终状态，暂时不更新 订单信息】
            FlowListenerParam param = contractVerifying(orderInfoExt, BusinessType.HANDLING);
            if (FlowStatus.SUCCESS != param.getFlowStatus()) {
                //return param;
                throw BusinessException.fail(param.getErrorMsg());
            }
            FlowListenerParam.success(param.getErrorMsg());
            verifyed.add(BusinessType.HANDLING);
        }

        // 为不产生重复校验，如果业务类型已经有过校验，直接
        BusinessType businessType = BusinessType.getBusinessType(orderInfoExt.getBusinessType());
        for (BusinessType v : verifyed) {
            if (null != businessType && v == businessType) {
                String msg = contractverifyingMsg(null, verifyed);
                FlowListenerParam param = contractUpdate(orderInfoExt, msg);
                if (FlowStatus.SUCCESS != param.getFlowStatus()) {
                    //return param;
                    throw BusinessException.fail(param.getErrorMsg());
                }
                FlowListenerParam.success(param.getErrorMsg());
                return orderInfoExt;
            }
        }

        //验证其他的
        FlowListenerParam param = verifyOther(orderInfoExt, deliveryType, verifyed, businessType);
        //非空，证明验证失败，直接返回了..
        if (param != null) {
                //return param;
            throw BusinessException.fail(param.getErrorMsg());
        }

        // 合同校验结果返回
        String msg = contractverifyingMsg(null, verifyed);
        FlowListenerParam flowListenerParam = contractUpdate(orderInfoExt, msg);
        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
            //return param;
            throw BusinessException.fail(flowListenerParam.getErrorMsg());
        }
        FlowListenerParam.success(flowListenerParam.getErrorMsg());

        orderInfoExt.setBusinessType(orderInfo.getBusinessType());
        orderInfoExt.setDeliveryType(orderInfo.getDeliveryType());
        return orderInfoExt;
    }

    private FlowListenerParam contractVerifyingAndUpdate(OrderInfoExt orderInfo, BusinessType businessType) {
        FlowListenerParam param = contractVerifying(orderInfo, businessType);
        if (FlowStatus.SUCCESS == param.getFlowStatus()) {
            param = contractUpdate(orderInfo, null);
        }
        return param;
    }

    /**
     * 更新会计主体
     *
     * @param orderInfo
     * @return
     */
    private FlowListenerParam contractUpdate(OrderInfoExt orderInfo, String msg) {
        lmpOrderFlowHelper.updateOrderInfo(orderInfo, "更新合同信息");
        if (msg == null) {
            BusinessType businessType = BusinessType.getBusinessType(orderInfo.getBusinessType());
            msg = businessType.getValue();
        }

        // 没有验证过，直接跳出
        if ("".equals(msg) && StringUtils.isEmpty(orderInfo.getInvoiceUnitCode())) {
            return FlowListenerParam.success("合同无需校验");
        }

        //cyy:合同校验时，判断合同产品类型productType是否为1，只要有一个合同为1，则认定该客户签订了标准产品合同，合同校验成功后，订单信息通过MQ同步PMS即可，同时审核日志中，增加【客户签订标准产品合同】
//        String extMsg = "";
//        if (CommonEnum.Y.getValue().equals(orderInfo.getIsSyncPms())) {
//            SyncOrderInfoToPmsDto syncOrderInfoToPmsDto = new SyncOrderInfoToPmsDto();
//            syncOrderInfoToPmsDto.setOrderNo(orderInfo.getOrderNo());
//            syncOrderInfoToPmsDto.setPmsMsgType(PmsMsgType.RECEIVE.getKey());
//            pmsFeign.syncOrderInfoToPms(syncOrderInfoToPmsDto);
//            extMsg = ",客户签订标准产品合同";
//        }

        String join = "合同主体: " + orderInfo.getInvoiceUnitCode() + "(" + orderInfo.getInvoiceUnitName() + ")";
        return FlowListenerParam.success(join);
    }

    /**
     * 合同校验过程
     *
     * @param orderInfo
     * @return
     */
    private FlowListenerParam contractVerifying(OrderInfoExt orderInfo, BusinessType businessType) {
        String customerCode = null;
        if(CnDispatchEnum.PRELOADED.getKey().equals(orderInfo.getCnDispatch())){
            customerCode = orderInfo.getUpperSourceCustomerCode();
        }else{
            customerCode = orderInfo.getContractCustomerCode();
        }
        String businessTypeKey = businessType == null ? orderInfo.getBusinessType() : businessType.getKey();
        String siteCode = null;
        // 2023/12/19 glh: 非自提 改成 业务类型=3，  2024/05/29 glh 配送、运输、宅配时
        if (BusinessType.isTwTrans(businessTypeKey)) {
            siteCode = businessParamHelper.getTwTransContractSite(orderInfo, orderInfo.getSiteCode());
        } else {
            siteCode = orderInfo.getSiteCode();
        }
        // 紧急订单合同解析客户平台配置（EM_SC_POINT），针对来源系统为MRP，紧急订单标识为是（emergence_flag=1）的订单，合同解析节点：读取数据字典的客户与平台+解析的计费业务类型读取会计主体；
        if (SourceSystem.isMRP(orderInfo.getSourceSystem()) && ObjectUtil.equals(CommonConstant.FLAG_YES, orderInfo.getEmergenceFlag())) {
            boolean isMrpJspsOrder = orderFlowHelper.isMrpJspsOrder(orderInfo);
            // 即时配送不查字典
            if (!isMrpJspsOrder) {
                List<MdmDataDictionaryDetail> emScPoint = dictHelper.getDict("EM_SC_POINT");
                MdmDataDictionaryDetail mdmDataDictionaryDetail = CollectionUtil.getFirst(emScPoint);
                if (mdmDataDictionaryDetail != null && ObjectUtil.equal(CommonConstant.FLAG_YES, mdmDataDictionaryDetail.getEnableFlag())) {
                    siteCode = mdmDataDictionaryDetail.getCode(); // 编码为服务平台编码
                    customerCode = mdmDataDictionaryDetail.getName(); // 名称为客户编码
                } else {
                    throw BusinessException.fail("未查询到虚拟结算客户，请检查数据字典：EM_SC_POINT");
                }
            }
        }
        SuContractDto suContractDto = new SuContractDto();
        suContractDto.setBusinessType(businessTypeKey);
        suContractDto.setCustomerCode(customerCode);
        suContractDto.setSiteCode(siteCode);
        SuContractDto suContractResult = redisHelper.getContracatAccount(suContractDto);
        if(null == suContractResult) {

            OrderInfoRequest orderInfoRequest = new OrderInfoRequest();
            orderInfoRequest.setBusinessType(businessTypeKey);
            orderInfoRequest.setCustomerCode(customerCode);
            orderInfoRequest.setSiteCode(siteCode);
            // JsonResponse<List<SuContractExt>> suContractResponse = omsSuContractFegin.getContracatAccount(orderInfoRequest);
            JsonResponse<List<SuContractExt>> suContractResponse = contractSwitchHelper.getContracatAccount(orderInfoRequest, orderInfo.getOrderNo());
            if (!"0".equals(suContractResponse.getCode())) {
                throw BusinessException.fail("查询合同用户失败：合同参数：" + JSONObject.toJSONString(orderInfoRequest) + ", 错误信息：" + suContractResponse.getMsg());
            }

            //为空校验失败
            if (CollectionUtils.isEmpty(suContractResponse.data)) {
                String s = "，客户" + orderInfoRequest.getCustomerCode() + "，在" + orderInfoRequest.getSiteCode() + "平台，无" + BusinessType.getName(orderInfoRequest.getBusinessType()) + "合同";//，合同参数：" + JSONObject.toJSONString(orderInfoRequest);

                return FlowListenerParam.fail(ExceptionType.CONTRACT_VERIFY_FAILED_EMPTY, s);
            }

            int size = suContractResponse.data.stream().map(SuContractExt::getAccountEntity).distinct().collect(Collectors.toList()).size();
            if (size > 1) {
                String s = "，客户" + orderInfoRequest.getCustomerCode() + "，在" + orderInfoRequest.getSiteCode() + "平台，" + BusinessType.getName(orderInfoRequest.getBusinessType()) + "合同主体不一致";

                return FlowListenerParam.fail(ExceptionType.CONTRACT_VERIFY_FAILED_DISACCORD, s);
            }
            //cyy:合同校验时，判断合同产品类型productType是否为1，只要有一个合同为1，则认定该客户签订了标准产品合同，合同校验成功后，订单信息通过MQ同步PMS即可，同时审核日志中，增加【客户签订标准产品合同】
//            boolean isBzContractFlag = suContractResponse.data.stream().anyMatch(s -> CommonEnum.Y.getValue().equals(s.getProductType()));
//            if (isBzContractFlag) {
//                suContractDto.setIsSyncPms(CommonEnum.Y.getValue());
//            }

            SuContractExt contract = suContractResponse.data.get(0);
            if (contractSwitchHelper.isCrm(contract)) {
                String cf = orderInfo.getCollectionFlag();
                if (CollectionFlagEnum.isY(cf) && ! (CommonEnum.isAllOne(contract.getPaymentCollection()) && CrmPaymentMethod.isY(contract.getPaymentMethod()))) {
                    String s = "，平台" + orderInfoRequest.getSiteCode() + "，客户" + orderInfoRequest.getCustomerCode() +"，无线上代收合同";
                    return FlowListenerParam.fail(ExceptionType.CONTRACT_VERIFY_COLLECTION_FLAG, s);
                }
                if (CollectionFlagEnum.isZ(cf) && ! (CommonEnum.isAllOne(contract.getPaymentCollection()) && CrmPaymentMethod.isZ(contract.getPaymentMethod()))) {
                    String s = "，平台" + orderInfoRequest.getSiteCode() + "，客户" + orderInfoRequest.getCustomerCode() +"，无线下代收合同";
                    return FlowListenerParam.fail(ExceptionType.CONTRACT_VERIFY_COLLECTION_FLAG, s);
                }
            }
            suContractDto.setAccountEntity(contract.getAccountEntity());
            suContractDto.setAccountName(contract.getAccountName());
            suContractDto.setContractCode(contract.getContractCode());
            suContractDto.setCrmContractCode(contract.getCrmContractCode());
            redisHelper.setContracatAccount(suContractDto);
        } else {
            suContractDto.setAccountEntity(suContractResult.getAccountEntity());
            suContractDto.setAccountName(suContractResult.getAccountName());
            suContractDto.setContractCode(suContractResult.getContractCode());
            suContractDto.setCrmContractCode(suContractResult.getCrmContractCode());
//            suContractDto.setIsSyncPms(suContractResult.getIsSyncPms());
        }


        orderInfo.setInvoiceUnitCode(suContractDto.getAccountEntity());
        orderInfo.setInvoiceUnitName(suContractDto.getAccountName());
        orderInfo.setContractNo(suContractDto.getContractCode());
        redisHelper.setContractCode(orderInfo.getOrderNo(), suContractDto.getCrmContractCode());
//        orderInfo.setIsSyncPms(suContractDto.getIsSyncPms());

        return FlowListenerParam.success("成功");
    }

    private static String contractverifyingMsg(String msg, List<BusinessType> verifyed) {
        msg = (msg == null) ? "" : (msg + ",");
        if (verifyed != null && verifyed.size() > 0) {
            for (BusinessType v2 : verifyed) {
                msg += v2.getValue();
                msg += ",";
            }
        }
        if (msg.endsWith(",")) {
            msg = msg.substring(0, msg.length() - 1);
        }
        return msg;
    }

    /**
     * @description: 验证其他的
     * @param:
     * @return:
     * @author: 陈永培
     * @createtime: 2020/5/7 14:26
     */
    private FlowListenerParam verifyOther(OrderInfoExt orderInfo, DeliveryType deliveryType, List<BusinessType> verifyed, BusinessType businessType) {

        // 2020-4-28 12:37:59 阳阳： 自提的单不需要解析
        if (DeliveryType.ZT == deliveryType) {
            return null;
        }

        //验证自身的饿业务类型
        FlowListenerParam param = contractVerifying(orderInfo, businessType);
        if (FlowStatus.SUCCESS != param.getFlowStatus()) {
            return param;
        }

        verifyed.add(businessType);
        return null;
    }

}
