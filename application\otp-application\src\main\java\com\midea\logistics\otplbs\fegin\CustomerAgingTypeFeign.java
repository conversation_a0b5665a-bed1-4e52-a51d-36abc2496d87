package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.CustomerAgingType;
import com.midea.logistics.otp.order.domain.response.CustomerAgingTypeResponse;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: CustomerAgingTypeFeign
* Author: luoh
* Date: 2020-4-16 15:28:33
* Description:客户时效控制 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface CustomerAgingTypeFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/customerAgingType", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerAgingType customerAgingType);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/customerAgingType/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/customerAgingType/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerAgingType customerAgingType);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/customerAgingType/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/customerAgingTypes", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap CustomerAgingType customerAgingType);
    @RequestMapping(value = "/customerAgingType/batchCreateOrUpdateToSave", method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdateToSave(@RequestBody List<CustomerAgingTypeResponse> customerAgingTypeResponse);

    @RequestMapping(value = "/customerAgingType/batchUpdate", method = RequestMethod.POST)
    JsonResponse batchUpdate(@RequestBody List<CustomerAgingType> customerAgingTypes);
}
