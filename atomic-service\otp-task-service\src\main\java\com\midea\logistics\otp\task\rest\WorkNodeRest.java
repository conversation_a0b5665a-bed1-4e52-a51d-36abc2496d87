package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.task.domain.bean.WorkNode;
import com.midea.logistics.otp.task.service.IWorkNodeService;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: WorkNodeController
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:物流节点控制层
 */
@RestController
public class WorkNodeRest extends BaseController<WorkNode> {

    private Logger logger = LoggerFactory.getLogger(WorkNodeRest.class);

    @Autowired
    private IWorkNodeService iWorkNodeService;

    /**
     * 新增
     *
     * @return
     */
    @RequestMapping(value = "/workNode", method = RequestMethod.POST)
    public JsonResponse create(@Valid @RequestBody WorkNode workNode, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            iWorkNodeService.saveWorkNode(workNode);
        };
        return doing.go(request, logger);
    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workNode/{id}", method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            WorkNode workNodeQr = new WorkNode();
            workNodeQr.setId(id);
            WorkNode workNode = iWorkNodeService.selectOneWorkNode(workNodeQr);
            jsonResponse.data = workNode;
        };
        return doing.go(request, logger);

    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workNode/{id}", method = RequestMethod.DELETE)
    public JsonResponse deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            WorkNode workNodeQr = new WorkNode(id);
            iWorkNodeService.deleteWorkNode(workNodeQr);
        };
        return doing.go(request, logger);

    }

    /**
     * @param workNode
     * @return
     */
    @RequestMapping(value = "/workNode/{id}", method = RequestMethod.PUT)
    public JsonResponse update(@PathVariable("id") Long id,  /**@Valid*/@RequestBody WorkNode workNode, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            workNode.setId(id);
            workNode.setVersion(workNode.getVersion());
            iWorkNodeService.updateWorkNode(workNode);
        };
        return doing.go(request, logger);
    }


    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/workNodes", method = RequestMethod.GET)
    public JsonResponse search(@SpringQueryMap WorkNode workNode, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<WorkNode> PageResponse = iWorkNodeService.selectWorkNodePage(workNode);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }

    /**
     * 批量新增
     *
     * @return
     */
    @RequestMapping(value = "/insertWorkNodes", method = RequestMethod.POST)
    public JsonResponse insertBatch(@RequestBody List<WorkNode> workNodes, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            iWorkNodeService.insertBatch(workNodes);
        };
        return doing.go(request, logger);
    }

}
