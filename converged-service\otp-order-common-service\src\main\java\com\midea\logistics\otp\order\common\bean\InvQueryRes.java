package com.midea.logistics.otp.order.common.bean;


import lombok.Data;


@Data
public class InvQueryRes {

    private String companyCode;
    private String lotAtt03;
    private String lotAtt04;
    private String lotAtt05;
    private String lotNum;
    private String lotatts;
    private String ownerCode;
    private int qty;
    private String rownum;
    private String siteCode;
    private String skuCode;
    private String skuName;
    private int useQty;
    private String whCode;
    private String whName;


}