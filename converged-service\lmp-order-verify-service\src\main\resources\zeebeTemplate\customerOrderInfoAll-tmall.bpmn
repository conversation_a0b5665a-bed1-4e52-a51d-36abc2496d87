<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all-tmall" name="父订单流程-tmall" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_08mw2j3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0d15yei" name="商品确认">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mappingOfc" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1oyv8yd</bpmn:incoming>
      <bpmn:outgoing>Flow_08b9w6e</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1oyv8yd" sourceRef="Activity_1k6ho42" targetRef="Activity_0d15yei" />
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08mw2j3</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyv8yd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/addressResolving" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k9k1b7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wi0xbe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kptmf2" name="货权转移校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/cargo/right/transfer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p7943l</bpmn:incoming>
      <bpmn:outgoing>Flow_1vzfg44</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0hgfqjk</bpmn:incoming>
      <bpmn:incoming>Flow_1vzfg44</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_04dmfyf" name="是否地址解析" default="Flow_191u9lk">
      <bpmn:incoming>Flow_111vlgx</bpmn:incoming>
      <bpmn:incoming>Flow_0wogabs</bpmn:incoming>
      <bpmn:outgoing>Flow_191u9lk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k9k1b7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0fo5mo8" name="是否货权转移" default="Flow_0hgfqjk">
      <bpmn:incoming>Flow_05szt6n</bpmn:incoming>
      <bpmn:outgoing>Flow_1p7943l</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hgfqjk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_191u9lk" name="no" sourceRef="Gateway_04dmfyf" targetRef="Activity_18h1y82" />
    <bpmn:sequenceFlow id="Flow_1wi0xbe" sourceRef="Activity_1i377su" targetRef="Activity_18h1y82" />
    <bpmn:sequenceFlow id="Flow_1k9k1b7" name="yes" sourceRef="Gateway_04dmfyf" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and (((customerOrderInfo.deliveryType!=null)=false or (customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT")) and (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO") and (customerOrderInfo.orderSource!=null and customerOrderInfo.orderSource!="HANDLE")))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1p7943l" name="yes" sourceRef="Gateway_0fo5mo8" targetRef="Activity_0kptmf2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["TMALLPO","MRPPO","MRPPI","MRPAI","MRPAO"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Event_09zbrsb" />
    <bpmn:serviceTask id="Activity_07ox4pt" name="统配计费解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/businessCategoryAndTcFlag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1kttxvd</bpmn:incoming>
      <bpmn:outgoing>Flow_05szt6n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_08mw2j3" sourceRef="StartEvent_1" targetRef="Activity_1k6ho42" />
    <bpmn:sequenceFlow id="Flow_05szt6n" sourceRef="Activity_07ox4pt" targetRef="Gateway_0fo5mo8" />
    <bpmn:sequenceFlow id="Flow_08b9w6e" sourceRef="Activity_0d15yei" targetRef="Gateway_1mxezr2" />
    <bpmn:sequenceFlow id="Flow_0hgfqjk" sourceRef="Gateway_0fo5mo8" targetRef="Activity_0uruim6" />
    <bpmn:sequenceFlow id="Flow_1vzfg44" sourceRef="Activity_0kptmf2" targetRef="Activity_0uruim6" />
    <bpmn:exclusiveGateway id="Gateway_1mxezr2" name="是否走收货单位解析" default="Flow_14kvfeh">
      <bpmn:incoming>Flow_08b9w6e</bpmn:incoming>
      <bpmn:outgoing>Flow_14kvfeh</bpmn:outgoing>
      <bpmn:outgoing>Flow_19c6390</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_0itetuh" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="receive" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_19c6390</bpmn:incoming>
      <bpmn:outgoing>Flow_1m7cd95</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_14kvfeh" name="no" sourceRef="Gateway_1mxezr2" targetRef="Gateway_11d16yy" />
    <bpmn:sequenceFlow id="Flow_1m7cd95" sourceRef="Activity_0itetuh" targetRef="Gateway_11d16yy" />
    <bpmn:sequenceFlow id="Flow_19c6390" name="yes" sourceRef="Gateway_1mxezr2" targetRef="Activity_0itetuh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["MRPPO","MRPRO","MRPAO"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_18h1y82" name="标准产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/standardProductAnalysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wi0xbe</bpmn:incoming>
      <bpmn:incoming>Flow_191u9lk</bpmn:incoming>
      <bpmn:outgoing>Flow_1kttxvd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1kttxvd" sourceRef="Activity_18h1y82" targetRef="Activity_07ox4pt" />
    <bpmn:serviceTask id="Activity_1qmqli4" name="仓间调拨解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/allocationParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="allocationParse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_112ju5r</bpmn:incoming>
      <bpmn:outgoing>Flow_0wogabs</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_11d16yy" name="是否仓间调拨" default="Flow_111vlgx">
      <bpmn:incoming>Flow_1m7cd95</bpmn:incoming>
      <bpmn:incoming>Flow_14kvfeh</bpmn:incoming>
      <bpmn:outgoing>Flow_111vlgx</bpmn:outgoing>
      <bpmn:outgoing>Flow_112ju5r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_111vlgx" sourceRef="Gateway_11d16yy" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_112ju5r" name="yes" sourceRef="Gateway_11d16yy" targetRef="Activity_1qmqli4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO") or (customerOrderInfo.sourceSystem="TTX" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AO")) or (customerOrderInfo.sourceSystem="OFC" and (customerOrderInfo.orderType="AO" or customerOrderInfo.orderType="PO" or customerOrderInfo.orderType="RO")) or (customerOrderInfo.sourceSystem="MSS" and (customerOrderInfo.orderType="PO" or customerOrderInfo.orderType="AO")) or (customerOrderInfo.sourceSystem="MRP" and (customerOrderInfo.orderType="AO" or customerOrderInfo.orderType="PO" or customerOrderInfo.orderType="RO")))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0wogabs" sourceRef="Activity_1qmqli4" targetRef="Gateway_04dmfyf" />
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all-tmall">
      <bpmndi:BPMNEdge id="Flow_19c6390_di" bpmnElement="Flow_19c6390">
        <di:waypoint x="695" y="180" />
        <di:waypoint x="740" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="162" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m7cd95_di" bpmnElement="Flow_1m7cd95">
        <di:waypoint x="840" y="180" />
        <di:waypoint x="865" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14kvfeh_di" bpmnElement="Flow_14kvfeh">
        <di:waypoint x="670" y="155" />
        <di:waypoint x="670" y="100" />
        <di:waypoint x="880" y="100" />
        <di:waypoint x="880" y="165" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="811" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vzfg44_di" bpmnElement="Flow_1vzfg44">
        <di:waypoint x="940" y="510" />
        <di:waypoint x="940" y="460" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hgfqjk_di" bpmnElement="Flow_0hgfqjk">
        <di:waypoint x="1045" y="420" />
        <di:waypoint x="990" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08b9w6e_di" bpmnElement="Flow_08b9w6e">
        <di:waypoint x="600" y="180" />
        <di:waypoint x="645" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05szt6n_di" bpmnElement="Flow_05szt6n">
        <di:waypoint x="1321" y="420" />
        <di:waypoint x="1095" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08mw2j3_di" bpmnElement="Flow_08mw2j3">
        <di:waypoint x="188" y="180" />
        <di:waypoint x="270" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="890" y="420" />
        <di:waypoint x="788" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p7943l_di" bpmnElement="Flow_1p7943l">
        <di:waypoint x="1070" y="445" />
        <di:waypoint x="1070" y="550" />
        <di:waypoint x="990" y="550" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1048" y="479" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyv8yd_di" bpmnElement="Flow_1oyv8yd">
        <di:waypoint x="370" y="180" />
        <di:waypoint x="500" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kttxvd_di" bpmnElement="Flow_1kttxvd">
        <di:waypoint x="1371" y="340" />
        <di:waypoint x="1371" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191u9lk_di" bpmnElement="Flow_191u9lk">
        <di:waypoint x="1088" y="205" />
        <di:waypoint x="1088" y="300" />
        <di:waypoint x="1321" y="300" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1204" y="278" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wi0xbe_di" bpmnElement="Flow_1wi0xbe">
        <di:waypoint x="1298" y="180" />
        <di:waypoint x="1371" y="180" />
        <di:waypoint x="1371" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9k1b7_di" bpmnElement="Flow_1k9k1b7">
        <di:waypoint x="1113" y="180" />
        <di:waypoint x="1198" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1123" y="163" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_111vlgx_di" bpmnElement="Flow_111vlgx">
        <di:waypoint x="915" y="180" />
        <di:waypoint x="1063" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_112ju5r_di" bpmnElement="Flow_112ju5r">
        <di:waypoint x="890" y="155" />
        <di:waypoint x="890" y="100" />
        <di:waypoint x="940" y="100" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="897" y="125" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wogabs_di" bpmnElement="Flow_0wogabs">
        <di:waypoint x="1040" y="100" />
        <di:waypoint x="1088" y="100" />
        <di:waypoint x="1088" y="155" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="203" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xzxgxd_di" bpmnElement="Activity_0d15yei">
        <dc:Bounds x="500" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="752" y="402" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="760" y="378" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="270" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07zfya5_di" bpmnElement="Activity_0kptmf2">
        <dc:Bounds x="890" y="510" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="890" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fo5mo8_di" bpmnElement="Gateway_0fo5mo8" isMarkerVisible="true">
        <dc:Bounds x="1045" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1037" y="371" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1mxezr2_di" bpmnElement="Gateway_1mxezr2" isMarkerVisible="true">
        <dc:Bounds x="645" y="155" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="633" y="212" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0itetuh_di" bpmnElement="Activity_0itetuh">
        <dc:Bounds x="740" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w1igdz_di" bpmnElement="Activity_07ox4pt">
        <dc:Bounds x="1321" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_18h1y82_di" bpmnElement="Activity_18h1y82">
        <dc:Bounds x="1321" y="260" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="1198" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04dmfyf_di" bpmnElement="Gateway_04dmfyf" isMarkerVisible="true">
        <dc:Bounds x="1063" y="155" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1097" y="153" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11d16yy_di" bpmnElement="Gateway_11d16yy" isMarkerVisible="true">
        <dc:Bounds x="865" y="155" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="860" y="212" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qmqli4_di" bpmnElement="Activity_1qmqli4">
        <dc:Bounds x="940" y="60" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
