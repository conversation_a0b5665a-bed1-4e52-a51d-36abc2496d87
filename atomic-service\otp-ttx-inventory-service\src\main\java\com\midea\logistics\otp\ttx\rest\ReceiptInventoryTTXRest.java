package com.midea.logistics.otp.ttx.rest;

import com.midea.logistics.otp.ttx.inventory.domain.TTXRouters;
import com.midea.logistics.otp.ttx.inventory.domain.bean.ReceiptHeader;
import com.midea.logistics.otp.ttx.inventory.domain.request.ReceiptDataTTXRequest;
import com.midea.logistics.otp.ttx.service.IReceiptInventoryTTXService;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ReceiptInventoryTTXRest
 * Author: caizt
 * Date: 2019-8-23 10:26:21
 * Description:TTX-Excel入库单导入
 */
@RestController
public class ReceiptInventoryTTXRest extends BaseController<ReceiptHeader> {
    @Autowired
    private IReceiptInventoryTTXService iReceiptInventoryTTXService;


    @PostMapping(TTXRouters.TTX_SAVE_RECEIPT)
    public JsonResponse importData(HttpServletRequest request, @RequestBody List<ReceiptDataTTXRequest> requests) {
        return iReceiptInventoryTTXService.importReceiptData(requests);
    }


}
