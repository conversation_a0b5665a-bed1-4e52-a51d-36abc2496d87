<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.ItemClassPriceConfigMapper">

    <select id="selectWithVolumeRoundOne" resultType="com.midea.logistics.otp.rule.domain.bean.ItemClassPriceConfig">
        select
        <include refid="searchFieldsSql"/>
        from item_class_price_config
        where delete_flag=0
        and `status`=0
        and `item_class` =#{itemClass}
        and `entity_id` =#{entityId}
        and `single_volume_from` &lt;=#{singleVolumeFrom}
        and `single_volume_to` &gt;#{singleVolumeTo}
        limit 1
    </select>

</mapper>