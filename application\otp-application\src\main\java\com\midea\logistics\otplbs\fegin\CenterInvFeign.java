package com.midea.logistics.otplbs.fegin;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.midea.logistics.logisticsbopsdk.constants.RequestUrlConstant;
import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.request.BatchCenterInvQueryReq;
import com.midea.logistics.otp.order.domain.request.CenterInvQueryReq;
import com.mideaframework.core.web.JsonResponse;


@FeignClient(value = "logistics-bop-service" ,url= BaseRouter.LOGISTICS_OTP_BOP_SERVICE)
public interface CenterInvFeign {
    @GetMapping(value = RequestUrlConstant.CENTER_INV_QUERY)
    JsonResponse<String> queryCenterInvInfo(@SpringQueryMap CenterInvQueryReq centerInvQueryReq);

    @PostMapping(value = RequestUrlConstant.BATCH_CENTER_INV_QUERY)
    JsonResponse<String> batchQueryCenterInvInfo(@RequestBody BatchCenterInvQueryReq centerInvQueryReq);
}
