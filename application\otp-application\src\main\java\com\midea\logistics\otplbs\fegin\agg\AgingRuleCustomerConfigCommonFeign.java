package com.midea.logistics.otplbs.fegin.agg;

import com.midea.logistics.otp.bean.agg.AgingRuleCustomerConfig;
import com.midea.logistics.otp.bean.agg.AgingRuleCustomerConfigDto;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "AgingRuleCustomerConfigCommonFeign")
public interface AgingRuleCustomerConfigCommonFeign {

    @GetMapping("/aging/rule/customer/config/list")
    JsonResponse<List<AgingRuleCustomerConfig>> agingRuleCustomerConfigList(@SpringQueryMap AgingRuleCustomerConfig entity);

    @GetMapping("/aging/rule/customer/config/page")
    JsonResponse agingRuleCustomerConfigPage(@SpringQueryMap AgingRuleCustomerConfig entity);

    @GetMapping("/aging/rule/customer/config/count")
    JsonResponse<BaseDomain> agingRuleCustomerConfigCount(@SpringQueryMap AgingRuleCustomerConfig entity);

    @GetMapping("/aging/rule/customer/config/detail")
    JsonResponse<AgingRuleCustomerConfig> agingRuleCustomerConfigDetail(@RequestParam("id") Long id);

    @GetMapping("/aging/rule/customer/config/detail")
    JsonResponse<AgingRuleCustomerConfig> agingRuleCustomerConfigDetail(@SpringQueryMap AgingRuleCustomerConfig entity);

    @PostMapping("/aging/rule/customer/config/add")
    JsonResponse<BaseDomain> agingRuleCustomerConfigAdd(@RequestBody AgingRuleCustomerConfig entity);

    @PutMapping("/aging/rule/customer/config/update")
    JsonResponse<Integer> agingRuleCustomerConfigUpdate(@RequestBody AgingRuleCustomerConfig entity);

    @DeleteMapping("/aging/rule/customer/config/remove")
    JsonResponse<Integer> agingRuleCustomerConfigRemove(@RequestBody AgingRuleCustomerConfig entity);

    @GetMapping("/agingRuleCustomerConfig/pageList")
    JsonResponse agingRuleCustomerConfigPageList(@SpringQueryMap AgingRuleCustomerConfig entity);

    @GetMapping("/agingRuleCustomerConfig/pageListAgingCode")
    public JsonResponse agingRuleCustomerConfigPageListAgingCode(@SpringQueryMap AgingRuleCustomerConfig entity);

    @PostMapping("/agingRuleCustomerConfig/batchUpdateList")
    JsonResponse batchUpdateList(@RequestBody List<AgingRuleCustomerConfig> list);

    @PostMapping("/agingRuleCustomerConfig/datailList")
    JsonResponse datailList(@RequestBody AgingRuleCustomerConfig t);

    @PostMapping("/agingRuleCustomerConfig/createOrUpdate")
    JsonResponse createOrUpdate(@RequestBody AgingRuleCustomerConfigDto entity);


    @GetMapping("/agingRuleCustomerConfig/getAgingCodeList")
   JsonResponse getAgingCodeList(@SpringQueryMap AgingRuleCustomerConfig t);

}

