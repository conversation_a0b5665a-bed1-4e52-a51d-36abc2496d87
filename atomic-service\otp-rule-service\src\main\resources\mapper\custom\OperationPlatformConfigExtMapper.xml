<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.OperationPlatformConfigExtMapper">

    <select id="selectDistant" resultType="com.midea.logistics.otp.rule.domain.bean.OperationPlatformConfig">
        select
        <include refid="com.midea.logistics.otp.rule.mapper.common.OperationPlatformConfigMapper.searchFieldsSql"></include>
        from operation_platform_config t
        <include refid="whereFieldsSql"/>
    </select>

    <update id="batchDeleteByIds">
            UPDATE operation_platform_config
            set `delete_flag`=1 , `version` = `version` + 1
            where delete_flag=0
            and `id` in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="siteCode !=null">
            and `site_code` =#{siteCode}
        </if>
        <if test="sourceSystem !=null">
            and `source_system` =#{sourceSystem}
        </if>
        <if test="customerCode !=null">
            and `customer_code` =#{customerCode}
        </if>
        <if test="receiverCityCode !=null">
            and `receiver_city_code` =#{receiverCityCode}
        </if>
        <if test="receiverProvinceCode !=null">
            and `receiver_province_code` =#{receiverProvinceCode}
        </if>
    </sql>

</mapper>