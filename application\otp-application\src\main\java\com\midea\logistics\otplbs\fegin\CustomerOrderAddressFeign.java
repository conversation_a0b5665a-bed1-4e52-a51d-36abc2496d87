package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CustomerOrderAddressFeign
 * Author: lindq2
 * Date: 2019-5-23 9:52:10
 * Description:订单发件人收件人信息表 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface CustomerOrderAddressFeign {

    /**
     * @return 根据orderNo查询
     */
    @RequestMapping(value = OrderRouters.CUSTOMER_ORDER_ADDRESS_FIND_BY_ORDERNO, method = RequestMethod.GET)
    public JsonResponse findByOrderNo(String orderNo);

    /**
     * 查询list
     *
     * @param customerOrderAddress
     * @return
     */
    @RequestMapping(value = OrderRouters.CUSTOMER_ORDER_ADDRESS_LIST, method = RequestMethod.GET)
    public JsonResponse list(@SpringQueryMap CustomerOrderAddress customerOrderAddress);


    @RequestMapping(value = "/customerOrderAddress", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerOrderAddress customerOrderAddress);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderAddress/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderAddress/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerOrderAddress customerOrderAddress);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderAddress/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/customerOrderAddresss", method = RequestMethod.POST)
    JsonResponse search(@RequestBody CustomerOrderAddress customerOrderAddress);
}
