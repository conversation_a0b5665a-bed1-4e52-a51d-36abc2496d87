package com.midea.logistics.otp.order.common.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.common.feign.BaseFeign;
import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.WhConfig;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = "logistics-otp-rule-service", url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface WhConfigFeign extends BaseFeign<WhConfig> {


    @GetMapping(OptRuleRouters.WH_CONFIG_LIST)
    JsonResponse<PageResponse<WhConfig>> getWhConfigs(@SpringQueryMap WhConfig whConfig);

}