<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.custom.TaskAddressExtMapper">

    <update id="batchUpdateNull">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_address
            SET network_code = NULL,
            network_name = NULL,
            network_contact = NULL,
            network_tel = NULL,
            network_phone = NULL,
            network_province_code = NULL,
            network_city_code = NULL,
            network_district_code = NULL,
            network_town_code = NULL,
            network_addr = NULL,
            network_city_name = NULL,
            network_district_name = NULL,
            network_town_name = NULL,
            network_province_name = NULL
            where `id` = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateByTaskNo">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_address
            <include refid="com.midea.logistics.otp.task.mapper.common.TaskAddressMapper.setBatchFieldsSql"/>
            where
            `task_no` =
            #{item.taskNo}
        </foreach>
    </update>

    <update id="batchUpdateCanSetEmptyByTaskNo">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_address
            <set>
                `version` = `version` + 1 ,
                <if test="item.receiverTel !=null">
                    `receiver_tel`  = #{item.receiverTel},
                </if>
                <if test="item.receiverTownCode !=null">
                    `receiver_town_code`  = #{item.receiverTownCode},
                </if>
                <if test="item.receiverDistrictCode !=null">
                    `receiver_district_code`  = #{item.receiverDistrictCode},
                </if>
                <if test="item.networkName !=null">
                    `network_name`  = #{item.networkName},
                </if>
                <if test="item.finalCityName !=null">
                    `final_city_name`  = #{item.finalCityName},
                </if>
                <if test="item.finalName !=null">
                    `final_name`  = #{item.finalName},
                </if>
                <if test="item.finalDistrictName !=null">
                    `final_district_name`  = #{item.finalDistrictName},
                </if>
                <if test="item.networkCityCode !=null">
                    `network_city_code`  = #{item.networkCityCode},
                </if>
                <if test="item.finalCountryName !=null">
                    `final_country_name`  = #{item.finalCountryName},
                </if>
                <if test="item.finalDistrictCode !=null">
                    `final_district_code`  = #{item.finalDistrictCode},
                </if>
                <if test="item.senderName !=null">
                    `sender_name`  = #{item.senderName},
                </if>
                <if test="item.senderTownCode !=null">
                    `sender_town_code`  = #{item.senderTownCode},
                </if>
                <if test="item.finalProvinceCode !=null">
                    `final_province_code`  = #{item.finalProvinceCode},
                </if>
                <if test="item.networkDistrictCode !=null">
                    `network_district_code`  = #{item.networkDistrictCode},
                </if>
                <if test="item.senderDistrictCode !=null">
                    `sender_district_code`  = #{item.senderDistrictCode},
                </if>
                <if test="item.networkPhone !=null">
                    `network_phone`  = #{item.networkPhone},
                </if>
                <if test="item.startLng != null">
                    `start_lng`  = #{item.startLng},
                </if>
                <if test="item.finalTel !=null">
                    `final_tel`  = #{item.finalTel},
                </if>
                <if test="item.startLat != null">
                    `start_lat`  = #{item.startLat},
                </if>
                <if test="item.receiverCountryCode !=null">
                    `receiver_country_code`  = #{item.receiverCountryCode},
                </if>
                <if test="item.networkTownName !=null">
                    `network_town_name`  = #{item.networkTownName},
                </if>
                <if test="item.faceSheetHold != null">
                    `face_sheet_hold`  = #{item.faceSheetHold},
                </if>
                <if test="item.finalCountryCode !=null">
                    `final_country_code`  = #{item.finalCountryCode},
                </if>
                <if test="item.finalCityCode !=null">
                    `final_city_code`  = #{item.finalCityCode},
                </if>
                <if test="item.updateUserCode != null">
                    `update_user_code`  = #{item.updateUserCode},
                </if>
                <if test="item.finalProvinceName !=null">
                    `final_province_name`  = #{item.finalProvinceName},
                </if>
                <if test="item.networkCityName !=null">
                    `network_city_name`  = #{item.networkCityName},
                </if>
                <if test="item.networkContact !=null">
                    `network_contact`  = #{item.networkContact},
                </if>
                <if test="item.senderCountryName !=null">
                    `sender_country_name`  = #{item.senderCountryName},
                </if>
                <if test="item.receiverCountryName !=null">
                    `receiver_country_name`  = #{item.receiverCountryName},
                </if>
                <if test="item.senderProvinceName !=null">
                    `sender_province_name`  = #{item.senderProvinceName},
                </if>
                <if test="item.senderCityCode !=null">
                    `sender_city_code`  = #{item.senderCityCode},
                </if>
                <if test="item.networkTownCode !=null">
                    `network_town_code`  = #{item.networkTownCode},
                </if>
                <if test="item.receiverCityCode !=null">
                    `receiver_city_code`  = #{item.receiverCityCode},
                </if>
                <if test="item.networkProvinceName !=null">
                    `network_province_name`  = #{item.networkProvinceName},
                </if>
                <if test="item.remark != null">
                    `remark`  = #{item.remark},
                </if>
                <if test="item.finalTownCode !=null">
                    `final_town_code`  = #{item.finalTownCode},
                </if>
                <if test="item.senderTel !=null">
                    `sender_tel`  = #{item.senderTel},
                </if>
                <if test="item.finalTownName !=null">
                    `final_town_name`  = #{item.finalTownName},
                </if>
                <if test="item.receiverVirtualMobile !=null">
                    `receiver_virtual_mobile`  = #{item.receiverVirtualMobile},
                </if>
                <if test="item.endLat != null">
                    `end_lat`  = #{item.endLat},
                </if>
                <if test="item.networkProvinceCode !=null">
                    `network_province_code`  = #{item.networkProvinceCode},
                </if>
                <if test="item.taskNo !=null">
                    `task_no`  = #{item.taskNo},
                </if>
                <if test="item.networkDistrictName !=null">
                    `network_district_name`  = #{item.networkDistrictName},
                </if>
                <if test="item.senderCountryCode !=null">
                    `sender_country_code`  = #{item.senderCountryCode},
                </if>
                <if test="item.endLng != null">
                    `end_lng`  = #{item.endLng},
                </if>
                <if test="item.receiverProvinceName !=null">
                    `receiver_province_name`  = #{item.receiverProvinceName},
                </if>
                <if test="item.senderCityName !=null">
                    `sender_city_name`  = #{item.senderCityName},
                </if>
                <if test="item.receiverName !=null">
                    `receiver_name`  = #{item.receiverName},
                </if>
                <if test="item.receiverMobile !=null">
                    `receiver_mobile`  = #{item.receiverMobile},
                </if>
                <if test="item.senderMobile !=null">
                    `sender_mobile`  = #{item.senderMobile},
                </if>
                <if test="item.finalMobile !=null">
                    `final_mobile`  = #{item.finalMobile},
                </if>
                <if test="item.receiverDetailAddr !=null">
                    `receiver_detail_addr`  = #{item.receiverDetailAddr},
                </if>
                <if test="item.senderDetailAddr !=null">
                    `sender_detail_addr`  = #{item.senderDetailAddr},
                </if>
                <if test="item.networkTel !=null">
                    `network_tel`  = #{item.networkTel},
                </if>
                <if test="item.networkAddr !=null">
                    `network_addr`  = #{item.networkAddr},
                </if>
                <if test="item.receiverCityName !=null">
                    `receiver_city_name`  = #{item.receiverCityName},
                </if>
                <if test="item.networkCode !=null">
                    `network_code`  = #{item.networkCode},
                </if>
                <if test="item.finalDetailAddr !=null">
                    `final_detail_addr`  = #{item.finalDetailAddr},
                </if>
                <if test="item.senderTownName !=null">
                    `sender_town_name`  = #{item.senderTownName},
                </if>
                <if test="item.receiverDistrictName !=null">
                    `receiver_district_name`  = #{item.receiverDistrictName},
                </if>
                <if test="item.receiverProvinceCode !=null">
                    `receiver_province_code`  = #{item.receiverProvinceCode},
                </if>
                <if test="item.receiverTownName !=null">
                    `receiver_town_name`  = #{item.receiverTownName},
                </if>
                <if test="item.senderDistrictName !=null">
                    `sender_district_name`  = #{item.senderDistrictName},
                </if>
                <if test="item.senderProvinceCode !=null">
                    `sender_province_code`  = #{item.senderProvinceCode},
                </if>
                <if test="item.rollbackFlag !=null">
                    `rollback_flag`  = #{item.rollbackFlag},
                </if>
                <if test="item.senderVirtualMobile !=null">
                    `sender_virtual_mobile`  = #{item.senderVirtualMobile},
                </if>
            </set>
            <where>
                `task_no` = #{item.taskNo}
                and `delete_flag` = 0
                <if test="item.id !=null">
                    and `id` =#{item.id}
                </if>
            </where>
        </foreach>
    </update>

    <select id="searchLastTaskAddressListByOrderNos"
            resultType="com.midea.logistics.otp.task.domain.bean.TaskAddress">
        select
            ta.`receiver_tel` AS  receiverTel,
            ta.`receiver_town_code` AS  receiverTownCode,
            ta.`receiver_district_code` AS  receiverDistrictCode,
            ta.`network_name` AS  networkName,
            ta.`final_city_name` AS  finalCityName,
            ta.`final_name` AS  finalName,
            ta.`final_district_name` AS  finalDistrictName,
            ta.`network_city_code` AS  networkCityCode,
            ta.`final_country_name` AS  finalCountryName,
            ta.`final_district_code` AS  finalDistrictCode,
            ta.`delete_flag` AS  deleteFlag,
            ta.`sender_name` AS  senderName,
            ta.`sender_town_code` AS  senderTownCode,
            ta.`final_province_code` AS  finalProvinceCode,
            ta.`network_district_code` AS  networkDistrictCode,
            ta.`id` AS  id,
            ta.`sender_district_code` AS  senderDistrictCode,
            ta.`network_phone` AS  networkPhone,
            ta.`start_lng` AS  startLng,
            ta.`final_tel` AS  finalTel,
            ta.`start_lat` AS  startLat,
            ta.`receiver_country_code` AS  receiverCountryCode,
            ta.`network_town_name` AS  networkTownName,
            ta.`face_sheet_hold` AS  faceSheetHold,
            ta.`version` AS  version,
            ta.`final_country_code` AS  finalCountryCode,
            ta.`final_city_code` AS  finalCityCode,
            ta.`update_user_code` AS  updateUserCode,
            ta.`final_province_name` AS  finalProvinceName,
            ta.`network_city_name` AS  networkCityName,
            ta.`network_contact` AS  networkContact,
            ta.`sender_country_name` AS  senderCountryName,
            ta.`receiver_country_name` AS  receiverCountryName,
            ta.`sender_province_name` AS  senderProvinceName,
            ta.`sender_city_code` AS  senderCityCode,
            ta.`network_town_code` AS  networkTownCode,
            ta.`receiver_city_code` AS  receiverCityCode,
            ta.`network_province_name` AS  networkProvinceName,
            ta.`remark` AS  remark,
            ta.`final_town_code` AS  finalTownCode,
            ta.`sender_tel` AS  senderTel,
            ta.`final_town_name` AS  finalTownName,
            ta.`receiver_virtual_mobile` AS  receiverVirtualMobile,
            ta.`end_lat` AS  endLat,
            ta.`network_province_code` AS  networkProvinceCode,
            ta.`task_no` AS  taskNo,
            ta.`network_district_name` AS  networkDistrictName,
            ta.`sender_country_code` AS  senderCountryCode,
            ta.`end_lng` AS  endLng,
            ta.`receiver_province_name` AS  receiverProvinceName,
            ta.`sender_city_name` AS  senderCityName,
            ta.`receiver_name` AS  receiverName,
            ta.`create_user_code` AS  createUserCode,
            ta.`receiver_mobile` AS  receiverMobile,
            ta.`sender_mobile` AS  senderMobile,
            ta.`update_time` AS  updateTime,
            ta.`final_mobile` AS  finalMobile,
            ta.`receiver_detail_addr` AS  receiverDetailAddr,
            ta.`sender_detail_addr` AS  senderDetailAddr,
            ta.`network_tel` AS  networkTel,
            ta.`network_addr` AS  networkAddr,
            ta.`create_time` AS  createTime,
            ta.`receiver_city_name` AS  receiverCityName,
            ta.`network_code` AS  networkCode,
            ta.`final_detail_addr` AS  finalDetailAddr,
            ta.`sender_town_name` AS  senderTownName,
            ta.`receiver_district_name` AS  receiverDistrictName,
            ta.`receiver_province_code` AS  receiverProvinceCode,
            ta.`receiver_town_name` AS  receiverTownName,
            ta.`sender_district_name` AS  senderDistrictName,
            ta.`sender_province_code` AS  senderProvinceCode,
            ta.`sender_virtual_mobile` AS  senderVirtualMobile
        from task_address ta join task t on ta.task_no = t.task_no
        where t.distribution_last_flag = 1 and t.delete_flag = 0 and ta.delete_flag = 0
        and t.order_no in (
            <foreach collection="orderNos" item="orderNo" separator=",">
                #{orderNo}
            </foreach>
        )
    </select>
</mapper>