package com.midea.logistics.otplbs.fegin;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.dispatch.domain.request.HongheFileInsertRequest;
import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.converged.domain.bean.DistributionBillingDto;
import com.midea.logistics.otp.order.converged.domain.bean.NetworkFeedback;
import com.midea.logistics.otp.order.converged.domain.request.*;
import com.midea.logistics.otp.order.converged.domain.response.CustomerOrderInfoResponse;
import com.midea.logistics.otp.order.converged.domain.response.VdCloseSearchResponse;
import com.midea.logistics.otp.order.converged.domain.response.VdSplitTaskSearchResponse;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.OrderLog;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExtBatch;
import com.midea.logistics.otp.order.domain.request.*;
import com.midea.logistics.otp.ordertask.converged.domain.request.ValueAddedServicesOrderRequest;
import com.midea.logistics.otp.rule.domain.response.CloseOrderResponse;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.domain.request.AssociatedCooperativeRequest;
import com.mideaframework.core.web.JsonResponse;

/**
 * ©Copyright ©订单中心接口组 ORDER_CENTER
 * FileName: OrderBaseInfoFeign
 * Author: ex_huyh
 * Date: 2019-6-2 21:46:18
 * Description:OrderFeign订单详情-基本信息（基本信息，发货收货信息）
 */
@FeignClient(value = "logistics-otp-order-converged-service",url = BaseRouter.LOGISTICS_OTP_ORDER_CONVERGED_SERVICE)
public interface OrderFeign {

    /**
     * @description: 新增或者修改安得订单
     * @param: [customerOrderNos]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2019/6/10 10:46
     */
    @PostMapping(OrderConvergedRouters.GREATE_OR_ADD_CUSTOMER_ORDER_INFO)
    JsonResponse<CustomerOrderInfoResponse> addOrUpdateCustomerOrderInfo(@RequestBody CustomerOrderInfoResponse customerOrderInfo);

    /**
     * @description: 获取安得订单信息、地址、明细
     * @param: [customerOrderNos]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2019/6/10 10:46
     */
    @GetMapping(OrderConvergedRouters.GET_CUSTOMER_ORDER_INFO)
    public JsonResponse getCustomerOrderInfo(@PathVariable("id") long id);

    /**
     * @param orderNo
     * @return
     */
    @RequestMapping(value = "/order/base/info/{orderNo}", method = RequestMethod.GET)
    //根据订单号查询订单详情-基本信息
    JsonResponse queryOrderBaseInfoByOrderNo(@PathVariable("orderNo") String orderNo);


    /**
     * @description: 客户订单号接收
     * @param: [customerOrderInfoRequest]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2019/6/10 15:39
     */
    @RequestMapping(value = OrderConvergedRouters.RECEIVE_CUSTORM_ORDER_INFO, method = RequestMethod.POST)
    public JsonResponse receiveCustormOrderInfo(@Valid @RequestBody CustomerOrderInfoRequest customerOrderInfoRequest);

    /**
     * @description: 财务单引单
     * @param: [customerOrderInfoRequest]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author:
     * @createtime: 2019/7/10 15:39
     */
    @RequestMapping(value = OrderConvergedRouters.IMPORT_CUSTORM_ORDER_INFO, method = RequestMethod.POST)
    public JsonResponse financialOrderImport(@RequestBody CustomerOrderInfoRequest customerOrderInfoRequest);

    /**
     * @description: 自动关闭订单
     * @param: [customerOrderNos]
     * @return:
     * @author: 胡永辉
     * @createtime: 2019/6/11 22:00
     */
    @RequestMapping(value = "/order/closedCustomerOrderStatusAutos", method = RequestMethod.PUT)
    public JsonResponse closedCustomerOrderStatusAutos(@RequestBody List<String> customerOrderNos);

    /**
     * @description: 手动关闭订单
     * @param: [customerOrderNos]
     * @return:
     * @author: 胡永辉
     * @createtime: 2019/6/11 22:00
     */
    @RequestMapping(value = "/order/closedCustomerOrderStatusMans", method = RequestMethod.PUT)
    public JsonResponse closedCustomerOrderStatusMans(@RequestBody List<String> customerOrderNos);

    /**
     * @description: 财务单手工关闭-查询
     * @param: [customerOrderNos]
     * @return:
     * @author: 胡永辉
     * @createtime: 2019/6/18 22:00
     */
    @RequestMapping(value = "/order/selectFinanceCustomerOrderItems", method = RequestMethod.POST)
    public JsonResponse selectFinanceCustomerOrderItems(@RequestBody List<String> customerOrderNos);

    /**
     * @description: 财务单手工关闭
     * @param: [customerOrderItems]
     * @return:
     * @author: 胡永辉
     * @createtime: 2019/6/18 22:00
     */
    @RequestMapping(value = "/order/closedFinanceCustomerOrderStatusMans", method = RequestMethod.PUT)
    public JsonResponse closedFinanceCustomerOrderStatusMans(@RequestBody List<CustomerOrderItem> customerOrderItems);

    /**
     * 订单冲销,用于页面点击
     *
     * @param orderCancelInnerRequest
     * @return
     */
    @PostMapping(value = OrderRouters.ORDER_INNERCANCLE)
    JsonResponse innerCancel(@RequestBody OrderCancelInnerRequest orderCancelInnerRequest);


    @RequestMapping(value = OrderConvergedRouters.NETWORK_FEEDBACK, method = RequestMethod.POST)
    JsonResponse networkFeedback(@RequestBody NetworkFeedback networkFeedback);


    @RequestMapping(value = OrderRouters.CLOSE_CUSTOMORDERITEMSBYORDERNOS, method = RequestMethod.PUT)
    JsonResponse<CloseOrderResponse> closeOrder(@RequestBody List<CustomerOrderInfoExt> customerOrderInfoExts);


    @RequestMapping(value = OrderRouters.CLOSE_BATCHCLOSEDCUSTOMERORDER, method = RequestMethod.POST)
    JsonResponse<CloseOrderResponse> batchClosedCustomerOrder(@RequestBody List<CustomerOrderInfoExt> customerOrderInfoExts);

    @GetMapping(OrderConvergedRouters.TASK_RETRACT)
    JsonResponse taskRetract(@RequestParam("taskNo") String taskNo);

    @PostMapping(OrderConvergedRouters.BATCH_TASK_RETRACT)
     JsonResponse batchTaskRetract(@RequestBody BatchWithDrawRequest obj);

    @PutMapping(OrderConvergedRouters.ORDER_FLOW_START)
    JsonResponse orderFlowStart(@RequestBody OrderLog orderLog);

    /**
     * 菜鸟增值服务单接单
     *
     * @param valueAddedServicesOrderRequest
     * @return
     */
    @PostMapping(OrderConvergedRouters.VALUE_ADDED_SERVICES_ORDER)
    JsonResponse valueAddedServicesOrder(@RequestBody ValueAddedServicesOrderRequest valueAddedServicesOrderRequest);

    /**
     * 菜鸟增值服务单-反馈上游
     *
     * @param snCode
     * @return
     */
    @GetMapping(OrderConvergedRouters.WMS_INVENTORY_STATUS_UPLOAD)
    JsonResponse wmsInventoryStatusUpload(@RequestParam("vdOrderNo") String vdOrderNo);

    /**
     * 菜鸟增值服务单关闭
     *
     * @param
     * @return
     */
    @GetMapping(OrderConvergedRouters.VALUE_ADDED_SERVICES_ORDER_CONFIRM)
    JsonResponse valueAddedServicesOrderConfirm(@RequestParam("vdOrderNo") String vdOrderNo);

    @RequestMapping(value = OrderRouters.CLOSE_CHECKISCANCLOSEORDER, method = RequestMethod.POST)
    JsonResponse<CloseOrderResponse> checkIsCanCloseOrder( @RequestBody List<String> customerOrderNos);

    @RequestMapping(value = OrderRouters.CLOSE_ORDERLISTBYORDERNOS, method = RequestMethod.POST)
    JsonResponse<List<CustomerOrderInfoExt>> closeOrderListByOrderNos(@RequestBody List<String> customerOrderNos);

    @RequestMapping(value = OrderRouters.CLOSE_BATCH_ORDERLISTBYORDERNOS, method = RequestMethod.POST)
    JsonResponse<CustomerOrderInfoExtBatch> batchCloseOrderListByOrderNos(@RequestBody List<String> orderNos);


    /**
    * @description: 任务中心-货权转移逻辑
    * @param: [task]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2019/8/12 14:11
    */
    @PostMapping(TaskRouters.TASK_TRANSFER_OF_GOODS_RIGHT)
    public JsonResponse transferOfGoodsRight(@RequestBody Task task);

    /**
     * @description: 任务中心-批量货权转移逻辑
     * @param: [searchTaskRequest, request]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2019/8/10 14:03
     */
    @PostMapping(TaskRouters.TASK_BATCH_TRANSFER_OF_GOODS_RIGHT)
    public JsonResponse batchTransferOfGoodsRight(@RequestBody List<String> taskNos);


   /**
   * @description: 关联协同单
   * @param: [task]
   * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.otp.task.domain.bean.Task>
   * @author: 陈永培
   * @createtime: 2019/8/12 14:10
   */
    @PostMapping(value = TaskRouters.TASK_ASSOCIATED_COOPERATIVE_ORDER)
    public JsonResponse<Task> associatedCooperativeOrder(@RequestBody AssociatedCooperativeRequest task);

    /**
     * 修改订单地址
     * @param customerOrderInfo
     * @return
     */
    @PostMapping(OrderConvergedRouters.UPDATE_CUSTOMER_ADDRESS)
    JsonResponse updateCustomerAddress(@RequestBody  CustomerAddressRequest addressRequest);


    /**
     * 异常处理
     * @param ids
     * @return
     */
    @PostMapping(value = OrderRouters.ORDER_EXCEPTION_HANDLESCONVERGE)
    JsonResponse handlesConverge(@RequestBody List<Long> ids);

    @PostMapping(OrderConvergedRouters.TRANSPORT_DISTRIBUTION_BILLING_BY_STATUS)
    public JsonResponse transportDistributionBillingByStatus(@RequestBody DistributionBillingDto rBillingDto);

    @PostMapping(OrderConvergedRouters.CVTE_ORDER_RECEIVER)
    public JsonResponse cvteOrderReceive(@RequestBody CVTEOrderRequest cvteOrderRequest);


    /**
     * 修改配送方式（网点配送退货入库单）
     *
     * @param orderInfos
     * @return
     */
    @PostMapping(OrderConvergedRouters.NET_RI_DELIVERY_TYPE)
    public JsonResponse netRiDeliveryType(@RequestBody List<OrderInfo> orderInfos);

    /**
     * 视源合单重推
     */
    @GetMapping(OrderConvergedRouters.RE_PUSH_CVTE_MERGE_ORDER)
    JsonResponse rePushCvteMergeOrder(@RequestParam("customerOrderNo") String customerOrderNo);

    @PostMapping(OrderConvergedRouters.RETURN_ORDER_FOR_HONGHE)
    public JsonResponse returnOrderForHonghe(@RequestBody HongheFileInsertRequest hongheFileInsertRequest);

    @PostMapping(OrderConvergedRouters.VD_SEPARATE_WAREHOUSE_CANCEL)
    JsonResponse valueAddedServicesCancel(@Valid @RequestBody ValueAddStatusWmsRequest valueAddStatusWmsRequest);

    @PostMapping(OrderConvergedRouters.VD_SEPARATE_WAREHOUSE_CONFIRM)
    JsonResponse separateWarehouse(@RequestBody VdWarehouseConfirmRequest request);

    @PostMapping(OrderConvergedRouters.VD_TASK_ISSUE)
    JsonResponse issueVdTask(@RequestBody List<String> taskNos);

    @RequestMapping(value = OrderConvergedRouters.VD_SEPARATE_WH_QUERY,method = RequestMethod.GET)
    JsonResponse separateWhQuery(@RequestParam("vdOrderNo") String vdOrderNo);

    @RequestMapping(value = OrderConvergedRouters.VD_SERVICE_VERIFY,method = RequestMethod.POST)
    JsonResponse verify(@RequestBody List<String> vdOrderNos);
    @PostMapping(value = "/item/CenterInvQuery")
    JsonResponse itemCenterInvQuery(@RequestBody CenterInvVo centerInvVo);
    @PostMapping(value = "/getSplitedOrderInfoItemsByParentOrderNos")
    public JsonResponse<List<OrderInfoItem>> getSplitedOrderInfoItemsByParentOrderNos(@RequestBody List<SplitedOrderInfoItemsRequest> splitedOrderInfoItemsRequests);

    @RequestMapping(value = OrderConvergedRouters.GREEN_IDENTIFY_CLOSE_ORDER_SEARCH,method = RequestMethod.GET)
    JsonResponse<VdCloseSearchResponse> greenIdentifyCloseOrderSearch(@RequestParam("vdOrderNo") String vdOrderNo);

    @RequestMapping(value = OrderConvergedRouters.GREEN_IDENTIFY_CLOSE_ORDER_CONFIRM,method = RequestMethod.POST)
    JsonResponse greenIdentifyCloseOrderConfirm(@RequestBody VdCloseOrderRequest vdCloseOrderRequest);

    @RequestMapping(value = OrderConvergedRouters.GREEN_IDENTIFY_SPLIT_TASK_SEARCH,method = RequestMethod.GET)
    JsonResponse<VdSplitTaskSearchResponse> greenIdentifySplitTaskSearch(@RequestParam("vdOrderNo") String vdOrderNo);

    @RequestMapping(value = OrderConvergedRouters.GREEN_IDENTIFY_SPLIT_TASK_CONFIRM,method = RequestMethod.POST)
    JsonResponse greenIdentifySplitTaskConfirm(@RequestBody VdSplitTaskConfirmRequest request);

    @RequestMapping(value = OrderConvergedRouters.VALUE_ADD_ORDER_CANCEL,method = RequestMethod.GET)
    JsonResponse valueAddOrderCancel(@RequestParam("vdOrderNo") String vdOrderNo);

}
