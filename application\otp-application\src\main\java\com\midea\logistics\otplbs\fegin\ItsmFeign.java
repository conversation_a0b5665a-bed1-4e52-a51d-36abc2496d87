package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otplbs.bean.ItsmLoginRequest;
import com.midea.logistics.otplbs.bean.ItsmLoginResponse;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(value = "logistics-bop-service" )
public interface ItsmFeign {

    @PostMapping(value = "/inner/T201904230000000014/udpApi/itsmLogin")
    JsonResponse<String> itsmLogin(@RequestBody ItsmLoginRequest itsmLoginRequest);
}
