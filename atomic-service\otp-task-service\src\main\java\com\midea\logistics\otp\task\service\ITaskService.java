package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.bean.JobRequest;
import com.midea.logistics.otp.bean.OrderRequest;
import com.midea.logistics.otp.bean.OrderResponse;
import com.midea.logistics.otp.order.converged.domain.dto.AllOrderInfoDto;
import com.midea.logistics.otp.order.converged.domain.dto.TaskStatusUpdateBatch;
import com.midea.logistics.otp.order.converged.domain.request.ChangeWarehouseTaskRequest;
import com.midea.logistics.otp.order.converged.domain.request.OrderChangedReceiveDateRequest;
import com.midea.logistics.otp.order.domain.bean.custom.OrderBatchSearchRequest;
import com.midea.logistics.otp.order.domain.dto.AiVoiceAppointDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.WmsInChangeWhRequest;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.domain.bean.TaskZsjx;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDetailResponse;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.otp.task.domain.bean.custom.TaskInfoAndDetail;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskDetailsResponse;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.mideaframework.core.web.PageResponse;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: ITaskService
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:ITaskService服务接口
 */
public interface ITaskService {

    Integer saveTask(Task task);

    Integer deleteTask(Task task);
	
    /**
    * @description: 删除任务且递增orderno
    * @param: [task]
    * @return: java.lang.Integer
    * @author: 陈永培
    * @createtime: 2025/4/11 14:35
    */
	Integer deleteByIdAndIncrementOrderNo(Task task);
	
	Integer updateTask(Task task);

    Task selectOneTask(Task task);

    PageResponse<Task> selectTaskPage(Task task);

    List<Task> selectTaskByIndex(Task task);

    int countTaskByIndex(Task task);

    Integer insertBatch(List<Task> tasks);

    Integer batchUpdate(List<Task> tasks);

    Integer batchUpdateTaskForNodeStatus(List<Task> tasks);

    Integer update(Task task);

    Integer updateStatusByTaskNos(TaskStatusUpdateBatch statusUpdateBatch);

    Integer deleteBatch(List<Task> tasks);

    List<Task> selectTask(List<String> orderNoes);

    Task deliveryCount(Task task);

    List<Task> getTasksByOrderNos(List<String> orderNoes);

    List<Task> getTasksByTaskNos(List<String> taskNos);

    PageResponse<SearchTaskResponse> searchTaskList(SearchTaskRequest searchTaskRequest);

    SearchTaskDetailsResponse searchTaskDetails(String taskNo);

    Integer batchUpdateByTask(List<Task> tasks);

    List<TaskDetailsResponse> getTasksAndAddrByOrderNos(List<String> orderNos);

    Task queryByTaskLast(Task taskQr);

    Task queryByTaskLastNow(Task taskQr);

    List<Task> searchTaskListByTaskNos(List<String> taskNos);

    Integer clearDistribution(Task task);

    Integer updateDistribution(Task task);

    Integer updateDispatchNo(List<Task> tasks);
    Integer updateLine(List<Task> tasks);

    List<Task> searchTaskListDoRdo(SearchTaskRequest searchTaskRequest);

    /**
     *根据时间查询任务号
     */
    List<TaskZsjx> searchTaskListByDate(Task task);

    PageResponse<SearchTaskResponse> searchTaskOutList(SearchTaskRequest searchTaskRequest);

    Task selectOneByRDIorRDO(Task search);

    Task selectOneByRDIorRDOorYS(Task search);

    Task selectOneByRDIorRDOorIT(Task search);

    PageResponse<OrderResponse> listTask(OrderRequest orderRequest);

    Integer updateTaskCreateTime(List<OrderChangedReceiveDateRequest> orderChangedReceiveDateRequests);

    Integer batchUpdateByParentOrderNo(List<Task> tasks);

    List<String> selectCancelTaskByUpdateTime(Task task);

    PageResponse<Task> searchCarArrivedTask(TaskExt task);

    Integer updateTaskByIndex(Task task);

    Integer updateTaskCreateTimeAndFlag(List<OrderChangedReceiveDateRequest> orderChangedReceiveDateRequests);

    List<TaskItem> listItemBytaskNo(List<String> taskNoList);

    List<TaskAddress> listAddrBytaskNo(List<String> taskNoList);

    List<String> listDeliveredTaskNo(JobRequest jobRequest);

    List<Task> queryByTaskNos(List<String> taskNos);

    @Transactional(readOnly = false)
    Integer deleteBatchById(List<Long> ids);

    List<Task> selectTaskByIndexIncludeDel(Task task);

    Integer updateChangeWarehouseTask(ChangeWarehouseTaskRequest changeWarehouseTaskRequest);

    Integer searchTaskListByTaskNosCount(OrderBatchSearchRequest searchRequest);

    List<TaskDetailResponse> searchTaskListByTaskNosIndex(OrderBatchSearchRequest searchRequest);

    List<TaskExt> listExtByTaskNos(SearchTaskRequest searchTaskRequest);

    Integer deleteTaskOfTI(Task task);
    
    Integer clearHoldFlag(Task task);
    
    Integer updateTaskRelationNo(Task task);
    
    
    Task selectTaskByTaskNoIncludeDel(Task task);
    
    Integer clearHoldFlagAndSetInvoice(Task task);

	Integer setInvoiceInfo(Task task);

	List<Task> getTasksByCustomerOrderNos(List<String> customerOrderNos);

    Integer getCancelOrderNo(String orderNo);

    Integer batchUpdateTaskCancelOrderNo(List<Task> tasks);

    Integer batchUpdateByTaskNo(List<Task> tasks);

    Integer searchTaskCount(SearchTaskRequest searchTaskRequest);

    Integer updateByIdCanSetEmpty(Task task);

    List<Task> queryByOrderNos(List<String> orderNos);

    Integer batchClearHoldFlagByTaskNo(List<String> taskNos);

    Integer taskZsjx(String param);

    Integer taskJsjxyc(List<String> param);

    List<Task> handOverTimeSearchTasks(AiVoiceAppointDto aiVoiceAppointDto);

    List<Task> holdAppointSearchTasks(AiVoiceAppointDto aiVoiceAppointDto);

    Integer saveTaskInfoAndDetail(TaskInfoAndDetail taskInfoAndDetail);

    Integer wmsInChangeWhByTask(WmsInChangeWhRequest request);
	
	List<AllOrderInfoDto> wfrV3SaveAllTask(List<AllOrderInfoDto> obj);

    List<Task> listByParentOrderNos(List<String> parentOrderNos);

    Integer batchClearHoldFlagByCompleteSetNos(List<String> CompleteSetNos);
}