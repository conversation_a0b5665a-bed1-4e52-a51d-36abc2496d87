package com.midea.logistics.otp.orderverify.config;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OtpExpressConvergedFeignConfig {

    /**
     * 配置请求重试
     */
    @Bean
    public Retryer feignRetryer() {
        return new Retryer.Default(200, 200, 0);
    }

    /**
     * 设置请求超时时间
     * 默认
     * public Options() {
     * this(10 * 1000, 60 * 1000);
     * }
     */
    @Bean
    Request.Options feignOptions() {
        return new Request.Options(5 * 1000, 60 * 1000);
    }


    /**
     * 打印请求日志
     *
     * @return
     */
    @Bean
    public Logger.Level multipartLoggerLevel() {
        return Logger.Level.FULL;
    }
}
