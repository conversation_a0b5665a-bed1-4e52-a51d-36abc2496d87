package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.WmsConfirmRule;
import com.midea.logistics.otp.rule.domain.request.WmsConfirmRuleRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: WmsConfirmRuleFeign
 * Author: luoh
 * Date: 2019-6-6 11:06:05
 * Description:WMS出入库确认规则 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface WmsConfirmRuleFeign {

    @RequestMapping(value = OptRuleRouters.WMS_CONFIRM_RULE_SAVE, method = RequestMethod.POST)
    JsonResponse create(@RequestBody WmsConfirmRuleRequest wmsConfirmRuleRequest);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WMS_CONFIRM_RULE_DETAIL, method = RequestMethod.GET)
    JsonResponse<WmsConfirmRule> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WMS_CONFIRM_RULE_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody WmsConfirmRule wmsConfirmRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WMS_CONFIRM_RULE_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = OptRuleRouters.WMS_CONFIRM_RULE_LIST, method = RequestMethod.GET)
    JsonResponse<PageResponse<WmsConfirmRule>> search(@SpringQueryMap WmsConfirmRuleRequest wmsConfirmRuleRequest);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WMS_CONFIRM_RULE_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<WmsConfirmRuleRequest> wmsConfirmRuleRequests);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WMS_CONFIRM_RULE_BATCH_DELETE_BY_BUSINESSKEY, method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
