package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.StoreApplyInfo;
import com.midea.logistics.otp.order.domain.dto.StoreApplyInfoDto;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: StoreApplyInfoFeign
* Author: lindq2
* Date: 2021-12-20 8:58:23
* Description:商家订购面单信息 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface StoreApplyInfoFeign{

    @RequestMapping(value = "/storeApplyInfo", method = RequestMethod.POST)
    JsonResponse create(@RequestBody StoreApplyInfo storeApplyInfo);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/storeApplyInfo/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/storeApplyInfo/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody StoreApplyInfo storeApplyInfo);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/storeApplyInfo/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/storeApplyInfos", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap StoreApplyInfo storeApplyInfo);

    @PostMapping("/storeApplyInfo/receiveExpressBillReview")
    JsonResponse receiveExpressBillReview(StoreApplyInfo storeApplyInfo);

    @RequestMapping(value = "/updateStoreApplyInfo", method = RequestMethod.PUT)
    JsonResponse updateStoreApplyInfo(@RequestBody StoreApplyInfoDto storeApplyInfos);

    @PostMapping(value = "/storeApplyInfo/anntoCancel")
    JsonResponse anntoCancelStoreApplyInfo(@RequestBody StoreApplyInfoDto storeApplyInfos);
}
