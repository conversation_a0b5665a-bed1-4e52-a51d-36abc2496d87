package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.cache.manager.EbLocationManager;
import com.midea.logistics.cache.manager.MidCustomerControlManager;
import com.midea.logistics.cache.manager.MidSiteWhControlManager;
import com.midea.logistics.domain.mdm.domain.*;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.AnalysisOfReceivingUnitService;
import com.midea.logistics.imp.orderverify.service.CargoRightTransferService;
import com.midea.logistics.logisticsbopsdk.service.OrderBopService;
import com.midea.logistics.otp.bean.ValidateSameWhDto;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.order.SearchWhFeign;
import com.midea.logistics.otp.common.feign.servicefeign.dc.DcAtomicFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbShippersFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.CnDispatchPlanFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.ScPosConfigFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.common.request.ContactsOms;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.common.request.DcWarehouseMapping;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.cache.RuleCache;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.DcMappingHelper;
import com.midea.logistics.otp.order.common.helper.DeliveryTypeHelper;
import com.midea.logistics.otp.order.common.helper.ScPosFlagHelper;
import com.midea.logistics.otp.order.common.service.CenterInvService;
import com.midea.logistics.otp.order.common.service.SeparateWarehouseService;
import com.midea.logistics.otp.order.converged.domain.request.ConsigneeSiteRequest;
import com.midea.logistics.otp.order.converged.domain.request.SearchWhRequest;
import com.midea.logistics.otp.order.converged.domain.response.CdWarehouseDto;
import com.midea.logistics.otp.order.converged.domain.response.ConsigneeSiteResponse;
import com.midea.logistics.otp.order.converged.domain.response.SearchWhResponse;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.dto.CustomerOrderInfoExtendConfDto;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParam;
import com.midea.logistics.otp.rule.domain.bean.ProjectClassifyConfig;
import com.midea.logistics.otp.rule.domain.bean.RatWarehouse;
import com.midea.logistics.otp.rule.domain.bean.ScPosConfig;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.MideaStringUtils;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AnalysisOfReceivingUnitServiceImpl implements AnalysisOfReceivingUnitService {
    @Autowired
    private DeliveryTypeHelper deliveryTypeHelper;

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @Autowired
    private OrderBopService orderBopService;

    @Autowired
    private RatWarehouseFeign ratWarehouseFeign;

    @Autowired
    private CenterInvService centerInvService;

    @Autowired
    private MidCustomerControlManager midCustomerControlManager;

    @Autowired
    private OmsCdCommonMaterialFegin omsCdCommonMaterialFegin;

    @Autowired
    private CargoRightTransferService cargoRightTransferService;

    @Autowired
    private SeparateWarehouseService separateWarehouseService;

    @Autowired
    private CnDispatchPlanFeign cnDispatchPlanFeign;

    @Autowired
    private EbShippersFeign ebShippersFeign;

    @Autowired
    private EbLocationManager ebLocationManager;

    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;

    @Autowired
    private OrderInfoFeign orderInfoFeign;

    @Autowired
    private RuleCache ruleCache;
    @Autowired
    private ScPosConfigFeign scPosConfigFeign;

    @Autowired
    private MidSiteWhControlManager midSiteWhControlManager;

    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private SearchWhFeign searchWhFeign;

    @Autowired
    private DcAtomicFeign dcAtomicFeign;

    @Autowired
    private BusinessParamHelper businessParamHelper;

    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;

    
    @Autowired
    private ScPosFlagHelper scPosFlagHelper;
    @Autowired
    private DcMappingHelper dcMappingHelper;

    @Autowired
    ValueAddedServiceFeign valueAddedServiceFeign;

    @Autowired
    OrderInfoItemFeign orderInfoItemFeign;

    @Autowired
    private DictHelper dictHelper;

    /**
     * !@收货单位解析 - 2.中台（IN2）
     * @param customerOrderInfo
     * @return
     */
    @Override
    @ZeebeFlow("REVEIVE_UNIT")
    public CustomerOrderInfoExt explainReceivePlatformAndCustomer(CustomerOrderInfoExt customerOrderInfo) {
        //校验
        if (StringUtils.isEmpty(customerOrderInfo.getOrderNo())) {
            throw BusinessException.fail("订单号不能为空");
        }
        String orderType = customerOrderInfo.getOrderType();
        String upperOrderType = customerOrderInfo.getUpperOrderType();
        // 上游收货客户【收货人】
        String upperReceiverCode = customerOrderInfo.getUpperReceiverCode();
        // 根据实体(entity_id【冰箱、电饭煲】)
        Integer entityId = customerOrderInfo.getEntityId();
        // 上游源客户编码【发货人】(upper_source_customer_code)
        String upperSourceCustomerCode = customerOrderInfo.getUpperSourceCustomerCode();
        String sourceSystem = customerOrderInfo.getSourceSystem();
        String joinType = customerOrderInfo.getJoinType();
        String upperTargeWhCode = customerOrderInfo.getUpperTargeWhCode();
        // glh:来源系统：OFC、SCC +  upperTargetLogisticsCenterCode 不为空: 按目标物流中心编码去查DC的服务平台对照；按上游客户编码（upperReceiverCode）查DC客户对照
        String searchTargetWhParam = upperTargeWhCode;
        if (SourceSystem.isOFCSCC(customerOrderInfo.getSourceSystem()) && StringUtils.isNotBlank(customerOrderInfo.getUpperTargetLogisticsCenterCode())) {
            searchTargetWhParam = customerOrderInfo.getUpperTargetLogisticsCenterCode();
        }
        //菜鸟调拨出库预配载标示1:是预配载，0：不是预配载
        Integer CnDispatchFlag =  customerOrderInfo.getCnDispatch()==null?0:customerOrderInfo.getCnDispatch();

        //!@收货单位解析 - 2.1解析商超标识
        //来源系为CCS，订单类型为销售出库时，需调用此接口判断收货方是否商超
        ScPosFlagSourceEnum scPosFlagSource = null;
        if ( (SourceSystem.OFC.getKey().equals(sourceSystem) || SourceSystem.CCS.getKey().equals(sourceSystem)) && OrderType.PO.getKey().equals(orderType)) {
            //            1)	CCS的B2B销售出库单，若upperTargeWhCode不为空，则：
//            a)	解析收货平台，根据upperTargeWhCode找财务仓对照表，若找到，则设置targetSiteCode；
            //1122 索超，移除此处的，目标平台跟客户对照  //设置商超标识
            String scPosFlag = null;
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(upperReceiverCode)) {
                if(redisHelper.isexsitKey(RedisHelper.EDI_KEY + upperReceiverCode)) {
                     scPosFlag = redisHelper.getScPosFlag(upperReceiverCode);
                    customerOrderInfo.setScPosFlag(Integer.parseInt(scPosFlag));
                } else {
                    // JsonResponse isHypermarketJsonResponse = orderBopService.upstreamIsHypermarket(upperReceiverCode);
                    JsonResponse isHypermarketJsonResponse = scPosFlagHelper.getScPosFlag(upperReceiverCode, customerOrderInfo.getOrderNo());
                    if (isHypermarketJsonResponse != null && BaseCodeEnum.SUCCESS.getCode().equals(isHypermarketJsonResponse.getCode())) {
                        if (null != isHypermarketJsonResponse.data) {
                            String responseData = (String) isHypermarketJsonResponse.getData();
                            JSONObject parse = (JSONObject) JSON.parse(responseData);
                             scPosFlag = parse.get("data").toString();
                            customerOrderInfo.setScPosFlag(Integer.parseInt(scPosFlag));
                            redisHelper.setScPosFlag(upperReceiverCode, scPosFlag);
                        }
                    }
                }
                if(ToolUtils.isNotEmpty(scPosFlag)){
                    customerOrderInfo.setScPosFlag(Integer.parseInt(scPosFlag));
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "更新商超标识");
                    scPosFlagSource = ScPosFlagSourceEnum.SYSTEM;
                }
            }
        }
        if (BusinessMode.isB2B(customerOrderInfo.getBusinessMode()) && !CommonConstant.FLAG_YES.equals(customerOrderInfo.getScPosFlag())){
            //履约中心订单接入时，若业务模式为“B2B”，则需按照一下规则对商品标识进行打标：若上游下发了商超标识，
            //即“sc_pos_flag”的值为“1”，则不调用配置中心商超标识解析，按照上游下发的标识写入；
            //若上游未下发商超标识，即“sc_pos_flag”的值为空，则调用配置中心的商超标识解析，若找到对
            //应商超标识配置项，则将sc_pos_flag”赋值为“1”，否则为空；
            ScPosConfig scPosConfig = new ScPosConfig();
            scPosConfig.setSourceSystem(customerOrderInfo.getSourceSystem());
            scPosConfig.setCustomerCode(customerOrderInfo.getCustomerCode());
            scPosConfig.setSiteCode(customerOrderInfo.getSiteCode());
            scPosConfig.setOrderType(customerOrderInfo.getOrderType());
            // 创维的退货入库单 要取 发货地址匹配 商标的详情地址
            if (SourceSystem.SKYWORTH.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.isRIOrder(customerOrderInfo.getOrderType())) {
                scPosConfig.setDetailAddr(customerOrderInfo.getCustomerOrderAddress().getSenderDetailAddr());
            } else if (OrderType.isRIOrder(orderType)) {
                scPosConfig.setDetailAddr(customerOrderInfo.getCustomerOrderAddress().getSenderDetailAddr());
            } else {
                scPosConfig.setDetailAddr(customerOrderInfo.getCustomerOrderAddress().getReceiverDetailAddr());
            }
            scPosConfig.setEnableFlag(1);
            if (StringUtils.isNotBlank(scPosConfig.getDetailAddr())) {
                JsonResponse jsonResponse1 = scPosConfigFeign.searchOne(scPosConfig);
                log.info("scPosConfigFeign.searchOne=====>{}", JSON.toJSONString(jsonResponse1));
                if (null != jsonResponse1.data()) {
                    customerOrderInfo.setScPosFlag(1);
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "更新商超标识");
                    scPosFlagSource = ScPosFlagSourceEnum.SC_POS_CONFIG;
                }
            }
        }

        // 商超标识解析时，若商超标识打标依据是来源于商超标识配置数据，则需打标为：人工（1），否则为：系统（2）
        updateScPosFlagSourceIfNecessary(customerOrderInfo.getOrderNo(), scPosFlagSource);

        boolean ofcPoFlag = SourceSystem.OFC.getKey().equals(sourceSystem) && OrderType.PO.getKey().equals(orderType);
        boolean ofcAoFlag = SourceSystem.OFC.getKey().equals(sourceSystem) && OrderType.AO.getKey().equals(orderType);
        boolean ofcParseFlag = SourceSystem.OFC.getKey().equals(sourceSystem) && (OrderType.CZF.getKey().equals(orderType) || OrderType.ZF.getKey().equals(orderType));
        boolean ofcRoFlag = SourceSystem.OFC.getKey().equals(sourceSystem) && OrderType.RO.getKey().equals(orderType);

        //零售货权转移
        boolean mrpDbFlag = SourceSystem.isMRP(sourceSystem) && CommonConstant.DB.equals(customerOrderInfo.getUpperOrderType());
    
    
        //!@收货单位解析 - 2.2 OU/货主转移解析目标客户和目标平台
        //借还货订单（无忧零售）
        if(BusinessHelper.isWFROrder(customerOrderInfo)) {
            return wfrOrderAnalysis(customerOrderInfo);
        }

        //美云销OU订单
        if(BusinessHelper.isSHTOrder(customerOrderInfo)) {
            return shtOrderAnalysis(customerOrderInfo);
        }
        
        //MSSOU订单
        if(BusinessHelper.isMssSHTOrder(customerOrderInfo)) {
            return mssShtOrderAnalysis(customerOrderInfo);
        }

        //云仓OU订单
        if(BusinessHelper.isCloudSHTOrder(customerOrderInfo)) {
            return cwShtOrderAnalysis(customerOrderInfo);
        }

        //gsc:云仓订单美云销给物流的是小B的收货地址，要根据寻仓接口来获取对应的收货仓库，获取对应的仓库的收货地址和收货联系人作为订单实际的收货地址以及对应收货联系人。同时收货平台、进行更新，订单的收货客户为云仓客户
        if (businessHelper.isSearchWhFlag(customerOrderInfo)) {
            CustomerOrderAddress customerOrderAddress = customerOrderInfo.getCustomerOrderAddress();
            MidCustomerControl midCustControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, SourceSystem.OFC.getKey());
            if (null != midCustControlCache) {
                customerOrderInfo.setTargetCustomerCode(midCustControlCache.getAnnCustomerCode());
            }
            boolean isByStreetCode = true;
            CustomerOrderAddress newCustomerOrderAddress = new CustomerOrderAddress();
            newCustomerOrderAddress.setId(customerOrderAddress.getId());
            //ljl:cloud_WhFlag = Y + (source_system=MRP & upperOrderType=FO/YO || (source_system=OFC & orderType=PO)) 的订单，收货平台解析通过来源系统（source_system=MRP）+上游仓库编码upper_wh_code+业务模式（B2B）查【DC-对照管理-服务平台对照】获取服务平台
            if (CommonEnum.Y.getKey().equals(customerOrderInfo.getCloudWhFlag())
                && (SourceSystem.isMRP(customerOrderInfo.getSourceSystem()) && (CommonConstant.FO.equals(customerOrderInfo.getUpperOrderType()) || CommonConstant.YO.equals(customerOrderInfo.getUpperOrderType()) || CommonConstant.KO.equals(customerOrderInfo.getUpperOrderType()) || UpperOrderType.isMrpHo(customerOrderInfo.getUpperOrderType()))
                    || (SourceSystem.isOFC(customerOrderInfo.getSourceSystem()) && (OrderType.isPOOrder(customerOrderInfo.getOrderType()) || OrderType.isCZFOrder(customerOrderInfo.getOrderType())  || OrderType.isZFOrder(customerOrderInfo.getOrderType()))))
            ) {
                //!@收货单位解析 - 2.3 云仓根据对照查询收货平台
                DcWarehouseMapping dcWarehouseMapping = new DcWarehouseMapping();
                dcWarehouseMapping.setSourceSystem(SourceSystem.MRP.getKey());
                dcWarehouseMapping.setUpperWhCode(upperTargeWhCode);
                dcWarehouseMapping.setBusinessMode(BusinessMode.B2B.getName());
                dcWarehouseMapping.setEnableFlag(1);
                dcWarehouseMapping = dcMappingHelper.getDcWarehouseMapping(dcWarehouseMapping);
                if (dcWarehouseMapping != null) {
                    CdWarehouseDto cdWarehouseDto = dcMappingHelper.searchWhBySiteCode(dcWarehouseMapping.getSiteCode());
                    if (cdWarehouseDto == null) {
                        throw BusinessException.fail("根据平台编码查询仓库信息为空");
                    }
                    customerOrderInfo.setTargetSiteCode(dcWarehouseMapping.getSiteCode());
                    this.setCloudWhAddreeInfo(newCustomerOrderAddress, cdWarehouseDto);
                    isByStreetCode = false;
                }
            }
    
            //!@收货单位解析 - 2.4 云仓根据四级编码查询收货平台
            if (isByStreetCode) {
                SearchWhRequest searchWhRequest = new SearchWhRequest();
                searchWhRequest.setStreetCode(customerOrderAddress.getReceiverTownCode());
                JsonResponse<SearchWhResponse> jsonResponse = searchWhFeign.searchNetPlanWhByStreetCode(searchWhRequest);
                SearchWhResponse whResponse = jsonResponse.getData();
                if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode()) || null == whResponse) {
                    throw BusinessException.fail(jsonResponse.getMsg());
                }
                customerOrderInfo.setTargetSiteCode(whResponse.getSiteCode());
                this.setCloudWhAddreeInfo(newCustomerOrderAddress, whResponse);
            }

            this.setCloudWhAddreeInfo(customerOrderAddress,newCustomerOrderAddress);
            lmpOrderFlowHelper.updateCustomerOrderAddress(newCustomerOrderAddress);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
            FlowListenerParam.success("解析云仓收货单位成功,收货平台:" + customerOrderInfo.getTargetSiteCode() + "收货客户:" + customerOrderInfo.getTargetCustomerCode());
            return customerOrderInfo;
        }
    
        //!@收货单位解析 - 2.5 CIMS/OFC/MRP/ 根据upperTargeWhCode查询目标平台和目标客户
        if (ToolUtils.isNotEmpty(upperTargeWhCode)
            && (SourceSystem.CIMS.getKey().equals(sourceSystem) && OrderType.PO.getKey().equals(orderType) || ofcPoFlag || ofcAoFlag || mrpDbFlag || ofcParseFlag )
            && BusinessMode.isB2B(customerOrderInfo.getBusinessMode())){
            //零售走OFC的逻辑
            if (mrpDbFlag) {
                sourceSystem = SourceSystem.OFC.getKey();
            }
            MidSiteWhControl midSiteWhControl = midSiteWhControlManager.getMidSiteWhControlCache(sourceSystem, searchTargetWhParam);
            String targetSiteCode = null;
            if (midSiteWhControl != null && org.apache.commons.lang3.StringUtils.isNotEmpty(midSiteWhControl.getSiteCode())) {
                if (!CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midSiteWhControl.getStatus()))) {
                    targetSiteCode = midSiteWhControl.getSiteCode();
                    customerOrderInfo.setTargetSiteCode(targetSiteCode);
                    customerOrderInfo.setTargetWhCode(midSiteWhControl.getWhCode());
                }
            }else{
                log.warn("upperTargeWhCode:{} is not mapping in midSiteWhControl mapping config.......", searchTargetWhParam);
            }
            
            if (ToolUtils.isEmpty(targetSiteCode)){
                midSiteWhControl = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.CCS.getKey(), searchTargetWhParam);

                if (midSiteWhControl != null && org.apache.commons.lang3.StringUtils.isNotEmpty(midSiteWhControl.getSiteCode())) {
                    if (!CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midSiteWhControl.getStatus()))) {
                        targetSiteCode = midSiteWhControl.getSiteCode();
                        customerOrderInfo.setTargetSiteCode(targetSiteCode);
                    }
                }else{
                    log.warn("upperTargeWhCode:{} is not mapping in midSiteWhControl mapping config.......", searchTargetWhParam);
                }
            }

            String annCustomerCode = null;
            //OFC逻辑优化
            if (SourceSystem.OFC.getKey().equals(sourceSystem)) {
                MidCustomerControl midCustomerControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode,SourceSystem.OFC.getKey());
                if (null != midCustomerControlCache) {
                    annCustomerCode = midCustomerControlCache.getAnnCustomerCode();
                    customerOrderInfo.setTargetCustomerCode(annCustomerCode);
                }
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
                FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode + "收货平台:" + targetSiteCode);
                return customerOrderInfo;
            }
            JsonResponse<List<MidCustomerControl>> stringJsonResponse = omsCdCommonMaterialFegin.custCustomerCode(upperTargeWhCode);
            if (BaseCodeEnum.SUCCESS.getCode().equals(stringJsonResponse.getCode())) {
                if (ToolUtils.isNotEmpty(stringJsonResponse.data())&& null !=stringJsonResponse.data().get(0)){
                    annCustomerCode = stringJsonResponse.data().get(0).getAnnCustomerCode();
                    customerOrderInfo.setTargetCustomerCode(annCustomerCode);
                }
            }
            log.info("/oms/midCustomerControl/custCustomerCode==>{}===>{}", upperTargeWhCode,stringJsonResponse);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
            FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode + "收货平台:" + targetSiteCode);
            return customerOrderInfo;
        }





        // 来源系统CIMS，设置targetSiteCode和targetCustomerCode
        if ((SourceSystem.CIMS.getKey().equals(sourceSystem) && OrderType.PO.getKey().equals(orderType) && OrderType.PO.getKey().equals(upperOrderType))
            || (SourceSystem.CIMS.getKey().equals(sourceSystem) && JoinType.SHARE.getKey().equals(joinType) && OrderType.RI.getKey().equals(orderType))
            || (SourceSystem.CIMS.getKey().equals(sourceSystem) && OrderType.AO.getKey().equals(orderType) && OrderType.AO.getKey().equals(upperOrderType))
            || (ofcPoFlag && OrderType.PO.getKey().equals(upperOrderType))
            || (ofcAoFlag && OrderType.AO.getKey().equals(upperOrderType))
            || ofcParseFlag
            || (ofcRoFlag && OrderType.RO.getKey().equals(upperOrderType))
        ) {
            String siteCode = "";
            String whCode = "";
            //1.如果是joinType = SHARE, 那么 targetSiteCode = siteCode
            if (JoinType.SHARE.getKey().equals(joinType)) {
                siteCode = customerOrderInfo.getSiteCode();
                whCode = customerOrderInfo.getWhCode();
                customerOrderInfo.setTargetSiteCode(siteCode);
                customerOrderInfo.setTargetWhCode(whCode);
                // 其他情况，需要去查询出来
            } else {

                //CIMS的PO单，收发货单位审核时，根据上游收货客户名称、收货地址查询老鼠仓配置，
                // 若有配置，则表示此单是发往老鼠仓，按照事业部要求，不允许发往老鼠仓，因此订单审核失败，不继续审核
                if(BusinessMode.isB2B(customerOrderInfo.getBusinessMode())) {
                    CustomerOrderAddress customerOrderAddress =customerOrderInfo.getCustomerOrderAddress(); //lmpOrderFlowHelper.getCustomerOrderAddress(customerOrderInfo.getOrderNo());
                    if (null != customerOrderAddress) {
                        RatWarehouse ratWarehouse = new RatWarehouse();

                        ratWarehouse.setUpperReceverName(customerOrderInfo.getUpperReceiverName());
                        ratWarehouse.setEnabledFalg(1);
                        ratWarehouse.setReceiverAddress(customerOrderAddress.getReceiverDetailAddr());
                        if (ToolUtils.isNotEmpty(customerOrderInfo.getUpperReceiverName())
                            && ToolUtils.isNotEmpty(customerOrderAddress.getReceiverDetailAddr())){
                            JsonResponse<PageResponse<RatWarehouse>> search = ratWarehouseFeign.search(ratWarehouse);
                            if (null != customerOrderAddress && null != search.data()
                                && ToolUtils.isNotEmpty(search.data().getList())) {
                                //获取配置业务人员
                                BusinessControlParam param = businessParamHelper.getBusinessParamByIndex(CommonConstant.BUSINESS_PERSONNE);
                                throw BusinessException.fail(param!=null?param.getParamName():"收货单位解析，此单的收货地址为受限制的老鼠仓地址，不允许往该地址进行发货");

                            }
                        }
                    }
                }

                if (SourceSystem.OFC.getKey().equals(sourceSystem)) {
                    if(StringUtils.isNotBlank(customerOrderInfo.getUpperTargeWhCode())) {
                        MidSiteWhControl midSiteWhControlCache = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.OFC.getKey(), searchTargetWhParam);
                        if (null != midSiteWhControlCache) {
                            customerOrderInfo.setTargetSiteCode(midSiteWhControlCache.getSiteCode());
                            customerOrderInfo.setTargetWhCode(midSiteWhControlCache.getWhCode());
                        }
                    }
                } else {
                    ConsigneeSiteRequest request = new ConsigneeSiteRequest();
                    //注意：这里传客户订单号
                    request.setOrderNo(customerOrderInfo.getCustomerOrderNo());
                    //根据订单号调用鹊桥查询收货平台编码
                    ConsigneeSiteResponse jsonResponse = centerInvService.getConsigneeSiteByOrderNo(request);
                    String code = jsonResponse.getIsSuccess();
                    // 非T时不处理
                    if ("T".equals(code)) {
                        String isAnntoWh = jsonResponse.getIsAnntoWarehouse();
                        siteCode = jsonResponse.getSiteCode();
                        whCode = jsonResponse.getWarehCode();
                        //是否安得仓库
                        if ("Y".equals(isAnntoWh)) {
                            if (org.apache.commons.lang3.StringUtils.isNotEmpty(siteCode)) {
                                customerOrderInfo.setTargetSiteCode(siteCode);
                            }
                            if (org.apache.commons.lang3.StringUtils.isNotEmpty(whCode)){
                                customerOrderInfo.setTargetWhCode(whCode);
                            }
                        }
                    }
                }
            }

            // 如果是调剂单，则用entityId 和 upperReceiverCode 查询 targetCustomerCode
            if (JoinType.SHARE.getKey().equals(joinType)) {

                String upperCode = upperReceiverCode;

                //2020年4月17日14:57:49 索超：默认取：upperReceiverCode   RI取： upperSourceCustomerCode
                if (OrderType.RI.getKey().equals(orderType)){
                    upperCode = upperSourceCustomerCode;
                }

                //2020-4-15 14:21:28 索超： 调剂订单取 upperReceiverCode
                String targetCustormerCode = getTargetCustomerCodeByEntityId(entityId, upperCode);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(targetCustormerCode)) {
                    customerOrderInfo.setTargetCustomerCode(targetCustormerCode);
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收货客户");
                    FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                    return customerOrderInfo;
                }else{
                    throw BusinessException.fail("解析不成功,未找到收货客户!");
                }
            }

            if (SourceSystem.OFC.getKey().equals(sourceSystem)) {
                MidCustomerControl midCustControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, SourceSystem.OFC.getKey());
                if (null != midCustControlCache) {
                    customerOrderInfo.setTargetCustomerCode(midCustControlCache.getAnnCustomerCode());
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收货客户");
                    FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                    return customerOrderInfo;
                }
                FlowListenerParam.success("不需要解析收货单位");
                return customerOrderInfo;
            }


            //步骤二、根据upperReceiverCode（原集团编码）查询 eb_custorm 的original_group_code 查询是否代理商或者销司 is_agent 是否代理商(否:0；是:1)
            CustomerAgentOrSaleCompany customerAgentOrSaleCompany = null;
            JsonResponse<CustomerAgentOrSaleCompany> receiveJson = omsCdCommonMaterialFegin.isAgentOrSaleCompany(upperReceiverCode);
            String code = receiveJson.getCode();
            if (BaseCodeEnum.SUCCESS.getCode().equals(code)) {
                customerAgentOrSaleCompany = receiveJson.getData();
            }

            if (null != customerAgentOrSaleCompany) {

                Integer isSaleCompany = customerAgentOrSaleCompany.getIsSaleCompany();
                Integer isAgent = customerAgentOrSaleCompany.getIsAgent();
                if (null == isSaleCompany || isAgent == null){
                    throw BusinessException.fail("解析失败:请维护【公共基础数据】—【客户相关】—【客户】中收货客户的代理商和销司属性,上游收货单位编码["+upperReceiverCode+"]");
                }

                //步骤二、根据upperReiverCode，查询客户表；
                String receiverPmCode = customerAgentOrSaleCompany.getPmCode();

                //步骤三、判断客户是销司OR代理商，是销司（is_sale_company=1）,则targetCustomerCode为步骤二中的客户编码；end；不是销司继续；
                if (1 == isSaleCompany) {
                    customerOrderInfo.setTargetCustomerCode(receiverPmCode);
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析销司");
                    FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                    return customerOrderInfo;
                }

                // 步骤四、客户为代理商（is_agent=1）,则根据实体(entity_id)、上游收货单位编码(upper_receiver_code)获取客户，查询收货客户的上游客户编码(upperCustomerCode)：
                String targetCustCodeByReceiverCode = getTargetCustomerCodeByEntityId(entityId, upperReceiverCode);

                if (1 == isAgent) {
                    customerOrderInfo.setTargetCustomerCode(targetCustCodeByReceiverCode);
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收货客户");
                    FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                    return customerOrderInfo;
                }

                //步骤五、如果targetSiteCode为空，则targetCustomerCode为步骤四的客户；end。
                if (org.apache.commons.lang3.StringUtils.isEmpty(siteCode)) {
                    customerOrderInfo.setTargetCustomerCode(targetCustCodeByReceiverCode);
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析目标客户");
                    FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                    return customerOrderInfo;
                }

                //步骤六、判断upper_receiver_code和upper_source_system_code是否相等：若相等，则targetCustomerCode为步骤四种获取的客户，end；不相等，则继续；
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(upperReceiverCode) && org.apache.commons.lang3.StringUtils.isNotEmpty(upperSourceCustomerCode) && upperReceiverCode.equals(upperSourceCustomerCode)) {
                    customerOrderInfo.setTargetCustomerCode(targetCustCodeByReceiverCode);
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收货客户");
                    FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                    return customerOrderInfo;
                }

                // 步骤七、根据upperSourceCustomerCode，查询客户表，获取客户
                JsonResponse<CustomerAgentOrSaleCompany> soureceCustomerJson = omsCdCommonMaterialFegin.isAgentOrSaleCompany(upperSourceCustomerCode);
                code = soureceCustomerJson.getCode();
                if (BaseCodeEnum.SUCCESS.getCode().equals(code)) {
                    customerAgentOrSaleCompany = soureceCustomerJson.getData();
                }

                if (null != customerAgentOrSaleCompany) {
                    String sourceCutomerpmCode = customerAgentOrSaleCompany.getPmCode();
                    //步骤八、targetSiteCode不为空时（入安得仓），根据targetSiteCode查询该平台下的协同仓，根据步骤四客户、步骤七客户、协同仓列表，调用WMS，获取同仓列表，若有，则认为协同，协同时，targetCustomerCode设置为步骤六的客户；
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(siteCode)) {
                        //同仓校验，判断协调仓关系
                        List<CdWarehouse> cdWarehouseList = cargoRightTransferService.getCollabWhBySiteCode(siteCode);
                        List<String> warehouseCodeList = cdWarehouseList.stream().map(CdWarehouse::getWhCode).collect(Collectors.toList());
//                        List<String> whCodes = separateWarehouseService.validateSameWarehouseCode(targetCustCodeByReceiverCode, sourceCutomerpmCode, warehouseCodeList);
                        ValidateSameWhDto validateSameWhDto = ValidateSameWhDto.builder()
                            .customerCode(targetCustCodeByReceiverCode)
                            .targetCustomerCode(sourceCutomerpmCode)
                            .whCodes(warehouseCodeList)
                            .orderNo(customerOrderInfo.getOrderNo())
                            .siteCode(siteCode)
                            .build();
                        List<String> whCodes = separateWarehouseService.validateSameWarehouseCodeSplit(validateSameWhDto);
                        //无协调关系  targetCustormerCode取upperReceiverCode客户
                        if (CollectionUtils.isEmpty(whCodes)) {
                            customerOrderInfo.setTargetCustomerCode(targetCustCodeByReceiverCode);
                            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析协调仓");
                            FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                            return customerOrderInfo;
                            //协同关系,取sourceCutomerpmCode
                        } else {
                            customerOrderInfo.setTargetCustomerCode(sourceCutomerpmCode);
                            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析协调仓");
                            FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                            return customerOrderInfo;

                        }
                    }
                }
            }
        }

        if (SourceSystem.MRP.getKey().equals(sourceSystem) &&
            (CommonConstant.EO.equals(customerOrderInfo.getUpperOrderType()) || CommonConstant.SO.equals(customerOrderInfo.getUpperOrderType())
                || CommonConstant.RO.equals(customerOrderInfo.getUpperOrderType()))
        ) {
            if(StringUtils.isNotBlank(upperTargeWhCode)) {
                boolean updateFlag = false;
                MidCustomerControl midCustControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, SourceSystem.OFC.getKey());
                if (null != midCustControlCache) {
                    updateFlag = true;
                    customerOrderInfo.setTargetCustomerCode(midCustControlCache.getAnnCustomerCode());
                }
                MidSiteWhControl midSiteWhControlCache = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.OFC.getKey(), upperTargeWhCode);
                if (null != midSiteWhControlCache) {
                    updateFlag = true;
                    customerOrderInfo.setTargetSiteCode(midSiteWhControlCache.getSiteCode());
                }
                if (updateFlag) {
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收货客户");
                }
                FlowListenerParam.success(customerOrderInfo.getTargetCustomerCode());
                return customerOrderInfo;
            }
        }

        //!@菜鸟嗲波4.0 -菜鸟的调拨出库单待预配载标志的，在收发货单位审核节点，增加获取关联预配单单里面的senderCode和receiverCOde, 获取后塞到父单的 from lijuan
        if(SourceSystem.CAINIAO.getKey().equals(sourceSystem) && OrderType.AO.getKey().equals(orderType) && 1 == CnDispatchFlag){
            CnDispatchPlan search = new CnDispatchPlan();
            //2020-4-9 10:17:55 培：请相信我，一定要加上下面这一行，不然到了原子层，你会很开心的看到orderNo也传过去了，无解~找了很久也是无解！！！
            search.setOrderNo("");
            search.setRelationOrderNo(customerOrderInfo.getCustomerOrderNo());
            JsonResponse<CnDispatchPlan> jsonResponse = cnDispatchPlanFeign.selectOne(search);
            if(jsonResponse == null || !BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())){
                throw BusinessException.fail("查询预配载订单失败:"+JSON.toJSONString(jsonResponse));
            }
            if(null == jsonResponse.getData()){
                throw BusinessException.fail("此订单对应的预配载订单不存在,客户订单号:"+customerOrderInfo.getCustomerOrderNo());
            }
            CnDispatchPlan cnDispatchPlan = jsonResponse.getData();
            String receiverCode = cnDispatchPlan.getReceiverCode();
            customerOrderInfo.setUpperReceiverCode(receiverCode);
            customerOrderInfo.setUpperSenderCode(cnDispatchPlan.getSenderCode());

            // 2020-4-8 14:51:59 泓铄： 解析收货单位
            setCNReceiveUnit(customerOrderInfo.getOrderNo(), receiverCode,null,customerOrderInfo);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析关联预配单收发货单位");
            FlowListenerParam.success("获取关联预配单收发货单位成功,收货单位:" + cnDispatchPlan.getReceiverCode() + "发货单位:" + cnDispatchPlan.getSenderCode());
            return customerOrderInfo;
            //return FlowListenerParam.success("获取关联预配单收发货单位成功,收货单位:" + cnDispatchPlan.getReceiverCode() + "发货单位:" + cnDispatchPlan.getSenderCode());
        }
        //菜鸟纯运输解析收发货单位
        if(SourceSystem.CAINIAO.getKey().equals(sourceSystem) && OrderType.YS.getKey().equals(orderType) &&  CnDispatchEnum.isALLOT(customerOrderInfo.getCnDispatch())){
            if(org.apache.commons.lang3.StringUtils.isEmpty(customerOrderInfo.getUpperReceiverCode()) || org.apache.commons.lang3.StringUtils.isEmpty(customerOrderInfo.getUpperSenderCode())){
                throw BusinessException.fail("上游收货单位编码或者上游发货单位编码为空");
            }

            setCNReceiveUnit(customerOrderInfo.getOrderNo(), customerOrderInfo.getUpperReceiverCode(),customerOrderInfo.getUpperSenderCode(),customerOrderInfo);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "更新收发货单位信息");
            FlowListenerParam.success("收货单位解析成功:收货单位:"+customerOrderInfo.getUpperReceiverCode() + " 发货单位:"+customerOrderInfo.getUpperSenderCode());

            return customerOrderInfo;
        }

        //生产物流 纯运输 解析收货单位
        if (SourceSystem.GSC.getKey().equals(sourceSystem) && OrderType.YS.getKey().equals(orderType)) {
            if (StringUtils.isEmpty(customerOrderInfo.getUpperReceiverCode())) {
                throw BusinessException.fail("上游收货单位编码为空");
            }
            setGscReceiverUnit(customerOrderInfo);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "更新收货单位信息");
            FlowListenerParam.success("收货单位解析成功:收货单位:" + customerOrderInfo.getUpperReceiverCode());
            return customerOrderInfo;
        }
    
    
        //!@收货单位解析 - 2.10 MSS/MSS-STC 根据upperTargeWhCode查询目标平台和目标客户
        boolean mssPoRoCzf = SourceSystem.isMSS(sourceSystem)
         && (OrderType.isPOAOOrder(orderType) || OrderType.isCZFOrder(orderType) || OrderType.RO.getKey().equals(orderType))
         && ToolUtils.isNotEmpty(upperTargeWhCode) && BusinessMode.isB2B(customerOrderInfo.getBusinessMode());
    
        //2025年1月7日16:50:55 丽红 新增MSS-STC https://cf.annto.com/pages/viewpage.action?pageId=70101029 (丽红说暂时不需要，所以下面先临时注释掉)
        boolean mssStcPo = SourceSystem.isMssStc(sourceSystem) && OrderType.isPOOrder(orderType) && ToolUtils.isNotEmpty(upperTargeWhCode)
        && BusinessMode.isB2B(customerOrderInfo.getBusinessMode());
        
        if (mssPoRoCzf /*|| mssStcPo*/) {
            MidSiteWhControl midSiteWhControl = midSiteWhControlManager.getMidSiteWhControlCache(sourceSystem, upperTargeWhCode);
            String targetSiteCode = null;
            if (midSiteWhControl != null && org.apache.commons.lang3.StringUtils.isNotEmpty(midSiteWhControl.getSiteCode())) {
                if (!CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midSiteWhControl.getStatus()))) {
                    targetSiteCode = midSiteWhControl.getSiteCode();
                    customerOrderInfo.setTargetSiteCode(targetSiteCode);
                    customerOrderInfo.setTargetWhCode(midSiteWhControl.getWhCode());
                }
            }
            String annCustomerCode = null;
            MidCustomerControl midCustomerControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, sourceSystem);
            if (null != midCustomerControlCache) {
                annCustomerCode = midCustomerControlCache.getAnnCustomerCode();
                customerOrderInfo.setTargetCustomerCode(annCustomerCode);
            }
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
            FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode + "收货平台:" + targetSiteCode);
            return customerOrderInfo;
        }

        FlowListenerParam.success("不需要解析收货单位");
        return customerOrderInfo;

    }
    
    /**
     * !@美云销OU -2、收货单位解析
     * @param customerOrderInfo
     * @return
     */
    private CustomerOrderInfoExt shtOrderAnalysis(CustomerOrderInfoExt customerOrderInfo) {
        String upperTargeWhCode = customerOrderInfo.getUpperTargeWhCode();
        MidCustomerControl midCustomerControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, SourceSystem.OFC.getKey());
        String annCustomerCode;
        String targetSiteCode;
        if (null != midCustomerControlCache && ToolUtils.isNotEmpty(midCustomerControlCache.getAnnCustomerCode())
            && !CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midCustomerControlCache.getStatus()))) {
            annCustomerCode = midCustomerControlCache.getAnnCustomerCode();
            customerOrderInfo.setTargetCustomerCode(annCustomerCode);
        } else {
            log.warn("upperTargeWhCode:{} is not mapping in MidCustControl mapping config.......", upperTargeWhCode);
            throw BusinessException.fail("美云销OU订单，解析目标客户失败，" + upperTargeWhCode);
        }

        MidSiteWhControl midSiteWhControl = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.OFC.getKey(), upperTargeWhCode);
        if (midSiteWhControl != null && ToolUtils.isNotEmpty(midSiteWhControl.getSiteCode())
            && !CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midSiteWhControl.getStatus()))) {
            targetSiteCode = midSiteWhControl.getSiteCode();
            customerOrderInfo.setTargetSiteCode(targetSiteCode);
            customerOrderInfo.setTargetWhCode(midSiteWhControl.getWhCode());
        } else {
            throw BusinessException.fail("美云销OU订单，解析目标平台仓库失败，" + upperTargeWhCode);
        }
        lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
        FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode + "收货平台:" + targetSiteCode);
        return customerOrderInfo;
    }
    /**
     * !@MSSOU -2、收货单位解析
     * @param customerOrderInfo
     * @return
     */
    private CustomerOrderInfoExt mssShtOrderAnalysis(CustomerOrderInfoExt customerOrderInfo) {
        String upperTargeWhCode = customerOrderInfo.getUpperTargeWhCode();
        MidCustomerControl midCustomerControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, SourceSystem.MSS.getKey());
        String annCustomerCode;
        String targetSiteCode;
        if (null != midCustomerControlCache && ToolUtils.isNotEmpty(midCustomerControlCache.getAnnCustomerCode())
            && !CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midCustomerControlCache.getStatus()))) {
            annCustomerCode = midCustomerControlCache.getAnnCustomerCode();
            customerOrderInfo.setTargetCustomerCode(annCustomerCode);
        } else {
            log.warn("upperTargeWhCode:{} is not mapping in MidCustControl mapping config.......", upperTargeWhCode);
            throw BusinessException.fail("楼宇货主转移订单，解析目标客户失败，" + upperTargeWhCode);
        }

        MidSiteWhControl midSiteWhControl = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.MSS.getKey(), upperTargeWhCode);
        if (midSiteWhControl != null && ToolUtils.isNotEmpty(midSiteWhControl.getSiteCode())
            && !CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midSiteWhControl.getStatus()))) {
            targetSiteCode = midSiteWhControl.getSiteCode();
            customerOrderInfo.setTargetSiteCode(targetSiteCode);
            customerOrderInfo.setTargetWhCode(midSiteWhControl.getWhCode());
        } else {
            throw BusinessException.fail("楼宇货主转移订单，解析目标平台仓库失败，" + upperTargeWhCode);
        }
        lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
        FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode + "收货平台:" + targetSiteCode);
        return customerOrderInfo;
    }

    /**
     * !@云仓OU -2、收货单位解析
     * @param customerOrderInfo
     * @return
     */
    public CustomerOrderInfoExt cwShtOrderAnalysis(CustomerOrderInfoExt customerOrderInfo) {
        String upperTargeWhCode = customerOrderInfo.getUpperTargeWhCode();
        String siteCode = customerOrderInfo.getSiteCode();
        MidCustomerControl midCustomerControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, SourceSystem.OFC.getKey());
        String annCustomerCode;
        if (null != midCustomerControlCache && ToolUtils.isNotEmpty(midCustomerControlCache.getAnnCustomerCode())
            && !CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midCustomerControlCache.getStatus()))) {
            annCustomerCode = midCustomerControlCache.getAnnCustomerCode();
            customerOrderInfo.setTargetCustomerCode(annCustomerCode);
        } else {
            throw BusinessException.fail("云仓OU订单，解析目标客户失败，" + upperTargeWhCode);
        }
        //目标服务平台跟服务平台一样，取siteCode
        customerOrderInfo.setTargetSiteCode(siteCode);
        lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
        FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode + "收货平台:" + siteCode);
        return customerOrderInfo;
    }

    /**
     * <a href='https://cf.annto.com/x/2byBAg'>更新商超标识来源</>
     * @param orderNo 父单号
     * @param scPosFlagSource 商超标识来源
     */
    private void updateScPosFlagSourceIfNecessary(String orderNo, ScPosFlagSourceEnum scPosFlagSource) {
        if (scPosFlagSource == null) {
            return;
        }
        CompletableFuture.runAsync(() -> {
            Optional.ofNullable(scPosFlagSource).ifPresent(item -> {
                CustomerOrderInfoExtend customerOrderInfoExtend = new CustomerOrderInfoExtend();
                customerOrderInfoExtend.setOrderNo(orderNo);
                JsonResponse<CustomerOrderInfoExtend> jsonResponse = customerOrderInfoExtendFeign.selectOne(customerOrderInfoExtend);
                if (jsonResponse.judgeSuccess() && (customerOrderInfoExtend = jsonResponse.getData()) != null) {
                    CustomerOrderInfoExtendConfDto ceDto = null;
                    if (customerOrderInfoExtend.getConfObj() != null) {
                        ceDto = JSON.parseObject(customerOrderInfoExtend.getConfObj(), CustomerOrderInfoExtendConfDto.class);
                    }
                    if (ceDto == null) {
                        ceDto = new CustomerOrderInfoExtendConfDto();
                    }
                    ceDto.setScPosFlagSource(item.getValue());
                    customerOrderInfoExtend.setConfObj(JSON.toJSONString(ceDto));
                    customerOrderInfoExtendFeign.update(customerOrderInfoExtend.getId(), customerOrderInfoExtend);
                } else {
                    log.error("更新商超标识来源，查询客户订单拓展表失败或未查询客户订单拓展表数据:{}", JSON.toJSONString(jsonResponse));
                }
            });
        });
    }

    /**
     * 借还货订单解析收货仓库、平台、客户
     * @param customerOrderInfo
     * @return
     */
    private CustomerOrderInfoExt wfrOrderAnalysis(CustomerOrderInfoExt customerOrderInfo) {
        String upperTargeWhCode = customerOrderInfo.getUpperTargeWhCode();
        MidCustomerControl midCustomerControlCache = midCustomerControlManager.getMidCustControlCache(upperTargeWhCode, SourceSystem.OFC.getKey());
        String annCustomerCode;
        String targetSiteCode;
        if (null != midCustomerControlCache && ToolUtils.isNotEmpty(midCustomerControlCache.getAnnCustomerCode())
            && !CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midCustomerControlCache.getStatus()))) {
            annCustomerCode = midCustomerControlCache.getAnnCustomerCode();
            customerOrderInfo.setTargetCustomerCode(annCustomerCode);
        } else {
            log.warn("upperTargeWhCode:{} is not mapping in MidCustControl mapping config.......", upperTargeWhCode);
            throw BusinessException.fail("借还货订单，解析目标客户失败，" + upperTargeWhCode);
        }

        //云仓还货单，只需要解析目标客户
        if (BusinessHelper.isCloudReturnOrder(customerOrderInfo)) {
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
            FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode);
            return customerOrderInfo;
        }

        MidSiteWhControl midSiteWhControl = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.OFC.getKey(), upperTargeWhCode);
        if (midSiteWhControl != null && ToolUtils.isNotEmpty(midSiteWhControl.getSiteCode())
            && !CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(midSiteWhControl.getStatus()))) {
            targetSiteCode = midSiteWhControl.getSiteCode();
            customerOrderInfo.setTargetSiteCode(targetSiteCode);
            customerOrderInfo.setTargetWhCode(midSiteWhControl.getWhCode());
        } else {
            log.warn("upperTargeWhCode:{} is not mapping in midSiteWhControl mapping config.......", upperTargeWhCode);
            throw BusinessException.fail("借还货订单，解析目标平台仓库失败，" + upperTargeWhCode);
        }
        lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "解析收发货单位");
        FlowListenerParam.success("获取收货单位成功,收货客户:" + annCustomerCode + "收货平台:" + targetSiteCode);
        return customerOrderInfo;
    }

    /**
     * only explain receiver unit;
     * use DC
     */
    private void setGscReceiverUnit(CustomerOrderInfoExt customerOrderInfo) {
        String orderNo = customerOrderInfo.getOrderNo();
        String upperReceiverCode = customerOrderInfo.getUpperReceiverCode();
        JsonResponse<PageResponse<ContactsOms>> contactsResponse = dcAtomicFeign.contacts(upperReceiverCode,"annto");
        log.info("GscReceiverUnitByDc->contacts orderNo:{},json:{}",customerOrderInfo.getCustomerOrderNo(),JSON.toJSONString(contactsResponse));
        checkContactsResponse(contactsResponse);
        ContactsOms contactsOms = contactsResponse.data().getList().get(0);
        CustomerOrderAddress customerOrderAddress = getCustomerOrderAddress(orderNo);
        if (null != customerOrderAddress) {
            setCustomerOrderAddressValues(customerOrderAddress, contactsOms);
            setCustomerOrderInfoReceiver(customerOrderInfo, contactsOms);
            lmpOrderFlowHelper.updateCustomerOrderAddress(customerOrderAddress);
        }
    }

    private void checkContactsResponse(JsonResponse<PageResponse<ContactsOms>> contactsResponse){
        if (contactsResponse == null || !BaseCodeEnum.SUCCESS.getCode().equals(contactsResponse.getCode())) {
            throw BusinessException.fail("收发货单位解析失败，获取收发货单位信息失败");
        }
        if(contactsResponse.data() == null){
            throw BusinessException.fail("收发货单位解析失败，获取收发货单位信息为空");
        }
        List<ContactsOms> list = contactsResponse.data().getList();
        if(ToolUtils.isEmpty(list)){
            throw BusinessException.fail("收发货单位解析失败，获取收发货单位信息为空");
        }
        ContactsOms contactsOms = list.get(0);
        if(contactsOms == null){
            throw BusinessException.fail("收发货单位解析失败，获取收发货单位信息为空");
        }
    }

    private void setCustomerOrderInfoReceiver(CustomerOrderInfoExt customerOrderInfo, ContactsOms contactsOms) {
        if (null == contactsOms) {
            return;
        }
        //设置收货单位中文名称
        String contactCompanyName = contactsOms.getContactCompanyName();
        customerOrderInfo.setUpperReceiverName(contactCompanyName);
    }

    private void setCustomerOrderAddressValues(CustomerOrderAddress customerOrderAddress, ContactsOms contactsOms) {
        if (null == contactsOms) {
            return;
        }
        customerOrderAddress.setReceiverProvinceCode(contactsOms.getProvinceCode());
        customerOrderAddress.setReceiverProvinceName(contactsOms.getProvinceName());
        customerOrderAddress.setReceiverCityCode(contactsOms.getCityCode());
        customerOrderAddress.setReceiverCityName(contactsOms.getCityName());
        customerOrderAddress.setReceiverDistrictCode(contactsOms.getDistrictCode());
        customerOrderAddress.setReceiverDistrictName(contactsOms.getDistrictName());
        customerOrderAddress.setReceiverTownCode(contactsOms.getTownCode());
        customerOrderAddress.setReceiverTownName(contactsOms.getTownName());
        customerOrderAddress.setReceiverDetailAddr(contactsOms.getDetailAddress());

        String longitude = contactsOms.getLongitude() == null ? "" : contactsOms.getLongitude();
        String latitude = contactsOms.getLatitude() == null ? "" : contactsOms.getLatitude();
        customerOrderAddress.setEndLng(new BigDecimal(longitude));
        customerOrderAddress.setEndLat(new BigDecimal(latitude));

        customerOrderAddress.setReceiverName(contactsOms.getContact());
        customerOrderAddress.setReceiverTel(contactsOms.getContactMobile());
        customerOrderAddress.setReceiverMobile(contactsOms.getContactMobile());
    }

    private CustomerOrderAddress getCustomerOrderAddress(String orderNo) {
        JsonResponse<CustomerOrderAddress> addressJsonResponse = customerOrderAddressFeign.findByOrderNo(orderNo);
        if (null == addressJsonResponse || !BaseCodeEnum.SUCCESS.getCode().equals(addressJsonResponse.getCode())) {
            throw BusinessException.fail("获取订单发件人收件人信息失败");
        }
        return addressJsonResponse.data();
    }


    /**
     * @description: 根据EntityId获取目标
     * @param: [entityId, customerCode]
     * @return: java.lang.String
     * @author: 陈永培
     * @createtime: 2019/7/24 13:53
     */
    protected String getTargetCustomerCodeByEntityId(Integer entityId, String customerCode) {
        String targetCustormerCode = "";
        JsonResponse<String> response = orderBopService.searchUpperCustomerCode(entityId, customerCode);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            throw new BusinessException("鹊桥通讯失败" + response.getMsg());
        }
        //鹊桥那边返回的data 内容 是String
        String json = response.getData();
        JSONObject jsonObject = JSON.parseObject(json);
        String code = (String) jsonObject.get("code");
        if (!BaseCodeEnum.SUCCESS.getCode().equals(code)) {
            Object msg = jsonObject.get("msg");
            if (null != msg) {
                log.error("获取上游客户编码失败  msg = {}", msg.toString());
                throw new BusinessException("获取上游客户编码失败" + msg.toString());
            }
            throw new BusinessException("获取上游客户编码失败");
        }
        String upperCustomerCode = (String) jsonObject.get("data");
        //4.根据步骤二中的upperCustomerCode查询B端客户映射，获取安得客户编码(targetCustomerCode)
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(upperCustomerCode)) {
            MidCustomerControl midCustomerControl = midCustomerControlManager.getMidCustControlCache(upperCustomerCode,SourceSystem.CCS.getKey());
            if (null != midCustomerControl) {
                targetCustormerCode = midCustomerControl.getAnnCustomerCode();
            }
        }
        return targetCustormerCode;
    }

    /**
     * 2020-4-8 10:04:59
     * 泓铄：
     * 判断条件：source_system = CAINIAO,order_type = AO,调拨4.0
     * 解析：
     * 1）在解析收货单位时，获取上游给的收货单位编码，这个编码匹配【MDM-eb_shipper-ebsp_upstream_code】中的值，如果无值，则报错卡住，“收货单位解析失败，请在LMDM-收发货人维护收货单位，编码：XX”；
     * 2）匹配ebsp_upstream_code后，获取ebsp_eblc_code，若无值，则报错卡住，“收货单位解析失败，请在LMDM-收发货人维护收货单位，编码：XX”；
     * 3）获取ebsp_eblc_code后，匹配【MDM-eb_location-eblc_code】，若无值，则报错卡住，“收货单位解析失败，请在LMDM-地点维护信息，编码：XX”；
     * 4）获取eblc_code，获取省市区县NAME、CODE，收货人名字、电话、详细地址，写入customer_order_address表receiver信息。
     */
    private void setCNReceiveUnit(String orderNo, String receiverCode, String senderCode, CustomerOrderInfo customerOrderInfo) {

        if(org.apache.commons.lang3.StringUtils.isEmpty(receiverCode)) {
            throw BusinessException.fail("菜鸟"+customerOrderInfo.getOrderType()+"调拨4.0收货人编码为空");
        }
        //获取收发货单位
        Map<String,Object> mapResult = getEblocaiton(receiverCode);
        //收发货人
        EbShipper ebShippers = (EbShipper)mapResult.get("EB_SHIPPER");
        //地址
        EbLocation ebLocation = (EbLocation)mapResult.get("EB_LOCATION");

        JsonResponse<CustomerOrderAddress> addressJsonResponse = customerOrderAddressFeign.findByOrderNo(orderNo);
        CustomerOrderAddress customerOrderAddress = addressJsonResponse.data();
        if (null != customerOrderAddress) {
            if (null != ebLocation) {
                customerOrderAddress.setReceiverCityCode(ebLocation.getEblcCityCode());
                customerOrderAddress.setReceiverCityName(ebLocation.getEblcCityName());
                customerOrderAddress.setReceiverCountryCode(ebLocation.getEblcCountryCode());
                customerOrderAddress.setReceiverCountryName(ebLocation.getEblcCountryName());
                customerOrderAddress.setReceiverDetailAddr(ebLocation.getEblcNameCn());
                customerOrderAddress.setReceiverDistrictCode(ebLocation.getEblcCountyCode());
                customerOrderAddress.setReceiverDistrictName(ebLocation.getEblcCountyName());
                customerOrderAddress.setReceiverProvinceCode(ebLocation.getEblcProvinceCode());
                customerOrderAddress.setReceiverProvinceName(ebLocation.getEblcProvinceName());
                customerOrderAddress.setReceiverTownCode(ebLocation.getEblcTownCode());
                customerOrderAddress.setReceiverTownName(ebLocation.getEblcTownName());
                customerOrderAddress.setReceiverDetailAddr(ebLocation.getEblcDetailAddress());
                String eblcLongitude = ebLocation.getEblcLongitude();
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(eblcLongitude)){
                    customerOrderAddress.setEndLng(new BigDecimal(eblcLongitude));
                }
                String eblcLatitude = ebLocation.getEblcLatitude();
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(eblcLatitude)){
                    customerOrderAddress.setEndLat(new BigDecimal(eblcLatitude));
                }
            }

            //设置收发货人单位中文名称
            String ebspNameCn = ebShippers.getEbspNameCn();
            customerOrderInfo.setUpperReceiverName(ebspNameCn);

            customerOrderAddress.setReceiverName(ebShippers.getEbspContact());
            customerOrderAddress.setReceiverTel(ebShippers.getEbspContactTel());
            customerOrderAddress.setReceiverMobile(ebShippers.getEbspContactMobile());
            //解析发货单位
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(senderCode)){
                //获取收发货单位
                Map<String,Object> mapShipperLocResult = getEblocaiton(senderCode);
                //收发货人
                EbShipper sdShippers = (EbShipper)mapShipperLocResult.get("EB_SHIPPER");
                //地址
                EbLocation sdLocation = (EbLocation)mapShipperLocResult.get("EB_LOCATION");

                if (null != customerOrderAddress &&  null != sdLocation ){
                    customerOrderAddress.setSenderCityCode(sdLocation.getEblcCityCode());
                    customerOrderAddress.setSenderCityName(sdLocation.getEblcCityName());
                    customerOrderAddress.setSenderCountryCode(sdLocation.getEblcCountryCode());
                    customerOrderAddress.setSenderCountryName(sdLocation.getEblcCountryName());
                    customerOrderAddress.setSenderDetailAddr(sdLocation.getEblcNameCn());
                    customerOrderAddress.setSenderDistrictCode(sdLocation.getEblcCountyCode());
                    customerOrderAddress.setSenderDistrictName(sdLocation.getEblcCountyName());
                    customerOrderAddress.setSenderProvinceCode(sdLocation.getEblcProvinceCode());
                    customerOrderAddress.setSenderProvinceName(sdLocation.getEblcProvinceName());
                    customerOrderAddress.setSenderTownCode(sdLocation.getEblcTownCode());
                    customerOrderAddress.setSenderTownName(sdLocation.getEblcTownName());
                    customerOrderAddress.setSenderDetailAddr(sdLocation.getEblcDetailAddress());
                }

                //设置收发货人单位中文名称
                String ebspName = sdShippers.getEbspNameCn();
                customerOrderInfo.setUpperSenderName(ebspName);

                customerOrderAddress.setSenderName(sdShippers.getEbspContact());
                customerOrderAddress.setSenderTel(sdShippers.getEbspContactTel());
                customerOrderAddress.setSenderMobile(sdShippers.getEbspContactMobile());

            }

            lmpOrderFlowHelper.updateCustomerOrderAddress(customerOrderAddress);
        }


    }

    private Map<String,Object> getEblocaiton(String receiverCode){
        Map<String,Object> map = new HashMap<String,Object>();
        EbShipper search = new EbShipper();
        search.setEbspUpstreamCode(receiverCode);
        search.setEbspStatus("0");
        PageResponse<EbShipper> pageResponse = ebShippersFeign.search(search).data();

        EbShipper ebShippers = null;
        if(null != pageResponse){
            List<EbShipper> list = pageResponse.getList();
            if (MideaStringUtils.isNotEmpty(list)) {
                ebShippers = list.get(0);
            }
        }

        if(null == ebShippers){
            throw BusinessException.fail("收发货单位解析失败，请在LMDM-收发货人维护收货单位，编码："+receiverCode);
        }
        map.put("EB_SHIPPER",ebShippers);
        String ebLcCode = ebShippers.getEbspEblcCode();
        if(org.apache.commons.lang3.StringUtils.isEmpty(ebLcCode)) {
            throw BusinessException.fail("收发货单位解析失败，请在LMDM-地点维护信息，编码："+receiverCode);
        }
        EbLocation ebLocation = ebLocationManager.getEbLocationCache(ebLcCode);
        if(null == ebLocation){
            throw BusinessException.fail("收发货单位解析失败，请在LMDM-收发货人维护收货单位，编码："+receiverCode);
        }
        map.put("EB_LOCATION", ebLocation);
        return map;

    }

    @Override
    @ZeebeFlow("PROJECT_CLASSIFY")
    public OrderInfoExt analysisProjectClassify(OrderInfoExt orderInfo) {

        if (StringUtils.isBlank(orderInfo.getOrderNo())) {
            throw BusinessException.fail("analysisProjectClassify , but orderNo is null ...");
        }
//        if (BusinessMode.isB2B(orderInfo.getBusinessMode())) {
//             FlowListenerParam.success("B2B订单无需映射分类");
//            return orderInfo;
//        }
//         邦山：这类单如果前面被打了标，还是要覆盖上去
        if (!(SourceSystem.SCC.getKey().equals(orderInfo.getSourceSystem()) && OrderType.isTFOrder(orderInfo.getOrderType()) && StringUtils.isNotEmpty(orderInfo.getOriginOrderNo()))) {
            if (StringUtils.isNotBlank(orderInfo.getProjectClassify())) {
                FlowListenerParam.success("项目分类已存在");
                return orderInfo;
            }
        }
        //查询项目类型
        ProjectClassifyConfig projectClassifyConfigRequest = new ProjectClassifyConfig();
        projectClassifyConfigRequest.setWhCode(orderInfo.getWhCode());
        projectClassifyConfigRequest.setBusinessMode(orderInfo.getBusinessMode());
        projectClassifyConfigRequest.setSourceSystem(orderInfo.getSourceSystem());

        String projectClassify = "";
        log.info("analysisProjectClassify-sourceSystem:{},isTFOrder:{},getOriginOrderNo：{}",orderInfo.getSourceSystem(),orderInfo.getOrderType(),orderInfo.getOriginOrderNo());
        // 如果是SCC的状态调整单且OriginOrderNo存在
        if (SourceSystem.SCC.getKey().equals(orderInfo.getSourceSystem()) && OrderType.isTFOrder(orderInfo.getOrderType()) && StringUtils.isNotEmpty(orderInfo.getOriginOrderNo())) {
            ValueAddedService valueAddedService = new ValueAddedService();
            valueAddedService.setVdOrderNo(orderInfo.getOriginOrderNo());
            JsonResponse<ValueAddedService> valueAddedServiceJsonResponse = valueAddedServiceFeign.selectOne(valueAddedService);
            if (valueAddedServiceJsonResponse == null || !BaseCodeEnum.SUCCESS.getCode().equals(valueAddedServiceJsonResponse.getCode())) {
                FlowListenerParam.fail("查询增值服务单失败:" + JSON.toJSONString(valueAddedServiceJsonResponse));
                return orderInfo;
            }
            if (null == valueAddedServiceJsonResponse.getData()) {
                FlowListenerParam.fail("此订单对应的增值服务单不存在,源单号:" + orderInfo.getOriginOrderNo());
                return orderInfo;
            }
            ValueAddedService data = valueAddedServiceJsonResponse.getData();
            // 增值服务的美的鉴定单,根据商品状态进行项目分类打标，写入project_classify
            if (ValueAddedServiceType.isMEO(data.getServiceType())) {
                JsonResponse<List<OrderInfoItem>> orderItemResponse = orderInfoItemFeign.getOrderItem(orderInfo.getOrderNo());
                if (orderItemResponse == null || !BaseCodeEnum.SUCCESS.getCode().equals(orderItemResponse.getCode())) {
                    FlowListenerParam.fail("查询订单明细失败:" + JSON.toJSONString(orderItemResponse));
                    return orderInfo;
                }
                if (null == orderItemResponse.getData()) {
                    FlowListenerParam.fail("此订单明细不存在,订单号:" + orderInfo.getOrderNo());
                    return orderInfo;
                }
                OrderInfoItem orderInfoItem = orderItemResponse.getData().get(0);
                String itemStatus = orderInfoItem.getItemStatus();
                String itemStatusTo = orderInfoItem.getItemStatusTo();
                if (ItemStatus.Q.getKey().equals(itemStatus)) {
                    if (ItemStatus.B.getKey().equals(itemStatusTo)) {
                        projectClassify = ProjectClassifyEnum.MEO_TO_REPAIR.getKey();
                    } else if (ItemStatus.Y.getKey().equals(itemStatusTo)) {
                        projectClassify = ProjectClassifyEnum.MEO_TO_CERT.getKey();
                    } else if (ItemStatus.N.getKey().equals(itemStatusTo)) {
                        projectClassify = ProjectClassifyEnum.MEO_TO_INCOMPLETE.getKey();
                    }
                }
            }
        } else {
            List<ProjectClassifyConfig> projectClassifys = ruleCache.searchList(projectClassifyConfigRequest);
            if (CollectionUtils.isEmpty(projectClassifys)) {
                FlowListenerParam.success("未找到项目分类信息,不分类");
                return orderInfo;
            }

            projectClassify = projectClassifys.get(0).getProjectClassify();
        }
        String projectClassifyName = dictHelper.getDictVaule(ProjectClassifyEnum.DICT_CODE.getKey(), projectClassify);
        if (StringUtils.isBlank(projectClassifyName)) {
            FlowListenerParam.success("查询到项目分类配置,配置为空,不分类");
            return orderInfo;
        }
        orderInfo.setProjectClassify(projectClassify);
        lmpOrderFlowHelper.updateOrderInfo(orderInfo, "项目分类解析结果:"+projectClassifyName);
        FlowListenerParam.success("项目分类解析结果:"+projectClassifyName);
        return orderInfo;

    }

    private void setCloudWhAddreeInfo(CustomerOrderAddress oldAdress, Object obj) {
        if (obj instanceof CustomerOrderAddress) {
            CustomerOrderAddress newAdress = (CustomerOrderAddress) obj;
            oldAdress.setReceiverDetailAddr(newAdress.getReceiverDetailAddr());
            oldAdress.setOriginAddr(newAdress.getReceiverDetailAddr());
            oldAdress.setReceiverTownCode(newAdress.getReceiverTownCode());
            oldAdress.setReceiverName(newAdress.getReceiverName());
            oldAdress.setReceiverTel(newAdress.getReceiverTel());
            oldAdress.setReceiverMobile(newAdress.getReceiverMobile());
        }

        if (obj instanceof SearchWhResponse) {
            SearchWhResponse whResponse = (SearchWhResponse) obj;
            oldAdress.setReceiverDetailAddr(whResponse.getDetailAddress());
            oldAdress.setOriginAddr(whResponse.getDetailAddress());
            oldAdress.setReceiverTownCode(whResponse.getTown());
            oldAdress.setReceiverName(whResponse.getContact());
            oldAdress.setReceiverTel(whResponse.getTel());
            oldAdress.setReceiverMobile(whResponse.getTel());
        }

        if (obj instanceof CdWarehouseDto) {
            CdWarehouseDto whDto = (CdWarehouseDto) obj;
            oldAdress.setReceiverDetailAddr(whDto.getEblcDetailAddress());
            oldAdress.setOriginAddr(whDto.getEblcDetailAddress());
            oldAdress.setReceiverTownCode(whDto.getEblcTownCode());
            oldAdress.setReceiverName(whDto.getCdwhContact());
            oldAdress.setReceiverTel(whDto.getCdwhTel());
            oldAdress.setReceiverMobile(whDto.getCdwhTel());
        }
        oldAdress.setReceiverCountryCode("");
        oldAdress.setReceiverCountryName("");
        oldAdress.setReceiverCityCode("");
        oldAdress.setReceiverCityName("");
        oldAdress.setReceiverDistrictCode("");
        oldAdress.setReceiverDistrictName("");
        oldAdress.setReceiverTownName("");
    }

}
