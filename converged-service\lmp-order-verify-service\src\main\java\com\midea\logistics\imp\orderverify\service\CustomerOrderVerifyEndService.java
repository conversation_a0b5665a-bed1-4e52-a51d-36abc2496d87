package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.mideaframework.core.web.JsonResponse;

public interface CustomerOrderVerifyEndService {

    /**
     * 父单流程审核结束触发子流程
     * @param customerOrderInfoExt
     * @return
     */
    CustomerOrderInfoExt customerOrderVerifyEnd(CustomerOrderInfoExt customerOrderInfoExt);

}