package com.midea.logistics.otplbs.manager.helper;

import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbPlace;
import com.midea.logistics.domain.mdm.request.CdWarehouseRequest;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.CdWarehouseFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbplaceFeign;
import com.midea.logistics.otp.common.helper.bean.ShippingTypeRuleExcelResponse;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.BusinessMode;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderType;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CheckShippingTypeRuleExcel implements CheckProperty<ShippingTypeRuleExcelResponse>{

    @Autowired
    private EbplaceFeign ebPlaceFeign;
    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private CdWarehouseFeign cdWarehouseFeign;
    @Autowired
    private EbCustomerManager ebCustomerManager;
    @Autowired
    private DictHelper dictHelper;

    /**
     * 配送方式导入：
     * 校验business_mode,wh_code不能为空；province_name,city_name,distract_name不能为空，
     * 并且解析出地址编码存入；town_name可以为空，如果有填写，也需解析出地址编码存入；
     * delivery_type也要校验不能为空，且要将汉字转换为对应的数据字典。
     * 如果导入失败，要提示第几行导入失败，其余继续导入。
     * 校验重复：business_mode,wh_code,distract_name相同时，该条导入失败，提示数据已存在
     * id不存在，默认新增，有id，则是修改
     * @param shippingTypeRule
     */
    @Override
    public void check(ShippingTypeRuleExcelResponse shippingTypeRule) {
        if (ToolUtils.isEmpty(shippingTypeRule.getSiteCode())) {
            throw BusinessException.fail("平台编不能为空");
        }
        if (ToolUtils.isEmpty(shippingTypeRule.getSiteName())) {
            throw BusinessException.fail("平台名称不能为空");
        }
        if (ToolUtils.isEmpty(shippingTypeRule.getBusinessMode())) {
            throw BusinessException.fail("业务模式不能为空");
        }
        //配送方式
        String deliveryTypeName = shippingTypeRule.getDeliveryType();
        if (ToolUtils.isEmpty(deliveryTypeName)) {
            throw BusinessException.fail("配送方式不能为空");
        }
        String deliveryType= DeliveryType.getType(deliveryTypeName);
        if(ToolUtils.isEmpty(deliveryType)){
            throw BusinessException.fail("配送方式填写错误，配送方式字典未匹配到！");
        }
        shippingTypeRule.setDeliveryType(deliveryType);
        //Optional<DeliveryType> first = Lists.newArrayList(DeliveryType.values()).stream().filter(o -> o.getValue().equals(deliveryTypeName)).findFirst();
        //if (first.isPresent()){
        //    shippingTypeRule.setDeliveryType(first.get().getKey());
        //}
        checkTown(shippingTypeRule.getProvinceName(),shippingTypeRule.getCityName(),shippingTypeRule.getDistrictName(),shippingTypeRule.getTownName(),shippingTypeRule);

        String orderType = shippingTypeRule.getOrderType();

        //2021年3月31日16:19:32  王涛 ： YS 仓库必须为空
        if (ToolUtils.isNotEmpty(orderType) && OrderType.YS.getKey().equals(orderType)){
            if (ToolUtils.isNotEmpty(shippingTypeRule.getWhCode())) {
                throw BusinessException.fail("订单类型=运输(YS),仓库编码必须为空");
            }
        }else{
            if (ToolUtils.isEmpty(shippingTypeRule.getWhCode())) {
                throw BusinessException.fail("仓库编码不能为空");
            }
        }

        //2021 0419 王涛:配送方式导入 根据业务类型B2B/B2C限制导入的配送方式
        if (BusinessMode.isB2C(shippingTypeRule.getBusinessMode())) {
            String dictVaule = dictHelper.getDictVaule(CommonConstant.DELIVERY_TYPE_B2C, shippingTypeRule.getDeliveryType());
            if (StringUtils.isEmpty(dictVaule)) {
                throw BusinessException.fail("当前导入B2C配送方式异常:" +DeliveryType.getName(shippingTypeRule.getDeliveryType()));
            }
        }
        if (BusinessMode.isB2B(shippingTypeRule.getBusinessMode())) {
            String dictVaule = dictHelper.getDictVaule(CommonConstant.DELIVERY_TYPE_B2B, shippingTypeRule.getDeliveryType());
            if (StringUtils.isEmpty(dictVaule)) {
                throw BusinessException.fail("当前导入B2B配送方式异常:" +DeliveryType.getName(shippingTypeRule.getDeliveryType()));
            }
        }

        CdWarehouse cdWarehouseCache = cdWarehouseManager.getCdWarehouseCache(shippingTypeRule.getWhCode());
        if (ToolUtils.isNotEmpty(cdWarehouseCache)) {
            shippingTypeRule.setWhName(cdWarehouseCache.getCdwhName());
        }
    }
    private String queryWhCode(String whName) {
        if(ToolUtils.isEmpty(whName)) {
            return null;
        }
        CdWarehouseRequest param = new CdWarehouseRequest();
        //cdwhIsStop=0
        param.setCdwhIsStop(0d);
        param.setCdwhName(whName);
        JsonResponse<PageResponse<CdWarehouse>> jsonResponse = cdWarehouseFeign.search(param);
        List<CdWarehouse> list = jsonResponse.data.list;
        CdWarehouse base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getWhCode();
    }


    /**
     * 以下为校验用户输入的省市区镇
     *
     * @param
     * @return
     */
    public void checkTown(String provinceName, String cityName, String districtName, String townName,ShippingTypeRuleExcelResponse shippingTypeRule) {



        if(ToolUtils.isNotEmpty(provinceName)){
            if ( ! (ToolUtils.isNotEmpty(cityName) && ToolUtils.isNotEmpty(districtName))){
                throw BusinessException.fail("省名称不为空，市区名称必填");
            }
        }

        if(ToolUtils.isNotEmpty(cityName)){
            if ( ! (ToolUtils.isNotEmpty(provinceName) && ToolUtils.isNotEmpty(districtName))){
                throw BusinessException.fail("市名称不为空，省区名称必填");
            }
        }

        if(ToolUtils.isNotEmpty(districtName)){
            if ( ! (ToolUtils.isNotEmpty(provinceName) && ToolUtils.isNotEmpty(cityName))){
                throw BusinessException.fail("区名称不为空，省市名称必填");
            }
        }

        if(ToolUtils.isNotEmpty(townName)){
            if ( ! (ToolUtils.isNotEmpty(provinceName) && ToolUtils.isNotEmpty(cityName) && ToolUtils.isNotEmpty(districtName))){
                throw BusinessException.fail("镇名称不为空，省市区名称必填");
            }
        }

        //控制到必须前面1-3、1-4级必须有，否则，全部return
        if (ToolUtils.isNotEmpty(provinceName) && ToolUtils.isNotEmpty(cityName) && ToolUtils.isNotEmpty(districtName)) {

            EbPlace place = queryPlace(provinceName, "PLACE_PROVINCE", "100000");
            if (place != null) {
                shippingTypeRule.setProvinceCode(place.getEbplCode());
                if (ToolUtils.isNotEmpty(cityName)) {
                    place = queryPlace(cityName, "PLACE_CITY", place.getEbplCode());
                }
            }

            if (place != null) {
                shippingTypeRule.setCityCode(place.getEbplCode());
                if (ToolUtils.isNotEmpty(districtName)) {
                    place = queryPlace(districtName, "PLACE_DISTRICT", place.getEbplCode());
                }
            }
            if (place == null) {
                throw new BusinessException(BaseCodeEnum.PARAMETER_INVALID.getCode(), provinceName + "省" + cityName + "市" + districtName + "区" + townName + "不存在");
            }
            shippingTypeRule.setDistrictCode(place.getEbplCode());
            if (ToolUtils.isNotEmpty(townName)) {
                place = queryPlace(townName, "PLACE_STREET", place.getEbplCode());
                if (place != null) {
                    shippingTypeRule.setTownCode(place.getEbplCode());
                }
            }

        }

    }
    /**
     * 查询地点
     */
    private EbPlace queryPlace(String name, String type, String parentCode) {
        EbPlace ebPlaceRequest = new EbPlace();
        ebPlaceRequest.setEbplNameCn(name);
        ebPlaceRequest.setEbplType(type);
        ebPlaceRequest.setEbplParentPmCode(parentCode);
        ebPlaceRequest.setEbplIsAble("ENABLE");
        JsonResponse<List<EbPlace>> jsonResponse = ebPlaceFeign.queryEbPlace(ebPlaceRequest,1);
        List<EbPlace> ebPlaceList = jsonResponse.data();
        EbPlace ebPlace = CollectionUtils.isEmpty(ebPlaceList) ? null : ebPlaceList.get(0);
        if (null !=ebPlace && ToolUtils.isNotEqual(name,ebPlace.getEbplNameCn())){
            ebPlace = null;
        }
        return ebPlace;
    }
}
