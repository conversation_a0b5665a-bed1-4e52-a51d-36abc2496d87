package com.midea.logistics.imp.orderverify.helper;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.bop.BopNetFeign;
import com.midea.logistics.otp.common.feign.convergedfeign.ordertask.TaskApiFeign;
import com.midea.logistics.otp.common.feign.servicefeign.ConvergedCustomerOrderFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.SetDeliveryOrderInfoFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.lastmile.bean.request.CspProtectSiteCodeRequest;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderAddressFeign;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.OrderInfoItemFeign;
import com.midea.logistics.otp.order.common.fegin.OrderLabelFeign;
import com.midea.logistics.otp.order.common.fegin.ShippingTypeRuleFeign;
import com.midea.logistics.otp.order.common.fegin.TaskFeign;
import com.midea.logistics.otp.order.common.helper.DeliveryTypeRuleHelper;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.ShippingTypeRuleHelper;
import com.midea.logistics.otp.order.common.helper.SmallPartsHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.SetDeliveryOrderInfo;
import com.midea.logistics.otp.order.domain.dto.CustomerOrderInfoExtendConfDto;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.midea.logistics.otp.rule.domain.request.ShippingTypeRuleRequest;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LmpDeliveryTypeHelper {

    @Autowired
    private DictHelper dictHelper;

    @Autowired
    private ConvergedCustomerOrderFeign convergedCustomerOrderFeign;
    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;
    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    private DeliveryTypeRuleHelper deliveryTypeRuleHelper;

    @Autowired
    private LmpOrderFlowHelper  lmpOrderFlowHelper;

    @Autowired
    private ShippingTypeRuleFeign shippingTypeRuleFeign;
    @Autowired
    BusinessHelper businessHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private ShippingTypeRuleHelper shippingTypeRuleHelper;
    @Autowired
    private SmallPartsHelper smallPartsHelper;
    @Autowired
    private BopNetFeign bopNetFeign;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;
    @Autowired
    private OrderLabelFeign orderLabelFeign;
    @Autowired
    private SetDeliveryOrderInfoFeign setDeliveryOrderInfoFeign;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private TaskFeign taskFeign;
    @Autowired
    private TaskApiFeign taskApiFeign;


    /**
     * !@配送方式解析 IN2查询配送方式配置入口
     * @param customerOrderInfo
     * @param orderInfo
     * @param businessMode
     * @param isWhCodeNull
     * @return
     */
    public ShippingTypeRule getDeliveryRule(CustomerOrderInfo customerOrderInfo,OrderInfo orderInfo, BusinessMode businessMode, boolean isWhCodeNull, boolean isFilterCompleteSet) {

        String orderType = orderInfo.getOrderType();
        String orderNo = orderInfo.getOrderNo();
        String parentOrderNo = orderInfo.getParentOrderNo();
        String customerCode = orderInfo.getCustomerCode();
        String whCode = orderInfo.getWhCode();
        String siteCode = orderInfo.getSiteCode();
        String sourceSystem = orderInfo.getSourceSystem();

        if (StringUtils.isEmpty(orderType)) {
            throw BusinessException.fail("订单类型不能为空!");
        }
        if (StringUtils.isBlank(whCode) && StringUtils.isBlank(siteCode)) {
            throw BusinessException.fail("仓库编码或平台编码为空，无法解析配送方式");
        }
        CustomerOrderAddress address = new CustomerOrderAddress();
        address.setOrderNo(parentOrderNo);
        JsonResponse<List<CustomerOrderAddress>> list = customerOrderAddressFeign.list(address);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(list.getCode()) || CollectionUtils.isEmpty(list.data)) {
            throw BusinessException.fail("获取地址失败");
        }
        address = list.getData().get(0);
        //仓库，业务模式，配送方式必填， 地址、客户不是必填，若地址选择，选择到三级就可以了
        //查询优先级，地址，客户

        //入库单要根据发货地址匹配，出库单、纯运输订单根据收货地址匹配
        String bigType = OrderType.getBigType(orderType);
        String provinceCode = address.getReceiverProvinceCode();
        String cityCode = address.getReceiverCityCode();
        String districtCode = address.getReceiverDistrictCode();
        String townCode = address.getReceiverTownCode();
        if (InOutType.IN.getName().equals(bigType)) {
            provinceCode = address.getSenderProvinceCode();
            cityCode = address.getSenderCityCode();
            districtCode = address.getSenderDistrictCode();
            townCode = address.getSenderTownCode();
        }

        ShippingTypeRuleRequest ruleSearch = new ShippingTypeRuleRequest();
        ruleSearch.setOrderNo(orderNo);
        ruleSearch.setParentOrderNo(parentOrderNo);
        ruleSearch.setOrderType(orderType);
        ruleSearch.setWhCodeNull(isWhCodeNull);
        ruleSearch.setWhCode(whCode);
        ruleSearch.setSiteCode(siteCode);
        ruleSearch.setBusinessMode(businessMode.getName());
        ruleSearch.setCustomerCode(customerCode);
        ruleSearch.setProvinceCode(provinceCode);
        ruleSearch.setCityCode(cityCode);
        ruleSearch.setTownCode(townCode);
        ruleSearch.setDistrictCode(districtCode);
        //方量取父单的总体积
        ruleSearch.setVolume(customerOrderInfo.getTotalVolume());
        //取子单的送装标识、毛重重量 by caoyy 20230823
        ruleSearch.setInstallFlag(orderInfo.getInstallFlag());
        ruleSearch.setWeight(orderInfo.getTotalGrossWeight());
        //202405 泓铄 增加是否高端机，满足送装解析才传
        //查扩展表
        CustomerOrderInfoExtend customerOrderInfoExtend = orderFlowHelper.getCustomerOrderInfoExtend(parentOrderNo);
        String completeSetNo = null;
        if(customerOrderInfoExtend != null){
            String confObj = customerOrderInfoExtend.getConfObj();
            if (StringUtils.isNotEmpty(confObj)){
                CustomerOrderInfoExtendConfDto customerOrderInfoExtendConfDto = JSON.parseObject(confObj, CustomerOrderInfoExtendConfDto.class);
                completeSetNo = customerOrderInfoExtendConfDto.getCompleteSetNo();
            }
        }
        
        if(CommonConstant.FLAG_YES.equals(orderInfo.getInstallFlag()) && BusinessMode.isB2C(businessMode.getName()) && OrderType.isPOOrder(orderType)){
            if(customerOrderInfoExtend != null){
                Integer vipService = customerOrderInfoExtend.getVipService();
                if (Objects.equals(vipService, VipServiceEnum.VIP_COLMO.getCode()) || Objects.equals(vipService, VipServiceEnum.VIP_TOSHIBA.getCode())) {
                    ruleSearch.setVipFlag(CommonConstant.FLAG_YES);
                }else{
                    ruleSearch.setVipFlag(CommonConstant.FLAG_NO);
                }
            }
        }
        
        //2025年7月13日11:47:52 泓铄，如果是套送，要替换成本地物流中心平台、仓库去匹配 https://cf.annto.com/pages/viewpage.action?pageId=82733315
        if(!isFilterCompleteSet && ToolUtils.isNotEmpty(completeSetNo)) {
            SetDeliveryOrderInfo query = new SetDeliveryOrderInfo();
            query.setCompleteSetNo(completeSetNo);
            query.setOrderNo(parentOrderNo);

            JsonResponse<List<SetDeliveryOrderInfo>> jsonResponse = setDeliveryOrderInfoFeign.list(query);
            if(jsonResponse != null && jsonResponse.judgeSuccess() && !CollectionUtils.isEmpty(jsonResponse.data)){
                SetDeliveryOrderInfo setDeliveryOrderInfo = jsonResponse.data.get(0);
                log.info("getDeliveryRule-> orderNo：{},走套送，套送号：{},新的仓库:{},新的平台:{}", orderNo, completeSetNo, setDeliveryOrderInfo.getLocalWhCode(),setDeliveryOrderInfo.getLocalSiteCode() );
                //如果带了仓库就设置,没带就不设置
                if(ToolUtils.isNotEmpty(ruleSearch.getWhCode())){
                    ruleSearch.setWhCode(setDeliveryOrderInfo.getLocalWhCode());
                }
                ruleSearch.setSiteCode(setDeliveryOrderInfo.getLocalSiteCode());
            }
        }
        
        log.info("getDeliveryRule-> orderNo:{},请求-》:installFlag={},volume={},weight={},vipFlag={},", orderNo, ruleSearch.getInstallFlag(), ruleSearch.getVolume(), ruleSearch.getWeight(), ruleSearch.getVipFlag());
        JsonResponse<ShippingTypeRule> jsonResponse=shippingTypeRuleFeign.getDeliveryRule(ruleSearch);
        if(!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())){
            throw BusinessException.fail(JSONObject.toJSONString(jsonResponse));
        }
        
        if(jsonResponse.getData() != null) {
            ShippingTypeRule shippingTypeRule = jsonResponse.getData();

            //2025年7月13日11:47:52 泓铄，套送逻辑 https://cf.annto.com/pages/viewpage.action?pageId=82733315
            if(!isFilterCompleteSet && ToolUtils.isNotEmpty(completeSetNo)){
                if(DeliveryType.isZT(shippingTypeRule.getDeliveryType()) 
                        || DeliveryType.isEXPRESS(shippingTypeRule.getDeliveryType()) 
                        || DeliveryType.isEAD(shippingTypeRule.getDeliveryType())){
                    clearCompleteSetNo(orderNo, customerOrderInfoExtend, parentOrderNo);

                    orderInfo.setDeliveryTypeSpecialType(CommonConstant.COMPLETE_SET_NO_NO);
                    return shippingTypeRule;
                }
                
                //如果非快递/自提，则走套送逻辑
                orderInfo.setDeliveryTypeSpecialType(CommonConstant.COMPLETE_SET_NO_YES);
            }
            
            //202408 泓铄 B2C大件BC一体配送方式解析增加逻辑：若匹配结果为网点直配/宅配，或者匹配不到默认DOT时，增加下判断：若子单业务模式为B2C，查询判断下所有商品的单台体积都小于系统控制参数【VOLUME_CONTROL】——配置0.1，则将配送方式转化为直配，打印订单审核日志展示为“小件订单，直配”  https://cf.annto.com/x/6J0TAw
            if (ToolUtils.isEmpty(orderInfo.getDeliveryTypeSpecialType())
                    && (DeliveryType.isDOTType(shippingTypeRule.getDeliveryType()) || DeliveryType.isNetMatching(shippingTypeRule.getDeliveryType()))
                    && shippingTypeRuleHelper.checkBigEcInstall(orderInfo.getBusinessMode(), orderInfo.getOrderType(), orderInfo.getInstallFlag(), orderInfo.getWhCode())) {
                JsonResponse<List<OrderInfoItem>> itemJsonResponse = orderInfoItemFeign.getOrderItem(orderInfo.getOrderNo());
                if (itemJsonResponse.judgeSuccess() && ToolUtils.isNotEmpty(itemJsonResponse.data())) {
                    List<OrderInfoItem> orderItemList = itemJsonResponse.data;
                    if (smallPartsHelper.checkSmallOrder(orderInfo.getOrderNo(), orderInfo.getSiteCode(), orderItemList)) {
                        log.info("getDeliveryRule -> orderNo:{},小件订单，配送方式转为直配", orderNo);
                        //设置为直配
                        ShippingTypeRule shippingTypeRuleTemp = new ShippingTypeRule();
                        shippingTypeRuleTemp.setDeliveryType(DeliveryType.WAREHOUSEMATCHING.getKey());
                        orderInfo.setDeliveryTypeSpecialType(CommonConstant.NET_MATCHING_OR_DOT_TO_WAREHOUSEMATCHING);
                        return shippingTypeRuleTemp;
                    }
                }
            } 
            
            //上面的特殊逻辑如果解析到了，就不处理下面的特殊逻辑处理了
            if (ToolUtils.isEmpty(orderInfo.getDeliveryTypeSpecialType()) 
                    && shippingTypeRuleHelper.checkProjectSiteRange(businessMode, sourceSystem, orderType, shippingTypeRule.getDeliveryType())) {
                //202412 嘉龙 满足条件来源系统=OFC、MRP，订单类型=销售出库、业务模式=B2C,如果配送方式=直配、网点直配,需要查询csp自保网点 https://cf.annto.com/pages/viewpage.action?pageId=59867038
                if (ToolUtils.isNotEmpty(customerOrderInfo.getUpperReceiverCode()) && ToolUtils.isNotEmpty(address.getReceiverDistrictCode())) {
                    try {
                        CspProtectSiteCodeRequest query = new CspProtectSiteCodeRequest();
                        query.setDistrictCode(address.getReceiverDistrictCode());
                        query.setCustomerCode(customerOrderInfo.getUpperReceiverCode());
                        JsonResponse jr = bopNetFeign.protectSiteCode(query);
                        if (jr.judgeSuccess() && ToolUtils.isNotEmpty(jr.getData())) {
                            log.info("getDeliveryRule -> orderNo:{},自保网点，配送方式为宅配", orderNo);
                            ShippingTypeRule shippingTypeRuleTemp = new ShippingTypeRule();
                            shippingTypeRuleTemp.setDeliveryType(DeliveryType.DOT.getKey());
                            orderInfo.setDeliveryTypeSpecialType(CommonConstant.NET_MATCHING_OR_WAREHOUSEMATCHING_TO_DOT);
                            return shippingTypeRuleTemp;
                        }
                    }catch (Exception e) {
                        log.warn("子单号：{},鹊桥调用csp获取自保网点接口异常：{}", orderNo, e.getMessage());
                        e.getStackTrace();
                    }
                }
            }

            log.info("getDeliveryRule-> orderNo:{},特殊场景：{},最终获取的配置是-》deliveryType:{}", orderNo, orderInfo.getDeliveryTypeSpecialType(), jsonResponse.getData().getDeliveryType());
            return shippingTypeRule;
        } else {
            if(!isFilterCompleteSet && ToolUtils.isNotEmpty(completeSetNo)){
                //失败也设置一下特殊场景,避免后续有其他特殊场景的影响
                orderInfo.setDeliveryTypeSpecialType(CommonConstant.COMPLETE_SET_NO_YES);
            }
        }
        return null;
    }

    public void clearCompleteSetNo(String orderNo, CustomerOrderInfoExtend customerOrderInfoExtend, String parentOrderNo) {
        log.info("getDeliveryRule-> orderNo:{},清空套送标,", orderNo);
        //2025年7月13日11:47:52 泓铄，如果是套送，结果为快递/自提且有套送标识，则打印日志：“快递/自提订单不执行套送”，且清空套送标识； https://cf.annto.com/pages/viewpage.action?pageId=82733315
        CustomerOrderInfoExtendConfDto customerOrderInfoExtendConfDto = JSON.parseObject(customerOrderInfoExtend.getConfObj(), CustomerOrderInfoExtendConfDto.class);
        customerOrderInfoExtendConfDto.setCompleteSetNo(null);
        CustomerOrderInfoExtendConfDto newCustomerOrderInfoExtendConfDto = new CustomerOrderInfoExtendConfDto();
        BeanUtils.copyProperties(customerOrderInfoExtendConfDto, newCustomerOrderInfoExtendConfDto);
        customerOrderInfoExtend.setConfObj(JSON.toJSONString(newCustomerOrderInfoExtendConfDto));
        orderFlowHelper.saveOrUpdateCustomerExtend(customerOrderInfoExtend);

        //异步
        CompletableFuture.runAsync(()->{
            //更新套送表父单不套送、下发父单下的所有holdFlag=18的任务
            saveSetDeliveryAndIssueTask(parentOrderNo, orderNo);
        });
    }

    /**
     * 更新套送表父单不套送、下发父单下的所有holdFlag=18的任务
     * @param parentOrderNo
     * @param orderNo
     */
    private void saveSetDeliveryAndIssueTask(String parentOrderNo, String orderNo) {
        log.info("{}，{} ==》 saveSetDeliveryAndIssueTask", parentOrderNo, orderNo);
        SetDeliveryOrderInfo setDeliveryOrderInfo = new SetDeliveryOrderInfo();
        setDeliveryOrderInfo.setOrderNo(parentOrderNo);
        setDeliveryOrderInfo.setNotSetFlag(CommonConstant.FLAG_YES);

        //更新套送表
        businessParamHelper.batchUpdateSetDeliveryOrderInfoByOrderNo(Collections.singletonList(setDeliveryOrderInfo));

        
        //根据父单号查询任务，取hold=18的下发
        JsonResponse<List<Task>> taskJsonResponse = taskFeign.listByParentOrderNos(Collections.singletonList(parentOrderNo));

        if(taskJsonResponse != null && taskJsonResponse.judgeSuccess() && ToolUtils.isNotEmpty(taskJsonResponse)){
            //下发的任务,只要hold=18
            List<Task> toBeIssuedList = taskJsonResponse.getData().stream().filter(i->HoldFlag.SET_DELIVERY_HOLD.getKey().equals(i.getHoldFlag())).collect(Collectors.toList());
            if(ToolUtils.isNotEmpty(toBeIssuedList)){
                List<String> toBeIssuedTaskNoList = toBeIssuedList.stream().map(Task::getTaskNo).collect(Collectors.toList());
                taskApiFeign.taskIssue(toBeIssuedTaskNoList);
            } else{
                log.warn("配送方式解析套送，父单号={}，子单号={}，移除套送标识-通过父单号查询任务返回为空：{}", parentOrderNo, orderNo, JSON.toJSONString(taskJsonResponse));
            }
        }else {
            log.warn("配送方式解析套送，父单号={}，子单号={}，移除套送标识-通过父单号查询任务失败：{}", parentOrderNo, orderNo, taskJsonResponse == null ? "null" : JSON.toJSONString(taskJsonResponse));
        }
    }

    /**
     * 配送方式逻辑前置判断
     *
     * @param customerOrderInfo
     * @param orderInfo
     * @param businessMode
     * @return
     */
    public DeliveryType getDeliveryType(CustomerOrderInfo customerOrderInfo, OrderInfo orderInfo, BusinessMode businessMode) {
        //B2C配送方式逻辑前置判断
        if (businessMode == BusinessMode.B2C) {
            OrderType orderType = OrderType.valueOf(customerOrderInfo.getOrderType());
            Integer outsourceFlag = customerOrderInfo.getOutsourceFlag();
            String joinType = customerOrderInfo.getJoinType();
            if((OrderType.YS.getKey().equals(customerOrderInfo.getOrderType())||OrderType.PO.getKey().equals(customerOrderInfo.getOrderType()))&&SourceSystem.CVTE.getKey().equals(customerOrderInfo.getSourceSystem())) {
                return DeliveryType.WAREHOUSEMATCHING;
            }
            if (OrderType.DPRI == orderType || OrderType.DP == orderType) {
                return DeliveryType.DOT;
            }
            boolean isZT = (DeliveryType.ZT.getKey().equals(customerOrderInfo.getDeliveryType())) || (OrderType.AO == orderType && null != outsourceFlag && 1 == outsourceFlag) || (OrderType.RO == orderType && null != outsourceFlag && 1 == outsourceFlag);
            if (isZT) {
                return DeliveryType.ZT;
            }
            //2022年3月7日17:12:07 李娟：中台的配送方式，YS默认为YS，有问题，改成先读配置，配置没读到2C的默认直配，2B的默认为运输，
            //if (OrderType.YS == orderType) {
            //    return DeliveryType.YS;
            //}
            boolean isZP = OrderType.AO == orderType || OrderType.RO == orderType;
            if (isZP) {
                return DeliveryType.WAREHOUSEMATCHING;
            }
            // 众筹订单是发快递的
            if (StringUtils.isNotBlank(joinType) && EnumUtils.isValidEnum(JoinType.class, joinType)) {
                JoinType joinTypeEnum = JoinType.valueOf(joinType);
                if (JoinType.CROWD_FUND == joinTypeEnum) {
                    return DeliveryType.EXPRESS;
                }
            }
            // join_type= 货权转移、调剂、电商货权转移的，配送方式自提
            if (StringUtils.isNotBlank(joinType) && EnumUtils.isValidEnum(JoinType.class, joinType)) {
                JoinType joinTypeEnum = JoinType.valueOf(joinType);
                if (JoinType.SHARE == joinTypeEnum || JoinType.TRANS_INV == joinTypeEnum || JoinType.SHARE1300 == joinTypeEnum) {
                    return DeliveryType.ZT;
                }
            }
            // 针对来源系统为MRP，紧急订单标识为是（emergence_flag=1）的订单，2C订单直接默认直配
            if (SourceSystem.isMRP(customerOrderInfo.getSourceSystem()) && ObjectUtil.equals(CommonConstant.FLAG_YES, customerOrderInfo.getEmergenceFlag())) {
                return DeliveryType.WAREHOUSEMATCHING;
            }
        }
        //B2B配送方式逻辑前置判断
        else {
            //OFC云仓货权转移默认自提
            if (businessHelper.isCloudWhTransFlag(orderInfo)) {
                return DeliveryType.ZT;
            }
            if((OrderType.YS.getKey().equals(orderInfo.getOrderType())||OrderType.PO.getKey().equals(orderInfo.getOrderType()))&&SourceSystem.CVTE.getKey().equals(orderInfo.getSourceSystem())) {
                return DeliveryType.WAREHOUSEMATCHING;
            }
            //国苏发网点的子单的join_type=PO-GS-01或者project_classify=PO-GS-01，就不去解析配送方式配置表,默认PO_GS
            if ((StringUtils.isNotEmpty(orderInfo.getProjectClassify()) && ProjectClassifyEnum.PO_GS_01.getKey().equals(orderInfo.getProjectClassify())) || (org.apache.commons.lang.StringUtils.isNotEmpty(orderInfo.getJoinType()) && JoinType.PO_GS_01.getKey().equals(orderInfo.getJoinType()))) {
                return DeliveryType.PO_GS;
            }
            // 针对来源系统为MRP，紧急订单标识为是（emergence_flag=1）的订单，2B订单直接默认配送
            if (SourceSystem.isMRP(orderInfo.getSourceSystem()) && ObjectUtil.equals(CommonConstant.FLAG_YES, orderInfo.getEmergenceFlag())) {
                return DeliveryType.DELIVERY;
            }
        }
        return null;
    }




    public void updateDeliveryType(OrderInfo orderInfo, DeliveryType deliveryType) {
//        if (BusinessMode.isB2C(orderInfo.getBusinessMode())) {
//            if ("10".equals(orderInfo.getOrderSourcePlatform())) {
//                orderInfo.setDeliveredVerifyFlag(1);
//            }
//            if (Lists.newArrayList(DeliveryType.WAREHOUSEMATCHING, DeliveryType.DOT).contains(deliveryType)) {
//                orderInfo.setDeliveredVerifyFlag(1);
//            }
//        }
        orderInfo.setDeliveryType(deliveryType.getKey());
        lmpOrderFlowHelper.updateOrderInfo(orderInfo, "配送方式");

    }

    /**
     * 父单流程查询配送方式
     * @param customerOrderInfo
     * @param address
     * @param businessMode
     * @param isWhCodeNull
     * @return
     */
    public ShippingTypeRule getDeliveryRule(CustomerOrderInfo customerOrderInfo,CustomerOrderAddress address, String businessMode, boolean isWhCodeNull) {

        String orderType = customerOrderInfo.getOrderType();
        String orderNo = customerOrderInfo.getOrderNo();
        String customerCode = customerOrderInfo.getCustomerCode();
        String whCode = customerOrderInfo.getWhCode();
        String siteCode = customerOrderInfo.getSiteCode();

        if (StringUtils.isEmpty(orderType)) {
            throw BusinessException.fail("订单类型不能为空!");
        }
        if (StringUtils.isBlank(whCode) && StringUtils.isBlank(siteCode)) {
            throw BusinessException.fail("仓库编码或平台编码为空，无法解析配送方式");
        }
        //仓库，业务模式，配送方式必填， 地址、客户不是必填，若地址选择，选择到三级就可以了
        //查询优先级，地址，客户

        //入库单要根据发货地址匹配，出库单、纯运输订单根据收货地址匹配
        String bigType = OrderType.getBigType(orderType);
        String provinceCode = address.getReceiverProvinceCode();
        String cityCode = address.getReceiverCityCode();
        String districtCode = address.getReceiverDistrictCode();
        String townCode = address.getReceiverTownCode();
        if (InOutType.IN.getName().equals(bigType)) {
            provinceCode = address.getSenderProvinceCode();
            cityCode = address.getSenderCityCode();
            districtCode = address.getSenderDistrictCode();
            townCode = address.getSenderTownCode();
        }

        ShippingTypeRuleRequest ruleSearch = new ShippingTypeRuleRequest();
        ruleSearch.setOrderNo(orderNo);
        ruleSearch.setParentOrderNo(orderNo);
        ruleSearch.setOrderType(orderType);
        ruleSearch.setWhCodeNull(isWhCodeNull);
        ruleSearch.setWhCode(whCode);
        ruleSearch.setSiteCode(siteCode);
        ruleSearch.setBusinessMode(businessMode);
        ruleSearch.setCustomerCode(customerCode);
        ruleSearch.setProvinceCode(provinceCode);
        ruleSearch.setCityCode(cityCode);
        ruleSearch.setTownCode(townCode);
        ruleSearch.setDistrictCode(districtCode);
        //方量取父单的总体积
        ruleSearch.setVolume(customerOrderInfo.getTotalVolume());
        log.info("getDeliveryRule-> orderNo:{},请求-》json:{}",orderNo, JSON.toJSONString(ruleSearch));
        JsonResponse<ShippingTypeRule> jsonResponse=shippingTypeRuleFeign.getDeliveryRule(ruleSearch);
        if(!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())){
            throw BusinessException.fail(JSONObject.toJSONString(jsonResponse));
        }
        if(jsonResponse.getData()!=null){
            log.info("getDeliveryRule-> orderNo:{},最终获取的配置是-》json:{}",orderNo,JSON.toJSONString(jsonResponse));
            return  jsonResponse.getData();
        }
        return null;
    }



}
