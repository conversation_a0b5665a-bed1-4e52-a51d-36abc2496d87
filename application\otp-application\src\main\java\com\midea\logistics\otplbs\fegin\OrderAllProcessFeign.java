package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.converged.domain.bean.NetworkFeedback;
import com.midea.logistics.otp.order.converged.domain.request.CustomerAddressRequest;
import com.midea.logistics.otp.order.converged.domain.response.CustomerOrderInfoResponse;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderLog;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.request.CustomerOrderInfoRequest;
import com.midea.logistics.otp.order.domain.request.OrderCancelInnerRequest;
import com.midea.logistics.otp.ordertask.converged.domain.request.ValueAddedServicesOrderRequest;
import com.midea.logistics.otp.report.service.domain.ReportRouters;
import com.midea.logistics.otp.report.service.domain.bean.OrderAllProcess;
import com.midea.logistics.otp.report.service.domain.bean.OrderAllProcessRequest;
import com.midea.logistics.otp.rule.domain.response.CloseOrderResponse;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.domain.request.AssociatedCooperativeRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


@FeignClient(value = "logistics-otp-report-service", url = BaseRouter.LOGISTICS_OTP_REPORT_SERVICE)
public interface OrderAllProcessFeign {

    @GetMapping(ReportRouters.ORDER_ALL_PROCESS)
    JsonResponse  getOrderAllProcess(@SpringQueryMap OrderAllProcessRequest orderAllProcessRequest);

    @GetMapping(ReportRouters.EXPORT_ORDER_ALL_PROCESS)
    JsonResponse<List<OrderAllProcess>> exportOrderAllProcess(@SpringQueryMap OrderAllProcessRequest orderAllProcessRequest);

    }
