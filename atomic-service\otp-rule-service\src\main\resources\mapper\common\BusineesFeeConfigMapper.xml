<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.BusineesFeeConfigMapper">

    <sql id="searchFieldsSql">
        `business_mode` AS  businessMode,
        `order_type` AS  orderType,
        `site_code` AS  siteCode,
        `create_user_code` AS  createUserCode,
        `delivery_type` AS  deliveryType,
        `customer_code` AS  customerCode,
        `update_time` AS  updateTime,
        `version` AS  version,
        `delete_flag` AS  deleteFlag,
        `update_user_code` AS  updateUserCode,
        `wh_name` AS  whName,
        `wh_code` AS  whCode,
        `create_time` AS  createTime,
        `id` AS  id,
        `business_type` AS  businessType
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="businessMode !=null and businessMode != ''">
            and `business_mode` =#{businessMode}
        </if>
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test=" orderType == ''">
            and (`order_type` ='' or `order_type`is null)
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and `delivery_type` =#{deliveryType}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="whName !=null and whName != ''">
            and `wh_name` =#{whName}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="businessType !=null and businessType != ''">
            and `business_type` =#{businessType}
        </if>
        <if test="dataAuthFlag == 1">
            and (@{"columnCode":"site_code","tableName":"es_company","custom":"site_code","dataCode":"D20190621009"}@)
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="businessMode !=null and businessMode != ''">
                `business_mode` = #{businessMode},
            </if>
            <if test="orderType !=null and orderType != ''">
                `order_type` = #{orderType},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="siteCode !=null and siteCode != ''">
                `site_code` = #{siteCode},
            </if>
            <if test="whName !=null and whName != ''">
                `wh_name` = #{whName},
            </if>
            <if test="whCode !=null and whCode != ''">
                `wh_code` = #{whCode},
            </if>
            <if test="deliveryType !=null and deliveryType != ''">
                `delivery_type` = #{deliveryType},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                `customer_code` = #{customerCode},
            </if>
            <if test="businessType !=null and businessType != ''">
                `business_type` = #{businessType},
            </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="businessMode !=null">
                            `business_mode` = #{businessMode},
                        </if>
                        <if test="orderType !=null">
                            `order_type` = #{orderType},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="siteCode !=null">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="whName !=null">
                            `wh_name` = #{whName},
                        </if>
                        <if test="whCode !=null">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="deliveryType !=null">
                            `delivery_type` = #{deliveryType},
                        </if>
                        <if test="customerCode !=null">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="businessType !=null">
                            `business_type` = #{businessType},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from businees_fee_config t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig">
        select
        <include refid="searchFieldsSql"/>
        from businees_fee_config t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from businees_fee_config t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig">
        select
        <include refid="searchFieldsSql"/>
        from businees_fee_config t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        businees_fee_config t
        <include refid="setFieldsSql"/>
        where
        `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        businees_fee_config t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
        `id` = #{id}
    </update>

    <update id="deleteById">
        update
            businees_fee_config t
        set  `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into businees_fee_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="businessMode !=null and businessMode != ''">
                `business_mode`,
            </if>

            <if test="orderType !=null and orderType != ''">
                `order_type`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="siteCode !=null and siteCode != ''">
                `site_code`,
            </if>

            <if test="whName !=null and whName != ''">
                `wh_name`,
            </if>

            <if test="whCode !=null and whCode != ''">
                `wh_code`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="deliveryType !=null and deliveryType != ''">
                `delivery_type`,
            </if>

            <if test="customerCode !=null and customerCode != ''">
                `customer_code`,
            </if>

            <if test="businessType !=null and businessType != ''">
                `business_type`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessMode !=null and businessMode != ''">
                #{businessMode},
            </if>
            <if test="orderType !=null and orderType != ''">
                #{orderType},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="siteCode !=null and siteCode != ''">
                #{siteCode},
            </if>
            <if test="whName !=null and whName != ''">
                #{whName},
            </if>
            <if test="whCode !=null and whCode != ''">
                #{whCode},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="deliveryType !=null and deliveryType != ''">
                #{deliveryType},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                #{customerCode},
            </if>
            <if test="businessType !=null and businessType != ''">
                #{businessType},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `business_mode`,
            `order_type`,
            `update_user_code`,
            `site_code`,
            `wh_name`,
            `wh_code`,
            `create_user_code`,
            `delivery_type`,
            `customer_code`,
            `business_type`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.businessMode},
            #{item.orderType},
            #{item.updateUserCode},
            #{item.siteCode},
            #{item.whName},
            #{item.whCode},
            #{item.createUserCode},
            #{item.deliveryType},
            #{item.customerCode},
            #{item.businessType},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        businees_fee_config
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.businessMode !=null and item.businessMode != ''">
                `business_mode` = #{item.businessMode},
            </if>
            <if test="item.orderType !=null">
                `order_type` = #{item.orderType},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.siteCode !=null and item.siteCode != ''">
                `site_code` = #{item.siteCode},
            </if>
            <if test="item.whName !=null and item.whName != ''">
                `wh_name` = #{item.whName},
            </if>
            <if test="item.whCode !=null and item.whCode != ''">
                `wh_code` = #{item.whCode},
            </if>
            <if test="item.deliveryType !=null and item.deliveryType != ''">
                `delivery_type` = #{item.deliveryType},
            </if>
            <if test="item.customerCode !=null">
                `customer_code` = #{item.customerCode},
            </if>
            <if test="item.businessType !=null and item.businessType != ''">
                `business_type` = #{item.businessType},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.businessMode !=null and item.businessMode != ''">
                and `business_mode` =#{item.businessMode}
            </if>
            <if test="item.orderType !=null and item.orderType != ''">
                and `order_type` =#{item.orderType}
            </if>
            <if test="item.siteCode !=null and item.siteCode != ''">
                and `site_code` =#{item.siteCode}
            </if>
            <if test="item.deliveryType !=null and item.deliveryType != ''">
                and `delivery_type` =#{item.deliveryType}
            </if>
            <if test="item.customerCode !=null and item.customerCode != ''">
                and `customer_code` =#{item.customerCode}
            </if>
            <if test="item.updateTime !=null">
                and `update_time` =#{item.updateTime}
            </if>
            <if test="item.version !=null">
                and `version` =#{item.version}
            </if>
            <if test="item.deleteFlag !=null">
                and `delete_flag` =#{item.deleteFlag}
            </if>
            <if test="item.whName !=null and item.whName != ''">
                and `wh_name` =#{item.whName}
            </if>
            <if test="item.whCode !=null and item.whCode != ''">
                and `wh_code` =#{item.whCode}
            </if>
            <if test="item.createTime !=null">
                and `create_time` =#{item.createTime}
            </if>
            <if test="item.businessType !=null and item.businessType != ''">
                and `business_type` =#{item.businessType}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE businees_fee_config
            <include refid="setBatchFieldsSql"/>
            where
            `id` = #{item.id}
        </foreach>

    </update>

    <update id="deleteBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE
            businees_fee_config t
            set `delete_flag`=#{item.deleteFlag}
            WHERE
            `id` = #{item.id}
        </foreach>
    </update>


</mapper>