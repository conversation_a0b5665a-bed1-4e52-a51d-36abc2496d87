package com.midea.logistics.otp.order.common.fegin;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.mideaframework.core.web.JsonResponse;

/** 
* @description:  
* @author: 陈永培
* @createtime: 2021/2/5 18:01 
*/ 
@FeignClient(value = "logistics-otp-ttx-inventory-service")
public interface TTXInventoryFeign {

    /**
     * 获取certification
     * @return
     */
    @PostMapping(value = "/ttx/inboundOrder/getCertification")
    JsonResponse<String> getCertification();
}
