package com.midea.logistics.imp.orderverify.rest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.request.CloudShtFeedBack;
import com.midea.logistics.otp.order.domain.request.MssFeedBack;
import com.midea.logistics.otp.order.domain.request.SCCFeedBack;
import com.midea.logistics.otp.order.domain.request.WfrFeedBack;
import com.mideaframework.core.web.JsonResponse;

import static com.midea.logistics.otp.enums.OrderOperateType.CARGO_RIGHT_TRANSFER;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: CargoRightTransferRest
 * @description:
 * @author: kongly1
 * @date: 2020-11-13 15:30
 */
@RestController
public class RepairDataRest {

    @Autowired
    private WfrTransferHelper wfrTransferHelper;
    @Autowired
    private ShtTransferHelper shtTransferHelper;
    @Autowired
    private MssTransferHelper mssTransferHelper;
    @Autowired
    private CloudShtTransferHelper cloudShtTransferHelper;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private CenterInvHelper centerInvHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    
    /**
    * !@修复 - !@无忧零售 -反馈上游总入口（修复数据）
    * @param: [wfrFeedBack]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2024/2/20 16:32
    */
    @PostMapping("/wfrFeedBackToScc")
    public JsonResponse wfrFeedBackToScc(@RequestBody WfrFeedBack wfrFeedBack) {
        String parentOrderNo = wfrFeedBack.getParentOrderNo();
        CommonConstant.check(parentOrderNo,"父单号不能为空");
        if(wfrFeedBack.isSuccess()){
            wfrTransferHelper.wfrSuccessResultToScc(parentOrderNo);
        }else{
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(parentOrderNo);
            FlowListenerParam result = new FlowListenerParam();
            String exceptionType = StringUtils.defaultIfBlank(wfrFeedBack.getExceptionType(),CARGO_RIGHT_TRANSFER.getKey());
            result.setExceptionType(exceptionType);
            result.setErrorMsg(wfrFeedBack.getMsg());
            wfrTransferHelper.wfrFailResultToScc(customerOrderInfoExt,result,exceptionType);
        }
        return JsonResponse.success("处理成功");
    }
    
    /**
    *!@修复 -  !@美云销OU -反馈上游总入口（修复数据）
    * @param: [wfrFeedBack]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2024/2/20 16:32
    */
    @PostMapping("/shtFeedBackToScc")
    public JsonResponse shtFeedBackToScc(@RequestBody SCCFeedBack feedBack) {
        String parentOrderNo = feedBack.getParentOrderNo();
        CommonConstant.check(parentOrderNo,"父单号不能为空");
        if(feedBack.isSuccess()){
            shtTransferHelper.shtSuccessResultToScc(parentOrderNo);
        }else{
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(parentOrderNo);
            FlowListenerParam result = new FlowListenerParam();
            String exceptionType = StringUtils.defaultIfBlank(feedBack.getExceptionType(),CARGO_RIGHT_TRANSFER.getKey());
            result.setExceptionType(exceptionType);
            result.setErrorMsg(feedBack.getMsg());
            shtTransferHelper.shtFailResultToScc(customerOrderInfoExt,result,exceptionType);
        }
        return JsonResponse.success("处理成功");
    }
    
    /**
    *!@修复 -  !@云仓OU -反馈上游总入口（修复数据）
    * @param: [wfrFeedBack]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2024/2/20 16:32
    */
    @PostMapping("/cloudShtFeedBackToMrp")
    public JsonResponse cloudShtFeedBackToMrp(@RequestBody CloudShtFeedBack feedBack) {
        String parentOrderNo = feedBack.getParentOrderNo();
        CommonConstant.check(parentOrderNo,"父单号不能为空");
        if(feedBack.isSuccess()){
            cloudShtTransferHelper.cloudSSuccessResultToMrp(parentOrderNo);
        }else{
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(parentOrderNo);
            FlowListenerParam result = new FlowListenerParam();
            String exceptionType = StringUtils.defaultIfBlank(feedBack.getExceptionType(),CARGO_RIGHT_TRANSFER.getKey());
            result.setExceptionType(exceptionType);
            result.setErrorMsg(feedBack.getMsg());
            cloudShtTransferHelper.cloudShtFailResultToMrp(customerOrderInfoExt,result,exceptionType);
        }
        return JsonResponse.success("处理成功");
    }
    
    /**
     * !@MSSOU -反馈上游总入口（修复数据）
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2024/2/20 16:32
     */
    @PostMapping("/shtFeedBackToMss")
    public JsonResponse shtFeedBackToMss(@RequestBody MssFeedBack feedBack) {
        String parentOrderNo = feedBack.getParentOrderNo();
        CommonConstant.check(parentOrderNo,"父单号不能为空");
        if(feedBack.isSuccess()){
            mssTransferHelper.shtSuccessResultToMss(parentOrderNo);
        }else{
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(parentOrderNo);
            FlowListenerParam result = new FlowListenerParam();
            String exceptionType = StringUtils.defaultIfBlank(feedBack.getExceptionType(),CARGO_RIGHT_TRANSFER.getKey());
            result.setExceptionType(exceptionType);
            result.setErrorMsg(feedBack.getMsg());
            mssTransferHelper.shtFailResultToMss(customerOrderInfoExt,result,exceptionType);
        }
        return JsonResponse.success("处理成功");
    }
    
    
    /**
     * !@取消库存占用 - 根据父单号取消所有子单的库存占用（修复数据）
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2024/2/20 16:32
     */
    @PostMapping("/cancelInvByOrderNoByParentOrderNo")
    public JsonResponse cancelInvByOrderNoByParentOrderNo(@RequestBody OrderInfo obj) {
        String parentOrderNo = obj.getParentOrderNo();
        //2025年3月7日10:30:44 进广：订单已经取消，但是没有取消库存预占，需要异步后延时处理 https://cf.annto.com/pages/viewpage.action?pageId=73140781
        centerInvHelper.cancelInvPreemptionSleepTwoSecond(parentOrderNo);
        return JsonResponse.success("处理成功");
    }
    
    /**
     * !@取消库存占用 - 根据子单号取消库存占用（修复数据）
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2024/2/20 16:32
     */
    @PostMapping("/cancelInvByOrderNo")
    public JsonResponse cancelInvByOrderNo(@RequestBody OrderInfo obj) {
        String orderNo = obj.getOrderNo();
        OrderInfoExt orderInfoExt = orderverifyHelper.getOrderInfoExt(orderNo);
        centerInvHelper.cancelInventoryPreemption(orderInfoExt);
        return JsonResponse.success("处理成功");
    }


}
