package com.midea.logistics.imp.orderverify.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.cache.manager.EbPlaceManager;
import com.midea.logistics.domain.mdm.domain.EbPlace;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.imp.orderverify.service.IAddressResolvingService;
import com.midea.logistics.otp.bean.AddressMapping;
import com.midea.logistics.otp.bean.GisDto;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.EbPlaceMdmFeign;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.bean.PlaceInfo;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderAddressFeign;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.flow.handle.FlowStarter;
import com.midea.logistics.otp.order.common.helper.AddressHelper;
import com.midea.logistics.otp.order.common.helper.DeliveryTypeHelper;
import com.midea.logistics.otp.order.common.helper.GisHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.sdk.helper.OkHttpCallHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: AddressResolvingManager
 * @author: crystal
 * @date: 2019/6/29 10:58
 * @description: 解析四级地址
 */
@Component
@Slf4j
public class AddressResolvingServiceImpl implements IAddressResolvingService {

    //省
    public static String PLACE_PROVINCE = "PLACE_PROVINCE";
    //市
    public static String PLACE_CITY = "PLACE_CITY";
    //区
    public static String PLACE_DISTRICT = "PLACE_DISTRICT";

    @Autowired
    private EbPlaceManager ebPlaceManager;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;
    @Autowired
    private FlowStarter flowStarter;
    @Autowired
    private OkHttpCallHelper okHttpCallHelper;
    @Autowired
    private EbPlaceMdmFeign ebPlaceMdmFeign;
    @Autowired
    private DeliveryTypeHelper deliveryTypeHelper;
    @Autowired
    private AddressHelper adressHelper;
    @Autowired
    private GisHelper gisHelper;

    @Autowired
    private OrderHelper orderHelper;

    private Logger logger = LoggerFactory.getLogger(AddressResolvingServiceImpl.class);

    /**
     * 1、收货单：IN ——> 解析收货地址，保存收货地址
     * 2、发货单：ON ——> 解析发货地址，保存发货地址
     * 3、运输单：YS ——> 解析收货、发货地址，保存收货、发货地址
     * B2B 改名称编码 B2C不改名称
     * 查询出数据，然后保存：
     * 1、获得地址解析接口返货的10位地址编码
     * 2、按规则截取：总共10位：1-3：省，4-5：市，6-7：县，8-10：区
     * 3、用截取到的code，分别去查询name
     * 4、然后分别保存
     * @param
     */
    /**
     * 解析地址，获得返回的countryCode,
     */
    private FlowListenerParam getCountryCode(CustomerOrderInfo customerOrderInfo, String address, boolean isReceiver) {
        if (StringUtils.isBlank(address)) {
            throw BusinessException.fail("做地址解析时，地址参数不能为空!");
        }
    
        try {
            GisDto gisDto = gisHelper.serchGisObject(address);
            return FlowListenerParam.success(gisDto.getCountryCode()+","+gisDto.getLng()+" "+gisDto.getLat());
        }catch (Exception e){
            if (isReceiver){
                return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED,"收货地址, " + e.getMessage());
            } else {
                return FlowListenerParam.fail(ExceptionType.SENDER_ADDRESS_FAILED,"发货地址, " + e.getMessage());
            }
        }
    }


    /**
     * 获取未停用的name
     */
    private String getAbleNameByCode(String code) {
        if (null == code) {
            throw BusinessException.fail("地址编码不能为空!");
        }
        EbPlace ebPlaceCache = ebPlaceManager.getEbPlaceCache(code);

        if (Objects.isNull(ebPlaceCache) || Objects.equals(ebPlaceCache.getEbplIsAble(), "DISABLE")) {
            return null;
        }
        return ebPlaceCache.getEbplNameCn();
    }
    private FlowListenerParam getAbleNameByName(String name,String ebplType, String parentCode) {
        if (null == name) {
            return null;
        }

        EbPlace ebPlace = new EbPlace();
        ebPlace.setEbplNameCn(name.substring(0,2));
        ebPlace.setEbplType(ebplType);
        ebPlace.setEbplIsAble("ENABLE");
        ebPlace.setEbplParentPmCode(parentCode);
        JsonResponse<List<EbPlace>> ebPlaceJsonResponse = ebPlaceMdmFeign.searchEbPlace(ebPlace);

        if (!"0".equals(ebPlaceJsonResponse.getCode())){
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "匹配标准库，查询接口异常.");
        }
        if(CollectionUtils.isEmpty(ebPlaceJsonResponse.getData())){
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "匹配标准库，查询接口返回空,地址为空");
        }

        EbPlace ebPlace1 = adressHelper.matchDistrictName(name, ebPlaceJsonResponse.getData()).getEbPlace();
        return FlowListenerParam.success(JSON.toJSONString(ebPlace1==null?ebPlaceJsonResponse.getData().get(0) : ebPlace1));
    }


    /**
     * 解析地址成功后，保存相应的数据
     */
    private FlowListenerParam setReceiverAddressCode(String countryCode, CustomerOrderAddress customerOrderAddress) {
        //省
        String province = countryCode.substring(0, 3);
        String provinceName = getAbleNameByCode(province);
        //市
        String city = countryCode.substring(0, 5);
        String cityName = getAbleNameByCode(city);
        //县
        String district = countryCode.substring(0, 7);
        String districtName = getAbleNameByCode(district);
        //..
        String townName = getAbleNameByCode(countryCode);


        if (StringUtils.isBlank(provinceName) ||StringUtils.isBlank(cityName)  ||StringUtils.isBlank(districtName)
            ||(StringUtils.isBlank(townName) && !CommonConstant.FLAG_YES.equals(customerOrderAddress.getPlanOrderFlag()))){
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "存在已经停用的地址编码！" );
        }

        customerOrderAddress.setReceiverProvinceCode(province);
        customerOrderAddress.setReceiverProvinceName(provinceName);
        customerOrderAddress.setReceiverCityCode(city);
        customerOrderAddress.setReceiverCityName(cityName);
        customerOrderAddress.setReceiverDistrictCode(district);
        customerOrderAddress.setReceiverDistrictName(districtName);
        customerOrderAddress.setReceiverTownCode(countryCode);
        customerOrderAddress.setReceiverTownName(townName);
        orderHelper.updateCustomerOrderAddress(customerOrderAddress);
        customerOrderAddress.setVersion(customerOrderAddress.getVersion()+1);
        return FlowListenerParam.success("保存成功");
    }


    /**
     * 解析地址成功后，保存相应的数据
     */
    private FlowListenerParam setSenderAddressCode( String countryCode, CustomerOrderAddress customerOrderAddress) {
        //省
        String province = countryCode.substring(0, 3);
        String provinceName = getAbleNameByCode(province);
        //市
        String city = countryCode.substring(0, 5);
        String cityName = getAbleNameByCode(city);
        //县
        String district = countryCode.substring(0, 7);
        String districtName = getAbleNameByCode(district);
        //..
        String townName = getAbleNameByCode(countryCode);

        if (StringUtils.isBlank(provinceName) ||StringUtils.isBlank(cityName)  ||StringUtils.isBlank(districtName)
            ||(StringUtils.isBlank(townName) && !CommonConstant.FLAG_YES.equals(customerOrderAddress.getPlanOrderFlag()))){
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "存在已经停用的地址编码！" );
        }

        customerOrderAddress.setSenderProvinceCode(province);
        customerOrderAddress.setSenderProvinceName(provinceName);
        customerOrderAddress.setSenderCityCode(city);
        customerOrderAddress.setSenderCityName(cityName);
        customerOrderAddress.setSenderDistrictCode(district);
        customerOrderAddress.setSenderDistrictName(districtName);
        customerOrderAddress.setSenderTownCode(countryCode);
        customerOrderAddress.setSenderTownName(townName);
        orderHelper.updateCustomerOrderAddress(customerOrderAddress);
        customerOrderAddress.setVersion(customerOrderAddress.getVersion()+1);

        return FlowListenerParam.success("保存成功");
    }

    private String getSenderAddress(CustomerOrderAddress customerOrderAddress) {
        String senderDetailAddr = customerOrderAddress.getSenderDetailAddr() == null ? "" : customerOrderAddress.getSenderDetailAddr().trim();

        String senderProvinceName = customerOrderAddress.getSenderProvinceName() == null ? "" : customerOrderAddress.getSenderProvinceName().trim();
        senderDetailAddr = gisHelper.fitKeyWord(senderDetailAddr, senderProvinceName);
        String senderCityName = customerOrderAddress.getSenderCityName() == null ? "" : customerOrderAddress.getSenderCityName().trim();
        senderDetailAddr = gisHelper.fitKeyWord(senderDetailAddr, senderCityName);
        String senderDistrictName = customerOrderAddress.getSenderDistrictName() == null ? "" : customerOrderAddress.getSenderDistrictName().trim();
        senderDetailAddr = gisHelper.fitKeyWord(senderDetailAddr, senderDistrictName);
        String senderTownName = customerOrderAddress.getSenderTownName() == null ? "" : customerOrderAddress.getSenderTownName().trim();
        senderDetailAddr = gisHelper.fitKeyWord(senderDetailAddr, senderTownName);
        String detailAddress = senderProvinceName + senderCityName + senderDistrictName + senderTownName + senderDetailAddr;
        return detailAddress;
    }

    private String getReceiverAddress(CustomerOrderAddress customerOrderAddress) {
        String receiverAddress = customerOrderAddress.getReceiverDetailAddr() == null ? "" : customerOrderAddress.getReceiverDetailAddr().trim();

        String receiverProvinceName = customerOrderAddress.getReceiverProvinceName() == null ? "" : customerOrderAddress.getReceiverProvinceName().trim();
        receiverAddress = gisHelper.fitKeyWord(receiverAddress, receiverProvinceName);
        String receiverCityName = customerOrderAddress.getReceiverCityName() == null ? "" : customerOrderAddress.getReceiverCityName().trim();
        receiverAddress = gisHelper.fitKeyWord(receiverAddress, receiverCityName);
        String receiverDistrictName = customerOrderAddress.getReceiverDistrictName() == null ? "" : customerOrderAddress.getReceiverDistrictName().trim();
        receiverAddress = gisHelper.fitKeyWord(receiverAddress, receiverDistrictName);
        String receiverTownName = customerOrderAddress.getReceiverTownName() == null ? "" : customerOrderAddress.getReceiverTownName().trim();
        receiverAddress = gisHelper.fitKeyWord(receiverAddress, receiverTownName);
        String detailAddress = receiverProvinceName + receiverCityName + receiverDistrictName + receiverTownName + receiverAddress;
        return detailAddress;
    }

    private String getNetAddress(CustomerOrderAddress customerOrderAddress) {
        String networkProvinceName = customerOrderAddress.getNetworkProvinceName() == null ? "" : customerOrderAddress.getNetworkProvinceName().trim();
        String networkCityName = customerOrderAddress.getNetworkCityName() == null ? "" : customerOrderAddress.getNetworkCityName().trim();
        String networkDistrictName = customerOrderAddress.getNetworkDistrictName() == null ? "" : customerOrderAddress.getNetworkDistrictName().trim();
        String networkTownName = customerOrderAddress.getNetworkTownName() == null ? "" : customerOrderAddress.getNetworkTownName().trim();

        String detailAddress = networkProvinceName + networkCityName + networkDistrictName + networkTownName;
        return detailAddress;
    }
    private void setNetAddressCode( CustomerOrderAddress customerOrderAddress) {

        String networkTownCode = customerOrderAddress.getNetworkTownCode();
        //省
        String province = networkTownCode.substring(0, 3);
        String provinceName = getAbleNameByCode(province);
        //市
        String city = networkTownCode.substring(0, 5);
        String cityName = getAbleNameByCode(city);
        //县
        String district = networkTownCode.substring(0, 7);
        String districtName = getAbleNameByCode(district);
        //..
        String townName = getAbleNameByCode(networkTownCode);

        customerOrderAddress.setNetworkProvinceName(provinceName);
        customerOrderAddress.setNetworkProvinceCode(province);
        customerOrderAddress.setNetworkCityName(cityName);
        customerOrderAddress.setNetworkCityCode(city);
        customerOrderAddress.setNetworkDistrictName(districtName);
        customerOrderAddress.setNetworkDistrictCode(district);
        customerOrderAddress.setNetworkTownName(townName);
        customerOrderAddress.setNetworkTownCode(networkTownCode);
    }
    private void setNetAddressCode(String countryCode , CustomerOrderAddress customerOrderAddress) {
        //省
        String province = countryCode.substring(0, 3);
        String provinceName = getAbleNameByCode(province);
        //市
        String city = countryCode.substring(0, 5);
        String cityName = getAbleNameByCode(city);
        //县
        String district = countryCode.substring(0, 7);
        String districtName = getAbleNameByCode(district);
        //..
        String townName = getAbleNameByCode(countryCode);


        customerOrderAddress.setNetworkProvinceName(provinceName);
        customerOrderAddress.setNetworkProvinceCode(province);
        customerOrderAddress.setNetworkCityName(cityName);
        customerOrderAddress.setNetworkCityCode(city);
        customerOrderAddress.setNetworkDistrictName(districtName);
        customerOrderAddress.setNetworkDistrictCode(district);
        customerOrderAddress.setNetworkTownName(townName);
        customerOrderAddress.setNetworkTownCode(countryCode);
    }

    private FlowListenerParam getCountryCode(String address) {
        if (StringUtils.isBlank(address)) {
            throw BusinessException.fail("做地址解析时，地址参数不能为空!");
        }
        try {
            GisDto gisDto = gisHelper.serchGisObject(address);
            return FlowListenerParam.success(gisDto.getCountryCode());
        }catch (Exception e){
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED,"网点" + e.getMessage());
        }
    }
    
    private void setEndLngLat(String lnglats , CustomerOrderAddress customerOrderAddress){
        String[] lnglat = lnglats.split("\\s");
        if (!Objects.equals(lnglat.length,2)){
            throw BusinessException.fail("地址解析接口，返回异常数据");
        }
        BigDecimal lng = new BigDecimal(lnglat[0]);
        BigDecimal lat = new BigDecimal(lnglat[1]);
        customerOrderAddress.setEndLat(lat);
        customerOrderAddress.setEndLng(lng);
    }
    private void setStartLngLat(String lnglats , CustomerOrderAddress customerOrderAddress){
        String[] lnglat = lnglats.split("\\s");
        if (!Objects.equals(lnglat.length,2)){
            throw BusinessException.fail("地址解析接口，返回异常数据");
        }
        BigDecimal lng = new BigDecimal(lnglat[0]);
        BigDecimal lat = new BigDecimal(lnglat[1]);
        customerOrderAddress.setStartLat(lat);
        customerOrderAddress.setStartLng(lng);
    }

    /**
     * 解析地址，获得返回的经纬度,
     */
    private FlowListenerParam getLnglat(String address, boolean isReceiver) {
    
        if (StringUtils.isBlank(address)) {
            throw BusinessException.fail("做地址解析时，地址参数不能为空!");
        }
    
        try {
            GisDto gisDto = gisHelper.serchGisObject(address);
            return FlowListenerParam.success(gisDto.getLng()+" "+gisDto.getLat());
        }catch (Exception e){
            if (isReceiver){
                return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED,"收货" + e.getMessage());
            } else {
                return FlowListenerParam.fail(ExceptionType.SENDER_ADDRESS_FAILED,"发货" + e.getMessage());
            }
        }
    }


    @Override
    @ZeebeFlow("ADDRESS_ANALYSIS")
    public CustomerOrderInfoExt queryForCountry(CustomerOrderInfoExt customerOrderInfo) {

        CommonConstant.checkOrderInfo(customerOrderInfo);
        String orderNo = customerOrderInfo.getOrderNo();

        String deliveryType = customerOrderInfo.getDeliveryType();
        OrderType orderType = OrderType.valueOf(customerOrderInfo.getOrderType());
        BusinessMode businessMode = BusinessMode.valueOf(customerOrderInfo.getBusinessMode());

        if (StringUtils.isNotBlank(deliveryType) && DeliveryType.ZT.getKey().equals(deliveryType)) {
            FlowListenerParam.success("");
            return customerOrderInfo;
        }

        // inOutType = INOUT 存在三种情况，只要管 TS
        if (OrderType.RDO == orderType){
            FlowListenerParam.success(orderType.getValue() + "无需解析");
            return customerOrderInfo;
        }

        CustomerOrderAddress qcustomerOrderAddress = customerOrderInfo.getCustomerOrderAddress();
        if (Objects.isNull(qcustomerOrderAddress)) {
            throw BusinessException.fail("根据该orderNo，未查询出地址!");
        }

        logger.info("单号："+orderNo+"初始地址："+JSON.toJSONString(qcustomerOrderAddress));
        qcustomerOrderAddress.setPlanOrderFlag(customerOrderInfo.getPlanOrderFlag());
        //如果是dot，则需要解析网点地址
        if ((OrderType.DO == orderType && DeliveryType.DOT.getKey().equals(customerOrderInfo.getDeliveryType()))
            || ProjectClassifyEnum.PO_GS_01.getKey().equals(customerOrderInfo.getUpperOrderType())){

            //如果code存在且有效，根据code查询出name即可
            if (StringUtils.isNotBlank(qcustomerOrderAddress.getNetworkTownCode()) && Objects.equals(qcustomerOrderAddress.getNetworkTownCode().length(),10)){
                setNetAddressCode(qcustomerOrderAddress);

                if (StringUtils.isBlank(qcustomerOrderAddress.getNetworkProvinceName()) || StringUtils.isBlank(qcustomerOrderAddress.getNetworkCityName())
                    || StringUtils.isBlank(qcustomerOrderAddress.getNetworkDistrictName()) || StringUtils.isBlank(qcustomerOrderAddress.getNetworkTownName())){
                    throw BusinessException.fail("此网点地址编码存在停用地址:"+qcustomerOrderAddress.getNetworkTownCode());
                }

            }else {
                //如果code不存在，去解析获取code
                String netAddress = getNetAddress(qcustomerOrderAddress);
                FlowListenerParam senderCountryCode =getCountryCode(netAddress);
                if (FlowStatus.SUCCESS != senderCountryCode.getFlowStatus()) {
                    throw BusinessException.fail(senderCountryCode.getErrorMsg());
                }

                compareNetworkAddress(senderCountryCode.getErrorMsg(),  qcustomerOrderAddress,customerOrderInfo.getSourceSystem());

                setNetAddressCode(senderCountryCode.getErrorMsg(),qcustomerOrderAddress);
            }
        }


        if (StringUtils.isBlank(customerOrderInfo.getInOutType())) {
            throw BusinessException.fail("解析出入库类型为空,无法进行解析");
        }

        String inOutType = customerOrderInfo.getInOutType();
        InOutType ioTypeEnum = EnumUtils.getEnum(InOutType.class, inOutType);


        if (InOutType.OUT == ioTypeEnum && StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownCode())
            && StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownName())
            && !Objects.isNull(qcustomerOrderAddress.getEndLat())
            && !Objects.isNull(qcustomerOrderAddress.getEndLng())) {
            FlowListenerParam.success("收货地址已存在,无需解析");
            return customerOrderInfo;
        }

        if (InOutType.IN == ioTypeEnum && StringUtils.isNotBlank(qcustomerOrderAddress.getSenderTownCode())
            && StringUtils.isNotBlank(qcustomerOrderAddress.getSenderTownName())
            && !Objects.isNull(qcustomerOrderAddress.getStartLat())
            && !Objects.isNull(qcustomerOrderAddress.getStartLng())) {
            FlowListenerParam.success("发货地址已存在,无需解析");
            return customerOrderInfo;
        }

        if ((InOutType.YS == ioTypeEnum ||  InOutType.INOUT == ioTypeEnum)
            && StringUtils.isNotBlank(qcustomerOrderAddress.getSenderTownCode())
            && StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownCode())
            && StringUtils.isNotBlank(qcustomerOrderAddress.getSenderTownName())
            && StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownName())
            && !Objects.isNull(qcustomerOrderAddress.getStartLat())
            && !Objects.isNull(qcustomerOrderAddress.getStartLng())
            && !Objects.isNull(qcustomerOrderAddress.getEndLat())
            && !Objects.isNull(qcustomerOrderAddress.getEndLng())) {
            FlowListenerParam.success("发货和收货地址均存在,无需解析");
            return customerOrderInfo;
        }

        //解析 receiver

        //分拨单
        if (OrderType.DO == orderType){

            if (StringUtils.isEmpty(qcustomerOrderAddress.getReceiverDetailAddr())){
                throw BusinessException.fail("详细地址不能为空,无法走地址解析流程!");
            }

            if (StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownCode())
                && Objects.equals(qcustomerOrderAddress.getReceiverTownCode().length(),10)
                && StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownName())
                && !Objects.isNull(qcustomerOrderAddress.getEndLat()) && !Objects.isNull(qcustomerOrderAddress.getEndLng())){
                //将前面的网点地址保存
                orderHelper.updateCustomerOrderAddress(qcustomerOrderAddress);
                customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                FlowListenerParam.success("地址：" + getReceiverAddress(qcustomerOrderAddress));
                return customerOrderInfo;
            }else {
                if (StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownCode()) && Objects.equals(qcustomerOrderAddress.getReceiverTownCode().length(),10)
                    && !Objects.isNull(qcustomerOrderAddress.getEndLat()) && !Objects.isNull(qcustomerOrderAddress.getEndLng())){
                    FlowListenerParam flowListenerParam = setReceiverAddressCode(qcustomerOrderAddress.getReceiverTownCode(), qcustomerOrderAddress);

                    if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                        throw BusinessException.fail(flowListenerParam.getErrorMsg()+",orderType:"+orderType.getValue());
                    }

                    if (StringUtils.isBlank(qcustomerOrderAddress.getReceiverProvinceName()) || StringUtils.isBlank(qcustomerOrderAddress.getReceiverCityName())
                        || StringUtils.isBlank(qcustomerOrderAddress.getReceiverDistrictName()) || StringUtils.isBlank(qcustomerOrderAddress.getReceiverTownName())){
                        throw BusinessException.fail("此收货地址编码存在停用地址:"+qcustomerOrderAddress.getReceiverTownCode());
                    }

                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("地址：" + getReceiverAddress(qcustomerOrderAddress));
                    return customerOrderInfo;

                }else {

                    //如果存在地址，则只update 经纬度
                    if (StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverTownCode()) && Objects.equals(qcustomerOrderAddress.getReceiverTownCode().length(),10)
                        && Objects.isNull(qcustomerOrderAddress.getEndLat()) && Objects.isNull(qcustomerOrderAddress.getEndLng())){

                        String receiverAddress = getReceiverAddress(qcustomerOrderAddress);
                        FlowListenerParam lnglat = getLnglat(receiverAddress, true);

                        if (FlowStatus.SUCCESS == lnglat.getFlowStatus()) {
                            String lnglats = lnglat.getErrorMsg();
                            setEndLngLat(lnglats , qcustomerOrderAddress);
                        }

                        FlowListenerParam flowListenerParam = setReceiverAddressCode(qcustomerOrderAddress.getReceiverTownCode(), qcustomerOrderAddress);
                        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                            throw BusinessException.fail(flowListenerParam.getErrorMsg()+",orderType:"+orderType.getValue());
                        }

                        if (StringUtils.isBlank(qcustomerOrderAddress.getReceiverProvinceName()) || StringUtils.isBlank(qcustomerOrderAddress.getReceiverCityName())
                            || StringUtils.isBlank(qcustomerOrderAddress.getReceiverDistrictName()) || StringUtils.isBlank(qcustomerOrderAddress.getReceiverTownName())){
                            throw BusinessException.fail("此收货地址编码存在停用地址:"+qcustomerOrderAddress.getReceiverTownCode());
                        }

                        customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                        FlowListenerParam.success("地址：" + receiverAddress);
                        return customerOrderInfo;
                    }

                    String receiverAddress = getReceiverAddress(qcustomerOrderAddress);
                    FlowListenerParam receiverCountryCode = getCountryCode(customerOrderInfo, receiverAddress, true);
                    if (FlowStatus.SUCCESS != receiverCountryCode.getFlowStatus()) {
                        throw BusinessException.fail(receiverCountryCode.getErrorMsg());
                    }
                    String errorMsg = receiverCountryCode.getErrorMsg();
                    String[] split = errorMsg.split(",");
                    if (!Objects.equals(split.length,2)){
                        throw BusinessException.fail( "地址解析接口，返回异常数据");
                    }
                    String contryCode = split[0];
                    String lnglats = split[1];

                    compareReceiverAddress(contryCode,  qcustomerOrderAddress,customerOrderInfo.getSourceSystem());

                    setEndLngLat(lnglats , qcustomerOrderAddress);
                    FlowListenerParam flowListenerParam = setReceiverAddressCode(contryCode, qcustomerOrderAddress);
                    if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                        throw BusinessException.fail(flowListenerParam.getErrorMsg()+",orderType:"+orderType.getValue());
                    }
                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("地址：" + receiverAddress);
                    return customerOrderInfo;
                }
            }
        }

        /**
         * CCS,CIMS  需要判断四级地址是否还有效
         */


        if (!(InOutType.IN == ioTypeEnum || InOutType.OUT == ioTypeEnum || InOutType.YS == ioTypeEnum || InOutType.INOUT == ioTypeEnum)) {
            throw BusinessException.fail("只能接收单据类型为：OUT,IN,YS,INOUT的单!");
        }


        String sourceSystem = customerOrderInfo.getSourceSystem();

        /**
         * CIMS,CSS
         */
        if (SourceSystem.CIMS.getKey().equals(sourceSystem) || SourceSystem.CCS.getKey().equals(sourceSystem)) {

            if (InOutType.OUT == ioTypeEnum || InOutType.YS == ioTypeEnum) {

                if (StringUtils.isEmpty(qcustomerOrderAddress.getReceiverDetailAddr())) {
                    throw BusinessException.fail("详细地址不能为空,无法走地址解析流程!");
                }

                //第四级不为null，并且等于10位，查基础数据，查到则成功，查不到则纠错
                String sourceTownCode = qcustomerOrderAddress.getReceiverTownCode();
                String sourceDistrictCode = qcustomerOrderAddress.getReceiverDistrictCode();
                if (StringUtils.isNotBlank(sourceTownCode) && Objects.equals(sourceTownCode.length(), 10)
                    || (CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag()) && StringUtils.isNotBlank(sourceDistrictCode))) {

                    String sourceTownName = null;
                    if(CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag())){
                        sourceTownName = getAbleNameByCode(sourceDistrictCode);
                    }else {
                        sourceTownName = getAbleNameByCode(sourceTownCode);
                    }

                    if (StringUtils.isBlank(sourceTownName)) {
                        throw BusinessException.fail("地址已经被停用或者编码无效："+sourceTownName);
                    }

                    //经纬度
                    if (Objects.isNull(qcustomerOrderAddress.getEndLat()) || Objects.isNull(qcustomerOrderAddress.getEndLng())) {

                        String receiverAddress = getReceiverAddress(qcustomerOrderAddress);

                        FlowListenerParam lnglat = getLnglat(receiverAddress, false);

                        if (FlowStatus.SUCCESS == lnglat.getFlowStatus()) {

                            String lnglats = lnglat.getErrorMsg();

                            setEndLngLat(lnglats, qcustomerOrderAddress);
                        }
                    }

                    if (SourceSystem.CIMS.getKey().equals(sourceSystem)) {

                        FlowListenerParam flowListenerParam = setReceiverAddressCode(sourceTownCode, qcustomerOrderAddress);

                        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {

                            throw BusinessException.fail(flowListenerParam.getErrorMsg()+",来源系统:"+sourceSystem);
                        }

                    } else {
                        //校验一下三个编码是否匹配
                        String receiverProvinceCode = qcustomerOrderAddress.getReceiverProvinceCode();
                        String receiverCityCode = qcustomerOrderAddress.getReceiverCityCode();
                        String receiverDistrictCode = qcustomerOrderAddress.getReceiverDistrictCode();
                        if (!receiverDistrictCode.equals(sourceTownCode.substring(0,7))) {
                            throw BusinessException.fail("地址不匹配"+",来源系统:"+sourceSystem);
                        }
                        FlowListenerParam flowListenerParam = setReceiverAddressCode(sourceTownCode, qcustomerOrderAddress);

                        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {

                            throw BusinessException.fail(flowListenerParam.getErrorMsg()+",来源系统:"+sourceSystem);
                        }
                    }

                    //                    return FlowListenerParam.success("解析成功，解析地址为：" + sourceTownName + ",来源系统：" + sourceSystem);
                }else {
                    //第四级==null,或者第四级长度等于7=   -------------------------调用gis

                    String receiverAddress;

                    if (SourceSystem.CCS.getKey().equals(sourceSystem)) {
                        receiverAddress=qcustomerOrderAddress.getReceiverDetailAddr();
                    }else {
                        receiverAddress = getReceiverAddress(qcustomerOrderAddress);
                    }

                    FlowListenerParam receiverCountryCode = getCountryCode(customerOrderInfo, receiverAddress, true);

                    if (FlowStatus.SUCCESS != receiverCountryCode.getFlowStatus()) {
                        throw BusinessException.fail(receiverCountryCode.getErrorMsg());
                    }
                    String errorMsg = receiverCountryCode.getErrorMsg();
                    String[] split = errorMsg.split(",");
                    if (!Objects.equals(split.length, 2)) {
                        throw BusinessException.fail( "地址解析接口，返回异常数据");
                    }
                    String contryCode = split[0];
                    String lnglats = split[1];

                    if (StringUtils.isBlank(contryCode) || !Objects.equals(contryCode.length(), 10)) {

                        throw BusinessException.fail("地址解析返回异常编码：" + (contryCode == null ? "null" : contryCode));
                    }

                    //1、CCS 第一二三级存在，
                    if (SourceSystem.CCS.getKey().equals(sourceSystem)) {
                        String receiverProvinceCode = qcustomerOrderAddress.getReceiverProvinceCode();
                        String receiverCityCode = qcustomerOrderAddress.getReceiverCityCode();
                        String receiverDistrictCode = qcustomerOrderAddress.getReceiverDistrictCode();

                        if (StringUtils.isNotBlank(receiverDistrictCode) && !receiverDistrictCode.equals(contryCode.substring(0, 7))) {

                            throw BusinessException.fail( "地址编码不互相匹配，或解析编码:" + contryCode + "和原编码不匹配：" + receiverDistrictCode);
                        }
                        setEndLngLat(lnglats, qcustomerOrderAddress);
                        FlowListenerParam flowListenerParam = setReceiverAddressCode(contryCode, qcustomerOrderAddress);

                        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {

                            throw BusinessException.fail(flowListenerParam.getErrorMsg()+",来源系统:"+sourceSystem);
                        }

                        //                        return FlowListenerParam.success("解析成功，解析地址编码为：" + contryCode + "来源系统：" + sourceSystem);
                    } else {
                        //2、CIMS 只有一个7位的编码

                        if (StringUtils.isBlank(sourceTownCode) || !Objects.equals(sourceTownCode.length(), 7)) {
                            throw BusinessException.fail("地址编码有误，来源系统：" + sourceSystem);
                        }

                        if (!sourceTownCode.equals(contryCode.substring(0, 7))) {
                            throw BusinessException.fail("解析编码：" + contryCode + "和原编码:" + sourceTownCode + "不匹配，来源系统:" + sourceSystem);
                        }

                        setEndLngLat(lnglats, qcustomerOrderAddress);
                        FlowListenerParam flowListenerParam = setReceiverAddressCode(contryCode, qcustomerOrderAddress);

                        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {

                            throw BusinessException.fail(flowListenerParam.getErrorMsg()+",来源系统:"+sourceSystem);
                        }

                    }
                }
            }


            if (InOutType.IN == ioTypeEnum || InOutType.YS == ioTypeEnum) {

                if (StringUtils.isEmpty(qcustomerOrderAddress.getSenderDetailAddr())) {
                    throw BusinessException.fail("详细地址不能为空,无法走地址解析流程!");
                }

                //第四级不为null，并且等于10位，查基础数据，查到则成功，查不到则纠错
                String sourceTownCode = qcustomerOrderAddress.getSenderTownCode();
                String sourceDistrictCode = qcustomerOrderAddress.getSenderDistrictCode();
                if (StringUtils.isNotBlank(sourceTownCode) && Objects.equals(sourceTownCode.length(), 10)
                    || (CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag()) && StringUtils.isNotBlank(sourceDistrictCode))) {

                    String sourceTownName = null;
                    if(CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag())){
                        sourceTownName = getAbleNameByCode(sourceDistrictCode);
                    }else {
                        sourceTownName = getAbleNameByCode(sourceTownCode);
                    }
                    if (StringUtils.isBlank(sourceTownName)) {
                        throw BusinessException.fail( "地址已经被停用或者编码无效!");
                    }

                    //经纬度
                    if (Objects.isNull(qcustomerOrderAddress.getStartLat()) || Objects.isNull(qcustomerOrderAddress.getStartLng())) {
                        String senderAddress = getSenderAddress(qcustomerOrderAddress);
                        FlowListenerParam lnglat = getLnglat(senderAddress, false);
                        if (FlowStatus.SUCCESS == lnglat.getFlowStatus()) {
                            String lnglats = lnglat.getErrorMsg();
                            setStartLngLat(lnglats, qcustomerOrderAddress);
                        }
                    }

                    if (SourceSystem.CIMS.getKey().equals(sourceSystem)) {
                        FlowListenerParam flowListenerParam = setSenderAddressCode(sourceTownCode, qcustomerOrderAddress);
                        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                            throw BusinessException.fail(flowListenerParam.getErrorMsg() + ",来源系统:" + sourceSystem);
                        }

                    } else {
                        //校验一下三个编码是否匹配
                        String senderProvinceCode = qcustomerOrderAddress.getSenderProvinceCode();
                        String senderCityCode = qcustomerOrderAddress.getSenderCityCode();
                        String senderDistrictCode = qcustomerOrderAddress.getSenderDistrictCode();

                        if (StringUtils.isEmpty(senderDistrictCode)) {
                            throw BusinessException.fail("发货区Code为空，请检查");
                        }

                        if (!senderDistrictCode.equals(sourceTownCode.substring(0,7))) {
                            throw BusinessException.fail("地址不匹配");
                        }

                        FlowListenerParam flowListenerParam = setSenderAddressCode(sourceTownCode, qcustomerOrderAddress);
                        if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                            throw BusinessException.fail(flowListenerParam.getErrorMsg() + ",来源系统:" + sourceSystem);
                        }

                    }
                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("");
                    return customerOrderInfo;
                }

                //第四级==null,或者第四级长度等于7=   -------------------------调用gis

                String senderAddress;
                if (SourceSystem.CCS.getKey().equals(sourceSystem)) {
                    senderAddress=qcustomerOrderAddress.getSenderDetailAddr();
                }else {
                    senderAddress = getSenderAddress(qcustomerOrderAddress);
                }

                FlowListenerParam senderCountryCode = getCountryCode(customerOrderInfo, senderAddress, true);

                if (FlowStatus.SUCCESS != senderCountryCode.getFlowStatus()) {
                    throw BusinessException.fail(senderCountryCode.getErrorMsg()+"地址："+senderAddress);
                }

                String errorMsg = senderCountryCode.getErrorMsg();
                String[] split = errorMsg.split(",");
                if (!Objects.equals(split.length, 2)) {
                    throw BusinessException.fail("地址解析接口，返回异常数据");
                }
                String contryCode = split[0];
                String lnglats = split[1];

                if (StringUtils.isBlank(contryCode) || !Objects.equals(contryCode.length(), 10)) {
                    throw BusinessException.fail("地址解析返回异常编码：" + (contryCode == null ? null : contryCode));
                }

                //1、CCS 第一二三级存在，
                if (SourceSystem.CCS.getKey().equals(sourceSystem)) {

                    String senderProvinceCode = qcustomerOrderAddress.getSenderProvinceCode();
                    String senderCityCode = qcustomerOrderAddress.getSenderCityCode();
                    String senderDistrictCode = qcustomerOrderAddress.getSenderDistrictCode();

                    if (!senderDistrictCode.equals(contryCode.substring(0, 7))) {
                        throw BusinessException.fail("地址编码不互相匹配，或解析编码:" + contryCode + "和原编码不匹配：" + senderDistrictCode);
                    }

                    setStartLngLat(lnglats, qcustomerOrderAddress);
                    FlowListenerParam flowListenerParam = setSenderAddressCode(contryCode, qcustomerOrderAddress);

                    if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                        throw BusinessException.fail(flowListenerParam.getErrorMsg()+"编码："+contryCode);
                    }

                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("解析成功，解析地址编码为：" + contryCode + "来源系统：" + sourceSystem);
                    return customerOrderInfo;

                } else {

                    //2、CIMS 只有一个7位的编码

                    if (StringUtils.isBlank(sourceTownCode) || !Objects.equals(sourceTownCode.length(), 7)) {
                        throw BusinessException.fail("地址编码有误，来源系统：" + sourceSystem);
                    }

                    if (!sourceTownCode.equals(contryCode.substring(0, 7))) {
                        throw BusinessException.fail("解析编码：" + contryCode + "和原编码:" + sourceTownCode + "不匹配，来源系统:" + sourceSystem);
                    }
                    setStartLngLat(lnglats, qcustomerOrderAddress);

                    FlowListenerParam flowListenerParam = setSenderAddressCode(contryCode, qcustomerOrderAddress);

                    if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                        throw BusinessException.fail(flowListenerParam.getErrorMsg()+"编码："+contryCode);
                    }

                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("解析成功，解析地址编码为：" + contryCode + "来源系统：" + sourceSystem);
                    return customerOrderInfo;
                }
            }
        }

        /**
         * PDD,CAINIAO,ECM,QiMen,FuRun
         */
        else if (SourceSystem.PDD.getKey().equals(sourceSystem) || SourceSystem.CAINIAO.getKey().equals(sourceSystem)
            || SourceSystem.ECM.getKey().equals(sourceSystem) || SourceSystem.QIMEN.getKey().equals(sourceSystem) || SourceSystem.FURUN.getKey().equals(sourceSystem)){

            if (OrderType.PO == orderType || OrderType.DP == orderType || OrderType.AO == orderType || OrderType.RO == orderType  || OrderType.YS == orderType){

                log.info("AddressResolvingServiceImpl->orderNo:{}->B2C地址解析逻辑开始(#^.^#)->qcustomerOrderAddress:{}",orderNo,JSON.toJSONString(qcustomerOrderAddress));

                //2020-3-19 14:29:43 ：李娟 纯运输订单需要解析四级地址
                //2020-4-22 08:50:02 ：泓铄 DPRI和DP也需要解析发货地址
                if(OrderType.YS == orderType || OrderType.DPRI == orderType  || OrderType.DP == orderType ) {
                    saveSenderAddressByInType(customerOrderInfo, qcustomerOrderAddress, sourceSystem);
                }

                String receiverDetailAddr = qcustomerOrderAddress.getReceiverDetailAddr();
                String receiverProvinceName = qcustomerOrderAddress.getReceiverProvinceName();
                String receiverCityName = qcustomerOrderAddress.getReceiverCityName();
                String receiverDistrictName = qcustomerOrderAddress.getReceiverDistrictName();

                //检查四级地址，如果有异常数据，直接hold住
                FlowListenerParam checkReceiveAddress = adressHelper.checkReceiveAddress(qcustomerOrderAddress);
                if (null != checkReceiveAddress) {
                    throw BusinessException.fail(checkReceiveAddress.getErrorMsg());
                }


                //1.根据接单的省查询标准库，然后设置省Code
                EbPlace provincePlace = adressHelper.matchMDMStandard(receiverProvinceName, PLACE_PROVINCE,null);
                if (null == provincePlace){
                    throw BusinessException.fail("根据接单的收货省地址["+receiverProvinceName+"]，匹配市标准库，查询接口为空");
                }
                log.info("AddressResolvingServiceImpl->orderNo:{}->provincePlace,解析receiverProvinceCode:["+provincePlace.getEbplCode()+"],receiverProvinceName:["+provincePlace.getEbplNameCn()+"]",orderNo);
                qcustomerOrderAddress.setReceiverProvinceCode(provincePlace.getEbplCode());
                qcustomerOrderAddress.setReceiverProvinceName(provincePlace.getEbplNameCn());

                //市映射表
                Map<String,String> cityMap = AddressMapping.cityMap;
                //区
                Map<String,String> distinctMap = AddressMapping.distinctMap;

                String cityName = cityMap.get(receiverCityName);
                String whCode = customerOrderInfo.getWhCode();

                boolean isSmall = StringUtils.isNotEmpty(whCode) &&  CommonEnum.YES.getValue().equals(deliveryTypeHelper.getOutCollabWh(whCode));
                //-------------------------------------------------2020-4-29 09:47:00 重构代码部分 begin--------------------------------------------------------------------//
                //小电 && B2C
                if (isSmall && BusinessMode.B2C == businessMode){
                    //2.市映射
                    if(StringUtils.isNotBlank(cityName)){
                        String[] split = cityName.split("-");
                        //市
                        String city = split[0];
                        //区
                        String district = split[1];
                        qcustomerOrderAddress.setReceiverCityName(city);
                        qcustomerOrderAddress.setReceiverDistrictName(district);
                    }

                    //3.区映射
                    String districtName = distinctMap.get(receiverCityName+"-"+receiverDistrictName);
                    if (StringUtils.isNotBlank(districtName)){
                        qcustomerOrderAddress.setReceiverDistrictName(districtName);
                    }

                    //区中替换市的情况
                    if(StringUtils.isNotBlank(districtName)){
                        String[] split = districtName.split("-");
                        if(split!=null && split.length == 2){
                            //市
                            String cityNew = split[0];
                            //区
                            String districtNew = split[1];
                            qcustomerOrderAddress.setReceiverCityName(cityNew);
                            qcustomerOrderAddress.setReceiverDistrictName(districtNew);
                        }
                    }

                    log.info("AddressResolvingServiceImpl->orderNo:{}->SmallAddressStart,receiverCityName:{},receiverDistrictName:{}小电逻辑开始(#^.^#)",orderNo,qcustomerOrderAddress.getReceiverCityName(),qcustomerOrderAddress.getReceiverDistrictName());

                    //4.根据市前2位，匹配标准库
                    PlaceInfo cityPlaceInfo = adressHelper.matchMoreMDMStandard(qcustomerOrderAddress.getReceiverCityName(), PLACE_CITY,qcustomerOrderAddress.getReceiverProvinceCode());
                    log.info("AddressResolvingServiceImpl->orderNo:{}->SmallAddress->cityPlaceInfo:{}",orderNo,JSON.toJSONString(cityPlaceInfo));
                    //没有匹配到市，阻断，因为计费需要市编码
                    if (null == cityPlaceInfo){
                        throw BusinessException.fail("小电收货市地址:["+qcustomerOrderAddress.getReceiverCityName()+"]，匹配标准库为空，无法找到市编码");
                    }
                    //默认设置最匹配的一个
                    qcustomerOrderAddress.setReceiverCityName(cityPlaceInfo.getClosestPlace().getEbplNameCn());
                    qcustomerOrderAddress.setReceiverCityCode(cityPlaceInfo.getClosestPlace().getEbplCode());

                    //5.根据区，循环匹配市，推导出最终的市和区
                    FlowListenerParam smallDistrictParam = setSmallDistrictName(orderNo, deliveryType,qcustomerOrderAddress, cityPlaceInfo);
                    log.info("AddressResolvingServiceImpl->orderNo:{}->SmallAddress->smallDistrictParam:{}",orderNo,JSON.toJSONString(smallDistrictParam));
                    if (null != smallDistrictParam) {
                        throw BusinessException.fail(smallDistrictParam.getErrorMsg());
                    }

                    orderHelper.updateCustomerOrderAddress( qcustomerOrderAddress);
                    qcustomerOrderAddress.setVersion(qcustomerOrderAddress.getVersion()+1);
                    log.info("AddressResolvingServiceImpl->orderNo:{}->SmallAddressEnd,小电逻辑结束(#^.^#)",orderNo);
                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);

                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("地图解析："+getReceiverAddress(qcustomerOrderAddress));
                    return customerOrderInfo;
                }
                //-------------------------------------------------2020-4-29 09:47:00 重构代码部分 end--------------------------------------------------------------------//

                //大电
                //-------------------------------------------------2020-4-23 15:58:41 重构代码部分 begin--------------------------------------------------------------------//
                log.info("AddressResolvingServiceImpl->orderNo:{}->BigAddressStart,大电逻辑开始(#^.^#)",orderNo);
                //记录多个市
                List<EbPlace> cityPlaces = new ArrayList();

                //2.找到city映射
                if(StringUtils.isNotBlank(cityName)){
                    String[] split = cityName.split("-");
                    //市
                    String city = split[0];
                    //区
                    String district = split[1];

                    //1.根据市去匹配标准库
                    EbPlace cityPlace = adressHelper.matchMDMStandard(city, PLACE_CITY,provincePlace.getEbplCode());
                    log.info("AddressResolvingServiceImpl->orderNo:{}->cityMap->cityPlace:{}",orderNo,cityPlace);
                    if (null == cityPlace){
                        throw BusinessException.fail("订单的收货市地址：["+receiverCityName+"]，根据市映射表市地址：["+city+"]，省编码：["+provincePlace.getEbplCode()+"]，匹配市标准库，查询接口为空");
                    }
                    qcustomerOrderAddress.setReceiverCityName(cityPlace.getEbplNameCn());
                    qcustomerOrderAddress.setReceiverCityCode(cityPlace.getEbplCode());

                    //2.根据区去匹配标准库
                    EbPlace districtPlace = adressHelper.matchMDMStandard(district, PLACE_DISTRICT,cityPlace.getEbplCode());
                    log.info("AddressResolvingServiceImpl->orderNo:{}->cityMap->districtPlace:{}",orderNo,districtPlace);
                    if (null == districtPlace){
                        throw BusinessException.fail("订单的收货区地址：["+receiverDistrictName+"]，根据市映射表区地址：["+district+"]，市编码：["+cityPlace.getEbplCode()+"]，匹配区标准库，查询接口为空");
                    }
                    qcustomerOrderAddress.setReceiverDistrictName(districtPlace.getEbplNameCn());
                    qcustomerOrderAddress.setReceiverDistrictCode(districtPlace.getEbplCode());

                    //3.无city映射
                }else{
                    //根据下单市前2位，匹配标准库
                    PlaceInfo cityPlaceInfo = adressHelper.matchMoreMDMStandard(receiverCityName, PLACE_CITY,qcustomerOrderAddress.getReceiverProvinceCode());
                    log.info("AddressResolvingServiceImpl->orderNo:{}->notCityMap->cityPlaceInfo:{}",orderNo,JSON.toJSONString(cityPlaceInfo));
                    if (null == cityPlaceInfo){
                        //原地址市无效，用原市+区找映射
                        String districtNameDict = distinctMap.get(receiverCityName+"-"+receiverDistrictName);
                        if (StringUtils.isNotBlank(districtNameDict) && districtNameDict.split("-").length == 2){
                            String dictCity = districtNameDict.split("-")[0];
                            cityPlaceInfo = adressHelper.matchMoreMDMStandard(dictCity, PLACE_CITY,qcustomerOrderAddress.getReceiverProvinceCode());
                        }else{
                            throw BusinessException.fail("无需市地址映射，订单的收货市地址：["+receiverCityName+"]，省编码["+qcustomerOrderAddress.getReceiverProvinceCode()+"]，匹配市标准库，查询接口为空");
                        }
                    }
                    if (null == cityPlaceInfo){
                        throw BusinessException.fail("无需市地址映射，订单的收货市地址：["+receiverCityName+"]，省编码["+qcustomerOrderAddress.getReceiverProvinceCode()+"]，匹配市标准库，查询接口为空");
                    }
                    //记录多个市的结果
                    cityPlaces = cityPlaceInfo.getEbPlaceList();
                    qcustomerOrderAddress.setReceiverCityName(cityPlaceInfo.getClosestPlace().getEbplNameCn());
                    qcustomerOrderAddress.setReceiverCityCode(cityPlaceInfo.getClosestPlace().getEbplCode());
                }

                //4.区映射，设置区并且倒推市
                String districtName = distinctMap.get(receiverCityName+"-"+receiverDistrictName);
                if (StringUtils.isNotBlank(districtName)){
                    //区中替换市的情况
                    String[] split = districtName.split("-");
                    if(split!=null && split.length == 2){
                            //市
                            String cityNew = split[0];
                            //区
                            String districtNew = split[1];
                            qcustomerOrderAddress.setReceiverCityName(cityNew);
                            qcustomerOrderAddress.setReceiverDistrictName(districtNew);
                    }else{
                        FlowListenerParam flowListenerParam = adressHelper.setDistrictAndRetrodictCity(qcustomerOrderAddress, districtName, cityPlaces);
                        log.info("AddressResolvingServiceImpl->orderNo:{}->districtName->flowListenerParam:{}",orderNo,JSON.toJSONString(flowListenerParam));
                        if (null != flowListenerParam) {
                            throw BusinessException.fail(flowListenerParam.getErrorMsg());
                        }
                    }
                }

                //ECM 来的单是有编码的，所以要特殊处理一下，不然会影响下面的获取区地址
                if (SourceSystem.ECM.getKey().equals(sourceSystem)) {
                    if (StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverDistrictCode())) {
                        qcustomerOrderAddress.setReceiverDistrictCode(null);
                    }
                }

                //5.匹配区和倒推市
                log.info("AddressResolvingServiceImpl->orderNo:{}->qcustomerOrderAddress:{}",orderNo,JSON.toJSONString(qcustomerOrderAddress));

                if (StringUtils.isBlank(qcustomerOrderAddress.getReceiverDistrictCode())){
                    if (StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverCityCode())){
                        //设置区并且倒推市
                        FlowListenerParam flowListenerParam = adressHelper.setDistrictAndRetrodictCity(qcustomerOrderAddress, qcustomerOrderAddress.getReceiverDistrictName(), cityPlaces);
                        log.info("AddressResolvingServiceImpl->orderNo:{}->setDistrictAndRetrodictCity->flowListenerParam:{}",orderNo,JSON.toJSONString(flowListenerParam));
                        if (null != flowListenerParam) {
                            throw BusinessException.fail(flowListenerParam.getErrorMsg());
                        }
                    }else {
                        throw BusinessException.fail("市编码未匹配到!");
                    }
                }
                log.info("AddressResolvingServiceImpl->orderNo:{}->BigAddressEnd,大电逻辑结束(#^.^#)",orderNo);
                //-------------------------------------------------2020-4-23 15:58:41 重构代码部分 end--------------------------------------------------------------------//


                //B2B的逻辑，走完就直接结束，后面都是B2C的逻辑
                if (BusinessMode.B2B.getName().equals(customerOrderInfo.getBusinessMode())){

                    //解析gis
                    String receiverAddress = getReceiverAddress(qcustomerOrderAddress);
                    FlowListenerParam receiverCountryCode = getCountryCode(customerOrderInfo, receiverAddress, true);

                    if (FlowStatus.SUCCESS != receiverCountryCode.getFlowStatus()) {
                        throw BusinessException.fail("来源系统:"+sourceSystem+"-B2B,地址解析接口异常:"+receiverCountryCode.getErrorMsg()+"");
                    }

                    String errorMsg = receiverCountryCode.getErrorMsg();
                    String[] split = errorMsg.split(",");

                    if (!Objects.equals(split.length,2)){
                        throw BusinessException.fail("来源系统:"+sourceSystem+"-B2B,地址解析接口，返回异常数据,配送类型：");
                    }
                    String contryCode = split[0];
                    String lnglats = split[1];

                    //比对
                    FlowListenerParam flowListenerParam = compareAddressByDeliveryType(contryCode, qcustomerOrderAddress, null);

                    if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                        throw BusinessException.fail( "来源系统:"+sourceSystem+"-B2B,gis返回地址和原地址不匹配,"+flowListenerParam.getErrorMsg());
                    }
                    setEndLngLat( lnglats , qcustomerOrderAddress);
                    FlowListenerParam flowListenerParam1 = setReceiverAddressCode(contryCode, qcustomerOrderAddress);

                    if (FlowStatus.SUCCESS != flowListenerParam1.getFlowStatus()) {

                        throw BusinessException.fail(flowListenerParam1.getErrorMsg()+",来源系统:"+sourceSystem+"-B2B");
                    }

                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("来源系统:"+sourceSystem+"-B2B,解析成功,"+"解析后地址："+getReceiverAddress(qcustomerOrderAddress)+",原地址："+qcustomerOrderAddress.getOriginAddr());
                    return customerOrderInfo;
                }

                //B2C的逻辑  --  只有 B2C 才能提交解析
                DeliveryType delivery = null;
                if ( businessMode == BusinessMode.B2C && StringUtils.isNotBlank(customerOrderInfo.getWhCode())){

                    Integer outCollabWh = deliveryTypeHelper.getOutCollabWh(customerOrderInfo.getWhCode());
                    customerOrderInfo.setOutsourceFlag(outCollabWh);

                    //配送类型
                    delivery = deliveryTypeHelper.getB2cDeliveryType(customerOrderInfo,null,qcustomerOrderAddress);
                    if (delivery != null){
                        customerOrderInfo.setDeliveryType(delivery.getKey());
                        orderHelper.updateCustomerOrderInfo( customerOrderInfo,"地址解析-配送方式");
                        customerOrderInfo.setVersion(customerOrderInfo.getVersion()+1);
                    }
                }

                if(delivery == null){
                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("发货类型为空,跳过地址解析。");
                    return customerOrderInfo;
                }

                // B2C 宅配
                if (delivery != null && DeliveryType.DOT == delivery){

                    String receiverDistrictCode = qcustomerOrderAddress.getReceiverDistrictCode();
                    String first = receiverDistrictCode.substring(0, 3);
                    String second = receiverDistrictCode.substring(0, 5);

                    if (!first.equals(qcustomerOrderAddress.getReceiverProvinceCode()) || !second.equals(qcustomerOrderAddress.getReceiverCityCode())){
                        throw BusinessException.fail("地址不合法，配送类型："+ (delivery==null?null:delivery.getValue()));
                    }

                    //经纬度
                    if (Objects.isNull(qcustomerOrderAddress.getEndLng()) || Objects.isNull(qcustomerOrderAddress.getEndLat())){

                        String receiverAddress = getReceiverAddress(qcustomerOrderAddress);
                        FlowListenerParam lnglat = getLnglat(receiverAddress, true);
                        if (FlowStatus.SUCCESS == lnglat.getFlowStatus()) {
                            String lnglats = lnglat.getErrorMsg();
                            setEndLngLat(lnglats, qcustomerOrderAddress);
                        }
                    }

                    //保存数据库
                    orderHelper.updateCustomerOrderAddress( qcustomerOrderAddress);
                    qcustomerOrderAddress.setVersion(qcustomerOrderAddress.getVersion()+1);
                    // 菜鸟大宝仓间调拨，调拨出库单，四级地址解析完成后，流程hold住，打地址解析异常，由人工确认地址无误后，再继续审核；

                    if(StringUtils.isNotBlank(customerOrderInfo.getCarrierCode())
                        && "TRUNK_13476700".equals(customerOrderInfo.getCarrierCode())
                        && OrderType.AO == orderType
                        && SourceSystem.CAINIAO.getKey().equals(sourceSystem)){
                        throw BusinessException.fail( "此类型单需人工确认地址无误,地址:"+getReceiverAddress(qcustomerOrderAddress));
                    }
                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("地图解析："+getReceiverAddress(qcustomerOrderAddress));
                    return customerOrderInfo;
                }else {

                    //解析gis
                    String receiverAddress = getReceiverAddress(qcustomerOrderAddress);
                    FlowListenerParam receiverCountryCode = getCountryCode(customerOrderInfo, receiverAddress, true);

                    if (FlowStatus.SUCCESS != receiverCountryCode.getFlowStatus()) {
                        throw BusinessException.fail("地址解析接口，返回异常数据,配送类型："+ (delivery==null?null:delivery.getValue()) + ",gis返回的报错信息：" + receiverCountryCode.getErrorMsg());
                    }

                    String errorMsg = receiverCountryCode.getErrorMsg();
                    String[] split = errorMsg.split(",");

                    if (!Objects.equals(split.length,2)){
                        throw BusinessException.fail("地址解析接口，返回异常数据,配送类型："+ (delivery==null?null:delivery.getValue())+ ",gis返回的报错信息：" + receiverCountryCode.getErrorMsg());
                    }
                    String contryCode = split[0];
                    String lnglats = split[1];

                    //比对
                    FlowListenerParam flowListenerParam = compareAddressByDeliveryType(contryCode, qcustomerOrderAddress, delivery == null ? null : delivery.getKey());

                    if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                        throw BusinessException.fail("gis返回地址和原地址不匹配,"+flowListenerParam.getErrorMsg()+",配送类型："+ (delivery==null?null:delivery.getValue()));
                    }
                    setEndLngLat( lnglats , qcustomerOrderAddress);
                    FlowListenerParam flowListenerParam1 = setReceiverAddressCode(contryCode, qcustomerOrderAddress);

                    if (FlowStatus.SUCCESS != flowListenerParam1.getFlowStatus()) {
                        throw BusinessException.fail(flowListenerParam1.getErrorMsg()+",来源系统:"+sourceSystem);
                    }

                    // 菜鸟大宝仓间调拨，调拨出库单，四级地址解析完成后，流程hold住，打地址解析异常，由人工确认地址无误后，再继续审核；
                    if(StringUtils.isNotBlank(customerOrderInfo.getCarrierCode())
                        && "TRUNK_13476700".equals(customerOrderInfo.getCarrierCode())
                        && OrderType.AO == orderType
                        && SourceSystem.CAINIAO.getKey().equals(sourceSystem)){
                        throw BusinessException.fail("此类型单需人工确认地址无误,地址:"+getReceiverAddress(qcustomerOrderAddress));
                    }
                }
            }

            //2020-2-12 17:50:04 李娟 ：采购入库单且配送方式不是自提的，需要解析始发地地址
            else if(OrderType.PI == orderType ){
                return saveSenderAddressByInType(customerOrderInfo, qcustomerOrderAddress, sourceSystem);
            }else {
                customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                FlowListenerParam.success("");
                return customerOrderInfo;
            }
        }
        /**
         * 其他的来源、业务类型等....
         */
        else {

            //新建模糊订单不需要填写收货地址，在订单补录时填写
            if (!CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag()) && (InOutType.OUT == ioTypeEnum || InOutType.YS == ioTypeEnum)) {
                if (StringUtils.isEmpty(qcustomerOrderAddress.getReceiverDetailAddr())) {
                    throw BusinessException.fail("详细地址不能为空,无法走地址解析流程!");
                }
                String receiverTownCode = qcustomerOrderAddress.getReceiverTownCode();

                //四级编码为空，调地址解析
                if (StringUtils.isBlank(receiverTownCode)){

                    String receiverAddress = getReceiverAddress(qcustomerOrderAddress);

                    FlowListenerParam receiverCountryCode = getCountryCode(customerOrderInfo, receiverAddress, true);

                    if (FlowStatus.SUCCESS != receiverCountryCode.getFlowStatus()) {
                        throw BusinessException.fail(receiverCountryCode.getErrorMsg());
                    }
                    String errorMsg = receiverCountryCode.getErrorMsg();
                    String[] split = errorMsg.split(",");
                    if (!Objects.equals(split.length, 2)) {
                        throw BusinessException.fail("地址解析接口，返回异常数据");
                    }
                    String contryCode = split[0];
                    String lnglats = split[1];

                    if (StringUtils.isBlank(contryCode) || !Objects.equals(contryCode.length(), 10)) {

                        throw BusinessException.fail("地址解析返回异常编码：" + (contryCode == null ? "null" : contryCode));
                    }

                    //TODO 比对是不是合法?==比较两级

                    if (StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverProvinceCode()) && !qcustomerOrderAddress.getReceiverProvinceCode().equals(contryCode.substring(0,3))){

                        throw BusinessException.fail("地址解析返回编码和原有编码不匹配，原：" +qcustomerOrderAddress.getReceiverProvinceCode()+",后："+contryCode);
                    }
                    if (StringUtils.isNotBlank(qcustomerOrderAddress.getReceiverCityCode()) && !qcustomerOrderAddress.getReceiverCityCode().equals(contryCode.substring(0,5))){

                        throw BusinessException.fail( "地址解析返回编码和原有编码不匹配，原：" +qcustomerOrderAddress.getReceiverCityCode()+",后："+contryCode);
                    }

                    setEndLngLat(lnglats, qcustomerOrderAddress);
                    FlowListenerParam flowListenerParam = setReceiverAddressCode(contryCode, qcustomerOrderAddress);
                    if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {

                        throw BusinessException.fail( flowListenerParam.getErrorMsg()+",来源系统:"+sourceSystem);
                    }

                    //return FlowListenerParam.success("调用gis地址解析成功,gis编码为：" + contryCode + ",来源系统：" + sourceSystem);
                }

                //四级编码存在，则查询出name、截取编码保存====以第四级编码为准，不校验前几级的编码是否匹配第四级
                else {

                    String receiverProvinceName = qcustomerOrderAddress.getReceiverProvinceName();
                    String receiverCityName = qcustomerOrderAddress.getReceiverCityName();
                    String receiverDistrictName = qcustomerOrderAddress.getReceiverDistrictName();
                    String receiverTownName = qcustomerOrderAddress.getReceiverTownName();

                    //经纬度
                    if(Objects.isNull(qcustomerOrderAddress.getEndLat()) || Objects.isNull(qcustomerOrderAddress.getEndLng())){
                        String receiverAddress = getReceiverAddress(qcustomerOrderAddress);
                        FlowListenerParam lnglat = getLnglat(receiverAddress, true);

                        if (FlowStatus.SUCCESS == lnglat.getFlowStatus()) {

                            String lnglats = lnglat.getErrorMsg();

                            setEndLngLat(lnglats , qcustomerOrderAddress);
                        }
                    }

                    //名字都存在，通过四级编码查询标准库，进行名称比对
                    if (StringUtils.isNotBlank(receiverProvinceName) && StringUtils.isNotBlank(receiverCityName)
                        && StringUtils.isNotBlank(receiverDistrictName) && StringUtils.isNotBlank(receiverTownName)){

                        //比较存在的编码和名字是否匹配

                        String mdmProvinceName = getAbleNameByCode(receiverTownCode.substring(0,3));
                        String mdmCityName = getAbleNameByCode(receiverTownCode.substring(0,5));
                        String mdmDistrictName = getAbleNameByCode(receiverTownCode.substring(0,7));
                        String mdmTownName = getAbleNameByCode(receiverTownCode);

                        boolean provinceFlag = receiverProvinceName.equals(mdmProvinceName);
                        boolean cityFlag = receiverCityName.equals(mdmCityName);

                        if (provinceFlag && cityFlag){

                            qcustomerOrderAddress.setReceiverProvinceCode(receiverTownCode.substring(0,3));
                            qcustomerOrderAddress.setReceiverCityCode(receiverTownCode.substring(0,5));
                            qcustomerOrderAddress.setReceiverDistrictCode(receiverTownCode.substring(0,7));
                            qcustomerOrderAddress.setReceiverTownCode(receiverTownCode);

                            //这个地方存在被覆盖的可能 ===值不一样
                            qcustomerOrderAddress.setReceiverDistrictName(mdmDistrictName);
                            qcustomerOrderAddress.setReceiverTownName(mdmTownName);

                            orderHelper.updateCustomerOrderAddress( qcustomerOrderAddress);
                            qcustomerOrderAddress.setVersion(qcustomerOrderAddress.getVersion()+1);

                            //return FlowListenerParam.success("地址编码和名字匹配标准库成功!");
                        }else {
                            throw BusinessException.fail( "已存在的地址和编码匹配失败。");
                        }
                    }

                    //有不存在的名称，查询标准库,保存编码和名称
                    else {

                        if (StringUtils.isBlank(receiverProvinceName)){
                            String mdmProvinceName = getAbleNameByCode(receiverTownCode.substring(0,3));
                            if (StringUtils.isBlank(mdmProvinceName)){
                                throw BusinessException.fail( "省份编码无法匹配到标准库!");
                            }
                            qcustomerOrderAddress.setReceiverProvinceCode(receiverTownCode.substring(0,3));
                            qcustomerOrderAddress.setReceiverProvinceName(mdmProvinceName);
                        }
                        if (StringUtils.isBlank(receiverCityName)){
                            String mdmCityName = getAbleNameByCode(receiverTownCode.substring(0,5));
                            if (StringUtils.isBlank(mdmCityName)){
                                throw BusinessException.fail( "市编码无法匹配到标准库!");
                            }
                            qcustomerOrderAddress.setReceiverCityCode(receiverTownCode.substring(0,5));
                            qcustomerOrderAddress.setReceiverCityName(mdmCityName);
                        }
                        if (StringUtils.isBlank(receiverDistrictName)){
                            String mdmDistrictName = getAbleNameByCode(receiverTownCode.substring(0,7));
                            if (StringUtils.isBlank(mdmDistrictName)){
                                throw BusinessException.fail( "区编码无法匹配到标准库!");
                            }
                            qcustomerOrderAddress.setReceiverDistrictCode(receiverTownCode.substring(0,7));
                            qcustomerOrderAddress.setReceiverDistrictName(mdmDistrictName);
                        }
                        if (StringUtils.isBlank(receiverTownName)){
                            String mdmTownName = getAbleNameByCode(receiverTownCode);
                            if (StringUtils.isBlank(mdmTownName)){
                                throw BusinessException.fail( "县编码无法匹配到标准库!");
                            }
                            qcustomerOrderAddress.setReceiverTownCode(receiverTownCode);
                            qcustomerOrderAddress.setReceiverTownName(mdmTownName);
                        }
                        orderHelper.updateCustomerOrderAddress( qcustomerOrderAddress);
                        qcustomerOrderAddress.setVersion(qcustomerOrderAddress.getVersion()+1);

                        //return FlowListenerParam.success("匹配标准库成功!");

                    }
                }
            }


            if (InOutType.IN == ioTypeEnum || InOutType.YS == ioTypeEnum) {
                return saveSenderAddressByInType(customerOrderInfo, qcustomerOrderAddress, sourceSystem);
            }
        }

        customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
        FlowListenerParam.success("地图解析："+getReceiverAddress(qcustomerOrderAddress));
        return customerOrderInfo;
        // 结果整理

    }

    /**
     * @description: 小电设置区
     * @param: [orderNo, deliveryType, qcustomerOrderAddress, cityPlaceInfo]
     * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
     * @author: 陈永培
     * @createtime: 2020/4/29 9:16
     */
    private FlowListenerParam setSmallDistrictName(String orderNo, String deliveryType,CustomerOrderAddress qcustomerOrderAddress, PlaceInfo cityPlaceInfo) {

        String receiverDistrictName = qcustomerOrderAddress.getReceiverDistrictName();

        //1.市都没有匹配到，就不需要配置区了
        if (null == cityPlaceInfo) {
            return null;
        }

        //2.区为空情况
        if (StringUtils.isEmpty(receiverDistrictName)) {

            //1.如果是快递，不需要解析第三级地址，如果匹配到多个市，只能是默认第一个了，没办法了~
            if (DeliveryType.EXPRESS.getKey().equals(deliveryType)) {
                return null;
            }

            //2.非快递的直接hold住
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "小电非快递的三级级地址不能空");
        }

        //3.区小于2位报错
        if (receiverDistrictName.length()<2) {
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "小电三级级地址：["+receiverDistrictName+"]，小于两位，无法识别");
        }

        //4.根据区，循环匹配市，推导出最终的市和区
        FlowListenerParam errorParam = adressHelper.setDistrictAndRetrodictCity(qcustomerOrderAddress, qcustomerOrderAddress.getReceiverDistrictName(), cityPlaceInfo.getEbPlaceList());
        log.info("AddressResolvingServiceImpl->orderNo:{}->SmallAddress->errorParam:{}",orderNo, JSON.toJSONString(errorParam));
        //不阻断流程，但是需要还原区的值
        if (null != errorParam) {
            qcustomerOrderAddress.setReceiverDistrictName(qcustomerOrderAddress.getReceiverDistrictName());
        }
        return null;
    }

    /**
     * @description: 入库类型解析始发、发货地址
     * @param: [customerOrderInfo, qcustomerOrderAddress, sourceSystem]
     * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
     * @author: 陈永培
     * @createtime: 2020/2/12 17:51
     */
    private CustomerOrderInfoExt saveSenderAddressByInType(CustomerOrderInfoExt customerOrderInfo,CustomerOrderAddress qcustomerOrderAddress, String sourceSystem) {

        log.info("AddressResolvingServiceImpl->orderNo:{}->saveSenderAddressByInType",customerOrderInfo.getOrderNo());

        if (StringUtils.isEmpty(qcustomerOrderAddress.getSenderDetailAddr())) {
            throw BusinessException.fail("详细地址不能为空,无法走地址解析流程!");
        }

        String senderTownCode = qcustomerOrderAddress.getSenderTownCode();
        //四级编码为空，调地址解析
        if (StringUtils.isBlank(senderTownCode)){

            String senderAddress = getSenderAddress(qcustomerOrderAddress);

            FlowListenerParam senderCountryCode = getCountryCode(customerOrderInfo, senderAddress, true);

            if (FlowStatus.SUCCESS != senderCountryCode.getFlowStatus()) {
                throw BusinessException.fail(senderCountryCode.getErrorMsg());
            }
            String errorMsg = senderCountryCode.getErrorMsg();
            String[] split = errorMsg.split(",");
            if (!Objects.equals(split.length, 2)) {
                throw BusinessException.fail("地址解析接口，返回异常数据");
            }
            String contryCode = split[0];
            String lnglats = split[1];

            if (StringUtils.isBlank(contryCode) || !CommonConstant.FLAG_YES.equals(qcustomerOrderAddress.getPlanOrderFlag()) && !Objects.equals(contryCode.length(), 10)) {
                throw BusinessException.fail("地址解析返回异常编码：" + (contryCode == null ? "null" : contryCode));
            }

            //比对是不是合法?==比较两级

            if (StringUtils.isNotBlank(qcustomerOrderAddress.getSenderProvinceCode()) && !qcustomerOrderAddress.getSenderProvinceCode().equals(contryCode.substring(0,3))){
                throw BusinessException.fail("地址解析返回编码和原有编码不匹配，原：" +qcustomerOrderAddress.getSenderProvinceCode()+",后："+contryCode);
            }
            if (StringUtils.isNotBlank(qcustomerOrderAddress.getSenderCityCode()) && !qcustomerOrderAddress.getSenderCityCode().equals(contryCode.substring(0,5))){
                throw BusinessException.fail("地址解析返回编码和原有编码不匹配，原：" +qcustomerOrderAddress.getSenderCityCode()+",后："+contryCode);
            }
            setStartLngLat(lnglats, qcustomerOrderAddress);
            FlowListenerParam flowListenerParam = setSenderAddressCode(contryCode, qcustomerOrderAddress);
            if (FlowStatus.SUCCESS != flowListenerParam.getFlowStatus()) {
                throw BusinessException.fail(flowListenerParam.getErrorMsg()+",来源系统:"+sourceSystem);
            }

            customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
            FlowListenerParam.success("调用gis地址解析成功,gis编码为：" + contryCode + ",来源系统：" + sourceSystem);
            return customerOrderInfo;
        }

        //四级编码存在，则查询出name、截取编码保存====以第四级编码为准，不校验前几级的编码是否匹配第四级
        else {

            String senderProvinceName = qcustomerOrderAddress.getSenderProvinceName();
            String senderCityName = qcustomerOrderAddress.getSenderCityName();
            String senderDistrictName = qcustomerOrderAddress.getSenderDistrictName();
            String senderTownName = qcustomerOrderAddress.getSenderTownName();

            //经纬度
            if(Objects.isNull(qcustomerOrderAddress.getStartLat()) || Objects.isNull(qcustomerOrderAddress.getStartLng())){
                String senderAddress = getSenderAddress(qcustomerOrderAddress);
                FlowListenerParam lnglat = getLnglat(senderAddress, true);
                if (FlowStatus.SUCCESS == lnglat.getFlowStatus()) {
                    String lnglats = lnglat.getErrorMsg();
                    setStartLngLat(lnglats , qcustomerOrderAddress);
                }
            }

            //名字都存在，通过四级编码查询标准库，进行名称比对
            if (StringUtils.isNotBlank(senderProvinceName) && StringUtils.isNotBlank(senderCityName)
                && StringUtils.isNotBlank(senderDistrictName) && StringUtils.isNotBlank(senderTownName)){

                //比较存在的编码和名字是否匹配

                String mdmProvinceName = getAbleNameByCode(senderTownCode.substring(0,3));
                String mdmCityName = getAbleNameByCode(senderTownCode.substring(0,5));
                String mdmDistrictName = getAbleNameByCode(senderTownCode.substring(0,7));
                String mdmTownName = getAbleNameByCode(senderTownCode);

                boolean provinceFlag = senderProvinceName.equals(mdmProvinceName);
                boolean cityFlag = senderCityName.equals(mdmCityName);

                if (provinceFlag && cityFlag){

                    qcustomerOrderAddress.setSenderProvinceCode(senderTownCode.substring(0,3));
                    qcustomerOrderAddress.setSenderCityCode(senderTownCode.substring(0,5));
                    qcustomerOrderAddress.setSenderDistrictCode(senderTownCode.substring(0,7));
                    qcustomerOrderAddress.setSenderTownCode(senderTownCode);

                    qcustomerOrderAddress.setSenderDistrictName(mdmDistrictName);
                    qcustomerOrderAddress.setSenderTownName(mdmTownName);

                    orderHelper.updateCustomerOrderAddress( qcustomerOrderAddress);
                    qcustomerOrderAddress.setVersion(qcustomerOrderAddress.getVersion()+1);

                    customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                    FlowListenerParam.success("");
                    return customerOrderInfo;
                }
                throw BusinessException.fail("已存在的地址和编码匹配失败。");
            }

            //有不存在的名称，查询标准库,保存编码和名称
            else {

                if (StringUtils.isBlank(senderProvinceName)){
                    String mdmProvinceName = getAbleNameByCode(senderTownCode.substring(0,3));
                    if (StringUtils.isBlank(mdmProvinceName)){
                        throw BusinessException.fail("省份编码无法匹配到标准库!");
                    }
                    qcustomerOrderAddress.setSenderProvinceCode(senderTownCode.substring(0,3));
                    qcustomerOrderAddress.setSenderProvinceName(mdmProvinceName);
                }
                if (StringUtils.isBlank(senderCityName)){
                    String mdmCityName = getAbleNameByCode(senderTownCode.substring(0,5));
                    if (StringUtils.isBlank(mdmCityName)){
                        throw BusinessException.fail("市编码无法匹配到标准库!");
                    }
                    qcustomerOrderAddress.setSenderCityCode(senderTownCode.substring(0,5));
                    qcustomerOrderAddress.setSenderCityName(mdmCityName);
                }
                if (StringUtils.isBlank(senderDistrictName)){
                    String mdmDistrictName = getAbleNameByCode(senderTownCode.substring(0,7));
                    if (StringUtils.isBlank(mdmDistrictName)){
                        throw BusinessException.fail("区编码无法匹配到标准库!");
                    }
                    qcustomerOrderAddress.setSenderDistrictCode(senderTownCode.substring(0,7));
                    qcustomerOrderAddress.setSenderDistrictName(mdmDistrictName);
                }
                if (StringUtils.isBlank(senderTownName)){
                    String mdmTownName = getAbleNameByCode(senderTownCode);
                    if (StringUtils.isBlank(mdmTownName)&& CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag())){
                        throw BusinessException.fail("县编码无法匹配到标准库!");
                    }
                    qcustomerOrderAddress.setSenderTownCode(senderTownCode);
                    qcustomerOrderAddress.setSenderTownName(mdmTownName);
                }

                orderHelper.updateCustomerOrderAddress( qcustomerOrderAddress);
                qcustomerOrderAddress.setVersion(qcustomerOrderAddress.getVersion()+1);
                customerOrderInfo.setCustomerOrderAddress(qcustomerOrderAddress);
                FlowListenerParam.success("匹配标准库成功!");
                return customerOrderInfo;
            }
        }
    }


    private FlowListenerParam compareReceiverAddress(String contryCode, CustomerOrderAddress customerOrderAddress, String sourceSystem) {
        //校验三级地址编码

        //校验四级
        String gisThirdName = getAbleNameByCode(contryCode.substring(0,7));

        if (StringUtils.isNotBlank(customerOrderAddress.getReceiverDistrictName()) && !customerOrderAddress.getReceiverDistrictName().equals(gisThirdName)){
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "解析地址和原地址不匹配,原："+customerOrderAddress.getReceiverDistrictName()+"，后："+gisThirdName);
        }

        return FlowListenerParam.success("校验成功");

    }

    private FlowListenerParam compareNetworkAddress(String errorMsg, CustomerOrderAddress qcustomerOrderAddress,String sourceSystem) {
        //内部单校验三级、四级
        if (SourceSystem.CIMS.getKey().equals(sourceSystem) || SourceSystem.CSS.getKey().equals(sourceSystem)) {
            //校验三级地址编码
            String districtCode = qcustomerOrderAddress.getNetworkDistrictCode();
            String thirdName = getAbleNameByCode(districtCode);

            if (StringUtils.isNotBlank(thirdName)) {
                return FlowListenerParam.fail(ExceptionType.NET_ADDRESS_FAILED, "三级网点地址编码不存在或已被停用!");
            }

            //校验四级
            String receiverTownName = qcustomerOrderAddress.getNetworkTownName();
            String districtName = getAbleNameByCode(errorMsg);

            if (StringUtils.isNotBlank(receiverTownName) && !receiverTownName.equals(districtName)){
                return FlowListenerParam.fail(ExceptionType.NET_ADDRESS_FAILED, "解析地址和原地址不匹配,原："+receiverTownName+"，后："+districtName);
            }

        }else {
            //外部单校验：省、市
            //原地址
            String networkProvinceName = qcustomerOrderAddress.getNetworkProvinceName();
            //解析地址
            String provinceName = getAbleNameByCode(errorMsg.substring(0,3));

            if (StringUtils.isBlank(provinceName)) {
                return FlowListenerParam.fail(ExceptionType.NET_ADDRESS_FAILED, "一级网点地址编码不存在或已被停用!");
            }

            if (StringUtils.isNotBlank(networkProvinceName) && !networkProvinceName.substring(0,2).equals(provinceName.substring(0,2))){
                return FlowListenerParam.fail(ExceptionType.NET_ADDRESS_FAILED, "解析后地址和原地址的省份不匹配，原："+networkProvinceName+"后："+provinceName);
            }

            String networkCityName = qcustomerOrderAddress.getNetworkCityName();
            String cityName = getAbleNameByCode(errorMsg.substring(0, 5));

            if (StringUtils.isBlank(cityName)) {
                return FlowListenerParam.fail(ExceptionType.NET_ADDRESS_FAILED, "二级网点地址编码不存在或已被停用!");
            }

            if (StringUtils.isNotBlank(networkCityName) && !networkCityName.substring(0,2).equals(cityName.substring(0,2))){
                return FlowListenerParam.fail(ExceptionType.NET_ADDRESS_FAILED, "解析后地址和原地址不匹配，原："+networkCityName+"后："+cityName);
            }
        }
        return FlowListenerParam.success("校验成功");
    }


    private FlowListenerParam compareAddressByDeliveryType(String contryCode, CustomerOrderAddress customerOrderAddress, String deliveryType) {

        if(null == customerOrderAddress){
            return FlowListenerParam.fail("地址表信息为空");
        }

        //快递 二级比对
        if (DeliveryType.EXPRESS.getKey().equals(deliveryType)) {
            String provinceName = getAbleNameByCode(contryCode.substring(0,3));

            if (StringUtils.isBlank(provinceName)) {
                return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "省不能为空");
            }

            if (StringUtils.isNotBlank(customerOrderAddress.getReceiverProvinceName()) && StringUtils.isNotBlank(provinceName)
                && !customerOrderAddress.getReceiverProvinceName().substring(0,2).equals(provinceName.substring(0,2))){
                return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "配送类型[快递]，解析后地址和原地址的省份不匹配，原："+customerOrderAddress.getReceiverProvinceName()+"后："+provinceName);
            }

            String cityName = getAbleNameByCode(contryCode.substring(0,5));
            if (StringUtils.isBlank(cityName)) {
                return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "市不能为空!");
            }

            logger.info("单号：{},gis解析地址：{}", customerOrderAddress.getOrderNo(),cityName);
            if (StringUtils.isNotBlank(customerOrderAddress.getReceiverCityName()) && StringUtils.isNotBlank(cityName)
                && !customerOrderAddress.getReceiverCityName().substring(0,2).equals(cityName.substring(0,2))){
                return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "配送类型[快递]，解析后地址和原地址的市不匹配，原："+customerOrderAddress.getReceiverCityName()+"后："+cityName);
            }

            return FlowListenerParam.success("校验成功");
        }

        //三级比对
        String receiverProvinceName = customerOrderAddress.getReceiverProvinceName();
        String receiverCityName = customerOrderAddress.getReceiverCityName();
        String receiverDistrictName = customerOrderAddress.getReceiverDistrictName();

        String threeAddress =  receiverProvinceName+receiverCityName+receiverDistrictName;

        String firstName = getAbleNameByCode(contryCode.substring(0,3));
        String secondName = getAbleNameByCode(contryCode.substring(0,5));
        String threeName = getAbleNameByCode(contryCode.substring(0,7));

        String gisAddress = firstName+secondName+threeName;

        logger.info("单号："+customerOrderAddress.getOrderNo()+",gis解析地址："+gisAddress+",原始地址："+threeAddress);

        if (!threeAddress.equals(gisAddress)){
            return FlowListenerParam.fail(ExceptionType.RECEIVER_ADDRESS_FAILED, "解析后地址和原地址不匹配，原："+threeAddress+"后："+threeName);
        }

        return FlowListenerParam.success("校验成功");
    }
}
