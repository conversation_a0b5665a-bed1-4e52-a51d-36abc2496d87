<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.BusineesFeeConfigExtMapper">

    <sql id="searchFieldsSql">
        `business_mode` AS businessMode,
        `order_type` AS orderType,
        `site_code` AS siteCode,
        `create_user_code` AS createUserCode,
        `delivery_type` AS deliveryType,
        `customer_code` AS customerCode,
        `update_time` AS updateTime,
        `version` AS version,
        `delete_flag` AS deleteFlag,
        `update_user_code` AS updateUserCode,
        `create_time` AS createTime,
        `id` AS id,
        `business_type` AS businessType
    </sql>

    <update id="deleteBatchById" parameterType="list">
        update
        businees_fee_config b
        set `delete_flag`=1
        where
        `id` in
        <foreach collection="array" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="getByCustomerCodeAndSiteCode" resultType="com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig">
        select
        <include refid="searchFieldsSql"/>
        from businees_fee_config t
        where delete_flag=0
        and site_code = #{siteCode}
        <if test="customerCode !=null and customerCode != ''">
            and customer_code = #{customerCode}
        </if>
        <if test="customerCode ==null or customerCode == ''">
            and (customer_code is null OR customer_code = '')
        </if>
        and business_mode = #{businessMode}
        and (order_type is null OR order_type = '' )
        limit 1
    </select>

    <select id="selectByIndexCanNull" resultType="com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig">
        select
        <include refid="com.midea.logistics.otp.rule.mapper.common.BusineesFeeConfigMapper.searchFieldsSql"/>
        from businees_fee_config t
        <include refid="com.midea.logistics.otp.rule.mapper.common.BusineesFeeConfigMapper.whereFieldsSql"/>
        <if test="customerCode ==null or customerCode == ''">
            and (customer_code is null OR customer_code = '')
        </if>
        <if test="whCode == null or whCode == ''">
            and (wh_code is null OR wh_code = '')
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="batchRepair">
        <foreach collection="list" item="item" separator=";">
            UPDATE ${item.table}
            set `delete_flag`=0
            where
            `id` = #{item.id}
        </foreach>
    </update>

    <update id="batchSetEmpty">
        <foreach collection="list" item="item" separator=";">
            UPDATE ${item.table}
            set ${item.fields}
            where
            `id` = #{item.id}
        </foreach>
    </update>

    <select id="analysisConfigRule" resultType="com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig">
        select *
        from (
            select
            concat(
                <!--1、业务模式、服务平台、客户、仓库、订单类型  -->
                case when #{whCode} is not null and #{whCode} != '' and #{whCode} = wh_code
                and #{customerCode} is not null and #{customerCode} != '' and  #{customerCode} = customer_code
                and #{orderType} is not null and #{orderType} != '' and  #{orderType} = order_type
                then 1 else 0 end
                ,
                <!--2、业务模式、服务平台、客户、仓库-->
                case when #{whCode} is not null and #{whCode} != '' and #{whCode} = wh_code
                and #{customerCode} is not null and #{customerCode} != '' and  #{customerCode} = customer_code
                and (`order_type` is null or `order_type` = '')
                then 1 else 0 end
                ,
                <!--3、业务模式、服务平台、仓库、订单类型-->
                case when #{whCode} is not null and #{whCode} != '' and #{whCode} = wh_code
                and (`customer_code` is null or `customer_code` = '')
                and #{orderType} is not null and #{orderType} != '' and  #{orderType} = order_type
                then 1 else 0 end
                ,
                <!--4、业务模式、服务平台、客户、订单类型-->
                case when (`wh_code` is null or `wh_code` = '')
                and #{customerCode} is not null and #{customerCode} != '' and  #{customerCode} = customer_code
                and #{orderType} is not null and #{orderType} != '' and  #{orderType} = order_type
                then 1 else 0 end
                ,
                <!--5、业务模式、服务平台、仓库-->
                case when #{whCode} is not null and #{whCode} != '' and #{whCode} = wh_code
                and (`customer_code` is null or `customer_code` = '')
                and (`order_type` is null or `order_type` = '')
                then 1 else 0 end
                ,
                <!--6、业务模式、服务平台、客户-->
                case when (`wh_code` is null or `wh_code` = '')
                and #{customerCode} is not null and #{customerCode} != '' and  #{customerCode} = customer_code
                and (`order_type` is null or `order_type` = '')
                then 1 else 0 end
                ,
                <!--7、业务模式、服务平台、订单类型-->
                case when (`wh_code` is null or `wh_code` = '')
                and (`customer_code` is null or `customer_code` = '')
                and #{orderType} is not null and #{orderType} != '' and  #{orderType} = order_type
                then 1 else 0 end
                ,
                <!--8、业务模式、服务平台、空-->
                case when (`wh_code` is null or `wh_code` = '')
                and (`customer_code` is null or `customer_code` = '')
                and (`order_type` is null or `order_type` = '')
                then 1 else 0 end
            )  as ranking,
            `id` AS id,
            `business_type` AS businessType
            from businees_fee_config t
            <where>
                delete_flag = 0
                and site_code = #{siteCode}
                and business_mode = #{businessMode}
            </where>
            order by ranking desc
            limit 1
        ) t
        where ranking > 0
    </select>

</mapper>