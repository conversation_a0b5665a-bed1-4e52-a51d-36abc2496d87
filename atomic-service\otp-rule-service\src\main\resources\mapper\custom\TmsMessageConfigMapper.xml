<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.TmsMessageConfigMapper">

    <select id="selectPage" resultType="com.midea.logistics.otp.rule.domain.bean.TmsMessageConfig">
        select
        <include refid="searchFieldsSql"/>
        from tms_message_config t
        <include refid="whereFieldsSql"/>
        order by id desc
        limit ${start},${pageSize}
    </select>

    <select id="selectAllByIndex" parameterType="com.midea.logistics.otp.rule.domain.bean.TmsMessageConfig"
            resultType="com.midea.logistics.otp.rule.domain.bean.TmsMessageConfig">
        <foreach collection="list" item="item" separator="union all" index="index">
            select
            <include refid="searchFieldsSql"/>
            from tms_message_config t
            where t.delete_flag = 0 and
            t.`customer_name` = #{item.customerName} and t.`message_status` = #{item.messageStatus}
        </foreach>
    </select>

</mapper>