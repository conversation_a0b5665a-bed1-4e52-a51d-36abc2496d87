package com.midea.logistics.otp.task.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.utils.SUtils;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.mapper.common.TaskItemMapper;
import com.midea.logistics.otp.task.mapper.common.TaskMapper;
import com.midea.logistics.otp.task.service.ITaskItemService;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.PageResponse;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskItemServiceImpl
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:TaskItemServiceImpl类
 */
@Service
public class TaskItemServiceImpl implements ITaskItemService {


    private static final Logger logger = LoggerFactory.getLogger(TaskItemServiceImpl.class);

    @Autowired
    private TaskItemMapper taskItemMapper;
    @Autowired
    private TaskMapper taskMapper;


    @Override
    @Transactional(readOnly = false)
    public Integer saveTaskItem(TaskItem taskItem) {

        if (null == taskItem) {
            logger.warn("save taskItem, but taskItem is null...");
            return 0;
        }

        return taskItemMapper.save(taskItem);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteTaskItem(TaskItem taskItem) {

        if (null == taskItem || null == taskItem.getId()) {
            logger.warn("delete taskItem, but taskItem is null  or taskItem id is null...");
            return 0;
        }

        return taskItemMapper.deleteById(taskItem);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateTaskItem(TaskItem taskItem) {

        if (null == taskItem || null == taskItem.getId()) {
            logger.warn("update taskItem, but taskItem is null  or taskItem id is null...");
            return 0;
        }

        return taskItemMapper.updateById(taskItem);
    }

    @Override
    public TaskItem selectOneTaskItem(TaskItem taskItem) {
        if (taskItem == null) {
            logger.warn("select taskItem one, but taskItem is null ...");
            return null;
        }
        taskItem = taskItemMapper.selectOne(taskItem);
        return taskItem;
    }

    @Override
    public PageResponse<TaskItem> selectTaskItemPage(TaskItem taskItem) {
        PageResponse<TaskItem> pagingData = new PageResponse<>();

        if (null == taskItem) {
            logger.warn("select taskItem page, but taskItem is null...");
            return pagingData;
        }

        Integer queryCount = 100;
        if(taskItem.getPageSize() <= 100){
            queryCount = taskItemMapper.selectByIndexCount(taskItem);
        }
        if (null != queryCount && queryCount <= 0) {
            logger.info("select taskItem page , but count {} == 0 ...", queryCount);
            return pagingData;
        }

        List<TaskItem> taskItems = selectTaskItemByIndex(taskItem);
        pagingData.init(taskItem.getPageNo(), taskItem.getPageSize(), queryCount, taskItems);
        return pagingData;
    }

    @Override
    public List<TaskItem> selectTaskItemByIndex(TaskItem taskItem) {
        List<TaskItem> taskItems = new ArrayList<>();
        if (taskItem == null) {
            logger.warn("select taskItem by index, but taskItem is null ...");
            return taskItems;
        }

        taskItems = taskItemMapper.selectByIndex(taskItem);

        return taskItems;
    }

    @Override
    public int countTaskItemByIndex(TaskItem taskItem) {
        int count = 0;
        if (taskItem == null) {
            logger.warn("count taskItem by index, but taskItem is null ...");
            return count;
        }

        count = taskItemMapper.selectByIndexCount(taskItem);

        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertBatch(List<TaskItem> taskItems) {
        if (CollectionUtils.isEmpty(taskItems)) {
            logger.warn("insertBatch taskItems, but taskItems is null ...");
            return 0;
        }

        int count = 0;
        int batchSize = 500; // 每批插入的数量
        for (int i = 0; i < taskItems.size(); i += batchSize) {
            int end = Math.min(i + batchSize, taskItems.size());
            List<TaskItem> batchItems = taskItems.subList(i, end);
            count += taskItemMapper.insertBatch(batchItems);
        }

        return count;
    }

    @Override
    public Integer batchUpdate(List<TaskItem> taskItems) {
        if (CollectionUtils.isEmpty(taskItems)) {
            logger.warn("batchUpdate taskItems, but taskItems is null ...");
            return 0;
        }
        return taskItemMapper.batchUpdate(taskItems);
    }

    @Override
    public Integer batchUpdateQty(List<TaskItem> taskItems) {
        if (CollectionUtils.isEmpty(taskItems)) {
            logger.warn("batchUpdate taskItems, but taskItems is null ...");
            return 0;
        }
        return taskItemMapper.batchUpdateQty(taskItems);
    }

    @Override
    public Integer deleteBatch(List<TaskItem> taskItems) {
        if (CollectionUtils.isEmpty(taskItems)) {
            logger.warn("deleteBatch taskItems, but taskItems is null ...");
            return 0;
        }
        return taskItemMapper.deleteBatchByIds(taskItems);
    }

    @Override
    public List<TaskItem> listByTaskNos(List<String> taskNos) {
        if (CollectionUtils.isEmpty(taskNos)) {
            logger.warn("listByTaskNos, but taskNos is null ...");
            return null;
        }
        return taskItemMapper.listByTaskNos(taskNos);
    }
    
    @Override
    public List<TaskItem> listByParentOrderNo(String parentOrderNo) {
        if (ToolUtils.isEmpty(parentOrderNo)) {
            logger.warn("listByParentOrderNo, but parentOrderNo is null ...");
            return null;
        }
        
        Task search = new Task();
        search.setParentOrderNo(parentOrderNo);
        search.setPageSize(CommonConstant.PAGE_SIZE);
        List<Task> tasks = taskMapper.selectByIndex(search);
        if (ToolUtils.isEmpty(tasks)) {
            return null;
        }
        return taskItemMapper.listByTaskNos(SUtils.toList(tasks, Task::getTaskNo));
    }

    @Override
    public List<TaskItem> listByParentOrderNoAndDistributionNum(String parentOrderNo, Integer distributionNum) {
        if (ToolUtils.isEmpty(parentOrderNo)) {
            logger.warn("listByParentOrderNoAndDistributeNum, but parentOrderNo is null ...");
            return null;
        }
        if (distributionNum==null){
            logger.warn("listByParentOrderNoAndDistributeNum, but distributeNum is null ...");
            return null;
        }
        Task search = new Task();
        search.setParentOrderNo(parentOrderNo);
        search.setPageSize(CommonConstant.PAGE_SIZE);
        search.setDistributionNum(distributionNum);
        List<Task> tasks = taskMapper.selectByIndex(search);
        if (ToolUtils.isEmpty(tasks)) {
            return null;
        }
        return taskItemMapper.listByTaskNos(SUtils.toList(tasks, Task::getTaskNo));
    }

    @Override
    public List<TaskItem> sumActQtyByTaskNos(List<String> taskNos) {
        if (CollectionUtils.isEmpty(taskNos)) {
            logger.warn("sumActQtyByTaskNos, but taskNos is null ...");
            return null;
        }
        return taskItemMapper.sumActQtyByTaskNos(taskNos);
    }

    @Override
    public List<TaskItem> querySplitTasksByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            logger.warn("querySplitTasksByOrderNo, but orderNo is null ...");
            return null;
        }
        return taskItemMapper.querySplitTasksByOrderNo(orderNo);
    }
}