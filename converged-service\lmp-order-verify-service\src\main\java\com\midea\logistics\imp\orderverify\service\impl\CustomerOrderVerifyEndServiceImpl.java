package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.imp.orderverify.service.CustomerOrderVerifyEndService;
import com.midea.logistics.imp.orderverify.service.ZeebeLmpService;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoFeign;
import com.midea.logistics.otp.enums.OrderStatus;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CustomerOrderVerifyEndServiceImpl implements CustomerOrderVerifyEndService {

    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private ZeebeLmpService zeebeLmpService;


    @Override
    @ZeebeFlow("IN_END")
    public CustomerOrderInfoExt customerOrderVerifyEnd(CustomerOrderInfoExt customerOrderInfoExt) {
        String orderNo = customerOrderInfoExt.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            throw BusinessException.fail("订单号不能为空");
        }

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setParentOrderNo(orderNo);
        JsonResponse<List<OrderInfo>> search = orderInfoFeign.list(orderInfo);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(search.getCode())) {
            throw BusinessException.fail("查询子订单失败: " + search.getMsg());
        }

        if (CollectionUtils.isEmpty(search.data)) {
            throw BusinessException.fail("无子订单信息");
        }
        //子单的order_status>=200,则该子单不加入待审核子单行列
        List<String> newWorkflowTickets = search.data.stream().filter(s -> s.getOrderStatus() < OrderStatus.AUDITED.getKey()).collect(Collectors.toList())
            .stream().map(s -> s.getOrderNo()).collect(Collectors.toList());

        List<String> orderNos = search.data.stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(orderNos)){
            orderNos.stream().forEach(s -> zeebeLmpService.orderVerifyZeebe(s));
        }
        FlowListenerParam.success("即将执行子订单流程，有" + newWorkflowTickets.size()+ "单执行审核→" + JSONObject.toJSONString(newWorkflowTickets));
        return customerOrderInfoExt;
    }
}
