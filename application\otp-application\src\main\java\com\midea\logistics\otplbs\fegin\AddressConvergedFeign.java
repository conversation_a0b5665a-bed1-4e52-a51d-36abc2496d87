package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.request.AlertAddressRequest;
import com.midea.logistics.otp.order.converged.domain.request.EcmUpdateTelRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: GisAddressInfoFeign
 * @author: crystal
 * @date: 2019/6/28 10:28
 * @description: 待接口对接
 */
@FeignClient(value = "logistics-otp-order-converged-service")
public interface AddressConvergedFeign {

    @GetMapping(value = "/alterAddressError")
    JsonResponse alterAddressError(@SpringQueryMap  AlertAddressRequest request);

    /**
     * 更新手机号
     */
    @RequestMapping(value = "/alterAddressTel/ecmUpdateTel", method = RequestMethod.POST)
    JsonResponse ecmUpdateTel(@RequestBody EcmUpdateTelRequest ecmUpdateTelRequest);
}
