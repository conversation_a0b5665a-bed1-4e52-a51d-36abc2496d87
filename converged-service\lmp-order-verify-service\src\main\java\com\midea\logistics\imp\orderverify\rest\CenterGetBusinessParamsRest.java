package com.midea.logistics.imp.orderverify.rest;

import com.midea.logistics.otp.order.common.helper.BusinessControlParamHelper;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.RestDoing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: dumg
 * @Date: 2023-03-06-19:47
 * Description:
 */
@RestController
@Slf4j
public class CenterGetBusinessParamsRest {
    @Autowired
    BusinessControlParamHelper businessControlParamHelper;

    @PostMapping("/getBusinessControlDetailByIndex")
    JsonResponse<List<BusinessControlParamDetail>> getBusinessControlDetailByIndex(String param,HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = businessControlParamHelper.getBusinessControlDetailByIndex("QCCUSTOMER", null);
        };
        return doing.go(request, log);
    }
}
