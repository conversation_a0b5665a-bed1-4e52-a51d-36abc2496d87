package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.ReportTab;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.mideaframework.core.web.PageResponse;
import java.util.List;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: ReportTabFeign
* Author: 刘院民
* Date: 2023-3-31 11:12:34
* Description:订单标签表 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface ReportTabFeign{

    /**
    * 创建
    * @param ReportTab reportTab
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_CREATE, method = RequestMethod.POST)
    JsonResponse<String> create(@RequestBody ReportTab reportTab);

    /**
    * 查询
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_DETAIL, method = RequestMethod.GET)
    JsonResponse<ReportTab> queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * 更新
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ReportTab reportTab);

    /**
    * 删除
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    * 分页查询
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_SEARCH, method = RequestMethod.GET)
    JsonResponse<PageResponse<ReportTab>> search(@SpringQueryMap ReportTab reportTab);

    /**
    * 查询单个
    * @param reportTab
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_SELECT_ONE, method = RequestMethod.GET)
    public JsonResponse<ReportTab> selectOne(@SpringQueryMap ReportTab reportTab);

    /**
    * 查询集合
    * @param reportTab
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_LIST, method = RequestMethod.GET)
    public JsonResponse<List<ReportTab>> list(@SpringQueryMap ReportTab reportTab);


    /**
    * 批量新增
    *
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_INSERT_BATCH, method = RequestMethod.POST)
    public JsonResponse insertBatch(@RequestBody List<ReportTab> list);


    /**
    * 批量修改
    *
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_UPDATE_BATCH, method = RequestMethod.POST)
    public JsonResponse updateBatch(@RequestBody List<ReportTab> list);


    /**
    * 批量删除
    *
    * @return
    */
    @RequestMapping(value = OrderRouters.REPORT_TAB_DELETE_BATCH, method = RequestMethod.POST)
    public JsonResponse deleteBatch(@RequestBody List<ReportTab> list);

    /**
     * 默认与取消默认
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.REPORT_TAB_UPDATE_DEFAULT, method = RequestMethod.POST)
    public JsonResponse updateWhetherDefault(@RequestBody ReportTab reportTab);
}
