package com.midea.logistics.otplbs.fegin;

import cn.hutool.db.PageResult;
import com.midea.logistics.otp.order.domain.bean.AddServiceOrderAging;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©1968-2024 Midea Group,IT
 *
 * @Description:
 * @FileName: AddServiceOrderAgingFeign
 * @Author: fengxw26
 * @Date: 2024/7/26 10:31
 */
@FeignClient(value = "logistics-order-agg-service", contextId = "AddServiceOrderAgingFeign")
public interface AddServiceOrderAgingFeign {
    @GetMapping("/add/service/order/aging/list")
    public JsonResponse<List<AddServiceOrderAging>> list(@SpringQueryMap AddServiceOrderAging entity);
    @GetMapping("/add/service/order/aging/page")
    public JsonResponse<PageResult<AddServiceOrderAging>> page(@SpringQueryMap AddServiceOrderAging entity);
    @GetMapping("/add/service/order/aging/count")
    public JsonResponse<BaseDomain> count(@SpringQueryMap AddServiceOrderAging entity);
    @GetMapping("/add/service/order/aging/detail")
    public JsonResponse<AddServiceOrderAging> detail(@SpringQueryMap AddServiceOrderAging entity);
    @PostMapping("/add/service/order/aging/add")
    public JsonResponse<BaseDomain> add(@RequestBody  AddServiceOrderAging entity);
    @PutMapping("/add/service/order/aging/update")
    public JsonResponse<Integer> update(@RequestBody AddServiceOrderAging entity);
    @DeleteMapping("/add/service/order/aging/remove")
    public JsonResponse<Integer> remove(@RequestBody AddServiceOrderAging entity);
}
