<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.AssembleRuleConfigMapper">

    <select id="queryByCustomerAndSiteCodes"
            resultType="com.midea.logistics.otp.rule.domain.bean.AssembleRuleConfig">
        select
        <include refid="searchFieldsSql"/>
        from assemble_rule_config arc
        where
        arc.`customer_code` in
        <foreach collection="customerCodes" item="customerCode"
                 open="(" close=")" separator=",">
            #{customerCode}
        </foreach>
        and arc.`site_code` in
        <foreach collection="siteCodes" item="siteCode"
                 open="(" close=")" separator=",">
            #{siteCode}
        </foreach>
    </select>
</mapper>