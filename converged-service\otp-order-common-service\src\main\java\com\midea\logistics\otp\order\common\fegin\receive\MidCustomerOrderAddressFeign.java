package com.midea.logistics.otp.order.common.fegin.receive;

import com.midea.logistics.otp.order.domain.bean.MidCustomerOrderAddress;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @Description: 订单中间地址表 feign
 */
@FeignClient(value = "logistics-otp-order-receive-service")
public interface MidCustomerOrderAddressFeign {

    @RequestMapping(value = "/midCustomerOrderAddress/updateByOrderNo", method = RequestMethod.PUT)
    JsonResponse updateByOrderNo(@RequestBody MidCustomerOrderAddress midCustomerOrderAddress);

}
