package com.midea.logistics.otp.ttx.interceptor;

import com.mideaframework.core.web.JsonResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

import java.sql.SQLException;

/**
 * sql 异常处理 ControllerAdvice
 * <p>
 * 屏蔽 sql 注入拦截器对错误的回显
 *
 * <AUTHOR>
 * @date 2022/8/23
 */
@ControllerAdvice
public class SqlExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(SqlExceptionHandler.class);

    @ExceptionHandler({SQLException.class})
    public final ResponseEntity<Object> handleException(SQLException ex, WebRequest request) throws Exception {
        log.error("sql异常,{}", ex.getMessage(), ex);
        return new ResponseEntity<>(JsonResponse.fail("异常操作"), HttpStatus.INTERNAL_SERVER_ERROR);
    }

}