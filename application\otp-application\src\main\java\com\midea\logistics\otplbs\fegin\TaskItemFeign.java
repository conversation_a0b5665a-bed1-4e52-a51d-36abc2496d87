package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskItemFeign
 * Author: fangwb
 * Date: 2019年5月14日 下午6:12:06
 * Description: feign
 */
@FeignClient(value = "logistics-otp-task-service")
public interface TaskItemFeign {

    @RequestMapping(value = "/taskItem", method = RequestMethod.POST)
    JsonResponse<Integer> create(@RequestBody TaskItem taskItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskItem/{id}", method = RequestMethod.GET)
    JsonResponse<TaskItem> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskItem/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody TaskItem taskItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskItem/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/taskItems", method = RequestMethod.GET)
    JsonResponse<PageResponse<TaskItem>> search(@SpringQueryMap TaskItem taskItem);
}
