<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.ProjectClassifyConfigExtMapper">

    <sql id="searchFieldsSql">
        `delete_flag` AS deleteFlag,
        `business_mode` AS businessMode,
        `update_user_code` AS updateUserCode,
        `wh_code` AS whCode,
        `create_time` AS createTime,
        `source_system` AS sourceSystem,
        `create_user_code` AS createUserCode,
        `project_classify` AS projectClassify,
        `update_time` AS updateTime,
        `id` AS id,
        `version` AS version
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="businessMode !=null and businessMode != ''">
            and `business_mode` =#{businessMode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and `source_system` =#{sourceSystem}
        </if>
        <if test="projectClassify !=null and projectClassify != ''">
            and `project_classify` =#{projectClassify}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="businessMode !=null and businessMode != ''">
                `business_mode` = #{businessMode},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="whCode !=null and whCode != ''">
                `wh_code` = #{whCode},
            </if>
            <if test="sourceSystem !=null and sourceSystem != ''">
                `source_system` = #{sourceSystem},
            </if>
            <if test="projectClassify !=null and projectClassify != ''">
                `project_classify` = #{projectClassify},
            </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from project_classify_config t
        where
        `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.ProjectClassifyConfig">
        select
        <include refid="searchFieldsSql"/>
        from project_classify_config t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.ProjectClassifyConfig">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from project_classify_config t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.ProjectClassifyConfig">
        select
        <include refid="searchFieldsSql"/>
        from project_classify_config t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        project_classify_config t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>

    <update id="deleteById">
        update
        project_classify_config t
        set `delete_flag`=1
        where
        `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.ProjectClassifyConfig"
            useGeneratedKeys="true" keyProperty="id">
        insert into project_classify_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="businessMode !=null and businessMode != ''">
                `business_mode`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="whCode !=null and whCode != ''">
                `wh_code`,
            </if>

            <if test="sourceSystem !=null and sourceSystem != ''">
                `source_system`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="projectClassify !=null and projectClassify != ''">
                `project_classify`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessMode !=null and businessMode != ''">
                #{businessMode},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="whCode !=null and whCode != ''">
                #{whCode},
            </if>
            <if test="sourceSystem !=null and sourceSystem != ''">
                #{sourceSystem},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="projectClassify !=null and projectClassify != ''">
                #{projectClassify},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `business_mode`,
            `update_user_code`,
            `wh_code`,
            `source_system`,
            `create_user_code`,
            `project_classify`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.businessMode},
            #{item.updateUserCode},
            #{item.whCode},
            #{item.sourceSystem},
            #{item.createUserCode},
            #{item.projectClassify},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        project_classify_config
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.businessMode !=null and item.businessMode != ''">
                `business_mode` = #{item.businessMode},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.whCode !=null and item.whCode != ''">
                `wh_code` = #{item.whCode},
            </if>
            <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                `source_system` = #{item.sourceSystem},
            </if>
            <if test="item.projectClassify !=null and item.projectClassify != ''">
                `project_classify` = #{item.projectClassify},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.id !=null">
                and `id` =#{item.id}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE project_classify_config
            <include refid="setBatchFieldsSql"/>
            where
            <include refid="setBatchWhereFields"/>
        </foreach>

    </update>

    <update id="deleteBatchById" parameterType="list">
        update
        project_classify_config p
        set `delete_flag`=1
        where
        `id` in
        <foreach collection="array" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>


</mapper>