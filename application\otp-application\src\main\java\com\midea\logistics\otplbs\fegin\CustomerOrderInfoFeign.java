package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.converged.domain.request.OrderTaskAndItemUpdateRequest;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderItemExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoInMapping;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoOutMapping;
import com.midea.logistics.otplbs.bean.CopOrderInfosRes;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CustomerOrderInfoFeign
 * Author: lindq2
 * Date: 2019-5-23 9:52:10
 * Description:订单表 feign
 */
@FeignClient(value = "logistics-otp-order-service", url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface CustomerOrderInfoFeign {
    @RequestMapping(value = OrderRouters.CHECK_PICK_UP_CODE,method = RequestMethod.POST)
    JsonResponse<Integer> checkPickUpCode(@RequestBody CustomerOrderInfo customerOrderInfo);

    /**
    * @description: 查询模糊订单
    * @param: [orderInfoInMapping]
    * @return: com.mideaframework.core.web.JsonResponse<com.mideaframework.core.web.PageResponse<com.midea.logistics.otp.order.domain.bean.custom.OrderInfoOutMapping>>
    * @author: 陈永培
    * @createtime: 2021/6/11 11:45
    */
    @GetMapping(OrderRouters.VAGUE_ORDER_SEARCH)
    public JsonResponse<PageResponse<OrderInfoOutMapping>> vagueOrderSearch(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);

    @RequestMapping(value = OrderRouters.CUSTOMERORDERINFO_SAVE, method = RequestMethod.POST)
    public JsonResponse save(@Valid @RequestBody CustomerOrderInfo customerOrderInfo);

    @GetMapping(OrderRouters.CUSTOMER_ORDER_GETBY_UPPERrELATIO_ORDER_NO)
    public JsonResponse getByUpperRelationOrderNo(@RequestParam("upperRelationOrderNo") String upperRelationOrderNo);

    /**
     * @param orderNo
     * @return 根据单号来查询订单实体
     */
    @RequestMapping(value = OrderRouters.CUSTOMERORDERINFO_BY_ORDERNOS, method = RequestMethod.GET)
    public JsonResponse<CustomerOrderInfo> getOrderInfoByOrderNos(@RequestParam("orderNo") String orderNo);


    /**
     * 新增（接口数据映射写表）
     *
     * @param customerOrderInfo
     * @return
     */
    @RequestMapping(value = "/customerOrderInfo", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerOrderInfo customerOrderInfo);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderInfo/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderInfo/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerOrderInfo customerOrderInfo);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderInfo/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     * @return 根据条件（包含父订单表数据+子表+地址表）查询订单（包含父订单表数据+子表+地址表）列表
     */
    @RequestMapping(value = "/customerOrderInfos", method = RequestMethod.GET)
    public JsonResponse<PageResponse<OrderInfoOutMapping>> search(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);


    /**
     * @return 查询总数
     */
    @RequestMapping(value = "/customerOrderInfosCount", method = RequestMethod.GET)
    public JsonResponse<Integer> customerOrderInfosCount(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);


    /**
     * 根据IN单号获取子订单信息
     */
    @GetMapping("/customerOrderInfos/getOrderInfos")
    JsonResponse<List<OrderInfo>> getOrderInfoByCustomerOrderNo(@RequestParam(value = "parentOrderNo") String parentOrderNo);

    @RequestMapping(value = "/authSearch", method = RequestMethod.GET)
    public JsonResponse<Map<String,List>> authSearch(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);


    /**
     * 根据客户订单号查询客户订单
     *
     * @param customerOrderNos
     * @return
     */
    @PostMapping(OrderRouters.ORDERITEM_LIST_BY_ORDERNOS)
    JsonResponse<List<CustomerOrderItemExt>> listByOrderNos(@RequestBody List<String> customerOrderNos);

    @PostMapping(OrderRouters.CUSTOMERORDERINFO_SELECT_ONE_JSON)
    JsonResponse<CustomerOrderInfo> searchOne(@RequestParam("customerOrderInfo") String customerOrderInfo);

    /**
     * 修改客户订单、子单、task、订单详情、订单地址 数据
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.UPDATE_ORDER_TASK_AND_ITEM, method = RequestMethod.POST)
    JsonResponse<Integer> updateOrderTaskAndItem(@RequestBody List<OrderTaskAndItemUpdateRequest> requestList);

    @PostMapping(OrderRouters.CUSTOMERORDERINFO_SELECT_ONE_POST)
    JsonResponse<CustomerOrderInfo> searchOnePost(@RequestBody CustomerOrderInfo customerOrderInfo);
}
