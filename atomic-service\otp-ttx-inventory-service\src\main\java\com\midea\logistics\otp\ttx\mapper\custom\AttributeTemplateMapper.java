package com.midea.logistics.otp.ttx.mapper.custom;

import com.midea.logistics.otp.ttx.inventory.domain.bean.AttributeTemplate;
import com.mideaframework.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ©Copyright ©1968-2021 Midea Group,IT
 * FileName: ConfigDetailMapper
 * Author: caizt
 * Date: 2021-3-4 10:26:21
 * Description:TTX-数据字典
 */
@Mapper
public interface AttributeTemplateMapper extends BaseMapper<AttributeTemplate> {
    List<AttributeTemplate> queryAttributeTemplate(@Param("code") String code, @Param("warehouseCode") String warehouseCode);

}
