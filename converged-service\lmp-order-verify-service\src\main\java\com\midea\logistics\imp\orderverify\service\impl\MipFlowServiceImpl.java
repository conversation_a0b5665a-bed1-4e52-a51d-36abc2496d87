package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.MipFlowService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.enums.CustomerOrderStatus;
import com.midea.logistics.otp.enums.ExcuteStatus;
import com.midea.logistics.otp.order.common.fegin.MipExecutionLogFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.MipExecutionLog;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MipFlowServiceImpl implements MipFlowService {

    @Autowired
    private MipExecutionLogFeign mipExecutionLogFeign;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    /**
     * MIP流程审核
     * 手工创建（来源系统是:手工单）的数量调整入库单、
     * 数量调整出库单、状态调整入库单、状态调整出库单、
     * 状态调整单，则读取客户配置 ，若客户配置的【调账是否流程管控】为是，
     * 则触发走MIP流程， 默认配置为是， 如果有个别用户不需要则单独根据客户配置。
     * @param customerOrderInfoExt
     * @return
     */
    @Override
    @ZeebeFlow("MIP_EXECUTION_AUDIT")
    public CustomerOrderInfoExt mipExecutionAudit(CustomerOrderInfoExt customerOrderInfoExt) {
        //校验
        if (customerOrderInfoExt == null) {
            throw BusinessException.fail("订单不存在");
        }
        CustomerOrderInfo custromOrderInfo = customerOrderInfoExt;
        String orderNo = customerOrderInfoExt.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            throw BusinessException.fail("订单号不能为空");
        }
        if (ToolUtils.isEmpty(custromOrderInfo.getMipFlag()) || CommonConstant.FLAG_NO.equals(custromOrderInfo.getMipFlag())){
            FlowListenerParam.success("不需要走MIP流程");
            return customerOrderInfoExt;
        }
        //查询MIP审核状态
        MipExecutionLog mipLog = new MipExecutionLog();
        mipLog.setOrderNo(orderNo);
        JsonResponse<MipExecutionLog> resultAuditResponse = mipExecutionLogFeign.selectOne(mipLog);
        if (BaseCodeEnum.SUCCESS.getCode().equals(resultAuditResponse.getCode()) && null != resultAuditResponse.data()){
            MipExecutionLog mipExecutionLog = resultAuditResponse.data();
            mipExecutionLog.setOrderType(custromOrderInfo.getOrderType());
            mipExecutionLogFeign.update(mipExecutionLog.getId(),mipExecutionLog);
            if (CommonConstant.SU_NOTICE_STATUS_PASS.equals(mipExecutionLog.getAuditStatus())){
                custromOrderInfo.setOrderStatus(CustomerOrderStatus.AUDITED_MIP.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(custromOrderInfo, "MIP审核");
                FlowListenerParam.success("MIP审核已通过");
                return customerOrderInfoExt;
            }else if(CommonConstant.SU_NOTICE_STATUS_BH.equals(mipExecutionLog.getAuditStatus())){
                // 2023年4月4日16:35:48 丽红 驳回还是MIP审核中
                custromOrderInfo.setOrderStatus(CustomerOrderStatus.AUDIT_MIP_ING.getKey());
                custromOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(custromOrderInfo, "MIP审核");
                throw BusinessException.fail("等待MIP审核");
            }else if(CommonConstant.SU_NOTICE_STATUS_FQ.equals(mipExecutionLog.getAuditStatus())){
                custromOrderInfo.setOrderStatus(CustomerOrderStatus.AUDIT_MIP_FAILE.getKey());
                custromOrderInfo.setExcuteStatus(ExcuteStatus.CLOSED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(custromOrderInfo, "MIP审核");
                throw BusinessException.fail("MIP审核已废弃");
            } else {
                custromOrderInfo.setOrderStatus(CustomerOrderStatus.AUDIT_MIP_ING.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(custromOrderInfo, "MIP审核");
                throw BusinessException.fail("等待MIP审核");
            }
        }
        custromOrderInfo.setOrderStatus(CustomerOrderStatus.AUDIT_MIP_ING.getKey());
        lmpOrderFlowHelper.updateCustomerOrderInfo(custromOrderInfo, "MIP审核");


        throw BusinessException.fail("等待MIP审核");
    }
}
