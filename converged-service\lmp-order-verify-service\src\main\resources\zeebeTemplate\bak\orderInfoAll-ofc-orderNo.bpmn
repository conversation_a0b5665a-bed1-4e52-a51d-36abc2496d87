<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-ofc" name="子订单流程-ofc" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0opo74t</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0opo74t" sourceRef="StartEvent_1" targetRef="Activity_0smupof" />
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_032c74y</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1foo88v" sourceRef="Activity_1es0apr" targetRef="Activity_1xd43z3" />
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_0c8zm3c</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:sequenceFlow id="Flow_0p8hiid" sourceRef="Activity_087r4hi" targetRef="Gateway_0aeq2y6" />
    <bpmn:exclusiveGateway id="Gateway_0smudh3" name="是否获取运单号1" default="Flow_1e8tnvs">
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:incoming>Flow_1m1o9v2</bpmn:incoming>
      <bpmn:incoming>Flow_06arwgx</bpmn:incoming>
      <bpmn:outgoing>Flow_0ss94pu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1e8tnvs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Gateway_0smudh3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0n3a8um" name="是否宅配" default="Flow_0aa1he6">
      <bpmn:incoming>Flow_0ss94pu</bpmn:incoming>
      <bpmn:incoming>Flow_1g901df</bpmn:incoming>
      <bpmn:outgoing>Flow_1riwlkp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0aa1he6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ss94pu" name="no" sourceRef="Gateway_0smudh3" targetRef="Gateway_0n3a8um">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=((orderInfo.businessMode="B2B" and orderInfo.deliveryType="ZT") or (orderInfo.businessMode="B2C" and orderInfo.orderType="PO" and orderInfo.deliveryType="ZT") or (orderInfo.orderType="PO" and orderInfo.deliveryType="EXPRESS"))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e8tnvs" name="yes" sourceRef="Gateway_0smudh3" targetRef="Activity_1fv890g" />
    <bpmn:sequenceFlow id="Flow_1g901df" sourceRef="Activity_1fv890g" targetRef="Gateway_0n3a8um" />
    <bpmn:sequenceFlow id="Flow_1riwlkp" name="yes" sourceRef="Gateway_0n3a8um" targetRef="Activity_0sog07d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="DOT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0exerix" name="是否纯运输" default="Flow_1t7g5e2">
      <bpmn:incoming>Flow_0aa1he6</bpmn:incoming>
      <bpmn:incoming>Flow_18iv5af</bpmn:incoming>
      <bpmn:outgoing>Flow_0y71mjc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t7g5e2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0y71mjc" name="yes" sourceRef="Gateway_0exerix" targetRef="Activity_1cv7k8o">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!="YS") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_02vnb2c" name="非自提" default="Flow_15pwtiu">
      <bpmn:incoming>Flow_1t7g5e2</bpmn:incoming>
      <bpmn:incoming>Flow_1wybauv</bpmn:incoming>
      <bpmn:outgoing>Flow_1hgo4ci</bpmn:outgoing>
      <bpmn:outgoing>Flow_15pwtiu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1t7g5e2" name="no" sourceRef="Gateway_0exerix" targetRef="Gateway_02vnb2c" />
    <bpmn:sequenceFlow id="Flow_1hgo4ci" name="yes" sourceRef="Gateway_02vnb2c" targetRef="Activity_0qzipw6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wybauv" sourceRef="Activity_1cv7k8o" targetRef="Gateway_02vnb2c" />
    <bpmn:sequenceFlow id="Flow_15pwtiu" name="no" sourceRef="Gateway_02vnb2c" targetRef="Activity_0f3fs9t" />
    <bpmn:sequenceFlow id="Flow_0n0vsfo" sourceRef="Activity_0qzipw6" targetRef="Activity_0f3fs9t" />
    <bpmn:sequenceFlow id="Flow_01mqa55" sourceRef="Activity_0f3fs9t" targetRef="Gateway_195jet3" />
    <bpmn:sequenceFlow id="Flow_039eniu" sourceRef="Activity_0tlqh7q" targetRef="Gateway_1izoota" />
    <bpmn:sequenceFlow id="Flow_047dg39" sourceRef="Gateway_1hquluh" targetRef="Gateway_0uv2a5c" />
    <bpmn:sequenceFlow id="Flow_12krfqa" sourceRef="Gateway_1hquluh" targetRef="Activity_16okhsc" />
    <bpmn:sequenceFlow id="Flow_1cof20d" sourceRef="Gateway_1hquluh" targetRef="Activity_195th7r" />
    <bpmn:sequenceFlow id="Flow_1fnx882" sourceRef="Activity_0wtgpmy" targetRef="Gateway_1n1cwyd" />
    <bpmn:sequenceFlow id="Flow_032c74y" sourceRef="Gateway_1n1cwyd" targetRef="Event_14d5skw" />
    <bpmn:sequenceFlow id="Flow_0mvp66j" sourceRef="Activity_16okhsc" targetRef="Gateway_1n1cwyd" />
    <bpmn:sequenceFlow id="Flow_0slmma7" sourceRef="Activity_1lzf3b0" targetRef="Gateway_1n1cwyd" />
    <bpmn:sequenceFlow id="Flow_13j2a01" sourceRef="Activity_195th7r" targetRef="Gateway_1n1cwyd" />
    <bpmn:serviceTask id="Activity_1es0apr" name="质押">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="pledge" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/pledgeCheck/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fnxy33</bpmn:incoming>
      <bpmn:outgoing>Flow_1foo88v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:parallelGateway id="Gateway_1hquluh" name="并行">
      <bpmn:incoming>Flow_1gehrtc</bpmn:incoming>
      <bpmn:outgoing>Flow_047dg39</bpmn:outgoing>
      <bpmn:outgoing>Flow_12krfqa</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cof20d</bpmn:outgoing>
      <bpmn:outgoing>Flow_03g4a4v</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Activity_0tlqh7q" name="时效产品">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="aging" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/agingParse/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_161nz2d</bpmn:incoming>
      <bpmn:outgoing>Flow_039eniu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0smupof" name="大小电解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="orderOutCollabWh" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/setOrderOutCollabWh/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0opo74t</bpmn:incoming>
      <bpmn:outgoing>Flow_0fnxy33</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1xd43z3" name="爆仓">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="isOutArea" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/isOutArea/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1foo88v</bpmn:incoming>
      <bpmn:outgoing>Flow_0c8zm3c</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
          <zeebe:output source="=responseBody.deliveryType" target="orderInfo.deliveryType" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_0p8hiid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e8tnvs</bpmn:incoming>
      <bpmn:outgoing>Flow_1g901df</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0sog07d" name="计费组解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bmsGroup" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/materialGroup5Verify/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1riwlkp</bpmn:incoming>
      <bpmn:outgoing>Flow_18iv5af</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1cv7k8o" name="装卸计费规则">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bmsFee" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/loadingFeeAnalyze/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0y71mjc</bpmn:incoming>
      <bpmn:outgoing>Flow_1wybauv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0qzipw6" name="计费业务类型解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bms" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/analysisBusineesFee/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hgo4ci</bpmn:incoming>
      <bpmn:outgoing>Flow_0n0vsfo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0f3fs9t" name="合同校验">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/contractVerification/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_15pwtiu</bpmn:incoming>
      <bpmn:incoming>Flow_0n0vsfo</bpmn:incoming>
      <bpmn:outgoing>Flow_01mqa55</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0wtgpmy" name="同步BMS">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bms" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/syncBms/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_16ydzzw</bpmn:incoming>
      <bpmn:outgoing>Flow_1fnx882</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_16okhsc" name="推送查单系统">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/pushLots/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_12krfqa</bpmn:incoming>
      <bpmn:outgoing>Flow_0mvp66j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1lzf3b0" name="下发末端配送">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="net" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/orderIssued/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_03g4a4v</bpmn:incoming>
      <bpmn:outgoing>Flow_0slmma7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_195th7r" name="生成任务">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="task" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/sentTask/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1cof20d</bpmn:incoming>
      <bpmn:outgoing>Flow_13j2a01</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0uv2a5c" name="是否自提" default="Flow_16ydzzw">
      <bpmn:incoming>Flow_047dg39</bpmn:incoming>
      <bpmn:outgoing>Flow_16ydzzw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0721unt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_16ydzzw" name="no" sourceRef="Gateway_0uv2a5c" targetRef="Activity_0wtgpmy" />
    <bpmn:sequenceFlow id="Flow_0721unt" name="yes" sourceRef="Gateway_0uv2a5c" targetRef="Gateway_1n1cwyd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1n1cwyd" name="或门&#10;&#10;">
      <bpmn:incoming>Flow_1fnx882</bpmn:incoming>
      <bpmn:incoming>Flow_0mvp66j</bpmn:incoming>
      <bpmn:incoming>Flow_0slmma7</bpmn:incoming>
      <bpmn:incoming>Flow_13j2a01</bpmn:incoming>
      <bpmn:incoming>Flow_0721unt</bpmn:incoming>
      <bpmn:outgoing>Flow_032c74y</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fnxy33" sourceRef="Activity_0smupof" targetRef="Activity_1es0apr" />
    <bpmn:exclusiveGateway id="Gateway_195jet3" name="是否自提" default="Flow_161nz2d">
      <bpmn:incoming>Flow_01mqa55</bpmn:incoming>
      <bpmn:outgoing>Flow_161nz2d</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mwtbnu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161nz2d" name="no" sourceRef="Gateway_195jet3" targetRef="Activity_0tlqh7q" />
    <bpmn:sequenceFlow id="Flow_1mwtbnu" name="yes" sourceRef="Gateway_195jet3" targetRef="Gateway_1izoota">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and (orderInfo.deliveryType="ZT" or orderInfo.deliveryType="EXPRESS" or derInfo.orderType="DO")) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1izoota" name="中转">
      <bpmn:incoming>Flow_039eniu</bpmn:incoming>
      <bpmn:incoming>Flow_1mwtbnu</bpmn:incoming>
      <bpmn:outgoing>Flow_1gehrtc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1gehrtc" sourceRef="Gateway_1izoota" targetRef="Gateway_1hquluh" />
    <bpmn:serviceTask id="Activity_09m8o30" name="网点解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisNetRange/byOrderNo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo.orderNo" target="parameterBody.orderNo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_19skgsw</bpmn:incoming>
      <bpmn:outgoing>Flow_1m1o9v2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0aeq2y6" name="是否网点配送" default="Flow_06arwgx">
      <bpmn:incoming>Flow_0p8hiid</bpmn:incoming>
      <bpmn:outgoing>Flow_06arwgx</bpmn:outgoing>
      <bpmn:outgoing>Flow_19skgsw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_06arwgx" name="no" sourceRef="Gateway_0aeq2y6" targetRef="Gateway_0smudh3" />
    <bpmn:sequenceFlow id="Flow_1m1o9v2" sourceRef="Activity_09m8o30" targetRef="Gateway_0smudh3" />
    <bpmn:sequenceFlow id="Flow_19skgsw" name="yes" sourceRef="Gateway_0aeq2y6" targetRef="Activity_09m8o30">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="NET") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_03g4a4v" sourceRef="Gateway_1hquluh" targetRef="Activity_1lzf3b0" />
    <bpmn:sequenceFlow id="Flow_0c8zm3c" sourceRef="Activity_1xd43z3" targetRef="Gateway_0mzkgmp" />
    <bpmn:sequenceFlow id="Flow_0aa1he6" sourceRef="Gateway_0n3a8um" targetRef="Gateway_0exerix" />
    <bpmn:sequenceFlow id="Flow_18iv5af" sourceRef="Activity_0sog07d" targetRef="Gateway_0exerix" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-ofc">
      <bpmndi:BPMNEdge id="Flow_18iv5af_di" bpmnElement="Flow_18iv5af">
        <di:waypoint x="1624" y="290" />
        <di:waypoint x="1675" y="290" />
        <di:waypoint x="1675" y="497" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aa1he6_di" bpmnElement="Flow_0aa1he6">
        <di:waypoint x="1524" y="177" />
        <di:waypoint x="1675" y="177" />
        <di:waypoint x="1675" y="497" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c8zm3c_di" bpmnElement="Flow_0c8zm3c">
        <di:waypoint x="566" y="177" />
        <di:waypoint x="741" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03g4a4v_di" bpmnElement="Flow_03g4a4v">
        <di:waypoint x="907" y="547" />
        <di:waypoint x="907" y="610" />
        <di:waypoint x="749" y="610" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19skgsw_di" bpmnElement="Flow_19skgsw">
        <di:waypoint x="995" y="202" />
        <di:waypoint x="995" y="271" />
        <di:waypoint x="1048" y="271" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1002" y="234" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m1o9v2_di" bpmnElement="Flow_1m1o9v2">
        <di:waypoint x="1148" y="271" />
        <di:waypoint x="1280" y="271" />
        <di:waypoint x="1280" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06arwgx_di" bpmnElement="Flow_06arwgx">
        <di:waypoint x="1020" y="177" />
        <di:waypoint x="1275" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1092" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gehrtc_di" bpmnElement="Flow_1gehrtc">
        <di:waypoint x="960" y="522" />
        <di:waypoint x="932" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mwtbnu_di" bpmnElement="Flow_1mwtbnu">
        <di:waypoint x="1220" y="497" />
        <di:waypoint x="1220" y="460" />
        <di:waypoint x="1010" y="460" />
        <di:waypoint x="1010" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1130" y="442" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161nz2d_di" bpmnElement="Flow_161nz2d">
        <di:waypoint x="1195" y="522" />
        <di:waypoint x="1145" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1164" y="504" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fnxy33_di" bpmnElement="Flow_0fnxy33">
        <di:waypoint x="297" y="177" />
        <di:waypoint x="327" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0721unt_di" bpmnElement="Flow_0721unt">
        <di:waypoint x="827" y="410" />
        <di:waypoint x="827" y="380" />
        <di:waypoint x="551" y="380" />
        <di:waypoint x="551" y="497" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="644" y="360" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16ydzzw_di" bpmnElement="Flow_16ydzzw">
        <di:waypoint x="802" y="435" />
        <di:waypoint x="749" y="435" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="417" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13j2a01_di" bpmnElement="Flow_13j2a01">
        <di:waypoint x="649" y="716" />
        <di:waypoint x="551" y="716" />
        <di:waypoint x="551" y="547" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0slmma7_di" bpmnElement="Flow_0slmma7">
        <di:waypoint x="649" y="610" />
        <di:waypoint x="551" y="610" />
        <di:waypoint x="551" y="547" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mvp66j_di" bpmnElement="Flow_0mvp66j">
        <di:waypoint x="649" y="522" />
        <di:waypoint x="576" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_032c74y_di" bpmnElement="Flow_032c74y">
        <di:waypoint x="526" y="522" />
        <di:waypoint x="445" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fnx882_di" bpmnElement="Flow_1fnx882">
        <di:waypoint x="649" y="435" />
        <di:waypoint x="551" y="435" />
        <di:waypoint x="551" y="497" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cof20d_di" bpmnElement="Flow_1cof20d">
        <di:waypoint x="907" y="547" />
        <di:waypoint x="907" y="716" />
        <di:waypoint x="749" y="716" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12krfqa_di" bpmnElement="Flow_12krfqa">
        <di:waypoint x="882" y="522" />
        <di:waypoint x="749" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_047dg39_di" bpmnElement="Flow_047dg39">
        <di:waypoint x="907" y="497" />
        <di:waypoint x="907" y="435" />
        <di:waypoint x="852" y="435" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_039eniu_di" bpmnElement="Flow_039eniu">
        <di:waypoint x="1045" y="522" />
        <di:waypoint x="1010" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01mqa55_di" bpmnElement="Flow_01mqa55">
        <di:waypoint x="1280" y="522" />
        <di:waypoint x="1245" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n0vsfo_di" bpmnElement="Flow_0n0vsfo">
        <di:waypoint x="1400" y="632" />
        <di:waypoint x="1330" y="632" />
        <di:waypoint x="1330" y="562" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15pwtiu_di" bpmnElement="Flow_15pwtiu">
        <di:waypoint x="1495" y="522" />
        <di:waypoint x="1380" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1432" y="504" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wybauv_di" bpmnElement="Flow_1wybauv">
        <di:waypoint x="1625" y="632" />
        <di:waypoint x="1540" y="632" />
        <di:waypoint x="1540" y="527" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hgo4ci_di" bpmnElement="Flow_1hgo4ci">
        <di:waypoint x="1520" y="547" />
        <di:waypoint x="1520" y="632" />
        <di:waypoint x="1500" y="632" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1527" y="587" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t7g5e2_di" bpmnElement="Flow_1t7g5e2">
        <di:waypoint x="1650" y="522" />
        <di:waypoint x="1545" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1591" y="504" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y71mjc_di" bpmnElement="Flow_0y71mjc">
        <di:waypoint x="1675" y="547" />
        <di:waypoint x="1675" y="592" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1682" y="567" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1riwlkp_di" bpmnElement="Flow_1riwlkp">
        <di:waypoint x="1499" y="202" />
        <di:waypoint x="1499" y="290" />
        <di:waypoint x="1524" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1506" y="243" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g901df_di" bpmnElement="Flow_1g901df">
        <di:waypoint x="1454" y="290" />
        <di:waypoint x="1480" y="290" />
        <di:waypoint x="1480" y="183" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e8tnvs_di" bpmnElement="Flow_1e8tnvs">
        <di:waypoint x="1300" y="202" />
        <di:waypoint x="1300" y="290" />
        <di:waypoint x="1354" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1307" y="243" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ss94pu_di" bpmnElement="Flow_0ss94pu">
        <di:waypoint x="1325" y="177" />
        <di:waypoint x="1474" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1393" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="766" y="152" />
        <di:waypoint x="766" y="110" />
        <di:waypoint x="1280" y="110" />
        <di:waypoint x="1280" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="767" y="122" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p8hiid_di" bpmnElement="Flow_0p8hiid">
        <di:waypoint x="939" y="177" />
        <di:waypoint x="970" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="791" y="177" />
        <di:waypoint x="839" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="795" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1foo88v_di" bpmnElement="Flow_1foo88v">
        <di:waypoint x="427" y="177" />
        <di:waypoint x="466" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0opo74t_di" bpmnElement="Flow_0opo74t">
        <di:waypoint x="172" y="177" />
        <di:waypoint x="197" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="136" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="143" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="409" y="504" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="416" y="547" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="741" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="753" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0smudh3_di" bpmnElement="Gateway_0smudh3" isMarkerVisible="true">
        <dc:Bounds x="1275" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1286" y="127" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0n3a8um_di" bpmnElement="Gateway_0n3a8um" isMarkerVisible="true">
        <dc:Bounds x="1474" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1477" y="128" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0exerix_di" bpmnElement="Gateway_0exerix" isMarkerVisible="true">
        <dc:Bounds x="1650" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1682" y="495" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02vnb2c_di" bpmnElement="Gateway_02vnb2c" isMarkerVisible="true">
        <dc:Bounds x="1495" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1505" y="467" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01lp2qq_di" bpmnElement="Activity_1es0apr">
        <dc:Bounds x="327" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02b8aw8_di" bpmnElement="Gateway_1hquluh">
        <dc:Bounds x="882" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="909" y="554" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tz6c6h_di" bpmnElement="Activity_0tlqh7q">
        <dc:Bounds x="1045" y="482" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_059zzl3_di" bpmnElement="Activity_0smupof">
        <dc:Bounds x="197" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1q12own_di" bpmnElement="Activity_1xd43z3">
        <dc:Bounds x="466" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="839" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="1354" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17q9w4e_di" bpmnElement="Activity_0sog07d">
        <dc:Bounds x="1524" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lmvyr1_di" bpmnElement="Activity_1cv7k8o">
        <dc:Bounds x="1625" y="592" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_023kpgh_di" bpmnElement="Activity_0qzipw6">
        <dc:Bounds x="1400" y="592" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0h86k46_di" bpmnElement="Activity_0f3fs9t">
        <dc:Bounds x="1280" y="482" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09uaaug_di" bpmnElement="Activity_0wtgpmy">
        <dc:Bounds x="649" y="395" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ia4nmi_di" bpmnElement="Activity_16okhsc">
        <dc:Bounds x="649" y="482" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xvdpcc_di" bpmnElement="Activity_1lzf3b0">
        <dc:Bounds x="649" y="570" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1aomcsq_di" bpmnElement="Activity_195th7r">
        <dc:Bounds x="649" y="676" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uv2a5c_di" bpmnElement="Gateway_0uv2a5c" isMarkerVisible="true">
        <dc:Bounds x="802" y="410" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="806" y="467" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_193q3lp_di" bpmnElement="Gateway_1n1cwyd" isMarkerVisible="true">
        <dc:Bounds x="526" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="554" y="554" width="21" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_195jet3_di" bpmnElement="Gateway_195jet3" isMarkerVisible="true">
        <dc:Bounds x="1195" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1199" y="554" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1izoota_di" bpmnElement="Gateway_1izoota" isMarkerVisible="true">
        <dc:Bounds x="960" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="974" y="554" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09m8o30_di" bpmnElement="Activity_09m8o30">
        <dc:Bounds x="1048" y="231" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0aeq2y6_di" bpmnElement="Gateway_0aeq2y6" isMarkerVisible="true">
        <dc:Bounds x="970" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="964" y="122" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
