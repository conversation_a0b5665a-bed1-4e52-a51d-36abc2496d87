package com.midea.logistics.imp.orderverify.rest;

import com.midea.logistics.imp.orderverify.service.EcommerceCategoriesAnalysisTag;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.WfrTransferService;
import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: CargoRightTransferRest
 * @description:
 * @author: kongly1
 * @date: 2020-11-13 15:30
 */
@RestController
@Slf4j
public class WfrTransferRest {

    @Autowired
    private WfrTransferService wfrCargoRightTransfer;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private EcommerceCategoriesAnalysisTag ecommerceCategoriesAnalysisTag;

    
    /**
    * @description: 无忧零售户货权转移
    * @param: [customerOrderInfoExt]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2024/2/20 9:51
    */
    @PostMapping("/lmp/validate/wfr/cargo/right/transfer")
    public JsonResponse wfrCargoRightTransfer(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {
        try {
            return JsonResponse.success(wfrCargoRightTransfer.wfrCargoRightTransfer(customerOrderInfoExt));
        } catch (Exception e) {
            throw BusinessException.fail("货权转移失败，失败原因:" + e.getMessage());
        }
    }
    
    /**
    * @description:  无忧零售户货权转移 - byOrderNo
    * @param: [customerOrderInfoExt]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2024/2/20 9:50
    */
    @PostMapping("/lmp/validate/wfr/cargo/right/transfer/byOrderNo")
    public JsonResponse wfrCargoRightTransferByOrderNo(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {
        try {
            CustomerOrderInfoExt c = lmpOrderFlowHelper.getCustomerOrderInfoExt(customerOrderInfoExt.getOrderNo());
            c.setCipherTextSystem(customerOrderInfoExt.isCipherTextSystem());
            return JsonResponse.success(wfrCargoRightTransfer.wfrCargoRightTransfer(c));
        } catch (Exception e) {
            throw BusinessException.fail("货权转移失败，失败原因:" + e.getMessage());
        }
    }
    
    /**
     * 无忧零售自动分仓
     * @return
     */
    @PostMapping(OrderConvergedRouters.WFR_SEPARATE_WAREHOUSE_AUTOMATIC)
    public JsonResponse automatic(@RequestBody CustomerOrderInfoExt entity) {
        try {
            return JsonResponse.success(wfrCargoRightTransfer.automatic(entity));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("无忧零售自动分仓失败，失败原因:" + e.getMessage());
        }
    }
    /**
     * 无忧零售自动分仓根据OrderNo
     * @return
     */
    @PostMapping(OrderConvergedRouters.WFR_SEPARATE_WAREHOUSE_AUTOMATIC_BY_ORDERNO)
    public JsonResponse automaticByOrderNo(@RequestBody CustomerOrderInfoExt entity) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(entity.getOrderNo());
            return JsonResponse.success(wfrCargoRightTransfer.automatic(customerOrderInfoExt));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("无忧零售自动分仓失败，失败原因:" + e.getMessage());
        }
    }


    /**
     * 电商分类解析-异步
     */
    @PostMapping("/wfrAsyncEcommerceCategoriesAnalysisTag")
    public JsonResponse asyncEcommerceCategoriesAnalysisTag(@RequestBody CustomerOrderInfoExt entity){
        return JsonResponse.success(ecommerceCategoriesAnalysisTag.asyncAnalysis(entity));

    }
}
