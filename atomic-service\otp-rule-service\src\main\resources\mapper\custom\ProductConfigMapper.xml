<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.ProductConfigExtMapper">

    <sql id="searchFieldsSql2">
        `product_line_code` AS  productLineCode,
        `bussiness_mode` AS  bussinessMode,
        `order_type` AS  orderType,
        `product_aging` AS  productAging,
        `sender_province_name` AS  senderProvinceName,
        `sender_city_code` AS  senderCityCode,
        `receiver_town_code` AS  receiverTownCode,
        `receiver_district_code` AS  receiverDistrictCode,
        `receiver_city_code` AS  receiverCityCode,
        `customer_code` AS  customerCode,
        `remark` AS  remark,
        `tenant_code` AS  tenantCode,
        `volume_from` AS  volumeFrom,
        `product_name` AS  productName,
        `delete_flag` AS  deleteFlag,
        `sender_town_code` AS  senderTownCode,
        `weight_to` AS  weightTo,
        `order_end_time` AS  orderEndTime,
        `id` AS  id,
        `sender_district_code` AS  senderDistrictCode,
        `receiver_province_name` AS  receiverProvinceName,
        `weight_from` AS  weightFrom,
        `sender_city_name` AS  senderCityName,
        `customer_group` AS  customerGroup,
        `create_user_code` AS  createUserCode,
        `delivery_type` AS  deliveryType,
        `update_time` AS  updateTime,
        `version` AS  version,
        `customer_name` AS  customerName,
        `update_user_code` AS  updateUserCode,
        `product_code` AS  productCode,
        `customer_group_name` AS  customerGroupName,
        `create_time` AS  createTime,
        `receiver_city_name` AS  receiverCityName,
        `product_line_name` AS  productLineName,
        `volume_to` AS  volumeTo,
        `sender_town_name` AS  senderTownName,
        `receiver_district_name` AS  receiverDistrictName,
        `sender_district_name` AS  senderDistrictName,
        `receiver_province_code` AS  receiverProvinceCode,
        `receiver_town_name` AS  receiverTownName,
        `sender_province_code` AS  senderProvinceCode,
        `order_start_time` AS  orderStartTime
    </sql>

    <sql id="whereFieldsSql2">
        where delete_flag=0
        <if test="productCode !=null and productCode != ''">
            and `product_code` =#{productCode}
        </if>
        <if test="bussinessMode !=null and bussinessMode != ''">
            and (`bussiness_mode` =#{bussinessMode} or `bussiness_mode` is null or `bussiness_mode`='')
        </if>
        <if test="orderType !=null and orderType != ''">
            and (`order_type` =#{orderType} or `order_type` is null or `order_type`='')
        </if>
        <if test="receiverTownCode !=null and receiverTownCode != ''">
            and `receiver_town_code` =#{receiverTownCode}
        </if>
        <if test="senderTownCode !=null and senderTownCode != ''">
            and `sender_town_code` =#{senderTownCode}
        </if>
        <if test="customerCode !=null and customerCode != '' and customerGroup !=null and customerGroup != '' ">
            and (`customer_code` =#{customerCode} or `customer_group` =#{customerGroup})
        </if>
        <if test="customerCode ==null  and customerGroup ==null  ">
            and (`customer_code` is null or `customer_code`='' ) and (`customer_group` is null or `customer_group`='')
        </if>
        <if test="tenantCode !=null and tenantCode != ''">
            and `tenant_code` =#{tenantCode}
        </if>
        <if test="volumeFrom !=null">
            and (`volume_from` &lt;=#{volumeFrom} or `volume_from` is null or `volume_from`='')
        </if>
        <if test="weightTo !=null">
            and (`weight_to` &gt;=#{weightTo} or `weight_to` is null or `weight_to`='' )
        </if>
        <if test="weightFrom !=null">
            and (`weight_from` &lt;=#{weightFrom} or `weight_from` is null or `weight_from`='')
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and (`delivery_type` =#{deliveryType} or `delivery_type` is null  OR `delivery_type`='')
        </if>
        <if test="volumeTo !=null">
            and (`volume_to` &gt;=#{volumeTo} or `volume_to` is null or `volume_to`='')
        </if>
        <!--<if test="orderStartTime !=null ">
            and `order_start_time` &lt; #{orderStartTime} and `order_end_time` &gt; #{orderStartTime}
        </if>-->

    </sql>

    <select id="selectByProductConfig" resultType="com.midea.logistics.otp.rule.domain.bean.ProductConfig">
        select
        <include refid="searchFieldsSql2"/>
        from product_config t
        <include refid="whereFieldsSql2"/>
    </select>


</mapper>