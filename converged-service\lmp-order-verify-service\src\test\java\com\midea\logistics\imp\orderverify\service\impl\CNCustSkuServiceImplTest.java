package com.midea.logistics.imp.orderverify.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.annto.framework.easyjunit.disable.apollo.EasyJunitDisableApollo;
import com.annto.framework.easyjunit.mariadb.EasyJunitMariadb;
import com.annto.framework.easyjunit.redis.EasyJunitRedis;
import com.annto.framework.easyjunit.rocketmq.EasyJunitRocketMQ;
import com.midea.logistics.dtc.dto.CustSkuInfo;
import com.midea.logistics.imp.orderverify.LmpOrderVerifyService;
import com.midea.logistics.otp.common.feign.convergedfeign.order.BopServiceFeign;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.common.service.CNCustSkuService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@EasyJunitDisableApollo
@EasyJunitRocketMQ
@EasyJunitMariadb
@EasyJunitRedis
@ActiveProfiles("local")
@TestPropertySource(locations = "classpath:local.properties")
@SpringBootTest(classes = LmpOrderVerifyService.class)
public class CNCustSkuServiceImplTest {

    @Autowired
    private CNCustSkuService cnCustSkuService;
    @Autowired
    private BopServiceFeign bopServiceFeign;

    @Test
    public void setCnCustSkuServiceTest(){

        CustSkuInfo custSkuInfo = new CustSkuInfo();
        custSkuInfo.setSender(SourceSystem.CAINIAO.getKey());
        custSkuInfo.setCustomerCode("2684357295");
        custSkuInfo.setSkuCode("ZT202502081738995136318");
        JSONObject result = cnCustSkuService.queryCNCustSkuCode(custSkuInfo);
        System.out.println(result.getString("custSkuCode"));

    }

}
