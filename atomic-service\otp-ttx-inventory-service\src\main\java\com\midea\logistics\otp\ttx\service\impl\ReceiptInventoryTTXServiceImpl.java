package com.midea.logistics.otp.ttx.service.impl;

import com.google.common.collect.Lists;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.ttx.inventory.domain.bean.*;
import com.midea.logistics.otp.ttx.inventory.domain.request.ReceiptDataTTXRequest;
import com.midea.logistics.otp.ttx.inventory.domain.response.ReceiptSaveTTXItem;
import com.midea.logistics.otp.ttx.mapper.common.ReceiptDetailMapper;
import com.midea.logistics.otp.ttx.mapper.custom.AttributeTemplateMapper;
import com.midea.logistics.otp.ttx.mapper.custom.ConfigDetailMapper;
import com.midea.logistics.otp.ttx.mapper.common.ReceiptHeaderMapper;
import com.midea.logistics.otp.ttx.mapper.custom.ItemMapper;
import com.midea.logistics.otp.ttx.service.IReceiptInventoryTTXService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: ReceiptInventoryTTXServiceImpl
 * Author: caizt
 * Date: 2019-8-22 8:45:52
 * Description:TTX-库存入库单导入实现类
 */
@Service
@Slf4j
public class ReceiptInventoryTTXServiceImpl implements IReceiptInventoryTTXService {
    private static final String PI = "PI";
    @Autowired
    Validatorhelper validatorhelper;
    @Autowired
    private ReceiptHeaderMapper receiptHeaderMapper;
    @Autowired
    private ReceiptDetailMapper receiptDetailMapper;
    @Autowired
    private ConfigDetailMapper configDetailMapper;
    @Autowired
    private ItemMapper itemMapper;
    @Autowired
    private AttributeTemplateMapper attributeTemplateMapper;



    public Integer saveBatch(ReceiptHeader rh){
        List<ReceiptDetail> details = rh.getDetails();
        Integer id = receiptHeaderMapper.save(rh);
        Date now = new Date();
        details.stream().forEach(detail-> {
            detail.setReceiptId(rh.getId());
            detail.setReceiptCode(rh.getCode());
            detail.setWarehouseCode(rh.getWarehouseCode());
            detail.setCompanyCode(rh.getCompanyCode());
            detail.setOpenQty(detail.getTotalQty());
            detail.setVersion(0L);
            detail.setItemListPrice(0D);
            detail.setItemNetPrice(0D);
            detail.setCreated(now);
            detail.setLastUpdated(now);
        });
        receiptDetailMapper.insertBatch(details);
        return id;
    }

    private List<ReceiptSaveTTXItem> convertItem(Map<String,ReceiptSaveTTXItem> map,List<ReceiptDataTTXRequest> requests,List<String> locationCodeList,Map<String,String> customerMsgMap){
        boolean flag = CollectionUtils.isEmpty(locationCodeList);
        List<ReceiptSaveTTXItem> responseList = Lists.newArrayList();
        if(flag && map.isEmpty()) {
              return responseList;
        }else if(flag) {
            return new ArrayList<>(map.values());
        }

        requests.stream().forEach(detail -> {
            StringBuilder bu = new StringBuilder();
            if (locationCodeList.contains(detail.getToLocation())) {
                bu.append(detail.getToLocation()).append("库位不存在,");
            }
            if(customerMsgMap.containsKey(detail.getToLocation())){
                bu.append(customerMsgMap.get(detail.getToLocation()));
            }
            String msg = bu.toString();
            if (StringUtils.isNotBlank(msg)) {
                ReceiptSaveTTXItem receiptSaveTTXItem = new ReceiptSaveTTXItem();
                receiptSaveTTXItem.setErpOrderLineNum(detail.getErpOrderLineNum());
                receiptSaveTTXItem.setItemCode(detail.getItemCode());
                receiptSaveTTXItem.setMsg(msg);
                responseList.add(receiptSaveTTXItem);
            }
        });
         return responseList;
    }

    /**
     *
     * @param requests
     * @param headerMap
     * @return
     */
    private JsonResponse convertReceiptData(List<ReceiptDataTTXRequest> requests,Map<String,ReceiptHeader> headerMap){
        if(CollectionUtils.isEmpty(requests)){
            throw BusinessException.fail("导入数据不能为空");
        }
        String warehouseCode = requests.get(0).getWarehouseCode();
        List<ConfigDetail> configDetails = configDetailMapper.queryConfigDetailBy(warehouseCode);
        if(CollectionUtils.isEmpty(configDetails)){
            throw BusinessException.fail("库存状态数据字典未配置"+warehouseCode);
        }
        Map<String,String> inventoryStsMap = configDetails.stream().collect(Collectors.toMap(ConfigDetail::getDescription, ConfigDetail::getIdentifier));
        Collection<String> inventorySts = inventoryStsMap.values();
        List<String> locationCodeList = Lists.newArrayList();
        Map<String,ReceiptSaveTTXItem> map = new HashMap<>();
        Map<String,String> customerMaps = new HashMap<>();

        for(ReceiptDataTTXRequest request:requests) {
            List<String> validate = validatorhelper.validate(request);
            if (!CollectionUtils.isEmpty(validate)) {
                throw new BusinessException(BaseCodeEnum.REQUEST_NULL.getCode(), validate.get(0));
            }
            ReceiptHeader rh = convertReceiptHeader( headerMap, request);



            ReceiptDetail detail= convertReceiptDetail(rh,request);
            rh.getDetails().add(detail);
            if(inventoryStsMap.containsKey(detail.getInventorySts())){
                detail.setInventorySts(inventoryStsMap.get(detail.getInventorySts()));
            }else if(!inventorySts.contains(detail.getInventorySts())){
                convertRespItem( map, detail,detail.getItemCode()+"未配置库存状态"+detail.getInventorySts() );
            }
            //校验商品
            checkItem(map, detail, warehouseCode ,rh);

            if(!locationCodeList.contains(detail.getToLocation())) {
                locationCodeList.add(detail.getToLocation());
            }

            convertLocation( detail, map , customerMaps,rh.getShipFromCode());
        }

        Map<String,String> customerMsgMap =  convetCustomerMap(customerMaps , warehouseCode);
        List<String> locationCode = configDetailMapper.queryLocationCode(warehouseCode,locationCodeList);
        locationCodeList.removeAll(locationCode);
        List<ReceiptSaveTTXItem> responseItems = convertItem( map,requests, locationCodeList,customerMsgMap);
        if(CollectionUtils.isNotEmpty(responseItems)){
            JsonResponse jsonResponse = new JsonResponse();
            jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
            jsonResponse.setData(responseItems);
            jsonResponse.setMsg("明细校验失败");
            return jsonResponse;
        }
        return null;
    }
    private void convertLocation(ReceiptDetail detail,Map<String,ReceiptSaveTTXItem> map ,Map<String,String> customerMaps,String customerCode){
        if(StringUtils.isNotBlank(customerCode)){
            if(customerMaps.containsKey(detail.getToLocation())){
                if(!StringUtils.equals(customerMaps.get(detail.getToLocation()),customerCode)){
                    convertRespItem( map, detail,detail.getToLocation()+"非正品库位存在不同客户编码，请到ttx检查"+customerCode);
                }
            }else{
                customerMaps.put(detail.getToLocation(),customerCode);
            }
            Integer count = configDetailMapper.queryInventoryCustomerCount(detail.getWarehouseCode(),detail.getCompanyCode(),customerCode,detail.getInventorySts());
            if(count != null && count <= 0 ){
                convertRespItem( map, detail,"未配置非正品客户对照，请到ttx检查"+customerCode);
            }
        }
    }

    private Map<String,String> convetCustomerMap(Map<String,String> customerMaps ,String warehouseCode){
        Map<String,String> customerMsgMap = new HashMap<>();
        if(!customerMaps.isEmpty()){
            for(Map.Entry<String, String> entry : customerMaps.entrySet()) {
                Integer count =  configDetailMapper.queryDiffLocationInventoryCount(warehouseCode,entry.getKey(),entry.getValue());
                if(count!=null && count > 0){
                    customerMsgMap.put(entry.getKey(),entry.getKey()+"非正品库位只能绑定空库位或相同客户编码，请到ttx检查"+entry.getValue());
                }
            }
        }
        return customerMsgMap;
    }

    private void checkItem(Map<String,ReceiptSaveTTXItem> map,ReceiptDetail detail,String warehouseCode ,ReceiptHeader rh){
        List<Item>  itemList = itemMapper.queryItem(detail.getItemCode(),warehouseCode,rh.getCompanyCode());
        if(CollectionUtils.isEmpty(itemList)){
            convertRespItem( map, detail,detail.getItemCode()+"商品未配置,请到ttx检查，或者检查模板数据是否正确" );
            return;
        }
        boolean m = detail.getManufactureDate()!=null;
        boolean e = detail.getExpirationDate() != null;
        boolean a = detail.getAgingDate() !=null;
        if(m || e || a){
            Item item = itemList.get(0);
            if(StringUtils.isBlank(item.getAttributeTemplateCode())){
                convertRespItem( map, detail,detail.getItemCode()+"商品属性模板为空，请检查模板数据" );
                return;
            }
            List<AttributeTemplate> attributeTemplateList =  attributeTemplateMapper.queryAttributeTemplate(item.getAttributeTemplateCode(),warehouseCode);
            if(CollectionUtils.isEmpty(attributeTemplateList)){
                convertRespItem( map, detail,detail.getItemCode()+"属性模板无数据，请检查模板数据" );
                return;
            }
            AttributeTemplate attributeTemplate = attributeTemplateList.get(0);
            if(m&&!(attributeTemplate.getManufactureDateActive()!=null&&attributeTemplate.getManufactureDateActive()==1)){
                convertRespItem( map, detail,detail.getItemCode()+"商品属性模板[生产日期]不是有效状态，请检查模板数据" );
                return;
            }
            if(e&&!(attributeTemplate.getExpirationDateActive()!=null&&attributeTemplate.getExpirationDateActive()==1)){
                convertRespItem( map, detail,detail.getItemCode()+"商品属性模板[失效日期]不是有效状态，请检查模板数据" );
                return;
            }
            if(a&&!(attributeTemplate.getAgingDateActive()!=null&&attributeTemplate.getAgingDateActive()==1)){
                convertRespItem( map, detail,detail.getItemCode()+"商品属性模板[入库日期]不是有效状态，请检查模板数据" );
                return;
            }

        }
    }

    private void convertRespItem(Map<String,ReceiptSaveTTXItem> map,ReceiptDetail detail,String msg ){
        ReceiptSaveTTXItem receiptSaveTTXItem = new ReceiptSaveTTXItem();
        receiptSaveTTXItem.setItemCode(detail.getItemCode());
        receiptSaveTTXItem.setErpOrderLineNum(detail.getErpOrderLineNum());
        receiptSaveTTXItem.setMsg(msg);
        map.put(detail.getErpOrderLineNum(),receiptSaveTTXItem);
    }
    private ReceiptDetail convertReceiptDetail(ReceiptHeader rh,ReceiptDataTTXRequest request){
        ReceiptDetail detail = new ReceiptDetail();
        detail.setCompanyCode(rh.getCompanyCode());
        detail.setWarehouseCode(rh.getWarehouseCode());
        detail.setItemNetPrice(0D);
        detail.setItemListPrice(0D);
        detail.setTotalQty(request.getTotalQty());
        detail.setVersion(0L);
        detail.setToLocation(request.getToLocation());
        detail.setItemCode(request.getItemCode());
        detail.setItemName(request.getItemName());
        detail.setInventorySts(request.getInventorySts());
        detail.setBatch(request.getBatch());
        detail.setLpn(request.getLpn());
        rh.setTotalQty(rh.getTotalQty()+detail.getTotalQty());

        detail.setManufactureDate(convertReceiptDate( request.getManufactureDate(),"生产日期"));
        detail.setExpirationDate(convertReceiptDate( request.getExpirationDate(),"失效日期"));
        detail.setAgingDate(convertReceiptDate( request.getAgingDate(),"入库日期"));
        return detail;
    }
    private Date convertReceiptDate(String dateStr,String fieldName) {
        if(StringUtils.isBlank(dateStr)){
            return null;
        }
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(dateStr);
        }catch(ParseException e){
            throw BusinessException.fail(fieldName+"格式错误["+dateStr+"],必须为yyyy-MM-dd格式");
        }

    }

    private ReceiptHeader convertReceiptHeader(Map<String,ReceiptHeader> headerMap,ReceiptDataTTXRequest request){
        String receiptCode = StringUtils.isBlank(request.getCustomerCode())?request.getCode():request.getCode()+"_"+request.getCustomerCode();
        if(headerMap.containsKey(receiptCode)) {
            return headerMap.get(receiptCode);
        }
        ReceiptHeader rh = new ReceiptHeader();
        headerMap.put(receiptCode,rh);
        rh.setCompanyCode(request.getCompanyCode());
        rh.setCode(receiptCode);
        rh.setWarehouseCode(request.getWarehouseCode());
        rh.setErpOrderCode(receiptCode);
        rh.setTotalQty(0);
        rh.setLeadingSts(100);
        rh.setTrailingSts(100);
        rh.setTotalLines(0);
        rh.setDetails(new ArrayList<>());
        if (!StringUtils.equals(request.getReceiptType(), PI) && !StringUtils.equals(request.getReceiptType(), "采购入库")) {
            throw BusinessException.fail("入库单类型不正确");
        } else {
            rh.setReceiptType(PI);
        }
        Integer count = configDetailMapper.queryWarehouseCount(rh.getWarehouseCode());
        if (isCountEmpty(count)) {
            throw BusinessException.fail("仓库不存在，请到ttx检查" + rh.getWarehouseCode());
        }
        count = receiptHeaderMapper.selectByIndexCount(rh);
        if (!isCountEmpty(count)) {
            throw BusinessException.fail("入库单号已存在，请到ttx检查" + receiptCode);
        }
        if (StringUtils.isNotBlank(request.getCustomerCode())) {
            count = configDetailMapper.queryCustomerCount(request.getCustomerCode(), rh.getWarehouseCode());
            if (isCountEmpty(count)) {
                throw BusinessException.fail("客户编码不存在，请到ttx检查" + request.getCustomerCode());
            }
            rh.setShipFromCode(request.getCustomerCode());
        }

        return rh;
    }

    private boolean isCountEmpty(Integer count){
       return null != count && count <= 0;
    }


    @Override
    @Transactional(readOnly = false)
    public JsonResponse importReceiptData(List<ReceiptDataTTXRequest> requests) {
        JsonResponse jsonResponse = new JsonResponse();
        jsonResponse.setCode(BaseCodeEnum.SUCCESS.getCode());
        Map<String,ReceiptHeader> headerMap = new HashMap<>();
        JsonResponse response = convertReceiptData(requests,headerMap);
        if(null != response){
            return response;
        }

        Collection<ReceiptHeader> receipts = headerMap.values();
        if(CollectionUtils.isEmpty(receipts)){
            throw BusinessException.fail("转换数据失败");
        }
        for(ReceiptHeader rh:receipts){
            saveBatch(rh);
        }

        return jsonResponse;
    }
}
