package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.OrderTrace;
import com.midea.logistics.otp.order.domain.request.OrderTraceRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: OrderTraceFeign
* Author: luoh
* Date: 2019-6-28 11:46:35
* Description:订单路由表 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface OrderTraceFeign {

    @RequestMapping(value = OrderRouters.ORDER_TRACE_SAVE, method = RequestMethod.POST)
    JsonResponse create(@RequestBody OrderTraceRequest orderTraceRequest);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.ORDER_TRACE_DETAIL, method = RequestMethod.GET)
    JsonResponse<OrderTrace> queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.ORDER_TRACE_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderTrace orderTrace);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.ORDER_TRACE_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = OrderRouters.ORDER_TRACE_LIST, method = RequestMethod.GET)
    JsonResponse<PageResponse<OrderTrace>> search(@SpringQueryMap OrderTraceRequest orderTraceRequest);
    
    /**
    * 批量新增或者保存，根据是否传id来判断
    *
    * @return
    */
    @RequestMapping(value = OrderRouters.ORDER_TRACE_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<OrderTraceRequest> orderTraceRequests);

   /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.ORDER_TRACE_BATCH_DELETE_BY_BUSINESSKEY, method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
