package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.task.domain.bean.OrderAuditMessage;
import com.midea.logistics.otp.task.service.IOrderAuditMessageService;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: OrderAuditMessageController
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:记录消费过的订单审核消息控制层
 */
@RestController
public class OrderAuditMessageRest extends BaseController<OrderAuditMessage> {

    private Logger logger = LoggerFactory.getLogger(OrderAuditMessageRest.class);

    @Autowired
    private IOrderAuditMessageService iOrderAuditMessageService;

    /**
     * 新增
     *
     * @return
     */
    @RequestMapping(value = "/orderAuditMessage", method = RequestMethod.POST)
    public JsonResponse create(@Valid @RequestBody OrderAuditMessage orderAuditMessage, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            iOrderAuditMessageService.saveOrderAuditMessage(orderAuditMessage);
        };
        return doing.go(request, logger);
    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderAuditMessage/{id}", method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            OrderAuditMessage orderAuditMessageQr = new OrderAuditMessage();
            orderAuditMessageQr.setId(id);
            OrderAuditMessage orderAuditMessage = iOrderAuditMessageService.selectOneOrderAuditMessage(orderAuditMessageQr);
            jsonResponse.data = orderAuditMessage;
        };
        return doing.go(request, logger);

    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderAuditMessage/{id}", method = RequestMethod.DELETE)
    public JsonResponse deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            OrderAuditMessage orderAuditMessageQr = new OrderAuditMessage(id);
            iOrderAuditMessageService.deleteOrderAuditMessage(orderAuditMessageQr);
        };
        return doing.go(request, logger);

    }

    /**
     * @param orderAuditMessage
     * @return
     */
    @RequestMapping(value = "/orderAuditMessage/{id}", method = RequestMethod.PUT)
    public JsonResponse update(@PathVariable("id") Long id,  /**@Valid*/@RequestBody OrderAuditMessage orderAuditMessage, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            orderAuditMessage.setId(id);
            orderAuditMessage.setVersion(orderAuditMessage.getVersion());
            iOrderAuditMessageService.updateOrderAuditMessage(orderAuditMessage);
        };
        return doing.go(request, logger);
    }


    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/orderAuditMessages", method = RequestMethod.GET)
    public JsonResponse<PageResponse<OrderAuditMessage>> search(@SpringQueryMap OrderAuditMessage orderAuditMessage, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<OrderAuditMessage> PageResponse = iOrderAuditMessageService.selectOrderAuditMessagePage(orderAuditMessage);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }

}
