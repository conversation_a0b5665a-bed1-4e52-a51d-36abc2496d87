package com.midea.logistics.otplbs.fegin;

import cn.hutool.db.PageResult;
import com.midea.logistics.otp.bean.agg.CustomerAgingRuleConfig;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "CustomerAgingRuleConfigFeign")
public interface CustomerAgingRuleConfigFeign {

    @GetMapping("/customer/aging/rule/config/list")
    JsonResponse<List<CustomerAgingRuleConfig>> customerAgingRuleConfigList(@SpringQueryMap CustomerAgingRuleConfig entity);

    @GetMapping("/customer/aging/rule/config/page")
    JsonResponse<PageResult<CustomerAgingRuleConfig>> customerAgingRuleConfigPage(@SpringQueryMap CustomerAgingRuleConfig entity);

    @GetMapping("/customer/aging/rule/config/count")
    JsonResponse<BaseDomain> customerAgingRuleConfigCount(@SpringQueryMap CustomerAgingRuleConfig entity);

    @GetMapping("/customer/aging/rule/config/detail")
    JsonResponse<CustomerAgingRuleConfig> customerAgingRuleConfigDetail(@RequestParam("id") Long id);

    @GetMapping("/customer/aging/rule/config/detail")
    JsonResponse<CustomerAgingRuleConfig> customerAgingRuleConfigDetail(@SpringQueryMap CustomerAgingRuleConfig entity);

    @PostMapping("/customer/aging/rule/config/add")
    JsonResponse<BaseDomain> customerAgingRuleConfigAdd(@RequestBody CustomerAgingRuleConfig entity);

    @PutMapping("/customer/aging/rule/config/update")
    JsonResponse<Integer> customerAgingRuleConfigUpdate(@RequestBody CustomerAgingRuleConfig entity);

    @DeleteMapping("/customer/aging/rule/config/remove")
    JsonResponse<Integer> customerAgingRuleConfigRemove(@RequestBody CustomerAgingRuleConfig entity);

    @PostMapping("/custom/customerAgingRuleConfig/searchPage")
    JsonResponse searchPage(@RequestBody CustomerAgingRuleConfig entity);

    @PostMapping("/custom/customerAgingRuleConfig/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<CustomerAgingRuleConfig> list);

}

