package com.midea.logistics.otplbs.fegin.agg;

import com.midea.logistics.otp.bean.agg.CustomerAgingRuleConfig;
import com.midea.logistics.otp.order.domain.bean.MileageAgingType;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©1968-2023 Midea Group,IT
 *
 * @Description: 里程时效
 * @FileName: MileageAgingTypeCustomFeign
 * @Author: fengxw26
 * @Date: 2023/3/13 9:57
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "MileageAgingTypeCustomFeign")
public interface MileageAgingTypeCustomFeign {
    @PostMapping(value = "/custom/mileageAgingType/searchPage")
    JsonResponse searchPage(@RequestBody MileageAgingType entity);

    @PostMapping(value = "/custom/mileageAgingType/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<MileageAgingType> list);
}
