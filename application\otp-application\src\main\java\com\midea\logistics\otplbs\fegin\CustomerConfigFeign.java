package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.CustomerConfig;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CustomerConfigFeign
 * Author: jiajun
 * Date: 2019-5-27 13:59:00
 * Description:客户配置 feign
 */
@FeignClient(value = "logistics-otp-rule-service",url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface CustomerConfigFeign {

    @RequestMapping(value = "/customerConfig", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerConfig customerConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerConfig/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerConfig/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerConfig customerConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerConfig/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/customerConfigs", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap CustomerConfig customerConfig);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.CUSTOMER_CONFIG_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<CustomerConfig> customerConfigs);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = "/customerConfig/batchDeleteByBusinessKey", method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
