package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.bean.msgcenter.AbandonDto;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "logistics-otp-order-converged-service")
public interface IflowFeign {
    @PostMapping("/mipIflowCreateDraft")
    JsonResponse createDraftProcess(@RequestBody String reqCreateDraftDto);

    @PostMapping("/mip/abandon")
    JsonResponse mipIflowAbandon(@RequestBody AbandonDto abandonDto);
}
