package com.midea.logistics.otp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.domain.mdm.domain.MdmDataDictionaryDetail;
import com.midea.logistics.otp.bean.BusinessHelperDto;
import com.midea.logistics.otp.common.bean.SuContractExt;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.enums.BusinessControlParamEnum;
import com.midea.logistics.otp.common.feign.convergedfeign.bop.BopCrmFeign;
import com.midea.logistics.otp.common.feign.convergedfeign.bop.BopDcFeign;
import com.midea.logistics.otp.common.feign.convergedfeign.order.PmsFeign;
import com.midea.logistics.otp.common.feign.servicefeign.aging.AgingFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderTraceFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.CustomerContractConfigFeign;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.helper.ContractSwitchHelper;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.common.mq.producer.CustomerContractAgingProducer;
import com.midea.logistics.otp.order.common.mq.producer.LotsSaveAnntoOrderProducer;
import com.midea.logistics.otp.order.common.mq.producer.OrderVerifyBmsProducer;
import com.midea.logistics.otp.order.common.mq.producer.SyncOrderInfoToPmsProducer;
import com.midea.logistics.otp.order.common.service.CommonPledgeFlowService;
import com.midea.logistics.otp.order.converged.domain.request.OrderInfoRequest;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.SyncOrderInfoToPmsDto;
import com.midea.logistics.otp.order.domain.dto.SuContractDto;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.IOrderInfoService;
import com.midea.logistics.otp.orderverify.service.LoadingFeeAnalyzeService;
import com.midea.logistics.otp.orderverify.service.ServiceProductService;
import com.midea.logistics.otp.orderverify.service.TaskService;
import com.midea.logistics.otp.request.CrmPersonReq;
import com.midea.logistics.otp.response.CrmPersonRes;
import com.midea.logistics.otp.response.DcCommerceCoverExistsRes;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail;
import com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.utils.thread.ThreadLocals;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.midea.logistics.otp.common.constants.CommonConstant.STRING_FLAG_NO;
import static com.midea.logistics.otp.common.constants.CommonConstant.STRING_FLAG_YES;
import static com.midea.logistics.otp.enums.OrderOperateType.*;

/**
 * <AUTHOR>
@Service
@Slf4j
public class OrderInfoServiceImpl implements IOrderInfoService {

    @Autowired
    TaskFeign taskFeign;
    @Autowired
    BusineesFeeConfigFeign busineesFeeConfigFeign;
    @Autowired
    OmsSuContractFegin omsSuContractFegin;
    @Autowired
    BopServiceFeign bopServiceFeign;
    @Autowired
    OrderFeign orderFeign;
    @Autowired
    OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    CustomerContractConfigFeign customerContractConfigFeign;
    @Autowired
    OrderTraceFeign orderTraceFeign;
    @Autowired
    private LotsSaveAnntoOrderProducer lotsSaveAnntoOrderProducer;
    @Autowired
    private OrderVerifyBmsProducer orderVerifyBmsProducer;
    @Autowired
    private LoadingFeeAnalyzeService loadingFeeAnalyze;
    @Autowired
    private IOrderInfoService orderInfoService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private OrderLogHelper orderLogHelper;
    @Autowired
    private ServiceProductService serviceProductService;
    @Autowired
    private ServiceProductsHelper serviceProductsHelper;
    @Autowired
    private PmsFeign pmsFeign;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private AgingFeign agingFeign;
    @Autowired
    private SyncOrderInfoToPmsProducer syncOrderInfoToPmsProducer;
    @Autowired
    private ContractSwitchHelper contractSwitchHelper;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private BopCrmFeign bopCrmFeign;
    @Autowired
    private BopDcFeign bopDcFeign;
    @Autowired
    private CommonPledgeFlowService commonPledgeFlowService;

    /**
    * @description: 快递子流程审核
    * @param: [orderInfo]
    * @return: void
    * @author: 陈永培
    * @createtime: 2021/10/12 18:08
    */
    @Override
    public void expressSubFlowVerify(OrderInfoExt orderInfo) {
        try {
            OrderInfoExt oldOrderInfo  = new OrderInfoExt();
            BeanUtils.copyProperties(orderInfo, oldOrderInfo);

            //质押校验
            boolean needPledgeByCustCode = isNeedPledgeByCustCode(orderInfo);
            if (needPledgeByCustCode){
                orderInfoService.pledgeCheck(orderInfo);
            }
            
//            this.explainServiceProduct(orderInfo);

            //合同计费
            orderInfoService.contractCharge(orderInfo, oldOrderInfo);

            // 时效解析 ， 同时更新子单
            orderInfoService.aging(orderInfo, oldOrderInfo);

            //推送BMS
            orderInfoService.syncBms(orderInfo);

            //推送路由
            orderInfoService.pushLots(orderInfo);

            //生成任务
            taskService.sentTask(orderInfo);
            
            //清除小电快递流程redis节点缓存
            redisHelper.delExpressFlowNode(orderInfo.getOrderNo());

        } catch (Exception e) {
            log.error("expressSubFlowVerify-> orderNo:{},message:{}",orderInfo.getOrderNo(),e.getMessage());
        }finally {
            //清除线程变量
            orderFlowHelper.clear(orderInfo.getOrderNo());
            orderFlowHelper.clear(orderInfo.getParentOrderNo());
            log.warn("clearSubOrderCache-> orderNo:{}",orderInfo.getOrderNo());
        }
    }
    
    
    /**
    * @description: 判断该客户是否需要质押
    * @param: [orderInfo]
    * @return: boolean
    * @author: 陈永培
    * @createtime: 2025/5/26 10:17
    */
    private boolean isNeedPledgeByCustCode(OrderInfoExt orderInfo) {
        BusinessHelperDto businessHelperDto = new BusinessHelperDto();
        businessHelperDto.setCustomerCode(orderInfo.getCustomerCode());
        List<BusinessControlParamDetail> bcpds = businessParamHelper.getBusinessControlDetailByIndex2(BusinessControlParamEnum.QIMEN_EXPRESS_CUST_CODE.getKey(), businessHelperDto);
        return ToolUtils.isNotEmpty(bcpds);
    }
    
    /**
     !@质押校验 - 快递 - 入口
     * @param: [orderInfo, oldOrderInfo]
     * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
     * @author: 陈永培
     * @createtime: 2025年5月24日08:26:33
     */
    @Override
    @ExpressFlow(node = PLEDGE_VERIFY)
    public FlowListenerParam pledgeCheck(OrderInfoExt orderInfo) {
        FlowListenerParam param =  commonPledgeFlowService.pledgeCheck(orderInfo);
        //hold的要转化成fail
        if (ToolUtils.isNotEmpty(param) && FlowStatus.HOLD == param.getFlowStatus() ) {
            param.setFlowStatus(FlowStatus.FAILD);
            return param;
        }else{
            return param;
        }
    }
    
    /**
    * @description: 解析服务产品
    * @param: [orderInfo]
    * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
    * @author: 陈永培
    * @createtime: 2022/1/10 19:44
    */
    public FlowListenerParam explainServiceProduct(OrderInfoExt orderInfo) {
        boolean fit = serviceProductsHelper.isFit(orderInfo);
        if (!fit) {
            return null;
        }
        return serviceProductService.explain(orderInfo);
    }



    /**
    * @description: 合同计费审核
    * @param: [orderInfo, oldOrderInfo]
    * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
    * @author: 陈永培
    * @createtime: 2021/10/12 18:08
    */
    @Override
    @ExpressFlow(node = CONTRACT_CHARGE)
    public FlowListenerParam contractCharge(OrderInfoExt orderInfo,OrderInfoExt oldOrderInfo) {

        //装卸费
        loadingFeeAnalyze.loadingFeeAnalyzeRedis(orderInfo);

        //业务类型
        orderInfo.setBusinessType(BusinessType.EXPRESS.getKey());

        //合同校验
        JsonResponse jsonResponse = orderInfoService.contractVerify(orderInfo);

        //更新子单（对比后更新）
//        orderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
//        orderInfo.setExcuteStatus(ExcuteStatus.AUDITED.getKey());
        orderInfoService.updateOrderInfo(orderInfo, oldOrderInfo);

        String orderRpFlag = orderInfo.getOrderRpFlag();
        String contractMsg = jsonResponse.getData().toString();
        return FlowListenerParam.success(FeeType.getName(orderRpFlag)+"，"+contractMsg);
    }


    /**
     * !@时效解析 - 3、入口（快递）
     * @param: [orderInfo]
     * @author: lindq2
     * @createtime: 2023/08/20 18:08
     */
    @Override
    @ExpressFlow(node = AGING_PRODUCT)
    public FlowListenerParam aging(OrderInfoExt orderInfo, OrderInfoExt oldOrderInfo) {
        //IN3流程，自提订单不解析时效 by 20230901 caoyy BUG2023090197268
        if (DeliveryType.ZT.getKey().equals(orderInfo.getDeliveryType())){
            return FlowListenerParam.success("IN3流程自提订单不解析时效");
        }

        JsonResponse standardAgingParse = agingFeign.standardAgingParse(orderInfo.getOrderNo());

        //更新子单（对比后更新）
        orderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
        orderInfo.setExcuteStatus(ExcuteStatus.AUDITED.getKey());

        if(!BaseCodeEnum.SUCCESS.getCode().equals(standardAgingParse.getCode())) {
            if(DeliveryType.isEXPRESS(orderInfo.getDeliveryType()) && OrderType.isRIOrder(orderInfo.getOrderType())){
                //202507 泓铄，去掉限制 https://cf.annto.com/pages/viewpage.action?pageId=78545760
//                if(BaseCodeEnum.VALIDATE_ERROR.getCode().equals(standardAgingParse.getCode())){
//                    orderInfoService.updateOrderInfo(orderInfo, oldOrderInfo);
//                    return FlowListenerParam.success("退货入库单，客户时效规则配置未配置大件电商时效规则，跳过时效解析");
//                }
                throw BusinessException.fail(standardAgingParse.getMsg());
            }else if (DeliveryType.isEXPRESS(orderInfo.getDeliveryType()) && BaseCodeEnum.VALIDATE_ERROR.getCode().equals(standardAgingParse.getCode())) {
                // 如果客户以时效规则是电商时效且配送方式不是快递的，直接走旧时效；或者规则是电商时效，但是未配置场景，则走旧时效；
                // 配送方式是快递，未配置时效规则或场景或时效，都卡住
                throw BusinessException.fail(standardAgingParse.getMsg());
            } else {

                orderLogHelper.saveLog(orderInfo, AGING_PRODUCT, STRING_FLAG_YES, standardAgingParse.getMsg());
                JsonResponse agingParse = agingFeign.agingParse(orderInfo.getOrderNo());
                if (BaseCodeEnum.SUCCESS.getCode().equals(agingParse.getCode())) {
                    orderInfoService.updateOrderInfo(orderInfo, oldOrderInfo);
                    return FlowListenerParam.success("走旧时效解析成功," + agingParse.getData());
                } else {
                    return FlowListenerParam.fail("快递时效解析失败：" + agingParse.getMsg());
                }
            }
        }
        orderInfoService.updateOrderInfo(orderInfo, oldOrderInfo);
        return FlowListenerParam.success(""+standardAgingParse.getData());

    }


    /** 
    * @description: !@合同校验 - 快递 
    * @param: [orderInfo] 
    * @return: com.mideaframework.core.web.JsonResponse 
    * @author: 陈永培
    * @createtime: 2022/7/18 14:55 
    */ 
    @Override
    public JsonResponse contractVerify(OrderInfoExt orderInfo) {

        if (StringUtils.isBlank(orderInfo.getSourceSystem())) {
            throw BusinessException.fail("sourceSystem 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfo.getBusinessMode())) {
            throw BusinessException.fail("businessMode 为空，无法确认合同状态");
        }

        //2020-5-7 14:18:54 阳阳： 加上非自提的判断，自提-业务类型可以为空
        if (StringUtils.isBlank(orderInfo.getBusinessType()) && ! (DeliveryType.ZT.getKey().equals(orderInfo.getDeliveryType()))) {
            throw BusinessException.fail("非自提订单，businessType 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfo.getOrderType())) {
            throw BusinessException.fail("orderType 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfo.getCustomerCode())) {
            throw BusinessException.fail("customerCode 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfo.getSiteCode())) {
            throw BusinessException.fail("siteCode 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfo.getInOutType())) {
            throw BusinessException.fail("inOutType 为空，无法确认合同状态");
        }
        if (StringUtils.isBlank(orderInfo.getDeliveryType())) {
            throw BusinessException.fail("deliveryType 为空，无法确认合同状态");
        }


        SourceSystem sourceSystem = SourceSystem.getByKey(orderInfo.getSourceSystem());
        if (sourceSystem == null) {
            throw BusinessException.fail("系统来源类型找不到");
        }

        String contractCustomerCode = null;
        String contractCustomerName = null;


        // 客户作为虚拟客户，查找合同客户
        CustomerContractConfig customerContractConfig = new CustomerContractConfig();
        customerContractConfig.setVirtualCustomerCode(orderInfo.getCustomerCode());
        customerContractConfig.setSourceSystem(orderInfo.getSourceSystem());
        //1、	解析虚拟客户时，根据虚拟客户、仓库、来源系统找，若找不到则依次去掉虚拟客户、仓库继续找 2019.10.12
       // 2、	B2C的分拨单解析虚拟客户时，仓库取upperSenderCode 2019.10.12
        customerContractConfig.setWhCode(orderInfo.getWhCode());
        if (BusinessMode.B2C.getName().equals(orderInfo.getBusinessMode()) && OrderType.DO.getKey().equals(orderInfo.getOrderType())){
            customerContractConfig.setWhCode(orderInfo.getUpperSenderCode());
        }
        customerContractConfig.setUpperWhCode(orderInfo.getUpperWhCode());
        /**
         * 2022年7月6日17:07:28 李娟：
         * 1.来源系统+虚拟客户+仓库；查不到时，转为2来查，2查不到时，转为3
         * 2.来源系统+仓库+虚拟客户为空
         * 3.来源系统+虚拟客户+仓库为空
         * 4、来源系统+虚拟客户为空+仓库为空
         */
        JsonResponse<CustomerContractConfig> search = customerContractConfigFeign.contractVerifySearchContractConfig(customerContractConfig);
        if (!"0".equals(search.getCode())) {
            throw BusinessException.fail("查询虚拟客户合同配置失败: " + search.getMsg());
        }

        // 合同客户,是否存在, 如果存在，取合同客户
        if (search.data != null) {
            CustomerContractConfig config = search.data;
            if (StringUtils.isBlank(config.getContractCustomerCode())) {
                throw BusinessException.fail("查询到合同虚拟客户配置，但合同客户编码不存在");
            }
            contractCustomerCode = config.getContractCustomerCode();
            contractCustomerName = config.getContractCustomerName();
        }

        // 合同客户,是否存在, 如果不在，直接取订单客户
        if (contractCustomerCode == null) {
            contractCustomerCode = orderInfo.getCustomerCode();
            contractCustomerName = orderInfo.getCustomerName();
        }

        // 拿到合同会计主体
        orderInfo.setContractCustomerCode(contractCustomerCode);
        orderInfo.setContractCustomerName(contractCustomerName);

        //菜鸟上门取件合同客户写死:
        if(SourceSystem.CAINIAO.getKey().equals(orderInfo.getSourceSystem()) && OrderType.DP.getKey().equals(orderInfo.getOrderType())){
            //lj:菜鸟上门取件单的合同客户（不再是写死的），取原单的合同客户，当原单找不到时，取订单客户
            OrderInfo cnDpOriginOrderInfo = businessParamHelper.getCnDpOriginOrderInfo(orderInfo.getOriginOrderNo());
            if (null != cnDpOriginOrderInfo) {
                orderInfo.setContractCustomerCode(cnDpOriginOrderInfo.getContractCustomerCode());
                orderInfo.setContractCustomerName(cnDpOriginOrderInfo.getContractCustomerName());
            } else {
                orderInfo.setContractCustomerCode(orderInfo.getCustomerCode());
                orderInfo.setContractCustomerName(orderInfo.getCustomerName());
            }
        }

        return contractVerifyingAndUpdate(orderInfo, null);
    }



    private JsonResponse contractVerifyingAndUpdate(OrderInfoExt orderInfo, BusinessType businessType) {
        JsonResponse param = contractVerifying(orderInfo, businessType);
        if (BaseCodeEnum.SUCCESS.getCode().equals(param.getCode())) {
            param = contractUpdate(orderInfo, null);
        }
        return param;
    }

    /**
     * 合同校验过程
     *
     * @param orderInfo
     * @return
     */
    private JsonResponse contractVerifying(OrderInfoExt orderInfo, BusinessType businessType) {
        SuContractDto suContractDto = new SuContractDto();
        suContractDto.setBusinessType(businessType == null ? orderInfo.getBusinessType() : businessType.getKey());
        suContractDto.setCustomerCode(orderInfo.getContractCustomerCode());
        suContractDto.setSiteCode(orderInfo.getSiteCode());
        SuContractDto suContractResult = redisHelper.getContracatAccount(suContractDto);
        if(null == suContractResult) {
            OrderInfoRequest orderInfoRequest = new OrderInfoRequest();
            orderInfoRequest.setBusinessType(businessType == null ? orderInfo.getBusinessType() : businessType.getKey());
            orderInfoRequest.setCustomerCode(orderInfo.getContractCustomerCode());
            orderInfoRequest.setSiteCode(orderInfo.getSiteCode());
            // JsonResponse<List<SuContractExt>> suContractResponse = omsSuContractFegin.getContracatAccount(orderInfoRequest);
            JsonResponse<List<SuContractExt>> suContractResponse = contractSwitchHelper.getContracatAccount(orderInfoRequest, orderInfo.getOrderNo());
            if (!"0".equals(suContractResponse.getCode())) {
                throw BusinessException.fail("查询合同用户失败：合同参数：" + JSONObject.toJSONString(orderInfoRequest) + ", 错误信息：" + suContractResponse.getMsg());
            }

            //为空校验失败
            if (CollectionUtils.isEmpty(suContractResponse.data)) {
                String s = "客户" + orderInfoRequest.getCustomerCode() + "，在" + orderInfoRequest.getSiteCode() + "平台，无" + BusinessType.getName(orderInfoRequest.getBusinessType()) + "合同";
                throw BusinessException.fail(ExceptionType.CONTRACT_VERIFY_FAILED_EMPTY.getValue() + "，" + s);
            }

            int size = suContractResponse.data.stream().map(SuContractExt::getAccountEntity).distinct().collect(Collectors.toList()).size();
            if (size > 1) {
                String s = "客户" + orderInfoRequest.getCustomerCode() + "，在" + orderInfoRequest.getSiteCode() + "平台，" + BusinessType.getName(orderInfoRequest.getBusinessType()) + "合同主体不一致";
                throw BusinessException.fail(ExceptionType.CONTRACT_VERIFY_FAILED_DISACCORD.getValue() + "，" + s);
            }
            //cyy:合同校验时，判断合同产品类型productType是否为1，只要有一个合同为1，则认定该客户签订了标准产品合同，合同校验成功后，订单信息通过MQ同步PMS即可，同时审核日志中，增加【客户签订标准产品合同】
//            boolean isBzContractFlag = suContractResponse.data.stream().anyMatch(s -> CommonEnum.Y.getValue().equals(s.getProductType()));
//            if (isBzContractFlag) {
//                suContractDto.setIsSyncPms(CommonEnum.Y.getValue());
//            }

            SuContractExt contract = suContractResponse.data.get(0);
            if (CommonEnum.Y.getKey().equals(orderInfo.getCollectionFlag()) && contractSwitchHelper.isCrm(contract) && ! CommonEnum.Y.getValue().equals(contract.getPaymentCollection())) {
                String s = "，平台" + orderInfoRequest.getSiteCode() + "，客户" + orderInfoRequest.getCustomerCode() +"，无代收合同";
                throw BusinessException.fail(ExceptionType.CONTRACT_VERIFY_COLLECTION_FLAG.getValue() + s);
            }
            suContractDto.setAccountEntity(contract.getAccountEntity());
            suContractDto.setAccountName(contract.getAccountName());
            suContractDto.setContractCode(contract.getContractCode());
            suContractDto.setCrmContractCode(contract.getCrmContractCode());
            redisHelper.setContracatAccount(suContractDto);
        } else {
            suContractDto.setAccountEntity(suContractResult.getAccountEntity());
            suContractDto.setAccountName(suContractResult.getAccountName());
            suContractDto.setContractCode(suContractResult.getContractCode());
            suContractDto.setCrmContractCode(suContractResult.getCrmContractCode());
//            suContractDto.setIsSyncPms(suContractResult.getIsSyncPms());
        }

        orderInfo.setInvoiceUnitCode(suContractDto.getAccountEntity());
        orderInfo.setInvoiceUnitName(suContractDto.getAccountName());
        orderInfo.setContractNo(suContractDto.getContractCode());
        redisHelper.setContractCode(orderInfo.getOrderNo(), suContractDto.getCrmContractCode());
//        orderInfo.setIsSyncPms(suContractDto.getIsSyncPms());

        return JsonResponse.success("成功");
    }


    /**
     * 更新会计主体
     *
     * @param orderInfo
     * @return
     */
    private JsonResponse contractUpdate(OrderInfoExt orderInfo, String msg) {
        if (msg == null) {
            BusinessType businessType = BusinessType.getBusinessType(orderInfo.getBusinessType());
            msg = businessType.getValue();
        }

        // 没有验证过，直接跳出
        if ("".equals(msg) && StringUtils.isEmpty(orderInfo.getInvoiceUnitCode())) {
            return JsonResponse.success("合同无需校验");
        }

        //cyy:合同校验时，判断合同产品类型productType是否为1，只要有一个合同为1，则认定该客户签订了标准产品合同，合同校验成功后，订单信息通过MQ同步PMS即可，同时审核日志中，增加【客户签订标准产品合同】
//        String extMsg = "";
//        if (CommonEnum.Y.getValue().equals(orderInfo.getIsSyncPms())) {
//            SyncOrderInfoToPmsDto syncOrderInfoToPmsDto = new SyncOrderInfoToPmsDto();
//            syncOrderInfoToPmsDto.setOrderNo(orderInfo.getOrderNo());
//            syncOrderInfoToPmsDto.setPmsMsgType(PmsMsgType.RECEIVE.getKey());
//            syncOrderInfoToPmsDto.setFlowType(IdGenHelper.PARENT_PREFIX_EXPRESS);
//            pmsFeign.syncOrderInfoToPms(syncOrderInfoToPmsDto);
//            extMsg = ",客户签订标准产品合同";
//        }

        String join ="合同主体: " + orderInfo.getInvoiceUnitCode() + "(" + orderInfo.getInvoiceUnitName() + ")";
        return JsonResponse.success(join);
    }

    @Override
    public JsonResponse syncBms(OrderInfo orderInfo) {
        if (CommonConstant.FLAG_YES.equals(orderInfo.getPlanOrderFlag())){
            return JsonResponse.success("模糊订单无需同步BMS");
        }
        if (StringUtils.isEmpty(orderInfo.getInvoiceUnitCode())) {
            return JsonResponse.success("不同步BMS");
        }
        // mq异步
        if(!orderVerifyBmsProducer.sent(orderInfo)) {
            orderLogHelper.saveLog(orderInfo,SYNC_BMS,STRING_FLAG_NO,null);
            throw BusinessException.fail("订单信息推送BMS系统失败"+ JSONObject.toJSONString(orderInfo.getOrderNo()));
        }
        orderLogHelper.saveLog(orderInfo,SYNC_BMS,STRING_FLAG_YES,null);

        return JsonResponse.success("BMS订单同步成功");
    }


    @Override
    public JsonResponse pushLots(OrderInfo orderInfo) {        
        if (!lotsSaveAnntoOrderProducer.sent(orderInfo)) {
            orderLogHelper.saveLog(orderInfo,PUSH_LOTS,STRING_FLAG_NO,null);
            throw BusinessException.fail("订单信息推送查单系统失败"+ JSONObject.toJSONString(orderInfo.getOrderNo()));
        }
        //记录日志
        orderLogHelper.saveLog(orderInfo,PUSH_LOTS,STRING_FLAG_YES,null);
        //cyy:异步同步PMS子单信息
        syncOrderInfoToPmsProducer.sent(new SyncOrderInfoToPmsDto(orderInfo.getOrderNo(), orderInfo.getParentOrderNo(), PmsMsgType.SUB_RECEIVE.getKey()));
        return JsonResponse.success("");

    }

    @Override
    public void updateOrderInfo(OrderInfo orderInfo, OrderInfo source){
        if (ToolUtils.isEmpty(orderInfo)){
            return;
        }
        source.setVersion(null);
        source.setId(null);
        Object tmp = CompareHelper.compareFieldsForObjiect(source, orderInfo,null);
        if(null == tmp){
            return;
        }
        OrderInfo target = (OrderInfo)tmp;
        OrderInfo targetNull = new OrderInfo();
        BeanUtils.copyProperties(target, targetNull);
        targetNull.setUpdateUserCode(null);
        targetNull.setUpdateUserName(null);
        targetNull.setVersion(null);
        targetNull.setId(null);
        if(JSON.toJSONString(targetNull).equals(CommonConstant.OBJECT_NULL)){
            return;
        }

        JsonResponse update = orderFeign.update(target.getId(), target);
        if (update == null) {
            throw BusinessException.fail(orderInfo.getOrderNo() + ",更新订单失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail(orderInfo.getOrderNo() + ",更新订单失败：" + update.getMsg());
        }
    }
}