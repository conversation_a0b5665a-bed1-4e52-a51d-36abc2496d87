package com.midea.logistics.otp.order.common.enums;

import org.apache.commons.lang3.StringUtils;

import com.mideaframework.core.utils.ToolUtils;

/**
 * @Description: 业务控制参数维度
 */
public enum ServiceProductEnums {
    //字典
    DICT_CODE("","PRODUCT_CONFIG",""),

    COMMUNITY_GROUP_BUYING("COMMUNITY_GROUP_BUYING", "社团产品","6,9,10"),
    E_COMMERCE_LARGE("E_COMMERCE_LARGE", "电商大件产品","3"),
    E_COMMERCE_LITTLE("E_COMMERCE_LITTLE", "电商小件产品","8"),
    LIGHT_E_COMMERCE_WH_DIS("LIGHT_E_COMMERCE_WH_DIS", "小件经济仓配",""),
    NON_LIGHT_E_COMMERCE_WH_DIS("NON_LIGHT_E_COMMERCE_WH_DIS", "非标小件经济仓配",""),
    ;

    private String key;
    private String value;
    private String whCategory;

    ServiceProductEnums(String key, String value,String whCategory) {
        this.key = key;
        this.value = value;
        this.whCategory = whCategory;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public String getwhCategory() {
        return whCategory;
    }

    
    /** 
    * @description: 根据仓库分类查询 
    * @param: [whCategory] 
    * @return: com.midea.logistics.otp.order.common.enums.ServiceProductEnums 
    * @author: 陈永培
    * @createtime: 2022/1/11 9:03 
    */ 
    public static ServiceProductEnums getByWhCategory(String whCategory) {
        if (StringUtils.isEmpty(whCategory)) {
            return null;
        }
        ServiceProductEnums[] values = ServiceProductEnums.values();
        for (ServiceProductEnums value : values) {
            String wc = value.getwhCategory();
            if (ToolUtils.isNotEmpty(wc)){
               if(wc.indexOf(",")>0){
                   String[] wcList = wc.split(",");
                   for (String s : wcList) {
                       if (s.equals(whCategory)){
                           return value;
                       }
                   }
               }else{
                   if (value.getwhCategory().equals(whCategory)){
                       return value;
                   }
               }
            }
        }
        return null;
    }

    
    /** 
    * @description: 根据名称查询 
    * @param: [key] 
    * @return: java.lang.String 
    * @author: 陈永培
    * @createtime: 2022/1/11 9:05
    */ 
    public static String getName(String key) {
        String name = "";
        if (StringUtils.isEmpty(key)) {
            return name;
        }
        ServiceProductEnums[] values = ServiceProductEnums.values();
        for (ServiceProductEnums value : values) {
            if (value.getKey().equals(key)) {
                name = value.getValue();
                break;
            }
        }
        return name;
    }
}
