<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.WorkNodeMapper">

    <sql id="searchFieldsSql">
            `order_no` AS  orderNo,
            `site_code` AS  siteCode,
            `reason_content` AS  reasonContent,
            `source_system` AS  sourceSystem,
            `create_user_code` AS  createUserCode,
            `customer_code` AS  customerCode,
            `order_status` AS  orderStatus,
            `remark` AS  remark,
            `update_time` AS  updateTime,
            `tenant_code` AS  tenantCode,
            `business_time` AS  businessTime,
            `version` AS  version,
            `delete_flag` AS  deleteFlag,
            `update_user_code` AS  updateUserCode,
            `wh_code` AS  whCode,
            `create_time` AS  createTime,
            `task_no` AS  taskNo,
            `id` AS  id,
            `reason_code` AS  reasonCode,
            `customer_order_no` AS  customerOrderNo,
            `task_status` AS  taskStatus
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="orderNo !=null and orderNo != ''">
            and `order_no` =#{orderNo}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="reasonContent !=null and reasonContent != ''">
            and `reason_content` =#{reasonContent}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and `source_system` =#{sourceSystem}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="orderStatus !=null">
            and `order_status` =#{orderStatus}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="tenantCode !=null and tenantCode != ''">
            and `tenant_code` =#{tenantCode}
        </if>
        <if test="businessTime !=null">
            and `business_time` =#{businessTime}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="taskNo !=null and taskNo != ''">
            and `task_no` =#{taskNo}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="reasonCode !=null and reasonCode != ''">
            and `reason_code` =#{reasonCode}
        </if>
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            and `customer_order_no` =#{customerOrderNo}
        </if>
        <if test="taskStatus !=null">
            and `task_status` =#{taskStatus}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="orderNo !=null and orderNo != ''">
                `order_no` = #{orderNo},
            </if>
            <if test="siteCode !=null and siteCode != ''">
                `site_code` = #{siteCode},
            </if>
            <if test="reasonContent !=null and reasonContent != ''">
                `reason_content` = #{reasonContent},
            </if>
            <if test="sourceSystem !=null and sourceSystem != ''">
                `source_system` = #{sourceSystem},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                `customer_code` = #{customerCode},
            </if>
            <if test="orderStatus != null">
                `order_status` = #{orderStatus},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="tenantCode !=null and tenantCode != ''">
                `tenant_code` = #{tenantCode},
            </if>
            <if test="businessTime != null">
                `business_time` = #{businessTime},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="whCode !=null and whCode != ''">
                `wh_code` = #{whCode},
            </if>
            <if test="taskNo !=null and taskNo != ''">
                `task_no` = #{taskNo},
            </if>
            <if test="reasonCode !=null and reasonCode != ''">
                `reason_code` = #{reasonCode},
            </if>
            <if test="customerOrderNo !=null and customerOrderNo != ''">
                `customer_order_no` = #{customerOrderNo},
            </if>
            <if test="taskStatus != null">
                `task_status` = #{taskStatus},
            </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from work_node t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.WorkNode">
        select
        <include refid="searchFieldsSql"/>
        from work_node t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.WorkNode">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from work_node t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.WorkNode">
        select
        <include refid="searchFieldsSql"/>
        from work_node t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        work_node t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>

    <update id="deleteById">
        update
        work_node t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.WorkNode" useGeneratedKeys="true"
            keyProperty="id">
        insert into work_node
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="orderNo !=null and orderNo != ''">
                `order_no`,
            </if>

            <if test="siteCode !=null and siteCode != ''">
                `site_code`,
            </if>

            <if test="reasonContent !=null and reasonContent != ''">
                `reason_content`,
            </if>

            <if test="sourceSystem !=null and sourceSystem != ''">
                `source_system`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="customerCode !=null and customerCode != ''">
                `customer_code`,
            </if>

            <if test="orderStatus != null">
                `order_status`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="tenantCode !=null and tenantCode != ''">
                `tenant_code`,
            </if>

            <if test="businessTime != null">
                `business_time`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="whCode !=null and whCode != ''">
                `wh_code`,
            </if>

            <if test="taskNo !=null and taskNo != ''">
                `task_no`,
            </if>

            <if test="reasonCode !=null and reasonCode != ''">
                `reason_code`,
            </if>

            <if test="customerOrderNo !=null and customerOrderNo != ''">
                `customer_order_no`,
            </if>

            <if test="taskStatus != null">
                `task_status`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo !=null and orderNo != ''">
                #{orderNo},
            </if>
            <if test="siteCode !=null and siteCode != ''">
                #{siteCode},
            </if>
            <if test="reasonContent !=null and reasonContent != ''">
                #{reasonContent},
            </if>
            <if test="sourceSystem !=null and sourceSystem != ''">
                #{sourceSystem},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                #{customerCode},
            </if>
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="tenantCode !=null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="businessTime != null">
                #{businessTime},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="whCode !=null and whCode != ''">
                #{whCode},
            </if>
            <if test="taskNo !=null and taskNo != ''">
                #{taskNo},
            </if>
            <if test="reasonCode !=null and reasonCode != ''">
                #{reasonCode},
            </if>
            <if test="customerOrderNo !=null and customerOrderNo != ''">
                #{customerOrderNo},
            </if>
            <if test="taskStatus != null">
                #{taskStatus},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `order_no`,
            `site_code`,
            `reason_content`,
            `source_system`,
            `create_user_code`,
            `customer_code`,
            `order_status`,
            `remark`,
            `tenant_code`,
            `business_time`,
            `update_user_code`,
            `wh_code`,
            `task_no`,
            `reason_code`,
            `customer_order_no`,
            `task_status`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.orderNo},
            #{item.siteCode},
            #{item.reasonContent},
            #{item.sourceSystem},
            #{item.createUserCode},
            #{item.customerCode},
            #{item.orderStatus},
            #{item.remark},
            #{item.tenantCode},
            #{item.businessTime},
            #{item.updateUserCode},
            #{item.whCode},
            #{item.taskNo},
            #{item.reasonCode},
            #{item.customerOrderNo},
            #{item.taskStatus},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        work_node
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.orderNo !=null and item.orderNo != ''">
                `order_no` = #{item.orderNo},
            </if>
            <if test="item.siteCode !=null and item.siteCode != ''">
                `site_code` = #{item.siteCode},
            </if>
            <if test="item.reasonContent !=null and item.reasonContent != ''">
                `reason_content` = #{item.reasonContent},
            </if>
            <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                `source_system` = #{item.sourceSystem},
            </if>
            <if test="item.customerCode !=null and item.customerCode != ''">
                `customer_code` = #{item.customerCode},
            </if>
            <if test="item.orderStatus != null">
                `order_status` = #{item.orderStatus},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.tenantCode !=null and item.tenantCode != ''">
                `tenant_code` = #{item.tenantCode},
            </if>
            <if test="item.businessTime != null">
                `business_time` = #{item.businessTime},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.whCode !=null and item.whCode != ''">
                `wh_code` = #{item.whCode},
            </if>
            <if test="item.taskNo !=null and item.taskNo != ''">
                `task_no` = #{item.taskNo},
            </if>
            <if test="item.reasonCode !=null and item.reasonCode != ''">
                `reason_code` = #{item.reasonCode},
            </if>
            <if test="item.customerOrderNo !=null and item.customerOrderNo != ''">
                `customer_order_no` = #{item.customerOrderNo},
            </if>
            <if test="item.taskStatus != null">
                `task_status` = #{item.taskStatus},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.orderNo !=null and item.orderNo != ''">
                and `order_no` =#{item.orderNo}
            </if>
            <if test="item.siteCode !=null and item.siteCode != ''">
                and `site_code` =#{item.siteCode}
            </if>
            <if test="item.reasonContent !=null and item.reasonContent != ''">
                and `reason_content` =#{item.reasonContent}
            </if>
            <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                and `source_system` =#{item.sourceSystem}
            </if>
            <if test="item.customerCode !=null and item.customerCode != ''">
                and `customer_code` =#{item.customerCode}
            </if>
            <if test="item.orderStatus !=null">
                and `order_status` =#{item.orderStatus}
            </if>
            <if test="item.remark !=null">
                and `remark` =#{item.remark}
            </if>
            <if test="item.updateTime !=null">
                and `update_time` =#{item.updateTime}
            </if>
            <if test="item.tenantCode !=null and item.tenantCode != ''">
                and `tenant_code` =#{item.tenantCode}
            </if>
            <if test="item.businessTime !=null">
                and `business_time` =#{item.businessTime}
            </if>
            <if test="item.version !=null">
                and `version` =#{item.version}
            </if>
            <if test="item.deleteFlag !=null">
                and `delete_flag` =#{item.deleteFlag}
            </if>
            <if test="item.whCode !=null and item.whCode != ''">
                and `wh_code` =#{item.whCode}
            </if>
            <if test="item.createTime !=null">
                and `create_time` =#{item.createTime}
            </if>
            <if test="item.taskNo !=null and item.taskNo != ''">
                and `task_no` =#{item.taskNo}
            </if>
            <if test="item.reasonCode !=null and item.reasonCode != ''">
                and `reason_code` =#{item.reasonCode}
            </if>
            <if test="item.customerOrderNo !=null and item.customerOrderNo != ''">
                and `customer_order_no` =#{item.customerOrderNo}
            </if>
            <if test="item.taskStatus !=null">
                and `task_status` =#{item.taskStatus}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE work_node
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>


    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE work_node
            set `delete_flag`=1
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

</mapper>