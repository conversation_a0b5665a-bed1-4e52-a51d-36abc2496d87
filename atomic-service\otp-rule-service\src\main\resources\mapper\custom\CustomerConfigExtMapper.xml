<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.CustomerConfigExtMapper">

    <sql id="searchFieldsSql">
            `business_mode` AS  businessMode,
            `order_type` AS  orderType,
            `volumn_flag` AS  volumnFlag,
            `single_volumn_flag` AS  singleVolumnFlag,
            `site_code` AS  siteCode,
            `create_user_code` AS  createUserCode,
            `single_weight_flag` AS  singleWeightFlag,
            `customer_code` AS  customerCode,
            `update_time` AS  updateTime,
            `remark` AS  remark,
            `order_system` AS  orderSystem,
            `version` AS  version,
            `apart_type` AS  apartType,
            `delete_flag` AS  deleteFlag,
            `hold_flag` AS  holdFlag,
            `hand_check_flag` AS  handCheckFlag,
            `update_user_code` AS  updateUserCode,
            `wh_code` AS  whCode,
            `create_time` AS  createTime,
            `weight_flag` AS  weightFlag,
            `compart_flag` AS  compartFlag,
            `adjust_mip_flag` AS  adjustMipFlag,
            `id` AS  id
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="businessMode !=null and businessMode != ''">
            and `business_mode` =#{businessMode}
        </if>
        <if test="businessMode ==null and businessMode == ''">
            and `business_mode` is null
        </if>

        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="orderType == null or orderType == ''">
            and `order_type` is null
        </if>
        <if test="volumnFlag !=null and volumnFlag != ''">
            and `volumn_flag` =#{volumnFlag}
        </if>
        <if test="singleVolumnFlag !=null and singleVolumnFlag != ''">
            and `single_volumn_flag` =#{singleVolumnFlag}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="siteCode ==null or siteCode == ''">
            and `site_code` is null
        </if>
        <if test="singleWeightFlag !=null and singleWeightFlag != ''">
            and `single_weight_flag` =#{singleWeightFlag}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="customerCode ==null or customerCode == ''">
            and `customer_code` is null
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="orderSystem !=null and orderSystem != ''">
            and `order_system` =#{orderSystem}
        </if>
        <if test="apartType !=null and apartType != ''">
            and `apart_type` =#{apartType}
        </if>
        <if test="holdFlag !=null">
            and `hold_flag` =#{holdFlag}
        </if>
        <if test="handCheckFlag !=null">
            and `hand_check_flag` =#{handCheckFlag}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>
        <if test="whCode ==null or whCode == ''">
            and `wh_code` is null
        </if>
        <if test="weightFlag !=null and weightFlag != ''">
            and `weight_flag` =#{weightFlag}
        </if>
    </sql>

    <!-- 弃用 -->
    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerConfig">
        select
        <include refid="com.midea.logistics.otp.rule.mapper.common.CustomerConfigMapper.searchFieldsSql"/>
        from customer_config t
        <include refid="whereFieldsSql"/>

        ORDER BY id ASC
        limit 2
    </select>


    <!-- 获取客户配置， 重写上面的方法 -->
    <select id="getCustomerConfig" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerConfig">
        SELECT
        <include refid="com.midea.logistics.otp.rule.mapper.common.CustomerConfigMapper.searchFieldsSql"/>

        FROM
        customer_config
        WHERE
        delete_flag = 0

        <if test="businessMode !=null and businessMode != ''">
            AND business_mode = #{businessMode}
        </if>
        <if test="businessMode ==null or businessMode == ''">
            AND (business_mode IS NULL or business_mode = '')
        </if>
        <if test="orderType !=null and orderType != ''">
            AND order_type = #{orderType}
        </if>
        <if test="orderType == null or orderType == ''">
            AND (order_type IS NULL or order_type = '')
        </if>
        <if test="siteCode !=null and siteCode != ''">
            AND site_code = #{siteCode}
        </if>
        <if test="siteCode ==null or siteCode == ''">
            AND (site_code IS NULL or site_code = '')
        </if>
        <if test="customerCode !=null and customerCode != ''">
            AND customer_code = #{customerCode}
        </if>
        <if test="customerCode ==null or customerCode == ''">
            AND (customer_code IS NULL or customer_code = '')
        </if>
        <if test="orderSystem !=null and orderSystem != ''">
            AND order_system = #{orderSystem}
        </if>
        <if test="orderSystem ==null or orderSystem == ''">
            AND (order_system IS NULL or order_system = '')
        </if>
        <if test="whCode !=null and whCode != ''">
            AND wh_code = #{whCode}
        </if>
        <if test="whCode ==null or whCode == ''">
            AND (wh_code IS NULL or wh_code = '')
        </if>
        LIMIT 1
    </select>

    <update id="updateById">
        update
        customer_config t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <choose>
                <when test="businessMode !=null and businessMode != ''">
                    `business_mode` = #{businessMode},
                </when>
                <otherwise>
                    `business_mode` = null,
                </otherwise>
            </choose>
            <choose>
                <when test="orderType !=null and orderType != ''">
                    `order_type` = #{orderType},
                </when>
                <otherwise>
                    `order_type` = null,
                </otherwise>
            </choose>
            <if test="volumnFlag != null">
                `volumn_flag` = #{volumnFlag},
            </if>
            <if test="singleVolumnFlag != null">
                `single_volumn_flag` = #{singleVolumnFlag},
            </if>
            <choose>
                <when test="siteCode !=null and siteCode != ''">
                    `site_code` = #{siteCode},
                </when>
                <otherwise>
                    `site_code` = null,
                </otherwise>
            </choose>
            <if test="singleWeightFlag != null">
                `single_weight_flag` = #{singleWeightFlag},
            </if>
            <choose>
                <when test="customerCode !=null and customerCode != ''">
                    `customer_code` = #{customerCode},
                </when>
                <otherwise>
                    `customer_code` = null,
                </otherwise>
            </choose>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="orderSystem !=null and orderSystem != ''">
                `order_system` = #{orderSystem},
            </if>
            <if test="apartType !=null and apartType != ''">
                `apart_type` = #{apartType},
            </if>
            <if test="holdFlag != null">
                `hold_flag` = #{holdFlag},
            </if>
            <if test="handCheckFlag != null">
                `hand_check_flag` = #{handCheckFlag},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <choose>
                <when test="whCode !=null and whCode != ''">
                    `wh_code` = #{whCode},
                </when>
                <otherwise>
                    `wh_code` = null,
                </otherwise>
            </choose>
            <if test="feeFlag != null">
                `fee_flag` = #{feeFlag},
            </if>
            <if test="weightFlag != null">
                `weight_flag` = #{weightFlag},
            </if>
            <if test="compartFlag !=null and compartFlag != ''">
                `compart_flag` = #{compartFlag},
            </if>
            <if test="apartFlag !=null and apartFlag != ''">
                `apart_flag` = #{apartFlag},
            </if>
            <if test="adjustMipFlag != null">
                `adjust_mip_flag` = #{adjustMipFlag},
            </if>
            <if test="holdVolumn != null">
                `hold_volumn` = #{holdVolumn},
            </if>
        </set>
    </sql>
</mapper>