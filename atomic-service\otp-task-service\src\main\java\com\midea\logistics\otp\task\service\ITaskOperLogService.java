package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.task.domain.bean.TaskOperLog;
import com.mideaframework.core.web.PageResponse;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: ITaskOperLogService
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:ITaskOperLogService服务接口
 */
public interface ITaskOperLogService {

    Integer saveTaskOperLog(TaskOperLog taskOperLog);

    Integer deleteTaskOperLog(TaskOperLog taskOperLog);

    Integer updateTaskOperLog(TaskOperLog taskOperLog);

    TaskOperLog selectOneTaskOperLog(TaskOperLog taskOperLog);

    PageResponse<TaskOperLog> selectTaskOperLogPage(TaskOperLog taskOperLog);

    List<TaskOperLog> selectTaskOperLogByIndex(TaskOperLog taskOperLog);

    int countTaskOperLogByIndex(TaskOperLog taskOperLog);

    Integer insertBatch(List<TaskOperLog> taskOperLogs);

    Integer batchUpdate(List<TaskOperLog> taskOperLogs);

    Integer deleteBatch(List<TaskOperLog> taskOperLogs);
}