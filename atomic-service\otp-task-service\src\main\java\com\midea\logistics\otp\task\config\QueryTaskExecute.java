package com.midea.logistics.otp.task.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class QueryTaskExecute {
    
    @Value("${queryTaskExecuteThreadPoolExecutorCore:8}")
    private int queryTaskExecuteThreadPoolExecutorCore;
    
    @Value("${queryTaskExecuteThreadPoolExecutorMax:16}")
    private int queryTaskExecuteThreadPoolExecutorMax;
    
    @Value("${queryTaskExecuteThreadPoolExecutorQueueSize:20000}")
    private int queryTaskExecuteThreadPoolExecutorQueueSize;
    
    @Bean("com.midea.logistics.otp.task.config.queryTaskExecuteThreadPoolExecutor")
    public ThreadPoolExecutor queryTaskExecuteThreadPoolExecutor() {
        return new ThreadPoolExecutor(queryTaskExecuteThreadPoolExecutorCore, queryTaskExecuteThreadPoolExecutorMax, 30,
            TimeUnit.MINUTES, new ArrayBlockingQueue<>(queryTaskExecuteThreadPoolExecutorQueueSize),
            new ThreadPoolExecutor.CallerRunsPolicy());
    }
    
}
