package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CustomerOrderItemFeign
 * Author: lindq2
 * Date: 2019-5-23 9:52:10
 * Description:订单明细表 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface CustomerOrderItemFeign {


    @GetMapping(OrderRouters.CUSTOMER_ORDER_ITEMS_LIST_BY_ORDERNO)
    JsonResponse<List<CustomerOrderItem>> listByOrderNo(@RequestParam("orderNo") String orderNo);


    @RequestMapping(value = "/customerOrderItem", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerOrderItem customerOrderItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderItem/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderItem/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerOrderItem customerOrderItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerOrderItem/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/customerOrderItems", method = RequestMethod.POST)
    JsonResponse search(@RequestBody CustomerOrderItem customerOrderItem);

    @RequestMapping(value = "/selectCustomerOrderItems", method = RequestMethod.POST)
    JsonResponse selectCustomerOrderItems(@RequestBody List<String> customerOrderNos);

    /**
     * 订单中心多单分仓获取商品详情
     * @param orderNos
     * @return
     */
    @RequestMapping(value = OrderRouters.CUSTOMER_ORDER_ITEM_LIST_WAREHOUSE_BY_ITEM_ORDER_NOS, method = RequestMethod.POST)
    public JsonResponse listWarehouseByItemOrderNos(@RequestBody List<String> orderNos);
}
