spring:
  # public setting
  application:
    name: logistics-otp-expressverify-converged-service
    organization: ltc
    owner: ltc
  # active setting
  profiles:
    active: uat
  main:
    allow-bean-definition-overriding: true
server:
  port: 10128
  tomcat:
    max-threads: 500
    max-connections: 10000
    accept-count: 500


breaker:
  enable: false
  auto:
    webmvc: false
    feign: false
  sentinel:
    enable: false

mgp:
  carrier:
    discovery:
      system: c-loms-annto
      version: 1.0
      auto-host-ip: true
      enable: true

logging:
  level:
    # 新增配置, 过滤掉该服务治理SDK版本过多日志输出问题
    com.midea.mgp: warn

feign:
  hystrix:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 60_000
        readTimeout: 60_000
        loggerLevel: basic

ribbon:
  # 同一实例最大重试次数，不包括首次调用。默认值为0
  MaxAutoRetries: 0
  # 同一个微服务其他实例的最大重试次数，不包括第一次调用的实例。默认值为1
  MaxAutoRetriesNextServer: 0
  # 是否所有操作（GET、POST等）都允许重试。默认值为false
  OkToRetryOnAllOperations: false

management:
  health:
    elasticsearch:
      enabled: false

#dev
---
spring:
  profiles: dev
  logistics-log:
    path: ./logs
  zipkin:
    #    base-url: http://127.0.0.1:8002
    #    base-url: http://************:8002
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-dev.annto.com:15821/eureka/
  server:
    enable-self-preservation: false

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: aeac7aa2b5184a1387ef8e442bcc5cca

#sit
---
spring:
  profiles: sit
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:9501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-sit.annto.com:15821/eureka/
  server:
    enable-self-preservation: false
mgp:
  carrier:
    discovery:
      env: sit
      registry: https://anapi-sit.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi
apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 24a8cceeb9f14044b200c05c9a42605b

#uat
---
spring:
  profiles: uat
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:19501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-uat.annto.com:15821/eureka/
mgp:
  carrier:
    discovery:
      env: uat
      registry: https://anapi-uat.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi
apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: d3160540289c4e03a38089cb07bd0e6a

#ver
---
spring:
  profiles: ver
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:19501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-ver.annto.com:15821/eureka/
mgp:
  carrier:
    discovery:
      env: ver
      registry: https://anapi-ver.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi
apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 9e1500c5c98645989f43408b4bd29fe3


#prod
---
spring:
  profiles: prod
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:9501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:l+3CmmYr4eejdJvTmip7S2dcGeEQej/7UUYR8gBQfWM=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi.annto.com:15821/eureka/

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: f2384b7938d9479493a32cf61d960d22

mgp:
  carrier:
    discovery:
      env: prd
      registry: https://anapi.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi