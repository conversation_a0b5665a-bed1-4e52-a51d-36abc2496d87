package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.OrderException;
import com.midea.logistics.otp.order.domain.bean.custom.OrderExceptionExt;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: OrderExceptionFeign
 * Author: liwende
 * Date: 2019-6-13 15:57:01
 * Description:订单异常表 feign
 */
@FeignClient(value = "logistics-otp-order-service",url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface OrderExceptionFeign {

    /**
     *
     */
    @RequestMapping(value = "/orderException", method = RequestMethod.POST)
    JsonResponse create(@RequestBody OrderException orderException);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderException/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderException/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderException orderException);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderException/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     *
     */
    @RequestMapping(value = "/orderExceptions", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap OrderExceptionExt orderExceptionExt);

    @RequestMapping(value = OrderRouters.ORDER_EXCEPTION_CLOSES, method = RequestMethod.POST)
    public JsonResponse closes(@RequestBody List<Long> ids);


    @RequestMapping(value =  OrderRouters.ORDER_EXCEPTION_HANDLES, method = RequestMethod.POST)
    public JsonResponse handles(@RequestBody List<Long> ids);

    @RequestMapping(value = "/orderExceptionsCount", method = RequestMethod.GET)
    JsonResponse orderExceptionsCount(@SpringQueryMap OrderExceptionExt orderExceptionExt);
}
