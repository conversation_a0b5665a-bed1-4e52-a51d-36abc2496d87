<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.LoadingFeeRuleExtMapper">


    <sql id="configWhereFieldsSql">
        where delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="customerName !=null and customerName != ''">
            and `customer_name` =#{customerName}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="pickFlag !=null and pickFlag != ''">
            and `pick_flag` =#{pickFlag}
        </if>
        <if test="pickFlag ==null or pickFlag == ''">
            and (`pick_flag` is null or `pick_flag` = '')
        </if>
        <if test="feeType !=null and feeType != ''">
            and `fee_type` =#{feeType}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="siteName !=null and siteName != ''">
            and `site_name` =#{siteName}
        </if>
        <if test="whName !=null and whName != ''">
            and `wh_name` =#{whName}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>

        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and `delivery_type` =#{deliveryType}
        </if>
        <if test="deliveryType ==null or deliveryType == ''">
            and (`delivery_type` is null or `delivery_type` = '')
        </if>
    </sql>

    <sql id="whereFieldsSqlOther">
        <if test="batchSiteCode !=null and batchSiteCode.size()>0">
            and site_code in
            <foreach collection="batchSiteCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="batchWhCode !=null and batchWhCode.size()>0">
            and wh_code in
            <foreach collection="batchWhCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getLoadingFeeRuleConfig" resultType="com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule">
        select
        <include refid="searchFieldsSql"/>
        from loading_fee_rule t
        <include refid="configWhereFieldsSql"/>
        limit 1
    </select>


    <sql id="searchFieldsSql">
        `order_type` AS orderType,
        `create_user_code` AS createUserCode,
        `update_user_name` AS updateUserName,
        `customer_code` AS customerCode,
        `customer_name` AS customerName,
        `create_user_name` AS createUserName,
        `update_time` AS updateTime,
        `remark` AS remark,
        `pick_flag` AS pickFlag,
        `fee_type` AS feeType,
        `site_code` AS  siteCode,
        `site_name` AS  siteName,
        `wh_name` AS  whName,
        `wh_code` AS  whCode,
        `version` AS version,
        `delete_flag` AS deleteFlag,
        `update_user_code` AS updateUserCode,
        `create_time` AS createTime,
        `id` AS id,
        `delivery_type` AS deliveryType
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="customerName !=null and customerName != ''">
            and `customer_name` =#{customerName}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="pickFlag !=null and pickFlag != ''">
            and `pick_flag` =#{pickFlag}
        </if>
        <if test="feeType !=null and feeType != ''">
            and `fee_type` =#{feeType}
        </if>

        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="siteName !=null and siteName != ''">
            and `site_name` =#{siteName}
        </if>
        <if test="whName !=null and whName != ''">
            and `wh_name` =#{whName}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>


        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="orderType !=null and orderType != ''">
                `order_type` = #{orderType},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="updateUserName !=null and updateUserName != ''">
                `update_user_name` = #{updateUserName},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                `customer_code` = #{customerCode},
            </if>
            <if test="customerName !=null and customerName != ''">
                `customer_name` =#{customerName},
            </if>
            <if test="createUserName !=null and createUserName != ''">
                `create_user_name` = #{createUserName},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="pickFlag !=null and pickFlag != ''">
                `pick_flag` = #{pickFlag},
            </if>
            <if test="feeType !=null and feeType != ''">
                `fee_type` = #{feeType},
            </if>
            <if test="siteCode !=null and siteCode != ''">
                `site_code` = #{siteCode},
            </if>
            <if test="site_name !=null and site_name != ''">
                `site_name` = #{site_name},
            </if>
            <if test="whName !=null and whName != ''">
                `wh_name` = #{whName},
            </if>
            <if test="whCode !=null and whCode != ''">
                `wh_code` = #{whCode},
            </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from loading_fee_rule t
        where
        `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule">
        select
        <include refid="searchFieldsSql"/>
        from loading_fee_rule t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from loading_fee_rule t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule">
        select
        <include refid="searchFieldsSql"/>
        from loading_fee_rule t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        loading_fee_rule t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>

    <update id="deleteById">
        update
        loading_fee_rule t
        set `delete_flag`=1
        where
        `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule" useGeneratedKeys="true"
            keyProperty="id">
        insert into loading_fee_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="orderType !=null and orderType != ''">
                `order_type`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="updateUserName !=null and updateUserName != ''">
                `update_user_name`,
            </if>

            <if test="customerCode !=null and customerCode != ''">
                `customer_code`,
            </if>

            <if test="customerName !=null and customerName != ''">
                `customer_name`,
            </if>
            <if test="createUserName !=null and createUserName != ''">
                `create_user_name`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="pickFlag !=null and pickFlag != ''">
                `pick_flag`,
            </if>

            <if test="feeType !=null and feeType != ''">
                `fee_type`,
            </if>

            <if test="siteCode !=null and siteCode != ''">
                `site_code`,
            </if>
            <if test="site_name !=null and site_name != ''">
                `site_name`,
            </if>
            <if test="whName !=null and whName != ''">
                `wh_name`,
            </if>
            <if test="whCode !=null and whCode != ''">
                `wh_code`,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderType !=null and orderType != ''">
                #{orderType},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="updateUserName !=null and updateUserName != ''">
                #{updateUserName},
            </if>
            <if test="customerCode !=null and customerCode != ''">
                #{customerCode},
            </if>
            <if test="customerName !=null and customerName != ''">
                #{customerName},
            </if>
            <if test="createUserName !=null and createUserName != ''">
                #{createUserName},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="pickFlag !=null and pickFlag != ''">
                #{pickFlag},
            </if>
            <if test="feeType !=null and feeType != ''">
                #{feeType},
            </if>

            <if test="siteCode !=null and siteCode != ''">
                #{siteCode},
            </if>
            <if test="site_name !=null and site_name != ''">
                #{site_name},
            </if>
            <if test="whName !=null and whName != ''">
                #{whName},
            </if>
            <if test="whCode !=null and whCode != ''">
                #{whCode},
            </if>

        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `order_type`,
            `update_user_code`,
            `create_user_code`,
            `update_user_name`,
            `customer_code`,
            `customer_name`,
            `create_user_name`,
            `remark`,
            `pick_flag`,
            `fee_type`,
            `site_code`,
            `site_name`,
            `wh_name`,
            `wh_code`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.orderType},
            #{item.updateUserCode},
            #{item.createUserCode},
            #{item.updateUserName},
            #{item.customerCode},
            #{item.customerName},
            #{item.createUserName},
            #{item.remark},
            #{item.pickFlag},
            #{item.feeType},
            #{item.siteCode},
            #{item.siteName},
            #{item.whName},
            #{item.whCode},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        loading_fee_rule
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.orderType !=null and item.orderType != ''">
                `order_type` = #{item.orderType},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.updateUserName !=null and item.updateUserName != ''">
                `update_user_name` = #{item.updateUserName},
            </if>
            <if test="item.customerCode !=null and item.customerCode != ''">
                `customer_code` = #{item.customerCode},
            </if>
            <if test="item.customerName !=null and item.customerName != ''">
                `customer_name` = #{item.customerName},
            </if>
            <if test="item.createUserName !=null and item.createUserName != ''">
                `create_user_name` = #{item.createUserName},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.pickFlag !=null and item.pickFlag != ''">
                `pick_flag` = #{item.pickFlag},
            </if>
            <if test="item.feeType !=null and item.feeType != ''">
                `fee_type` = #{item.feeType},
            </if>

            <if test="item.siteCode !=null and item.siteCode != ''">
                `site_code` = #{item.siteCode},
            </if>
            <if test="item.siteName !=null and item.siteName != ''">
                `site_name` = #{item.siteName},
            </if>
            <if test="item.whName !=null and item.whName != ''">
                `wh_name` = #{item.whName},
            </if>
            <if test="item.whCode !=null and item.whCode != ''">
                `wh_code` = #{item.whCode},
            </if>

        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.id !=null">
                and `id` =#{item.id}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE loading_fee_rule
            <include refid="setBatchFieldsSql"/>
            where
            <include refid="setBatchWhereFields"/>
        </foreach>

    </update>

    <update id="deleteBatchById" parameterType="list">
        update
        loading_fee_rule t
        set `delete_flag`=1
        where
        `id` in
        <foreach collection="array" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="getDOFeeRuleConfig" resultType="com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule">
        select
        <include refid="searchFieldsSql"/>
        from loading_fee_rule t
        where delete_flag=0
        and `order_type` =#{orderType}
        and `customer_code` =#{customerCode}
        and `site_code` =#{siteCode}
        and `wh_code` =#{whCode}
        limit 1
    </select>

    <select id="selectLoadingFeeRuleByIndexCount" resultType="java.lang.Integer">
        select count(*)
        from loading_fee_rule t
        <include refid="com.midea.logistics.otp.rule.mapper.common.LoadingFeeRuleMapper.whereFieldsSql"/>
        <include refid="whereFieldsSqlOther"/>
    </select>

    <select id="selectLoadingFeeRuleByIndex"
            resultType="com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule">
        select
        <include refid="com.midea.logistics.otp.rule.mapper.common.LoadingFeeRuleMapper.searchFieldsSql"/>
        from loading_fee_rule t
        <include refid="com.midea.logistics.otp.rule.mapper.common.LoadingFeeRuleMapper.whereFieldsSql"/>
        <include refid="whereFieldsSqlOther"/>
        limit ${start},${pageSize}
    </select>
</mapper>