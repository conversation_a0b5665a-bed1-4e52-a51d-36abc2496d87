<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.CustomerContractConfigExMapper">


    <!-- 使用 来源平台，虚拟客户 查询合同 -->
    <select id="contractVerifySearchContractConfig" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig">
        SELECT
            id,
            contract_customer_code,
            contract_customer_name
        FROM
            customer_contract_config
        WHERE
            delete_flag = 0
            AND source_system = #{sourceSystem}
        <if test="whCode !=null and whCode != ''">
            and  wh_code  =#{whCode}
        </if>
        <if test="whCode == null or whCode == ''  ">
            and (`wh_code` is null or `wh_code` = '')
        </if>

        <if test="virtualCustomerCode !=null and virtualCustomerCode != ''">
           AND virtual_customer_code = #{virtualCustomerCode}
        </if>

         <if test="virtualCustomerCode == null or virtualCustomerCode == ''  ">
            and (`virtual_customer_code` is null or `virtual_customer_code` = '')
         </if>

         limit 1
    </select>

    <!-- 使用 来源平台，虚拟客户 查询合同 -->
    <select id="searchBySystemAndVirtualCustomerCode" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig">
        SELECT
            id,
            contract_customer_code,
            contract_customer_name
        FROM
            customer_contract_config
        WHERE
            delete_flag = 0
            AND source_system = #{sourceSystem}
        <if test="whCode !=null and whCode != ''">
            and  wh_code  =#{whCode}
        </if>
        <if test="whCode == ''">
            and  `wh_code`  IS NULL
        </if>
            AND virtual_customer_code = #{virtualCustomerCode}
            limit 1
    </select>

    <!-- 使用 来源平台，非虚拟客户 查询合同 -->
    <select id="searchBySystemAndNotVirtualCustomerCode" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig">
        SELECT
            id,
            contract_customer_code,
            contract_customer_name
        FROM
            customer_contract_config
        WHERE
            delete_flag = 0
            AND source_system = #{sourceSystem}
        <if test="whCode !=null and whCode != ''">
            and  `wh_code`  =#{whCode}
        </if>
        <if test="whCode == ''">
            and  `wh_code`  IS NULL
        </if>
            AND virtual_customer_code IS NULL
        limit 1
    </select>



    <!-- 使用 来源平台，虚拟客户 统计合同 -->
    <select id="countBySystemAndVirtualCustomerCode" resultType="java.lang.Integer">
        SELECT
            count(*) count
        FROM
            customer_contract_config
        WHERE
            delete_flag = 0
            AND source_system = #{sourceSystem}
        <if test="whCode !=null and whCode != ''">
            and  `wh_code`  =#{whCode}
        </if>
        <if test="whCode == ''">
            and  `wh_code`  IS NULL
        </if>
            AND virtual_customer_code = #{virtualCustomerCode}
    </select>

    <!-- 使用 来源平台，非虚拟客户 统计合同 -->
    <select id="countBySystemAndNotVirtualCustomerCode" resultType="java.lang.Integer">
        SELECT
            count(*) count
        FROM
            customer_contract_config
        WHERE
            delete_flag = 0
            AND source_system = #{sourceSystem}
        <if test="whCode !=null and whCode != ''">
            and  `wh_code`  =#{whCode}
        </if>
        <if test="whCode == ''">
            and  `wh_code`  IS NULL
        </if>
            AND virtual_customer_code IS NULL
    </select>

    <!-- 使用 来源平台，虚拟客户 查询合同 -->
    <select id="contractVerifySearchContractConfigs" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig">
        SELECT
        id,
        contract_customer_code,
        contract_customer_name,
        upper_wh_code
        FROM
        customer_contract_config
        WHERE
        delete_flag = 0
        AND source_system = #{sourceSystem}
        <if test="whCode !=null and whCode != ''">
            and  wh_code  =#{whCode}
        </if>
        <if test="whCode == null or whCode == ''  ">
            and (`wh_code` is null or `wh_code` = '')
        </if>

        <if test="virtualCustomerCode !=null and virtualCustomerCode != ''">
            AND virtual_customer_code = #{virtualCustomerCode}
        </if>

        <if test="virtualCustomerCode == null or virtualCustomerCode == ''  ">
            and (`virtual_customer_code` is null or `virtual_customer_code` = '')
        </if>

    </select>

    <select id="searchByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig">
        SELECT
        id,
        contract_customer_code,
        contract_customer_name,
        upper_wh_code
        FROM
        customer_contract_config
        WHERE
        delete_flag = 0
        AND source_system = #{sourceSystem}
        <if test="whCode !=null and whCode != ''">
            and  wh_code  =#{whCode}
        </if>
        <if test="whCode == null or whCode == ''  ">
            and (`wh_code` is null or `wh_code` = '')
        </if>

        <if test="virtualCustomerCode !=null and virtualCustomerCode != ''">
            AND virtual_customer_code = #{virtualCustomerCode}
        </if>

        <if test="virtualCustomerCode == null or virtualCustomerCode == ''  ">
            and (`virtual_customer_code` is null or `virtual_customer_code` = '')
        </if>

    </select>


</mapper>