package com.midea.logistics.otplbs.manager.helper;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.lc.file.sdk.builder.FileUploadBuilder;
import com.midea.logistics.lc.file.sdk.service.LcFileService;
import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otplbs.bean.ImportResponseVo;
import com.midea.mip.core.util.StringUtil;
import com.mideaframework.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

@Component
@Slf4j
public class AsyncUpLoadTask{
    @Autowired
    DictHelper dictHelper;
    @Autowired
    UpDownloadHelper upDownloadHelper;
    @Autowired
    private LcFileService lcFileService;


    /**
     * 异步调用上传接口
     */
    public ImportResponseVo upLoadTask(MultipartFile file, CountDownLatch latch,String userCode) {
        try {
            ImportResponseVo vo = new ImportResponseVo();
            //先上传oss
            ImportFileRequest request = uploadFileToOss(file, userCode);
            if (null == request) {
                log.error("上传oss失败");
                return null;
            }
            vo.fileName(request.getFileName()).createTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                .createUserCode(request.getUserCode()).filePath(request.getFileUrl()).fileSize(Double.valueOf(file.getSize()));


            //上传iflow
//            String fileName = UUID.randomUUID() + file.getOriginalFilename();
//            String filePath = this.getClass().getResource("/").getPath() + "/" + fileName;
//            File tempFile = new File(filePath);
//            FileUtils.copyInputStreamToFile(file.getInputStream(), tempFile);
//            int dotIndex = file.getOriginalFilename().lastIndexOf(".");
//            String docName = file.getOriginalFilename().contains("/") || file.getOriginalFilename().contains("\\") ? file.getOriginalFilename()
//                .substring(file.getOriginalFilename().lastIndexOf("/") + file.getOriginalFilename().lastIndexOf("\\") + 2, dotIndex) : file.getOriginalFilename().substring(0, dotIndex);
//            String docExt = file.getOriginalFilename().substring(dotIndex + 1);
//            FileSystemResource resource = new FileSystemResource(tempFile);
//            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
//            param.add("uploadFile", resource);
//            param.add("fileName", docName);
//            param.add("fileExt", docExt);
//            HttpHeaders headers = new HttpHeaders();
//            MediaType type = MediaType.parseMediaType("multipart/form-data;charset=UTF-8");
//            headers.setContentType(type);
//            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<MultiValueMap<String, Object>>(param, headers);
//            RestTemplate restClient = new RestTemplate();
//            String result = restClient.postForObject(uploadUrl, requestEntity, String.class);
//            tempFile.delete();
//
//            if (result != null && !result.isEmpty()) {
//                JSONObject responseJson = JSONObject.parseObject(result);
//                JSONObject bodyJson = responseJson.getJSONObject("body");
//                String code = bodyJson.getString("resultCode");
//                if ("010".equals(code)) {
//                    JSONObject data = bodyJson.getJSONObject("data");
//                    //组装返回参数
//                    vo.fileName(request.getFileName()).createTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
//                        .createUserCode(request.getUserCode()).filePath(request.getFileUrl()).fileSize(Double.valueOf(file.getSize()));
//
//                    String docId=(data.get("docId") == null ? null : String.valueOf(data.get("docId")));
//                    vo.docId(docId);
//                    return StringUtil.isNotNull(docId)?vo:null;
//                }
//                log.error(bodyJson.get("resultMsg") == null ? null : (String) bodyJson.get("resultMsg"));
//            }
            return vo;
        } catch (Exception e) {
            log.error("iflow审批意见附件上传失败", e);
            return null;
        } finally {
            latch.countDown();
        }
    }

    public ImportFileRequest uploadFileToOss(MultipartFile file,String userCode) {
        ImportFileRequest request = new ImportFileRequest();
        request.setFileName(file.getOriginalFilename());
        request.setUserCode(userCode);

        FileUploadBuilder build = FileUploadBuilder.newBuilder().file(file).bucket("c-loms").userCode(userCode).publicRead(true).build();
        String upload = lcFileService.upload(build);
        if (StringUtils.isBlank(upload)) {
            throw BusinessException.fail("文件上传异常");
        }
        request.setFileUrl(upload);
        return request;
    }
}
