package com.midea.logistics.imp.orderverify.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.otp.enums.JoinType;
import com.midea.logistics.otp.order.common.flow.helper.FlowHelper;
import com.midea.logistics.otp.order.domain.request.FlowRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.midea.logistics.imp.orderverify.service.PreposeTaskService;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.PreposeTaskFeign;
import com.midea.logistics.otp.common.feign.servicefeign.task.TaskFeign;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.order.common.fegin.orderAgg.OrderAggFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.PreposeTask;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PreposeTaskServiceImpl implements PreposeTaskService {


    @Autowired
    private PreposeTaskFeign preposeTaskFeign;
    @Autowired
    private TaskFeign taskFeign;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private OrderAggFeign orderAggFeign;
    @Autowired
    private FlowHelper flowHelper;

    /**
    * !@集拼 - 2.生成前置任务（后分仓）
    * @param: [customerOrderInfoExt]
    * @return: com.midea.logistics.otp.order.domain.bean.PreposeTask
    */
    @Override
    @ZeebeFlow("GENERATE_PREPOSE_TASK")
    public CustomerOrderInfoExt generatePreposeTask(CustomerOrderInfoExt customerOrderInfoExt) {
        log.info("GENERATE_PREPOSE_TASK:"+customerOrderInfoExt.getOrderNo());
            //判断是否有前置任务并且集拼了，否则创建了需要hold住流程
            if(customerOrderInfoExt==null || StringUtils.isEmpty(customerOrderInfoExt.getOrderNo())){
                throw BusinessException.fail("订单号不能为空!");
            }
        // !@后分仓，货权转移订单处理
        if (JoinType.isJoinType(customerOrderInfoExt.getJoinType())) {
            FlowRequest obj = new FlowRequest();
            obj.setParentOrderNo(customerOrderInfoExt.getOrderNo());
            flowHelper.startSubFlow(obj);
            return customerOrderInfoExt;
        }
            JsonResponse<List<PreposeTask>> jsonResponse =   preposeTaskFeign.listByOrderNos(Arrays.asList(customerOrderInfoExt.getOrderNo()));
            if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode()) && !CollectionUtils.isEmpty(jsonResponse.getData())){
                log.info("操作前置任务，已集拼，直接返回:"+customerOrderInfoExt.getOrderNo());
                return customerOrderInfoExt;
            }

        log.info("开始创建前置任务（推送集拼池）"+customerOrderInfoExt.getOrderNo());
        JsonResponse<PreposeTask> response = preposeTaskFeign.generatePreposeTaskByOrderNo(customerOrderInfoExt.getOrderNo());
       if(!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) || null == response.getData() ){
           throw BusinessException.fail("创建前置任务失败"+response.getMsg());
       }
    
      //  FlowListenerParam.holdAndAddLog(String.format("订单"+customerOrderInfoExt.getOrderNo()+"已挂起，请到集拼池集拼后再审核"),null);
        FlowListenerParam.success("已生成前置任务并推送集拼池，请前往集拼模块操作!");
        return customerOrderInfoExt;
    }

    @Override
    @ZeebeFlow("GENERATE_PREPOSE_TASK_BY_TASK")
    public OrderInfoExt generatePreposeTaskByTask(OrderInfoExt orderInfo) {
        log.info("generatePreposeTaskByTask:"+orderInfo.getParentOrderNo());
        //判断是否有前置任务并且集拼了，否则创建了需要hold住流程
        if(orderInfo==null || StringUtils.isEmpty(orderInfo.getOrderNo())){
            throw BusinessException.fail("订单号不能为空!");
        }

        JsonResponse<List<Task>> jsonResponseTasks =  taskFeign.selectTaskListByOrderNo(orderInfo.getOrderNo());

        List<Task> taskList = new ArrayList<>();
        if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResponseTasks.getCode()) && !CollectionUtils.isEmpty(jsonResponseTasks.getData())){
            List<Task> tasks = jsonResponseTasks.getData();

            for(Task task:tasks){
                log.info("判断现有任务是否已经生成前置任务,任务号:"+task.getTaskNo());
                PreposeTask preposeTask = new PreposeTask();
                preposeTask.setRelationTaskNo(task.getTaskNo());
                JsonResponse<PreposeTask> jsonResponse = preposeTaskFeign.searchOne(preposeTask);
                if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())){
                    //已生成前置任务
                   if(jsonResponse.getData()!=null){
                       log.info("根据任务查询前置任务，前置任务:" + jsonResponse.getData().getPreposeTaskNo());
                       continue;
                   }else {
                       taskList.add(task);
                   }
                }else{
                    throw BusinessException.fail("查询前置任务失败!"+jsonResponse.getMsg());
                }
            }

        }else{
            throw BusinessException.fail("根据子单号查询任务为空或者失败!"+jsonResponseTasks.getMsg());
        }

        //没有需要重建前置任务的任务
        if(CollectionUtils.isEmpty(taskList)){
            FlowListenerParam.success("已生成前置任务，无需生成前置任务!");
            return orderInfo;
        }
        List<TaskExt> taskExtList = new ArrayList<>();
        for(Task task : taskList){
           JsonResponse<TaskExt> taskExtJsonResponse = taskFeign.queryTaskExtByTask(task);
           if(BaseCodeEnum.FAILED.getCode().equals(taskExtJsonResponse.getCode()) || jsonResponseTasks.getData()==null){
               throw BusinessException.fail("查询任务和任务明细失败"+jsonResponseTasks.getMsg());
           }
            taskExtList.add(taskExtJsonResponse.getData());
        }

        JsonResponse<Integer> response = preposeTaskFeign.generatePreposeTaskBytask(taskExtList);
        if(!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) || null == response.getData() || response.getData()<=0){
            throw BusinessException.fail("创建前置任务失败"+response.getMsg());
        }
        log.info("生成前置任务:" + orderInfo.getOrderNo());
        FlowListenerParam.success("已推送集拼池，请前往集拼模块操作");
        return orderInfo;
    }
}
