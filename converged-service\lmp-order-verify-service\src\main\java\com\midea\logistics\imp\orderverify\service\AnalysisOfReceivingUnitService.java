package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;

public interface AnalysisOfReceivingUnitService {

    CustomerOrderInfoExt explainReceivePlatformAndCustomer(CustomerOrderInfoExt customerOrderInfo);


    OrderInfoExt analysisProjectClassify(OrderInfoExt orderInfo);



}
