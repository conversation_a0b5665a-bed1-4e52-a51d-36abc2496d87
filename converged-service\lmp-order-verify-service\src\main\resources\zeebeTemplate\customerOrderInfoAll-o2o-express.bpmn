<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0tksg34" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all-o2o-express" name="父订单流程-o2o-express" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_112xeyl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_1i77hzc" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_112xeyl</bpmn:incoming>
      <bpmn:outgoing>Flow_1em0vd0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1yp3n1z" name="商品确认">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mappingOfc" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1em0vd0</bpmn:incoming>
      <bpmn:outgoing>Flow_0zjhpiv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0cvbk2j" name="标准产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/standardProductAnalysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zjhpiv</bpmn:incoming>
      <bpmn:outgoing>Flow_14hy1ah</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0bj6n29" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_14hy1ah</bpmn:incoming>
      <bpmn:outgoing>Flow_1b4x3oq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1em0vd0" sourceRef="Activity_1i77hzc" targetRef="Activity_1yp3n1z" />
    <bpmn:sequenceFlow id="Flow_0zjhpiv" sourceRef="Activity_1yp3n1z" targetRef="Activity_0cvbk2j" />
    <bpmn:sequenceFlow id="Flow_14hy1ah" sourceRef="Activity_0cvbk2j" targetRef="Activity_0bj6n29" />
    <bpmn:sequenceFlow id="Flow_112xeyl" sourceRef="StartEvent_1" targetRef="Activity_1i77hzc" />
    <bpmn:endEvent id="Event_1mas74y" name="结束">
      <bpmn:incoming>Flow_1b4x3oq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1b4x3oq" sourceRef="Activity_0bj6n29" targetRef="Event_1mas74y" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all-o2o-express">
      <bpmndi:BPMNEdge id="Flow_1em0vd0_di" bpmnElement="Flow_1em0vd0">
        <di:waypoint x="350" y="117" />
        <di:waypoint x="410" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zjhpiv_di" bpmnElement="Flow_0zjhpiv">
        <di:waypoint x="510" y="117" />
        <di:waypoint x="580" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14hy1ah_di" bpmnElement="Flow_14hy1ah">
        <di:waypoint x="680" y="117" />
        <di:waypoint x="740" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_112xeyl_di" bpmnElement="Flow_112xeyl">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="250" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b4x3oq_di" bpmnElement="Flow_1b4x3oq">
        <di:waypoint x="840" y="117" />
        <di:waypoint x="902" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1i77hzc_di" bpmnElement="Activity_1i77hzc">
        <dc:Bounds x="250" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1yp3n1z_di" bpmnElement="Activity_1yp3n1z">
        <dc:Bounds x="410" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cvbk2j_di" bpmnElement="Activity_0cvbk2j">
        <dc:Bounds x="580" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0bj6n29_di" bpmnElement="Activity_0bj6n29">
        <dc:Bounds x="740" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mas74y_di" bpmnElement="Event_1mas74y">
        <dc:Bounds x="902" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="910" y="75" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
