package com.midea.logistics.otp.ttx.service.impl;

import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder;
import com.midea.logistics.otp.ttx.mapper.otp.InboundOrderMapper;
import com.midea.logistics.otp.ttx.service.InboundOrderService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.PageResponse;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;

/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: InboundOrderServiceImpl
* Author: yaowl
* Date: 2019-8-21 19:21:37
* Description:InboundOrderServiceImpl类
*/
@Service
public class InboundOrderServiceImpl implements InboundOrderService {

    private static final Logger logger = LoggerFactory.getLogger( InboundOrderServiceImpl.class );

    @Autowired
    private InboundOrderMapper inboundOrderMapper;
    @Autowired
    private UpDownloadHelper upDownloadHelper;
    /**
     * 数据唯一约束实现(每一个约束都要实现)
     * @param inboundOrder
     * @return
     */
    private InboundOrder selectDuplicate(InboundOrder inboundOrder) {
        InboundOrder duplicateQuery=new InboundOrder();
        if(inboundOrder.getId()!=null) {
            duplicateQuery.setId(inboundOrder.getId());
            duplicateQuery.setDeleteFlag(null);
            return inboundOrderMapper.selectOne(duplicateQuery);
        }
        else{
            return null;
        }

    }

    /**
     * 把业务参数复制到pojo中
     * @param source
     * @param target
     */
    private void swapBizData(InboundOrder source, InboundOrder target){
        BeanUtils.copyProperties(source,target,"id","updateUserCode","updateTime","version");
    }


    @Override
    public Integer save(InboundOrder inboundOrder) {
        if( null == inboundOrder) {
            logger.warn("save inboundOrder, but inboundOrder is null...");
            return null;
        }
        InboundOrder duplicate = selectDuplicate(inboundOrder);
        if(duplicate == null){
            return inboundOrderMapper.save(inboundOrder);
        }
        if(duplicate.getDeleteFlag()==1){
            swapBizData(inboundOrder,duplicate);
            duplicate.setDeleteFlag(0);
            return  inboundOrderMapper.updateById(duplicate);
        }
        throw BusinessException.fail("数据已存在");
    }


    @Override
    public InboundOrder update(InboundOrder inboundOrder) {
        if( null == inboundOrder || null == inboundOrder.getId()) {
            logger.warn("update inboundOrder, but inboundOrder is null  or inboundOrder id is null...");
            return null;
        }
        InboundOrder beforChange = selectById(inboundOrder);
        if(beforChange!=null){
            InboundOrder afterChange=new InboundOrder();
            BeanUtils.copyProperties(beforChange,afterChange);
            swapBizData(inboundOrder,afterChange);
            afterChange.setUpdateUserCode(inboundOrder.getUpdateUserCode());
            afterChange.setDeleteFlag(0);
            afterChange.setUpdateTime(new Date());
            inboundOrderMapper.updateById(afterChange);
            return afterChange;
        }
        throw BusinessException.fail("数据不存在");
    }

    @Override
    public Integer batchUpdate(List<InboundOrder> list) {
        if( null == list ||list.isEmpty()) {
            logger.warn("update inboundOrderList, but inboundOrderList is null  or inboundOrderList  is empty...");
            return null;
        }
       return inboundOrderMapper.batchUpdate(list);
    }

    @Override
    public InboundOrder selectOne(InboundOrder inboundOrder) {
        if( inboundOrder == null) {
            logger.warn("select inboundOrder one, but inboundOrder is null ...");
            return null;
        }
        return inboundOrderMapper.selectOne(inboundOrder);
    }

    @Override
    public InboundOrder selectById(InboundOrder inboundOrder) {
        if(inboundOrder.getId()==null){
            logger.warn("select inboundOrder by id, but inboundOrder.id is null ...");
            return null;
        }
        InboundOrder idQuery=new InboundOrder();
        idQuery.setId(inboundOrder.getId());
        return inboundOrderMapper.selectOne(idQuery);
    }

    @Override
    public List<InboundOrder> select(InboundOrder inboundOrder) {
        List<InboundOrder> inboundOrders = new ArrayList<>();
        if( inboundOrder == null) {
            logger.warn("select inboundOrder by index, but inboundOrder is null ...");
            return inboundOrders;
        }
        return inboundOrderMapper.selectByIndex(inboundOrder);
    }

    @Override
    public int count(InboundOrder inboundOrder) {
        int count = 0;
        if( inboundOrder == null) {
            logger.warn("count inboundOrder by index, but inboundOrder is null ...");
            return count;
        }
        return inboundOrderMapper.selectByIndexCount(inboundOrder);
    }

    /**
    * @Description: 查询，带分页
    * @Param: [inboundOrder]
    * @return: com.mideaframework.core.web.PageResponse<com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder>
    * @Author: yaowl
    * @Date: 2019-8-22
    */
    @Override
    public PageResponse<InboundOrder> selectPage(InboundOrder inboundOrder) {
        PageResponse<InboundOrder> pageResponse = new PageResponse<>();
        if( null == inboundOrder) {
            logger.warn("select inboundOrder page, but inboundOrder is null...");
            return pageResponse;
        }
        Integer total = count(inboundOrder);
        if( total!=null && total <= 0 ) {
            logger.info("select inboundOrder page , but count {} == 0 ...",total);
            return pageResponse;
        }
        List<InboundOrder> inboundOrderList =  select(inboundOrder);
        pageResponse.init(inboundOrder.getPageNo(), inboundOrder.getPageSize(),total, inboundOrderList);

        return pageResponse;
    }


    /**
    * @Description: 下载模板
    * @Param: [response, request]
    * @return: java.lang.Boolean
    * @Author: yaowl
    * @Date: 2019-8-22
    */
    @Override
    public void inboundOrderTemplateDownload(HttpServletRequest request, HttpServletResponse response) {

        ServletOutputStream out = null;
        String file ="inbound_order_template.xlsx";
        try (
            InputStream fis = this.getClass().getResourceAsStream("/printTemplates/" + file);
            XSSFWorkbook workbook = new XSSFWorkbook(fis)
        ) {
            response.setContentType("application/binary;charset=ISO8859-1");
            String fileName = URLEncoder.encode(file, "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            logger.info(e.getMessage());
        } finally {
            //关闭文件输出流
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                logger.error("文件流关闭失败{}", e.getMessage());
            }
        }
    }

    @Override
    public ImportFileRequest inboundOrderUpload(@RequestParam("file") MultipartFile file){
        return upDownloadHelper.uploadFileToOss(file);
    }
}