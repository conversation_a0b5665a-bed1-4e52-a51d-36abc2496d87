
package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

@Data
public class LocQueryRes {

    private String rowState;
    private String rownum;
    private String whCode;
    private String whName;
    private String skuCode;
    private String custMaterialNo;
    private String ownerCode;
    private String skuName;
    private String ownerName;
    private String lotCode;
    private String lotNum;
    private String traceId;
    private String lotAtt01;
    private String lotAtt02;
    private String lotAtt03;
    private String lotAtt04;
    private String lotAtt05;
    private String lotAtt06;
    private String lotAtt07;
    private String lotAtt08;
    private String lotAtt09;
    private String lotAtt10;
    private String lotAtt11;
    private String lotAtt12;
    private int qty;

}