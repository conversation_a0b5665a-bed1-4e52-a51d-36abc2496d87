package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.MipExecutionLog;
import com.midea.logistics.otp.order.domain.bean.custom.DeleteChangeApplyIflow;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: MipExecutionLogFeign
* Author: luoh
* Date: 2020-1-15 10:56:30
* Description:mip流程日志 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface MipExecutionLogFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/mipExecutionLog", method = RequestMethod.POST)
    JsonResponse create(@RequestBody MipExecutionLog mipExecutionLog);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipExecutionLog/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipExecutionLog/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody MipExecutionLog mipExecutionLog);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipExecutionLog/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/mipExecutionLogs", method = RequestMethod.POST)
    JsonResponse search(@RequestBody MipExecutionLog mipExecutionLog);

    @RequestMapping(value = "/mipExecutionLog/selectOne", method = RequestMethod.POST)
    JsonResponse<MipExecutionLog> selectOne(@RequestBody MipExecutionLog mipExecutionLog);


    @RequestMapping(value = "/selectAndUpdateChangeApplyDraft", method = RequestMethod.POST)
    JsonResponse selectAndUpdateChangeApplyDraft(@RequestBody DeleteChangeApplyIflow deleteChangeApplyIflow);

}
