package com.midea.logistics.otp.orderverify.rest;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.orderverify.service.ExpressAddressService;
import com.mideaframework.core.web.JsonResponse;

/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: AddressResolvingManager
 * @author: crystal
 * @date: 2019/6/29 10:58
 * @description: 解析四级地址
 */
@RestController
public class AddressResolvingRest {
    private Logger logger = LoggerFactory.getLogger(AddressResolvingRest.class);

    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private ExpressAddressService expressAddressService;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    /**
     * 解析四级地址
     */
    @RequestMapping(value = "/expressAddressResolving", method = RequestMethod.GET)
    public JsonResponse<FlowListenerParam> queryForCountry(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        JsonResponse<FlowListenerParam> jsonResponse = new JsonResponse<>();
        CustomerOrderInfoExt customerOrderInfoExt = orderverifyHelper.getCustomerOrderInfoExt(orderNo);
        FlowListenerParam flowListenerParam = expressAddressService.addressMapping(customerOrderInfoExt);
        jsonResponse.setData(flowListenerParam);
        return jsonResponse;
    }

    /**
     * 解析四级地址（仅仅方便测试人员测试）
     */
    @RequestMapping(value = "/testExpressAddressResolving", method = RequestMethod.GET)
    public JsonResponse<FlowListenerParam> testAddressResolving(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        JsonResponse<FlowListenerParam> jsonResponse = new JsonResponse<>();
        try {
            CustomerOrderInfoExt customerOrderInfoExt = orderverifyHelper.getCustomerOrderInfoExt(orderNo);
            FlowListenerParam flowListenerParam = expressAddressService.addressMapping(customerOrderInfoExt);
            CustomerOrderAddress address = orderFlowHelper.getCustomerOrderAddress(orderNo);
            FlowStatus flowStatus = flowListenerParam.getFlowStatus();
            if (FlowStatus.SUCCESS == flowStatus) {
                address.setRemark("地址解析成功");
            }else{
                address.setRemark(flowListenerParam.getErrorMsg());
            }
            orderFlowHelper.updateCustomerOrderAddress(address);
            jsonResponse.setData(flowListenerParam);
        } catch (Exception e) {
            CustomerOrderAddress address = orderFlowHelper.getCustomerOrderAddress(orderNo);
            address.setRemark(e.getMessage());
            orderFlowHelper.updateCustomerOrderAddress(address);
            e.printStackTrace();
        }
        return jsonResponse;
    }

}
