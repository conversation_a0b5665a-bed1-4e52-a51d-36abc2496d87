package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.custom.InventoryFlowExt;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: InventoryFlowSearchFeign
 * Author: zhanghz115
 * Date: 2024-8-20 09:08:30
 * Description:库存流水查询 feign
 */
@FeignClient(value = "logistics-search-agg-service")
public interface InventoryFlowSearchFeign {

    @GetMapping(value = "/inventoryFlows")
    JsonResponse<PageResponse<InventoryFlowExt>> search(@SpringQueryMap InventoryFlowExt inventoryFlow);

    @GetMapping(value = "/inventoryFlowsCount")
    JsonResponse<Integer> inventoryFlowsCount(@SpringQueryMap InventoryFlowExt inventoryFlow);

}
