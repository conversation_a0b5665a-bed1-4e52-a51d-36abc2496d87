<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.custom.TaskCustomMapper">
    
    <update id="batchUpdateDriverQueueCode">
        update task t
        set t.driver_queue_code = #{driverQueueCode}
        where t.id in
        <foreach collection="taskIds" item="taskId"
                 open="(" close=")" separator=",">

            #{taskId}

        </foreach>
    </update>

    <select id="selectTask" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        `order_type` AS orderType,
        `task_status` AS taskStatus,
        `task_no` AS taskNo,
        `task_type` AS taskType,
        `excute_status` AS excuteStatus,
        `parent_order_no` AS parentOrderNo,
        `order_no` AS orderNo,
        `order_status` AS orderStatus,
        wh_system AS whSystem,
        wh_code AS whCode,
        join_type AS joinType,
        id AS id
        from task
        where delete_flag=0 and `parent_order_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </select>

    <select id="listByParentOrderNos" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task
        where delete_flag=0 and `parent_order_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </select>

    <update id="updateDispatchNo">
        <foreach collection="list" item="item" separator=";">
            <if test="item.taskNo != null and item.taskNo != ''">
                UPDATE task
                set `dispatch_no` = #{item.dispatchNo}
                <if test="item.taskStatus !=null">
                    , `task_status` =#{item.taskStatus}
                </if>
                <if test="item.orderStatus !=null">
                    , `order_status` =#{item.orderStatus}
                </if>
                <if test="item.deliveryType !=null and item.deliveryType != ''">
                    , `delivery_type` =#{item.deliveryType}
                </if>
                <if test="item.customerAgingCode !=null and item.customerAgingCode != ''">
                    , `customer_aging_code`  = #{item.customerAgingCode}
                </if>
                <if test="item.jpOrderNo !=null and item.jpOrderNo != ''">
                    , `jp_order_no`  = #{item.jpOrderNo}
                </if>
                <if test="item.deliveredVerifyFlag !=null">
                    , `delivered_verify_flag`  = #{item.deliveredVerifyFlag}
                </if>
                <if test="item.taskType !=null and item.taskType != ''">
                    , `task_type`  = #{item.taskType}
                </if>
                <if test="item.inOutType !=null and item.inOutType != ''">
                    , `in_out_type`  = #{item.inOutType}
                </if>
                <if test="item.whCode !=null">
                    , `wh_code` =#{item.whCode}
                </if>
                <if test="item.lineAging != null">
                    ,`line_aging` = #{item.lineAging}
                </if>
                <if test="item.lineName != null">
                    ,`line_name` = #{item.lineName}
                </if>
                <if test="item.lineCost != null">
                    ,`line_cost` = #{item.lineCost}
                </if>
                <if test="item.lineMileage != null">
                    ,`line_mileage` = #{item.lineMileage}
                </if>
                <if test="item.lineCode !=null">
                    ,`line_code` = #{item.lineCode}
                </if>
                where
                `task_no` =#{item.taskNo}
            </if>
        </foreach>
    </update>

    <update id="updateLine">
        <foreach collection="list" item="item" separator=";">
            <if test="item.taskNo != null and item.taskNo != ''">
                UPDATE task
                set `version` = `version` + 1,`dispatch_no` = #{item.dispatchNo}
                <if test="item.taskStatus !=null">
                    , `task_status` =#{item.taskStatus}
                </if>
                <if test="item.orderStatus !=null">
                    , `order_status` =#{item.orderStatus}
                </if>
                <if test="item.deliveryType !=null and item.deliveryType != ''">
                    , `delivery_type` =#{item.deliveryType}
                </if>
                <if test="item.customerAgingCode !=null and item.customerAgingCode != ''">
                    , `customer_aging_code`  = #{item.customerAgingCode}
                </if>
                <if test="item.jpOrderNo !=null and item.jpOrderNo != ''">
                    , `jp_order_no`  = #{item.jpOrderNo}
                </if>
                <if test="item.deliveredVerifyFlag !=null">
                    , `delivered_verify_flag`  = #{item.deliveredVerifyFlag}
                </if>
                <if test="item.taskType !=null and item.taskType != ''">
                    , `task_type`  = #{item.taskType}
                </if>
                <if test="item.inOutType !=null and item.inOutType != ''">
                    , `in_out_type`  = #{item.inOutType}
                </if>
                <if test="item.whCode !=null">
                    , `wh_code` =#{item.whCode}
                </if>
                <if test="item.lineAging != null">
                    ,`line_aging` = #{item.lineAging}
                </if>
                <if test="item.lineName != null">
                    ,`line_name` = #{item.lineName}
                </if>
                <if test="item.lineCost != null">
                    ,`line_cost` = #{item.lineCost}
                </if>
                <if test="item.lineMileage != null">
                    ,`line_mileage` = #{item.lineMileage}
                </if>
                <if test="item.lineCode !=null">
                    ,`line_code` = #{item.lineCode}
                </if>
                where
                `task_no` =#{item.taskNo}
            </if>
        </foreach>
    </update>

    <update id="update">
        UPDATE task
        set `task_status` = #{taskStatus}
        <if test="orderStatus !=null">
            ,`order_status` =#{orderStatus}
        </if>
        <if test="excuteStatus !=null">
            ,`excute_status` =#{excuteStatus}
        </if>
        where
        `task_no` =#{taskNo}
    </update>

    <update id="updateStatusByTaskNos">
        UPDATE task
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderStatus !=null">
                `order_status` = #{orderStatus},
            </if>
            <if test="taskStatus !=null">
                `task_status` = #{taskStatus},
            </if>
            <if test="excuteStatus !=null">
                `excute_status` =#{excuteStatus},
            </if>
        </trim>
        where `task_no` in
        <foreach collection="taskNos" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <update id="clearDistribution">
        update task a set `version` = `version` + 1
        <if test="distributionFlag !=null">
            ,a.distribution_flag=#{distributionFlag}
        </if>
        <if test="distributionWhCode !=null">
            ,a.`distribution_wh_code` =#{distributionWhCode}
        </if>
        <if test="distributionSiteCode !=null">
            ,a.`distribution_site_code` =#{distributionSiteCode}
        </if>
        <if test="nextDistributionSiteCode !=null">
            ,a.`next_distribution_site_code` =#{nextDistributionSiteCode}
        </if>
        <if test="nextDistributionWhCode !=null">
            ,a.`next_distribution_wh_code` =#{nextDistributionWhCode}
        </if>
        <if test="distributionSiteName !=null">
            ,a.`distribution_site_name` =#{distributionSiteName}
        </if>
        <if test="distributionWhName !=null">
            ,a.`distribution_wh_name` =#{distributionWhName}
        </if>
        <if test="distributionLastFlag !=null">
            ,a.`distribution_last_flag` =#{distributionLastFlag}
        </if>
        <if test="distributionNum !=null">
            ,a.`distribution_num` =#{distributionNum}
        </if>
        <if test="targeSiteCode !=null">
            ,a.`targe_site_code` =#{targeSiteCode}
        </if>
        <if test="targeWhCode !=null">
            ,a.`targe_wh_code` =#{targeWhCode}
        </if>
        where a.task_no=#{taskNo}
    </update>

    <update id="updateDistribution">
        update task a set `version` = `version` + 1
        <if test="distributionFlag !=null">
            ,a.distribution_flag=#{distributionFlag}
        </if>
        <if test="distributionWhCode !=null">
            ,a.`distribution_wh_code` =#{distributionWhCode}
        </if>
        <if test="distributionSiteCode !=null">
            ,a.`distribution_site_code` =#{distributionSiteCode}
        </if>
        <if test="nextDistributionSiteCode !=null">
            ,a.`next_distribution_site_code` =#{nextDistributionSiteCode}
        </if>
        <if test="nextDistributionWhCode !=null">
            ,a.`next_distribution_wh_code` =#{nextDistributionWhCode}
        </if>
        <if test="distributionSiteName !=null">
            ,a.`distribution_site_name` =#{distributionSiteName}
        </if>
        <if test="distributionWhName !=null">
            ,a.`distribution_wh_name` =#{distributionWhName}
        </if>
        <if test="distributionStationType !=null">
            ,a.`distribution_station_type` =#{distributionStationType}
        </if>
        <if test="distributionLastFlag !=null">
            ,a.`distribution_last_flag` =#{distributionLastFlag}
        </if>
        <if test="distributionNum !=null">
            ,a.`distribution_num` =#{distributionNum}
        </if>
        <if test="targeSiteCode !=null">
            ,a.`targe_site_code` =#{targeSiteCode}
        </if>
        <if test="targeWhCode !=null">
            ,a.`targe_wh_code` =#{targeWhCode}
        </if>
        <if test="customerAgingCode != null and customerAgingCode != ''">
            ,a.`customer_aging_code` = #{customerAgingCode}
        </if>
        <if test="whCode !=null">
            ,a.`wh_code` =#{whCode}
        </if>
        <if test="siteCode !=null">
            ,a.`site_code` =#{siteCode}
        </if>
        <if test="siteName !=null">
            ,a.`site_name` =#{siteName}
        </if>
        <if test="whName !=null">
            ,a.`wh_name` =#{whName}
        </if>
        <if test="stationType !=null">
            ,a.`station_type` =#{stationType}
        </if>
        <if test="transferFlag !=null">
            ,a.`transfer_flag` =#{transferFlag}
        </if>
        <if test="professionalCompany !=null and professionalCompany != ''">
            ,a.`professional_company` = #{professionalCompany}
        </if>
        where a.task_no=#{taskNo}
    </update>

    <select id="deliveryCount" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select count(total_volume) total_volume, count(total_qty) total_qty from task
        where delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <![CDATA[
          and `order_status` < #{orderStatus}
         ]]>
    </select>


    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="orderStatus !=null and orderStatus != ''">
            and `order_status` =#{orderStatus}
        </if>
    </sql>

    <select id="getTasksByOrderNos" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task
        where `order_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
        and delete_flag = 0
    </select>

    <select id="getTasksByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task
        where `task_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
        and delete_flag = 0
    </select>

    <select id="searchTaskListCount" resultType="Integer">
        select count(1)
        from task t
        <if test="siteCode !=null and siteCode != '' and createStartTime !=null and createStartTime !='' and createEndTime !=null and createEndTime !='' and ignoreIndex==1">
            IGNORE INDEX (create_time_index)
        </if>
        left join task_address a on a.task_no = t.task_no and a.delete_flag=0
        <choose>
            <when test="reDistributeList != null and reDistributeList.size() >0 or sourceBuyerTenantType != null">
                LEFT JOIN order_extend e ON e.order_no = t.order_no AND e.delete_flag=0
                <if test="sourceBuyerTenantType !=null">
                    and e.`source_buyer_tenant_type` = #{sourceBuyerTenantType}
                </if>
            </when>
        </choose>
        <choose>
            <when test="anntoCarrierCodeList != null and anntoCarrierCodeList.size() >0">
                LEFT JOIN task_extend te ON te.task_no = t.task_no AND te.delete_flag=0
            </when>
        </choose>
        <choose>
            <when test="preAppointWayList == null and preAppointStatusList == null and preAppointStatus ==null and exceptionType ==null">
            </when>
            <otherwise>
                LEFT JOIN order_appointment_info p FORCE INDEX(index_order_no) ON p.order_no = t.order_no AND p.delete_flag=0
            </otherwise>
        </choose>
        <choose>
            <when test="requireOutEndTime ==null and requireOutStartTime==null" >
            </when>
            <otherwise>
                LEFT JOIN order_aging o ON t.order_no = o.order_no AND o.order_status = 370 AND o.delete_flag=0
            </otherwise>
        </choose>
        <include refid="whereFieldsSql4"/>
    </select>


    <select id="searchTaskListCountTaskTable" resultType="Integer">
        select count(1)
        from task t
        <include refid="whereFieldsSql3"/>
    </select>



    <select id="searchTaskList" resultType="com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse">
        select
        t.`id` AS  id,
        t.`arrive_appointment_require_flag` AS  arriveAppointmentRequireFlag,
        t.`task_no` AS  taskNo,
        t.`order_no` AS  orderNo,
        t.`customer_order_no` AS  customerOrderNo,
        t.`waybill_no` AS  waybillNo,
        t.`delivery_type` AS  deliveryType,
        t.`task_type` AS  taskType,
        t.service_type AS serviceType,
        t.`order_status` AS  orderStatus,
        t.`excute_status` AS  excuteStatus,
        t.`customer_code` AS  customerCode,
        t.`site_code` AS  siteCode,
        t.`site_name` AS  siteName,
        t.`targe_site_code` AS targeSiteCode,
        t.`wh_code` AS  whCode,
        t.`total_qty` AS  totalQty,
        t.`total_volume` AS  totalVolume,
        t.`total_net_weight` AS  totalNetWeight,
        t.`upper_receiver_name` AS upperReceiverName,
        t.`order_value` AS  orderValue,
        t.`order_type` AS  orderType,
        t.`company_name` AS  companyName,
        t.`total_gross_weight` AS  totalGrossWeight,
        t.`wh_name` AS  whName,
        t.`task_status` AS  taskStatus,
        t.`aging_product_code` AS  agingProductCode,
        t.`customer_aging_code` AS  customerAgingCode,
        t.`relation_order_no` AS  relationOrderNo,
        t.`customer_name` AS  customerName,
        t.`upstream_doc_type` AS  upstreamDocType,
        t.`source_system` AS  sourceSystem,
        t.`distribution_flag` AS  distributionFlag,
        t.`distribution_last_flag` AS  distributionLastFlag,
        t.`distribution_num` AS  distributionNum,
        t.`contract_no` AS  contractNo,
        t.`project_classify` AS  projectClassify,
        t.`specimen_type` AS  specimenType,
        t.`business_mode` AS  businessMode,
        t.`update_time` AS  updateTime,
        t.`create_time` AS  createTime,
        t.`parent_order_no` AS  parentOrderNo,
        t.`delete_flag` AS  deleteFlag,
        t.`company_code` AS  companyCode,
        t.`appointment_type` AS  appointmentType,
        t.`order_source_platform` AS  orderSourcePlatform,
        t.`remark` AS  remark,
        t.`appointment_reason` AS  appointmentReason,
        t.`in_out_type` AS  inOutType,
        t.`logistic_mode` AS  logisticMode,
        t.`sc_pos_flag` AS  scPosFlag,
        t.`appointment_time` AS  appointmentTime,
        t.`transport_type` AS  transportType,
        t.`order_source` AS  orderSource,
        t.`entity_id` AS  entityId,
        t.`join_type` AS  joinType,
        t.`consignee_time_to` AS  consigneeTimeTo,
        t.`business_type` AS  businessType,
        t.`origin_order_no` AS originOrderNo,
        t.`order_rp_flag` AS orderRpFlag,
        t.`equipment_type` AS  equipmentType,
        t.`equipment_name` AS  equipmentName,
        t.`plan_order_flag` AS  planOrderFlag,
        t.`cn_dispatch` AS  cnDispatch,
        t.`gray_flag` AS  grayFlag,
        t.`load_type` as loadType,
        t.`freight_basis` as freightBasis,
        t.`expect_install_type` AS  expectInstallType,
        t.`appoint_order_no` AS  appointOrderNo,
        t.`expect_install_start_time` AS  expectInstallStartTime,
        t.`expect_install_end_time` AS  expectInstallEndTime,
        t.`hold_flag` AS  holdFlag,
        t.`transport_system` AS  transportSystem,
        t.`transport_order_no` AS  transportOrderNo,
        t.`transfer_flag` AS  transferFlag,
        t.consolidation_order_no AS consolidationOrderNo,
        t.`professional_company` AS  professionalCompany,
        t.`carrier_code` as carrierCode,
        t.`distribution_wh_code` as distributionWhCode,
        t.`distribution_wh_Name` as distributionWhName,
        t.`dispatch_no` as dispatchNo,
        t.`emergence_flag` as emergenceFlag,
        t.`target_customer_code` as targetCustomerCode,
        t.`platform_order_no` as platformOrderNo,
        t.`upper_order_type` as upperOrderType,
        o.`plan_time` AS  requireOutTime,

        a.`receiver_name` AS  receiverName,
        a.`receiver_mobile` AS  receiverMobile,
        a.`receiver_tel` AS  receiverTel,
        a.`receiver_virtual_mobile` AS  receiverVirtualMobile,
        a.`receiver_province_name` AS  receiverProvinceName,
        a.`receiver_city_name` AS  receiverCityName,
        a.`receiver_town_name` AS  receiverTownName,
        a.`receiver_district_name` AS  receiverDistrictName,
        a.`receiver_detail_addr` AS  receiverDetailAddr,
        a.`sender_name` AS  senderName,
        a.`sender_tel` AS  senderTel,
        a.`sender_virtual_mobile` AS  senderVirtualMobile,
        a.`sender_mobile` AS  senderMobile,
        a.`sender_province_name` AS  senderProvinceName,
        a.`sender_city_name` AS  senderCityName,
        a.`sender_town_name` AS  senderTownName,
        a.`sender_district_name` AS  senderDistrictName,
        a.`sender_detail_addr` AS  senderDetailAddr,
        a.`network_name` AS  networkName,
        a.`network_code` AS  networkCode,
        a.`network_province_name` AS  networkProvinceName,
        a.`network_town_name` AS  networkTownName,
        a.`network_city_name` AS  networkCityName,
        a.`network_district_name` AS  networkDistrictName,
        a.`network_addr` AS  networkAddr,

        a.`final_mobile` AS  finalMobile,
        a.`final_name` AS  finalName,
        a.`final_tel` AS  finalTel,
        a.`final_country_code` AS  finalCountryCode,
        a.`final_country_name` AS  finalCountryName,
        a.`final_province_code` AS  finalProvinceCode,
        a.`final_province_name` AS  finalProvinceName,
        a.`final_city_code` AS  finalCityCode,
        a.`final_city_name` AS  finalCityName,
        a.`final_district_code` AS  finalDistrictCode,
        a.`final_district_name` AS  finalDistrictName,
        a.`final_town_code` AS  finalTownCode,
        a.`final_town_name` AS  finalTownName,
        a.`final_detail_addr` AS  finalDetailAddr,
        a.`rollback_flag` AS  rollbackFlag,

        e.id AS orderExtendId,
        e.`re_distribute` AS  reDistribute,
        e.`contact_name` AS  contactName,
        e.`contact_mobile` AS  contactMobile,
        e.`customer_order_no` AS  customerOrderNo,
        e.`overdue_reason1` AS  overdueReason1,
        e.`overdue_reason2` AS  overdueReason2,
        e.`overdue_time` AS  overdueTime,
        e.`overdue_remark` AS  overdueRemark,
        e.`arrive_overdue_remark` AS  arriveOverdueRemark,
        e.`arrive_overdue_contact_mobile` AS  arriveOverdueContactMobile,
        e.`arrive_overdue_time` AS  arriveOverdueTime,
        e.`arrive_overdue_contact_name` AS  arriveOverdueContactName,
        e.`arrive_overdue_reason1` AS  arriveOverdueReason1,
        e.`arrive_overdue_reason2` AS  arriveOverdueReason2,
        e.`arrive_plan_time` AS  arrivePlanTime,
        e.`customer_aging_type` AS  customerAgingType,
        ci.aging_rule_name AS customerAgingTypeName,
        e.`aging_type` AS  agingType,
        e.`source_buyer_tenant_type` as sourceBuyerTenantType,
        e.`business_category` as businessCategory,
        e.`order_line_type` as orderLineType,
        e.`order_start_time` AS  orderStartTime,
        e.`order_end_time` AS  orderEndTime,

        p.`exception_type` AS  exceptionType,
        p.arrive_start_time AS orderAppointmentTime,
        p.pre_appoint_status AS preAppointStatus,
        p.`pick_appointment_require_flag` AS  pickAppointmentRequireFlag,
        p.`arrive_time_slot` AS  arriveTimeSlot,
        p.`pick_appointment_flag` AS  pickAppointmentFlag,
        p.arrive_reschedule_times AS arriveRescheduleTimes,
        p.`exception_name` AS  exceptionName,
        p.`pre_appoint_way` AS preAppointWay,
        p.`remark` AS  appointRemark,
        p.`update_time` AS  appointCreateTime,
        p.`revisiter` AS  revisiter,
        p.`revisiter_mobile` AS  revisiterMobile,
        p.`appoint_reason` AS  appointReason,
        p.`appoint_reason_name` AS  appointReasonName,
        p.`secend_appoint_reason` AS  secendAppointReason,
        p.`secend_appoint_name` AS  secendAppointName,

        ac.aging_product_name as agingProductName,

        m.`audit_status` AS  auditStatus,
        m.`ext_frame_url` AS  extFrameUrl,

        te.`transportation_division` AS  transportationDivision,
        te.carrier_code AS anntoCarrierCode,
        te.carrier_name AS anntoCarrierName,
        e.`risk_flag` AS  riskFlag,
        e.commerce_categories AS commerceCategories,
        te.`conf_obj` AS  confObj

        <choose>
            <when test="senderProvinceCode !=null or senderCityCode !=null or senderDistrictCode !=null or senderTownCode !=null or senderDetailAddr !=null
            or finalMobile !=null or finalProvinceCode !=null or finalCityCode !=null or finalDistrictCode !=null or finalTownCode !=null
            or finalDetailAddr !=null or finalProvinceName !=null or finalCityName !=null or finalDistrictName !=null or finalTownName !=null or receiverMobile !=null
            or senderMobile !=null or networkCode !=null or rollbackFlag !=null">
                from task t
                <choose>
                    <when test="rollbackFlag !=null and rollbackFlag != ''">
                    </when>
                    <when test="siteCodeList !=null and siteCodeList.size()>0 and createStartTime !=null and createStartTime !='' and createEndTime !=null and createEndTime !='' and ignoreIndex==1">
                        IGNORE INDEX (create_time_index)
                    </when>
                </choose>
                inner join task_address a on a.task_no = t.task_no
            </when>
            <when test="(reDistributeList != null and reDistributeList.size() >0)
                or (anntoCarrierCodeList !=null and anntoCarrierCodeList.size() >0)
                or (preAppointStatus !=null and preAppointStatus != '')
                or (requireOutStartTime!=null and requireOutStartTime != '')
                or (requireOutEndTime !=null and requireOutEndTime !='')
                or (preAppointWayList != null and preAppointWayList.size() >0)
                or (preAppointStatusList != null and preAppointStatusList.size() >0)">
                from task t
                <if test="siteCodeList !=null and siteCodeList.size()>0 and createStartTime !=null and createStartTime !='' and createEndTime !=null and createEndTime !='' and ignoreIndex==1">
                    IGNORE INDEX (create_time_index)
                </if>
                left join task_address a on a.task_no = t.task_no
            </when>
            <when test="sourceBuyerTenantType !=null">
                from (select t.* from task t
                    left join order_extend e on t.order_no = e.order_no and e.delete_flag = 0
                    where t.delete_flag=0
                    <include refid="whereFieldsSqlTask"/>
                    and e.source_buyer_tenant_type = #{sourceBuyerTenantType}
                    order by t.create_time desc
                    limit ${start},${pageSize}
                ) t
                left join task_address a on a.task_no = t.task_no and a.delete_flag=0
            </when>
            <otherwise>
                from (select * from task t
                    where t.delete_flag=0
                    <include refid="whereFieldsSqlTask"/>
                    order by t.create_time desc
                limit ${start},${pageSize}
                ) t
                left join task_address a on a.task_no = t.task_no and a.delete_flag=0
            </otherwise>
        </choose>

        left join order_extend e on t.order_no = e.order_no and e.delete_flag=0
        left join standard_aging_rule_config_info ci on ci.aging_rule_code = e.customer_aging_type and ci.delete_flag=0
        left join aging_config ac FORCE INDEX (uk_aging_product_code)  on t.aging_product_code=ac.aging_product_code and ac.delete_flag=0
        left join order_appointment_info p force index(index_order_no) on t.order_no = p.order_no and p.delete_flag=0
        left join mip_execution_log m on t.task_no = m.order_no and m.delete_flag=0
        left join order_aging o on t.order_no = o.order_no and o.order_status = 370 and o.delete_flag=0
        left join task_extend te on t.task_no = te.task_no and te.delete_flag=0

        <choose>
            <when test="senderProvinceCode !=null or senderCityCode !=null or senderDistrictCode !=null or senderTownCode !=null or senderDetailAddr !=null
            or finalMobile !=null or finalProvinceCode !=null or finalCityCode !=null or finalDistrictCode !=null or finalTownCode !=null
            or finalDetailAddr !=null or finalProvinceName !=null or finalCityName !=null or finalDistrictName !=null or finalTownName !=null or receiverMobile !=null
            or senderMobile !=null or networkCode !=null or rollbackFlag !=null">
                where t.delete_flag=0
                <include refid="whereFieldsSqlTask"/>
                <include refid="whereFieldsSqlOther"/>
                order by a.id desc
                limit ${start},${pageSize}
            </when>
            <when test="(reDistributeList != null and reDistributeList.size() >0)
                or (anntoCarrierCodeList !=null and anntoCarrierCodeList.size() >0)
                or (preAppointStatus !=null and preAppointStatus != '')
                or (requireOutStartTime!=null and requireOutStartTime != '')
                or (requireOutEndTime !=null and requireOutEndTime !='')
                or (preAppointWayList != null and preAppointWayList.size() >0)
                or (preAppointStatusList != null and preAppointStatusList.size() >0)">
                where t.delete_flag=0
                <include refid="whereFieldsSqlTask"/>
                <include refid="whereFieldsSqlOther"/>
                order by t.create_time desc
                limit ${start},${pageSize}
            </when>
            <otherwise>
                where a.delete_flag=0
                <include refid="whereFieldsSqlOther"/>
            </otherwise>
        </choose>
    </select>

    <select id="searchTaskOutListCount" resultType="Integer">
        select count(1)
        from task t
        inner join task_address a on a.task_no = t.task_no and a.delete_flag=0
        where t.delete_flag=0
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            and t.`customer_order_no` =#{customerOrderNo}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and t.`customer_code` =#{customerCode}
        </if>
        <if test="excuteStatus !=null">
            and t.`excute_status` !=#{excuteStatus}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and t.`site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and t.`wh_code` =#{whCode}
        </if>
        <if test="taskType !=null and taskType != ''">
            and t.`task_type` =#{taskType}
        </if>
        <if test="joinType !=null and joinType != ''">
            and t.`join_type` =#{joinType}
        </if>
        <if test="createStartTime!=null and createStartTime != ''">
            <![CDATA[
               and t.create_time >=  DATE_FORMAT(#{createStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="createEndTime!=null and createEndTime !=''">
            <![CDATA[
              and t.create_time <=  DATE_FORMAT(#{createEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

    </select>

    <select id="searchTaskOutList" resultType="com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse">
        select
        t.`id` AS  id,
        t.`task_no` AS  taskNo,
        t.`order_no` AS  orderNo,
        t.`customer_order_no` AS  customerOrderNo,
        t.`waybill_no` AS  waybillNo,
        t.`delivery_type` AS  deliveryType,
        t.`task_type` AS  taskType,
        t.`order_status` AS  orderStatus,
        t.`excute_status` AS  excuteStatus,
        t.`customer_code` AS  customerCode,
        t.`site_code` AS  siteCode,
        t.`site_name` AS  siteName,
        t.`wh_code` AS  whCode,
        t.`total_qty` AS  totalQty,
        t.`total_volume` AS  totalVolume,
        t.`total_net_weight` AS  totalNetWeight,
        t.`upper_receiver_name` AS upperReceiverName,
        t.`order_value` AS  orderValue,
        t.`order_type` AS  orderType,
        t.`require_out_time` AS  requireOutTime,
        t.`company_name` AS  companyName,
        t.`total_gross_weight` AS  totalGrossWeight,
        t.`wh_name` AS  whName,
        t.`task_status` AS  taskStatus,
        t.`aging_product_code` AS  agingProductCode,
        t.`customer_aging_code` AS  customerAgingCode,
        t.`relation_order_no` AS  relationOrderNo,
        t.`customer_name` AS  customerName,
        t.`upstream_doc_type` AS  upstreamDocType,
        t.`source_system` AS  sourceSystem,
        t.`distribution_flag` AS  distributionFlag,
        t.`contract_no` AS  contractNo,
        t.`project_classify` AS  projectClassify,
        t.`specimen_type` AS  specimenType,
        t.`business_mode` AS  businessMode,
        t.`update_time` AS  updateTime,
        t.`create_time` AS  createTime,
        t.`parent_order_no` AS  parentOrderNo,
        t.`delete_flag` AS  deleteFlag,
        t.`company_code` AS  companyCode,
        t.`appointment_type` AS  appointmentType,
        t.`order_source_platform` AS  orderSourcePlatform,
        t.`remark` AS  remark,
        t.`appointment_reason` AS  appointmentReason,
        t.`in_out_type` AS  inOutType,
        t.`logistic_mode` AS  logisticMode,
        t.`sc_pos_flag` AS  scPosFlag,
        t.`appointment_time` AS  appointmentTime,
        t.`transport_type` AS  transportType,
        t.`order_source` AS  orderSource,
        t.`entity_id` AS  entityId,
        t.`join_type` AS  joinType,
        t.`consignee_time_to` AS  consigneeTimeTo,
        t.`business_type` AS  businessType,
        t.`origin_order_no` AS originOrderNo,
        t.`order_rp_flag` AS orderRpFlag,
        t.`equipment_type` AS  equipmentType,
        t.`equipment_name` AS  equipmentName,
        t.`plan_order_flag` AS  planOrderFlag,
        t.`cn_dispatch` AS  cnDispatch,
        t.`gray_flag` AS  grayFlag,
        t.`expect_install_type` AS  expectInstallType,
        t.`appoint_order_no` AS  appointOrderNo,
        t.`expect_install_start_time` AS  expectInstallStartTime,
        t.`expect_install_end_time` AS  expectInstallEndTime,
        t.`order_start_time` AS  orderStartTime,
        t.`order_end_time` AS  orderEndTime,
        t.`hold_flag` AS  holdFlag,
        t.`transport_system` AS  transportSystem,
        t.`transport_order_no` AS  transportOrderNo,

        a.`receiver_name` AS  receiverName,
        a.`receiver_mobile` AS  receiverMobile,
        a.`receiver_tel` AS  receiverTel,
        a.`receiver_virtual_mobile` AS  receiverVirtualMobile,
        a.`receiver_province_name` AS  receiverProvinceName,
        a.`receiver_city_name` AS  receiverCityName,
        a.`receiver_town_name` AS  receiverTownName,
        a.`receiver_district_name` AS  receiverDistrictName,
        a.`receiver_detail_addr` AS  receiverDetailAddr,
        a.`sender_name` AS  senderName,
        a.`sender_tel` AS  senderTel,
        a.`sender_mobile` AS  senderMobile,
        a.`sender_province_name` AS  senderProvinceName,
        a.`sender_city_name` AS  senderCityName,
        a.`sender_town_name` AS  senderTownName,
        a.`sender_district_name` AS  senderDistrictName,
        a.`sender_detail_addr` AS  senderDetailAddr,
        a.`network_name` AS  networkName,
        a.`network_code` AS  networkCode,
        a.`network_province_name` AS  networkProvinceName,
        a.`network_town_name` AS  networkTownName,
        a.`network_city_name` AS  networkCityName,
        a.`network_district_name` AS  networkDistrictName,
        a.`network_addr` AS  networkAddr,

        a.`final_mobile` AS  finalMobile,
        a.`final_name` AS  finalName,
        a.`final_tel` AS  finalTel,
        a.`final_country_code` AS  finalCountryCode,
        a.`final_country_name` AS  finalCountryName,
        a.`final_province_code` AS  finalProvinceCode,
        a.`final_province_name` AS  finalProvinceName,
        a.`final_city_code` AS  finalCityCode,
        a.`final_city_name` AS  finalCityName,
        a.`final_district_code` AS  finalDistrictCode,
        a.`final_district_name` AS  finalDistrictName,
        a.`final_town_code` AS  finalTownCode,
        a.`final_town_name` AS  finalTownName,
        a.`final_detail_addr` AS  finalDetailAddr
        from task t
        inner join task_address a on a.task_no = t.task_no and a.delete_flag=0
        where t.delete_flag=0
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            and t.`customer_order_no` =#{customerOrderNo}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and t.`customer_code` =#{customerCode}
        </if>
        <if test="orderStatus !=null and orderStatus != ''">
            and t.`order_status` =#{orderStatus}
        </if>
        <if test="excuteStatus !=null ">
            and t.`excute_status` &lt; #{excuteStatus}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and t.`site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and t.`wh_code` =#{whCode}
        </if>
        <if test="taskTypeList != null and taskTypeList.size() >0">
            and t.`task_type` in
            <foreach close=")" collection="taskTypeList" item="taskTypeItem" open="(" separator=",">
                #{taskTypeItem}
            </foreach>
        </if>
        <if test="joinType !=null and joinType != ''">
            and t.`join_type` =#{joinType}
        </if>
        <if test="createStartTime!=null and createStartTime != ''">
            <![CDATA[
               and t.create_time >=  DATE_FORMAT(#{createStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="createEndTime!=null and createEndTime !=''">
            <![CDATA[
              and t.create_time <=  DATE_FORMAT(#{createEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="dataAuthFlag == 1">
            and (@{"columnCode":"site_code","tableName":"es_company","custom":"coi.site_code","dataCode":"D20190621009"}@)
        </if>
        <if test="customerAuthFlag!=null and customerAuthFlag == 1">
            and (@{"columnCode":"pm_code","tableName":"eb_customer","custom":"coi.customer_code","dataCode":"D20240118001"}@)
        </if>
        order by t.create_time desc

        limit ${start},${pageSize}
    </select>



    <sql id="whereFieldsSql2">
        where t.delete_flag=0
        <if test="reDistributeList != null and reDistributeList.size() >0">
            and e.`re_distribute` in
            <foreach close=")" collection="reDistributeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="senderProvinceCode !=null and senderProvinceCode != ''">
            and a.`sender_province_code` =#{senderProvinceCode}
        </if>
        <if test="senderCityCode !=null and senderCityCode != ''">
            and a.`sender_city_code` =#{senderCityCode}
        </if>
        <if test="senderDistrictCode !=null and senderDistrictCode != ''">
            and a.`sender_district_code` =#{senderDistrictCode}
        </if>
        <if test="senderTownCode !=null and senderTownCode != ''">
            and a.`sender_town_code` =#{senderTownCode}
        </if>
        <if test="senderDetailAddr !=null and senderDetailAddr != ''">
            and a.`sender_detail_addr` like CONCAT('',#{senderDetailAddr},'%')
        </if>

        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>

        <if test="upperSenderCode !=null and upperSenderCode != ''">
            and t.upper_sender_code =#{upperSenderCode}
        </if>
        <if test="upperReceiverCode !=null and upperReceiverCode != ''">
            and t.upper_receiver_code =#{upperReceiverCode}
        </if>
        <if test="upperSenderName !=null and upperSenderName != ''">
            and t.upper_sender_name like concat('',#{upperSenderName},'%')
        </if>
        <if test="upperReceiverName !=null and upperReceiverName != ''">
            and t.upper_receiver_name like concat('',#{upperReceiverName},'%')
        </if>
        <if test="orderWhCode !=null and orderWhCode != ''">
            and t.`order_wh_code` =#{orderWhCode}
        </if>
        <if test="transportSystem !=null and transportSystem != ''">
            and t.`transport_system` =#{transportSystem}
        </if>
        <if test="transportOrderNo !=null and transportOrderNo != ''">
            and t.`transport_order_no` =#{transportOrderNo}
        </if>

        <if test="agingProductCode !=null and agingProductCode != ''">
            and t.`aging_product_code` =#{agingProductCode}
        </if>
        <if test="joinType !=null and joinType != ''">
            and t.`join_type` =#{joinType}
        </if>
        <if test="finalMobile !=null and finalMobile != ''">
            and a.`final_mobile` =#{finalMobile}
        </if>

        <if test="finalProvinceCode !=null and finalProvinceCode != ''">
            and a.final_province_code =#{finalProvinceCode}
        </if>
        <if test="finalCityCode !=null and finalCityCode != ''">
            and a.final_city_code =#{finalCityCode}
        </if>
        <if test="finalDistrictCode !=null and finalDistrictCode != ''">
            and a.final_district_code =#{finalDistrictCode}
        </if>
        <if test="finalTownCode !=null and finalTownCode != ''">
            and a.final_town_code =#{finalTownCode}
        </if>
        <if test="finalDetailAddr !=null and finalDetailAddr != ''">
            and a.final_detail_addr like CONCAT('',#{finalDetailAddr},'%')
        </if>

        <if test="finalProvinceName !=null and finalProvinceName != ''">
            and a.`final_province_name` =#{finalProvinceName}
        </if>
        <if test="finalCityName !=null and finalCityName != ''">
            and a.`final_city_name` =#{finalCityName}
        </if>
        <if test="finalDistrictName !=null and finalDistrictName != ''">
            and a.`final_district_name` =#{finalDistrictName}
        </if>
        <if test="finalTownName !=null and finalTownName != ''">
            and a.`final_town_name` =#{finalTownName}
        </if>
        <if test="receiverMobile !=null and receiverMobile != ''">
            and a.`receiver_mobile` =#{receiverMobile}
        </if>
        <if test="senderMobile !=null and senderMobile != ''">
            and a.`sender_mobile` =#{senderMobile}
        </if>
        <if test="networkCode !=null and networkCode != ''">
            and a.`network_code` =#{networkCode}
        </if>

        <if test="taskNo !=null and taskNo != ''">
            and t.`task_no` =#{taskNo}
        </if>
        <if test="taskType !=null and taskType != ''">
            and t.`task_type` =#{taskType}
        </if>
        <if test="orderNo !=null and orderNo != ''">
            and t.`order_no` =#{orderNo}
        </if>
        <if test="parentOrderNo !=null and parentOrderNo != ''">
            and t.`parent_order_no` = #{parentOrderNo}
        </if>
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            <choose>
                <when test="isFuzzy !=null and isFuzzy != ''">
                    and t.customer_order_no like concat('',#{customerOrderNo},'%')
                </when>
                <otherwise>
                    and t.customer_order_no = #{customerOrderNo}
                </otherwise>
            </choose>
        </if>
        <if test="relationOrderNo !=null and relationOrderNo != ''">
            and t.`relation_order_no` = #{relationOrderNo}
        </if>
        <if test="waybillNo !=null and waybillNo != ''">
            and t.`waybill_no` =#{waybillNo}
        </if>
        <if test="orderType !=null and orderType != ''">
            and t.`order_type` =#{orderType}
        </if>
        <if test="excuteStatus !=null">
            and t.`excute_status` =#{excuteStatus}
        </if>
        <if test="orderStatus !=null">
            and t.`order_status` =#{orderStatus}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and t.`customer_code` =#{customerCode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and t.`site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and t.`wh_code` =#{whCode}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and t.`source_system` = #{sourceSystem}
        </if>
        <if test="specCarrierFlag !=null and specCarrierFlag != ''">
            and t.`spec_carrier_flag` =#{specCarrierFlag}
        </if>
        <if test="businessMode !=null and businessMode != ''">
            and t.`business_mode` =#{businessMode}
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and t.`delivery_type` =#{deliveryType}
        </if>
        <if test="logisticMode !=null and logisticMode != ''">
            and t.`logistic_mode` =#{logisticMode}
        </if>
        <if test="serviceOrderNo !=null and serviceOrderNo != ''">
            and t.`service_order_no` =#{serviceOrderNo}
        </if>
        <if test="networkName !=null and networkName != ''">
            and a.`network_name` =#{networkName}
        </if>
        <if test="distributionFlag !=null">
            and t.`distribution_flag` =#{distributionFlag}
        </if>
        <if test="dispatchNo !=null and dispatchNo != ''">
            and t.`dispatch_no` =#{dispatchNo}
        </if>
        <if test="thirdFlag !=null and thirdFlag != ''">
            and t.`third_flag` =#{thirdFlag}
        </if>
        <if test="serviceType !=null and serviceType != ''">
            and t.`service_type` =#{serviceType}
        </if>
        <if test="emergenceFlag !=null and emergenceFlag != ''">
            and t.`emergence_flag` =#{emergenceFlag}
        </if>
        <if test="taskStatus !=null">
            <![CDATA[
            and  t.`task_status`  <= #{taskStatus}
            ]]>
        </if>
        <if test="customerGroup !=null and customerGroup != ''">
            and t.`customer_group` =#{customerGroup}
        </if>

        <if test="orderConfirmTimeStartTime!=null and orderConfirmTimeStartTime != ''">
            <![CDATA[
               and t.order_confirm_time >=  DATE_FORMAT(#{orderConfirmTimeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderConfirmTimeEndTime!=null and orderConfirmTimeEndTime != ''">
            <![CDATA[
              and t.order_confirm_time <=  DATE_FORMAT(#{orderConfirmTimeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="createStartTime!=null and createStartTime != ''">
            <![CDATA[
               and t.create_time >=  DATE_FORMAT(#{createStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="createEndTime!=null and createEndTime !=''">
            <![CDATA[
              and t.create_time <=  DATE_FORMAT(#{createEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="orderStartBTime!=null and orderStartBTime != ''">
            <![CDATA[
               and t.order_start_time >=  DATE_FORMAT(#{orderStartBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderStartETime!=null and orderStartETime !=''">
            <![CDATA[
              and t.order_start_time <=  DATE_FORMAT(#{orderStartETime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="orderEndBTime!=null and orderEndBTime != ''">
            <![CDATA[
               and t.order_end_time >=  DATE_FORMAT(#{orderEndBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderEndETime!=null and orderEndETime !=''">
            <![CDATA[
              and t.order_end_time <=  DATE_FORMAT(#{orderEndETime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="agingStartBTime!=null and agingStartBTime != ''">
            <![CDATA[
               and t.aging_start_time >=  DATE_FORMAT(#{agingStartBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="agingStartETime!=null and agingStartETime !=''">
            <![CDATA[
              and t.aging_start_time <=  DATE_FORMAT(#{agingStartETime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="expectArriveStartTimeFrom!=null and expectArriveStartTimeFrom != ''">
            <![CDATA[
               and t.expect_arrive_start_time >=  DATE_FORMAT(#{expectArriveStartTimeFrom}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveStartTimeTo!=null and expectArriveStartTimeTo !=''">
            <![CDATA[
              and t.expect_arrive_start_time <=  DATE_FORMAT(#{expectArriveStartTimeTo}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="expectArriveEndTimeFrom!=null and expectArriveEndTimeFrom != ''">
            <![CDATA[
               and t.expect_arrive_end_time >=  DATE_FORMAT(#{expectArriveEndTimeFrom}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveEndTimeTo!=null and expectArriveEndTimeTo !=''">
            <![CDATA[
              and t.expect_arrive_end_time <=  DATE_FORMAT(#{expectArriveEndTimeTo}, '%Y-%m-%d %T')
            ]]>
        </if>


        <if test="consigneeStartTime!=null and consigneeStartTime != ''">
            <![CDATA[
               and t.consignee_time_from >=  DATE_FORMAT(#{consigneeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="consigneeEndTime !=null and consigneeEndTime !=''">
            <![CDATA[
              and t.consignee_time_to <=  DATE_FORMAT(#{consigneeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="requireOutStartTime!=null and requireOutStartTime != ''">
            <![CDATA[
               and t.require_out_time >=  DATE_FORMAT(#{requireOutStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="requireOutEndTime !=null and requireOutEndTime !=''">
            <![CDATA[
              and t.require_out_time <=  DATE_FORMAT(#{requireOutEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="orderStatusList != null and orderStatusList.size() >0">
            and t.`order_status` in
            <foreach close=")" collection="orderStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="orderTypeList != null and orderTypeList.size() >0">
            and t.`order_type` in
            <foreach close=")" collection="orderTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="taskStatusList != null and taskStatusList.size() >0">
            and t.`task_status` in
            <foreach close=")" collection="taskStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="taskTypeList != null and taskTypeList.size() >0">
            and t.`task_type` in
            <foreach close=")" collection="taskTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="excuteStatusList != null and excuteStatusList.size() >0">
            and t.`excute_status` in
            <foreach close=")" collection="excuteStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="businessTypeList != null and businessTypeList.size() >0">
            and t.`business_type` in
            <foreach close=")" collection="businessTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="deliveryTypeList != null and deliveryTypeList.size() >0">
            and t.`delivery_type` in
            <foreach close=")" collection="deliveryTypeList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectClassify !=null and projectClassify != ''">
            and t.`project_classify` =#{projectClassify}
        </if>
        <if test="scPosFlag !=null">
            and t.`sc_pos_flag` =#{scPosFlag}
        </if>
        <if test="specimenType !=null and specimenType != ''">
            and t.`specimen_type` =#{specimenType}
        </if>
        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>
        <if test="contractNo !=null and contractNo != ''">
            and t.`contract_no` =#{contractNo}
        </if>
        <if test="originOrderNo !=null and originOrderNo != ''">
            and t.`origin_order_no` =#{originOrderNo}
        </if>
        <if test="inOutType !=null and inOutType != ''">
            and t.`in_out_type` =#{inOutType}
        </if>
        <if test="dataAuthFlag == 1">
            and (@{"columnCode":"site_code","tableName":"es_company","custom":"t.site_code","dataCode":"D20190621009"}@)
        </if>
        <if test="customerAuthFlag!=null and customerAuthFlag == 1">
            and (@{"columnCode":"pm_code","tableName":"eb_customer","custom":"t.customer_code","dataCode":"D20240118001"}@)
        </if>
    </sql>

    <sql id="whereFieldsSql4">
        where t.delete_flag=0

        <if test="batchQueryKey !=null and batchQueryKey != ''  and batchQueryValue !=null and batchQueryValue.size()>0 ">
            <choose>
                <when test="batchQueryKey=='customerOrderNo'">
                    and t.customer_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='taskNo'">
                    and t.task_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='waybillNo'">
                    and t.waybill_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='parentOrderNo'">
                    and t.parent_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='originOrderNo'">
                    and t.origin_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='relationOrderNo'">
                    and t.relation_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='dispatchNo'">
                    and t.dispatch_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='platformOrderNo'">
                    and t.platform_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <otherwise>
                    and t.order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="appointOrderNo !=null and appointOrderNo != ''">
            and t.`appoint_order_no` =#{appointOrderNo}
        </if>

        <if test="reDistributeList != null and reDistributeList.size() >0">
            and e.`re_distribute` in
            <foreach close=")" collection="reDistributeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="anntoCarrierCodeList != null and anntoCarrierCodeList.size() >0">
            and te.`carrier_code` in
            <foreach close=")" collection="anntoCarrierCodeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="arriveAppointmentFlag !=null and arriveAppointmentFlag == 0">
            and t.`appointment_time` is null
        </if>
        <if test="arriveAppointmentFlag !=null and arriveAppointmentFlag == 1">
            and t.`appointment_time` is not null
        </if>
        <if test="preAppointStatus !=null and preAppointStatus != ''">
            and p.pre_appoint_status =#{preAppointStatus}
        </if>
        <if test="senderProvinceCode !=null and senderProvinceCode != ''">
            and a.`sender_province_code` =#{senderProvinceCode}
        </if>
        <if test="senderCityCode !=null and senderCityCode != ''">
            and a.`sender_city_code` =#{senderCityCode}
        </if>
        <if test="senderDistrictCode !=null and senderDistrictCode != ''">
            and a.`sender_district_code` =#{senderDistrictCode}
        </if>
        <if test="senderTownCode !=null and senderTownCode != ''">
            and a.`sender_town_code` =#{senderTownCode}
        </if>
        <if test="senderDetailAddr !=null and senderDetailAddr != ''">
            and a.`sender_detail_addr` like CONCAT('',#{senderDetailAddr},'%')
        </if>
        <if test="rollbackFlag !=null and rollbackFlag != ''">
            and a.`rollback_flag` =#{rollbackFlag}
        </if>

        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>
        <if test="transportSystem !=null and transportSystem != ''">
            and t.`transport_system` =#{transportSystem}
        </if>
        <if test="transportOrderNo !=null and transportOrderNo != ''">
            and t.`transport_order_no` =#{transportOrderNo}
        </if>

        <if test="upperSenderCode !=null and upperSenderCode != ''">
            and t.upper_sender_code =#{upperSenderCode}
        </if>
        <if test="upperReceiverCode !=null and upperReceiverCode != ''">
            and t.upper_receiver_code =#{upperReceiverCode}
        </if>
        <if test="upperSenderName !=null and upperSenderName != ''">
            and t.upper_sender_name like concat('',#{upperSenderName},'%')
        </if>
        <if test="upperReceiverName !=null and upperReceiverName != ''">
            and t.upper_receiver_name like concat('',#{upperReceiverName},'%')
        </if>
        <if test="orderWhCode !=null and orderWhCode != ''">
            and t.`order_wh_code` =#{orderWhCode}
        </if>

        <if test="agingProductCode !=null and agingProductCode != ''">
            and t.`aging_product_code` =#{agingProductCode}
        </if>
        <if test="joinType !=null and joinType != ''">
            and t.`join_type` =#{joinType}
        </if>
        <if test="finalMobile !=null and finalMobile != ''">
            and a.`final_mobile` =#{finalMobile}
        </if>

        <if test="finalProvinceCode !=null and finalProvinceCode != ''">
            and a.final_province_code =#{finalProvinceCode}
        </if>
        <if test="finalCityCode !=null and finalCityCode != ''">
            and a.final_city_code =#{finalCityCode}
        </if>
        <if test="finalDistrictCode !=null and finalDistrictCode != ''">
            and a.final_district_code =#{finalDistrictCode}
        </if>
        <if test="finalTownCode !=null and finalTownCode != ''">
            and a.final_town_code =#{finalTownCode}
        </if>
        <if test="finalDetailAddr !=null and finalDetailAddr != ''">
            and a.final_detail_addr like CONCAT('',#{finalDetailAddr},'%')
        </if>

        <if test="finalProvinceName !=null and finalProvinceName != ''">
            and a.`final_province_name` =#{finalProvinceName}
        </if>
        <if test="finalCityName !=null and finalCityName != ''">
            and a.`final_city_name` =#{finalCityName}
        </if>
        <if test="finalDistrictName !=null and finalDistrictName != ''">
            and a.`final_district_name` =#{finalDistrictName}
        </if>
        <if test="finalTownName !=null and finalTownName != ''">
            and a.`final_town_name` =#{finalTownName}
        </if>
        <if test="receiverMobile !=null and receiverMobile != ''">
            and a.`receiver_mobile` =#{receiverMobile}
        </if>
        <if test="senderMobile !=null and senderMobile != ''">
            and a.`sender_mobile` =#{senderMobile}
        </if>
        <if test="networkCode !=null and networkCode != ''">
            and a.`network_code` =#{networkCode}
        </if>

        <if test="taskNo !=null and taskNo != ''">
            and t.`task_no` =#{taskNo}
        </if>
        <if test="taskType !=null and taskType != ''">
            and t.`task_type` =#{taskType}
        </if>
        <if test="orderNo !=null and orderNo != ''">
            and t.`order_no` =#{orderNo}
        </if>
        <if test="parentOrderNo !=null and parentOrderNo != ''">
            and t.`parent_order_no` = #{parentOrderNo}
        </if>
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            <choose>
                <when test="isFuzzy !=null and isFuzzy != ''">
                    and t.customer_order_no like concat('',#{customerOrderNo},'%')
                </when>
                <otherwise>
                    and t.customer_order_no = #{customerOrderNo}
                </otherwise>
            </choose>
        </if>
        <if test="relationOrderNo !=null and relationOrderNo != ''">
            and t.`relation_order_no` = #{relationOrderNo}
        </if>
        <if test="waybillNo !=null and waybillNo != ''">
            and t.`waybill_no` =#{waybillNo}
        </if>
        <if test="orderType !=null and orderType != ''">
            and t.`order_type` =#{orderType}
        </if>
        <if test="excuteStatus !=null">
            and t.`excute_status` =#{excuteStatus}
        </if>
        <if test="orderStatus !=null">
            and t.`order_status` =#{orderStatus}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and t.`customer_code` =#{customerCode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and t.`site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and t.`wh_code` =#{whCode}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and t.`source_system` = #{sourceSystem}
        </if>
        <if test="specCarrierFlag !=null and specCarrierFlag != ''">
            and t.`spec_carrier_flag` =#{specCarrierFlag}
        </if>
        <if test="businessMode !=null and businessMode != ''">
            and t.`business_mode` =#{businessMode}
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and t.`delivery_type` =#{deliveryType}
        </if>
        <if test="logisticMode !=null and logisticMode != ''">
            and t.`logistic_mode` =#{logisticMode}
        </if>
        <if test="serviceOrderNo !=null and serviceOrderNo != ''">
            and t.`service_order_no` =#{serviceOrderNo}
        </if>
        <if test="networkName !=null and networkName != ''">
            and a.`network_name` =#{networkName}
        </if>
        <if test="distributionFlag !=null">
            and t.`distribution_flag` =#{distributionFlag}
        </if>
        <if test="dispatchNo !=null and dispatchNo != ''">
            and t.`dispatch_no` =#{dispatchNo}
        </if>
        <if test="thirdFlag !=null and thirdFlag != ''">
            and t.`third_flag` =#{thirdFlag}
        </if>
        <if test="serviceType !=null and serviceType != ''">
            and t.`service_type` =#{serviceType}
        </if>
        <if test="emergenceFlag !=null and emergenceFlag != ''">
            and t.`emergence_flag` =#{emergenceFlag}
        </if>
        <if test="taskStatus !=null">
            <![CDATA[
            and  t.`task_status`  <= #{taskStatus}
            ]]>
        </if>
        <if test="customerGroup !=null and customerGroup != ''">
            and t.`customer_group` =#{customerGroup}
        </if>
        <!-- 任务中心查询数据条数的时候，当选了Hold单类型时，返回数据量错误，在查询总记录数时添加条件，永培：此字段可能还要加索引 -->
        <if test="holdFlag !=null">
            and t.`hold_flag` =#{holdFlag}
        </if>

        <if test="orderConfirmTimeStartTime!=null and orderConfirmTimeStartTime != ''">
            <![CDATA[
               and t.order_confirm_time >=  DATE_FORMAT(#{orderConfirmTimeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderConfirmTimeEndTime!=null and orderConfirmTimeEndTime != ''">
            <![CDATA[
              and t.order_confirm_time <=  DATE_FORMAT(#{orderConfirmTimeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="createStartTime!=null and createStartTime != ''">
            <![CDATA[
               and t.create_time >=  DATE_FORMAT(#{createStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="createEndTime!=null and createEndTime !=''">
            <![CDATA[
              and t.create_time <=  DATE_FORMAT(#{createEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="orderStartBTime!=null and orderStartBTime != ''">
            <![CDATA[
               and t.order_start_time >=  DATE_FORMAT(#{orderStartBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderStartETime!=null and orderStartETime !=''">
            <![CDATA[
              and t.order_start_time <=  DATE_FORMAT(#{orderStartETime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="orderEndBTime!=null and orderEndBTime != ''">
            <![CDATA[
               and t.order_end_time >=  DATE_FORMAT(#{orderEndBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderEndETime!=null and orderEndETime !=''">
            <![CDATA[
              and t.order_end_time <=  DATE_FORMAT(#{orderEndETime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="agingStartBTime!=null and agingStartBTime != ''">
            <![CDATA[
               and t.aging_start_time >=  DATE_FORMAT(#{agingStartBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="agingStartETime!=null and agingStartETime !=''">
            <![CDATA[
              and t.aging_start_time <=  DATE_FORMAT(#{agingStartETime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="expectArriveStartTimeFrom!=null and expectArriveStartTimeFrom != ''">
            <![CDATA[
               and t.expect_arrive_start_time >=  DATE_FORMAT(#{expectArriveStartTimeFrom}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveStartTimeTo!=null and expectArriveStartTimeTo !=''">
            <![CDATA[
              and t.expect_arrive_start_time <=  DATE_FORMAT(#{expectArriveStartTimeTo}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="expectArriveEndTimeFrom!=null and expectArriveEndTimeFrom != ''">
            <![CDATA[
               and t.expect_arrive_end_time >=  DATE_FORMAT(#{expectArriveEndTimeFrom}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveEndTimeTo!=null and expectArriveEndTimeTo !=''">
            <![CDATA[
              and t.expect_arrive_end_time <=  DATE_FORMAT(#{expectArriveEndTimeTo}, '%Y-%m-%d %T')
            ]]>
        </if>


        <if test="consigneeStartTime!=null and consigneeStartTime != ''">
            <![CDATA[
               and t.consignee_time_from >=  DATE_FORMAT(#{consigneeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="consigneeEndTime !=null and consigneeEndTime !=''">
            <![CDATA[
              and t.consignee_time_to <=  DATE_FORMAT(#{consigneeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="requireOutStartTime!=null and requireOutStartTime != ''">
            <![CDATA[
               and o.plan_time >=  DATE_FORMAT(#{requireOutStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="requireOutEndTime !=null and requireOutEndTime !=''">
            <![CDATA[
              and o.plan_time <=  DATE_FORMAT(#{requireOutEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="orderStatusList != null and orderStatusList.size() >0">
            and t.`order_status` in
            <foreach close=")" collection="orderStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="orderTypeList != null and orderTypeList.size() >0">
            and t.`order_type` in
            <foreach close=")" collection="orderTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="taskStatusList != null and taskStatusList.size() >0">
            and t.`task_status` in
            <foreach close=")" collection="taskStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="taskTypeList != null and taskTypeList.size() >0">
            and t.`task_type` in
            <foreach close=")" collection="taskTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="preAppointWayList != null and preAppointWayList.size() >0">
            and p.`pre_appoint_way` in
            <foreach close=")" collection="preAppointWayList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="preAppointStatusList != null and preAppointStatusList.size() >0">
            and p.`pre_appoint_status` in
            <foreach close=")" collection="preAppointStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="excuteStatusList != null and excuteStatusList.size() >0">
            and t.`excute_status` in
            <foreach close=")" collection="excuteStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="businessTypeList != null and businessTypeList.size() >0">
            and t.`business_type` in
            <foreach close=")" collection="businessTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="deliveryTypeList != null and deliveryTypeList.size() >0">
            and t.`delivery_type` in
            <foreach close=")" collection="deliveryTypeList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="entityIdList != null and entityIdList.size() >0">
            and t.`entity_id` in
            <foreach close=")" collection="entityIdList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="projectClassify !=null and projectClassify != ''">
            and t.`project_classify` =#{projectClassify}
        </if>
        <if test="scPosFlag !=null">
            and t.`sc_pos_flag` =#{scPosFlag}
        </if>
        <if test="specimenType !=null and specimenType != ''">
            and t.`specimen_type` =#{specimenType}
        </if>
        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>
        <if test="contractNo !=null and contractNo != ''">
            and t.`contract_no` =#{contractNo}
        </if>
        <if test="originOrderNo !=null and originOrderNo != ''">
            and t.`origin_order_no` =#{originOrderNo}
        </if>
        <if test="inOutType !=null and inOutType != ''">
            and t.`in_out_type` =#{inOutType}
        </if>
        <if test="exceptionType !=null and exceptionType != ''">
            and p.`exception_type` =#{exceptionType}
        </if>
        <if test="isPreciseDelivery != null and isPreciseDelivery==1">
            and t.`upper_aging_code` = #{upperAgingCode}
        </if>
        <if test="isPreciseDelivery != null and isPreciseDelivery==0">
            and (t.`upper_aging_code` != #{upperAgingCode} OR t.`upper_aging_code` is null or t.`upper_aging_code` ='')
        </if>
        <if test="batchSiteCode !=null and batchSiteCode.size()>0">
            and t.site_code in
            <foreach collection="batchSiteCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="batchWhCode !=null and batchWhCode.size()>0">
            and t.wh_code in
            <foreach collection="batchWhCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="batchCustomerCode !=null and batchCustomerCode.size()>0">
            and t.customer_code in
            <foreach collection="batchCustomerCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="companyCode !=null and companyCode != ''">
            and t.`company_code` =#{companyCode}
        </if>
        <if test="sourceBuyerTenantType !=null">
            and e.`source_buyer_tenant_type` = #{sourceBuyerTenantType}
        </if>
        <if test="dataAuthFlag == 1">
            and (@{"columnCode":"site_code","tableName":"es_company","custom":"t.site_code","dataCode":"D20190621009"}@)
        </if>
        <if test="customerAuthFlag!=null and customerAuthFlag == 1">
            and (@{"columnCode":"pm_code","tableName":"eb_customer","custom":"t.customer_code","dataCode":"D20240118001"}@)
        </if>
        <if test="targeSiteCode !=null and targeSiteCode != ''">
            and t.`targe_site_code` =#{targeSiteCode}
        </if>
    </sql>


    <sql id="whereFieldsSqlTask">

        <if test="batchQueryKey !=null and batchQueryKey != ''  and batchQueryValue !=null and batchQueryValue.size()>0 ">
            <choose>
                <when test="batchQueryKey=='customerOrderNo'">
                    and t.customer_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='taskNo'">
                    and t.task_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='waybillNo'">
                    and t.waybill_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='parentOrderNo'">
                    and t.parent_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='originOrderNo'">
                    and t.origin_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='relationOrderNo'">
                    and t.relation_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='dispatchNo'">
                    and t.dispatch_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <when test="batchQueryKey=='platformOrderNo'">
                    and t.platform_order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>

                <otherwise>
                    and t.order_no in
                    <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>

        <if test="arriveAppointmentFlag !=null and arriveAppointmentFlag == 0">
            and t.`appointment_time` is null
        </if>
        <if test="arriveAppointmentFlag !=null and arriveAppointmentFlag == 1">
            and t.`appointment_time` is not null
        </if>
        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>
        <if test="transferFlag !=null and transferFlag != ''">
            and t.`transfer_flag` =#{transferFlag}
        </if>
        <if test="transportSystem !=null and transportSystem != ''">
            and t.`transport_system` =#{transportSystem}
        </if>
        <if test="transportOrderNo !=null and transportOrderNo != ''">
            and t.`transport_order_no` =#{transportOrderNo}
        </if>
        <if test="upperSenderCode !=null and upperSenderCode != ''">
            and t.upper_sender_code =#{upperSenderCode}
        </if>
        <if test="upperReceiverCode !=null and upperReceiverCode != ''">
            and t.upper_receiver_code =#{upperReceiverCode}
        </if>
        <if test="upperSenderName !=null and upperSenderName != ''">
            and t.upper_sender_name like concat('',#{upperSenderName},'%')
        </if>
        <if test="upperReceiverName !=null and upperReceiverName != ''">
            and t.upper_receiver_name like concat('',#{upperReceiverName},'%')
        </if>
        <if test="orderWhCode !=null and orderWhCode != ''">
            and t.`order_wh_code` =#{orderWhCode}
        </if>
        <if test="agingProductCode !=null and agingProductCode != ''">
            and t.`aging_product_code` =#{agingProductCode}
        </if>
        <if test="joinType !=null and joinType != ''">
            and t.`join_type` =#{joinType}
        </if>
        <if test="taskNo !=null and taskNo != ''">
            and t.`task_no` =#{taskNo}
        </if>
        <if test="taskType !=null and taskType != ''">
            and t.`task_type` =#{taskType}
        </if>
        <if test="orderNo !=null and orderNo != ''">
            and t.`order_no` =#{orderNo}
        </if>
        <if test="parentOrderNo !=null and parentOrderNo != ''">
            and t.`parent_order_no` = #{parentOrderNo}
        </if>
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            <choose>
                <when test="isFuzzy !=null and isFuzzy != ''">
                    and t.customer_order_no like concat('',#{customerOrderNo},'%')
                </when>
                <otherwise>
                    and t.customer_order_no = #{customerOrderNo}
                </otherwise>
            </choose>
        </if>
        <if test="relationOrderNo !=null and relationOrderNo != ''">
            and t.`relation_order_no` = #{relationOrderNo}
        </if>
        <if test="waybillNo !=null and waybillNo != ''">
            and t.`waybill_no` =#{waybillNo}
        </if>
        <if test="orderType !=null and orderType != ''">
            and t.`order_type` =#{orderType}
        </if>
        <if test="excuteStatus !=null">
            and t.`excute_status` =#{excuteStatus}
        </if>
        <if test="orderStatus !=null">
            and t.`order_status` =#{orderStatus}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and t.`customer_code` =#{customerCode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and t.`site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and t.`wh_code` =#{whCode}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and t.`source_system` = #{sourceSystem}
        </if>
        <if test="specCarrierFlag !=null and specCarrierFlag != ''">
            and t.`spec_carrier_flag` =#{specCarrierFlag}
        </if>
        <if test="businessMode !=null and businessMode != ''">
            and t.`business_mode` =#{businessMode}
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and t.`delivery_type` =#{deliveryType}
        </if>
        <if test="logisticMode !=null and logisticMode != ''">
            and t.`logistic_mode` =#{logisticMode}
        </if>
        <if test="serviceOrderNo !=null and serviceOrderNo != ''">
            and t.`service_order_no` =#{serviceOrderNo}
        </if>
        <if test="distributionFlag !=null">
            and t.`distribution_flag` =#{distributionFlag}
        </if>
        <if test="dispatchNo !=null and dispatchNo != ''">
            and t.`dispatch_no` =#{dispatchNo}
        </if>
        <if test="thirdFlag !=null and thirdFlag != ''">
            and t.`third_flag` =#{thirdFlag}
        </if>
        <if test="serviceType !=null and serviceType != ''">
            and t.`service_type` =#{serviceType}
        </if>
        <if test="emergenceFlag !=null and emergenceFlag != ''">
            and t.`emergence_flag` =#{emergenceFlag}
        </if>
        <if test="taskStatus !=null">
            <![CDATA[
            and  t.`task_status`  <= #{taskStatus}
            ]]>
        </if>
        <if test="customerGroup !=null and customerGroup != ''">
            and t.`customer_group` =#{customerGroup}
        </if>
        <if test="orderConfirmTimeStartTime!=null and orderConfirmTimeStartTime != ''">
            <![CDATA[
               and t.order_confirm_time >=  DATE_FORMAT(#{orderConfirmTimeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderConfirmTimeEndTime!=null and orderConfirmTimeEndTime != ''">
            <![CDATA[
              and t.order_confirm_time <=  DATE_FORMAT(#{orderConfirmTimeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="createStartTime!=null and createStartTime != ''">
            <![CDATA[
               and t.create_time >=  DATE_FORMAT(#{createStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="createEndTime!=null and createEndTime !=''">
            <![CDATA[
              and t.create_time <=  DATE_FORMAT(#{createEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderStartBTime!=null and orderStartBTime != ''">
            <![CDATA[
               and t.order_start_time >=  DATE_FORMAT(#{orderStartBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderStartETime!=null and orderStartETime !=''">
            <![CDATA[
              and t.order_start_time <=  DATE_FORMAT(#{orderStartETime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderEndBTime!=null and orderEndBTime != ''">
            <![CDATA[
               and t.order_end_time >=  DATE_FORMAT(#{orderEndBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderEndETime!=null and orderEndETime !=''">
            <![CDATA[
              and t.order_end_time <=  DATE_FORMAT(#{orderEndETime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="agingStartBTime!=null and agingStartBTime != ''">
            <![CDATA[
               and t.aging_start_time >=  DATE_FORMAT(#{agingStartBTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="agingStartETime!=null and agingStartETime !=''">
            <![CDATA[
              and t.aging_start_time <=  DATE_FORMAT(#{agingStartETime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveStartTimeFrom!=null and expectArriveStartTimeFrom != ''">
            <![CDATA[
               and t.expect_arrive_start_time >=  DATE_FORMAT(#{expectArriveStartTimeFrom}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveStartTimeTo!=null and expectArriveStartTimeTo !=''">
            <![CDATA[
              and t.expect_arrive_start_time <=  DATE_FORMAT(#{expectArriveStartTimeTo}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveEndTimeFrom!=null and expectArriveEndTimeFrom != ''">
            <![CDATA[
               and t.expect_arrive_end_time >=  DATE_FORMAT(#{expectArriveEndTimeFrom}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="expectArriveEndTimeTo!=null and expectArriveEndTimeTo !=''">
            <![CDATA[
              and t.expect_arrive_end_time <=  DATE_FORMAT(#{expectArriveEndTimeTo}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="consigneeStartTime!=null and consigneeStartTime != ''">
            <![CDATA[
               and t.consignee_time_from >=  DATE_FORMAT(#{consigneeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="consigneeEndTime !=null and consigneeEndTime !=''">
            <![CDATA[
              and t.consignee_time_to <=  DATE_FORMAT(#{consigneeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderStatusList != null and orderStatusList.size() >0">
            and t.`order_status` in
            <foreach close=")" collection="orderStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="orderTypeList != null and orderTypeList.size() >0">
            and t.`order_type` in
            <foreach close=")" collection="orderTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="taskStatusList != null and taskStatusList.size() >0">
            and t.`task_status` in
            <foreach close=")" collection="taskStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="taskTypeList != null and taskTypeList.size() >0">
            and t.`task_type` in
            <foreach close=")" collection="taskTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="excuteStatusList != null and excuteStatusList.size() >0">
            and t.`excute_status` in
            <foreach close=")" collection="excuteStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="businessTypeList != null and businessTypeList.size() >0">
            and t.`business_type` in
            <foreach close=")" collection="businessTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="deliveryTypeList != null and deliveryTypeList.size() >0">
            and t.`delivery_type` in
            <foreach close=")" collection="deliveryTypeList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="entityIdList != null and entityIdList.size() >0">
            and t.`entity_id` in
            <foreach close=")" collection="entityIdList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectClassify !=null and projectClassify != ''">
            and t.`project_classify` =#{projectClassify}
        </if>
        <if test="scPosFlag !=null">
            and t.`sc_pos_flag` =#{scPosFlag}
        </if>
        <if test="specimenType !=null and specimenType != ''">
            and t.`specimen_type` =#{specimenType}
        </if>
        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>
        <if test="contractNo !=null and contractNo != ''">
            and t.`contract_no` =#{contractNo}
        </if>
        <if test="originOrderNo !=null and originOrderNo != ''">
            and t.`origin_order_no` =#{originOrderNo}
        </if>
        <if test="inOutType !=null and inOutType != ''">
            and t.`in_out_type` =#{inOutType}
        </if>

        <if test="holdFlag !=null">
            and t.`hold_flag` =#{holdFlag}
        </if>


        <if test="isPreciseDelivery != null and isPreciseDelivery==1">
            and t.`upper_aging_code` = #{upperAgingCode}
        </if>
        <if test="isPreciseDelivery != null and isPreciseDelivery==0">
            and (t.`upper_aging_code` != #{upperAgingCode} OR t.`upper_aging_code` is null or t.`upper_aging_code` ='')
        </if>
        <if test="dataAuthFlag == 1">
            and (@{"columnCode":"site_code","tableName":"es_company","custom":"t.site_code","dataCode":"D20190621009"}@)
        </if>
        <if test="customerAuthFlag!=null and customerAuthFlag == 1">
            and (@{"columnCode":"pm_code","tableName":"eb_customer","custom":"t.customer_code","dataCode":"D20240118001"}@)
        </if>
        <if test="consolidationOrderNo !=null and consolidationOrderNo != ''">
            and t.`consolidation_order_no` = #{consolidationOrderNo}
        </if>
        <if test="entityId !=null">
            and t.entity_id = #{entityId}
        </if>
        <if test="batchSiteCode !=null and batchSiteCode.size()>0">
            and t.site_code in
            <foreach collection="batchSiteCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="batchWhCode !=null and batchWhCode.size()>0">
            and t.wh_code in
            <foreach collection="batchWhCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="batchCustomerCode !=null and batchCustomerCode.size()>0">
            and t.customer_code in
            <foreach collection="batchCustomerCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="targeSiteCode !=null and targeSiteCode != ''">
            and t.`targe_site_code` =#{targeSiteCode}
        </if>
        <if test="companyCode !=null and companyCode != ''">
            and t.`company_code` =#{companyCode}
        </if>
    </sql>


    <sql id="whereFieldsSqlOther">
        <if test="reDistributeList != null and reDistributeList.size() >0">
            and e.`re_distribute` in
            <foreach close=")" collection="reDistributeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="anntoCarrierCodeList != null and anntoCarrierCodeList.size() >0">
            and te.`carrier_code` in
            <foreach close=")" collection="anntoCarrierCodeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="preAppointStatus !=null and preAppointStatus != ''">
            and p.pre_appoint_status =#{preAppointStatus}
        </if>
        <if test="senderProvinceCode !=null and senderProvinceCode != ''">
            and a.`sender_province_code` =#{senderProvinceCode}
        </if>
        <if test="senderCityCode !=null and senderCityCode != ''">
            and a.`sender_city_code` =#{senderCityCode}
        </if>
        <if test="senderDistrictCode !=null and senderDistrictCode != ''">
            and a.`sender_district_code` =#{senderDistrictCode}
        </if>
        <if test="senderTownCode !=null and senderTownCode != ''">
            and a.`sender_town_code` =#{senderTownCode}
        </if>
        <if test="senderDetailAddr !=null and senderDetailAddr != ''">
            and a.`sender_detail_addr` like CONCAT('',#{senderDetailAddr},'%')
        </if>
        <if test="finalMobile !=null and finalMobile != ''">
            and a.`final_mobile` =#{finalMobile}
        </if>

        <if test="finalProvinceCode !=null and finalProvinceCode != ''">
            and a.final_province_code =#{finalProvinceCode}
        </if>
        <if test="finalCityCode !=null and finalCityCode != ''">
            and a.final_city_code =#{finalCityCode}
        </if>
        <if test="finalDistrictCode !=null and finalDistrictCode != ''">
            and a.final_district_code =#{finalDistrictCode}
        </if>
        <if test="finalTownCode !=null and finalTownCode != ''">
            and a.final_town_code =#{finalTownCode}
        </if>
        <if test="finalDetailAddr !=null and finalDetailAddr != ''">
            and a.final_detail_addr like CONCAT('',#{finalDetailAddr},'%')
        </if>

        <if test="finalProvinceName !=null and finalProvinceName != ''">
            and a.`final_province_name` =#{finalProvinceName}
        </if>
        <if test="finalCityName !=null and finalCityName != ''">
            and a.`final_city_name` =#{finalCityName}
        </if>
        <if test="finalDistrictName !=null and finalDistrictName != ''">
            and a.`final_district_name` =#{finalDistrictName}
        </if>
        <if test="finalTownName !=null and finalTownName != ''">
            and a.`final_town_name` =#{finalTownName}
        </if>
        <if test="receiverMobile !=null and receiverMobile != ''">
            and a.`receiver_mobile` =#{receiverMobile}
        </if>
        <if test="senderMobile !=null and senderMobile != ''">
            and a.`sender_mobile` =#{senderMobile}
        </if>
        <if test="networkCode !=null and networkCode != ''">
            and a.`network_code` =#{networkCode}
        </if>
        <if test="networkName !=null and networkName != ''">
            and a.`network_name` =#{networkName}
        </if>
        <if test="requireOutStartTime!=null and requireOutStartTime != ''">
            <![CDATA[
               and o.plan_time >=  DATE_FORMAT(#{requireOutStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="requireOutEndTime !=null and requireOutEndTime !=''">
            <![CDATA[
              and o.plan_time <=  DATE_FORMAT(#{requireOutEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="preAppointWayList != null and preAppointWayList.size() >0">
            and p.`pre_appoint_way` in
            <foreach close=")" collection="preAppointWayList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="preAppointStatusList != null and preAppointStatusList.size() >0">
            and p.`pre_appoint_status` in
            <foreach close=")" collection="preAppointStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="exceptionType !=null and exceptionType != ''">
            and p.`exception_type` =#{exceptionType}
        </if>
        <if test="rollbackFlag !=null and rollbackFlag != ''">
            and a.`rollback_flag` =#{rollbackFlag}
        </if>
        <if test="sourceBuyerTenantType !=null">
            and e.`source_buyer_tenant_type` = #{sourceBuyerTenantType}
        </if>
    </sql>

    <update id="synFinancialNo">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_item
            set
            `finance_no` = #{item.financeNo}
            where
            `task_no` = #{item.taskNo}
            and `customer_item_code` = #{item.customerItemCode}
            and `upper_line_no` = #{item.upperLineNo}
        </foreach>
    </update>

    <sql id="setBatchFieldsSqlByTask">
        <set>
            `version` = `version` + 1 ,
            <if test="item.zoneCode !=null and item.zoneCode != ''">
                `zone_code` = #{item.zoneCode},
            </if>
            <if test="item.zoneName !=null and item.zoneName != ''">
                `zone_name` = #{item.zoneName},
            </if>
            <if test="item.orderType !=null and item.orderType != ''">
                `order_type` = #{item.orderType},
            </if>
            <if test="item.consigneeTimeFrom != null">
                `consignee_time_from` = #{item.consigneeTimeFrom},
            </if>
            <if test="item.companyName !=null and item.companyName != ''">
                `company_name` = #{item.companyName},
            </if>
            <if test="item.totalGrossWeight != null">
                `total_gross_weight` = #{item.totalGrossWeight},
            </if>
            <if test="item.whName !=null and item.whName != ''">
                `wh_name` = #{item.whName},
            </if>
            <if test="item.upperCustomerCode !=null and item.upperCustomerCode != ''">
                `upper_customer_code` = #{item.upperCustomerCode},
            </if>
            <if test="item.targeSiteCode !=null and item.targeSiteCode != ''">
                `targe_site_code` = #{item.targeSiteCode},
            </if>
            <if test="item.targetCustomerCode !=null and item.targetCustomerCode != ''">
                `target_customer_code` = #{item.targetCustomerCode},
            </if>
            <if test="item.taskStatus != null">
                `task_status` = #{item.taskStatus},
            </if>
            <if test="item.agingProductCode !=null and item.agingProductCode != ''">
                `aging_product_code` = #{item.agingProductCode},
            </if>
            <if test="item.nextDistributionWhCode !=null and item.nextDistributionWhCode != ''">
                `next_distribution_wh_code` = #{item.nextDistributionWhCode},
            </if>
            <if test="item.orderConfirmTime != null">
                `order_confirm_time` = #{item.orderConfirmTime},
            </if>
            <if test="item.relationOrderNo !=null and item.relationOrderNo != ''">
                `relation_order_no` = #{item.relationOrderNo},
            </if>
            <if test="item.invoiceUnitName !=null and item.invoiceUnitName != ''">
                `invoice_unit_name` = #{item.invoiceUnitName},
            </if>
            <if test="item.expectArriveStartTime != null">
                `expect_arrive_start_time` = #{item.expectArriveStartTime},
            </if>
            <if test="item.targeWhCode !=null and item.targeWhCode != ''">
                `targe_wh_code` = #{item.targeWhCode},
            </if>
            <if test="item.customerName !=null and item.customerName != ''">
                `customer_name` = #{item.customerName},
            </if>
            <if test="item.excuteStatus != null">
                `excute_status` = #{item.excuteStatus},
            </if>
            <if test="item.upperReceiverCode !=null and item.upperReceiverCode != ''">
                `upper_receiver_code` = #{item.upperReceiverCode},
            </if>
            <if test="item.upperReferenceId !=null and item.upperReferenceId != ''">
                `upper_reference_id` = #{item.upperReferenceId},
            </if>
            <if test="item.freightBasis !=null and item.freightBasis != ''">
                `freight_basis` = #{item.freightBasis},
            </if>
            <if test="item.carrierCode !=null and item.carrierCode != ''">
                `carrier_code` = #{item.carrierCode},
            </if>
            <if test="item.totalNetWeight != null">
                `total_net_weight` = #{item.totalNetWeight},
            </if>
            <if test="item.distributionWhName !=null and item.distributionWhName != ''">
                `distribution_wh_name` = #{item.distributionWhName},
            </if>
            <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                `source_system` = #{item.sourceSystem},
            </if>
            <if test="item.distributionFlag != null">
                `distribution_flag` = #{item.distributionFlag},
            </if>
            <if test="item.contractNo !=null and item.contractNo != ''">
                `contract_no` = #{item.contractNo},
            </if>
            <if test="item.customerCode !=null and item.customerCode != ''">
                `customer_code` = #{item.customerCode},
            </if>
            <if test="item.siteName !=null and item.siteName != ''">
                `site_name` = #{item.siteName},
            </if>
            <if test="item.supplierCode !=null and item.supplierCode != ''">
                `supplier_code` = #{item.supplierCode},
            </if>
            <if test="item.orderValue !=null and item.orderValue != ''">
                `order_value` = #{item.orderValue},
            </if>
            <if test="item.specCarrierFlag !=null and item.specCarrierFlag != ''">
                `spec_carrier_flag` = #{item.specCarrierFlag},
            </if>
            <if test="item.upperOrderType !=null and item.upperOrderType != ''">
                `upper_order_type` = #{item.upperOrderType},
            </if>
            <if test="item.returnReason !=null and item.returnReason != ''">
                `return_reason` = #{item.returnReason},
            </if>
            <if test="item.projectClassify !=null">
                `project_classify` = #{item.projectClassify},
            </if>
            <if test="item.cancelReason !=null and item.cancelReason != ''">
                `cancel_reason` = #{item.cancelReason},
            </if>
            <if test="item.specimenType !=null and item.specimenType != ''">
                `specimen_type` = #{item.specimenType},
            </if>
            <if test="item.businessMode !=null and item.businessMode != ''">
                `business_mode` = #{item.businessMode},
            </if>
            <if test="item.dispatchNo !=null and item.dispatchNo != ''">
                `dispatch_no` = #{item.dispatchNo},
            </if>
            <if test="item.collectionAmount != null">
                `collection_amount` = #{item.collectionAmount},
            </if>
            <if test="item.thirdFlag !=null and item.thirdFlag != ''">
                `third_flag` = #{item.thirdFlag},
            </if>
            <if test="item.expectArriveEndTime != null">
                `expect_arrive_end_time` = #{item.expectArriveEndTime},
            </if>
            <if test="item.upperWhName !=null and item.upperWhName != ''">
                `upper_wh_name` = #{item.upperWhName},
            </if>
            <if test="item.deliveryCycle !=null and item.deliveryCycle != ''">
                `delivery_cycle` = #{item.deliveryCycle},
            </if>
            <if test="item.distributionLastFlag != null">
                `distribution_last_flag` = #{item.distributionLastFlag},
            </if>
            <if test="item.customerOrderNo !=null and item.customerOrderNo != ''">
                `customer_order_no` = #{item.customerOrderNo},
            </if>
            <if test="item.totalVolume != null">
                `total_volume` = #{item.totalVolume},
            </if>
            <if test="item.serviceType !=null and item.serviceType != ''">
                `service_type` = #{item.serviceType},
            </if>
            <if test="item.totalQty != null">
                `total_qty` = #{item.totalQty},
            </if>
            <if test="item.payTime != null">
                `pay_time` = #{item.payTime},
            </if>
            <if test="item.distributionNum != null">
                `distribution_num` = #{item.distributionNum},
            </if>
            <if test="item.parentOrderNo !=null and item.parentOrderNo != ''">
                `parent_order_no` = #{item.parentOrderNo},
            </if>
            <if test="item.distributionSiteCode !=null and item.distributionSiteCode != ''">
                `distribution_site_code` = #{item.distributionSiteCode},
            </if>
            <if test="item.buyerRemark !=null and item.buyerRemark != ''">
                `buyer_remark` = #{item.buyerRemark},
            </if>
            <if test="item.driverQueueCode !=null and item.driverQueueCode != ''">
                `driver_queue_code` = #{item.driverQueueCode},
            </if>
            <if test="item.invoiceUnitCode !=null and item.invoiceUnitCode != ''">
                `invoice_unit_code` = #{item.invoiceUnitCode},
            </if>
            <if test="item.orderRpFlag !=null and item.orderRpFlag != ''">
                `order_rp_flag` = #{item.orderRpFlag},
            </if>
            <if test="item.shopId !=null and item.shopId != ''">
                `shop_id` = #{item.shopId},
            </if>
            <if test="item.mileage != null">
                `mileage` = #{item.mileage},
            </if>
            <if test="item.companyCode !=null and item.companyCode != ''">
                `company_code` = #{item.companyCode},
            </if>
            <if test="item.invoiceFlag !=null and item.invoiceFlag != ''">
                `invoice_flag` = #{item.invoiceFlag},
            </if>
            <if test="item.appointmentType !=null and item.appointmentType != ''">
                `appointment_type` = #{item.appointmentType},
            </if>
            <if test="item.orderNo !=null and item.orderNo != ''">
                `order_no` = #{item.orderNo},
            </if>
            <if test="item.customerGroup !=null and item.customerGroup != ''">
                `customer_group` = #{item.customerGroup},
            </if>
            <if test="item.collectionFlag !=null and item.collectionFlag != ''">
                `collection_flag` = #{item.collectionFlag},
            </if>
            <if test="item.orderSourcePlatform !=null and item.orderSourcePlatform != ''">
                `order_source_platform` = #{item.orderSourcePlatform},
            </if>
            <if test="item.outsourceFlag !=null and item.outsourceFlag != ''">
                `outsource_flag` = #{item.outsourceFlag},
            </if>
            <if test="item.serviceOrderNo !=null and item.serviceOrderNo != ''">
                `service_order_no` = #{item.serviceOrderNo},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.whCode !=null and item.whCode != ''">
                `wh_code` = #{item.whCode},
            </if>
            <if test="item.setupType !=null and item.setupType != ''">
                `setup_type` = #{item.setupType},
            </if>
            <if test="item.emergenceFlag !=null and item.emergenceFlag != ''">
                `emergence_flag` = #{item.emergenceFlag},
            </if>
            <if test="item.orderStatus != null">
                `order_status` = #{item.orderStatus},
            </if>
            <if test="item.shopName !=null and item.shopName != ''">
                `shop_name` = #{item.shopName},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.tenantCode !=null and item.tenantCode != ''">
                `tenant_code` = #{item.tenantCode},
            </if>
            <if test="item.pickFlag !=null and item.pickFlag != ''">
                `pick_flag` = #{item.pickFlag},
            </if>
            <if test="item.appointmentReason !=null and item.appointmentReason != ''">
                `appointment_reason` = #{item.appointmentReason},
            </if>
            <if test="item.taskType !=null and item.taskType != ''">
                `task_type` = #{item.taskType},
            </if>
            <if test="item.inOutType !=null and item.inOutType != ''">
                `in_out_type` = #{item.inOutType},
            </if>
            <if test="item.pledgeType !=null and item.pledgeType != ''">
                `pledge_type` = #{item.pledgeType},
            </if>
            <if test="item.logisticMode !=null and item.logisticMode != ''">
                `logistic_mode` = #{item.logisticMode},
            </if>
            <if test="item.scPosFlag != null">
                `sc_pos_flag` = #{item.scPosFlag},
            </if>
            <if test="item.upperSenderCode !=null and item.upperSenderCode != ''">
                `upper_sender_code` = #{item.upperSenderCode},
            </if>
            <if test="item.appointmentTime != null">
                `appointment_time` = #{item.appointmentTime},
            </if>
            <if test="item.whSystem !=null and item.whSystem != ''">
                `wh_system`  = #{item.whSystem},
            </if>
            <if test="item.distributionWhCode !=null and item.distributionWhCode != ''">
                `distribution_wh_code` = #{item.distributionWhCode},
            </if>
            <if test="item.transportType !=null and item.transportType != ''">
                `transport_type` = #{item.transportType},
            </if>
            <if test="item.withdrawalReason !=null and item.withdrawalReason != ''">
                `withdrawal_reason` = #{item.withdrawalReason},
            </if>
            <if test="item.upperSourceCustomerCode !=null and item.upperSourceCustomerCode != ''">
                `upper_source_customer_code` = #{item.upperSourceCustomerCode},
            </if>
            <if test="item.orderSource !=null and item.orderSource != ''">
                `order_source` = #{item.orderSource},
            </if>
            <if test="item.siteCode !=null and item.siteCode != ''">
                `site_code` = #{item.siteCode},
            </if>
            <if test="item.upperWhCode !=null and item.upperWhCode != ''">
                `upper_wh_code` = #{item.upperWhCode},
            </if>
            <if test="item.deliveryType !=null and item.deliveryType != ''">
                `delivery_type` = #{item.deliveryType},
            </if>
            <if test="item.entityId != null">
                `entity_id` = #{item.entityId},
            </if>
            <if test="item.distributionSiteName !=null and item.distributionSiteName != ''">
                `distribution_site_name` = #{item.distributionSiteName},
            </if>
            <if test="item.originOrderNo !=null and item.originOrderNo != ''">
                `origin_order_no` = #{item.originOrderNo},
            </if>
            <if test="item.upperCustomerName !=null and item.upperCustomerName != ''">
                `upper_customer_name` = #{item.upperCustomerName},
            </if>
            <if test="item.platformOrderNo !=null and item.platformOrderNo != ''">
                `platform_order_no` = #{item.platformOrderNo},
            </if>
            <if test="item.joinType !=null">
                `join_type` = #{item.joinType},
            </if>
            <if test="item.consigneeTimeTo != null">
                `consignee_time_to` = #{item.consigneeTimeTo},
            </if>
            <if test="item.businessType !=null and item.businessType != ''">
                `business_type` = #{item.businessType},
            </if>
            <if test="item.loadType !=null and item.loadType != ''">
                `load_type` = #{item.loadType},
            </if>
            <if test="item.nextDistributionSiteCode !=null and item.nextDistributionSiteCode != ''">
                `next_distribution_site_code` = #{item.nextDistributionSiteCode},
            </if>
            <if test="item.waybillNo !=null and item.waybillNo != ''">
                `waybill_no` = #{item.waybillNo},
            </if>
            <if test="item.transportSystem !=null and item.transportSystem != ''">
                `transport_system` = #{item.transportSystem},
            </if>
            <if test="item.transferFlag !=null and item.transferFlag != ''">
                `transfer_flag` = #{item.transferFlag},
            </if>
        </set>
    </sql>

    <update id="batchUpdateByTask">
        <foreach collection="list" item="item" separator=";">
            UPDATE task
            <include refid="setBatchFieldsSqlByTask"/>
            where
            task_no = #{item.taskNo}
            and delete_flag = 0
            <if test="item.orderStatus != null and item.orderStatus!=690 and item.finalSignStatusFlag != 1">
                <![CDATA[
                and (`order_status` <= #{item.orderStatus} or `order_status` in (255,310,300,330,375,410,935))
                 ]]>
            </if>
            <if test="item.orderStatus != null and item.orderStatus==250">
                and `order_status` != 375
            </if>
            <if test="item.orderStatus != null and item.orderStatus==690">
                and 1=1
            </if>

        </foreach>
    </update>

    <update id="batchUpdateTaskForNodeStatus">
        <foreach collection="list" item="item" separator=";">
            UPDATE task set
            <if test="item.excuteStatus !=null and item.excuteStatus != ''">
                <![CDATA[
                excute_status = CASE
                WHEN excute_status < #{item.excuteStatus} THEN #{item.excuteStatus}
                ELSE excute_status
                END,
                ]]>
            </if>
            <if test="item.orderStatus !=null">
                <![CDATA[
                order_status = CASE
                WHEN order_status < #{item.orderStatus} THEN #{item.orderStatus}
                ELSE order_status
                END,
                ]]>
            </if>
            <if test="item.targeSiteCode !=null and item.targeSiteCode != ''">
                targe_site_code=#{item.targeSiteCode},
            </if>
            <if test="item.whSystem !=null and item.whSystem != ''">
                wh_system= #{item.whSystem},
            </if>
            <if test="item.transportSystem !=null and item.transportSystem != ''">
                transport_system = #{item.transportSystem},
            </if>
            version = version+1
            where task_no = #{item.taskNo} and delete_flag = 0
        </foreach>
    </update>

    <select id="getTasksAndAddrByOrderNos"
            resultType="com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse">
        select
        t.*,a.*
        from task t
        inner join task_address a on a.task_no = t.task_no and a.delete_flag=0
        where t.`order_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
        and t.delete_flag = 0
    </select>

    <select id="taskNewList" resultType="String">
        select
        t.task_no as taskNo
        from task t
        where t.delete_flag = 0 and t.order_status =#{orderStatus}
        limit ${start},${pageSize}
    </select>

    <select id="queryByTaskLast" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task where id in (
        select max(id)
        from task where `distribution_last_flag`='1' and order_no= #{orderNo}
        )

    </select>

    <select id="queryByTaskLastNow" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select task_no as taskNo,distribution_flag as distributionFlag,distribution_last_flag as distributionLastFlag,logistic_mode as logisticMode,delivery_type as deliveryType,
        task_type as taskType , order_type as orderType, wh_code as whCode, customer_code as customerCode,site_code as siteCode,business_type as businessType,business_mode as businessMode,
        order_no as orderNo,parent_order_no as parentOrderNo,customer_order_no as customerOrderNo
        from task where id in (
        select max(id)
        from task where delete_flag = 0 and order_no= #{orderNo}
        )
    </select>


    <update id="synContractNo">
        UPDATE task
        set
        `contract_no`= #{contractNo}
        where
        task_no = #{taskNo}
    </update>

    <select id="searchTaskListByTaskNos"
            resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task t
        where t.`task_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
        and t.delete_flag = 0
    </select>

    <select id="searchTaskListByTaskNosCount"  resultType="Integer">
        select
        count(*)
        from task t
        <if test="batchQueryValue !=null and batchQueryValue.size()>0">
            <where>
                t.task_no in
                <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="dataAuthFlag !=null and dataAuthFlag == 1">
                    and (@{"columnCode":"site_code","tableName":"es_company","custom":"site_code","dataCode":"D20190621009"}@)
                </if>
                and t.delete_flag = 0
                and t.excute_status != 999
            </where>
        </if>
    </select>

    <select id="searchTaskListByTaskNosIndex" resultType="com.midea.logistics.otp.task.domain.bean.custom.TaskDetailResponse">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task t
        <if test="batchQueryValue !=null and batchQueryValue.size()>0">
            <where>
                t.task_no in
                <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="dataAuthFlag !=null and dataAuthFlag == 1">
                    and (@{"columnCode":"site_code","tableName":"es_company","custom":"site_code","dataCode":"D20190621009"}@)
                </if>
                and t.delete_flag = 0
                and t.excute_status != 999
            </where>
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <select id="searchTaskListDoRdo" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        SELECT
          <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        FROM
            task
        WHERE
            delete_flag = 0
            AND waybill_no = #{waybillNo}

        UNION ALL

        SELECT
            <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        FROM
            task
        WHERE
            delete_flag = 0
            AND task_no = #{waybillNo}
    </select>


    <sql id="whereFieldsSql3">
        where t.delete_flag=0
        <if test="agingProductCode !=null and agingProductCode != ''">
            and t.`aging_product_code` =#{agingProductCode}
        </if>
        <if test="joinType !=null and joinType != ''">
            and t.`join_type` =#{joinType}
        </if>
        <if test="taskNo !=null and taskNo != ''">
            and t.`task_no` =#{taskNo}
        </if>
        <if test="taskType !=null and taskType != ''">
            and t.`task_type` =#{taskType}
        </if>
        <if test="orderNo !=null and orderNo != ''">
            and t.`order_no` =#{orderNo}
        </if>
        <if test="parentOrderNo !=null and parentOrderNo != ''">
            and t.`parent_order_no` = #{parentOrderNo}
        </if>
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            and t.`customer_order_no` like concat('',#{customerOrderNo},'%')
        </if>
        <if test="relationOrderNo !=null and relationOrderNo != ''">
            and t.`relation_order_no` = #{relationOrderNo}
        </if>
        <if test="waybillNo !=null and waybillNo != ''">
            and t.`waybill_no` =#{waybillNo}
        </if>
        <if test="orderType !=null and orderType != ''">
            and t.`order_type` =#{orderType}
        </if>
        <if test="excuteStatus !=null">
            and t.`excute_status` =#{excuteStatus}
        </if>
        <if test="orderStatus !=null">
            and t.`order_status` =#{orderStatus}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and t.`customer_code` =#{customerCode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and t.`site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and t.`wh_code` =#{whCode}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and t.`source_system` = #{sourceSystem}
        </if>
        <if test="specCarrierFlag !=null and specCarrierFlag != ''">
            and t.`spec_carrier_flag` =#{specCarrierFlag}
        </if>
        <if test="businessMode !=null and businessMode != ''">
            and t.`business_mode` =#{businessMode}
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and t.`delivery_type` =#{deliveryType}
        </if>
        <if test="logisticMode !=null and logisticMode != ''">
            and t.`logistic_mode` =#{logisticMode}
        </if>
        <if test="serviceOrderNo !=null and serviceOrderNo != ''">
            and t.`service_order_no` =#{serviceOrderNo}
        </if>
        <if test="distributionFlag !=null">
            and t.`distribution_flag` =#{distributionFlag}
        </if>
        <if test="dispatchNo !=null and dispatchNo != ''">
            and t.`dispatch_no` =#{dispatchNo}
        </if>
        <if test="thirdFlag !=null and thirdFlag != ''">
            and t.`third_flag` =#{thirdFlag}
        </if>
        <if test="serviceType !=null and serviceType != ''">
            and t.`service_type` =#{serviceType}
        </if>
        <if test="emergenceFlag !=null and emergenceFlag != ''">
            and t.`emergence_flag` =#{emergenceFlag}
        </if>
        <if test="taskStatus !=null">
            <![CDATA[
            and  t.`task_status`  <= #{taskStatus}
            ]]>
        </if>
        <if test="customerGroup !=null and customerGroup != ''">
            and t.`customer_group` =#{customerGroup}
        </if>

        <if test="orderConfirmTimeStartTime!=null and orderConfirmTimeStartTime != ''">
            <![CDATA[
               and t.order_confirm_time >=  DATE_FORMAT(#{orderConfirmTimeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="orderConfirmTimeEndTime!=null and orderConfirmTimeEndTime != ''">
            <![CDATA[
              and t.order_confirm_time <=  DATE_FORMAT(#{orderConfirmTimeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="createStartTime!=null and createStartTime != ''">
            <![CDATA[
               and t.create_time >=  DATE_FORMAT(#{createStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="createEndTime!=null and createEndTime !=''">
            <![CDATA[
              and t.create_time <=  DATE_FORMAT(#{createEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="consigneeStartTime!=null and consigneeStartTime != ''">
            <![CDATA[
               and t.consignee_time_from >=  DATE_FORMAT(#{consigneeStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="consigneeEndTime !=null and consigneeEndTime !=''">
            <![CDATA[
              and t.consignee_time_to <=  DATE_FORMAT(#{consigneeEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="requireOutStartTime!=null and requireOutStartTime != ''">
            <![CDATA[
               and t.require_out_time >=  DATE_FORMAT(#{requireOutStartTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="requireOutEndTime !=null and requireOutEndTime !=''">
            <![CDATA[
              and t.require_out_time <=  DATE_FORMAT(#{requireOutEndTime}, '%Y-%m-%d %T')
            ]]>
        </if>

        <if test="orderStatusList != null and orderStatusList.size() >0">
            and t.`order_status` in
            <foreach close=")" collection="orderStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="orderTypeList != null and orderTypeList.size() >0">
            and t.`order_type` in
            <foreach close=")" collection="orderTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="taskStatusList != null and taskStatusList.size() >0">
            and t.`task_status` in
            <foreach close=")" collection="taskStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="taskTypeList != null and taskTypeList.size() >0">
            and t.`task_type` in
            <foreach close=")" collection="taskTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="excuteStatusList != null and excuteStatusList.size() >0">
            and t.`excute_status` in
            <foreach close=")" collection="excuteStatusList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="businessTypeList != null and businessTypeList.size() >0">
            and t.`business_type` in
            <foreach close=")" collection="businessTypeList" item="listItem" open="(" separator=",">
                #{listItem}
            </foreach>
        </if>
        <if test="deliveryTypeList != null and deliveryTypeList.size() >0">
            and t.`delivery_type` in
            <foreach close=")" collection="deliveryTypeList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectClassify !=null and projectClassify != ''">
            and t.`project_classify` =#{projectClassify}
        </if>
        <if test="scPosFlag !=null">
            and t.`sc_pos_flag` =#{scPosFlag}
        </if>
        <if test="specimenType !=null and specimenType != ''">
            and t.`specimen_type` =#{specimenType}
        </if>
        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>
        <if test="contractNo !=null and contractNo != ''">
            and t.`contract_no` =#{contractNo}
        </if>
        <if test="originOrderNo !=null and originOrderNo != ''">
            and t.`origin_order_no` =#{originOrderNo}
        </if>
        <if test="inOutType !=null and inOutType != ''">
            and t.`in_out_type` =#{inOutType}
        </if>

        <if test="dataAuthFlag == 1">
            and (@{"columnCode":"site_code","tableName":"es_company","custom":"t.site_code","dataCode":"D20190621009"}@)
        </if>
    </sql>

    <select id="selectAgingProductNameByCode" resultType="string">
        SELECT
        aging_product_name
        FROM
        aging_config
        WHERE
        delete_flag = 0
        AND aging_product_code = #{code}
        limit 1
    </select>

    <select id="selectOneByRDIorRDO" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task where order_no = #{orderNo} and (task_type ='RDI' or task_type ='RDO') order by create_time  asc limit 1
    </select>

    <select id="selectOneByRDIorRDOorYS" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task where order_no = #{orderNo} and (task_type ='RDI' or task_type ='RDO' or task_type ='YS') order by create_time  asc limit 1
    </select>

    <select id="selectOneByRDIorRDOorIT" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task where order_no = #{orderNo} and (task_type ='RDI' or task_type ='RDO' or task_type ='IN') order by create_time  asc limit 1
    </select>

    <select id="taskHoldList" resultType="String">
        SELECT
        task_no AS taskNo
        FROM
        task t
        LEFT JOIN order_extend o ON o.order_no = t.order_no
        WHERE t.delete_flag = 0
        AND t.create_time > DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND t.hold_flag = 1
        AND t.order_status &lt; 240
        AND o.order_end_time &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        limit ${start},${pageSize}
    </select>

    <select id="taskFlowControlList" resultType="String">
        SELECT
        task_no AS taskNo
        FROM
        task
        WHERE delete_flag = 0
        AND create_time >= #{beginDate}
        AND create_time &lt; #{endDate}
        AND hold_flag = 5
        AND order_status &lt; 240
        limit ${limit}
    </select>

    <select id="taskApptList" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        SELECT
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        FROM
        task
        WHERE delete_flag = 0
        AND create_time > DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND order_status &lt; 605
        AND upper_receiver_code =#{upperReceiverCode}
        AND wh_code = #{whCode}
    </select>



    <select id="listTaskByPhoneNo" resultType="com.midea.logistics.otp.bean.TaskList">
        select
        t.`id` AS  id,
        t.`task_no` AS  taskNo,
        t.customer_order_no AS  customerOrderNo,
        t.order_no AS  orderNo,
        t.`parent_order_no` AS parentOrderNo,
        t.`waybill_no` AS  waybillNo,
        t.dispatch_no AS dispatchNo,
        t.`customer_code` AS  customerCode,
        t.`customer_name` AS  customerName,
        t.`update_time` AS  updateTime,
        t.`create_time` AS  createTime,
        t.order_status AS  orderStatus,
        t.expect_arrive_start_time AS requireArriveTime,
        a.`end_lng` AS endLng,
        a.`end_lat` AS endLat,
        a.`start_lng` AS startLng,
        a.`start_lat` AS startLat,
        a.`receiver_name` AS  receiverName,
        a.`receiver_mobile` AS  receiverMobile,
        a.`receiver_city_name` AS  receiverCityName,
        a.`receiver_city_code` AS  receiverCityCode,
        a.`receiver_detail_addr` AS  receiverDetailAddr,
        a.`sender_name` AS  senderName,
        a.`sender_mobile` AS  senderMobile,
        a.`sender_city_name` AS  senderCityName,
        a.`sender_city_code` AS  senderCityCode,
        a.`sender_detail_addr` AS  senderDetailAddr,
        a.`final_mobile` AS  finalMobile,
        a.`final_tel` AS  finalTel,
        a.`final_name` AS  finalName,
        a.`final_detail_addr` AS  finalDetailAddr

        from task t
        left join task_address a on a.task_no = t.task_no and a.delete_flag=0
        where t.delete_flag=0
        <if test="phoneNo !=null and phoneNo != ''">
            and a.`final_mobile` =#{phoneNo}
        </if>
        <if test="senderMobile !=null and senderMobile != ''">
            and a.`sender_mobile` =#{senderMobile}
        </if>
        <if test="orderNo !=null and orderNo != ''">
            and t.`customer_order_no` =#{orderNo}
        </if>
        <if test="startTime!=null">
            <![CDATA[
               and t.create_time >=  DATE_FORMAT(#{startTime}, '%Y-%m-%d %T')
            ]]>
        </if>
        <if test="endTime!=null">
            <![CDATA[
              and t.create_time <=  DATE_FORMAT(#{endTime}, '%Y-%m-%d %T')
            ]]>
        </if>


        order by t.create_time desc

        limit ${start},${pageSize}
    </select>


    <select id="listItemBytaskNo" resultType="com.midea.logistics.otp.task.domain.bean.TaskItem">
        SELECT
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskItemMapper.searchFieldsSql"/>
        FROM
        task_item
        WHERE delete_flag = 0
         and `task_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </select>

    <select id="listAddrBytaskNo" resultType="com.midea.logistics.otp.task.domain.bean.TaskAddress">
        SELECT
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskAddressMapper.searchFieldsSql"/>
        FROM
        task_address
        WHERE delete_flag = 0
        and `task_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </select>

    <select id="tasksByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        *
        from task t
        where delete_flag = 0
        and task_no in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="listByTaskNosAndAuth" resultType="com.midea.logistics.otp.task.domain.bean.custom.TaskExt">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task t
        <where>
            t.delete_flag = 0
            <if test="batchQueryValue !=null and batchQueryValue.size()>0">
                and t.task_no in
                <foreach collection="batchQueryValue" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dataAuthFlag == 1 and siteCodeList!=null">
                and t.site_code in
                <foreach collection="siteCodeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listDeliveredTaskNo" resultType="java.lang.String">
        SELECT
        t.task_no taskNo
        FROM
        task t
        LEFT JOIN order_aging a ON a.order_no = t.order_no AND a.delete_flag = 0
        WHERE
        t.delete_flag = 0
        AND a.order_status = 350
        AND a.plan_time >= #{beginDate}
        AND a.plan_time &lt; #{endDate}

        AND t.hold_flag = 8

        AND t.task_status = 100


        limit ${limit}

    </select>

    <select id="getTasksByCustomerOrderNos" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.searchFieldsSql"/>
        from task
        where `customer_order_no` in
        <foreach close=")" collection="list" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
        and delete_flag = 0
    </select>

    <select id="tasksDeliveryTypeByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.custom.TaskDeliveryType">
        select
        t.`id` AS  id,
        t.`task_no` AS  taskNo ,
        t.`order_no` AS  orderNo,
        t.`business_mode` AS  businessMode,
        t.`transport_system` AS  transportSystem,
        t.`source_system` AS  sourceSystem,
        t.`delivery_type` AS  deliveryType,
        t.`parent_order_no` AS  parentOrderNo,
        t.`customer_order_no` AS  customerOrderNo,
        t.`customer_code` AS  customerCode,
        t.`task_type`  AS  taskType,
        t.`wh_system`  AS  whSystem,
        t.`distribution_last_flag`  AS  distributionLastFlag,
        t.`distribution_num`  AS  distributionNum,
        t.`wh_code`  AS  whCode
        from task t
        <where>
         t.task_no in
        <foreach collection="list" item="taskNos" index="index" open="(" close=")" separator=",">
            #{taskNos}
        </foreach>
        and t.delete_flag=0
        </where>
    </select>

</mapper>