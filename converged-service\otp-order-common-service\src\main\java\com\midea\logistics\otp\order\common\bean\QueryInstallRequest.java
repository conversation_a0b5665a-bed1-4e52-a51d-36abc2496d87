package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.util.List;

/**
 * ClassName: QueryInstallRequest
 * Description:查询是否需要安装请求类
 *
 * <AUTHOR>
 * @date 2023/9/5 17:32
 */
@Data
public class QueryInstallRequest {

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 第三方标识
     */
    private Integer thirdFlag;

    /**
     * 订单明细
     */
    private List<QueryInstallItemDto> orderItemDtos;

}
