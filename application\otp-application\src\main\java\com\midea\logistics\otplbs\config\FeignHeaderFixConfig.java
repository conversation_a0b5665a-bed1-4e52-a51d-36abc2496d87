package com.midea.logistics.otplbs.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignHeaderFixConfig implements BeanPostProcessor {

    private static final Logger logger = LoggerFactory.getLogger(FeignHeaderFixConfig.class);

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) {
        if ("mideaAuthFeignHeaderInterceptor".equalsIgnoreCase(beanName)) {
            logger.info("过滤有 bug 的 bean: {}", beanName);
            return null;
        }
        return bean;
    }

}
