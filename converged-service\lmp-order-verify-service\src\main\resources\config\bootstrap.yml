spring:
  # public setting
  application:
    name: logistics-lmp-order-verify-service
  profiles:
    active: uat
  main:
    allow-bean-definition-overriding: true
server:
  port: 10127
  tomcat:
    max-threads: 300
    max-connections: 30000

feign:
  hystrix:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 60_000
        readTimeout: 60_000
        loggerLevel: basic

breaker:
  enable: false
  auto:
    webmvc: false
    feign: false
  sentinel:
    enable: false

mgp:
  carrier:
    discovery:
      system: c-loms-annto
      version: 1.0
      auto-host-ip: true
      enable: true

logging:
  level:
    # 新增配置, 过滤掉该服务治理SDK版本过多日志输出问题
    com.midea.mgp: warn

management:
  health:
    elasticsearch:
      enabled: false

#dev
---
spring:
  profiles: dev
  logistics-log:
    path: ./logs
lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-dev.annto.com:15821/eureka/
  server:
    enable-self-preservation: false
mgp:
  carrier:
    discovery:
      env: dev

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 079f8a3c358344c09798e5a8eda37e0c

#sit
---
spring:
  profiles: sit
  logistics-log:
    path: ./logs
lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-sit.annto.com:15821/eureka/
  server:
    enable-self-preservation: false
mgp:
  carrier:
    discovery:
      env: sit
      registry: https://anapi-sit.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 58b2f65fdf744da4afd1ed816f9bc721

#uat
---
spring:
  profiles: uat
  logistics-log:
    path: ./logs
lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-uat.annto.com:15821/eureka/
mgp:
  carrier:
    discovery:
      env: uat
      registry: https://anapi-uat.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 45e3013695714f3383831eb5bf7c53e9

#ver
---
spring:
  profiles: ver
  logistics-log:
    path: ./logs
lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-ver.annto.com:15821/eureka/
mgp:
  carrier:
    discovery:
      env: ver
      registry: https://anapi-ver.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 30258309758541f78a662f49373562e9


#prod
---
spring:
  profiles: prod
  logistics-log:
    path: ./logs
lc:
  eureka:
    user: eureka-user
    password: cipher:l+3CmmYr4eejdJvTmip7S2dcGeEQej/7UUYR8gBQfWM=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi.annto.com:15821/eureka/

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: ec0a45f413fb496f8752ae10f91410b3

mgp:
  carrier:
    discovery:
      env: prd
      registry: https://anapi.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi
