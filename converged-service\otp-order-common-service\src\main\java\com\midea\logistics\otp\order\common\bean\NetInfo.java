package com.midea.logistics.otp.order.common.bean;

import com.mideaframework.core.bean.TenantPagingDomain;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: NetInfo
 * Author: zhangquan
 * Date: 2019-11-26 10:27:26
 * Description:网点信息表实体类
 */
@Data
public class NetInfo extends TenantPagingDomain{


   /**
    * 网点编码
    */
    private String netCode;


   /**
    * 网点名称
    */
    private String netName;


   /**
    * 供应商编码
    */
    private String supplierCode;


   /**
    * 网点联系人
    */
    private String netContact;


   /**
    * 网点手机
    */
    private String netMobile;


   /**
    * 网点电话
    */
    private String netTel;


   /**
    * 网点省编码
    */
    private String netProvinceCode;


   /**
    * 网点省名称
    */
    private String netProvinceName;


   /**
    * 网点市编码
    */
    private String netCityCode;


   /**
    * 网点市名称
    */
    private String netCityName;


   /**
    * 网点区域编码
    */
    private String netDistrictCode;


   /**
    * 网点区域名称
    */
    private String netDistrictName;


   /**
    * 网点乡镇编码
    */
    private String netTownCode;


   /**
    * 网点乡镇名称
    */
    private String netTownName;


   /**
    * 网点详细地址
    */
    private String netAddr;


   /**
    * 网点状态(Y有效，N无效，D呆滞)
    */
    private String netStatus;


   /**
    * 网点登录账号(A+网点编码)
    */
    private String netAccount;


   /**
    * 网点分中心名称
    */
    private String netCenterName;


   /**
    * 网点分中心编码
    */
    private String netCenterCode;


   /**
    * 网点经度
    */
    private BigDecimal netLng;


   /**
    * 网点维度
    */
    private BigDecimal netLat;

    /**
     * 站点编码
     */
    private String serverCode;

    /**
     * 站点名称
     */
    private String serverName;

    /**
     * 信息员名称
     */
    private String officerName;
    /**
     * 信息员手机
     */
    private String officerPhone;

    public NetInfo() {
    }

    public NetInfo(Long id) {
        this.id = id;
    }

}
