package com.midea.logistics.imp.orderverify.mq.consumer;

import com.midea.logistics.imp.orderverify.service.ZeebeLmpService;
import com.midea.logistics.otp.common.helper.IdGenHelper;
import com.midea.logistics.otp.constants.MessageQueueDefine;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.OrderFeign;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.transactionservice.mq.MqConsumer;
import com.mideaframework.transactionservice.mq.consumer.MQMessageHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 触发zeebe订单流程
 * @date 2020-10-28
 */
@Component
@MqConsumer(topic = MessageQueueDefine.ZEEBE_CUSTOMER_ORDER_RETRY_TOPIC, tag = MessageQueueDefine.ZEEBE_CUSTOMER_ORDER_RETRY_TAG)
public class ZeebeReturyConsumer implements MQMessageHandler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private OrderFeign orderFeign;
    @Autowired
    private ZeebeLmpService zeebeLmpService;

    @Override
    public void handlerMessage(MessageExt messageExt) {

        MDC.put("traceId", UUID.randomUUID().toString());
        String orderNo = new String(messageExt.getBody());
        logger.info(" mq消息 "+ MessageQueueDefine.ZEEBE_CUSTOMER_ORDER_RETRY_TOPIC + "   消费内容 message =  {}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            logger.warn("订单号为空：{}", messageExt.getMsgId());
            return;
        }

        List<String> tmpParentOrderNos = new ArrayList<>();
        List<String> parentOrderNos = new ArrayList<>();
        List<String> annntoOrderNos = new ArrayList<>();

        // AN 开头的单号，不检查父单
        if (!orderNo.startsWith(IdGenHelper.ANNTO_PREFIX) && !orderNo.startsWith(IdGenHelper.BN_PREFIX)){
            // 客户单号检测
            CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
            customerOrderInfo.setOrderNo(orderNo);
            JsonResponse<CustomerOrderInfo> custOrderRespone = customerOrderInfoFeign.searchOne(customerOrderInfo);
            if (custOrderRespone == null) {
                throw BusinessException.fail("查询订单失败：" + orderNo);
            }
            if (!BaseCodeEnum.SUCCESS.getCode().equals(custOrderRespone.getCode())) {
                throw BusinessException.fail("查询订单失败：" + custOrderRespone.getMsg());
            }
            customerOrderInfo = custOrderRespone.data;
            if (customerOrderInfo != null) {
                Integer orderStatus = customerOrderInfo.getOrderStatus();
                tmpParentOrderNos.add(orderNo);

                // 不需要走审核的订单
                if (orderStatus.intValue() >= 200 ) {
                    logger.info("====> 父订单 {} 状态为 {}, 不进行审核！", orderNo,  orderStatus);
                } else if ((System.currentTimeMillis() - customerOrderInfo.getUpdateTime().getTime() < 60 * 1000 ) && orderStatus > 100) {
                    logger.info("====> 订单 {} 最后一次更新才不到一分钟, 不进行审核！", orderNo);
                } else {
                    parentOrderNos.add(orderNo);
                }
            }
        }


        // IN 开头的单号，不检查子单
        if (!orderNo.startsWith("IN")){
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderNo(orderNo);
            JsonResponse<OrderInfo> orderRespone = orderFeign.selectOne(orderInfo);
            if (orderRespone == null) {
                throw BusinessException.fail("查询订单失败：" + orderNo);
            }
            if (!BaseCodeEnum.SUCCESS.getCode().equals(orderRespone.getCode())) {
                throw BusinessException.fail("查询订单失败：" + orderRespone.getMsg());
            }

            orderInfo = orderRespone.data;
            if (orderInfo != null) {
                Integer orderStatus = orderInfo.getOrderStatus();

                // 不需要走审核的订单
                if (orderStatus >= 200 ) {
                    logger.info("====> 订单 {} 状态为 {}, 不进行审核！", orderNo,  orderStatus);
                } else if ((System.currentTimeMillis() - orderInfo.getUpdateTime().getTime() < 60 * 1000 ) && orderStatus > 100) {
                    logger.info("====> 订单 {} 最后一次更新才不到一分钟, 不进行审核！", orderNo);
                } else {
                    annntoOrderNos.add(orderNo);
                }
            }
        }

        // 父单号查找子单号
        tmpParentOrderNos.forEach(parentOrderNo -> {
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setParentOrderNo(parentOrderNo);
            JsonResponse<List<OrderInfo>> search = orderFeign.list(orderInfo);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(search.getCode())) {
                throw BusinessException.fail("查询子订单失败");
            }
            List<String> collect = search.data.stream().filter(a -> a.getOrderStatus()<200).map(OrderInfo::getOrderNo).collect(Collectors.toList());
            annntoOrderNos.addAll(collect);
        });




        // 触发审核
        Integer success = 0;
        for (String parentOrderNo : parentOrderNos) {
            success += zeebeLmpService.trigger(parentOrderNo, true);
        }
        for (String anntoOrderNo : annntoOrderNos) {
            success += zeebeLmpService.trigger(anntoOrderNo, false);
        }
        logger.info("成功手动触发了 {} 个订单", success);
    }


}


