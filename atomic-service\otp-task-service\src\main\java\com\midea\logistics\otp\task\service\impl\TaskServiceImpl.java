package com.midea.logistics.otp.task.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.cache.manager.MdmDataDictionaryDetailManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.otp.bean.*;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.enums.AuditStatus;
import com.midea.logistics.otp.common.enums.BusinessControlParamEnum;
import com.midea.logistics.otp.common.enums.LineControlEnum;
import com.midea.logistics.otp.common.enums.WhCategoryEnum;
import com.midea.logistics.otp.common.feign.convergedfeign.order.BopServiceFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderExceptionRegistrationFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.VirtualPhoneInfoFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.ProfessionalCompanyConfigFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.helper.PremissionHelper;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.common.helper.bean.PremissionRequest;
import com.midea.logistics.otp.common.request.TmsBusinessCategoryConfigReq;
import com.midea.logistics.otp.common.utils.BeanUtil;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.helper.AesHelper;
import com.midea.logistics.otp.helper.CodeHelper;
import com.midea.logistics.otp.order.converged.domain.dto.AllOrderInfoDto;
import com.midea.logistics.otp.order.converged.domain.dto.TaskStatusUpdateBatch;
import com.midea.logistics.otp.order.converged.domain.request.ChangeWarehouseTaskRequest;
import com.midea.logistics.otp.order.converged.domain.request.OrderChangedReceiveDateRequest;
import com.midea.logistics.otp.order.domain.bean.OrderExceptionRegistration;
import com.midea.logistics.otp.order.domain.bean.VirtualPhoneInfo;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.OrderBatchSearchRequest;
import com.midea.logistics.otp.order.domain.dto.AiVoiceAppointDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.WmsInChangeWhRequest;
import com.midea.logistics.otp.rule.domain.bean.ProfessionalCompanyConfig;
import com.midea.logistics.otp.task.domain.bean.*;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDetailResponse;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.otp.task.domain.bean.custom.TaskInfoAndDetail;
import com.midea.logistics.otp.task.domain.bean.custom.TaskItemDTO;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskDetailsResponse;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse;
import com.midea.logistics.otp.task.domain.bean.response.TaskName;
import com.midea.logistics.otp.task.domain.dto.TaskExtendConfDto;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.midea.logistics.otp.task.mapper.common.TaskAddressMapper;
import com.midea.logistics.otp.task.mapper.common.TaskExtendMapper;
import com.midea.logistics.otp.task.mapper.common.TaskItemMapper;
import com.midea.logistics.otp.task.mapper.common.TaskMapper;
import com.midea.logistics.otp.task.mapper.custom.TaskAddressExtMapper;
import com.midea.logistics.otp.task.mapper.custom.TaskCustomMapper;
import com.midea.logistics.otp.task.service.ITaskAddressService;
import com.midea.logistics.otp.task.service.ITaskExtendService;
import com.midea.logistics.otp.task.service.ITaskItemService;
import com.midea.logistics.otp.task.service.ITaskService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.utils.date.DateUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.sdk.helper.MideaAuthUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskServiceImpl
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:TaskServiceImpl类
 */
@Service
@Slf4j
public class TaskServiceImpl implements ITaskService {

    private static final String TASK_NULL_OBJ =
        "{\"asc\":\" asc\",\"dataMap\":{},\"deleteFlag\":0,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"pageSize\":30,\"start\":0,\"version\":0}";

    private static final Logger logger = LoggerFactory.getLogger(TaskServiceImpl.class);

    // 缓存
    private static Map<String, String> mapArriveOverdueReason1 = new HashMap();
    private static Map<String, String> mapArriveOverdueReason2 = new HashMap();
    private static Map<String, String> mapOverdueReasonIn1 = new HashMap();
    private static Map<String, String> mapOverdueReasonIn2 = new HashMap();
    private static Map<String, String> mapOverdueReasonOut1 = new HashMap();
    private static Map<String, String> mapOverdueReasonOut2 = new HashMap();
    private static Map<String, String> mapSpecimenType = new HashMap();
    private static Map<String, String> mapLoadType = new HashMap();
    private static Map<String, String> mapServiceType = new HashMap();

    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskItemMapper taskItemMapper;
    @Autowired
    private TaskCustomMapper taskCustomMapper;
    @Autowired
    private EbCustomerManager ebCustomerManager;
    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private TaskAddressMapper taskAddressMapper;
    @Autowired
    private EsCompanyManager esCompanyManager;
    @Autowired
    private MdmDataDictionaryDetailManager dictionaryDetailManager;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private MideaAuthUserHelper mideaAuthUserHelper;
    @Autowired
    private CodeHelper codeHelper;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private VirtualPhoneInfoFeign virtualPhoneInfoFeign;
    @Autowired
    private TaskExtendMapper taskExtendMapper;
    @Autowired
    private OrderExceptionRegistrationFeign orderExceptionRegistrationFeign;
    @Autowired
    private TaskAddressExtMapper taskAddressExtMapper;
    @Autowired
    private PremissionHelper premissionHelper;
    @Autowired
    private ITaskAddressService iTaskAddressService;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;
    @Autowired
    private OrderExtendFeign orderExtendFeign;
    @Autowired
    private BopServiceFeign bopServiceFeign;
    @Autowired
    private ProfessionalCompanyConfigFeign professionalCompanyConfigFeign;
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private ITaskItemService taskItemService;
    @Autowired
    private ITaskExtendService taskExtendService;

    @Override
    @Transactional(readOnly = false)
    public Integer saveTask(Task task) {

        if (null == task) {
            logger.warn("save task, but task is null...");
            return 0;
        }

        task.setOaid(null);
        return taskMapper.save(task);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteTask(Task task) {

        if (null == task || null == task.getId()) {
            logger.warn("delete task, but task is null  or task id is null...");
            return 0;
        }

        return taskMapper.deleteById(task);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteByIdAndIncrementOrderNo(Task task) {

        if (null == task || null == task.getId()) {
            logger.warn("delete task, but task is null  or task id is null...");
            return 0;
        }

        task = taskMapper.selectById(task.getId());
        if (ToolUtils.isEmpty(task)) {
            logger.warn("delete task, but task not find");
            return 0;
        }

        //查询最大的订单号 +1
        String orderNo = task.getOrderNo();
        Integer deleteOrderNo = taskMapper.getDeleteOrderNo(orderNo);
        if (ToolUtils.isEmpty(deleteOrderNo)) {
            deleteOrderNo = 0;
        }
        deleteOrderNo++;

        //删除的同时更新订单号
        String newOrderNo = orderNo + "#" + deleteOrderNo;
        task.setOrderNo(newOrderNo);
        return taskMapper.deleteByIdAndIncrementOrderNo(task);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateTask(Task task) {

        if (null == task || null == task.getId()) {
            logger.warn("update task, but task is null  or task id is null...");
            return 0;
        }
        task.setOaid(null);
        return taskMapper.updateById(task);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer clearDistribution(Task task) {

        if (null == task || null == task.getTaskNo()) {
            logger.warn("update task, but task is null  or task no is null...");
            return 0;
        }

        return taskCustomMapper.clearDistribution(task);
    }


    @Override
    @Transactional(readOnly = false)
    public Integer updateDistribution(Task task) {

        if (null == task || null == task.getTaskNo()) {
            logger.warn("update task, but task is null  or task no is null...");
            return 0;
        }

        return taskCustomMapper.updateDistribution(task);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateDispatchNo(List<Task> tasks) {

        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("update tasks, but tasks is null ...");
            return 0;
        }

        return taskCustomMapper.updateDispatchNo(tasks);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateLine(List<Task> tasks) {

        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("update tasks, but tasks is null ...");
            return 0;
        }

        return taskCustomMapper.updateLine(tasks);
    }


    @Override
    public Task selectOneTask(Task task) {

        if (task == null) {
            logger.warn("select task one, but task is null ...");
            return null;
        }

        if (TASK_NULL_OBJ.equals(task.toString())) {
            ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            HttpServletRequest req = requestAttributes.getRequest();
            logger.error("{}|{}|{}| 请求了空参数用于查询任务", req.getRemoteHost(), req.getRequestURI(),
                req.getQueryString());
            return null;
        }

        task = taskMapper.selectOne(task);
        return task;
    }

    @Override
    public Task selectOneByRDIorRDO(Task task) {

        if (task == null) {
            logger.warn("select task one, but task is null ...");
            return null;
        }

        if (TASK_NULL_OBJ.equals(task.toString())) {
            ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            HttpServletRequest req = requestAttributes.getRequest();
            logger.error("{}|{}|{}| 请求了空参数用于查询任务", req.getRemoteHost(), req.getRequestURI(),
                req.getQueryString());
            return null;
        }

        task = taskCustomMapper.selectOneByRDIorRDO(task);
        return task;
    }

    @Override
    public Task selectOneByRDIorRDOorYS(Task task) {

        if (task == null) {
            logger.warn("select task one, but task is null ...");
            return null;
        }

        if (TASK_NULL_OBJ.equals(task.toString())) {
            ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            HttpServletRequest req = requestAttributes.getRequest();
            logger.error("{}|{}|{}| 请求了空参数用于查询任务", req.getRemoteHost(), req.getRequestURI(),
                req.getQueryString());
            return null;
        }

        task = taskCustomMapper.selectOneByRDIorRDOorYS(task);
        return task;
    }

    @Override
    public Task selectOneByRDIorRDOorIT(Task task) {

        if (task == null) {
            logger.warn("select task one, but task is null ...");
            return null;
        }

        if (TASK_NULL_OBJ.equals(task.toString())) {
            ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            HttpServletRequest req = requestAttributes.getRequest();
            logger.error("{}|{}|{}| 请求了空参数用于查询任务", req.getRemoteHost(), req.getRequestURI(),
                req.getQueryString());
            return null;
        }

        task = taskCustomMapper.selectOneByRDIorRDOorIT(task);
        return task;
    }

    @Override
    public PageResponse<Task> selectTaskPage(Task task) {
        PageResponse<Task> pagingData = new PageResponse<>();

        if (null == task) {
            logger.warn("select task page, but task is null...");
            return pagingData;
        }

        Integer queryCount = 1;
        // 最后一段任务
        boolean distributionLastFlag =
            StringUtils.isNotBlank(task.getOrderNo()) && null != task.getDistributionLastFlag() &&
                task.getDistributionLastFlag() == 1;
        if (StringUtils.isBlank(task.getTaskNo()) && !distributionLastFlag) {
            queryCount = taskMapper.selectByIndexCount(task);
            if (null != queryCount && queryCount <= 0) {
                logger.info("select task page , but count {} == 0 ...", queryCount);
                return pagingData;
            }
        }

        List<Task> tasks = selectTaskByIndex(task);
        pagingData.init(task.getPageNo(), task.getPageSize(), queryCount, tasks);
        return pagingData;
    }

    @Override
    public List<Task> selectTaskByIndex(Task task) {
        List<Task> tasks = new ArrayList<>();
        if (task == null) {
            logger.warn("select task by index, but task is null ...");
            return tasks;
        }

        tasks = taskMapper.selectByIndex(task);

        return tasks;
    }

    @Override
    public int countTaskByIndex(Task task) {
        int count = 0;
        if (task == null) {
            logger.warn("count task by index, but task is null ...");
            return count;
        }

        count = taskMapper.selectByIndexCount(task);

        return count;
    }

    @Override
    public Integer insertBatch(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("insertBatch tasks, but tasks is null ...");
            return 0;
        }
        return taskMapper.insertBatch(tasks);
    }

    @Override
    public Integer batchUpdate(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("batchUpdate tasks, but tasks is null ...");
            return 0;
        }
        return taskMapper.batchUpdate(tasks);
    }

    @Override
    public Integer batchUpdateByTask(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("batchUpdate tasks, but tasks is null ...");
            return 0;
        }
        return taskCustomMapper.batchUpdateByTask(tasks);
    }


    @Override
    public Integer batchUpdateTaskForNodeStatus(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("batchUpdateTaskForNodeStatus tasks, but tasks is null ...");
            return 0;
        }
        return taskCustomMapper.batchUpdateTaskForNodeStatus(tasks);
    }


    @Override
    public Integer update(Task task) {
        if (null == task) {
            logger.warn("update task, but task is null ...");
            return 0;
        }
        return taskCustomMapper.update(task);
    }

    @Override
    public Integer updateStatusByTaskNos(TaskStatusUpdateBatch statusUpdateBatch) {
        if (null == statusUpdateBatch) {
            logger.warn("update taskByTaskNos, but statusUpdateBatch is null ...");
            return 0;
        }
        if (CollectionUtil.isEmpty(statusUpdateBatch.getTaskNos())) {
            logger.warn("update taskByTaskNos, but taskNos is null ...");
            return 0;
        }
        if (statusUpdateBatch.getTaskStatus() == null && statusUpdateBatch.getExcuteStatus() == null) {
            logger.warn("update taskByTaskNos, but taskStatus and excuteStatus are null ...");
            return 0;
        }
        return taskCustomMapper.updateStatusByTaskNos(statusUpdateBatch);
    }

    @Override
    public Integer deleteBatch(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("deleteBatch tasks, but tasks is null ...");
            return 0;
        }
        return taskMapper.deleteBatch(tasks);
    }

    @Override
    public List<Task> selectTask(List<String> parentOrderNoes) {
        if (null == parentOrderNoes || parentOrderNoes.size() == 0) {
            logger.warn("selectTask,but orderNoes is null ...");
        }
        return taskCustomMapper.selectTask(parentOrderNoes);
    }

    @Override
    public Task deliveryCount(Task task) {
        if (null == task || StringUtils.isBlank(task.getSiteCode())) {
            logger.warn("deliveryCount,but task is null or site code is null ...");
        }
        return taskCustomMapper.deliveryCount(task);
    }

    @Override
    public List<Task> getTasksByOrderNos(List<String> orderNoes) {
        if (null == orderNoes || orderNoes.size() == 0) {
            logger.warn("getTasksByOrderNos,but orderNoes is null ...");
        }
        return taskCustomMapper.getTasksByOrderNos(orderNoes);
    }

    @Override
    public List<Task> getTasksByTaskNos(List<String> taskNos) {
        if (null == taskNos || taskNos.size() == 0) {
            logger.warn("getTasksByOrderNos,but orderNoes is null ...");
        }
        return taskCustomMapper.getTasksByTaskNos(taskNos);
    }

    @Override
    public PageResponse<SearchTaskResponse> searchTaskList(SearchTaskRequest searchTaskRequest) {
        PageResponse<SearchTaskResponse> pagingData = new PageResponse<>();

        Integer queryCount = 0;
        String isExport = searchTaskRequest.getIsExport();

        // 非导出的时候才查询数量
        if (CommonConstant.STRING_FLAG_NO.equals(isExport)) {
            queryCount = 10000;//taskCustomMapper.searchTaskListCountTaskTable(searchTaskRequest);
            //            if (null != queryCount && queryCount <= 0) {
            //                logger.info("searchTaskList page , but count {} == 0 ...", queryCount);
            //                return pagingData;
            //            }
            // 导出设置分页大小为999999
        } else {
            searchTaskRequest.setPageSize(CommonConstant.PAGE_SIZE);
        }
        List<SearchTaskResponse> taskResponseList = Lists.newArrayList();

        // 校验查询条件site_code与数据权限
        if (CommonEnum.Y.getValue().equals(searchTaskRequest.getDataAuthFlag())) {
            List<String> authCodes = mideaAuthUserHelper.getAuthCodesCaceable(CommonConstant.SITECODE_DATA);
            if (CollectionUtils.isEmpty(authCodes)) {
                return pagingData;
            }
            if (authCodes.contains("ALL")) {
                searchTaskRequest.setDataAuthFlag(CommonEnum.N.getValue());
            }
            String siteCode = searchTaskRequest.getSiteCode();
            if (StringUtils.isNotBlank(siteCode)) {
                if (authCodes.contains(siteCode)) {
                    searchTaskRequest.setDataAuthFlag(CommonEnum.N.getValue());
                }
                // 不存在ALL或搜索siteCode时直接返回
                if (!authCodes.contains("ALL") && !authCodes.contains(siteCode)) {
                    return pagingData;
                }
                searchTaskRequest.setSiteCodeList(Lists.newArrayList(siteCode));
            } else {
                if (!authCodes.contains("ALL")) {
                    searchTaskRequest.setSiteCodeList(authCodes);
                }
            }
        }
        //增加客户查询权限控制
        if (CommonEnum.Y.getValue().equals(searchTaskRequest.getCustomerAuthFlag())) {
            List<String> authCodes = mideaAuthUserHelper.getAuthCodesCaceable(CommonConstant.CUSTOMER_DATA_OTP);
            if (CollectionUtils.isEmpty(authCodes)) {
                return pagingData;
            }
            if (authCodes.contains("ALL")) {
                searchTaskRequest.setCustomerAuthFlag(CommonEnum.N.getValue());
            }
            String customerCode = searchTaskRequest.getCustomerCode();
            if (StringUtils.isNotBlank(customerCode)) {
                if (authCodes.contains(customerCode)) {
                    searchTaskRequest.setCustomerAuthFlag(CommonEnum.N.getValue());
                }
                // 不存在ALL或搜索siteCode时直接返回
                if (!authCodes.contains("ALL") && !authCodes.contains(customerCode)) {
                    return pagingData;
                }
                searchTaskRequest.setCustomerCodeList(Lists.newArrayList(customerCode));
            } else {
                if (!authCodes.contains("ALL")) {
                    searchTaskRequest.setCustomerCodeList(authCodes);
                }
            }
        }

        // 七天内不放弃时间索引 去掉时间索引控制 1是 0否
        if (BeanUtil.diffDay(searchTaskRequest.getCreateStartTime(), searchTaskRequest.getCreateEndTime(), 3)) {
            searchTaskRequest.setIgnoreIndex(1);
        }

        //是否精准配送
        if (null != searchTaskRequest.getIsPreciseDelivery()) {
            searchTaskRequest.setUpperAgingCode("JZP");
        }
        List<TaskDetailsResponse> detailsResponses = taskCustomMapper.searchTaskList(searchTaskRequest);
        HashMap<String, String> buyerType = dictHelper.getDictToMap("OMS_BUYER_TYPE");

        // 根据任务号取查询订单异常登记 表的内容
        JsonResponse<List<OrderExceptionRegistration>> orderExceptionRegistrationResponse =
            orderExceptionRegistrationFeign.queryLatestRecordByTaskNos(
                detailsResponses.stream().map(TaskDetailsResponse::getTaskNo).distinct().collect(Collectors.toList()));

        detailsResponses.stream().forEach(
            t ->{
                SearchTaskResponse searchTaskResponse = new SearchTaskResponse();
                //处理一下基础数据和数据字典
                setName(t);
                // 设置逾期备注
                setOverdueRemark(t, orderExceptionRegistrationResponse.data);
//                setFinalField(t);
                setPrivacyField(t);
                BeanUtils.copyProperties(t,searchTaskResponse);
                setFinalField(t,searchTaskResponse);
                //脱敏处理
                setSecurityMessage(searchTaskResponse);
                setBuyerType(t,searchTaskResponse,buyerType);
                //抽取扩展字段里的值
                getConfObjInfo(t, searchTaskResponse);

            String businessCategory = t.getBusinessCategory();
            if (StringUtils.isNotBlank(businessCategory)) {
                searchTaskResponse.setBusinessCategoryName(BusinessControlParamEnum.getValue(businessCategory));
            }
            String orderLineType = t.getOrderLineType();
            if (StringUtils.isNotBlank(orderLineType)) {
                searchTaskResponse.setOrderLineTypeName(BusinessControlParamEnum.getValue(orderLineType));
            }

            taskResponseList.add(searchTaskResponse);
        });
        pagingData.init(searchTaskRequest.getPageNo(), searchTaskRequest.getPageSize(), queryCount, taskResponseList);
        return pagingData;
    }

    /**
     * 抽取扩展字段里的值
     * @param t
     * @param searchTaskResponse
     */
    private void getConfObjInfo(TaskDetailsResponse t, SearchTaskResponse searchTaskResponse) {
        TaskExtendConfDto taskExtendConfDto = t.confObjJsonToDtoNotThrow();
        if (taskExtendConfDto != null) {
            searchTaskResponse.setPrintBoxLabelFlag(taskExtendConfDto.getPrintBoxLabel());
            searchTaskResponse.setLineControl(taskExtendConfDto.getLineControl());
            if (StringUtils.isNotBlank(taskExtendConfDto.getLineControl())) {
                searchTaskResponse.setLineControlName(LineControlEnum.getName(taskExtendConfDto.getLineControl()));
            }
        }
    }

    /**
     * 列表查询设置逾期备注
     * @param taskDetailsResponse
     * @param orderExceptionRegistrationList
     */
    private void setOverdueRemark(TaskDetailsResponse taskDetailsResponse,
        List<OrderExceptionRegistration> orderExceptionRegistrationList) {
        if (CollectionUtil.isNotEmpty(orderExceptionRegistrationList)) {
            // 根据任务号比对
            Optional<OrderExceptionRegistration> optional = orderExceptionRegistrationList.stream()
                .filter(item -> StringUtils.equals(taskDetailsResponse.getTaskNo(), item.getTaskNo())).findFirst();
            if (optional.isPresent()) {
                // 找到记录copy属性
                OrderExceptionRegistration orderExceptionRegistration = optional.get();
                BeanUtils.copyProperties(orderExceptionRegistration, taskDetailsResponse, "tenantCode",
                    "customerOrderNo", "orderNo", "taskNo", "waybillNo", "remark", "id", "version", "createTime",
                    "createUserCode", "updateTime", "updateUserCode");

                // 拿到逾期大类
                OverdueClassifyEnum overdueClassifyEnum =
                    OverdueClassifyEnum.getEnumByKey(orderExceptionRegistration.getOverdueClassify());
                taskDetailsResponse.setOverdueClassifyName(
                    Objects.requireNonNull(overdueClassifyEnum).getValue()); // 设置逾期分类
                taskDetailsResponse.setOverdueRegistrationTime(orderExceptionRegistration.getCreateTime()); // 设置逾期登记时间
                taskDetailsResponse.setOverdueUpdateTime(orderExceptionRegistration.getUpdateTime());
                taskDetailsResponse.setOverdueUpdateUserCode(orderExceptionRegistration.getUpdateUserCode());
            }
        }
    }

    private void setSecurityMessage(TaskDetailsResponse taskDetailsResponse) {
        String senderName = taskDetailsResponse.getSenderName();
        String senderMobile = taskDetailsResponse.getSenderMobile();
        String senderDetailAddr = taskDetailsResponse.getSenderDetailAddr();
        String receiverName = taskDetailsResponse.getReceiverName();
        String receiverMobile = taskDetailsResponse.getReceiverMobile();
        String receiverDetailAddr = taskDetailsResponse.getReceiverDetailAddr();
        String finalName = taskDetailsResponse.getFinalName();
        String finalDetailAddr = taskDetailsResponse.getFinalDetailAddr();
        String finalConsignee = taskDetailsResponse.getFinalConsignee();
        String finalDeliveryAddress = taskDetailsResponse.getFinalDeliveryAddress();

        try {
            String key = AesHelper.KEY;
            if (!codeHelper.isEncrypt(receiverName, CodeHelper.NAME)) {
                taskDetailsResponse.setReceiverName(codeHelper.msgDesensitization(receiverName, CodeHelper.NAME));
                taskDetailsResponse.setReceiverNameEncrypted(AesHelper.aesEncode(key, receiverName));
            }
            if (!codeHelper.isEncrypt(receiverMobile, CodeHelper.MOBILE)) {
                taskDetailsResponse.setReceiverMobile(codeHelper.msgDesensitization(receiverMobile, CodeHelper.MOBILE));
                taskDetailsResponse.setReceiverMobileEncrypted(AesHelper.aesEncode(key, receiverMobile));
            }
            if (!codeHelper.isEncrypt(receiverDetailAddr, CodeHelper.ADDR)) {
                taskDetailsResponse.setReceiverDetailAddr(
                    codeHelper.msgDesensitization(receiverDetailAddr, CodeHelper.ADDR));
                taskDetailsResponse.setReceiverDetailAddrEncrypted(AesHelper.aesEncode(key, receiverDetailAddr));
            }
            if (!codeHelper.isEncrypt(senderName, CodeHelper.NAME)) {
                taskDetailsResponse.setSenderName(codeHelper.msgDesensitization(senderName, CodeHelper.NAME));
                taskDetailsResponse.setSenderNameEncrypted(AesHelper.aesEncode(key, senderName));
            }
            if (!codeHelper.isEncrypt(senderMobile, CodeHelper.MOBILE)) {
                taskDetailsResponse.setSenderMobile(codeHelper.msgDesensitization(senderMobile, CodeHelper.MOBILE));
                taskDetailsResponse.setSenderMobileEncrypted(AesHelper.aesEncode(key, senderMobile));
            }
            if (!codeHelper.isEncrypt(senderDetailAddr, CodeHelper.ADDR)) {
                taskDetailsResponse.setSenderDetailAddr(
                    codeHelper.msgDesensitization(senderDetailAddr, CodeHelper.ADDR));
                taskDetailsResponse.setSenderDetailAddrEncrypted(AesHelper.aesEncode(key, senderDetailAddr));
            }
            if (!codeHelper.isEncrypt(finalName, CodeHelper.NAME)) {
                taskDetailsResponse.setFinalName(codeHelper.msgDesensitization(finalName, CodeHelper.NAME));
                taskDetailsResponse.setFinalName(AesHelper.aesEncode(key, finalName));
            }
            if (!codeHelper.isEncrypt(finalDetailAddr, CodeHelper.ADDR)) {
                taskDetailsResponse.setFinalDetailAddr(codeHelper.msgDesensitization(finalDetailAddr, CodeHelper.ADDR));
                taskDetailsResponse.setFinalDetailAddrEncrypted(AesHelper.aesEncode(key, finalDetailAddr));
            }
            if (!codeHelper.isEncrypt(finalConsignee, CodeHelper.NAME)) {
                taskDetailsResponse.setFinalConsignee(codeHelper.msgDesensitization(finalConsignee, CodeHelper.NAME));
                taskDetailsResponse.setFinalConsigneeEncrypted(AesHelper.aesEncode(key, finalConsignee));
            }
            if (!codeHelper.isEncrypt(finalDeliveryAddress, CodeHelper.ADDR)) {
                taskDetailsResponse.setFinalDeliveryAddress(
                    codeHelper.msgDesensitization(finalDeliveryAddress, CodeHelper.ADDR));
                taskDetailsResponse.setFinalDeliveryAddressEncrypted(AesHelper.aesEncode(key, finalDeliveryAddress));
            }
        } catch (Exception exception) {
            logger.warn("收发货信息加密异常,任务号:{}", taskDetailsResponse.getTaskNo());
        }
    }

    private void setBuyerType(TaskDetailsResponse detailsResponse, SearchTaskResponse searchTaskResponse,
        HashMap<String, String> buyerType) {
        Integer sourceBuyerTenantType = detailsResponse.getSourceBuyerTenantType();
        if (sourceBuyerTenantType != null) {
            buyerType.forEach((key, value) -> {
                if (key.equals(String.valueOf(sourceBuyerTenantType))) {
                    searchTaskResponse.setSourceBuyerTenantName(value);
                }
            });
        }
    }
    
    /**
     * !@虚拟号 - 隐私号脱敏处理（任务中心查询）
     * @param t
     */
    private void setPrivacyField(TaskDetailsResponse t) {
        BusinessHelperDto dto = new BusinessHelperDto();
        BeanUtils.copyProperties(t, dto);
        //这里比较特殊，必须设置为空，否则如果子单非ZT,任务是ZT就会不展示了
        dto.setDeliveryType("");
        boolean showPrivacyPhone = businessHelper.isShowPrivacyPhone(dto);
        //是否展示虚拟号
        if (showPrivacyPhone) {

            //隐私号订单脱敏处理
            if (StringUtils.isNotBlank(t.getReceiverVirtualMobile())) {
                String finalMobile = t.getFinalMobile();
                String finalTel = t.getFinalTel();
                String receiverMobile = t.getReceiverMobile();
                String receiverTel = t.getReceiverTel();
                String networkPhone = t.getNetworkPhone();
                String networkTel = t.getNetworkTel();
                if (null != finalMobile && finalMobile.equals(receiverMobile)) {
                    t.setReceiverMobile(codeHelper.mobileDesensitization(receiverMobile));
                }
                if (null != finalTel && finalTel.equals(receiverTel)) {
                    t.setReceiverTel(codeHelper.mobileDesensitization(receiverTel));
                }
                if (null != finalMobile && finalMobile.equals(networkPhone)) {
                    t.setNetworkPhone(codeHelper.mobileDesensitization(networkPhone));
                }
                if (null != finalTel && finalTel.equals(networkTel)) {
                    t.setNetworkTel(codeHelper.mobileDesensitization(networkTel));
                }
                t.setFinalMobile(codeHelper.mobileDesensitization(finalMobile));
                t.setFinalTel(codeHelper.mobileDesensitization(finalTel));
                //有效期过期不展示虚拟号
                VirtualPhoneInfo info = new VirtualPhoneInfo();
                info.setParentOrderNo(t.getParentOrderNo());
                VirtualPhoneInfo virtualPhoneInfo = virtualPhoneInfoFeign.select(info).getData();
                if (virtualPhoneInfo == null) {
                    t.setReceiverVirtualMobile(null);
                    t.setSenderVirtualMobile(null);
                    return;
                }
                if (virtualPhoneInfo.getValidTime() == null) {
                    t.setReceiverVirtualMobile(null);
                    t.setSenderVirtualMobile(null);
                    return;
                }
                boolean valid = virtualPhoneInfo.getValidTime().getTime() - System.currentTimeMillis() >= 0;
                if (!valid) {
                    t.setReceiverVirtualMobile(null);
                    t.setSenderVirtualMobile(null);
                }
            }

            //发货人虚拟号
            if (StringUtils.isNotBlank(t.getSenderVirtualMobile())) {
                String finalMobile = t.getFinalMobile();
                String finalTel = t.getFinalTel();
                String senderMobile = t.getSenderMobile();
                String senderTel = t.getReceiverTel();
                String networkPhone = t.getNetworkPhone();
                String networkTel = t.getNetworkTel();
                if (null != finalMobile && finalMobile.equals(senderMobile)) {
                    t.setSenderMobile(codeHelper.mobileDesensitization(senderMobile));
                }
                if (null != finalTel && finalTel.equals(senderTel)) {
                    t.setSenderTel(codeHelper.mobileDesensitization(senderTel));
                }
                //if (null != finalMobile && finalMobile.equals(networkPhone)) {
                //    t.setNetworkPhone(codeHelper.mobileDesensitization(networkPhone));
                //}
                //if (null != finalTel && finalTel.equals(networkTel)) {
                //    t.setNetworkTel(codeHelper.mobileDesensitization(networkTel));
                //}
                t.setFinalMobile(codeHelper.mobileDesensitization(finalMobile));
                t.setFinalTel(codeHelper.mobileDesensitization(finalTel));
                //有效期过期不展示虚拟号
                VirtualPhoneInfo info = new VirtualPhoneInfo();
                info.setParentOrderNo(t.getParentOrderNo());
                VirtualPhoneInfo virtualPhoneInfo = virtualPhoneInfoFeign.select(info).getData();
                if (virtualPhoneInfo == null) {
                    t.setSenderVirtualMobile(null);
                    return;
                }
                if (virtualPhoneInfo.getValidTime() == null) {
                    t.setSenderVirtualMobile(null);
                    return;
                }
                boolean valid = virtualPhoneInfo.getValidTime().getTime() - System.currentTimeMillis() >= 0;
                if (!valid) {
                    t.setSenderVirtualMobile(null);
                }
            }
            
        } else {
            //不符合的置空虚拟号
            t.setReceiverVirtualMobile(null);
            t.setSenderVirtualMobile(null);
        }
    }

    private void setFinalField(TaskDetailsResponse t, SearchTaskResponse searchTaskResponse) {
        StringBuilder finalDeliveryAddress = new StringBuilder();
        StringBuilder contactInformation = new StringBuilder();
        finalDeliveryAddress.append(null == t.getFinalProvinceName() ? "-" : t.getFinalProvinceName());
        finalDeliveryAddress.append("/");
        finalDeliveryAddress.append(null == t.getFinalCityName() ? "-" : t.getFinalCityName());
        finalDeliveryAddress.append("/");
        finalDeliveryAddress.append(null == t.getFinalDistrictName() ? "-" : t.getFinalDistrictName());
        finalDeliveryAddress.append("/");
        finalDeliveryAddress.append(null == t.getFinalTownName() ? "-" : t.getFinalTownName());
        finalDeliveryAddress.append("/");
        finalDeliveryAddress.append(null == t.getFinalDetailAddr() ? "-" : t.getFinalDetailAddr());
        searchTaskResponse.setFinalDeliveryAddress(finalDeliveryAddress.toString());
        if (null != t.getReceiverVirtualMobile()) {
            contactInformation.append(t.getReceiverVirtualMobile());
            contactInformation.append("/");
        }
        if (null != t.getFinalMobile()) {
            contactInformation.append(t.getFinalMobile());
            contactInformation.append("/");
        }
        if (null != t.getFinalTel()) {
            contactInformation.append(t.getFinalTel());
        }
        if (contactInformation.length() == 0) {
            contactInformation.append(" ");
        }
        if (contactInformation.lastIndexOf("/") == contactInformation.length() - 1) {
            contactInformation.deleteCharAt(contactInformation.length() - 1);
        }
        searchTaskResponse.setContactInformation(contactInformation.toString());
        searchTaskResponse.setFinalConsignee(t.getFinalName());
    }

    private void setName(TaskName t) {
        t.setPreAppointWayName(PreAppointmentType.getName(t.getPreAppointWay()));
        String appointmentTimeSlot = "";
        if (ToolUtils.isNotEmpty(t.getOrderAppointmentTime())) {
            appointmentTimeSlot = DateUtils.getDate(t.getOrderAppointmentTime(), CommonConstant.DAY);
        }
        t.setArriveAppointmentFlag(0);
        if (ToolUtils.isNotEmpty(t.getAppointmentTime())) {
            t.setArriveAppointmentFlag(1);
            // appointmentTimeSlot =  DateUtils.getDate(t.getAppointmentTime(),CommonConstant.DAY);
        }
        if (ToolUtils.isNotEmpty(t.getArriveTimeSlot())) {
            appointmentTimeSlot = appointmentTimeSlot + " " + t.getArriveTimeSlot();
        }
        t.setAppointmentTimeSlot(appointmentTimeSlot);

        if (ToolUtils.isNotEmpty(t.getReDistribute())) {
            if (1 == t.getReDistribute()) {
                t.setReDistributeName("二次派送");
            }
            if (2 == t.getReDistribute()) {
                t.setReDistributeName("二次派送核销");
            }
        }
//        if (ToolUtils.isNotEmpty(t.getAgingProductCode())) {
//            t.setAgingProductName(taskCustomMapper.selectAgingProductNameByCode(t.getAgingProductCode()));
//        }
        t.setExcuteStatusName(ExcuteStatus.taskExcuteStatus(t.getExcuteStatus()));

        String orderType = t.getOrderType();
        String serviceType = t.getServiceType();
        String orderTypeName = OrderType.getName(orderType);
        // 逆向纯运输
        if (ToolUtils.isNotEmpty(serviceType) &&
            Objects.equals(serviceType, String.valueOf(ServiceType.RIYS.getKey()))) {
            orderTypeName = "逆向纯运输订单";
        }
        t.setOrderTypeName(orderTypeName);
        t.setTaskStatusName(OrderStatus.getName(t.getTaskStatus()));
        t.setPreAppointStatusName(PreAppointmentStatus.getName(t.getPreAppointStatus()));
        String projectClassify = t.getProjectClassify();
        t.setProjectClassifyName(dictHelper.getDictVaule("PROJECT_CLASSIFY", projectClassify));
        t.setJoinTypeName(dictHelper.getDictVaule("JOIN_TYPE", t.getJoinType()));
        t.setProfessionalCompanyName(ProfessionalCompanyEnum.getNamebyValue(t.getProfessionalCompany()));
        t.setHoldFlagName(HoldFlag.getName(t.getHoldFlag()));
        //上撤样
        String specimenType = t.getSpecimenType();
        if (ToolUtils.isNotEmpty(specimenType)) {
            String specimenTypeName = null;
            if (MapUtils.isNotEmpty(mapSpecimenType) && mapSpecimenType.containsKey(specimenType)) {
                specimenTypeName = mapSpecimenType.get(specimenType);
            } else {
                specimenTypeName =
                    dictHelper.getDictVaule(SpecimenType.UPSTREAM_SPECIMEN_TYPE.getValue(), specimenType);
                mapSpecimenType.put(specimenType, specimenTypeName);
            }
            t.setSpecimenTypeName(specimenTypeName);
        }

        //客户名称
        if (ToolUtils.isNotEmpty(t.getCustomerCode()) && StringUtils.isBlank(t.getCustomerName())) {
            EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(t.getCustomerCode());
            if (ebCustomerCache != null) {
                t.setCustomerShortName(ebCustomerCache.getEbcuShortName());
                t.setCustomerName(ebCustomerCache.getEbcuNameCn());
            }
        }

        // 目标客户
        if (ToolUtils.isNotEmpty(t.getTargetCustomerCode()) && StringUtils.isBlank(t.getTargetCustomerName())){
            EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(t.getTargetCustomerCode());
            if (ebCustomerCache != null) {
                t.setTargetCustomerName(ebCustomerCache.getEbcuNameCn());
            }
        }

        //装卸费
        String orderRpFlag = t.getOrderRpFlag();
        if (ToolUtils.isNotEmpty(orderRpFlag)) {
            t.setOrderRpFlagName(FeeType.getName(orderRpFlag));
        }

        //中转快运标示
        Integer transferFlag = t.getTransferFlag();
        if (ToolUtils.isNotEmpty(transferFlag)) {
            t.setTransferFlagName(TransFerFlag.getName(transferFlag));
        }

        //测量标准
        String freightBasis = t.getFreightBasis();
        if (ToolUtils.isNotEmpty(freightBasis)) {
            t.setFreightBasisName(dictHelper.getDictVaule(FreightBasisEnum.DICT_CODE.getKey(), freightBasis));
        }

        //整车零担标识
        String loadType = t.getLoadType();
        if (ToolUtils.isNotEmpty(loadType)) {
            String loadTypeName = null;
            if (MapUtils.isNotEmpty(mapLoadType) && mapLoadType.containsKey(loadType)) {
                loadTypeName = mapLoadType.get(loadType);
            } else {
                loadTypeName = dictHelper.getDictVaule(LoadType.DICT_CODE.getValue(), loadType);
                mapLoadType.put(loadType, loadTypeName);
            }
            t.setLoadTypeName(loadTypeName);
        }

        //审核状态
        String auditStatus = t.getAuditStatus();
        if (ToolUtils.isNotEmpty(auditStatus)) {
            t.setAuditStatusName(AuditStatus.getName(auditStatus));
        }

        //商超默认否
        Integer scPosFlag = t.getScPosFlag();
        if (null == scPosFlag) {
            t.setScPosFlag(0);
        }

        //任务类型
        if (ToolUtils.isNotEmpty(t.getTaskType())) {
            t.setTaskTypeName(TaskType.getValue(t.getTaskType()));
        }
        //订单状态
        if(ToolUtils.isNotEmpty((t.getOrderStatus()))){
            t.setOrderStatusName(OrderStatus.taskOrderStatus(t.getOrderStatus()));
        }
        //仓库名称
        if(ToolUtils.isNotEmpty(t.getWhCode()) && StringUtils.isBlank(t.getWhName())){
            CdWarehouse warehouse = cdWarehouseManager.getCdWarehouseCache(t.getWhCode());
            if (warehouse != null) {
                t.setWhName(warehouse.getCdwhName());
            }
        }
        //列配置中增加配送方式、业务模式、网点名称、分拨标识、分拨仓
        if(ToolUtils.isNotEmpty((t.getDeliveryType()))){
            t.setDeliveryTypeName(DeliveryType.getName(t.getDeliveryType()));
        }
        if (ToolUtils.isNotEmpty(t.getBusinessType())){
            t.setBusinessTypeName(BusinessType.getName(t.getBusinessType()));
        }

        //增加计费标准
        if(ToolUtils.isNotEmpty(t.getFreightBasis())){
            t.setFreightBasisName(dictHelper.getDictVaule(FreightBasisEnum.DICT_CODE.getKey(),freightBasis));
        }

        //时效类型名称
        if(ToolUtils.isNotEmpty(t.getAgingType())){
            t.setAgingTypeName(dictHelper.getDictVaule("RULE_AGING_TYPE",t.getAgingType()));
        }

        //一级分拨仓库编码找名称
        String distributionWhCode = t.getDistributionWhCode();
        if (StringUtils.isNoneBlank(distributionWhCode) && StringUtils.isBlank(t.getDistributionWhName())) {
            CdWarehouse cdWarehouseCache1 = cdWarehouseManager.getCdWarehouseCache(distributionWhCode);
            if (cdWarehouseCache1 != null) {
                String distributionWhName = cdWarehouseCache1.getCdwhName();
                t.setDistributionWhName(distributionWhName);
            } else {
                logger.error("找不到一级分拨仓库:{}", distributionWhCode);
            }
        }
        //二级分拨仓库编码找名称
        //        String nextDistributeWhCode = t.getNextDistributionWhCode();
        //        if (StringUtils.isNoneBlank(nextDistributeWhCode)) {
        //            CdWarehouse cdWarehouseCache2 = cdWarehouseManager.getCdWarehouseCache(nextDistributeWhCode);
        //            if (cdWarehouseCache2 != null) {
        //                String nextDistributeWhName = cdWarehouseCache2.getCdwhName();
        //                t.setNextDistributionWhName(nextDistributeWhName);
        //            } else {
        //                logger.error("找不到二级分拨仓库:{}", distributionWhCode);
        //            }
        //        }

        if (ToolUtils.isNotEmpty(t.getSiteCode()) && StringUtils.isBlank(t.getSiteName())) {
            EsCompany esCompanyCache = esCompanyManager.getEsCompanyCache(t.getSiteCode());
            if (esCompanyCache != null) {
                String nameCn = esCompanyCache.getEscoCompanyNameCn();
                t.setSiteName(nameCn);
            } else {
                logger.error("找不到平台:{}", t.getSiteCode());
            }
        }


        // 到达逾期原因1
        String arriveOverdueReason1 = t.getArriveOverdueReason1();
        if (ToolUtils.isNotEmpty(arriveOverdueReason1)) {
            String arriveOverdueReason1Name = null;
            if (MapUtils.isNotEmpty(mapArriveOverdueReason1) &&
                mapArriveOverdueReason1.containsKey(arriveOverdueReason1)) {
                arriveOverdueReason1Name = mapArriveOverdueReason1.get(arriveOverdueReason1);
            } else {
                arriveOverdueReason1Name =
                    dictHelper.getDictVaule(overDueReason.DICT_CODE_ARRIVE_1.getValue(), arriveOverdueReason1);
                mapArriveOverdueReason1.put(arriveOverdueReason1, arriveOverdueReason1Name);
            }
            t.setArriveOverdueReason1Name(arriveOverdueReason1Name);
        }

        // 到达逾期原因1
        String arriveOverdueReason2 = t.getArriveOverdueReason2();
        if (ToolUtils.isNotEmpty(arriveOverdueReason2)) {
            String arriveOverdueReason2Name = null;
            if (MapUtils.isNotEmpty(mapArriveOverdueReason2) &&
                mapArriveOverdueReason2.containsKey(arriveOverdueReason2)) {
                arriveOverdueReason2Name = mapArriveOverdueReason2.get(arriveOverdueReason2);
            } else {
                arriveOverdueReason2Name =
                    dictHelper.getDictVaule(overDueReason.DICT_CODE_ARRIVE_2.getValue(), arriveOverdueReason2);
                mapArriveOverdueReason2.put(arriveOverdueReason2, arriveOverdueReason2Name);
            }
            t.setArriveOverdueReason2Name(arriveOverdueReason2Name);
        }

        String inOutType = t.getInOutType();
        if (InOutType.IN.getName().equals(inOutType)) {
            // 到达逾期原因1
            String overdueReason1 = t.getOverdueReason1();
            if (ToolUtils.isNotEmpty(overdueReason1)) {
                String overdueReason1Name = null;
                if (MapUtils.isNotEmpty(mapOverdueReasonIn1) && mapOverdueReasonIn1.containsKey(overdueReason1)) {
                    overdueReason1Name = mapOverdueReasonIn1.get(overdueReason1);
                } else {
                    overdueReason1Name =
                        dictHelper.getDictVaule(overDueReason.DICT_CODE_IN_1.getValue(), overdueReason1);
                    mapOverdueReasonIn1.put(overdueReason1, overdueReason1Name);
                }
                t.setInOverdueReason1Name(overdueReason1Name);
            }

            // 到达逾期原因1
            String overdueReason2 = t.getOverdueReason2();
            if (ToolUtils.isNotEmpty(overdueReason2)) {
                String overdueReason2Name = null;
                if (MapUtils.isNotEmpty(mapOverdueReasonIn2) && mapOverdueReasonIn2.containsKey(overdueReason2)) {
                    overdueReason2Name = mapOverdueReasonIn2.get(overdueReason2);
                } else {
                    overdueReason2Name =
                        dictHelper.getDictVaule(overDueReason.DICT_CODE_IN_2.getValue(), overdueReason2);
                    mapOverdueReasonIn2.put(overdueReason2, overdueReason2Name);
                }
                t.setInOverdueReason2Name(overdueReason2Name);
            }

            // 入库备注
            t.setInOverdueRemark(t.getOverdueRemark());
            // 发货逾期登记时间
            t.setInOverdueTime(t.getOverdueTime());

        } else if (InOutType.OUT.getName().equals(inOutType) || InOutType.YS.getName().equals(inOutType)) {
            // 出库逾期原因1
            String overdueReason1 = t.getOverdueReason1();
            if (ToolUtils.isNotEmpty(overdueReason1)) {
                String overdueReason1Name = null;
                if (MapUtils.isNotEmpty(mapOverdueReasonOut1) && mapOverdueReasonOut1.containsKey(overdueReason1)) {
                    overdueReason1Name = mapOverdueReasonOut1.get(overdueReason1);
                } else {
                    overdueReason1Name =
                        dictHelper.getDictVaule(overDueReason.DICT_CODE_OUT_1.getValue(), overdueReason1);
                    mapOverdueReasonOut1.put(overdueReason1, overdueReason1Name);
                }
                t.setOutOverdueReason1Name(overdueReason1Name);
            }

            // 出库逾期原因1
            String overdueReason2 = t.getOverdueReason2();
            if (ToolUtils.isNotEmpty(overdueReason2)) {
                String overdueReason2Name = null;
                if (MapUtils.isNotEmpty(mapOverdueReasonOut2) && mapOverdueReasonOut2.containsKey(overdueReason2)) {
                    overdueReason2Name = mapOverdueReasonOut2.get(overdueReason2);
                } else {
                    overdueReason2Name =
                        dictHelper.getDictVaule(overDueReason.DICT_CODE_OUT_2.getValue(), overdueReason2);
                    mapOverdueReasonOut2.put(overdueReason2, overdueReason2Name);
                }
                t.setOutOverdueReason2Name(overdueReason2Name);
            }

            // 出库备注
            t.setOutContactMobile(t.getContactMobile());
            // 出库备注
            t.setOutContactName(t.getContactName());
            // 出库备注
            t.setOutOverdueRemark(t.getOverdueRemark());
            // 发货逾期登记时间
            t.setOutOverdueTime(t.getOutOverdueTime());
        }
        //处理老的时效规则 先匹配CustomerAgingTypeEnum的老的时效 后面再查 standard_aging_rule_config_info 表再匹配一次数据
        //不清楚CustomerAgingTypeEnum是否有其他地方在使用，enum不动先，再次查表匹配时效是防止Enum配置的与standard_aging_rule_config_info 对不上数据
        if (StringUtils.isNotBlank(t.getCustomerAgingType())) {
            Arrays.stream(CustomerAgingTypeEnum.values())
                .filter(typeEnum -> typeEnum.getKey().equals(t.getCustomerAgingType())).findFirst()
                .ifPresent(typeEnum -> t.setCustomerAgingTypeName(typeEnum.getValue()));
        }

        t.setTargetSiteCode(t.getTargeSiteCode());
        t.setTargetSiteName(getSiteName(t.getTargeSiteCode()));
        t.setTargeSiteCode(null);

        //运输任务划分标识
        String transportationDivision = t.getTransportationDivision();
        if (ToolUtils.isNotEmpty(transportationDivision)) {
            t.setTransportationDivisionName(TransportationDivision.getValue(transportationDivision));
        }
    }

    /**
     * 根据平台编码获取平台名称
     * @param siteCode
     * @return
     */
    private String getSiteName(String siteCode) {
        if (!org.springframework.util.StringUtils.isEmpty(siteCode)) {
            EsCompany esCompany = esCompanyManager.getEsCompanyCache(siteCode);
            if (esCompany != null) {
                return esCompany.getEscoCompanyNameCn();
            }
        }
        return "";
    }

    /**
     * !@任务详情 - 最终入口
     * @createtime: 2023/9/13 14:20
     */
    @Override
    public SearchTaskDetailsResponse searchTaskDetails(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            throw BusinessException.fail("任务详情查询，任务号不能为空");
        }

        SearchTaskDetailsResponse searchTaskDetailsResponse = new SearchTaskDetailsResponse();
        Task t = new Task();
        t.setTaskNo(taskNo);
        t = taskMapper.selectOne(t);
        if (t == null) {
            throw BusinessException.fail("未查到任务");
        }
        BeanUtils.copyProperties(t, searchTaskDetailsResponse);

        // 查询 任务扩展表
        TaskExtend te = new TaskExtend();
        te.setTaskNo(taskNo);
        TaskExtend taskExtend = taskExtendMapper.selectOne(te);
        if (taskExtend != null) {
            searchTaskDetailsResponse.setTransportationDivision(taskExtend.getTransportationDivision());
            // 设置扩展字段
            extendConfObj(searchTaskDetailsResponse, taskExtend);
        }

        //处理一下基础数据和数据字典
        setName(searchTaskDetailsResponse);
        TaskAddress taskAddress = new TaskAddress();
        taskAddress.setTaskNo(taskNo);
        taskAddress = taskAddressMapper.selectOne(taskAddress);
        //虚拟号判断和处理
        judgeAndHandleAxn(t, taskAddress);
        searchTaskDetailsResponse.setTaskAddress(taskAddress);
        TaskItem taskItem = new TaskItem();
        taskItem.setTaskNo(taskNo);
        taskItem.setPageSize(CommonConstant.LIST_PAGE_SIZE);
        List<TaskItem> itemList = taskItemMapper.selectByIndex(taskItem);
        List<TaskItemDTO> itemDTOList = Lists.newArrayList();
        itemList.stream().forEach(it -> {
            TaskItemDTO taskItemDTO = generateTaskItemDTO(it);
            itemDTOList.add(taskItemDTO);
        });
        if (ToolUtils.isNotEmpty(itemDTOList)) {
            itemDTOList.sort(Comparator.comparing(o -> null == o.getItemLineNo() ? 0 : o.getItemLineNo()));
        }
        searchTaskDetailsResponse.setItemList(itemDTOList);
        return searchTaskDetailsResponse;
    }

    // 设置扩展字段
    private void extendConfObj(SearchTaskDetailsResponse searchTaskDetailsResponse, TaskExtend taskExtend) {
        TaskExtendConfDto taskExtendConfDto = taskExtend.confObjJsonToDtoNotThrow();
        if (null != taskExtendConfDto){
            searchTaskDetailsResponse.setLineControl(taskExtendConfDto.getLineControl());
            searchTaskDetailsResponse.setUserImageLogoName(taskExtendConfDto.getUserImageLogoName());
            searchTaskDetailsResponse.setEmergencyRemark(taskExtendConfDto.getEmergencyRemark());
        }
    }

    /**
     * !@虚拟号 - 隐私号脱敏处理（任务详情）
     * @param t
     * @param taskAddress
     */
    private void judgeAndHandleAxn(Task t, TaskAddress taskAddress) {
        BusinessHelperDto dto = new BusinessHelperDto();
        BeanUtils.copyProperties(t,dto);
        //这里比较特殊，必须设置为空，否则如果子单非ZT,任务是ZT就会不展示了
        dto.setDeliveryType("");
        //如果隐私号开关打开,如果有虚拟号,则真实手机号要做脱敏处理
        boolean showPrivacyPhone = businessHelper.isShowPrivacyPhone(dto);
        if (showPrivacyPhone){
            if (StringUtils.isNotBlank(taskAddress.getReceiverVirtualMobile())) {
                String finalMobile = taskAddress.getFinalMobile();
                String finalTel = taskAddress.getFinalTel();
                String receiverMobile = taskAddress.getReceiverMobile();
                String receiverTel = taskAddress.getReceiverTel();
                String networkPhone = taskAddress.getNetworkPhone();
                String networkTel = taskAddress.getNetworkTel();
                if (null != finalMobile && finalMobile.equals(receiverMobile)) {
                    taskAddress.setReceiverMobile(codeHelper.mobileDesensitization(receiverMobile));
                }
                if (null != finalTel && finalTel.equals(receiverTel)) {
                    taskAddress.setReceiverTel(codeHelper.mobileDesensitization(receiverTel));
                }
                if (null != finalMobile && finalMobile.equals(networkPhone)) {
                    taskAddress.setNetworkPhone(codeHelper.mobileDesensitization(networkPhone));
                }
                if (null != finalTel && finalTel.equals(networkTel)) {
                    taskAddress.setNetworkTel(codeHelper.mobileDesensitization(networkTel));
                }
                taskAddress.setFinalMobile(codeHelper.mobileDesensitization(finalMobile));
                taskAddress.setFinalTel(codeHelper.mobileDesensitization(finalTel));
                //有效期过期不展示虚拟号
                VirtualPhoneInfo info = new VirtualPhoneInfo();
                info.setParentOrderNo(t.getParentOrderNo());
                VirtualPhoneInfo virtualPhoneInfo = virtualPhoneInfoFeign.select(info).getData();
                if (virtualPhoneInfo == null) {
                    taskAddress.setReceiverVirtualMobile(null);
                    return;
                }
                if (virtualPhoneInfo.getValidTime() == null) {
                    taskAddress.setReceiverVirtualMobile(null);
                    return;
                }
                boolean valid = virtualPhoneInfo.getValidTime().getTime() - System.currentTimeMillis() >= 0;
                if (!valid) {
                    taskAddress.setReceiverVirtualMobile(null);
                }
            }

            if (StringUtils.isNotBlank(taskAddress.getSenderVirtualMobile())) {
                String finalMobile = taskAddress.getFinalMobile();
                String finalTel = taskAddress.getFinalTel();
                String senderMobile = taskAddress.getSenderMobile();
                String senderTel = taskAddress.getSenderTel();
                String networkPhone = taskAddress.getNetworkPhone();
                String networkTel = taskAddress.getNetworkTel();
                if (null != finalMobile && finalMobile.equals(senderMobile)) {
                    taskAddress.setSenderMobile(codeHelper.mobileDesensitization(senderMobile));
                }
                if (null != finalTel && finalTel.equals(senderTel)) {
                    taskAddress.setSenderTel(codeHelper.mobileDesensitization(senderTel));
                }
                taskAddress.setFinalMobile(codeHelper.mobileDesensitization(finalMobile));
                taskAddress.setFinalTel(codeHelper.mobileDesensitization(finalTel));
                //有效期过期不展示虚拟号
                VirtualPhoneInfo info = new VirtualPhoneInfo();
                info.setParentOrderNo(t.getParentOrderNo());
                VirtualPhoneInfo virtualPhoneInfo = virtualPhoneInfoFeign.select(info).getData();
                if (virtualPhoneInfo == null) {
                    taskAddress.setSenderVirtualMobile(null);
                    return;
                }
                if (virtualPhoneInfo.getValidTime() == null) {
                    taskAddress.setSenderVirtualMobile(null);
                    return;
                }
                boolean valid = virtualPhoneInfo.getValidTime().getTime() - System.currentTimeMillis() >= 0;
                if (!valid) {
                    taskAddress.setSenderVirtualMobile(null);
                }
            }
        } else {
            //不符合的置空虚拟号
            taskAddress.setReceiverVirtualMobile(null);
            taskAddress.setSenderVirtualMobile(null);
        }
    }

    @Override
    public List<TaskDetailsResponse> getTasksAndAddrByOrderNos(List<String> orderNos) {
        if (null == orderNos || orderNos.size() == 0) {
            logger.warn("getTasksAndAddrByOrderNos,but orderNoes is null ...");
        }
        List<TaskDetailsResponse> tasksAndAddrByOrderNos = taskCustomMapper.getTasksAndAddrByOrderNos(orderNos);
        tasksAndAddrByOrderNos.stream().forEach(t -> setName(t));
        return tasksAndAddrByOrderNos;
    }

    @Override
    public Task queryByTaskLast(Task taskQr) {
        return taskCustomMapper.queryByTaskLast(taskQr);
    }

    @Override
    public Task queryByTaskLastNow(Task taskQr) {
        return taskCustomMapper.queryByTaskLastNow(taskQr);
    }

    @Override
    public List<Task> searchTaskListByTaskNos(List<String> taskNos) {
        return taskCustomMapper.searchTaskListByTaskNos(taskNos);
    }

    private TaskItemDTO generateTaskItemDTO(TaskItem it) {
        TaskItemDTO taskItemDTO = new TaskItemDTO();
        BeanUtils.copyProperties(it, taskItemDTO);
        taskItemDTO.setItemStatusName(dictHelper.getItemStatusName(taskItemDTO.getItemStatus()));
        taskItemDTO.setItemStatusToName(dictHelper.getItemStatusName(taskItemDTO.getItemStatusTo()));
        return taskItemDTO;
    }


    @Override
    public List<Task> searchTaskListDoRdo(SearchTaskRequest searchTaskRequest) {
        return taskCustomMapper.searchTaskListDoRdo(searchTaskRequest);
    }

    @Override
    public List<TaskZsjx> searchTaskListByDate(Task task) {
        return taskMapper.searchTaskListByDate(task);
    }

    @Override
    public PageResponse<SearchTaskResponse> searchTaskOutList(SearchTaskRequest searchTaskRequest) {

        PageResponse<SearchTaskResponse> pagingData = new PageResponse<>();

        Integer queryCount = 10000;
        //        if (null != queryCount && queryCount <= 0) {
        //            logger.info("searchTaskList page , but count {} == 0 ...", queryCount);
        //            return pagingData;
        //        }
        List<SearchTaskResponse> taskResponseList = Lists.newArrayList();
        List<TaskDetailsResponse> tasks = taskCustomMapper.searchTaskOutList(searchTaskRequest);
        tasks.stream().forEach(t -> {
            SearchTaskResponse searchTaskResponse = new SearchTaskResponse();
            //处理一下基础数据和数据字典
            setName(t);
            BeanUtils.copyProperties(t, searchTaskResponse);
            taskResponseList.add(searchTaskResponse);
        });
        pagingData.init(searchTaskRequest.getPageNo(), searchTaskRequest.getPageSize(), queryCount, taskResponseList);
        return pagingData;


    }


    @Override
    public PageResponse<OrderResponse> listTask(OrderRequest orderRequest) {
        PageResponse<OrderResponse> pagingData = new PageResponse<>();

        List<OrderResponse> orderList = Lists.newArrayList();
        List<TaskList> taskLists = taskCustomMapper.listTaskByPhoneNo(orderRequest);
        Map<String, List<TaskList>> orderMap =
            taskLists.stream().collect(Collectors.groupingBy(TaskList::getCustomerOrderNo));
        orderMap.forEach((k, v) -> {
            OrderResponse orderResponse = new OrderResponse();
            TaskList taskList = v.get(0);
            v.forEach(t -> {
                TaskItem taskItem = new TaskItem();
                taskItem.setTaskNo(t.getTaskNo());
                taskItem.setPageSize(CommonConstant.LIST_PAGE_SIZE);
                List<TaskItem> itemList = taskItemMapper.selectByIndex(taskItem);
                List<TaskItems> taskItemsList = Lists.newArrayList();
                itemList.stream().forEach(it -> {
                    TaskItems taskItems = new TaskItems();
                    BeanUtils.copyProperties(it, taskItems);
                    taskItemsList.add(taskItems);
                });
                t.setTaskItems(taskItemsList);
            });
            List<TaskList> lists = v.stream().sorted(Comparator.comparing(TaskList::getOrderStatus).reversed())
                .collect(Collectors.toList());
            orderResponse.setTaskList(lists);
            orderResponse.setCustomerOrderNo(k);
            orderResponse.setCustomerCode(taskList.getCustomerCode());
            orderResponse.setCustomerName(taskList.getCustomerName());
            orderResponse.setCreateTime(taskList.getCreateTime());
            orderList.add(orderResponse);
        });
        List<OrderResponse> collect =
            orderList.stream().sorted(Comparator.comparing(OrderResponse::getCreateTime).reversed())
                .collect(Collectors.toList());

        pagingData.init(orderRequest.getPageNo(), orderRequest.getPageSize(), CommonConstant.LIST_PAGE_SIZE, collect);
        return pagingData;
    }

    @Override
    public Integer updateTaskCreateTime(List<OrderChangedReceiveDateRequest> orderChangedReceiveDateRequests) {
        if (CollectionUtils.isEmpty(orderChangedReceiveDateRequests)) {
            return 0;
        }
        return taskMapper.updateTaskCreateTime(orderChangedReceiveDateRequests);
    }

    @Override
    public Integer batchUpdateByParentOrderNo(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return 0;
        }
        return taskMapper.batchUpdateByParentOrderNo(tasks);
    }

    @Override
    public List<String> selectCancelTaskByUpdateTime(Task task) {
        List<String> taskNos = new ArrayList<>();
        if (task == null) {
            logger.warn("select canceltask by index, but task is null ...");
            return taskNos;
        }

        taskNos = taskMapper.selectCancelTaskByUpdateTime(task);

        return taskNos;
    }


    @Override
    public PageResponse<Task> searchCarArrivedTask(TaskExt task) {
        if (null == task) {
            throw BusinessException.fail("查询到车登记任务失败，传参不能为空");
        }
        Date startTime = task.getStartTime();
        Date endTime = task.getEndTime();
        if (StringUtils.isBlank(task.getWhCode())) {
            throw BusinessException.fail("查询到车登记任务失败，仓库不能为空");
        }
        if (StringUtils.isBlank(task.getCustomerOrderNo()) &&
            (null == startTime || null == endTime || BeanUtil.diffDay(task.getStartTime(), task.getEndTime(), 7))) {
            throw BusinessException.fail("最多支持查询7天内的到车登记任务");
        }
        PageResponse<Task> pagingData = new PageResponse<>();
        Integer queryCount = taskMapper.selectCarArrivedTaskByIndexCount(task);
        if (null != queryCount && queryCount <= 0) {
            logger.info("select carArrivedTask page , but count {} == 0 ...", queryCount);
            return pagingData;
        }

        List<Task> tasks = taskMapper.selectCarArrivedTaskByIndex(task);
        pagingData.init(task.getPageNo(), task.getPageSize(), queryCount, tasks);
        return pagingData;

    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateTaskByIndex(Task task) {
        if (null == task || null == task.getTaskNo()) {
            logger.warn("update task by index, but task is null  or taskNo is null...");
            return 0;
        }
        return taskMapper.updateByIndex(task);
    }

    @Override
    public Integer updateTaskCreateTimeAndFlag(List<OrderChangedReceiveDateRequest> orderChangedReceiveDateRequests) {
        if (CollectionUtils.isEmpty(orderChangedReceiveDateRequests)) {
            return 0;
        }
        return taskMapper.updateTaskCreateTimeAndFlag(orderChangedReceiveDateRequests);
    }

    @Override
    public List<TaskItem> listItemBytaskNo(List<String> taskNoList) {

        return taskCustomMapper.listItemBytaskNo(taskNoList);
    }

    @Override
    public List<TaskAddress> listAddrBytaskNo(List<String> taskNoList) {

        return taskCustomMapper.listAddrBytaskNo(taskNoList);
    }

    @Override
    public List<String> listDeliveredTaskNo(JobRequest jobRequest) {

        return taskCustomMapper.listDeliveredTaskNo(jobRequest);
    }

    @Override
    public List<Task> queryByTaskNos(List<String> taskNos) {
        if (CollectionUtils.isEmpty(taskNos)) {
            logger.warn("select task by taskNos, but taskNos is null");
            return null;
        }
        return taskMapper.queryByTaskNos(taskNos);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public Integer deleteBatchById(List<Long> ids) {
        return taskMapper.deleteBatchById(ids);
    }

    @Override
    public List<Task> selectTaskByIndexIncludeDel(Task task) {
        List<Task> tasks = new ArrayList<>();
        if (task == null) {
            logger.warn("select task by index, but task is null ...");
            return tasks;
        }
        tasks = taskMapper.selectAllTaskIncludeDel(task);
        return tasks;
    }
    
    @Override
    public Task selectTaskByTaskNoIncludeDel(Task task) {

        if (ToolUtils.isEmpty(task) || ToolUtils.isEmpty(task.getTaskNo())) {
            return null;
        }
        return taskMapper.selectTaskByTaskNoIncludeDel(task);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateChangeWarehouseTask(ChangeWarehouseTaskRequest changeWarehouseTaskRequest) {
        if (changeWarehouseTaskRequest == null) {
            throw BusinessException.fail("changeWarehouseTaskRequest is null");
        }
        Task preTask = changeWarehouseTaskRequest.getPreTask();
        Task delTask = changeWarehouseTaskRequest.getDelTask();
        TaskAddress preTaskAddr = changeWarehouseTaskRequest.getPreTaskAddr();
        if (null == preTask || null == preTask.getId() || null == delTask || null == delTask.getId() ||
            null == preTaskAddr || null == preTaskAddr.getTaskNo()) {
            throw BusinessException.fail("The input task is null or id is null");
        }
        Integer integer1 = taskMapper.updateById(preTask);
        Integer integer2 = taskMapper.deleteChangeWarehouseTask(delTask);
        Integer integer3 = taskAddressExtMapper.batchUpdateByTaskNo(Lists.newArrayList(preTaskAddr));
        return integer1 + integer2 + integer3;
    }

    @Override
    public Integer searchTaskListByTaskNosCount(OrderBatchSearchRequest searchRequest) {
        if (Objects.isNull(searchRequest)) {
            logger.warn("searchRequest is null ...");
            return null;
        }
        return taskCustomMapper.searchTaskListByTaskNosCount(searchRequest);
    }

    @Override
    public List<TaskDetailResponse> searchTaskListByTaskNosIndex(OrderBatchSearchRequest searchRequest) {
        if (Objects.isNull(searchRequest)) {
            logger.warn("searchRequest is null ...");
            return null;
        }
        return taskCustomMapper.searchTaskListByTaskNosIndex(searchRequest);
    }

    @Override
    public List<TaskExt> listExtByTaskNos(SearchTaskRequest searchTaskRequest) {
        if (null == searchTaskRequest) {
            logger.warn("listExtByCustomerOrderNos , but orderInfoInMapping is null ...");
            return null;
        }
        if (CollectionUtils.isEmpty(searchTaskRequest.getBatchQueryValue())) {
            logger.warn("listExtByCustomerOrderNos , but batchQueryValue is null ...");
            return null;
        }
        // 校验查询条件site_code与数据权限
        if (CommonEnum.Y.getValue().equals(searchTaskRequest.getDataAuthFlag())) {
            List<String> authCodes = mideaAuthUserHelper.getAuthCodesCaceable(CommonConstant.SITECODE_DATA);
            if (CollectionUtils.isEmpty(authCodes)) {
                return null;
            }
            if (authCodes.contains("ALL")) {
                searchTaskRequest.setDataAuthFlag(CommonEnum.N.getValue());
            }
            String siteCode = searchTaskRequest.getSiteCode();
            if (StringUtils.isNotBlank(siteCode)) {
                if (authCodes.contains(siteCode)) {
                    searchTaskRequest.setDataAuthFlag(CommonEnum.N.getValue());
                }
                // 不存在ALL或搜索siteCode时直接返回
                if (!authCodes.contains("ALL") && !authCodes.contains(siteCode)) {
                    return null;
                }
                searchTaskRequest.setSiteCodeList(Lists.newArrayList(siteCode));
            } else {
                if (!authCodes.contains("ALL")) {
                    searchTaskRequest.setSiteCodeList(authCodes);
                }
            }
        }
        List<TaskExt> taskExts = taskCustomMapper.listByTaskNosAndAuth(searchTaskRequest);
        if (CollectionUtils.isEmpty(taskExts) && taskExts.size() == 0) {
            return null;
        }

        for (TaskExt taskExt : taskExts) {
            TaskItem taskItem = new TaskItem();
            taskItem.setTaskNo(taskExt.getTaskNo());
            taskItem.setPageSize(Integer.MAX_VALUE);
            List<TaskItem> taskItems = taskItemMapper.selectByIndex(taskItem);
            taskExt.setTaskItems(taskItems);
            TaskAddress taskAddress = new TaskAddress();
            taskAddress.setTaskNo(taskExt.getTaskNo());
            taskAddress = taskAddressMapper.selectOne(taskAddress);
            BeanUtils.copyProperties(taskAddress, taskExt, "id", "remark");
        }
        return taskExts;
    }

    @Override
    public Integer deleteTaskOfTI(Task task) {
        if (task == null) {
            return 0;
        }
        return taskMapper.deleteTaskOfTI(task);
    }
    
    @Override
    public Integer clearHoldFlag(Task task) {
        if (task == null) {
            return 0;
        }
        Long id = task.getId();
        if (ToolUtils.isEmpty(id)) {
            return 0;
        }
        return taskMapper.clearHoldFlag(task);
    }
    
    @Override
    public Integer updateTaskRelationNo(Task task) {
        if (task == null) {
            return 0;
        }
        String taskNo = task.getTaskNo();
        if (ToolUtils.isEmpty(taskNo)) {
            return 0;
        }
        return taskMapper.updateTaskRelationNo(task);
    }
    
    @Override
    public Integer clearHoldFlagAndSetInvoice(Task task) {
        if (task == null) {
            return 0;
        }
        Long id = task.getId();
        if (ToolUtils.isEmpty(id)) {
            return 0;
        }
        return taskMapper.clearHoldFlagAndSetInvoice(task);
    }


    @Override
    public Integer setInvoiceInfo(Task task) {
        if (task == null) {
            return 0;
        }
        Long id = task.getId();
        if (ToolUtils.isEmpty(id)) {
            return 0;
        }
        return taskMapper.setInvoiceInfo(task);
    }

    @Override
    public List<Task> getTasksByCustomerOrderNos(List<String> customerOrderNos) {
        if (null == customerOrderNos || customerOrderNos.size() == 0) {
            logger.warn("getTasksByCustomerOrderNos,but customerOrderNos is null ...");
            return new ArrayList<>();
        }
        return taskCustomMapper.getTasksByCustomerOrderNos(customerOrderNos);
    }

    @Override
    public Integer getCancelOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return 0;
        }
        Integer orderNoCount = taskMapper.getCancelOrderNo(orderNo);
        return null == orderNoCount ? 0 : orderNoCount;
    }

    /**
     * 批量更新任务信息及子单号
     */
    @Override
    @Transactional(readOnly = false)
    public Integer batchUpdateTaskCancelOrderNo(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("batchUpdateTaskCancelOrderNo, but tasks is null ...");
            return 0;
        }
        Map<String, List<Task>> taskMap = new HashMap();
        List<String> taskNos = new ArrayList();
        List<Task> updateTasks;
        for (Task task : tasks) {
            if (StringUtils.isBlank(task.getTaskNo()) || StringUtils.isBlank(task.getOrderNo())) {
                throw BusinessException.fail("batchUpdateTaskCancelOrderNo 任务号或订单号不能为空");
            }
            updateTasks = taskMap.get(task.getOrderNo());
            if (CollectionUtils.isEmpty(updateTasks)) {
                updateTasks = new ArrayList();
                taskMap.put(task.getOrderNo(), updateTasks);
            }
            updateTasks.add(task);
            taskNos.add(task.getTaskNo());
        }
        updateTasks = new ArrayList();
        for (String orderNo : taskMap.keySet()) {
            Integer cancelCount = taskMapper.getCancelOrderNo(orderNo);
            cancelCount = cancelCount == null ? 0 : cancelCount;
            cancelCount++;
            String newOrderNo = orderNo + "#" + cancelCount;
            List<Task> tasks1 =
                taskMap.get(orderNo).stream().peek(t -> t.setOrderNo(newOrderNo)).collect(Collectors.toList());
            updateTasks.addAll(tasks1);
        }
        Integer count = taskMapper.batchUpdateByTaskNo(updateTasks);
        if (null == count || count <= 0) {
            throw BusinessException.fail("更新任务取消单号失败:" + taskNos);
        }
        return count;
    }

    @Override
    public Integer batchUpdateByTaskNo(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("batchUpdateByTaskNo , but tasks is null ...");
            return 0;
        }
        return taskMapper.batchUpdateByTaskNo(tasks);
    }

    @Override
    public Integer updateByIdCanSetEmpty(Task task) {
        if (task == null || task.getId() == null) {
            logger.warn("updateByIdCanSetEmpty , but tasks is null ...");
            return 0;
        }


        return taskMapper.updateByIdCanSetEmpty(task);
    }

    @Override
    public Integer searchTaskCount(SearchTaskRequest searchTaskRequest) {
        if (searchTaskRequest == null) {
            logger.warn("searchTaskCount , but searchTaskRequest is null ...");
            return 0;
        }
        PremissionRequest premissionRequest = new PremissionRequest();
        BeanUtils.copyProperties(searchTaskRequest, premissionRequest);
        if (!premissionHelper.checkPermission(premissionRequest)) {
            return 0;
        }

        premissionHelper.setPermission(premissionRequest, searchTaskRequest);

        return taskCustomMapper.searchTaskListCount(searchTaskRequest);
    }

    @Override
    public List<Task> queryByOrderNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            logger.warn("select task by orderNos, but orderNos is null");
            return null;
        }
        return taskMapper.queryByOrderNos(orderNos);
    }

    @Override
    public Integer batchClearHoldFlagByTaskNo(List<String> taskNos) {
        if (CollectionUtils.isEmpty(taskNos)) {
            return 0;
        }
        return taskMapper.batchClearHoldFlagByTaskNo(taskNos);

    }
    @Override
    public Integer batchClearHoldFlagByCompleteSetNos(List<String> completeSetNos) {
        if (CollectionUtils.isEmpty(completeSetNos)) {
            return 0;
        }
        return taskMapper.batchClearHoldFlagByCompleteSetNos(completeSetNos);
    }

    @Override
    public Integer taskZsjx(String param) {

        JSONObject jsonObject = JSONObject.parseObject(param);
        Long stid =  jsonObject.getLong("startId");
        //Long.parseLong(jsonObject.get("startId"))
        if(stid!=null&&stid!=0) {
            Long startId = jsonObject.getLong("startId");
            Long endId = jsonObject.getLong("endId");
            Long per = jsonObject.getLong("idper");
            //根据id去处理数据
            return assembleId(startId,endId,per);

        }
        JSONArray jsonArrayListDate = (JSONArray) jsonObject.get("listDate");
        //如果日期不为空则优选选择日期参数
        if(!jsonArrayListDate.isEmpty()) {
            //根据日期去组装查询参数
            return assembleDate(jsonArrayListDate);
        }

        JSONArray jsonArrayListDateTime = (JSONArray) jsonObject.get("listDateTime");
        DateFormat fmt =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //组装时间参数查询数据
        int sum = 0;
        for (int i = 0; i < jsonArrayListDateTime.size(); i++) {
            JSONObject jsonObjectReturn = jsonArrayListDateTime.getJSONObject(i);
            String startDateStr = jsonObjectReturn.getString("startDateTime");
            String endDateStr = jsonObjectReturn.getString("endDateTime");
            Date startDate = null;
            Date endDate = null;
            try {
                startDate = fmt.parse(startDateStr);
                endDate = fmt.parse(endDateStr);
            }catch (ParseException e) {
                continue;
            }
            int s = queryTaskInfosAndExecute(startDate,endDate);
            if(s!=0) {
                sum = sum+s;
            }
        }

        return sum;
    }

    private Integer assembleId(Long startId, Long endId, Long per) {
        int sum =0;
        Long start = startId;
        Long end = endId;

        while (start < end) {
            Long change = start+per;
            if(change<end) {
                int s = queryTaskInfosAndExecute(start,change);
                start = change;
                if(s!=0) {
                    sum = sum+s;
                }
            }
            if(change > end) {
                int s = queryTaskInfosAndExecute(start,end);
                start = change;
                if(s!=0) {
                    sum = sum+s;
                }
            }

        }

        return sum;
    }

    //根据日期去组装查询参数
    private Integer assembleDate(JSONArray jsonArrayListDate) {
        int sum =0;
        DateFormat fmt =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < jsonArrayListDate.size(); i++) {
            JSONObject jsonObject = jsonArrayListDate.getJSONObject(i);
            String startDateStr = jsonObject.getString("startDate");
            String endDateStr = jsonObject.getString("endDate");
            //因为根据日期去查询，所以每隔两个小时进行累加查询
            Date startDate = null;
            Date endDate = null;
            try {
                startDate = fmt.parse(startDateStr);
                endDate = fmt.parse(endDateStr);
            }catch (ParseException e) {
                continue;
            }
            Calendar calendar = Calendar.getInstance();
            if(startDate!=null && endDate!=null) {
                while (startDate.before(endDate)) {
                    calendar.setTime(startDate);
                    calendar.add(Calendar.HOUR,2);
                    Date changeDate  = calendar.getTime();
                    int s = queryTaskInfosAndExecute(startDate,changeDate);
                    startDate = changeDate;
                    if(s!=0) {
                        sum = sum+s;
                    }
                }

            }

        }
        return sum;
    }


    //根据时间查询数据
    private Integer queryTaskInfosAndExecute(Long start, Long end) {

        List<TaskZsjx> taskList = taskMapper.queryByTaskIds(start,end);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskList)) {
            return 0;
        }

        //过滤数据
        taskList = taskList.stream()
                .filter(task1 -> !StringUtils.equals("ZT", task1.getDeliveryType()))
                .filter(task1 -> !StringUtils.equals("2", task1.getProfessionalCompany())
                        &&!StringUtils.equals("7", task1.getProfessionalCompany()))
                .collect(Collectors.toList());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskList)) {
            return 0;
        }
        List<String> taskNoList = taskList.stream().map(TaskZsjx::getTaskNo).collect(Collectors.toList());

        List<TaskAddress> taskAddressList = iTaskAddressService.searchTaskAddressListByTaskNos(taskNoList);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskAddressList)) {
            return 0;
        }
        //根据任务号分组
        Map<String, List<TaskAddress>> taskAddressMap = taskAddressList.stream().collect(Collectors.groupingBy(TaskAddress::getTaskNo));
        //专司数据解析
        executeTaskZSJX(taskList,taskAddressMap);

        if(!CollectionUtils.isEmpty(taskList)) {
            List<TaskZsjx> finalTaskList = taskList;
            CompletableFuture.runAsync(()->{
                //异步执行插入记录表数据
                //更新task信息
                updateTaskInfoBatch(finalTaskList);
            });
        }
        return taskNoList.size();
    }

    //根据时间查询数据
    private Integer queryTaskInfosAndExecute(Date startDate, Date endDate) {

        Task task = new Task();
        task.setStartTime(startDate);
        task.setEndTime(endDate);
        List<TaskZsjx> taskList = taskMapper.searchTaskListByDate(task);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskList)) {
            return 0;
        }
        List<String> taskNoList = taskList.stream().map(TaskZsjx::getTaskNo).collect(Collectors.toList());

        List<TaskAddress> taskAddressList = iTaskAddressService.searchTaskAddressListByTaskNos(taskNoList);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskAddressList)) {
            return 0;
        }
        //根据任务号分组
        Map<String, List<TaskAddress>> taskAddressMap = taskAddressList.stream().collect(Collectors.groupingBy(TaskAddress::getTaskNo));
        //专司数据解析
        executeTaskZSJX(taskList,taskAddressMap);

        if(!CollectionUtils.isEmpty(taskList)) {
            List<TaskZsjx> finalTaskList = taskList;
            CompletableFuture.runAsync(()->{
                //异步执行插入记录表数据
                //更新task信息
                updateTaskInfoBatch(finalTaskList);
            });
        }
        return taskNoList.size();
    }

    @Override
    public Integer taskJsjxyc(List<String> param) {

        if(CollectionUtils.isEmpty(param)) {
            return 0;
        }
        List<TaskZsjx> taskList = taskMapper.queryByTaskNosZs(param);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskList)) {
            return 0;
        }
        List<String> taskNoList = taskList.stream().map(TaskZsjx::getTaskNo).collect(Collectors.toList());
        List<TaskAddress> taskAddressList = iTaskAddressService.searchTaskAddressListByTaskNos(taskNoList);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskAddressList)) {
            return 0;
        }
        //根据任务号分组
        Map<String, List<TaskAddress>> taskAddressMap = taskAddressList.stream().collect(Collectors.groupingBy(TaskAddress::getTaskNo));
        //处理数据
        executeTaskZSJX(taskList,taskAddressMap);

        if(!CollectionUtils.isEmpty(taskList)) {
            List<TaskZsjx> finalTaskList = taskList;
            CompletableFuture.runAsync(()->{
                //异步执行插入记录表数据
                //更新task信息
                updateTaskInfoBatch(finalTaskList);
            });
        }

        return taskList.size();
    }

    @Transactional(rollbackFor = {Exception.class})
    public void updateTaskInfoBatch(List<TaskZsjx> taskList) {

        String key = String.format("%s%s%s","TMSUPDATE","&",taskList.toString());
        try {
            //获取锁
            boolean b = redisLockHelper.tryLock(key, 30L);
            if (!b) {
                throw BusinessException.fail("系统处理中,请勿重复操作");
            }
            updateBatchInfo(taskList);
        } catch (Exception e) {
            List<String> taskNoList = taskList.stream().map(TaskZsjx::getTaskNo).collect(Collectors.toList());
            log.info("专业公司刷数失败：taskNos: "+taskNoList.toString());
        }finally {
            redisLockHelper.unLock(key);
        }
    }

    private void updateBatchInfo(List<TaskZsjx> taskzsjxList) {

        List<Task> taskList = new ArrayList<>(taskzsjxList.size());
        for(TaskZsjx taskZsjx : taskzsjxList) {
            Task task1 = new Task();
            BeanUtils.copyProperties(taskZsjx,task1);
            taskList.add(task1);
        }
        // 每批次数据记录数量
        int partialLimit = 2000;
        if(partialLimit < taskList.size()) {
            //当前数据按限制条数可分为多少批次
            int part = taskList.size()/partialLimit;
            List<Task>  partList;
            for (int i = 0; i < part; i++) {
                // 截取批次长度的list
                partList = taskList.subList(0, partialLimit);
                // 分批业务逻辑处理- 打印替代
                taskMapper.batchUpdate(partList);
                // 去除已经处理的部分 (Arrays.asList()方式生成的数据不能进行此修改操作，会报错)
                partList.clear();
            }
            // 获取最后一次截取后的剩余列表数据
            if (!taskList.isEmpty()) {
                // 业务逻辑数据处理， - 打印替代
                taskMapper.batchUpdate(taskList);
            }
        } else {
            taskMapper.batchUpdate(taskList);
        }

    }

    private void executeTaskZSJX(List<TaskZsjx> taskList, Map<String, List<TaskAddress>> taskAddressMap) {
        //循环执行逻辑
        for (TaskZsjx task : taskList) {
            List<TaskAddress> taskAddressList = taskAddressMap.get(task.getTaskNo());
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(taskAddressList)) {
                continue;
            }
            //执行专司解析逻辑
            getProfessionalCompany(task,taskAddressList.get(0));

        }

    }


    /**
     * !@任务生成 - 1.1.1、设置专业公司
     * @param task
     */
    private void getProfessionalCompany(TaskZsjx task,TaskAddress taskAddress){
        //郭丽红 2024-05-16
        // 修改需求优先判断自提
        //(1)自提
        if (DeliveryType.isZT(task.getDeliveryType())){
            task.setProfessionalCompany(null);
            return;
        }
        //(2)郭丽红 2024-05-16
        //客户时效规则中专司可以配置生产物流，送装
        //生产物流不走以下逻辑:order_extend.professional_company=2;
        // orderExtend表是送装公司，取送装公司返回
        List<String> extendProfessionalCompanys = Lists.newArrayList(ProfessionalCompanyEnum.PRODUCTION_LOGISTICS.getValue(),ProfessionalCompanyEnum.SHIPPING_COMPANY.getValue());
        if (ToolUtils.isNotEmpty(task.getProfessionalCompanyOrderExtend())
                && extendProfessionalCompanys.contains(task.getProfessionalCompanyOrderExtend())){
            task.setProfessionalCompany(task.getProfessionalCompanyOrderExtend());
            return;
        }


        //(3)郭丽红 2024-05-16
        //任务的段号是第一段，根据基地标识打标零担干线
        //如果订单拓展表（customer_order_info_extend）里的订单区分标识（order_distinction_flag）
        // 是基地（BO-RW、BO-GRW、BO-FW、BO-GFW）的订单，
        // 专业公司（professional_company）统一打成“零担干线”，值：3
       /* List<CustomerOrderInfoExtend> customerOrderInfoExtends = Optional.ofNullable(customerOrderInfoExtendFeign.listByOrderNos(Lists.newArrayList(task.getParentOrderNo())).data())
                .orElseThrow(() -> BusinessException.fail("查询订单失败"));
        if (ToolUtils.isNotEmpty(customerOrderInfoExtends)){
            CustomerOrderInfoExtend extend = customerOrderInfoExtends.get(0);
            if (Lists.newArrayList(OrderDistinctionFlag.BO_RW.getKey(),
                    OrderDistinctionFlag.BO_GRW.getKey()).contains(extend.getOrderDistinctionFlag())
                    && task.getDistributionNum() != null && 1 == task.getDistributionNum()
            ){
                task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                return;
            }
        }*/

        if(StringUtils.isNotBlank(task.getOrderDistinctionFlag())) {
            if (Lists.newArrayList(OrderDistinctionFlag.BO_RW.getKey(),
                    OrderDistinctionFlag.BO_GRW.getKey()).contains(task.getOrderDistinctionFlag())
                    && task.getDistributionNum() != null && 1 == task.getDistributionNum()
            ){
                task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                return;
            }
        }


        //(4)郭丽红 2024-05-16
        //纯运输订单专司解析
        //判断客户属性
        if (OrderType.isYSOrder(task.getOrderType())){
            //获取客户信息
            EbCustomer ebCustomer = ebCustomerManager.getEbCustomerCache(task.getCustomerCode());
            //判断继续客户属性是否是纯外部客户,CHANNEL=2为外部客户
            if(ebCustomer!=null) {
                //CHANNEL=2为外部客户
                if(StringUtils.equals("2",ebCustomer.getChannel())) {
                    //判断是否有逻辑仓
                    if(StringUtils.isBlank(task.getWhCode())) {
                        task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                        return;
                    }else {

                        String tmsflag = redisLockHelper.getValue("tms"+task.getWhCode()+task.getCustomerCode()+task.getOrderType());
                        //查询TMS业务场景配置表，条件仓库+客户+订单类型+启用
                        //没有匹配结果 零担干线
                        if(StringUtils.isBlank(tmsflag)) {
                            TmsBusinessCategoryConfigReq tmsReq = new TmsBusinessCategoryConfigReq();
                            tmsReq.setWhCode(task.getWhCode());
                            tmsReq.setCustomerCode(task.getCustomerCode());
                            tmsReq.setOrderType(task.getOrderType());
                            JsonResponse<String> jsonResponse = bopServiceFeign.queryBusinessCategoryConfig(tmsReq);
                            if(BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())&&jsonResponse.getData()!=null) {
                                JSONObject jsonObjectReturn = JSONObject.parseObject(jsonResponse.getData());
                                if(BaseCodeEnum.SUCCESS.getCode().equals(jsonObjectReturn.get("code"))) {
                                    //转换接收类
                                    JSONArray jsonArray = (JSONArray) jsonObjectReturn.get("data");
                                    if(jsonArray.isEmpty()) {
                                        redisLockHelper.setLock("tms"+task.getWhCode()+task.getCustomerCode()+task.getOrderType(),2L,"tmsKong", TimeUnit.HOURS);
                                        tmsflag = "tmsKong";
                                    }else {
                                        tmsflag = "tmsREQ";
                                        redisLockHelper.setLock("tms"+task.getWhCode()+task.getCustomerCode()+task.getOrderType(),2L,"tmsREQ", TimeUnit.HOURS);
                                    }

                                }
                            }
                        }
                        //没有匹配结果
                        if(StringUtils.equals("tmsKong",tmsflag)) {
                            task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                            return;
                        }
                        //如果有匹配结果，查询仓库
                        CdWarehouse cdWarehouse = cdWarehouseManager.getCdWarehouseCache(task.getWhCode());
                        if (null != cdWarehouse) {
                            //如果是基地仓-零担干线
                            if (StringUtils.equals(WhCategoryEnum.JDI.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.ZG.getCode(),cdWarehouse.getWhCategory())) {
                                task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                                return;
                            }
                            if (StringUtils.equals(WhCategoryEnum.ZX.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.DJDS.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.STXT.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.STZX.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.LDFB.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.LDJH.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.LDPFB.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.QZ.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.STWG.getCode(),cdWarehouse.getWhCategory())
                                    || StringUtils.equals(WhCategoryEnum.XJDS.getCode(),cdWarehouse.getWhCategory())) {
                                task.setProfessionalCompany(ProfessionalCompanyEnum.OTP_COMPANY.getValue());
                                return;
                            }
                        }
                    }
                }
                //客户属性=内部/美的渠道
                //查询特殊地址
                ProfessionalCompanyConfig data = checkProfessionalCompanyConfig(taskAddress);
                if (null != data){
                    task.setProfessionalCompany(data.getProfessionalCompany());
                    return;
                }
                String senderProvinceCode = taskAddress.getSenderProvinceCode();
                String receiverProvinceCode = taskAddress.getReceiverProvinceCode();
                // 无配置则根据以下规则：省内receiver_province_code= sender_province_code，
                // 专司为城配task. professional_company=5；
                if (StringUtils.equals(senderProvinceCode,receiverProvinceCode)){
                    task.setProfessionalCompany(ProfessionalCompanyEnum.OTP_COMPANY.getValue());
                    return;
                }
                // 省际receiver_province_code ≠sender_province_code，
                // 专司为零担干线task. professional_company=3；
                task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                return;
            }
        }

        //(5)郭丽红 2024-05-16，（含仓任务不单单指出库任务，而是仓库不为空的非自提任务）
        if (StringUtils.isNotBlank(task.getWhCode())) {
            //获取客户信息
            EbCustomer ebCustomer = ebCustomerManager.getEbCustomerCache(task.getCustomerCode());
            //获取仓库信息
            CdWarehouse cdWarehouse = cdWarehouseManager.getCdWarehouseCache(task.getWhCode());
            if (null != cdWarehouse) {
                //小件电商仓
                if (StringUtils.equals(WhCategoryEnum.XJDS.getCode(),cdWarehouse.getWhCategory())) {
                    if(ebCustomer!=null) {
                        //CHANNEL=2为外部客户
                        if(StringUtils.equals("2",ebCustomer.getChannel())) {
                            task.setProfessionalCompany(ProfessionalCompanyEnum.E_COMMERCE_COMPANY.getValue());
                            return;
                        }else {
                            EsCompany esCompanyCache = esCompanyManager.getEsCompanyCache(task.getSiteCode());
                            if (null != esCompanyCache) {
                                String escoSubstr2 = ObjectUtils.defaultIfNull(esCompanyCache.getEscoSubstr2(),"99");
                                //基地物流-零担干线
                                if (StringUtils.equals("01",escoSubstr2)){
                                    task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                                    return;
                                }else {
                                    //服务平台不是基地物流电商公司
                                    task.setProfessionalCompany(ProfessionalCompanyEnum.E_COMMERCE_COMPANY.getValue());
                                    return;
                                }
                            }
                        }
                    }
                }

                //基地仓
                if (StringUtils.equals(WhCategoryEnum.JDI.getCode(),cdWarehouse.getWhCategory())) {
                    //客户属性判断
                    if(StringUtils.equals("2",ebCustomer.getChannel())) {
                        if (DeliveryType.isEXPRESS(task.getDeliveryType())){
                            task.setProfessionalCompany(ProfessionalCompanyEnum.E_COMMERCE_COMPANY.getValue());
                            return;
                        }
                        task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                        return;
                    }
                    task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                    return;
                }

                if (StringUtils.equals(WhCategoryEnum.ZX.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.DJDS.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.STXT.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.STZX.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.LDFB.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.LDJH.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.LDPFB.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.QZ.getCode(),cdWarehouse.getWhCategory())
                        || StringUtils.equals(WhCategoryEnum.STWG.getCode(),cdWarehouse.getWhCategory())) {
                    task.setProfessionalCompany(ProfessionalCompanyEnum.OTP_COMPANY.getValue());
                    return;
                }
                //VMI
                if (StringUtils.equals(WhCategoryEnum.VMI.getCode(),cdWarehouse.getWhCategory())) {
                    //根据收发货地址读取配置
                    //查询特殊地址
                    ProfessionalCompanyConfig data = checkProfessionalCompanyConfig(taskAddress);
                    if (null != data){
                        task.setProfessionalCompany(data.getProfessionalCompany());
                        return;
                    }
                    String senderProvinceCode = taskAddress.getSenderProvinceCode();
                    String receiverProvinceCode = taskAddress.getReceiverProvinceCode();
                    // 无配置则根据以下规则：省内receiver_province_code= sender_province_code，
                    // 专司为城配task. professional_company=5；
                    if (StringUtils.equals(senderProvinceCode,receiverProvinceCode)){
                        task.setProfessionalCompany(ProfessionalCompanyEnum.OTP_COMPANY.getValue());
                        return;
                    }
                    // 省际receiver_province_code ≠sender_province_code，
                    // 专司为零担干线task. professional_company=3；
                    task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                    return;
                }

                //自管仓
                if(StringUtils.equals(WhCategoryEnum.ZG.getCode(),cdWarehouse.getWhCategory())) {
                    task.setProfessionalCompany(ProfessionalCompanyEnum.LCL_TRUNK_WHOLECAR.getValue());
                    return;
                }
            }
        }

        //(6)郭丽红 2024-05-16，（以上场景都不符合为城配公司）
        task.setProfessionalCompany(ProfessionalCompanyEnum.OTP_COMPANY.getValue());

    }

    //查询特殊地址
    private ProfessionalCompanyConfig checkProfessionalCompanyConfig(TaskAddress taskAddress) {
        ProfessionalCompanyConfig professionalCompanyConfig = new ProfessionalCompanyConfig();
        BeanUtils.copyProperties(taskAddress,professionalCompanyConfig);
        return professionalCompanyConfigFeign.getProfessionalCompanyConfig(professionalCompanyConfig).data();
    }

    @Override
    public List<Task> handOverTimeSearchTasks(AiVoiceAppointDto aiVoiceAppointDto) {
        if (aiVoiceAppointDto == null || aiVoiceAppointDto.getRowNum() == null) {
            return new ArrayList<>();
        }
        return taskMapper.handOverTimeSearchTasks(aiVoiceAppointDto);
    }

    @Override
    public List<Task> holdAppointSearchTasks(AiVoiceAppointDto aiVoiceAppointDto) {
        if (aiVoiceAppointDto == null || aiVoiceAppointDto.getRowNum() == null) {
            return new ArrayList<>();
        }
        return taskMapper.holdAppointSearchTasks(aiVoiceAppointDto);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer saveTaskInfoAndDetail(TaskInfoAndDetail taskInfoAndDetail) {
        if (taskInfoAndDetail == null) {
            log.info("saveTaskInfoAndDetail taskInfoAndDetail is null");
            return 0;
        }
        Task task = taskInfoAndDetail.getTask();
        TaskAddress taskAddress = taskInfoAndDetail.getTaskAddress();
        TaskExtend taskExtend = taskInfoAndDetail.getTaskExtend();
        List<TaskItem> taskItems = taskInfoAndDetail.getTaskItems();
        boolean isNeedAllSuccess = CommonEnum.isAllOne(taskInfoAndDetail.getNeedAllSuccessFlag());
        Integer count = 0;
        if (task != null) {
            Integer i = this.saveTask(task);
            if (isNeedAllSuccess && i < 1) {
                throw BusinessException.fail("保存任务失败：" + task.getTaskNo());
            }
            count += i;
        }
        if (taskAddress != null) {
            Integer i = iTaskAddressService.saveTaskAddress(taskAddress);
            if (isNeedAllSuccess && i < 1) {
                throw BusinessException.fail("保存任务地址失败：" + taskAddress.getTaskNo());
            }
        }

        if (taskItems != null && taskItems.size() > 0) {
            Integer i = taskItemService.insertBatch(taskItems);
            if (isNeedAllSuccess && i < 1) {
                throw BusinessException.fail("保存任务明细失败：" + taskItems.get(0).getTaskNo());
            }
            count += i;
        }

        if (taskExtend != null) {
            Integer i = taskExtendService.saveTaskExtend(taskExtend);
            if (isNeedAllSuccess && i < 1) {
                throw BusinessException.fail("保存任务扩展失败：" + taskExtend.getTaskNo());
            }
            count += i;
        }
        return count;
    }

    @Override
    @Transactional
    public Integer wmsInChangeWhByTask(WmsInChangeWhRequest request) {

        Task task = new Task();
        task.setTaskNo(request.getTaskNo());
        task.setWhCode(request.getWhCode());
        task.setWhName(request.getWhName());
        task.setTargeWhCode(request.getWhCode());
        task.setDriverQueueCode(request.getDriverQueueCode());
        task.setVersion(null);
        Integer taskCount = taskMapper.updateByIndex(task);

        if (taskCount == null || taskCount <= 0) {
            throw BusinessException.fail("更新任务表失败");
        }

        TaskAddress paramAddress = new TaskAddress();
        paramAddress.setTaskNo(request.getTaskNo());
        TaskAddress taskAddress = taskAddressMapper.selectOne(paramAddress);
        if (taskAddress == null) {
            throw BusinessException.fail("查询地址信息空");
        }
        TaskAddress updateAddress = getTaskAddress(request, taskAddress);
        Integer addressCount = taskAddressMapper.updateById(updateAddress);

        if (addressCount == null || addressCount <= 0) {
            throw BusinessException.fail("更新地址表失败");
        }

        return taskCount + taskCount;
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = { Exception.class })
    public List<AllOrderInfoDto> wfrV3SaveAllTask(List<AllOrderInfoDto> list) {

        CommonConstant.check(list, "请求列表为空");

        for (AllOrderInfoDto allOrderInfoDto : list) {

            //1、保存任务
            List<Task> taskList = allOrderInfoDto.getTaskList();
            if (ToolUtils.isEmpty(taskList)) {
               continue;
            }
            String customerOrderNo = taskList.get(0).getCustomerOrderNo();
            String key = "单号：["+customerOrderNo+"]，";
            Integer save = taskMapper.insertBatch(taskList);
            if (null == save || save < 1) {
                throw BusinessException.fail(key+ "保存任务信息失败");
            }

            //2、保存任务地址
            List<TaskAddress> taskAddressList = allOrderInfoDto.getTaskAddressList();
            if (ToolUtils.isNotEmpty(taskAddressList)) {
                Integer addressSize = taskAddressMapper.insertBatch(taskAddressList);
                if (null == addressSize || addressSize < 1) {
                    throw BusinessException.fail(key+ "保存任务地址失败");
                }
            }

            //3、保存任务拓展表
            List<TaskExtend> taskExtendList = allOrderInfoDto.getTaskExtendList();
            if (ToolUtils.isNotEmpty(taskExtendList)) {
                Integer extendSize = taskExtendMapper.insertBatch(taskExtendList);
                if (null == extendSize || extendSize < 1) {
                    throw BusinessException.fail(key+ "保存任务拓展信息失败");
                }
            }

            //4、保存任务明细
            List<TaskItem> taskItemList = allOrderInfoDto.getTaskItemList();
            if (ToolUtils.isNotEmpty(taskItemList)) {
                Integer itemSize = taskItemMapper.insertBatch(taskItemList);
                if (null == itemSize || itemSize < 1) {
                    throw BusinessException.fail(key+ "保存任务明细信息失败");
                }
            }
        }

        return list;
    }


    @Override
    public List<Task> listByParentOrderNos(List<String> parentOrderNos) {
        if (null == parentOrderNos || parentOrderNos.size() == 0) {
            logger.warn("listByParentOrderNos,but parentOrderNos is null ...");
        }
        return taskCustomMapper.listByParentOrderNos(parentOrderNos);
    }


    private static TaskAddress getTaskAddress(WmsInChangeWhRequest request, TaskAddress taskAddress) {
        TaskAddress updateAddress = new TaskAddress();
        updateAddress.setId(taskAddress.getId());
        updateAddress.setVersion(taskAddress.getVersion());
        updateAddress.setReceiverProvinceCode(request.getReceiverProvinceCode());
        updateAddress.setReceiverProvinceName(request.getReceiverProvinceName());
        updateAddress.setReceiverCityCode(request.getReceiverCityCode());
        updateAddress.setReceiverCityName(request.getReceiverCityName());
        updateAddress.setReceiverDistrictCode(request.getReceiverDistrictCode());
        updateAddress.setReceiverDistrictName(request.getReceiverDistrictName());
        updateAddress.setReceiverTownCode(request.getReceiverTownCode());
        updateAddress.setReceiverTownName(request.getReceiverTownName());
        updateAddress.setReceiverDetailAddr(request.getReceiverDetailAddr());
        updateAddress.setReceiverName(request.getReceiverName());
        updateAddress.setReceiverTel(request.getReceiverTel());
        updateAddress.setReceiverMobile(request.getReceiverMobile());
        return updateAddress;
    }
}