package com.midea.logistics.otplbs.fegin.orderverify;

import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©订单中心接口组 ORDER_CENTER
 * FileName: OrderBaseInfoFeign
 * Author: ex_huyh
 * Date: 2019-6-2 21:46:18
 * Description:OrderFeign订单详情-基本信息（基本信息，发货收货信息）
 */
@FeignClient(value = "logistics-otp-orderverify-converged-service")
public interface OrderverifyFeign {

    /**
     * 王开存 订单审核
     *
     * @param orderNos
     * @return
     */
    @PutMapping(OrderConvergedRouters.ORDER_VERIFY)
    JsonResponse orderVerify(@RequestBody List<String> orderNos);


}
