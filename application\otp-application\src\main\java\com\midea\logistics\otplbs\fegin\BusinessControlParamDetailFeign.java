package com.midea.logistics.otplbs.fegin;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: BusinessControlParamDetailFeign
* Author: 陈永培
* Date: 2022-1-6 16:20:11
* Description:业务控制参数明细 feign
*/
@FeignClient(value = "logistics-otp-rule-service")
public interface BusinessControlParamDetailFeign{

    /**
    * 创建
    * @param  businessControlParamDetail
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_CREATE, method = RequestMethod.POST)
    JsonResponse<String> create(@RequestBody BusinessControlParamDetail businessControlParamDetail);

    /**
    * 查询
    * @param id
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_DETAIL, method = RequestMethod.GET)
    JsonResponse<BusinessControlParamDetail> queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * 更新
    * @param id
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody BusinessControlParamDetail businessControlParamDetail);

    /**
    * 删除
    * @param id
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    * 分页查询
    * @param
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_SEARCH, method = RequestMethod.GET)
    JsonResponse<PageResponse<BusinessControlParamDetail>> search(@SpringQueryMap BusinessControlParamDetail businessControlParamDetail);

    /**
    * 查询单个
    * @param businessControlParamDetail
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_SELECT_ONE, method = RequestMethod.GET)
    public JsonResponse<BusinessControlParamDetail> selectOne(@SpringQueryMap BusinessControlParamDetail businessControlParamDetail);

    /**
    * 查询集合
    * @param businessControlParamDetail
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_LIST, method = RequestMethod.GET)
    public JsonResponse<List<BusinessControlParamDetail>> list(@SpringQueryMap BusinessControlParamDetail businessControlParamDetail);


    /**
    * 批量新增
    *
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_INSERT_BATCH, method = RequestMethod.POST)
    public JsonResponse insertBatch(@RequestBody List<BusinessControlParamDetail> list);


    /**
    * 批量修改
    *
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_UPDATE_BATCH, method = RequestMethod.POST)
    public JsonResponse updateBatch(@RequestBody List<BusinessControlParamDetail> list);


    /**
    * 批量删除
    *
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL_DELETE_BATCH, method = RequestMethod.POST)
    public JsonResponse deleteBatch(@RequestBody List<BusinessControlParamDetail> list);


}
