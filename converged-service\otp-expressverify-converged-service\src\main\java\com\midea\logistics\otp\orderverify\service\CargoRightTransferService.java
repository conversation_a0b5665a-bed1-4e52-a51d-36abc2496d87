package com.midea.logistics.otp.orderverify.service;

import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * Author:   zhangjh
 * Date:     2019-06-25 10:50
 * Description: 货权转移
 */
public interface CargoRightTransferService {

    /**
    * @description: 商品校验
    * @param: [customerOrderInfoExt]
    * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
    * @author: 陈永培
    * @createtime: 2021/10/4 16:06
    */
    FlowListenerParam auditItemsByFlow(CustomerOrderInfoExt customerOrderInfoExt);

}