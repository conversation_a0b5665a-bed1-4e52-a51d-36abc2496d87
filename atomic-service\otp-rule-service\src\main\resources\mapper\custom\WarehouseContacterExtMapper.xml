<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.WarehouseContacterExtMapper">

    <select id="contacterList" parameterType="com.midea.logistics.otp.rule.domain.request.WarehouseContacterRequest"
            resultType="com.midea.logistics.otp.rule.domain.response.WarehouseContacterResponse">
        select
        contact as contact,
        contact_tel as contactTel,
        site_name as siteName,
        business_role_code as role
        from warehouse_contacter
        where delete_flag = 0
        and site_code = #{siteCode}
        and business_role_code =#{role}
        <if test="dataAuthFlag !=null and dataAuthFlag == 1">
            and (@{"columnCode":"site_code","tableName":"es_company","custom":"site_code","dataCode":"D20190621009"}@)
        </if>
    </select>
</mapper>