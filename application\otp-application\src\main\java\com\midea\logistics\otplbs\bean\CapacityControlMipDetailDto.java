package com.midea.logistics.otplbs.bean;
import com.midea.logistics.otp.bean.agg.CapacityControl;
import com.midea.logistics.otp.order.domain.bean.MipFile;
import java.util.List;
import lombok.Data;

/**
 * @Author: dumg
 * @Date: 2024-04-18-11:41
 * Description:
 */
@Data
public class CapacityControlMipDetailDto {
    private List<CapacityControl> dataList;
    private String applyReason;
    private String remark;
    private List<MipFile> fileList;
}
