package com.midea.logistics.imp.orderverify.rest;

import com.google.common.collect.Maps;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.*;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.domain.bean.PreposeTask;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeServerRequestDto;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

@Slf4j
@RestController
public class ZeebeOrderRest {
    @Autowired
    private  LmpSeparateWarehouseService lmpSeparateWarehouseService;
    @Autowired
    private AnalysisOfReceivingUnitService analysisOfReceivingUnitService;
    @Autowired
    private MipFlowService mipFlowService;
    @Autowired
    private IAddressResolvingService iAddressResolvingService;
    @Autowired
    private WarehouseAllocationService warehouseAllocationService;
    @Autowired
    private CustomerOrderVerifyEndService customerOrderVerifyEndService;
    @Autowired
    private ZeebeLmpService zeebeLmpService;
    @Autowired
    private GetUnifiedCharingService getUnifiedCharingService;

    @Autowired
    private PieceAnalysisService pieceAnalysisService;

    @Autowired
    private BusinessCategoryService businessCategoryService;

    @Autowired
    private ProductConfigService productConfigService;

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @Autowired
    private PreposeTaskService preposeTaskService;
    @Autowired
    private DeliveryTypeResetService deliveryTypeResetService;
    @Autowired
    private IOutAreaService iOutAreaService;
    @Autowired
    private StandardProductAnalysisService standardProductAnalysisService;

    /**
     * 触发父订单流程 zeebe
     * @param orderNo
     * @return
     */
    @GetMapping("/customerOrderVerifyZeebeTest")
    public JsonResponse customerOrderVerifyZeebe(@RequestParam("orderNo") String orderNo) {
        return zeebeLmpService.customerOrderVerifyZeebe(orderNo);
    }


    /**
     * 触发父订单流程带模板id zeebe
     * @param zeebeRequestDto
     * @return
     */
    @PostMapping("/customerOrderVerifyZeebe")
    public JsonResponse customerOrderVerifyZeebe(@RequestBody ZeebeServerRequestDto zeebeRequestDto) {
        return zeebeLmpService.customerOrderVerifyZeebe(zeebeRequestDto);
    }



    /**
     * 触发父订单流程 zeebeCancel
     * @param orderNo
     * @return
     */
    @GetMapping("/customerOrderCancelVerifyZeebeTest")
    public JsonResponse customerOrderCancelVerifyZeebe(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        return zeebeLmpService.customerOrderVerifyZeebeCancel(orderNo);
    }


    /**
     * 自动分仓
     * @return
     */
    @PostMapping(OrderConvergedRouters.SEPARATE_WAREHOUSE_AUTOMATIC)
    public JsonResponse automatic(@RequestBody CustomerOrderInfoExt entity) {
        try {
            return JsonResponse.success(lmpSeparateWarehouseService.automatic(entity));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("自动分仓失败，失败原因:" + e.getMessage());
        }
    }
    /**
     * 自动分仓根据OrderNo
     * @return
     */
    @PostMapping(OrderConvergedRouters.SEPARATE_WAREHOUSE_AUTOMATIC_BY_ORDERNO)
    public JsonResponse automaticByOrderNo(@RequestBody CustomerOrderInfoExt entity) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(entity.getOrderNo());
            val map = Maps.newHashMap();
            JsonResponse automatic = automatic(customerOrderInfoExt);
            map.put("customerOrderInfo",null);
            automatic.setData(map);
            return automatic;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("自动分仓失败，失败原因:" + e.getMessage());
        }
    }
    /**
     * 件型解析
     * @param entity
     * @return
     */
    @PostMapping("/lmp/pieceAnalysis")
    public JsonResponse pieceAnalysis(@RequestBody CustomerOrderInfoExt entity) {
        try {
            return JsonResponse.success(pieceAnalysisService.pieceAnalysisExec(entity));
        } catch (Exception e) {
            throw BusinessException.fail("件型解析失败，失败原因:" + e.getMessage());
        }
    }
    /**
     * 件型解析by orderNo
     * @param
     * @return
     */
    @PostMapping("/lmp/pieceAnalysis/byOrderNo")
    public JsonResponse pieceAnalysisByOrderNo(@RequestBody CustomerOrderInfoExt entity) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(entity.getOrderNo());
            customerOrderInfoExt = pieceAnalysisService.pieceAnalysisExec(customerOrderInfoExt);
            return JsonResponse.success(customerOrderInfoExt.getOrderNo());
        } catch (Exception e) {
            throw BusinessException.fail("件型解析失败，失败原因:" + e.getMessage());
        }
    }
    /**
     * 业务大类解析
     */
    @PostMapping("/lmp/businessCategory")
    public JsonResponse businessCategory(@RequestBody CustomerOrderInfoExt entity) {
        try {
            //2023年7月3日10:48:48 杨顺怀：停止‘业务大类’的解析
            return JsonResponse.success(entity);
//            return JsonResponse.success(businessCategoryService.businessCategory(entity));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw BusinessException.fail("业务大类解析，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/lmp/businessCategory/byOrderNo")
    public JsonResponse businessCategoryByOrderNo(@RequestBody CustomerOrderInfoExt entity) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoOnly(entity.getOrderNo());
            businessCategoryService.businessCategory(customerOrderInfoExt);
            return JsonResponse.success(new HashMap<>());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw BusinessException.fail("业务大类解析，失败原因:" + e.getMessage());
        }
    }

    /**
     * 业务大类解析
     */
    @PostMapping("/lmp/businessCategoryAndTcFlag")
    public JsonResponse businessCategoryAndTcFlag(@RequestBody CustomerOrderInfoExt entity) {
        try {
            return JsonResponse.success(getUnifiedCharingService.doWork(entity));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw BusinessException.fail("统配计费类型解析，失败原因:" + e.getMessage());
        }
        //2023年7月3日10:48:48 杨顺怀：停止‘业务大类’的解析
//        try {
//            return JsonResponse.success(businessCategoryService.businessCategory(entity));
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            throw BusinessException.fail("业务大类解析，失败原因:" + e.getMessage());
//        }
    }

    /**
     * 件型解析原逻辑
     * @param entity
     * @return
     */
    @PostMapping("/lmp/pieceAnalysisOld")
    public JsonResponse pieceAnalysisOld(@RequestBody CustomerOrderInfoExt entity) {
        try {
            return JsonResponse.success(pieceAnalysisService.itemSizeAnalysis(entity));
        } catch (Exception e) {
            throw BusinessException.fail("件型解析失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/lmp/pieceAnalysisOld/byOrderNo")
    public JsonResponse pieceAnalysisOldByOrderNo(@RequestBody CustomerOrderInfoExt entity) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoOnly(entity.getOrderNo());
            pieceAnalysisService.itemSizeAnalysis(customerOrderInfoExt);
            return JsonResponse.success(null);
        } catch (Exception e) {
            throw BusinessException.fail("件型解析失败，失败原因:" + e.getMessage());
        }
    }
    /**
     * MIP流程审核
     * @param customerOrderInfoExt
     * @return
     */
    @PostMapping(OrderConvergedRouters.CONVERGED_MIP_EXECUTION_AUDIT)
    public JsonResponse mipExecutionAudit(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {
        try {
            return JsonResponse.success(mipFlowService.mipExecutionAudit(customerOrderInfoExt));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("MIP流程审核，失败原因:" + e.getMessage());
        }
    }

    @PostMapping(OrderConvergedRouters.CONVERGED_MIP_EXECUTION_AUDIT_BY_ORDERNO)
    public JsonResponse mipExecutionAuditByOrderNo(@RequestBody CustomerOrderInfoExt entity) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoOnly(entity.getOrderNo());
            mipFlowService.mipExecutionAudit(customerOrderInfoExt);
            return JsonResponse.success(null);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("MIP流程审核，失败原因:" + e.getMessage());
        }
    }
    /**
     * 收货单位解
     * @param entity
     * @return
     */
    @PostMapping("/explain/receive/platform/customer")
    public JsonResponse explainReceivePlatformAndCustomer(@RequestBody CustomerOrderInfoExt entity) {
        try {
            return JsonResponse.success(analysisOfReceivingUnitService.explainReceivePlatformAndCustomer(entity));
        } catch (Exception e) {
            throw BusinessException.fail("收货单位解析失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/explain/receive/platform/customer/byOrderNo")
    public JsonResponse explainReceivePlatformAndCustomerByOrderNo(@RequestBody CustomerOrderInfoExt entity) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(entity.getOrderNo());
            customerOrderInfoExt=analysisOfReceivingUnitService.explainReceivePlatformAndCustomer(customerOrderInfoExt);
            return JsonResponse.success(lmpOrderFlowHelper.getZeebeResponseParam(customerOrderInfoExt));
        } catch (Exception e) {
            throw BusinessException.fail("收货单位解析失败，失败原因:" + e.getMessage());
        }
    }

    /**
     * @description: 地址解析
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 9:45
     */
    @PostMapping("/address")
    public JsonResponse address(@RequestBody CustomerOrderInfoExt customerOrderInfoExt){
        try {
            return JsonResponse.success(iAddressResolvingService.queryForCountry(customerOrderInfoExt));
        } catch (Exception e) {
            throw BusinessException.fail("地址解析失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/address/byOrderNo")
    public JsonResponse addressByOrderNo(@RequestBody CustomerOrderInfoExt entity){
        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(entity.getOrderNo());
            return address(customerOrderInfoExt);
        } catch (Exception e) {
            throw BusinessException.fail("地址解析失败，失败原因:" + e.getMessage());
        }
    }

    /**
     * @description: 仓间调拨
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 9:45
     */
    @PostMapping("/allocationParse/byOrderNo")
    public JsonResponse allocationParseByOrderNo(@RequestBody CustomerOrderInfoExt entity){

        try {
            CustomerOrderInfoExt customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(entity.getOrderNo());
            allocationParse(customerOrderInfoExt);
            return JsonResponse.success(null);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("仓间调拨失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/allocationParse")
    public JsonResponse allocationParse(@RequestBody CustomerOrderInfoExt customerOrderInfoExt){

        try {
            return JsonResponse.success(warehouseAllocationService.allocationParse(customerOrderInfoExt));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw BusinessException.fail("仓间调拨失败，失败原因:" + e.getMessage());
        }
    }


    /**
     * 父单流程审核结束触发子流程
     * @param orderNo
     * @return
     */
    @PostMapping("/customerOrderVerifyEnd")
    public JsonResponse customerOrderVerifyEnd(@RequestBody String orderNo) {
        try {
            CustomerOrderInfoExt customerOrderInfoExt = new CustomerOrderInfoExt();
            customerOrderInfoExt.setOrderNo(orderNo);
            return JsonResponse.success(customerOrderVerifyEndService.customerOrderVerifyEnd(customerOrderInfoExt));
        } catch (Exception e) {
            throw BusinessException.fail("父单流程审核结束触发子流程，失败原因:" + e.getMessage());
        }
    }

    /**
     * 服务产品规则解析
     * @param customerOrderInfoExt
     * @return
     */
    @PostMapping("/lmp/productConfig")
    public JsonResponse checkProductConfig(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {
        return JsonResponse.success(productConfigService.checkProductConfig(customerOrderInfoExt));
    }

    @PostMapping("/lmp/productConfig/byOrderNo")
    public JsonResponse checkProductConfigByOrderNo(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {
        CustomerOrderInfoExt customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfoExt(customerOrderInfoExt.getOrderNo());
        productConfigService.checkProductConfig(customerOrderInfo);
        return JsonResponse.success(null);
    }


    /**
     * 分仓触发子单流程 zeebe
     * @param orderNo
     * @return
     */
    @GetMapping("/zeebeSeparateWarehouse")
    public JsonResponse zeebeSeparateWarehouse(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        return zeebeLmpService.zeebeSeparateWarehouse(orderNo);
    }


    /**
     * 取消父单流程 zeebe
     * @param key
     * @return
     */
    @GetMapping("/zeebeCustomerOrderCancel")
    public JsonResponse zeebeCustomerOrderCancel(@RequestParam("key") String key, HttpServletRequest request) {
        return zeebeLmpService.zeebeCustomerOrderCancel(key);
    }



    /**
     * 重试订单流程 zeebe
     * @param orderNo
     * @return
     */
    @GetMapping("/zeebeRetry")
    public JsonResponse zeebeRetry(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        return zeebeLmpService.zeebeRetry(orderNo);
    }


    /**
     * 取消zeebe实例
     * @param orderNo
     * @return
     */
    @GetMapping("/zeebeCancelInstance")
    JsonResponse zeebeCancelInstance(@RequestParam("orderNo") String orderNo) {
        try {
            return JsonResponse.success(zeebeLmpService.zeebeCancelInstance(orderNo));
        } catch (Exception e) {
            throw BusinessException.fail("取消zeebe实例失败，失败原因:" + e.getMessage());
        }
    }


    /**
     * 重置流程参数
     * @param orderNo
     * @return
     */
    @GetMapping("/resetZeebeParamter")
    JsonResponse resetZeebeParamter(@RequestParam("orderNo") String orderNo) {
        try {
            return JsonResponse.success(zeebeLmpService.resetZeebeParamter(orderNo));
        } catch (Exception e) {
            throw BusinessException.fail("取消zeebe实例失败，失败原因:" + e.getMessage());
        }
    }


    /**
     * !@大物流切换 - 后分仓-父单流程节点生成前置任务
     * @param entity
     * @return
     */
    @PostMapping(OrderConvergedRouters.PREPOSE_TASK_GENERATE)
    public JsonResponse generatePreposeTask(@RequestBody CustomerOrderInfoExt entity) {
        try {
            log.info("前端创建前置仓创建流程:"+entity.getOrderNo());
            return JsonResponse.success(preposeTaskService.generatePreposeTask(entity));
        } catch (Exception e) {
            throw BusinessException.fail("生成前置任务失败，失败原因:" + e.getMessage());
        }
    }


    /**
     * 父单配送方式设置
     * @param entity
     * @return
     */
    @PostMapping("/lmp/customerDeliveryTypeReset")
    public JsonResponse customerDeliveryTypeReset(@RequestBody CustomerOrderInfoExt entity){
        try {
            return JsonResponse.success(deliveryTypeResetService.resetDeliveryType(entity));
        } catch (Exception e) {
            throw BusinessException.fail("重置配送方式失败，失败原因:" + e.getMessage());
        }
    }

    /**
     * @description: 检验爆仓
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 刘院民
     * @createtime: 2023/08/14 17:44
     */
    @PostMapping("/isOutArea/byCustomerOrderInfoExt")
    public JsonResponse isOutArea(@RequestBody CustomerOrderInfoExt entity){
        try {
            return JsonResponse.success(iOutAreaService.isOutArea(entity));
        }catch (Exception e){
            throw BusinessException.fail("爆仓失败，失败原因:"+e.getMessage());
        }
    }

    /**
     * 标准产品解析
     * @return
     */
    @PostMapping("/standardProductAnalysis")
    public JsonResponse standardProductAnalysis(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {
        try {
            return JsonResponse.success(standardProductAnalysisService.standardProductAnalysis(customerOrderInfoExt));
        } catch (Exception e) {
            throw BusinessException.fail("标准产品解析失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/standardProductAnalysisByOrderNo")
    public JsonResponse standardProductAnalysisByOrderNo(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {
        CustomerOrderInfoExt customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfoExt(customerOrderInfoExt.getOrderNo());
        return JsonResponse.success(standardProductAnalysisService.standardProductAnalysis(customerOrderInfo));
    }

}
