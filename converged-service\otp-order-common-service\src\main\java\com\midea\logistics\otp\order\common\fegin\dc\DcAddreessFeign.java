package com.midea.logistics.otp.order.common.fegin.dc;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import com.midea.logistics.lcp.domain.dc.entity.AddressMapping;
import com.midea.logistics.lcp.domain.dc.entity.Place;
import com.midea.logistics.lcp.domain.order.bean.LocationBean;
import com.midea.logistics.lcp.domain.order.bean.PlaceBean;
import com.mideaframework.core.web.JsonResponse;

@FeignClient(value = "logistics-lcp-dc-atomic")
public interface DcAddreessFeign {

    @GetMapping("/place/list")
    JsonResponse<List<PlaceBean>> places(@SpringQueryMap PlaceBean entity);

    @GetMapping("/place/list/cache")
    JsonResponse<List<PlaceBean>> placeListFromEsCached(@SpringQueryMap PlaceBean entity);

    @GetMapping("/location/list")
    JsonResponse<List<LocationBean>> locationBean(@SpringQueryMap LocationBean locationBean);

    @GetMapping(value = "/address/mapping/list",headers = {"tenantCode=annto"})
    JsonResponse<List<AddressMapping>> addressMappingList(@SpringQueryMap AddressMapping entity);

    @GetMapping("/place/list/es")
    JsonResponse<List<Place>> placeListFromEs(@SpringQueryMap Place entity);


}
