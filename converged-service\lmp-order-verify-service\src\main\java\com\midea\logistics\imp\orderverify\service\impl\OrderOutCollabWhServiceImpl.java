package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.OrderOutCollabWhService;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.enums.ExcuteStatus;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.DeliveryTypeHelper;
import com.midea.logistics.otp.order.common.helper.WithDrawRedisLockHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrderOutCollabWhServiceImpl implements OrderOutCollabWhService {

    @Autowired
    private DeliveryTypeHelper deliveryTypeHelper;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private WithDrawRedisLockHelper withDrawRedisLockHelper;
    @Autowired
    private BusinessHelper businessHelper;

    /**
     * 设置大小电标识
     *
     * @param
     */
    @Override
    @ZeebeFlow("OUTSOURCE_FLAG_ANALYSIS")
    public OrderInfoExt setOrderOutCollabWh(OrderInfoExt orderInfoExt) {
    
        /**
         * 模拟卡流程（不上生产）
         */
        businessHelper.throwFail(orderInfoExt);
        
        String orderNo = orderInfoExt.getOrderNo();
        if (StringUtils.isEmpty(orderNo)) {
            throw BusinessException.fail("设置大小电标示，订单号为空！" + orderNo);
        }
        OrderInfo orderInfo = orderInfoExt;
        String parentOrderNo = orderInfo.getParentOrderNo();
        CustomerOrderInfo customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfo(parentOrderNo);

        // 检查取消分仓
        boolean cancelWareHouseLock = withDrawRedisLockHelper.checkCancelWareHouseLock(orderNo);
        if (cancelWareHouseLock) {
            throw BusinessException.fail("已经取消分仓，取消子单审核");
        }

        // 父单已经完成时，不执行运作
        if(customerOrderInfo.getExcuteStatus().compareTo(ExcuteStatus.CLOSED.getKey()) >= 0 ){
            throw BusinessException.fail("父单已经完结，取消子单审核");
        }

        if (orderInfo.getOutsourceFlag() != null) {
            String flag = orderInfo.getOutsourceFlag() == 1 ? "小电" : "大电";
            flag = updateOrderOutCollabWh(orderInfo, customerOrderInfo, orderInfo.getOutsourceFlag(), flag, orderInfoExt);
            FlowListenerParam.success(flag);
            return orderInfoExt;
        }
        if (StringUtils.isNotEmpty(orderInfo.getOrderType())) {
            if (OrderType.YS.getKey().equals(orderInfo.getOrderType())) {
                // 纯运输 不影响
                FlowListenerParam.success("纯运输订单无需判断大小电");
                return orderInfoExt;
            } else if (OrderType.ZF.getKey().equals(orderInfo.getOrderType())) {
                FlowListenerParam.success("直发订单无需判断大小电"); // 丽红：直发订单和纯运输订单一样
                return orderInfoExt;
            }
        }

        Integer outsourceFlag = deliveryTypeHelper.getOutCollabWh(orderInfo.getWhCode());

        String flag = outsourceFlag == 1 ? "小电" : "大电";
        flag = updateOrderOutCollabWh(orderInfo, customerOrderInfo, outsourceFlag, flag, orderInfoExt);
        orderInfoExt.setOutsourceFlag(outsourceFlag);


        if (customerOrderInfo.getOutsourceFlag() != null) {
            FlowListenerParam.success(flag);
            return orderInfoExt;
        }

        customerOrderInfo.setOutsourceFlag(outsourceFlag);
        CustomerOrderInfo updateCust = new CustomerOrderInfo();
        updateCust.setOutsourceFlag(outsourceFlag);
        updateCust.setId(customerOrderInfo.getId());
        updateCust.setOrderNo(customerOrderInfo.getOrderNo());
        updateCust.setVersion(customerOrderInfo.getVersion());
        lmpOrderFlowHelper.updateCustomerOrderInfo(updateCust, "父订单更新大小电标示");
        FlowListenerParam.success(flag);
        return orderInfoExt;

    }

    private String updateOrderOutCollabWh(OrderInfo orderInfo, CustomerOrderInfo customerOrderInfo, Integer outsourceFlag, String flag, OrderInfoExt orderInfoExt) {
        orderInfo.setOutsourceFlag(outsourceFlag);
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOutsourceFlag(outsourceFlag);
        updateOrder.setId(orderInfo.getId());
        updateOrder.setVersion(orderInfo.getVersion());
        updateOrder.setOrderNo(orderInfo.getOrderNo());
        //小电订单 + vip 修改为顺丰派送
        boolean vipSFFlag = outsourceFlag == 1 && customerOrderInfo.getVipFlag() == 1 && OrderType.PO.getKey().equals(customerOrderInfo.getOrderType());
        if (vipSFFlag) {
            updateOrder.setCarrierCode("SF");
            orderInfoExt.setCarrierCode("SF");
            flag += ",VIP小电订单统一顺丰快递";
        }
        lmpOrderFlowHelper.updateOrderInfo(updateOrder, "子订单更新大小电标示");
        return flag;
    }
}
