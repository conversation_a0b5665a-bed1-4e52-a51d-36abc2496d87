<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.AssembleRuleConfigMapper">

    <sql id="searchFieldsSql">
            `assemble_rule` AS  assembleRule,
            `order_type` AS  orderType,
            `site_code` AS  siteCode,
            `assemble_volume_end` AS  assembleVolumeEnd,
            `target_site_same` AS  targetSiteSame,
            `source_system` AS  sourceSystem,
            `create_user_code` AS  createUserCode,
            `customer_code` AS  customerCode,
            `site_name` AS  siteName,
            `update_time` AS  updateTime,
            `remark` AS  remark,
            `address_control` AS  addressControl,
            `version` AS  version,
            `customer_name` AS  customerName,
            `delete_flag` AS  deleteFlag,
            `update_user_code` AS  updateUserCode,
            `wh_name` AS  whName,
            `wh_code` AS  whCode,
            `create_time` AS  createTime,
            `assemble_volume_start` AS  assembleVolumeStart,
            `transport_product` AS  transportProduct,
            `is_auto` AS isAuto,
            `id` AS  id,
            `enable_flag` AS  enableFlag
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="assembleRule !=null and assembleRule != ''">
                        and `assemble_rule` =#{assembleRule}
                    </if>
                    <if test="orderType !=null and orderType != ''">
                        and `order_type` like concat('%',#{orderType},'%')
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        and `site_code` like concat('%',#{siteCode},'%')
                    </if>
                    <if test="assembleVolumeEnd !=null">
                        and `assemble_volume_end` =#{assembleVolumeEnd}
                    </if>
                    <if test="targetSiteSame !=null">
                        and `target_site_same` =#{targetSiteSame}
                    </if>
                    <if test="sourceSystem !=null and sourceSystem != ''">
                        and `source_system` like concat('%',#{sourceSystem},'%')
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        and `customer_code` like concat('%', #{customerCode},'%')
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        and `site_name` =#{siteName}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="remark !=null">
                        and `remark` =#{remark}
                    </if>
                    <if test="addressControl !=null">
                        and `address_control` =#{addressControl}
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        and `customer_name` =#{customerName}
                    </if>
                    <if test="whName !=null and whName != ''">
                        and `wh_name` =#{whName}
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        and `wh_code` like concat('%', #{whCode} ,'%')
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="assembleVolumeStart !=null">
                        and `assemble_volume_start` =#{assembleVolumeStart}
                    </if>
                    <if test="transportProduct !=null and transportProduct != ''">
                        and `transport_product` =#{transportProduct}
                    </if>
                    <if test="isAuto !=null">
                        and `is_auto` =#{isAuto}
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="enableFlag !=null">
                        and `enable_flag` =#{enableFlag}
                    </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
                        <if test="assembleRule !=null and assembleRule != ''">
                            `assemble_rule` = #{assembleRule},
                        </if>
                        <if test="orderType !=null and orderType != ''">
                            `order_type` = #{orderType},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="siteCode !=null and siteCode != ''">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="assembleVolumeEnd != null">
                            `assemble_volume_end` = #{assembleVolumeEnd},
                        </if>
                        <if test="assembleVolumeStart != null">
                            `assemble_volume_start` = #{assembleVolumeStart},
                        </if>
                        <if test="targetSiteSame != null">
                            `target_site_same` = #{targetSiteSame},
                        </if>
                        <if test="sourceSystem !=null and sourceSystem != ''">
                            `source_system` = #{sourceSystem},
                        </if>
                        <if test="customerCode !=null and customerCode != ''">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="siteName !=null and siteName != ''">
                            `site_name` = #{siteName},
                        </if>
                        <if test="transportProduct !=null and transportProduct != ''">
                            `transport_product` = #{transportProduct},
                        </if>
                        <if test="isAuto != null">
                            `is_auto` = #{isAuto},
                        </if>
                        <if test="addressControl != null">
                            `address_control` = #{addressControl},
                        </if>
                        <if test="whCode !=null and whCode != ''">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="whName !=null and whName != ''">
                            `wh_name` = #{whName},
                        </if>
                        <if test="enableFlag != null">
                            `enable_flag` = #{enableFlag},
                        </if>
                        <if test="customerName !=null and customerName != ''">
                            `customer_name` = #{customerName},
                        </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="assembleRule !=null">
                            `assemble_rule` = #{assembleRule},
                        </if>
                        <if test="orderType !=null">
                            `order_type` = #{orderType},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="siteCode !=null">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="assembleVolumeEnd != null">
                            `assemble_volume_end` = #{assembleVolumeEnd},
                        </if>
                        <if test="assembleVolumeStart != null">
                            `assemble_volume_start` = #{assembleVolumeStart},
                        </if>
                        <if test="targetSiteSame != null">
                            `target_site_same` = #{targetSiteSame},
                        </if>
                        <if test="sourceSystem !=null">
                            `source_system` = #{sourceSystem},
                        </if>
                        <if test="customerCode !=null">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="siteName !=null">
                            `site_name` = #{siteName},
                        </if>
                        <if test="transportProduct !=null">
                            `transport_product` = #{transportProduct},
                        </if>
                        <if test="isAuto != null">
                            `is_auto` = #{isAuto},
                        </if>
                        <if test="addressControl != null">
                            `address_control` = #{addressControl},
                        </if>
                        <if test="whCode !=null">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="whName !=null">
                            `wh_name` = #{whName},
                        </if>
                        <if test="enableFlag != null">
                            `enable_flag` = #{enableFlag},
                        </if>
                        <if test="customerName !=null">
                            `customer_name` = #{customerName},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from assemble_rule_config t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.AssembleRuleConfig">
        select
        <include refid="searchFieldsSql"/>
        from assemble_rule_config t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">${orderByType}</if>
        </if>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.AssembleRuleConfig">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from assemble_rule_config t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.AssembleRuleConfig">
        select
        <include refid="searchFieldsSql"/>
        from assemble_rule_config t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
${orderByType}            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        assemble_rule_config t
        <include refid="setFieldsSql"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        assemble_rule_config t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        assemble_rule_config t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.AssembleRuleConfig" useGeneratedKeys="true" keyProperty="id">
        insert into assemble_rule_config
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="assembleRule !=null and assembleRule != ''">
                        `assemble_rule`,
                    </if>

                    <if test="orderType !=null and orderType != ''">
                        `order_type`,
                    </if>

                    <if test="updateUserCode != null">
                        `update_user_code`,
                    </if>

                    <if test="siteCode !=null and siteCode != ''">
                        `site_code`,
                    </if>

                    <if test="assembleVolumeEnd != null">
                        `assemble_volume_end`,
                    </if>

                    <if test="assembleVolumeStart != null">
                        `assemble_volume_start`,
                    </if>

                    <if test="targetSiteSame != null">
                        `target_site_same`,
                    </if>

                    <if test="sourceSystem !=null and sourceSystem != ''">
                        `source_system`,
                    </if>

                    <if test="createUserCode != null">
                        `create_user_code`,
                    </if>

                    <if test="customerCode !=null and customerCode != ''">
                        `customer_code`,
                    </if>

                    <if test="siteName !=null and siteName != ''">
                        `site_name`,
                    </if>

                    <if test="transportProduct !=null and transportProduct != ''">
                        `transport_product`,
                    </if>

                    <if test="isAuto != null">
                        `is_auto`,
                    </if>

                    <if test="remark != null">
                        `remark`,
                    </if>

                    <if test="addressControl != null">
                        `address_control`,
                    </if>

                    <if test="whCode !=null and whCode != ''">
                        `wh_code`,
                    </if>

                    <if test="whName !=null and whName != ''">
                        `wh_name`,
                    </if>

                    <if test="enableFlag != null">
                        `enable_flag`,
                    </if>

                    <if test="customerName !=null and customerName != ''">
                        `customer_name`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="assembleRule !=null and assembleRule != ''">
                        #{assembleRule},
                    </if>
                    <if test="orderType !=null and orderType != ''">
                        #{orderType},
                    </if>
                    <if test="updateUserCode != null">
                        #{updateUserCode},
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        #{siteCode},
                    </if>
                    <if test="assembleVolumeEnd != null">
                        #{assembleVolumeEnd},
                    </if>
                    <if test="assembleVolumeStart != null">
                        #{assembleVolumeStart},
                    </if>
                    <if test="targetSiteSame != null">
                        #{targetSiteSame},
                    </if>
                    <if test="sourceSystem !=null and sourceSystem != ''">
                        #{sourceSystem},
                    </if>
                    <if test="createUserCode != null">
                        #{createUserCode},
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        #{customerCode},
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        #{siteName},
                    </if>
                    <if test="transportProduct !=null and transportProduct != ''">
                        #{transportProduct},
                    </if>
                    <if test="remark != null">
                        #{remark},
                    </if>
                    <if test="addressControl != null">
                        #{addressControl},
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        #{whCode},
                    </if>
                    <if test="whName !=null and whName != ''">
                        #{whName},
                    </if>
                    <if test="enableFlag != null">
                        #{enableFlag},
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        #{customerName},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                `assemble_rule`,
                `order_type`,
                `update_user_code`,
                `site_code`,
                `assemble_volume_end`,
                `assemble_volume_start`,
                `target_site_same`,
                `source_system`,
                `create_user_code`,
                `customer_code`,
                `site_name`,
                `transport_product`,
                `is_auto`,
                `remark`,
                `address_control`,
                `wh_code`,
                `wh_name`,
                `enable_flag`,
                `customer_name`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.assembleRule},
                #{item.orderType},
                #{item.updateUserCode},
                #{item.siteCode},
                #{item.assembleVolumeEnd},
                #{item.assembleVolumeStart},
                #{item.targetSiteSame},
                #{item.sourceSystem},
                #{item.createUserCode},
                #{item.customerCode},
                #{item.siteName},
                #{item.transportProduct},
                #{item.isAuto},
                #{item.remark},
                #{item.addressControl},
                #{item.whCode},
                #{item.whName},
                #{item.enableFlag},
                #{item.customerName},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        assemble_rule_config
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.assembleRule !=null and item.assembleRule != ''">
                     `assemble_rule`  = #{item.assembleRule},
                 </if>
                <if test="item.orderType !=null and item.orderType != ''">
                    `order_type`  = #{item.orderType},
                </if>
                <if test="item.orderType == ''">
                    `order_type`  = #{item.orderType},
                </if>
                 <if test="item.updateUserCode != null">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.siteCode !=null and item.siteCode != ''">
                     `site_code`  = #{item.siteCode},
                 </if>
                 <if test="item.assembleVolumeEnd != null">
                     `assemble_volume_end`  = #{item.assembleVolumeEnd},
                 </if>
                 <if test="item.assembleVolumeStart != null">
                     `assemble_volume_start`  = #{item.assembleVolumeStart},
                 </if>
                 <if test="item.targetSiteSame != null and item.targetSiteSame >=0 ">
                     `target_site_same`  = #{item.targetSiteSame},
                 </if>
                 <if test="item.targetSiteSame != null and item.targetSiteSame == -1 ">
                     `target_site_same`  = NULL,
                 </if>
                 <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                     `source_system`  = #{item.sourceSystem},
                 </if>
                <if test="item.sourceSystem == ''">
                    `source_system`  = NULL,
                </if>
                 <if test="item.customerCode !=null and item.customerCode != ''">
                     `customer_code`  = #{item.customerCode},
                 </if>
                 <if test="item.siteName !=null and item.siteName != ''">
                     `site_name`  = #{item.siteName},
                 </if>
                 <if test="item.transportProduct !=null and item.transportProduct != ''">
                     `transport_product`  = #{item.transportProduct},
                 </if>
                 <if test="item.isAuto != null">
                    `is_auto` = #{item.isAuto},
                 </if>
                 <if test="item.remark != null and item.remark !='' ">
                     `remark`  = #{item.remark},
                 </if>
                 <if test="item.remark == ''">
                    `remark`  = NULL,
                 </if>
                 <if test="item.addressControl != null and item.addressControl >=0 ">
                    `address_control`  = #{item.addressControl},
                 </if>
                 <if test="item.addressControl != null and item.addressControl == -1 ">
                    `address_control`  = NULL,
                 </if>
                 <if test="item.whCode !=null and item.whCode != ''">
                     `wh_code`  = #{item.whCode},
                 </if>
                <if test="item.whCode == ''">
                    `wh_code`  = NULL,
                </if>
                <if test="item.whName !=null and item.whName != ''">
                     `wh_name`  = #{item.whName},
                </if>
                <if test=" item.whName == ''">
                    `wh_name`  = NULL,
                </if>
                 <if test="item.enableFlag != null">
                     `enable_flag`  = #{item.enableFlag},
                 </if>
                 <if test="item.customerName !=null and item.customerName != ''">
                     `customer_name`  = #{item.customerName},
                 </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.assembleRule !=null and item.assembleRule != ''">
                        and  `assemble_rule`  =#{item.assembleRule}
                    </if>
                    <if test="item.orderType !=null and item.orderType != ''">
                        and  `order_type`  =#{item.orderType}
                    </if>
                    <if test="item.siteCode !=null and item.siteCode != ''">
                        and  `site_code`  =#{item.siteCode}
                    </if>
                    <if test="item.assembleVolumeEnd !=null">
                        and  `assemble_volume_end`  =#{item.assembleVolumeEnd}
                    </if>
                    <if test="item.targetSiteSame !=null">
                        and  `target_site_same`  =#{item.targetSiteSame}
                    </if>
                    <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                        and  `source_system`  =#{item.sourceSystem}
                    </if>
                    <if test="item.customerCode !=null and item.customerCode != ''">
                        and  `customer_code`  =#{item.customerCode}
                    </if>
                    <if test="item.siteName !=null and item.siteName != ''">
                        and  `site_name`  =#{item.siteName}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.remark !=null">
                        and  `remark`  =#{item.remark}
                    </if>
                    <if test="item.addressControl !=null">
                        and  `address_control`  =#{item.addressControl}
                    </if>
                    <if test="item.version !=null">
                        and  `version`  =#{item.version}
                    </if>
                    <if test="item.customerName !=null and item.customerName != ''">
                        and  `customer_name`  =#{item.customerName}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.whName !=null and item.whName != ''">
                        and  `wh_name`  =#{item.whName}
                    </if>
                    <if test="item.whCode !=null and item.whCode != ''">
                        and  `wh_code`  =#{item.whCode}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.assembleVolumeStart !=null">
                        and  `assemble_volume_start`  =#{item.assembleVolumeStart}
                    </if>
                    <if test="item.transportProduct !=null and item.transportProduct != ''">
                        and  `transport_product`  =#{item.transportProduct}
                    </if>
                    <if test="item.isAuto != null">
                        `is_auto` = #{item.isAuto},
                    </if>
                    <if test="item.enableFlag !=null">
                        and  `enable_flag`  =#{item.enableFlag}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE assemble_rule_config
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
#{item.id}
        </foreach>
    </update>


    <update id="deleteBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE assemble_rule_config
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>

    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE assemble_rule_config
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>
</mapper>