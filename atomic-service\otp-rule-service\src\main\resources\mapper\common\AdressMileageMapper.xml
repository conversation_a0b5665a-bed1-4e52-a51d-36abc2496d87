<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.AdressMileageMapper">

    <sql id="searchFieldsSql">
        `start_address_code` AS  startAddressCode,
        `end_address_name` AS  endAddressName,
        `create_user_code` AS  createUserCode,
        `update_time` AS  updateTime,
        `remark` AS  remark,
        `start_level` AS  startLevel,
        `version` AS  version,
        `end_level` AS  endLevel,
        `delete_flag` AS  deleteFlag,
        `update_user_code` AS  updateUserCode,
        `create_time` AS  createTime,
        `end_address_code` AS  endAddressCode,
        `id` AS  id,
        `start_address_name` AS  startAddressName,
        `mileage` AS  mileage
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="startAddressCode !=null and startAddressCode != ''">
                        and  `start_address_code`  =#{startAddressCode}
                    </if>
                    <if test="endAddressName !=null and endAddressName != ''">
                        and  `end_address_name`  =#{endAddressName}
                    </if>
                    <if test="updateTime !=null">
                        and  `update_time`  =#{updateTime}
                    </if>
                    <if test="remark !=null">
                        and  `remark`  =#{remark}
                    </if>
                    <if test="startLevel !=null and startLevel != ''">
                        and  `start_level`  =#{startLevel}
                    </if>
                    <if test="endLevel !=null and endLevel != ''">
                        and  `end_level`  =#{endLevel}
                    </if>
                    <if test="createTime !=null">
                        and  `create_time`  =#{createTime}
                    </if>
                    <if test="endAddressCode !=null and endAddressCode != ''">
                        and  `end_address_code`  =#{endAddressCode}
                    </if>
                    <if test="id !=null">
                        and  `id`  =#{id}
                    </if>
                    <if test="startAddressName !=null and startAddressName != ''">
                        and  `start_address_name`  =#{startAddressName}
                    </if>
                    <if test="mileage !=null">
                        and  `mileage`  =#{mileage}
                    </if>
    </sql>

    <sql id="setFieldsSql">
       <set>
                     `version` = `version` + 1 ,
                 <if test="startAddressCode !=null and startAddressCode != ''">
                     `start_address_code`  = #{startAddressCode},
                 </if>
                 <if test="updateUserCode != null">
                     `update_user_code`  = #{updateUserCode},
                 </if>
                 <if test="endAddressName !=null and endAddressName != ''">
                     `end_address_name`  = #{endAddressName},
                 </if>
                 <if test="remark != null">
                     `remark`  = #{remark},
                 </if>
                 <if test="endAddressCode !=null and endAddressCode != ''">
                     `end_address_code`  = #{endAddressCode},
                 </if>
                 <if test="startLevel !=null and startLevel != ''">
                     `start_level`  = #{startLevel},
                 </if>
                 <if test="startAddressName !=null and startAddressName != ''">
                     `start_address_name`  = #{startAddressName},
                 </if>
                 <if test="endLevel !=null and endLevel != ''">
                     `end_level`  = #{endLevel},
                 </if>
                 <if test="mileage != null">
                     `mileage`  = #{mileage},
                 </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from adress_mileage t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.AdressMileage">
        select
        <include refid="searchFieldsSql"/>
        from adress_mileage t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.AdressMileage">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from adress_mileage t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.AdressMileage">
        select
        <include refid="searchFieldsSql"/>
        from adress_mileage t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
            adress_mileage t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
            and `id` = #{id}
    </update>

    <update id="deleteById">
        update
            adress_mileage t
        set  `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.AdressMileage" useGeneratedKeys="true"  keyProperty="id">
        insert into adress_mileage
        <trim prefix="(" suffix=")" suffixOverrides="," >

                    <if test="startAddressCode !=null and startAddressCode != ''">
                        `start_address_code`,
                    </if>

                    <if test="updateUserCode != null">
                        `update_user_code`,
                    </if>

                    <if test="endAddressName !=null and endAddressName != ''">
                        `end_address_name`,
                    </if>

                    <if test="createUserCode != null">
                        `create_user_code`,
                    </if>

                    <if test="remark != null">
                        `remark`,
                    </if>

                    <if test="endAddressCode !=null and endAddressCode != ''">
                        `end_address_code`,
                    </if>

                    <if test="startLevel !=null and startLevel != ''">
                        `start_level`,
                    </if>

                    <if test="startAddressName !=null and startAddressName != ''">
                        `start_address_name`,
                    </if>

                    <if test="endLevel !=null and endLevel != ''">
                        `end_level`,
                    </if>

                    <if test="mileage != null">
                        `mileage`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
                    <if test="startAddressCode !=null and startAddressCode != ''">
                        #{startAddressCode},
                    </if>
                    <if test="updateUserCode != null">
                        #{updateUserCode},
                    </if>
                    <if test="endAddressName !=null and endAddressName != ''">
                        #{endAddressName},
                    </if>
                    <if test="createUserCode != null">
                        #{createUserCode},
                    </if>
                    <if test="remark != null">
                        #{remark},
                    </if>
                    <if test="endAddressCode !=null and endAddressCode != ''">
                        #{endAddressCode},
                    </if>
                    <if test="startLevel !=null and startLevel != ''">
                        #{startLevel},
                    </if>
                    <if test="startAddressName !=null and startAddressName != ''">
                        #{startAddressName},
                    </if>
                    <if test="endLevel !=null and endLevel != ''">
                        #{endLevel},
                    </if>
                    <if test="mileage != null">
                        #{mileage},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn" >
        <trim prefix="(" suffix=")" suffixOverrides="," >
                    `start_address_code`,
                    `update_user_code`,
                    `end_address_name`,
                    `create_user_code`,
                    `remark`,
                    `end_address_code`,
                    `start_level`,
                    `start_address_name`,
                    `end_level`,
                    `mileage`,
        </trim>
    </sql>

    <sql id="batchInsertValue" >
        <trim prefix="(" suffix=")" suffixOverrides="," >
                    #{item.startAddressCode},
                    #{item.updateUserCode},
                    #{item.endAddressName},
                    #{item.createUserCode},
                    #{item.remark},
                    #{item.endAddressCode},
                    #{item.startLevel},
                    #{item.startAddressName},
                    #{item.endLevel},
                    #{item.mileage},
        </trim>
    </sql>


    <insert id="insertBatch">
            insert into
            adress_mileage
             <include refid="batchInsertColumn"/>
            values
            <foreach collection="list" item="item" index="index" separator="," >
                <include refid="batchInsertValue"/>
            </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.startAddressCode !=null and item.startAddressCode != ''">
                     `start_address_code`  = #{item.startAddressCode},
                 </if>
                 <if test="item.updateUserCode != null">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.endAddressName !=null and item.endAddressName != ''">
                     `end_address_name`  = #{item.endAddressName},
                 </if>
                 <if test="item.remark != null">
                     `remark`  = #{item.remark},
                 </if>
                 <if test="item.endAddressCode !=null and item.endAddressCode != ''">
                     `end_address_code`  = #{item.endAddressCode},
                 </if>
                 <if test="item.startLevel !=null and item.startLevel != ''">
                     `start_level`  = #{item.startLevel},
                 </if>
                 <if test="item.startAddressName !=null and item.startAddressName != ''">
                     `start_address_name`  = #{item.startAddressName},
                 </if>
                 <if test="item.endLevel !=null and item.endLevel != ''">
                     `end_level`  = #{item.endLevel},
                 </if>
                 <if test="item.mileage != null">
                     `mileage`  = #{item.mileage},
                 </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.startAddressCode !=null and item.startAddressCode != ''">
                        and  `start_address_code`  =#{item.startAddressCode}
                    </if>
                    <if test="item.endAddressName !=null and item.endAddressName != ''">
                        and  `end_address_name`  =#{item.endAddressName}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.remark !=null">
                        and  `remark`  =#{item.remark}
                    </if>
                    <if test="item.startLevel !=null and item.startLevel != ''">
                        and  `start_level`  =#{item.startLevel}
                    </if>
                    <if test="item.version !=null">
                        and  `version`  =#{item.version}
                    </if>
                    <if test="item.endLevel !=null and item.endLevel != ''">
                        and  `end_level`  =#{item.endLevel}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.endAddressCode !=null and item.endAddressCode != ''">
                        and  `end_address_code`  =#{item.endAddressCode}
                    </if>
                    <if test="item.startAddressName !=null and item.startAddressName != ''">
                        and  `start_address_name`  =#{item.startAddressName}
                    </if>
                    <if test="item.mileage !=null">
                        and  `mileage`  =#{item.mileage}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE adress_mileage
            <include refid="setBatchFieldsSql"/>
            where
            `id` = #{item.id}
        </foreach>

    </update>
    
    <update id="deleteBatch">
       	<foreach collection="list" item="item" separator=";">
	       UPDATE 
	            adress_mileage t
	       set  `delete_flag`=#{item.deleteFlag}
	       WHERE
	           `id` = #{item.id}
        </foreach>
    </update>


</mapper>