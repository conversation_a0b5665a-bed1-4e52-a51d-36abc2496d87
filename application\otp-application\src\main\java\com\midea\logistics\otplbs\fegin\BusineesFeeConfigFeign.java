package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig;
import com.midea.logistics.otp.rule.domain.request.BusineesFeeConfigRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: BusineesFeeConfigFeign
 * Author: luoh
 * Date: 2019-6-6 11:05:00
 * Description:业务计费类型 feign
 */
@FeignClient(value = "logistics-otp-rule-service",url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface BusineesFeeConfigFeign {

    @RequestMapping(value = OptRuleRouters.BUSINEES_FEE_CONFIG_SAVE, method = RequestMethod.POST)
    JsonResponse create(@RequestBody BusineesFeeConfigRequest busineesFeeConfigRequest);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINEES_FEE_CONFIG_DETAIL, method = RequestMethod.GET)
    JsonResponse<BusineesFeeConfig> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINEES_FEE_CONFIG_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody BusineesFeeConfig busineesFeeConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINEES_FEE_CONFIG_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = OptRuleRouters.BUSINEES_FEE_CONFIG_LIST, method = RequestMethod.GET)
    JsonResponse<PageResponse<BusineesFeeConfig>> search(@SpringQueryMap BusineesFeeConfigRequest busineesFeeConfigRequest);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINEES_FEE_CONFIG_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<BusineesFeeConfigRequest> busineesFeeConfigRequests);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINEES_FEE_CONFIG_BATCH_DELETE_BY_BUSINESSKEY, method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
