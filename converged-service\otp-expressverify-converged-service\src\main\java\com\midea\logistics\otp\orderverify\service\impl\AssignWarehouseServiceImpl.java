package com.midea.logistics.otp.orderverify.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.logisticsbopsdk.bean.WmsSameWhCodeValidateDto;
import com.midea.logistics.logisticsbopsdk.service.OtpService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.ControlParamFeign;
import com.midea.logistics.otp.common.feign.servicefeign.orderAgg.OrderAggFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.CompartRuleFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.CustomerConfigFeign;
import com.midea.logistics.otp.common.helper.*;
import com.midea.logistics.otp.common.request.CreateOrderContext;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.bean.OrderSplitDto;
import com.midea.logistics.otp.order.common.es.EsOrderLogService;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.flow.helper.FlowHelper;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.helper.PartCancelSplitRedisLockHelper;
import com.midea.logistics.otp.order.common.helper.TmallHelper;
import com.midea.logistics.otp.order.common.iflow.helper.ICustomerFlowDataHelper;
import com.midea.logistics.otp.order.common.mq.producer.PledgeCancelProducer;
import com.midea.logistics.otp.order.common.mq.producer.WorkflowVerifyProducer;
import com.midea.logistics.otp.order.common.mq.producer.ZeebeOrderProducer;
import com.midea.logistics.otp.order.common.service.CenterInvService;
import com.midea.logistics.otp.order.common.utils.StockShortageResponseUtil;
import com.midea.logistics.otp.order.converged.domain.request.SeparateWarehouseConfirmRequest;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.AssignWareHouseInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.AssignWarehouseService;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail;
import com.midea.logistics.otp.rule.domain.bean.CompartRule;
import com.midea.logistics.otp.rule.domain.bean.CustomerConfig;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.OpenJsonResponse;
import com.mideaframework.core.web.PageResponse;

import lombok.extern.slf4j.Slf4j;
import static com.midea.logistics.otp.common.constants.CommonConstant.NOT_APART;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssignWarehouseServiceImpl implements AssignWarehouseService {

    @Autowired
    CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private CenterInvService centerInvService;
    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    private OrderFeign orderFeign;
    @Autowired
    CustomerConfigFeign customerConfigFeign;
    @Autowired
    CompartRuleFeign compartRuleFeign;
    @Autowired
    CustomerOrderItemFeign customerOrderItemFeign;
    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private OtpService otpService;
    @Autowired
    private PledgeCancelProducer pledgeCancelProducer;
    @Autowired
    private WorkflowVerifyProducer workflowVerifyProducer;
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private EsOrderLogService esOrderLogServiceImpl;
    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private IdGenHelper idGenHelper;
    @Autowired
    private ControlParamFeign controlParamFeign;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private ZeebeOrderProducer zeebeOrderProducer;
    @Autowired
    private MipExecutionLogFeign mipExecutionLogFeign;
    @Autowired
    private ICustomerFlowDataHelper customerFlowDataHelper;
    @Autowired
    private FlowRuleHelper flowRuleHelper;
    @Autowired
    private FlowHelper flowHelper;
    @Autowired
    private TmallHelper tmallHelper;
    @Autowired
    private PartCancelSplitRedisLockHelper partCancelSplitRedisLockHelper;
    @Autowired
    private OrderAggFeign orderAggFeign;

    @Override
    @ExpressFlow(node = OrderOperateType.APART)
    public FlowListenerParam automatic(CustomerOrderInfoExt customerOrderInfo) {

        //这里重新获取一下订单信息，因为订单取消有对明细进行处理
        customerOrderInfo = orderverifyHelper.getCustomerOrderInfoExt(customerOrderInfo.getOrderNo());

        if (ToolUtils.isEmpty(customerOrderInfo.getOrderType())) {
            throw BusinessException.fail("父单订单类型为空！");
        }

        String orderNo = customerOrderInfo.getOrderNo();
        // 检查 lock
        String key = "SEPARATEWAREHOUSE:AUTOMATIC" + orderNo;
        boolean lock = redisLockHelper.tryLock(key, 30L);
        if (!lock) {
            throw BusinessException.fail("分仓过于频繁，请稍后再试！");
        }
        
        // 检查一下是否正在部分取消
        partCancelSplitRedisLockHelper.checkPartCancelLockAndThrow(orderNo);
        
        FlowListenerParam flowListenerParam = new FlowListenerParam();
        flowListenerParam.setFlowStatus(FlowStatus.SUCCESS);
    
        //2024年8月8日23:04:52 浩明：https://cf.annto.com/pages/resumedraft.action?draftId=46466128&draftShareId=11a5e11b-b739-4252-b70c-1d7a52b1395f&，往订单描述写备注
        customerOrderInfo.setExceptionDesc("402：订单"+customerOrderInfo.getCustomerOrderNo()+"，待分仓 "+orderAggFeign.getVerifyFailPersonByCustOrder(customerOrderInfo).data);
        
        //查询客户订单
        if (ApartType.HANDLE.getKey().equals(customerOrderInfo.getApartType())) {
            return FlowListenerParam.hold("手工拆单");
        }

        //已经拆单的直接成功
        if (ToolUtils.isNotEmpty(customerOrderInfo.getApartStatus()) && CommonConstant.APARTED.equals(customerOrderInfo.getApartStatus())) {
            return FlowListenerParam.success("");
        }

        List<CustomerOrderItem> customerOrderItems = customerOrderInfo.getCustomerOrderItems();

        //是否出库任务
        boolean empoyFlag = InOutType.OUT == InOutType.getEnum(customerOrderInfo.getInOutType()) || InOutType.ADJUST == InOutType.getEnum(customerOrderInfo.getInOutType());

        //默认不拆单
        CustomerConfig customerConfig = new CustomerConfig();
        customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_NO);
        customerConfig.setApartType(ApartType.NOT_APART.getKey());

        CreateOrderContext cot = new CreateOrderContext();
        cot.setCustomerOrderInfo(customerOrderInfo);
        cot.setOrderAddress(customerOrderInfo.getCustomerOrderAddress());
        cot.setCustomerOrderItemList(customerOrderItems);
        cot.setOrderInfoExtend(customerOrderInfo.getCustomerOrderInfoExtend());
        // 有仓库存在
        if (StringUtils.isNotBlank(customerOrderInfo.getWhCode())) {
            //拆单，生成拆单对象
            List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfo,customerOrderItems, customerConfig.getApartType());
            if (ToolUtils.isNotEmpty(orderList)) {
                String whCode = customerOrderInfo.getWhCode();
                //获取仓库，执行分仓
                for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                    list.forEach(
                        req -> req.setWhCode(whCode)
                    );
                }
                OrderSplitDto dto = batchGenerateOrder(cot, orderList, empoyFlag);
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    return FlowListenerParam.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT, dto.getErrMsg());
                }
            }
            List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());

            return FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
        }

        // 没有仓库存在

        //运输单，1：1生成子单
        if (Arrays.asList(OrderType.YS, OrderType.ZF).contains(EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType()))) {
            List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfo , customerOrderItems, customerConfig.getApartType());
            if (ToolUtils.isNotEmpty(orderList)) {
                //获取仓库，执行分仓
                for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                    list.forEach(
                        req -> req.setWhCode(CommonConstant.WHCODE_FLAG)
                    );
                }

                OrderSplitDto dto = batchGenerateOrder(cot, orderList, empoyFlag && CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()));
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    if (dto.getErrMsg().contains("获取库存等待锁超时")) {
                        return FlowListenerParam.timeout(dto.getErrMsg());
                    }
                    return FlowListenerParam.fail(ExceptionType.INVENTORY_INSUFFICIENT, dto.getErrMsg());
                }
            }
            List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());

            return FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
        }
        //判断是否自动分仓

        //订单接单时，先判断订单的仓库字段是否为空，若为空，去调用配置中心-》》客户配置，此订单是否自动分仓
        //if (CommonConstant.STRING_FLAG_NO.equals(customerConfig.getCompartFlag())) {
        //    customerOrderInfo.setApartType(ApartType.HANDLE.getKey());
        //    setOrderStatusAudited(customerOrderInfo);
        //    orderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "分仓拆单");
        //    flowListenerParam.setFlowStatus(FlowStatus.HOLD);
        //    flowListenerParam.setErrorMsg("是否分仓配置为否，需要手动分仓");
        //    return flowListenerParam;
        //}

        //拆单，生成拆单对象
        List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfo, customerOrderItems,customerConfig.getApartType());
        if (ToolUtils.isNotEmpty(orderList)) {
            //自动分仓，查询分仓规则
            CompartRule compartRule = getCompartRule(customerOrderInfo);
            if (null == compartRule) {
                customerOrderInfo.setApartType(ApartType.HANDLE.getKey());
                setOrderStatusAudited(customerOrderInfo);
                orderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "分仓");
                return FlowListenerParam.hold("未查到分仓规则,无法自动分仓");
            }
            String whCode = compartRule.getWhCode();
            if (ToolUtils.isEmpty(whCode)) {
                customerOrderInfo.setApartType(ApartType.HANDLE.getKey());
                setOrderStatusAudited(customerOrderInfo);
                orderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "分仓");
                return FlowListenerParam.hold("分仓规则未配置仓库，无法分仓");
            }
            if (ToolUtils.isNotEmpty(orderList)) {
                //获取仓库，执行分仓
                for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                    list.forEach(
                        req -> req.setWhCode(whCode)
                    );
                }
                OrderSplitDto dto = batchGenerateOrder(cot, orderList, empoyFlag && CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()));
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    if (dto.getErrMsg().contains("获取库存等待锁超时")) {
                        return FlowListenerParam.timeout(dto.getErrMsg());
                    }
                    return FlowListenerParam.fail(ExceptionType.INVENTORY_INSUFFICIENT, JSONObject.toJSONString(dto.getErrMsg()));
                }
            }
            List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
            return FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
        }

        return FlowListenerParam.fail("未定义的分仓规则");
    }

    private void setOrderStatusAudited(CustomerOrderInfo customerOrderInfo) {
        if (CommonConstant.APARTED.equals(customerOrderInfo.getApartStatus())) {
            customerOrderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
        }
    }

    private void setExcuteStatusAudited(CustomerOrderInfo customerOrderInfo) {
        if (CommonConstant.APARTED.equals(customerOrderInfo.getApartStatus())) {
            customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
        }
    }

    private OrderSplitDto batchGenerateOrder(CreateOrderContext cot, List<List<SeparateWarehouseConfirmRequest>> separateWarehouseConfirmRequestList, boolean compartFlag) {
        // 当前子订单
        List<OrderInfo> orderInfoList = Lists.newArrayList();
        List<OrderInfoItem> orderInfoItemList = Lists.newArrayList();
        List<OrderInfoExt> orderInfoExtList = Lists.newArrayList();
        List<String> errorInfos = Lists.newArrayList();
        separateWarehouseConfirmRequestList.forEach(
            s -> {
                OrderSplitDto dto = generateOrder(cot, s);
                if (ToolUtils.isEmpty(dto.getErrMsg())) {
                    orderInfoList.addAll(cot.getOrderInfoList());
                    orderInfoItemList.addAll(cot.getOrderInfoItemList());
                    orderInfoExtList.addAll(cot.getOrderInfoExtList());
                } else {
                    errorInfos.add(dto.getErrMsg());
                }
            }
        );


        if (ToolUtils.isNotEmpty(errorInfos)) {
            List<String> errorInfo = errorInfos.stream().distinct().collect(Collectors.toList());
            return OrderSplitDto.fail(errorInfos.toString());
        }

        if (ToolUtils.isEmpty(orderInfoList)) {
            return OrderSplitDto.fail("分仓生产子单的数量为0");
        }
    
        //2021-10-16 17:12:49 阳阳： 快递流程直接NOT_APART
        CustomerOrderInfo customerOrderInfo = cot.getCustomerOrderInfo();
        
        //预占库存
        if (compartFlag) {
            OrderSplitDto orderSplitDto = proHoldInvBatch(cot, orderInfoExtList);
            if (orderSplitDto != null)
                return orderSplitDto;
        }

       
        customerOrderInfo.setApartStatus(CommonConstant.APARTED);
        if (Lists.newArrayList(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT.getKey(), ExceptionType.INVENTORY_INSUFFICIENT.getKey()).contains(customerOrderInfo.getExceptionType())) {
            customerOrderInfo.setExceptionType("");
            customerOrderInfo.setExceptionDesc("");
        }

        customerOrderInfo.setApartType(NOT_APART);
        customerOrderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
        customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
        setExcuteStatusAudited(customerOrderInfo);


        //2021年12月13日17:13:45 永培：将分仓生成子单的逻辑移动到原子服务，避免服务挂掉，后重新生成子单
        AssignWareHouseInfo assignWareHouseInfo = new  AssignWareHouseInfo();
        assignWareHouseInfo.setOrderInfoList(orderInfoList);
        assignWareHouseInfo.setOrderInfoItemList(orderInfoItemList);
        assignWareHouseInfo.setCustomerOrderInfo(customerOrderInfo);

        JsonResponse<AssignWareHouseInfo> jsonResponse = orderFeign.assignWareGenerateOrderInfo(assignWareHouseInfo);
        String code = jsonResponse.getCode();
        if (!BaseCodeEnum.SUCCESS.getCode().equals(code) || null == jsonResponse.getData()) {
            log.info("express batchGenerateOrder-> ：customerOrderNo：{} 生成子单失败，回滚库存",customerOrderInfo.getCustomerOrderNo());
            orderInfoExtList.stream().forEach(
                orderInfoExt ->//取消库存
                    centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey())
            );
            return OrderSplitDto.fail("分仓生成子单失败");
        }

        //启动子流程
        flowHelper.startSubFlow(customerOrderInfo.getOrderNo(),orderInfoList);

        cot.setOrderInfoItemList(orderInfoItemList);
        cot.setOrderInfoExtList(orderInfoExtList);
        cot.setOrderInfoList(orderInfoList);
        return OrderSplitDto.success(orderInfoExtList);
    }
    
    
    /**
    * @description: 占用库存
    * @param: [cot, orderInfoExtList, customerOrderInfo]
    * @return: com.midea.logistics.otp.order.common.bean.OrderSplitDto
    * @author: 陈永培
    * @createtime: 2022/7/25 20:04
    */
    private OrderSplitDto proHoldInvBatch(CreateOrderContext cot, List<OrderInfoExt> orderInfoExtList) {
    
        /**
         * 蓝月亮2c - 如果发现拓展表有子单号，不需要占用库存了，因为在此方法占用了：com.midea.logistics.otp.order.service.impl.BlueMoon2CConvergedServiceImpl#proHoldInvBatch(com.midea.logistics.otp.order.domain.request.CustomerOrderInfoRequest, java.lang.String)
         */
        CustomerOrderInfo customerOrderInfo = cot.getCustomerOrderInfo();
        if (ToolUtils.isEmpty(customerOrderInfo)) {
            return null;
        }
        
        CustomerOrderInfoExtend orderInfoExtend = cot.getOrderInfoExtend();
        if (null != orderInfoExtend){
            String subOrderNo = orderInfoExtend.getSubOrderNo();
            if (ToolUtils.isNotEmpty(subOrderNo)) {
                return null;
            }
        }
        
        long currentTimeMillis = System.currentTimeMillis();
        JsonResponse jsonResponse = centerInvService.proHoldInvBatch(orderInfoExtList, CenterInvType.PLAN_OMS_OUT.getKey());
        log.info("占用的-调用中央库存---{}--------proHoldInvBatch-----------耗时{}ms", jsonResponse, System.currentTimeMillis() - currentTimeMillis);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            String whCode = orderInfoExtList.get(0).getWhCode();
            String whName = Optional.ofNullable(cdWarehouseManager.getCdWarehouseNameByWhCode(whCode)).get();
            Object data = jsonResponse.getData();
            //订单有仓库分仓失败,"占用库存调用鹊桥失败：商品：0010604951仓库可用库存数不足,操作失败,可用库存为 0"
            //***可用库存不足，商品0010604951占用失败；请联系核算主管确认！
            //2022年3月28日11:01:31 李娟： 天猫平台，需要同步天猫缺货记录
            tmallHelper.tmallItemLackNotice(cot.getCustomerOrderInfo(), cot.getCustomerOrderItemList(),jsonResponse.getMsg());
            // 获取到库存不足的itemCode，并拼接在仓库名称后面
            String itemCodesStringValue = StockShortageResponseUtil.getStockShortageItemCodes(jsonResponse);
            if (data == null) {
                return OrderSplitDto.fail(whName + itemCodesStringValue + "可用库存不足；请联系核算主管确认！");
            }
            return OrderSplitDto.fail(whName + itemCodesStringValue + "可用库存不足；" + data.toString() + "请联系核算主管确认！");
        }
        return null;
    }

    /**
     * 复制客户订单信息到新生成的订单
     *
     * @return
     */
    private OrderSplitDto generateOrder(CreateOrderContext cot, List<SeparateWarehouseConfirmRequest> separateWarehouseConfirmRequestList) {
        CustomerOrderInfo customerOrderInfo = cot.getCustomerOrderInfo();
        CustomerOrderAddress address = cot.getOrderAddress();
        List<CustomerOrderItem> customerOrderItems = cot.getCustomerOrderItemList();
        Map<String, Map<Integer, List<CustomerOrderItem>>> customerOrderItemMaps = Maps.newHashMap();
        //根据商品分组
        try {
            customerOrderItemMaps = customerOrderItems
                .stream().collect(Collectors.groupingBy(CustomerOrderItem::getItemCode, Collectors.groupingBy(CustomerOrderItem::getItemLineNo)));
        } catch (NullPointerException e) {
            log.error(e.getMessage(), e);
            return OrderSplitDto.fail("请检查商品信息！商品行号或者商品编码为空");
        }

        List<OrderInfo> orderInfoList = Lists.newArrayList();
        List<OrderInfoItem> orderInfoItemList = Lists.newArrayList();
        List<OrderInfoExt> orderInfoExts = Lists.newArrayList();

        //一个仓库一张订单，所以根据仓库分组
        Map<String, List<SeparateWarehouseConfirmRequest>> separateWarehouseConfirmMap = separateWarehouseConfirmRequestList
            .stream().collect(Collectors.groupingBy(SeparateWarehouseConfirmRequest::getWhCode));
        //家居订单不能多个子单 只能一个仓库
        boolean HLFlag = StringUtils.isNotBlank(customerOrderInfo.getProjectClassify()) && ProjectClassifyEnum.HL.getKey().equals(customerOrderInfo.getProjectClassify());
        //虚拟商品
        boolean fictitiousFlag = StringUtils.isNotBlank(customerOrderInfo.getProjectClassify()) && ProjectClassifyEnum.CDCM_FICTITIOUS.getKey().equals(customerOrderInfo.getProjectClassify());
        if (HLFlag && separateWarehouseConfirmMap.size() != 1) {
            return OrderSplitDto.fail("家居订单不支持拆成多仓库拆单");
        }
        //代收货款不允许拆单
        if (CommonConstant.STRING_FLAG_YES.equals(customerOrderInfo.getCollectionFlag()) && separateWarehouseConfirmMap.size() != 1) {
            return OrderSplitDto.fail("代收货款不允许拆单");
        }

        order:
        for (List<SeparateWarehouseConfirmRequest> whs : separateWarehouseConfirmMap.values()) {
            OrderInfoExt orderInfoExt = new OrderInfoExt();
            List<OrderInfoItem> orderInfoItems = Lists.newArrayList();
            OrderInfo orderInfo = generateOrder(customerOrderInfo);
            
            //蓝月亮2c： 如果拓展表有子单号，直接用拓展表的字段覆盖
            CustomerOrderInfoExtend orderInfoExtend = cot.getOrderInfoExtend();
            Optional.ofNullable(orderInfoExtend).ifPresent(it -> {
                if (ToolUtils.isNotEmpty(it.getSubOrderNo())) {
                    orderInfo.setOrderNo(it.getSubOrderNo());
                }
            });
            
            if (address != null) {
                BeanUtils.copyProperties(address, orderInfo
                    , "id", "orderNo", "remark", "planOrderFlag", "tenantCode");
            }
            //数量
            BigDecimal totalQty = BigDecimal.ZERO;
            //体积
            BigDecimal totalVolume = BigDecimal.ZERO;
            //重量
            BigDecimal totalGrossWeight = BigDecimal.ZERO;
            //货值
            BigDecimal orderValue = BigDecimal.ZERO;
            item:
            for (SeparateWarehouseConfirmRequest wh : whs) {
                if (CommonConstant.getApartSingle().contains(customerOrderInfo.getUpperOrderType())
                    && BusinessMode.isB2B(customerOrderInfo.getBusinessMode())
                ) {
                    orderInfo.setWaybillNo(wh.getWaybillNo());
                }

                //纯运输订单没有仓库
                if (!CommonConstant.WHCODE_FLAG.equals(wh.getWhCode())) {
                    orderInfo.setWhCode(wh.getWhCode());
                    String whName = cdWarehouseManager.getCdWarehouseNameByWhCode(wh.getWhCode());
                    orderInfo.setWhName(whName);
                }
                //生成订单商品信息
                OrderInfoItem orderInfoItem = new OrderInfoItem();
                orderInfoItem.setItemCode(wh.getItemCode());
                if (ToolUtils.isEmpty(customerOrderItemMaps.get(wh.getItemCode())) ||
                    ToolUtils.isEmpty(customerOrderItemMaps.get(wh.getItemCode()).get(wh.getItemLineNo()))) {
                    continue item;
                }

                CustomerOrderItem customerOrderItem = customerOrderItemMaps.get(wh.getItemCode()).get(wh.getItemLineNo()).get(0);
                //复制客户订单商品信息到新生成的订单商品
                BeanUtils.copyProperties(customerOrderItem, orderInfoItem
                    , "id", "cancleQty", "splitQty", "orderNo", "actQty");
                orderInfoItem.setPlanQty(BigDecimal.valueOf(wh.getCount()));
                orderInfoItem.setParentOrderNo(orderInfo.getParentOrderNo());
                orderInfoItem.setOrderNo(orderInfo.getOrderNo());
                orderInfoItem.setSubOrderNo(customerOrderItem.getSubOrdercode());
                orderInfoItem.setUpdateUserName(null);
                orderInfoItem.setUpdateUserCode(null);
                orderInfoItem.setCreateUserCode(null);
                orderInfoItem.setCreateUserName(null);
                orderInfoItem.setPackingAmount(customerOrderItem.getPackingAmount());

                orderverifyHelper.setCCSInstallType(orderInfo, customerOrderItem);
                //设置子单商品表的总体积、总毛重
                BigDecimal volume = Optional.ofNullable(orderInfoItem.getVolume()).orElse(BigDecimal.ZERO); // 单台体积
                BigDecimal grossWeight = Optional.ofNullable(orderInfoItem.getGrossWeight()).orElse(BigDecimal.ZERO); // 单台毛重
                BigDecimal planQty = orderInfoItem.getPlanQty(); // 计划数量
                orderInfoItem.setTotalVolume(planQty.multiply(volume));
                orderInfoItem.setTotalGrossWeight(planQty.multiply(grossWeight));

                //由于家居订单不允许部分拆单，不允许多仓库发货、明细的总体积、总毛重直接取父单明细的
                if (HLFlag) {
                    orderInfoItem.setTotalVolume(customerOrderItem.getTotalVolume());
                    orderInfoItem.setTotalGrossWeight(customerOrderItem.getTotalGrossWeight());
                }
                // 虚拟商品 数量相同直接去父单明细的总体积毛重
                if (fictitiousFlag && planQty.compareTo(customerOrderItem.getPlanQty()) == 0) {
                    orderInfoItem.setTotalVolume(customerOrderItem.getTotalVolume());
                    orderInfoItem.setTotalGrossWeight(customerOrderItem.getTotalGrossWeight());
                }

                orderInfoItemList.add(orderInfoItem);
                orderInfoItems.add(orderInfoItem);
                //计算订单
                totalQty = totalQty.add(orderInfoItem.getPlanQty());


                if (orderInfoItem.getTotalGrossWeight() != null) {
                    totalGrossWeight = totalGrossWeight.add(orderInfoItem.getTotalGrossWeight());
                } else if (orderInfoItem.getGrossWeight() != null) {
                    BigDecimal grossWeightTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getGrossWeight());
                    totalGrossWeight = totalGrossWeight.add(grossWeightTemp);
                }
                if (orderInfoItem.getTotalVolume() != null) {
                    totalVolume = totalVolume.add(orderInfoItem.getTotalVolume());
                } else if (orderInfoItem.getVolume() != null) {
                    BigDecimal volumeTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getVolume());
                    totalVolume = totalVolume.add(volumeTemp);
                }



            }

            //家居订单直接取父单的值
            if (HLFlag) {
                totalVolume = customerOrderInfo.getTotalVolume();
                totalGrossWeight = customerOrderInfo.getTotalGrossWeight();
            }

            //计算订单数量，体积，重量
            orderInfo.setTotalQty(totalQty);
            orderInfo.setTotalVolume(totalVolume);
            orderInfo.setTotalGrossWeight(totalGrossWeight);
            orderInfo.setApartStatus(CommonConstant.APARTED);
            if (BigDecimal.ZERO.compareTo(totalQty) == 0) {
                continue order;
            }


            orderInfo.setUpdateUserName(null);
            orderInfo.setUpdateUserCode(null);
            orderInfo.setCreateUserCode(null);
            orderInfo.setCreateUserName(null);
//           CIMS/手工订单子单货值：
//            (orderInfo.totalQty/customerOrderInfo.totalQty)*customerOrderInfo.orderValue
            if (Lists.newArrayList(SourceSystem.CIMS.getKey()
                , SourceSystem.HANDLE.getKey()
                , SourceSystem.KingDee_SD.getKey()
                ,SourceSystem.JNC_CXP.getKey()).contains(customerOrderInfo.getSourceSystem())
                && ToolUtils.isNotEmpty(customerOrderInfo.getOrderValue())) {
                orderValue = ObjectUtils.defaultIfNull(customerOrderInfo.getOrderValue(), BigDecimal.ZERO)
                    .multiply(ObjectUtils.defaultIfNull(orderInfo.getTotalQty(), BigDecimal.ZERO)).divide(customerOrderInfo.getTotalQty(), 8, BigDecimal.ROUND_HALF_UP);
            }
            orderInfo.setOrderValue(orderValue);

            orderInfoList.add(orderInfo);

            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);


            boolean isShare = JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType());
            //纯运输、直发订单不分仓
            if (Arrays.asList(OrderType.YS, OrderType.ZF).contains(EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType()))
                //入库订单不占用库存
                || (InOutType.IN.getName().equals(customerOrderInfo.getInOutType()) && !isShare)
                || OrderType.DO.getKey().equals(customerOrderInfo.getOrderType())
            ) {
                continue order;
            }
            //货权转移,校验是否同仓
            if (isShare) {
                String customerCode = customerOrderInfo.getCustomerCode();
                String targetCustomerCode = customerOrderInfo.getTargetCustomerCode();
                if (ToolUtils.isEmpty(targetCustomerCode)) {
                    return OrderSplitDto.fail("目标客户为空，调剂单,货权转移校验失败！！");
                }
                List<String> sameWarehouseCode = validateSameWarehouseCode(customerCode, targetCustomerCode, Lists.newArrayList(orderInfoExt.getWhCode()));
                if (org.springframework.util.CollectionUtils.isEmpty(sameWarehouseCode)) {
                    //LOGGER.warn("WMS同仓校验失败，orderNo:{}", orderNo);
                    return OrderSplitDto.fail("分仓校验，WMS同仓校验失败，请到WMS-【配置】-【出库配置】-【客户】维护协同关系[客户编码:"+ customerCode + ",目标客户编码:"+ targetCustomerCode +"]！！");
                }
                if (OrderType.RI.getKey().equals(customerOrderInfo.getOrderType())) {
                    orderInfoExt.setCustomerCode(customerOrderInfo.getTargetCustomerCode());
                }
            }
            orderInfoExts.add(orderInfoExt);
        }
        cot.setOrderInfoList(orderInfoList);
        cot.setOrderInfoItemList(orderInfoItemList);
        cot.setOrderInfoExtList(orderInfoExts);
        return OrderSplitDto.success(orderInfoExts);
    }


    private CompartRule getCompartRule(CustomerOrderInfo customerOrderInfo) {

        CompartRule compartRule = new CompartRule();
        compartRule.setCustomerCode(customerOrderInfo.getCustomerCode());
        compartRule.setInOutType(customerOrderInfo.getInOutType());
        compartRule.setSiteCode(customerOrderInfo.getSiteCode());
        compartRule.setOrderType(customerOrderInfo.getOrderType()); //分仓增加订单类型匹配
        JsonResponse<PageResponse<CompartRule>> search = compartRuleFeign.searchSeparateWarehouse(compartRule);
        if (search == null || search.data == null || CollectionUtils.isEmpty(search.data.list)) {
            return null;
        }
        //多条，返回取订单类型为空的那一条
        List<CompartRule> empOrderTypeRules = new ArrayList<CompartRule>();
        boolean allHasOrderType = true;//是否都包含订单类型
        for (CompartRule rule : search.data.list) {
            //返回的就是查询的那一条，直接返回
            if(!StringUtils.isEmpty(customerOrderInfo.getOrderType()) && customerOrderInfo.getOrderType().equals(rule.getOrderType()) ){
                return rule;
            }
            if(ToolUtils.isEmpty(rule.getOrderType())){
                allHasOrderType = false;
                empOrderTypeRules.add(rule);
            }
        }
        if(CollectionUtils.isNotEmpty(empOrderTypeRules)){
            return empOrderTypeRules.get(0);
        }
        if(allHasOrderType){
            return  null;
        }
        return search.data.list.get(0);
    }

    /**
     * 复制客户订单信息到新生成的订单
     *
     * @param customerOrderInfo
     * @return
     */
    private OrderInfo generateOrder(CustomerOrderInfo customerOrderInfo) {
        OrderInfo orderInfo = new OrderInfo();
        BeanUtils.copyProperties(customerOrderInfo, orderInfo, "id", "orderNo");
        orderInfo.setOrderStatus(OrderStatus.NEW.getKey());
        orderInfo.setUpperAgingCode(customerOrderInfo.getAgingProductCode());
        orderInfo.setExcuteStatus(ExcuteStatus.NEW.getKey());
        orderInfo.setParentOrderNo(customerOrderInfo.getOrderNo());
        String orderNo  = flowRuleHelper.getAnOrderNo(customerOrderInfo);
        orderInfo.setTenantCode(customerOrderInfo.getTenantCode());
        orderInfo.setOrderNo(orderNo);
        return orderInfo;
    }

    private List<List<SeparateWarehouseConfirmRequest>> generateSeparateWarehouseConfirmRequest(CustomerOrderInfo customerOrderInfo,List<CustomerOrderItem> items, String apartType) {
        List<List<SeparateWarehouseConfirmRequest>> orderList = Lists.newArrayList();
        //有两个上游订单类型直接单品单件拆单
        if (CommonConstant.getApartSingle().contains(customerOrderInfo.getUpperOrderType())) {
            apartType = ApartType.SINGLE.getKey();
        }

        items.forEach(
            customerOrderItem -> {
                if (ToolUtils.isEmpty(customerOrderItem.getItemCode())) {
                    throw BusinessException.fail("订单商品信息缺少商品编码");
                }
                if (ToolUtils.isEmpty(customerOrderItem.getItemLineNo())) {
                    throw BusinessException.fail("订单商品信息缺少商品行号");
                }
            }
        );
        if (SourceSystem.QIMEN.getKey().equals(customerOrderInfo.getSourceSystem())){
            orderList = this.packingSplit(customerOrderInfo,items);
            if(null != orderList){
                return orderList;
            }
            orderList = Lists.newArrayList();
        }



        Integer waybillNo = 0;
        if (CommonConstant.getApartSingle().contains(customerOrderInfo.getUpperOrderType())
            && BusinessMode.isB2B(customerOrderInfo.getBusinessMode())
        ) {
            waybillNo = orderFeign.getWaybillNoByCustomerOrderNo(customerOrderInfo.getCustomerOrderNo()).data();
        }
        waybillNo = null == waybillNo ? 0 : waybillNo;
        //如果配置为不拆单，直接生成订单
        List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
        waybillNo++;
        for (CustomerOrderItem customerOrderItem : items) {
            BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
            SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
            separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
            separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
            separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
            separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
            list.add(separateWarehouseConfirmRequest);
        }
        orderList.add(list);
        if (OrderType.YS != EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType())) {
            if (!ApartType.UNALLOWED.getKey().equals(customerOrderInfo.getApartType())) {
                customerOrderInfo.setApartType(ApartType.NOT_APART.getKey());
            }
            //重新更新状态
            setOrderStatusAudited(customerOrderInfo);
            //orderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "分仓拆单");
        }
        return orderList;

    }


    private List<List<SeparateWarehouseConfirmRequest>> packingSplit(CustomerOrderInfo customerOrderInfo, List<CustomerOrderItem> items) {
        List<BusinessControlParamDetail> data = businessParamHelper.getBusinessControlDetailByIndex("ITEM_FCL_SPLIT_TASK", customerOrderInfo);
        if (org.springframework.util.CollectionUtils.isEmpty(data)) {
            log.info("单号{} 未获取到控制参数",customerOrderInfo.getOrderNo());
            return null;
        }


        List<List<SeparateWarehouseConfirmRequest>> orderList = Lists.newArrayList();
        List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
        for (CustomerOrderItem customerOrderItem : items) {
            BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO)).setScale(4, BigDecimal.ROUND_HALF_UP);
            BigDecimal packingAmount = BigDecimal.ZERO;
            if (CommonConstant.FLAG_YES.equals(customerOrderItem.getItemSplitTaskFlag())){
                packingAmount = customerOrderItem.getPackingDecimalAmount();
                if (packingAmount == null || packingAmount.compareTo(BigDecimal.ZERO) < 1) {
                    packingAmount = customerOrderItem.getPackingAmount() != null ? new BigDecimal(customerOrderItem.getPackingAmount()) : subtract;
                }
            }

            SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
            separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
            separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
            separateWarehouseConfirmRequest.setCount(subtract.doubleValue());

            // 不拆箱时
            if(packingAmount.compareTo(BigDecimal.ZERO) == 0){
                list.add(separateWarehouseConfirmRequest);
                continue;
            }
            packingAmount = packingAmount.setScale(4, BigDecimal.ROUND_HALF_UP);

            separateWarehouseConfirmRequest.setCount(packingAmount.doubleValue());

            // 拆箱不够箱的和不拆箱的一个箱包裹
            if (subtract.remainder(packingAmount).compareTo(BigDecimal.ZERO)!=0){
                separateWarehouseConfirmRequest.setCount(subtract.remainder(packingAmount).doubleValue());
                list.add(separateWarehouseConfirmRequest);
            }

            subtract = subtract.subtract(packingAmount);
            if (subtract.remainder(packingAmount).compareTo(BigDecimal.ZERO) == 0){
                subtract = subtract.add(packingAmount);
            }
            while (subtract.compareTo(BigDecimal.ZERO) > 0){
                List<SeparateWarehouseConfirmRequest> list2 = Lists.newArrayList();
                SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest2 = new SeparateWarehouseConfirmRequest();
                separateWarehouseConfirmRequest2.setCount(packingAmount.doubleValue());
                separateWarehouseConfirmRequest2.setItemCode(customerOrderItem.getItemCode());
                separateWarehouseConfirmRequest2.setItemLineNo(customerOrderItem.getItemLineNo());
                list2.add(separateWarehouseConfirmRequest2);
                orderList.add(list2);
                subtract = subtract.subtract(packingAmount);
            }
        }
        if(ToolUtils.isNotEmpty(list)){
            orderList.add(list);
        }

        return orderList;
    }

    public List<String> validateSameWarehouseCode(String customerCode, String targetCustomerCode, List<String> whCodes) {
        WmsSameWhCodeValidateDto whCodeValidateDto = new WmsSameWhCodeValidateDto();
        whCodeValidateDto.setCustomerCode(customerCode);
        whCodeValidateDto.setTargetCustomerCode(targetCustomerCode);
        whCodeValidateDto.setWhCode(whCodes);
        JsonResponse<String> response = otpService.validateWmsSameWhCodeValidate(whCodeValidateDto);

        if (String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()).equals(response.getCode())) {
            throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
        }

        if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            throw BusinessException.fail("同仓校验,调用鹊桥失败");
        }
        log.info("validateWmsSameWhCodeValidate==============>{}", response.toString());
        String stringResult = response.data();
        OpenJsonResponse<String> openJsonResponse = JSON.parseObject(stringResult, new TypeReference<OpenJsonResponse<String>>() {
        });

        if (!"0".equals(openJsonResponse.getCode())) {
            throw BusinessException.fail("同仓校验失败:" + stringResult);
        }

        List<String> list1 = JSON.parseObject(openJsonResponse.data(), List.class);
        return list1;

    }


}
