package com.midea.logistics.otp.order.common.aop;

import com.midea.logistics.otp.enums.ApartType;
import com.midea.logistics.otp.enums.ValuedStatus;
import com.midea.logistics.otp.enums.VdOrderOperateType;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.GreenIdentifyFlowHelper;
import com.midea.logistics.otp.order.domain.bean.ValueAddedService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.thread.ThreadLocals;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * ClassName: VdOrderFlowAspect
 * Description: 用于增值服务订单审核流程的日志记录和异常时状态更新
 *
 * <AUTHOR>
 * @date 2024/5/17 16:00
 */
@Aspect
@Component
@Slf4j
public class VdOrderFlowAspect {
    @Autowired
    private GreenIdentifyFlowHelper greenIdentifyFlowHelper;

    private final String POINT_CUT = "@annotation(com.midea.logistics.otp.order.common.aop.VdOrderFlow)";

    /**
     * : @Around环绕通知
     * : @Before通知执行
     * : @Before通知执行结束
     * : @Around环绕通知执行结束
     * : @After后置通知执行了!
     * : @AfterReturning第一个后置返回通知的返回值：18
     */
    @Pointcut(POINT_CUT)
    public void pointCut() {
    }

    /**
     * !@绿色鉴定 - 12、切面
     * 环绕通知：
     * 注意:Spring AOP的环绕通知会影响到AfterThrowing通知的运行,不要同时使用
     * <p>
     * 环绕通知非常强大，可以决定目标方法是否执行，什么时候执行，执行时是否需要替换方法参数，执行完毕是否需要替换返回值。
     * 环绕通知第一个参数必须是org.aspectj.lang.ProceedingJoinPoint类型
     */
    @Around(value = POINT_CUT)
    public Object doAroundAdvice(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        Optional<ValueAddedService> first = Arrays.stream(args).filter(Objects::nonNull).filter(arg -> arg instanceof ValueAddedService).map(arg -> (ValueAddedService) arg).findFirst();
        if (!first.isPresent()) {
            throw BusinessException.fail("流程入参不是增值服务订单，无法执行");
        }
        ValueAddedService valueAddedService = first.get();
        VdOrderFlow vdOrderFlow = getVdOrderAnnotation(point);
        FlowListenerParam result = null;
        Object obj = null;
        Throwable exception = null;
        try {
            //方法执行前先清除一下线程变量
            clear();
            //目标方法执行
            obj = point.proceed();
            Object flow = ThreadLocals.get("flow");
            if (flow instanceof FlowListenerParam) {
                result = (FlowListenerParam) flow;
                result.setFlowStatus(FlowStatus.SUCCESS);
            }
            if (result == null) {
                result = new FlowListenerParam(FlowStatus.SUCCESS, "");
            }
        } catch (Throwable throwable) {
            exception = throwable;
            result = new FlowListenerParam();
            result.setFlowStatus(FlowStatus.FAILD);
            result.setErrorMsg(throwable.getMessage());
        } finally {
            //方法执行完清除线程变量
            clear();
        }

        //保存日志和更新状态
        saveLogAndChangeStatus(valueAddedService, result, vdOrderFlow);

        if (exception != null) {
            throw exception;
        }

        return obj;
    }

    private void clear() {
        ThreadLocals.remove("flow");
    }

    private void saveLogAndChangeStatus(ValueAddedService valueAddedService, FlowListenerParam result, VdOrderFlow vdOrderFlow) {
        boolean isSuccess = result.getFlowStatus() == FlowStatus.SUCCESS;
        String msg = result.getErrorMsg();
        if (StringUtils.isNotBlank(msg) && msg.length() > 500) {
            msg = msg.substring(0, 500);
        }
        String operateFlag = isSuccess ? "Y" : "N";

        //自动分仓失败，转手工分仓
        if (VdOrderOperateType.ORDER_SPLIT.equals(vdOrderFlow.node()) && !isSuccess) {
            greenIdentifyFlowHelper.updateStatus(valueAddedService, ValuedStatus.PREPARE_SEPARATE.getKey(), ApartType.HANDLE.getKey(), msg);
            greenIdentifyFlowHelper.saveLog(valueAddedService, vdOrderFlow.node(), operateFlag, msg);
            return;
        }

        //更新订单
        if (!isSuccess) {
            greenIdentifyFlowHelper.updateStatus(valueAddedService, ValuedStatus.EXCEPTION.getKey(), null, msg);
        }
        //记录日志
        greenIdentifyFlowHelper.saveLog(valueAddedService, vdOrderFlow.node(), operateFlag, msg);
    }

    private VdOrderFlow getVdOrderAnnotation(ProceedingJoinPoint point) {
        Signature signature = point.getSignature();
        VdOrderFlow vdOrderFlow = null;
        if (signature instanceof MethodSignature) {
            MethodSignature methodSignature = (MethodSignature) signature;
            // 被切的方法
            Method method = methodSignature.getMethod();
            // 返回类型
            Class<?> methodReturnType = method.getReturnType();
            //if (methodReturnType != CustomerOrderInfoExt.class && methodReturnType != OrderInfoExt.class) {
            //    throw BusinessException.fail("流程出参不是父单也不是子单，无法执行");
            //}
            vdOrderFlow = methodSignature.getMethod().getAnnotation(VdOrderFlow.class);
        }
        if(null == vdOrderFlow){
            throw BusinessException.fail("流程出参找不到该节点信息");
        }
        return vdOrderFlow;
    }


}
