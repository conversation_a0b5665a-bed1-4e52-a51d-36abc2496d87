package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule;
import com.midea.logistics.otp.rule.domain.request.LoadingFeeRuleRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: LoadingFeeRuleFeign
 * Author: liwende
 * Date: 2019-5-23 19:19:00
 * Description:装卸费计费规则 feign
 */
@FeignClient(value = "logistics-otp-rule-service",url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface LoadingFeeRuleFeign {

    @RequestMapping(value = "/loadingFeeRule", method = RequestMethod.POST)
    JsonResponse create(@RequestBody LoadingFeeRule loadingFeeRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/loadingFeeRule/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/loadingFeeRule/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody LoadingFeeRule loadingFeeRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/loadingFeeRule/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/loadingFeeRules", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap LoadingFeeRuleRequest loadingFeeRuleRequest);

    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/loadingFeeRule/page", method = RequestMethod.GET)
    JsonResponse searchPage(@SpringQueryMap LoadingFeeRuleRequest loadingFeeRuleRequest);

    /**
     * 批量新增和批量修改统一保存
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/loadingFeeRule/batchCreateOrUpdate", method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(/**@Valid*/@RequestBody List<LoadingFeeRuleRequest> loadingFeeRuleRequests);

    /**
     * 批量删除
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/loadingFeeRules/{id}", method = RequestMethod.DELETE)
    JsonResponse batchDelete(@PathVariable("id") Long[] ids);
}
