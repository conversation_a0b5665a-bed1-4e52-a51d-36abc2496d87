package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.StandardAgingNode;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©1968-2023 Midea Group,IT
 *
 * @Description:
 * @FileName: StandardAgingNodeFeign
 * @Author: fengxw26
 * @Date: 2023/2/20 16:50
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "StandardAgingNodeFeign")
public interface StandardAgingNodeFeign {

    /**
     * 分页查询
     *
     * @param entity
     * @return
     */
    @PostMapping("/custom/standardAgingNode/searchPage")
    JsonResponse searchPage(@RequestBody StandardAgingNode entity);

    /**
     * 批量新增或修改
     *
     * @param entityList
     * @return
     */
    @PostMapping("/custom/standardAgingNode/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<StandardAgingNode> entityList);

    /**
     * 批量删除
     *
     * @param entityList
     * @return
     */
    @PostMapping("/custom/standardAgingNode/deleteBatch")
    JsonResponse deleteBatch(@RequestBody List<StandardAgingNode> entityList);
}
