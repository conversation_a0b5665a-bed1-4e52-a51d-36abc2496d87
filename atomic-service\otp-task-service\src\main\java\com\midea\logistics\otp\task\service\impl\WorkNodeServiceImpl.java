package com.midea.logistics.otp.task.service.impl;

import com.midea.logistics.otp.task.domain.bean.WorkNode;
import com.midea.logistics.otp.task.mapper.common.WorkNodeMapper;
import com.midea.logistics.otp.task.service.IWorkNodeService;
import com.mideaframework.core.web.PageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: WorkNodeServiceImpl
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:WorkNodeServiceImpl类
 */
@Service
public class WorkNodeServiceImpl implements IWorkNodeService {


    private static final Logger logger = LoggerFactory.getLogger(WorkNodeServiceImpl.class);

    @Autowired
    private WorkNodeMapper workNodeMapper;


    @Override
    @Transactional(readOnly = false)
    public Integer saveWorkNode(WorkNode workNode) {

        if (null == workNode) {
            logger.warn("save workNode, but workNode is null...");
            return 0;
        }

        return workNodeMapper.save(workNode);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteWorkNode(WorkNode workNode) {

        if (null == workNode || null == workNode.getId()) {
            logger.warn("delete workNode, but workNode is null  or workNode id is null...");
            return 0;
        }

        return workNodeMapper.deleteById(workNode);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateWorkNode(WorkNode workNode) {

        if (null == workNode || null == workNode.getId()) {
            logger.warn("update workNode, but workNode is null  or workNode id is null...");
            return 0;
        }

        return workNodeMapper.updateById(workNode);
    }

    @Override
    public WorkNode selectOneWorkNode(WorkNode workNode) {
        if (workNode == null) {
            logger.warn("select workNode one, but workNode is null ...");
            return null;
        }
        workNode = workNodeMapper.selectOne(workNode);
        return workNode;
    }

    @Override
    public PageResponse<WorkNode> selectWorkNodePage(WorkNode workNode) {
        PageResponse<WorkNode> pagingData = new PageResponse<>();

        if (null == workNode) {
            logger.warn("select workNode page, but workNode is null...");
            return pagingData;
        }

        Integer queryCount = workNodeMapper.selectByIndexCount(workNode);
        if (null != queryCount && queryCount <= 0) {
            logger.info("select workNode page , but count {} == 0 ...", queryCount);
            return pagingData;
        }

        List<WorkNode> workNodes = selectWorkNodeByIndex(workNode);
        pagingData.init(workNode.getPageNo(), workNode.getPageSize(), queryCount, workNodes);
        return pagingData;
    }

    @Override
    public List<WorkNode> selectWorkNodeByIndex(WorkNode workNode) {
        List<WorkNode> workNodes = new ArrayList<>();
        if (workNode == null) {
            logger.warn("select workNode by index, but workNode is null ...");
            return workNodes;
        }

        workNodes = workNodeMapper.selectByIndex(workNode);

        return workNodes;
    }

    @Override
    public int countWorkNodeByIndex(WorkNode workNode) {
        int count = 0;
        if (workNode == null) {
            logger.warn("count workNode by index, but workNode is null ...");
            return count;
        }

        count = workNodeMapper.selectByIndexCount(workNode);

        return count;
    }

    @Override
    public Integer insertBatch(List<WorkNode> workNodes) {
        if (CollectionUtils.isEmpty(workNodes)) {
            logger.warn("insertBatch workNodes, but workNodes is null ...");
            return 0;
        }
        return workNodeMapper.insertBatch(workNodes);
    }

    @Override
    public Integer batchUpdate(List<WorkNode> workNodes) {
        if (CollectionUtils.isEmpty(workNodes)) {
            logger.warn("batchUpdate workNodes, but workNodes is null ...");
            return 0;
        }
        return workNodeMapper.batchUpdate(workNodes);
    }

    @Override
    public Integer deleteBatch(List<WorkNode> workNodes) {
        if (CollectionUtils.isEmpty(workNodes)) {
            logger.warn("deleteBatch workNodes, but workNodes is null ...");
            return 0;
        }
        return workNodeMapper.deleteBatch(workNodes);
    }

}