<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskAddressMapper">

    <sql id="searchFieldsSql">
            `receiver_tel` AS  receiverTel,
            `receiver_town_code` AS  receiverTownCode,
            `receiver_district_code` AS  receiverDistrictCode,
            `network_name` AS  networkName,
            `final_city_name` AS  finalCityName,
            `final_name` AS  finalName,
            `final_district_name` AS  finalDistrictName,
            `network_city_code` AS  networkCityCode,
            `final_country_name` AS  finalCountryName,
            `final_district_code` AS  finalDistrictCode,
            `delete_flag` AS  deleteFlag,
            `sender_name` AS  senderName,
            `sender_town_code` AS  senderTownCode,
            `final_province_code` AS  finalProvinceCode,
            `network_district_code` AS  networkDistrictCode,
            `id` AS  id,
            `sender_district_code` AS  senderDistrictCode,
            `network_phone` AS  networkPhone,
            `start_lng` AS  startLng,
            `final_tel` AS  finalTel,
            `start_lat` AS  startLat,
            `receiver_country_code` AS  receiverCountryCode,
            `network_town_name` AS  networkTownName,
            `face_sheet_hold` AS  faceSheetHold,
            `version` AS  version,
            `final_country_code` AS  finalCountryCode,
            `final_city_code` AS  finalCityCode,
            `update_user_code` AS  updateUserCode,
            `final_province_name` AS  finalProvinceName,
            `network_city_name` AS  networkCityName,
            `network_contact` AS  networkContact,
            `sender_country_name` AS  senderCountryName,
            `receiver_country_name` AS  receiverCountryName,
            `sender_province_name` AS  senderProvinceName,
            `sender_city_code` AS  senderCityCode,
            `network_town_code` AS  networkTownCode,
            `receiver_city_code` AS  receiverCityCode,
            `network_province_name` AS  networkProvinceName,
            `remark` AS  remark,
            `final_town_code` AS  finalTownCode,
            `sender_tel` AS  senderTel,
            `final_town_name` AS  finalTownName,
            `receiver_virtual_mobile` AS  receiverVirtualMobile,
            `end_lat` AS  endLat,
            `network_province_code` AS  networkProvinceCode,
            `task_no` AS  taskNo,
            `network_district_name` AS  networkDistrictName,
            `sender_country_code` AS  senderCountryCode,
            `end_lng` AS  endLng,
            `receiver_province_name` AS  receiverProvinceName,
            `sender_city_name` AS  senderCityName,
            `receiver_name` AS  receiverName,
            `create_user_code` AS  createUserCode,
            `receiver_mobile` AS  receiverMobile,
            `sender_mobile` AS  senderMobile,
            `update_time` AS  updateTime,
            `final_mobile` AS  finalMobile,
            `receiver_detail_addr` AS  receiverDetailAddr,
            `sender_detail_addr` AS  senderDetailAddr,
            `network_tel` AS  networkTel,
            `network_addr` AS  networkAddr,
            `create_time` AS  createTime,
            `receiver_city_name` AS  receiverCityName,
            `network_code` AS  networkCode,
            `final_detail_addr` AS  finalDetailAddr,
            `sender_town_name` AS  senderTownName,
            `receiver_district_name` AS  receiverDistrictName,
            `receiver_province_code` AS  receiverProvinceCode,
            `receiver_town_name` AS  receiverTownName,
            `sender_district_name` AS  senderDistrictName,
            `sender_province_code` AS  senderProvinceCode,
            `sender_virtual_mobile` AS  senderVirtualMobile
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="receiverTel !=null and receiverTel != ''">
            and `receiver_tel` =#{receiverTel}
        </if>
        <if test="receiverTownCode !=null and receiverTownCode != ''">
            and `receiver_town_code` =#{receiverTownCode}
        </if>
        <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
            and `receiver_district_code` =#{receiverDistrictCode}
        </if>
        <if test="networkName !=null and networkName != ''">
            and `network_name` =#{networkName}
        </if>
        <if test="finalCityName !=null and finalCityName != ''">
            and `final_city_name` =#{finalCityName}
        </if>
        <if test="finalName !=null and finalName != ''">
            and `final_name` =#{finalName}
        </if>
        <if test="finalDistrictName !=null and finalDistrictName != ''">
            and `final_district_name` =#{finalDistrictName}
        </if>
        <if test="networkCityCode !=null and networkCityCode != ''">
            and `network_city_code` =#{networkCityCode}
        </if>
        <if test="finalCountryName !=null and finalCountryName != ''">
            and `final_country_name` =#{finalCountryName}
        </if>
        <if test="finalDistrictCode !=null and finalDistrictCode != ''">
            and `final_district_code` =#{finalDistrictCode}
        </if>
        <if test="senderName !=null and senderName != ''">
            and `sender_name` =#{senderName}
        </if>
        <if test="senderTownCode !=null and senderTownCode != ''">
            and `sender_town_code` =#{senderTownCode}
        </if>
        <if test="finalProvinceCode !=null and finalProvinceCode != ''">
            and `final_province_code` =#{finalProvinceCode}
        </if>
        <if test="networkDistrictCode !=null and networkDistrictCode != ''">
            and `network_district_code` =#{networkDistrictCode}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="senderDistrictCode !=null and senderDistrictCode != ''">
            and `sender_district_code` =#{senderDistrictCode}
        </if>
        <if test="networkPhone !=null and networkPhone != ''">
            and `network_phone` =#{networkPhone}
        </if>
        <if test="startLng !=null">
            and `start_lng` =#{startLng}
        </if>
        <if test="finalTel !=null and finalTel != ''">
            and `final_tel` =#{finalTel}
        </if>
        <if test="startLat !=null">
            and `start_lat` =#{startLat}
        </if>
        <if test="receiverCountryCode !=null and receiverCountryCode != ''">
            and `receiver_country_code` =#{receiverCountryCode}
        </if>
        <if test="networkTownName !=null and networkTownName != ''">
            and `network_town_name` =#{networkTownName}
        </if>
        <if test="faceSheetHold !=null">
            and `face_sheet_hold` =#{faceSheetHold}
        </if>
        <if test="finalCountryCode !=null and finalCountryCode != ''">
            and `final_country_code` =#{finalCountryCode}
        </if>
        <if test="finalCityCode !=null and finalCityCode != ''">
            and `final_city_code` =#{finalCityCode}
        </if>
        <if test="finalProvinceName !=null and finalProvinceName != ''">
            and `final_province_name` =#{finalProvinceName}
        </if>
        <if test="networkCityName !=null and networkCityName != ''">
            and `network_city_name` =#{networkCityName}
        </if>
        <if test="networkContact !=null and networkContact != ''">
            and `network_contact` =#{networkContact}
        </if>
        <if test="senderCountryName !=null and senderCountryName != ''">
            and `sender_country_name` =#{senderCountryName}
        </if>
        <if test="receiverCountryName !=null and receiverCountryName != ''">
            and `receiver_country_name` =#{receiverCountryName}
        </if>
        <if test="senderProvinceName !=null and senderProvinceName != ''">
            and `sender_province_name` =#{senderProvinceName}
        </if>
        <if test="senderCityCode !=null and senderCityCode != ''">
            and `sender_city_code` =#{senderCityCode}
        </if>
        <if test="networkTownCode !=null and networkTownCode != ''">
            and `network_town_code` =#{networkTownCode}
        </if>
        <if test="receiverCityCode !=null and receiverCityCode != ''">
            and `receiver_city_code` =#{receiverCityCode}
        </if>
        <if test="networkProvinceName !=null and networkProvinceName != ''">
            and `network_province_name` =#{networkProvinceName}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="finalTownCode !=null and finalTownCode != ''">
            and `final_town_code` =#{finalTownCode}
        </if>
        <if test="senderTel !=null and senderTel != ''">
            and `sender_tel` =#{senderTel}
        </if>
        <if test="finalTownName !=null and finalTownName != ''">
            and `final_town_name` =#{finalTownName}
        </if>
        <if test="receiverVirtualMobile !=null and receiverVirtualMobile != ''">
            and `receiver_virtual_mobile` =#{receiverVirtualMobile}
        </if>
        <if test="endLat !=null">
            and `end_lat` =#{endLat}
        </if>
        <if test="networkProvinceCode !=null and networkProvinceCode != ''">
            and `network_province_code` =#{networkProvinceCode}
        </if>
        <if test="taskNo !=null and taskNo != ''">
            and `task_no` =#{taskNo}
        </if>
        <if test="networkDistrictName !=null and networkDistrictName != ''">
            and `network_district_name` =#{networkDistrictName}
        </if>
        <if test="senderCountryCode !=null and senderCountryCode != ''">
            and `sender_country_code` =#{senderCountryCode}
        </if>
        <if test="endLng !=null">
            and `end_lng` =#{endLng}
        </if>
        <if test="receiverProvinceName !=null and receiverProvinceName != ''">
            and `receiver_province_name` =#{receiverProvinceName}
        </if>
        <if test="senderCityName !=null and senderCityName != ''">
            and `sender_city_name` =#{senderCityName}
        </if>
        <if test="receiverName !=null and receiverName != ''">
            and `receiver_name` =#{receiverName}
        </if>
        <if test="receiverMobile !=null and receiverMobile != ''">
            and `receiver_mobile` =#{receiverMobile}
        </if>
        <if test="senderMobile !=null and senderMobile != ''">
            and `sender_mobile` =#{senderMobile}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="finalMobile !=null and finalMobile != ''">
            and `final_mobile` =#{finalMobile}
        </if>
        <if test="receiverDetailAddr !=null and receiverDetailAddr != ''">
            and `receiver_detail_addr` =#{receiverDetailAddr}
        </if>
        <if test="senderDetailAddr !=null and senderDetailAddr != ''">
            and `sender_detail_addr` =#{senderDetailAddr}
        </if>
        <if test="networkTel !=null and networkTel != ''">
            and `network_tel` =#{networkTel}
        </if>
        <if test="networkAddr !=null and networkAddr != ''">
            and `network_addr` =#{networkAddr}
        </if>
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="receiverCityName !=null and receiverCityName != ''">
            and `receiver_city_name` =#{receiverCityName}
        </if>
        <if test="networkCode !=null and networkCode != ''">
            and `network_code` =#{networkCode}
        </if>
        <if test="finalDetailAddr !=null and finalDetailAddr != ''">
            and `final_detail_addr` =#{finalDetailAddr}
        </if>
        <if test="senderTownName !=null and senderTownName != ''">
            and `sender_town_name` =#{senderTownName}
        </if>
        <if test="receiverDistrictName !=null and receiverDistrictName != ''">
            and `receiver_district_name` =#{receiverDistrictName}
        </if>
        <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
            and `receiver_province_code` =#{receiverProvinceCode}
        </if>
        <if test="receiverTownName !=null and receiverTownName != ''">
            and `receiver_town_name` =#{receiverTownName}
        </if>
        <if test="senderDistrictName !=null and senderDistrictName != ''">
            and `sender_district_name` =#{senderDistrictName}
        </if>
        <if test="senderProvinceCode !=null and senderProvinceCode != ''">
            and `sender_province_code` =#{senderProvinceCode}
        </if>
        <if test="senderVirtualMobile !=null and senderVirtualMobile != ''">
            and `sender_virtual_mobile` =#{senderVirtualMobile}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="receiverTel !=null and receiverTel != ''">
                `receiver_tel` = #{receiverTel},
            </if>
            <if test="receiverTownCode !=null and receiverTownCode != ''">
                `receiver_town_code` = #{receiverTownCode},
            </if>
            <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
                `receiver_district_code` = #{receiverDistrictCode},
            </if>
            <if test="networkName !=null and networkName != ''">
                `network_name` = #{networkName},
            </if>
            <if test="finalCityName !=null and finalCityName != ''">
                `final_city_name` = #{finalCityName},
            </if>
            <if test="finalName !=null and finalName != ''">
                `final_name` = #{finalName},
            </if>
            <if test="finalDistrictName !=null and finalDistrictName != ''">
                `final_district_name` = #{finalDistrictName},
            </if>
            <if test="networkCityCode !=null and networkCityCode != ''">
                `network_city_code` = #{networkCityCode},
            </if>
            <if test="finalCountryName !=null and finalCountryName != ''">
                `final_country_name` = #{finalCountryName},
            </if>
            <if test="finalDistrictCode !=null and finalDistrictCode != ''">
                `final_district_code` = #{finalDistrictCode},
            </if>
            <if test="senderName !=null and senderName != ''">
                `sender_name` = #{senderName},
            </if>
            <if test="senderTownCode !=null and senderTownCode != ''">
                `sender_town_code` = #{senderTownCode},
            </if>
            <if test="finalProvinceCode !=null and finalProvinceCode != ''">
                `final_province_code` = #{finalProvinceCode},
            </if>
            <if test="networkDistrictCode !=null and networkDistrictCode != ''">
                `network_district_code` = #{networkDistrictCode},
            </if>
            <if test="senderDistrictCode !=null and senderDistrictCode != ''">
                `sender_district_code` = #{senderDistrictCode},
            </if>
            <if test="networkPhone !=null and networkPhone != ''">
                `network_phone` = #{networkPhone},
            </if>
            <if test="startLng != null">
                `start_lng` = #{startLng},
            </if>
            <if test="finalTel !=null and finalTel != ''">
                `final_tel` = #{finalTel},
            </if>
            <if test="startLat != null">
                `start_lat` = #{startLat},
            </if>
            <if test="receiverCountryCode !=null and receiverCountryCode != ''">
                `receiver_country_code` = #{receiverCountryCode},
            </if>
            <if test="networkTownName !=null and networkTownName != ''">
                `network_town_name` = #{networkTownName},
            </if>
            <if test="faceSheetHold != null">
                `face_sheet_hold` = #{faceSheetHold},
            </if>
            <if test="finalCountryCode !=null and finalCountryCode != ''">
                `final_country_code` = #{finalCountryCode},
            </if>
            <if test="finalCityCode !=null and finalCityCode != ''">
                `final_city_code` = #{finalCityCode},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="finalProvinceName !=null and finalProvinceName != ''">
                `final_province_name` = #{finalProvinceName},
            </if>
            <if test="networkCityName !=null and networkCityName != ''">
                `network_city_name` = #{networkCityName},
            </if>
            <if test="networkContact !=null and networkContact != ''">
                `network_contact` = #{networkContact},
            </if>
            <if test="senderCountryName !=null and senderCountryName != ''">
                `sender_country_name` = #{senderCountryName},
            </if>
            <if test="receiverCountryName !=null and receiverCountryName != ''">
                `receiver_country_name` = #{receiverCountryName},
            </if>
            <if test="senderProvinceName !=null and senderProvinceName != ''">
                `sender_province_name` = #{senderProvinceName},
            </if>
            <if test="senderCityCode !=null and senderCityCode != ''">
                `sender_city_code` = #{senderCityCode},
            </if>
            <if test="networkTownCode !=null and networkTownCode != ''">
                `network_town_code` = #{networkTownCode},
            </if>
            <if test="receiverCityCode !=null and receiverCityCode != ''">
                `receiver_city_code` = #{receiverCityCode},
            </if>
            <if test="networkProvinceName !=null and networkProvinceName != ''">
                `network_province_name` = #{networkProvinceName},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="finalTownCode !=null and finalTownCode != ''">
                `final_town_code` = #{finalTownCode},
            </if>
            <if test="senderTel !=null and senderTel != ''">
                `sender_tel` = #{senderTel},
            </if>
            <if test="finalTownName !=null and finalTownName != ''">
                `final_town_name` = #{finalTownName},
            </if>
            <if test="receiverVirtualMobile !=null and receiverVirtualMobile != ''">
                `receiver_virtual_mobile` = #{receiverVirtualMobile},
            </if>
            <if test="endLat != null">
                `end_lat` = #{endLat},
            </if>
            <if test="networkProvinceCode !=null and networkProvinceCode != ''">
                `network_province_code` = #{networkProvinceCode},
            </if>
            <if test="taskNo !=null and taskNo != ''">
                `task_no` = #{taskNo},
            </if>
            <if test="networkDistrictName !=null and networkDistrictName != ''">
                `network_district_name` = #{networkDistrictName},
            </if>
            <if test="senderCountryCode !=null and senderCountryCode != ''">
                `sender_country_code` = #{senderCountryCode},
            </if>
            <if test="endLng != null">
                `end_lng` = #{endLng},
            </if>
            <if test="receiverProvinceName !=null and receiverProvinceName != ''">
                `receiver_province_name` = #{receiverProvinceName},
            </if>
            <if test="senderCityName !=null and senderCityName != ''">
                `sender_city_name` = #{senderCityName},
            </if>
            <if test="receiverName !=null and receiverName != ''">
                `receiver_name` = #{receiverName},
            </if>
            <if test="receiverMobile !=null and receiverMobile != ''">
                `receiver_mobile` = #{receiverMobile},
            </if>
            <if test="senderMobile !=null and senderMobile != ''">
                `sender_mobile` = #{senderMobile},
            </if>
            <if test="finalMobile !=null and finalMobile != ''">
                `final_mobile` = #{finalMobile},
            </if>
            <if test="receiverDetailAddr !=null and receiverDetailAddr != ''">
                `receiver_detail_addr` = #{receiverDetailAddr},
            </if>
            <if test="senderDetailAddr !=null and senderDetailAddr != ''">
                `sender_detail_addr` = #{senderDetailAddr},
            </if>
            <if test="networkTel !=null and networkTel != ''">
                `network_tel` = #{networkTel},
            </if>
            <if test="networkAddr !=null and networkAddr != ''">
                `network_addr` = #{networkAddr},
            </if>
            <if test="receiverCityName !=null and receiverCityName != ''">
                `receiver_city_name` = #{receiverCityName},
            </if>
            <if test="networkCode !=null and networkCode != ''">
                `network_code` = #{networkCode},
            </if>
            <if test="finalDetailAddr !=null and finalDetailAddr != ''">
                `final_detail_addr` = #{finalDetailAddr},
            </if>
            <if test="senderTownName !=null and senderTownName != ''">
                `sender_town_name` = #{senderTownName},
            </if>
            <if test="receiverDistrictName !=null and receiverDistrictName != ''">
                `receiver_district_name` = #{receiverDistrictName},
            </if>
            <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
                `receiver_province_code` = #{receiverProvinceCode},
            </if>
            <if test="receiverTownName !=null and receiverTownName != ''">
                `receiver_town_name` = #{receiverTownName},
            </if>
            <if test="senderDistrictName !=null and senderDistrictName != ''">
                `sender_district_name` = #{senderDistrictName},
            </if>
            <if test="senderProvinceCode !=null and senderProvinceCode != ''">
                `sender_province_code` = #{senderProvinceCode},
            </if>
            <if test="senderVirtualMobile !=null and senderVirtualMobile != ''">
                `sender_virtual_mobile` = #{senderVirtualMobile},
            </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
            <if test="receiverTel !=null">
                `receiver_tel` = #{receiverTel},
            </if>
            <if test="receiverTownCode !=null">
                `receiver_town_code` = #{receiverTownCode},
            </if>
            <if test="receiverDistrictCode !=null">
                `receiver_district_code` = #{receiverDistrictCode},
            </if>
            <if test="networkName !=null">
                `network_name` = #{networkName},
            </if>
            <if test="finalCityName !=null">
                `final_city_name` = #{finalCityName},
            </if>
            <if test="finalName !=null">
                `final_name` = #{finalName},
            </if>
            <if test="finalDistrictName !=null">
                `final_district_name` = #{finalDistrictName},
            </if>
            <if test="networkCityCode !=null">
                `network_city_code` = #{networkCityCode},
            </if>
            <if test="finalCountryName !=null">
                `final_country_name` = #{finalCountryName},
            </if>
            <if test="finalDistrictCode !=null">
                `final_district_code` = #{finalDistrictCode},
            </if>
            <if test="senderName !=null">
                `sender_name` = #{senderName},
            </if>
            <if test="senderTownCode !=null">
                `sender_town_code` = #{senderTownCode},
            </if>
            <if test="finalProvinceCode !=null">
                `final_province_code` = #{finalProvinceCode},
            </if>
            <if test="networkDistrictCode !=null">
                `network_district_code` = #{networkDistrictCode},
            </if>
            <if test="senderDistrictCode !=null">
                `sender_district_code` = #{senderDistrictCode},
            </if>
            <if test="networkPhone !=null">
                `network_phone` = #{networkPhone},
            </if>
            <if test="startLng != null">
                `start_lng` = #{startLng},
            </if>
            <if test="finalTel !=null">
                `final_tel` = #{finalTel},
            </if>
            <if test="startLat != null">
                `start_lat` = #{startLat},
            </if>
            <if test="receiverCountryCode !=null">
                `receiver_country_code` = #{receiverCountryCode},
            </if>
            <if test="networkTownName !=null">
                `network_town_name` = #{networkTownName},
            </if>
            <if test="faceSheetHold != null">
                `face_sheet_hold` = #{faceSheetHold},
            </if>
            <if test="finalCountryCode !=null">
                `final_country_code` = #{finalCountryCode},
            </if>
            <if test="finalCityCode !=null">
                `final_city_code` = #{finalCityCode},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="finalProvinceName !=null">
                `final_province_name` = #{finalProvinceName},
            </if>
            <if test="networkCityName !=null">
                `network_city_name` = #{networkCityName},
            </if>
            <if test="networkContact !=null">
                `network_contact` = #{networkContact},
            </if>
            <if test="senderCountryName !=null">
                `sender_country_name` = #{senderCountryName},
            </if>
            <if test="receiverCountryName !=null">
                `receiver_country_name` = #{receiverCountryName},
            </if>
            <if test="senderProvinceName !=null">
                `sender_province_name` = #{senderProvinceName},
            </if>
            <if test="senderCityCode !=null">
                `sender_city_code` = #{senderCityCode},
            </if>
            <if test="networkTownCode !=null">
                `network_town_code` = #{networkTownCode},
            </if>
            <if test="receiverCityCode !=null">
                `receiver_city_code` = #{receiverCityCode},
            </if>
            <if test="networkProvinceName !=null">
                `network_province_name` = #{networkProvinceName},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="finalTownCode !=null">
                `final_town_code` = #{finalTownCode},
            </if>
            <if test="senderTel !=null">
                `sender_tel` = #{senderTel},
            </if>
            <if test="finalTownName !=null">
                `final_town_name` = #{finalTownName},
            </if>
            <if test="receiverVirtualMobile !=null">
                `receiver_virtual_mobile` = #{receiverVirtualMobile},
            </if>
            <if test="endLat != null">
                `end_lat` = #{endLat},
            </if>
            <if test="networkProvinceCode !=null">
                `network_province_code` = #{networkProvinceCode},
            </if>
            <if test="taskNo !=null">
                `task_no` = #{taskNo},
            </if>
            <if test="networkDistrictName !=null">
                `network_district_name` = #{networkDistrictName},
            </if>
            <if test="senderCountryCode !=null">
                `sender_country_code` = #{senderCountryCode},
            </if>
            <if test="endLng != null">
                `end_lng` = #{endLng},
            </if>
            <if test="receiverProvinceName !=null">
                `receiver_province_name` = #{receiverProvinceName},
            </if>
            <if test="senderCityName !=null">
                `sender_city_name` = #{senderCityName},
            </if>
            <if test="receiverName !=null">
                `receiver_name` = #{receiverName},
            </if>
            <if test="receiverMobile !=null">
                `receiver_mobile` = #{receiverMobile},
            </if>
            <if test="senderMobile !=null">
                `sender_mobile` = #{senderMobile},
            </if>
            <if test="finalMobile !=null">
                `final_mobile` = #{finalMobile},
            </if>
            <if test="receiverDetailAddr !=null">
                `receiver_detail_addr` = #{receiverDetailAddr},
            </if>
            <if test="senderDetailAddr !=null">
                `sender_detail_addr` = #{senderDetailAddr},
            </if>
            <if test="networkTel !=null">
                `network_tel` = #{networkTel},
            </if>
            <if test="networkAddr !=null">
                `network_addr` = #{networkAddr},
            </if>
            <if test="receiverCityName !=null">
                `receiver_city_name` = #{receiverCityName},
            </if>
            <if test="networkCode !=null">
                `network_code` = #{networkCode},
            </if>
            <if test="finalDetailAddr !=null">
                `final_detail_addr` = #{finalDetailAddr},
            </if>
            <if test="senderTownName !=null">
                `sender_town_name` = #{senderTownName},
            </if>
            <if test="receiverDistrictName !=null">
                `receiver_district_name` = #{receiverDistrictName},
            </if>
            <if test="receiverProvinceCode !=null">
                `receiver_province_code` = #{receiverProvinceCode},
            </if>
            <if test="receiverTownName !=null">
                `receiver_town_name` = #{receiverTownName},
            </if>
            <if test="senderDistrictName !=null">
                `sender_district_name` = #{senderDistrictName},
            </if>
            <if test="senderProvinceCode !=null">
                `sender_province_code` = #{senderProvinceCode},
            </if>
            <if test="senderVirtualMobile !=null">
                `sender_virtual_mobile` = #{senderVirtualMobile},
            </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from task_address t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.TaskAddress">
        select
        <include refid="searchFieldsSql"/>
        from task_address t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.TaskAddress">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from task_address t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.TaskAddress">
        select
        <include refid="searchFieldsSql"/>
        from task_address t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        task_address t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        task_address t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        task_address t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.TaskAddress" useGeneratedKeys="true" keyProperty="id">
        insert into task_address
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="receiverTel !=null and receiverTel != ''">
                `receiver_tel`,
            </if>

            <if test="receiverTownCode !=null and receiverTownCode != ''">
                `receiver_town_code`,
            </if>

            <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
                `receiver_district_code`,
            </if>

            <if test="networkName !=null and networkName != ''">
                `network_name`,
            </if>

            <if test="finalCityName !=null and finalCityName != ''">
                `final_city_name`,
            </if>

            <if test="finalName !=null and finalName != ''">
                `final_name`,
            </if>

            <if test="finalDistrictName !=null and finalDistrictName != ''">
                `final_district_name`,
            </if>

            <if test="networkCityCode !=null and networkCityCode != ''">
                `network_city_code`,
            </if>

            <if test="finalCountryName !=null and finalCountryName != ''">
                `final_country_name`,
            </if>

            <if test="finalDistrictCode !=null and finalDistrictCode != ''">
                `final_district_code`,
            </if>

            <if test="senderName !=null and senderName != ''">
                `sender_name`,
            </if>

            <if test="senderTownCode !=null and senderTownCode != ''">
                `sender_town_code`,
            </if>

            <if test="finalProvinceCode !=null and finalProvinceCode != ''">
                `final_province_code`,
            </if>

            <if test="networkDistrictCode !=null and networkDistrictCode != ''">
                `network_district_code`,
            </if>

            <if test="senderDistrictCode !=null and senderDistrictCode != ''">
                `sender_district_code`,
            </if>

            <if test="networkPhone !=null and networkPhone != ''">
                `network_phone`,
            </if>

            <if test="startLng != null">
                `start_lng`,
            </if>

            <if test="finalTel !=null and finalTel != ''">
                `final_tel`,
            </if>

            <if test="startLat != null">
                `start_lat`,
            </if>

            <if test="receiverCountryCode !=null and receiverCountryCode != ''">
                `receiver_country_code`,
            </if>

            <if test="networkTownName !=null and networkTownName != ''">
                `network_town_name`,
            </if>

            <if test="faceSheetHold != null">
                `face_sheet_hold`,
            </if>

            <if test="finalCountryCode !=null and finalCountryCode != ''">
                `final_country_code`,
            </if>

            <if test="finalCityCode !=null and finalCityCode != ''">
                `final_city_code`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="finalProvinceName !=null and finalProvinceName != ''">
                `final_province_name`,
            </if>

            <if test="networkCityName !=null and networkCityName != ''">
                `network_city_name`,
            </if>

            <if test="networkContact !=null and networkContact != ''">
                `network_contact`,
            </if>

            <if test="senderCountryName !=null and senderCountryName != ''">
                `sender_country_name`,
            </if>

            <if test="receiverCountryName !=null and receiverCountryName != ''">
                `receiver_country_name`,
            </if>

            <if test="senderProvinceName !=null and senderProvinceName != ''">
                `sender_province_name`,
            </if>

            <if test="senderCityCode !=null and senderCityCode != ''">
                `sender_city_code`,
            </if>

            <if test="networkTownCode !=null and networkTownCode != ''">
                `network_town_code`,
            </if>

            <if test="receiverCityCode !=null and receiverCityCode != ''">
                `receiver_city_code`,
            </if>

            <if test="networkProvinceName !=null and networkProvinceName != ''">
                `network_province_name`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="finalTownCode !=null and finalTownCode != ''">
                `final_town_code`,
            </if>

            <if test="senderTel !=null and senderTel != ''">
                `sender_tel`,
            </if>

            <if test="finalTownName !=null and finalTownName != ''">
                `final_town_name`,
            </if>

            <if test="receiverVirtualMobile !=null and receiverVirtualMobile != ''">
                `receiver_virtual_mobile`,
            </if>

            <if test="endLat != null">
                `end_lat`,
            </if>

            <if test="networkProvinceCode !=null and networkProvinceCode != ''">
                `network_province_code`,
            </if>

            <if test="taskNo !=null and taskNo != ''">
                `task_no`,
            </if>

            <if test="networkDistrictName !=null and networkDistrictName != ''">
                `network_district_name`,
            </if>

            <if test="senderCountryCode !=null and senderCountryCode != ''">
                `sender_country_code`,
            </if>

            <if test="endLng != null">
                `end_lng`,
            </if>

            <if test="receiverProvinceName !=null and receiverProvinceName != ''">
                `receiver_province_name`,
            </if>

            <if test="senderCityName !=null and senderCityName != ''">
                `sender_city_name`,
            </if>

            <if test="receiverName !=null and receiverName != ''">
                `receiver_name`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="receiverMobile !=null and receiverMobile != ''">
                `receiver_mobile`,
            </if>

            <if test="senderMobile !=null and senderMobile != ''">
                `sender_mobile`,
            </if>

            <if test="finalMobile !=null and finalMobile != ''">
                `final_mobile`,
            </if>

            <if test="receiverDetailAddr !=null and receiverDetailAddr != ''">
                `receiver_detail_addr`,
            </if>

            <if test="senderDetailAddr !=null and senderDetailAddr != ''">
                `sender_detail_addr`,
            </if>

            <if test="networkTel !=null and networkTel != ''">
                `network_tel`,
            </if>

            <if test="networkAddr !=null and networkAddr != ''">
                `network_addr`,
            </if>

            <if test="receiverCityName !=null and receiverCityName != ''">
                `receiver_city_name`,
            </if>

            <if test="networkCode !=null and networkCode != ''">
                `network_code`,
            </if>

            <if test="finalDetailAddr !=null and finalDetailAddr != ''">
                `final_detail_addr`,
            </if>

            <if test="senderTownName !=null and senderTownName != ''">
                `sender_town_name`,
            </if>

            <if test="receiverDistrictName !=null and receiverDistrictName != ''">
                `receiver_district_name`,
            </if>

            <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
                `receiver_province_code`,
            </if>

            <if test="receiverTownName !=null and receiverTownName != ''">
                `receiver_town_name`,
            </if>

            <if test="senderDistrictName !=null and senderDistrictName != ''">
                `sender_district_name`,
            </if>

            <if test="senderProvinceCode !=null and senderProvinceCode != ''">
                `sender_province_code`,
            </if>

            <if test="senderVirtualMobile !=null and senderVirtualMobile != ''">
                `sender_virtual_mobile`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="receiverTel !=null and receiverTel != ''">
                #{receiverTel},
            </if>
            <if test="receiverTownCode !=null and receiverTownCode != ''">
                #{receiverTownCode},
            </if>
            <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
                #{receiverDistrictCode},
            </if>
            <if test="networkName !=null and networkName != ''">
                #{networkName},
            </if>
            <if test="finalCityName !=null and finalCityName != ''">
                #{finalCityName},
            </if>
            <if test="finalName !=null and finalName != ''">
                #{finalName},
            </if>
            <if test="finalDistrictName !=null and finalDistrictName != ''">
                #{finalDistrictName},
            </if>
            <if test="networkCityCode !=null and networkCityCode != ''">
                #{networkCityCode},
            </if>
            <if test="finalCountryName !=null and finalCountryName != ''">
                #{finalCountryName},
            </if>
            <if test="finalDistrictCode !=null and finalDistrictCode != ''">
                #{finalDistrictCode},
            </if>
            <if test="senderName !=null and senderName != ''">
                #{senderName},
            </if>
            <if test="senderTownCode !=null and senderTownCode != ''">
                #{senderTownCode},
            </if>
            <if test="finalProvinceCode !=null and finalProvinceCode != ''">
                #{finalProvinceCode},
            </if>
            <if test="networkDistrictCode !=null and networkDistrictCode != ''">
                #{networkDistrictCode},
            </if>
            <if test="senderDistrictCode !=null and senderDistrictCode != ''">
                #{senderDistrictCode},
            </if>
            <if test="networkPhone !=null and networkPhone != ''">
                #{networkPhone},
            </if>
            <if test="startLng != null">
                #{startLng},
            </if>
            <if test="finalTel !=null and finalTel != ''">
                #{finalTel},
            </if>
            <if test="startLat != null">
                #{startLat},
            </if>
            <if test="receiverCountryCode !=null and receiverCountryCode != ''">
                #{receiverCountryCode},
            </if>
            <if test="networkTownName !=null and networkTownName != ''">
                #{networkTownName},
            </if>
            <if test="faceSheetHold != null">
                #{faceSheetHold},
            </if>
            <if test="finalCountryCode !=null and finalCountryCode != ''">
                #{finalCountryCode},
            </if>
            <if test="finalCityCode !=null and finalCityCode != ''">
                #{finalCityCode},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="finalProvinceName !=null and finalProvinceName != ''">
                #{finalProvinceName},
            </if>
            <if test="networkCityName !=null and networkCityName != ''">
                #{networkCityName},
            </if>
            <if test="networkContact !=null and networkContact != ''">
                #{networkContact},
            </if>
            <if test="senderCountryName !=null and senderCountryName != ''">
                #{senderCountryName},
            </if>
            <if test="receiverCountryName !=null and receiverCountryName != ''">
                #{receiverCountryName},
            </if>
            <if test="senderProvinceName !=null and senderProvinceName != ''">
                #{senderProvinceName},
            </if>
            <if test="senderCityCode !=null and senderCityCode != ''">
                #{senderCityCode},
            </if>
            <if test="networkTownCode !=null and networkTownCode != ''">
                #{networkTownCode},
            </if>
            <if test="receiverCityCode !=null and receiverCityCode != ''">
                #{receiverCityCode},
            </if>
            <if test="networkProvinceName !=null and networkProvinceName != ''">
                #{networkProvinceName},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="finalTownCode !=null and finalTownCode != ''">
                #{finalTownCode},
            </if>
            <if test="senderTel !=null and senderTel != ''">
                #{senderTel},
            </if>
            <if test="finalTownName !=null and finalTownName != ''">
                #{finalTownName},
            </if>
            <if test="receiverVirtualMobile !=null and receiverVirtualMobile != ''">
                #{receiverVirtualMobile},
            </if>
            <if test="endLat != null">
                #{endLat},
            </if>
            <if test="networkProvinceCode !=null and networkProvinceCode != ''">
                #{networkProvinceCode},
            </if>
            <if test="taskNo !=null and taskNo != ''">
                #{taskNo},
            </if>
            <if test="networkDistrictName !=null and networkDistrictName != ''">
                #{networkDistrictName},
            </if>
            <if test="senderCountryCode !=null and senderCountryCode != ''">
                #{senderCountryCode},
            </if>
            <if test="endLng != null">
                #{endLng},
            </if>
            <if test="receiverProvinceName !=null and receiverProvinceName != ''">
                #{receiverProvinceName},
            </if>
            <if test="senderCityName !=null and senderCityName != ''">
                #{senderCityName},
            </if>
            <if test="receiverName !=null and receiverName != ''">
                #{receiverName},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="receiverMobile !=null and receiverMobile != ''">
                #{receiverMobile},
            </if>
            <if test="senderMobile !=null and senderMobile != ''">
                #{senderMobile},
            </if>
            <if test="finalMobile !=null and finalMobile != ''">
                #{finalMobile},
            </if>
            <if test="receiverDetailAddr !=null and receiverDetailAddr != ''">
                #{receiverDetailAddr},
            </if>
            <if test="senderDetailAddr !=null and senderDetailAddr != ''">
                #{senderDetailAddr},
            </if>
            <if test="networkTel !=null and networkTel != ''">
                #{networkTel},
            </if>
            <if test="networkAddr !=null and networkAddr != ''">
                #{networkAddr},
            </if>
            <if test="receiverCityName !=null and receiverCityName != ''">
                #{receiverCityName},
            </if>
            <if test="networkCode !=null and networkCode != ''">
                #{networkCode},
            </if>
            <if test="finalDetailAddr !=null and finalDetailAddr != ''">
                #{finalDetailAddr},
            </if>
            <if test="senderTownName !=null and senderTownName != ''">
                #{senderTownName},
            </if>
            <if test="receiverDistrictName !=null and receiverDistrictName != ''">
                #{receiverDistrictName},
            </if>
            <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
                #{receiverProvinceCode},
            </if>
            <if test="receiverTownName !=null and receiverTownName != ''">
                #{receiverTownName},
            </if>
            <if test="senderDistrictName !=null and senderDistrictName != ''">
                #{senderDistrictName},
            </if>
            <if test="senderProvinceCode !=null and senderProvinceCode != ''">
                #{senderProvinceCode},
            </if>
            <if test="senderVirtualMobile !=null and senderVirtualMobile != ''">
                #{senderVirtualMobile},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `receiver_tel`,
            `receiver_town_code`,
            `receiver_district_code`,
            `network_name`,
            `final_city_name`,
            `final_name`,
            `final_district_name`,
            `network_city_code`,
            `final_country_name`,
            `final_district_code`,
            `sender_name`,
            `sender_town_code`,
            `final_province_code`,
            `network_district_code`,
            `sender_district_code`,
            `network_phone`,
            `start_lng`,
            `final_tel`,
            `start_lat`,
            `receiver_country_code`,
            `network_town_name`,
            `face_sheet_hold`,
            `final_country_code`,
            `final_city_code`,
            `update_user_code`,
            `final_province_name`,
            `network_city_name`,
            `network_contact`,
            `sender_country_name`,
            `receiver_country_name`,
            `sender_province_name`,
            `sender_city_code`,
            `network_town_code`,
            `receiver_city_code`,
            `network_province_name`,
            `remark`,
            `final_town_code`,
            `sender_tel`,
            `final_town_name`,
            `receiver_virtual_mobile`,
            `end_lat`,
            `network_province_code`,
            `task_no`,
            `network_district_name`,
            `sender_country_code`,
            `end_lng`,
            `receiver_province_name`,
            `sender_city_name`,
            `receiver_name`,
            `create_user_code`,
            `receiver_mobile`,
            `sender_mobile`,
            `final_mobile`,
            `receiver_detail_addr`,
            `sender_detail_addr`,
            `network_tel`,
            `network_addr`,
            `receiver_city_name`,
            `network_code`,
            `final_detail_addr`,
            `sender_town_name`,
            `receiver_district_name`,
            `receiver_province_code`,
            `receiver_town_name`,
            `sender_district_name`,
            `sender_province_code`,
            `sender_virtual_mobile`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.receiverTel},
            #{item.receiverTownCode},
            #{item.receiverDistrictCode},
            #{item.networkName},
            #{item.finalCityName},
            #{item.finalName},
            #{item.finalDistrictName},
            #{item.networkCityCode},
            #{item.finalCountryName},
            #{item.finalDistrictCode},
            #{item.senderName},
            #{item.senderTownCode},
            #{item.finalProvinceCode},
            #{item.networkDistrictCode},
            #{item.senderDistrictCode},
            #{item.networkPhone},
            #{item.startLng},
            #{item.finalTel},
            #{item.startLat},
            #{item.receiverCountryCode},
            #{item.networkTownName},
            #{item.faceSheetHold},
            #{item.finalCountryCode},
            #{item.finalCityCode},
            #{item.updateUserCode},
            #{item.finalProvinceName},
            #{item.networkCityName},
            #{item.networkContact},
            #{item.senderCountryName},
            #{item.receiverCountryName},
            #{item.senderProvinceName},
            #{item.senderCityCode},
            #{item.networkTownCode},
            #{item.receiverCityCode},
            #{item.networkProvinceName},
            #{item.remark},
            #{item.finalTownCode},
            #{item.senderTel},
            #{item.finalTownName},
            #{item.receiverVirtualMobile},
            #{item.endLat},
            #{item.networkProvinceCode},
            #{item.taskNo},
            #{item.networkDistrictName},
            #{item.senderCountryCode},
            #{item.endLng},
            #{item.receiverProvinceName},
            #{item.senderCityName},
            #{item.receiverName},
            #{item.createUserCode},
            #{item.receiverMobile},
            #{item.senderMobile},
            #{item.finalMobile},
            #{item.receiverDetailAddr},
            #{item.senderDetailAddr},
            #{item.networkTel},
            #{item.networkAddr},
            #{item.receiverCityName},
            #{item.networkCode},
            #{item.finalDetailAddr},
            #{item.senderTownName},
            #{item.receiverDistrictName},
            #{item.receiverProvinceCode},
            #{item.receiverTownName},
            #{item.senderDistrictName},
            #{item.senderProvinceCode},
            #{item.senderVirtualMobile},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        task_address
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.receiverTel !=null and item.receiverTel != ''">
                `receiver_tel`  = #{item.receiverTel},
            </if>
            <if test="item.receiverTownCode !=null and item.receiverTownCode != ''">
                `receiver_town_code`  = #{item.receiverTownCode},
            </if>
            <if test="item.receiverDistrictCode !=null and item.receiverDistrictCode != ''">
                `receiver_district_code`  = #{item.receiverDistrictCode},
            </if>
            <if test="item.networkName !=null and item.networkName != ''">
                `network_name`  = #{item.networkName},
            </if>
            <if test="item.finalCityName !=null and item.finalCityName != ''">
                `final_city_name`  = #{item.finalCityName},
            </if>
            <if test="item.finalName !=null and item.finalName != ''">
                `final_name`  = #{item.finalName},
            </if>
            <if test="item.finalDistrictName !=null and item.finalDistrictName != ''">
                `final_district_name`  = #{item.finalDistrictName},
            </if>
            <if test="item.networkCityCode !=null and item.networkCityCode != ''">
                `network_city_code`  = #{item.networkCityCode},
            </if>
            <if test="item.finalCountryName !=null and item.finalCountryName != ''">
                `final_country_name`  = #{item.finalCountryName},
            </if>
            <if test="item.finalDistrictCode !=null and item.finalDistrictCode != ''">
                `final_district_code`  = #{item.finalDistrictCode},
            </if>
            <if test="item.senderName !=null and item.senderName != ''">
                `sender_name`  = #{item.senderName},
            </if>
            <if test="item.senderTownCode !=null and item.senderTownCode != ''">
                `sender_town_code`  = #{item.senderTownCode},
            </if>
            <if test="item.finalProvinceCode !=null and item.finalProvinceCode != ''">
                `final_province_code`  = #{item.finalProvinceCode},
            </if>
            <if test="item.networkDistrictCode !=null and item.networkDistrictCode != ''">
                `network_district_code`  = #{item.networkDistrictCode},
            </if>
            <if test="item.senderDistrictCode !=null and item.senderDistrictCode != ''">
                `sender_district_code`  = #{item.senderDistrictCode},
            </if>
            <if test="item.networkPhone !=null and item.networkPhone != ''">
                `network_phone`  = #{item.networkPhone},
            </if>
            <if test="item.startLng != null">
                `start_lng`  = #{item.startLng},
            </if>
            <if test="item.finalTel !=null and item.finalTel != ''">
                `final_tel`  = #{item.finalTel},
            </if>
            <if test="item.startLat != null">
                `start_lat`  = #{item.startLat},
            </if>
            <if test="item.receiverCountryCode !=null and item.receiverCountryCode != ''">
                `receiver_country_code`  = #{item.receiverCountryCode},
            </if>
            <if test="item.networkTownName !=null and item.networkTownName != ''">
                `network_town_name`  = #{item.networkTownName},
            </if>
            <if test="item.faceSheetHold != null">
                `face_sheet_hold`  = #{item.faceSheetHold},
            </if>
            <if test="item.finalCountryCode !=null and item.finalCountryCode != ''">
                `final_country_code`  = #{item.finalCountryCode},
            </if>
            <if test="item.finalCityCode !=null and item.finalCityCode != ''">
                `final_city_code`  = #{item.finalCityCode},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code`  = #{item.updateUserCode},
            </if>
            <if test="item.finalProvinceName !=null and item.finalProvinceName != ''">
                `final_province_name`  = #{item.finalProvinceName},
            </if>
            <if test="item.networkCityName !=null and item.networkCityName != ''">
                `network_city_name`  = #{item.networkCityName},
            </if>
            <if test="item.networkContact !=null and item.networkContact != ''">
                `network_contact`  = #{item.networkContact},
            </if>
            <if test="item.senderCountryName !=null and item.senderCountryName != ''">
                `sender_country_name`  = #{item.senderCountryName},
            </if>
            <if test="item.receiverCountryName !=null and item.receiverCountryName != ''">
                `receiver_country_name`  = #{item.receiverCountryName},
            </if>
            <if test="item.senderProvinceName !=null and item.senderProvinceName != ''">
                `sender_province_name`  = #{item.senderProvinceName},
            </if>
            <if test="item.senderCityCode !=null and item.senderCityCode != ''">
                `sender_city_code`  = #{item.senderCityCode},
            </if>
            <if test="item.networkTownCode !=null and item.networkTownCode != ''">
                `network_town_code`  = #{item.networkTownCode},
            </if>
            <if test="item.receiverCityCode !=null and item.receiverCityCode != ''">
                `receiver_city_code`  = #{item.receiverCityCode},
            </if>
            <if test="item.networkProvinceName !=null and item.networkProvinceName != ''">
                `network_province_name`  = #{item.networkProvinceName},
            </if>
            <if test="item.remark != null">
                `remark`  = #{item.remark},
            </if>
            <if test="item.finalTownCode !=null and item.finalTownCode != ''">
                `final_town_code`  = #{item.finalTownCode},
            </if>
            <if test="item.senderTel !=null and item.senderTel != ''">
                `sender_tel`  = #{item.senderTel},
            </if>
            <if test="item.finalTownName !=null and item.finalTownName != ''">
                `final_town_name`  = #{item.finalTownName},
            </if>
            <if test="item.receiverVirtualMobile !=null and item.receiverVirtualMobile != ''">
                `receiver_virtual_mobile`  = #{item.receiverVirtualMobile},
            </if>
            <if test="item.endLat != null">
                `end_lat`  = #{item.endLat},
            </if>
            <if test="item.networkProvinceCode !=null and item.networkProvinceCode != ''">
                `network_province_code`  = #{item.networkProvinceCode},
            </if>
            <if test="item.taskNo !=null and item.taskNo != ''">
                `task_no`  = #{item.taskNo},
            </if>
            <if test="item.networkDistrictName !=null and item.networkDistrictName != ''">
                `network_district_name`  = #{item.networkDistrictName},
            </if>
            <if test="item.senderCountryCode !=null and item.senderCountryCode != ''">
                `sender_country_code`  = #{item.senderCountryCode},
            </if>
            <if test="item.endLng != null">
                `end_lng`  = #{item.endLng},
            </if>
            <if test="item.receiverProvinceName !=null and item.receiverProvinceName != ''">
                `receiver_province_name`  = #{item.receiverProvinceName},
            </if>
            <if test="item.senderCityName !=null and item.senderCityName != ''">
                `sender_city_name`  = #{item.senderCityName},
            </if>
            <if test="item.receiverName !=null and item.receiverName != ''">
                `receiver_name`  = #{item.receiverName},
            </if>
            <if test="item.receiverMobile !=null and item.receiverMobile != ''">
                `receiver_mobile`  = #{item.receiverMobile},
            </if>
            <if test="item.senderMobile !=null and item.senderMobile != ''">
                `sender_mobile`  = #{item.senderMobile},
            </if>
            <if test="item.finalMobile !=null and item.finalMobile != ''">
                `final_mobile`  = #{item.finalMobile},
            </if>
            <if test="item.receiverDetailAddr !=null and item.receiverDetailAddr != ''">
                `receiver_detail_addr`  = #{item.receiverDetailAddr},
            </if>
            <if test="item.senderDetailAddr !=null and item.senderDetailAddr != ''">
                `sender_detail_addr`  = #{item.senderDetailAddr},
            </if>
            <if test="item.networkTel !=null and item.networkTel != ''">
                `network_tel`  = #{item.networkTel},
            </if>
            <if test="item.networkAddr !=null and item.networkAddr != ''">
                `network_addr`  = #{item.networkAddr},
            </if>
            <if test="item.receiverCityName !=null and item.receiverCityName != ''">
                `receiver_city_name`  = #{item.receiverCityName},
            </if>
            <if test="item.networkCode !=null and item.networkCode != ''">
                `network_code`  = #{item.networkCode},
            </if>
            <if test="item.finalDetailAddr !=null and item.finalDetailAddr != ''">
                `final_detail_addr`  = #{item.finalDetailAddr},
            </if>
            <if test="item.senderTownName !=null and item.senderTownName != ''">
                `sender_town_name`  = #{item.senderTownName},
            </if>
            <if test="item.receiverDistrictName !=null and item.receiverDistrictName != ''">
                `receiver_district_name`  = #{item.receiverDistrictName},
            </if>
            <if test="item.receiverProvinceCode !=null and item.receiverProvinceCode != ''">
                `receiver_province_code`  = #{item.receiverProvinceCode},
            </if>
            <if test="item.receiverTownName !=null and item.receiverTownName != ''">
                `receiver_town_name`  = #{item.receiverTownName},
            </if>
            <if test="item.senderDistrictName !=null and item.senderDistrictName != ''">
                `sender_district_name`  = #{item.senderDistrictName},
            </if>
            <if test="item.senderProvinceCode !=null and item.senderProvinceCode != ''">
                `sender_province_code`  = #{item.senderProvinceCode},
            </if>
             <if test="item.rollbackFlag !=null and item.rollbackFlag != ''">
                 `rollback_flag`  = #{item.rollbackFlag},
             </if>
             <if test="item.senderVirtualMobile !=null and item.senderVirtualMobile != ''">
                 `sender_virtual_mobile`  = #{item.senderVirtualMobile},
             </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
            <if test="item.receiverTel !=null and item.receiverTel != ''">
                and  `receiver_tel`  =#{item.receiverTel}
            </if>
            <if test="item.receiverTownCode !=null and item.receiverTownCode != ''">
                and  `receiver_town_code`  =#{item.receiverTownCode}
            </if>
            <if test="item.receiverDistrictCode !=null and item.receiverDistrictCode != ''">
                and  `receiver_district_code`  =#{item.receiverDistrictCode}
            </if>
            <if test="item.networkName !=null and item.networkName != ''">
                and  `network_name`  =#{item.networkName}
            </if>
            <if test="item.finalCityName !=null and item.finalCityName != ''">
                and  `final_city_name`  =#{item.finalCityName}
            </if>
            <if test="item.finalName !=null and item.finalName != ''">
                and  `final_name`  =#{item.finalName}
            </if>
            <if test="item.finalDistrictName !=null and item.finalDistrictName != ''">
                and  `final_district_name`  =#{item.finalDistrictName}
            </if>
            <if test="item.networkCityCode !=null and item.networkCityCode != ''">
                and  `network_city_code`  =#{item.networkCityCode}
            </if>
            <if test="item.finalCountryName !=null and item.finalCountryName != ''">
                and  `final_country_name`  =#{item.finalCountryName}
            </if>
            <if test="item.finalDistrictCode !=null and item.finalDistrictCode != ''">
                and  `final_district_code`  =#{item.finalDistrictCode}
            </if>
            <if test="item.deleteFlag !=null">
                and  `delete_flag`  =#{item.deleteFlag}
            </if>
            <if test="item.senderName !=null and item.senderName != ''">
                and  `sender_name`  =#{item.senderName}
            </if>
            <if test="item.senderTownCode !=null and item.senderTownCode != ''">
                and  `sender_town_code`  =#{item.senderTownCode}
            </if>
            <if test="item.finalProvinceCode !=null and item.finalProvinceCode != ''">
                and  `final_province_code`  =#{item.finalProvinceCode}
            </if>
            <if test="item.networkDistrictCode !=null and item.networkDistrictCode != ''">
                and  `network_district_code`  =#{item.networkDistrictCode}
            </if>
            <if test="item.senderDistrictCode !=null and item.senderDistrictCode != ''">
                and  `sender_district_code`  =#{item.senderDistrictCode}
            </if>
            <if test="item.networkPhone !=null and item.networkPhone != ''">
                and  `network_phone`  =#{item.networkPhone}
            </if>
            <if test="item.startLng !=null">
                and  `start_lng`  =#{item.startLng}
            </if>
            <if test="item.finalTel !=null and item.finalTel != ''">
                and  `final_tel`  =#{item.finalTel}
            </if>
            <if test="item.startLat !=null">
                and  `start_lat`  =#{item.startLat}
            </if>
            <if test="item.receiverCountryCode !=null and item.receiverCountryCode != ''">
                and  `receiver_country_code`  =#{item.receiverCountryCode}
            </if>
            <if test="item.networkTownName !=null and item.networkTownName != ''">
                and  `network_town_name`  =#{item.networkTownName}
            </if>
            <if test="item.faceSheetHold !=null">
                and  `face_sheet_hold`  =#{item.faceSheetHold}
            </if>
            <if test="item.version !=null">
                and  `version`  =#{item.version}
            </if>
            <if test="item.finalCountryCode !=null and item.finalCountryCode != ''">
                and  `final_country_code`  =#{item.finalCountryCode}
            </if>
            <if test="item.finalCityCode !=null and item.finalCityCode != ''">
                and  `final_city_code`  =#{item.finalCityCode}
            </if>
            <if test="item.finalProvinceName !=null and item.finalProvinceName != ''">
                and  `final_province_name`  =#{item.finalProvinceName}
            </if>
            <if test="item.networkCityName !=null and item.networkCityName != ''">
                and  `network_city_name`  =#{item.networkCityName}
            </if>
            <if test="item.networkContact !=null and item.networkContact != ''">
                and  `network_contact`  =#{item.networkContact}
            </if>
            <if test="item.senderCountryName !=null and item.senderCountryName != ''">
                and  `sender_country_name`  =#{item.senderCountryName}
            </if>
            <if test="item.receiverCountryName !=null and item.receiverCountryName != ''">
                and  `receiver_country_name`  =#{item.receiverCountryName}
            </if>
            <if test="item.senderProvinceName !=null and item.senderProvinceName != ''">
                and  `sender_province_name`  =#{item.senderProvinceName}
            </if>
            <if test="item.senderCityCode !=null and item.senderCityCode != ''">
                and  `sender_city_code`  =#{item.senderCityCode}
            </if>
            <if test="item.networkTownCode !=null and item.networkTownCode != ''">
                and  `network_town_code`  =#{item.networkTownCode}
            </if>
            <if test="item.receiverCityCode !=null and item.receiverCityCode != ''">
                and  `receiver_city_code`  =#{item.receiverCityCode}
            </if>
            <if test="item.networkProvinceName !=null and item.networkProvinceName != ''">
                and  `network_province_name`  =#{item.networkProvinceName}
            </if>
            <if test="item.remark !=null">
                and  `remark`  =#{item.remark}
            </if>
            <if test="item.finalTownCode !=null and item.finalTownCode != ''">
                and  `final_town_code`  =#{item.finalTownCode}
            </if>
            <if test="item.senderTel !=null and item.senderTel != ''">
                and  `sender_tel`  =#{item.senderTel}
            </if>
            <if test="item.finalTownName !=null and item.finalTownName != ''">
                and  `final_town_name`  =#{item.finalTownName}
            </if>
            <if test="item.receiverVirtualMobile !=null and item.receiverVirtualMobile != ''">
                and  `receiver_virtual_mobile`  =#{item.receiverVirtualMobile}
            </if>
            <if test="item.endLat !=null">
                and  `end_lat`  =#{item.endLat}
            </if>
            <if test="item.networkProvinceCode !=null and item.networkProvinceCode != ''">
                and  `network_province_code`  =#{item.networkProvinceCode}
            </if>
            <if test="item.taskNo !=null and item.taskNo != ''">
                and  `task_no`  =#{item.taskNo}
            </if>
            <if test="item.networkDistrictName !=null and item.networkDistrictName != ''">
                and  `network_district_name`  =#{item.networkDistrictName}
            </if>
            <if test="item.senderCountryCode !=null and item.senderCountryCode != ''">
                and  `sender_country_code`  =#{item.senderCountryCode}
            </if>
            <if test="item.endLng !=null">
                and  `end_lng`  =#{item.endLng}
            </if>
            <if test="item.receiverProvinceName !=null and item.receiverProvinceName != ''">
                and  `receiver_province_name`  =#{item.receiverProvinceName}
            </if>
            <if test="item.senderCityName !=null and item.senderCityName != ''">
                and  `sender_city_name`  =#{item.senderCityName}
            </if>
            <if test="item.receiverName !=null and item.receiverName != ''">
                and  `receiver_name`  =#{item.receiverName}
            </if>
            <if test="item.receiverMobile !=null and item.receiverMobile != ''">
                and  `receiver_mobile`  =#{item.receiverMobile}
            </if>
            <if test="item.senderMobile !=null and item.senderMobile != ''">
                and  `sender_mobile`  =#{item.senderMobile}
            </if>
            <if test="item.updateTime !=null">
                and  `update_time`  =#{item.updateTime}
            </if>
            <if test="item.finalMobile !=null and item.finalMobile != ''">
                and  `final_mobile`  =#{item.finalMobile}
            </if>
            <if test="item.receiverDetailAddr !=null and item.receiverDetailAddr != ''">
                and  `receiver_detail_addr`  =#{item.receiverDetailAddr}
            </if>
            <if test="item.senderDetailAddr !=null and item.senderDetailAddr != ''">
                and  `sender_detail_addr`  =#{item.senderDetailAddr}
            </if>
            <if test="item.networkTel !=null and item.networkTel != ''">
                and  `network_tel`  =#{item.networkTel}
            </if>
            <if test="item.networkAddr !=null and item.networkAddr != ''">
                and  `network_addr`  =#{item.networkAddr}
            </if>
            <if test="item.createTime !=null">
                and  `create_time`  =#{item.createTime}
            </if>
            <if test="item.receiverCityName !=null and item.receiverCityName != ''">
                and  `receiver_city_name`  =#{item.receiverCityName}
            </if>
            <if test="item.networkCode !=null and item.networkCode != ''">
                and  `network_code`  =#{item.networkCode}
            </if>
            <if test="item.finalDetailAddr !=null and item.finalDetailAddr != ''">
                and  `final_detail_addr`  =#{item.finalDetailAddr}
            </if>
            <if test="item.senderTownName !=null and item.senderTownName != ''">
                and  `sender_town_name`  =#{item.senderTownName}
            </if>
            <if test="item.receiverDistrictName !=null and item.receiverDistrictName != ''">
                and  `receiver_district_name`  =#{item.receiverDistrictName}
            </if>
            <if test="item.receiverProvinceCode !=null and item.receiverProvinceCode != ''">
                and  `receiver_province_code`  =#{item.receiverProvinceCode}
            </if>
            <if test="item.receiverTownName !=null and item.receiverTownName != ''">
                and  `receiver_town_name`  =#{item.receiverTownName}
            </if>
            <if test="item.senderDistrictName !=null and item.senderDistrictName != ''">
                and  `sender_district_name`  =#{item.senderDistrictName}
            </if>
            <if test="item.senderProvinceCode !=null and item.senderProvinceCode != ''">
                and  `sender_province_code`  =#{item.senderProvinceCode}
            </if>
            <if test="item.senderVirtualMobile !=null and item.senderVirtualMobile != ''">
                and  `sender_virtual_mobile`  =#{item.senderVirtualMobile}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_address
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>


    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_address
            set `delete_flag`=1
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

    <select id="searchTaskAddressListByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.TaskAddress"
            parameterType="java.util.List" timeout="60">
        select
        <include refid="searchFieldsSql"/>
        from task_address t
        <where>
            t.delete_flag = 0
            <if test=" listTaskNo!= null and listTaskNo.size() > 0">
                and t.task_no in
                <foreach item="listTaskNo" collection="listTaskNo" index="index" open="(" separator="," close=")">
                    #{listTaskNo}
                </foreach>
            </if>
        </where>
    </select>

</mapper>