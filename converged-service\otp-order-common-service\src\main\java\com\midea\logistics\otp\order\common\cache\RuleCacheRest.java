package com.midea.logistics.otp.order.common.cache;

import com.midea.logistics.otp.rule.domain.bean.IssueRule;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: RuleCacheRest
 * Author: lindq2
 * Date: 2020-4-1
 * Description: 配置缓存
 */

@Slf4j
@RestController
public class RuleCacheRest {

    @Autowired
    private RuleCache ruleCache;

    /**
    * @description: 获取所有配置，便于分析
    * @param: [request]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2020/4/28 9:56
    */
    @PostMapping("/showAllConfig")
    public JsonResponse showAllConfig(@RequestBody String clazzName,HttpServletRequest request){
        JsonResponse jsonResponse = new JsonResponse();
        jsonResponse.setData(RuleCacheImpl.getAllConfig().get(clazzName));
        return jsonResponse;
    }

    @PostMapping("/clearConfig")
    public JsonResponse clearConfig(@RequestBody Class clazz, HttpServletRequest request){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            ruleCache.clear(clazz);
        } catch (Exception e){
            jsonResponse.build(BaseCodeEnum.FAILED.getCode(), clazz + "：清除缓存异常！");
            log.error("", e);
        }

        log.info("{}：清除缓存成功！", clazz);
        return jsonResponse;
    }

    @GetMapping("/caCheClear")
    public JsonResponse caCheClear(Class clazz, HttpServletRequest request){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            RuleCacheImpl.remove(clazz.getName());
        } catch (Exception e){
            jsonResponse.build(BaseCodeEnum.FAILED.getCode(), clazz + "：清除缓存异常！");
            log.error("", e);
        }

        log.info("{}：清除缓存成功！", clazz);
        return jsonResponse;
    }

    @GetMapping("/caCheClears")
    public JsonResponse caCheClears(String clazzName, HttpServletRequest request){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            jsonResponse.setData(RuleCacheImpl.getAllConfig().get(clazzName));
            RuleCacheImpl.remove(clazzName);
            return jsonResponse;
        } catch (Exception e){
            jsonResponse.build(BaseCodeEnum.FAILED.getCode(), clazzName + "：清除缓存异常！");
            log.error("", e);
        }

        log.info("{}：清除缓存成功！", clazzName);
        return jsonResponse;
    }

    @GetMapping("/caCheSearch")
    public JsonResponse caCheSearch(Class clazz, HttpServletRequest request){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            ShippingTypeRule issueRuleRequest = new ShippingTypeRule();
            issueRuleRequest.setProvinceCode("144");
            List<ShippingTypeRule> issueRules = ruleCache.searchList(issueRuleRequest);
            jsonResponse.setData(clazz);
        } catch (Exception e){
            jsonResponse.build(BaseCodeEnum.FAILED.getCode(), clazz + "：获取缓存异常！");
            log.error("", e);
        }

        log.info("{}：获取缓存成功！", clazz);
        return jsonResponse;
    }
}