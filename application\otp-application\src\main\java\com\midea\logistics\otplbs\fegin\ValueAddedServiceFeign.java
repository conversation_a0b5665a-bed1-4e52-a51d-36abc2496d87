package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.bean.ValueAddedService;
import com.midea.logistics.otp.order.domain.bean.custom.ValueAddedServiceExt;
import com.midea.logistics.otp.order.domain.bean.custom.VdRelationInfoDto;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ValueAddedServiceFeign
 * Author: lindq2
 * Date: 2019-5-14 11:40:46
 * Description:增值服务单 feign
 */
@FeignClient(value = "logistics-otp-order-service",url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface ValueAddedServiceFeign {

    @RequestMapping(value = "/valueAddedService", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ValueAddedService valueAddedService);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedService/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedService/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ValueAddedService valueAddedService);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedService/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/valueAddedServices", method = RequestMethod.GET)
    JsonResponse<PageResponse<ValueAddedService>> search(@SpringQueryMap ValueAddedServiceExt valueAddedServiceExt);

    @RequestMapping(value = "/valueAddedServices/selectOne", method = RequestMethod.GET)
    JsonResponse<ValueAddedService> selectOne(@SpringQueryMap ValueAddedService valueAddedService);

    /**
     * @return 查询关联信息集合
     */
    @RequestMapping(value = "/valueAddedService/selectRelationOrderAndTask", method = RequestMethod.POST)
    JsonResponse<VdRelationInfoDto>  selectRelationOrderAndTask(@RequestBody ValueAddedServiceExt valueAddedServiceExt);
}
