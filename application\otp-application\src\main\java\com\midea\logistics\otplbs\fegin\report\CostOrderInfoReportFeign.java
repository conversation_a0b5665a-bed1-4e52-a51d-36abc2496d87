package com.midea.logistics.otplbs.fegin.report;

import com.midea.logistics.otp.report.service.domain.ReportRouters;
import com.midea.logistics.otp.report.service.domain.bean.common.CostOrderInfo;
import com.midea.logistics.otp.report.service.domain.request.CostOrderInfoRequest;
import com.midea.logistics.otp.report.service.domain.response.CostOrderInfoResponse;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: CostOrderInfoFeign
* Author: james
* Date: 2021-9-3 15:13:53
* Description:外部客户订单表 feign
*/
@FeignClient(value = "logistics-otp-report-service")
public interface CostOrderInfoReportFeign {

    /**
    *
    *
    */
    @RequestMapping(value = "/report/costOrderInfo", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CostOrderInfo costOrderInfo);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/report/costOrderInfo/{id}", method = RequestMethod.GET)
    JsonResponse<CostOrderInfo> queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/report/costOrderInfo/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CostOrderInfo costOrderInfo);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/report/costOrderInfo/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/report/costOrderInfos", method = RequestMethod.POST)
    JsonResponse<PageResponse<CostOrderInfo>> search(@RequestBody CostOrderInfo costOrderInfo);

    @PostMapping(value = "/report/costOrderInfo/selectOne")
    JsonResponse<CostOrderInfo> selectOne(@RequestBody CostOrderInfo costOrderInfo);

    @PostMapping(value = "/report/costOrderInfo/insertBatch")
    JsonResponse insertBatch(@RequestBody List<CostOrderInfo> costOrderInfos);

    @PostMapping(value = "/report/costOrderInfo/batchUpdate")
    JsonResponse<Integer> batchUpdate(@RequestBody List<CostOrderInfo> costOrderInfos);


    @PostMapping(value = "/report/costOrderInfo/deleteBatch")
    JsonResponse deleteBatch(@RequestBody List<CostOrderInfo> costOrderInfos) ;

    @PostMapping(value = "/report/costOrderInfo/searchByCustomerOrderNos")
    JsonResponse<List<CostOrderInfo>> searchByCustomerOrderNos(@RequestBody CostOrderInfoRequest costOrderInfoRequest) ;

    @RequestMapping(value = "/report/costOrderInfos/searchDesc", method = RequestMethod.POST)
    JsonResponse<PageResponse<CostOrderInfoResponse>> search(@RequestBody CostOrderInfoRequest costOrderInfo);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/report/costOrderInfo/updateByIdCanSetEmpty/{id}", method = RequestMethod.PUT)
    JsonResponse updateByIdCanSetEmpty(@PathVariable("id") Long id, @RequestBody CostOrderInfo costOrderInfo);

    @RequestMapping(value = ReportRouters.REPORT_SEARCH_COST_ORDER_WITH_PROCESS_COUNT, method = RequestMethod.POST)
    JsonResponse<List<CostOrderInfo>> selectCostOrderInfoPageWithProcessCount(@RequestBody CostOrderInfoRequest costOrderInfo);

    @PostMapping(value = ReportRouters.REPORT_BATCH_UPDATE_WITH_PROCESS_COUNT)
    JsonResponse batchUpdateWithProcessCount(@RequestBody List<CostOrderInfo> costOrderInfos);

    @PostMapping(value = ReportRouters.REPORT_BATCH_UPDATE_CAN_SET_EMPTY)
    JsonResponse batchUpdateCanSetEmpty(@RequestBody List<CostOrderInfo> costOrderInfos);

    @PostMapping("/report/converged/costOrderInfoIdVerify")
    JsonResponse verifyByIds(@RequestBody List<Long> ids);

    @PostMapping(value = ReportRouters.REPORT_DISTINCT_COST_ORDER)
    JsonResponse distinctCostOrder(@RequestBody CostOrderInfo costOrderInfo);

    @PostMapping(value = ReportRouters.REPORT_CONFIRM_COST_ORDER)
    JsonResponse orderConfirm(@RequestBody CostOrderInfo costOrderInfo);

    @RequestMapping(value = ReportRouters.REPORT_CANCEL_COST_ORDER, method = RequestMethod.POST)
    JsonResponse orderCancel(@RequestBody List<CostOrderInfo> costOrderInfoList);

    @RequestMapping(value = ReportRouters.REPORT_CANCEL_COST_ORDER_WITH_TRANS, method = RequestMethod.POST)
    JsonResponse orderCancel(@RequestBody CostOrderInfo costOrderInfo);
}
