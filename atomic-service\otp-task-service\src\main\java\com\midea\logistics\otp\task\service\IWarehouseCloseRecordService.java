package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord;
import com.midea.logistics.otp.task.domain.request.WarehouseCloseRecordReq;
import com.mideaframework.core.web.PageResponse;

import java.util.List;
/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: IWarehouseCloseRecordService
* Author: dongxy31
* Date: 2024-6-11 10:44:53
* Description:IWarehouseCloseRecordService服务接口
*/
public interface IWarehouseCloseRecordService{

    Integer saveWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord);

    Integer deleteWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord);

    Integer updateWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord);

    WarehouseCloseRecord selectOneWarehouseCloseRecord(WarehouseCloseRecord warehouseCloseRecord);

    PageResponse<WarehouseCloseRecord> selectWarehouseCloseRecordPage(WarehouseCloseRecord warehouseCloseRecord);

    List<WarehouseCloseRecord> selectWarehouseCloseRecordByIndex(WarehouseCloseRecord warehouseCloseRecord);

    int countWarehouseCloseRecordByIndex(WarehouseCloseRecord warehouseCloseRecord);

    Integer insertBatch(List<WarehouseCloseRecord> warehouseCloseRecords);

    Integer batchUpdate(List<WarehouseCloseRecord> warehouseCloseRecords);

    Integer batchUpdateTask(List<WarehouseCloseRecord> warehouseCloseRecords);

    Integer deleteBatch(List<WarehouseCloseRecord> warehouseCloseRecords);

    List<WarehouseCloseRecord> queryListByUpdate(WarehouseCloseRecordReq warehouseCloseRecord);

    List<WarehouseCloseRecord> queryList(WarehouseCloseRecordReq warehouseCloseRecord);
}