package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialNoAndConstractNoSyncDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialOrderSyncItemDto;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.service.ITaskSynFinancialNoService;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * Author:   zhangjh
 * Date:     2019-06-29 17:58
 * Description: 同步财务单号
 */
@RestController
public class TasksSynFinancialNoRest {
    private Logger LOGGER = LoggerFactory.getLogger(TasksSynFinancialNoRest.class);


    @Autowired
    private ITaskSynFinancialNoService iTaskSynFinancialNoService;

    /**
     * 同步财务单号
     *
     * @return
     */
    @RequestMapping(value = TaskRouters.TASKS_SYN_FINANCIALNO, method = RequestMethod.POST)
    public JsonResponse synFinancialNo(@Valid @RequestBody List<FinancialOrderSyncItemDto> itemDtoList, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            iTaskSynFinancialNoService.synFinancialNo(itemDtoList);
        };

        return doing.go(request, LOGGER);
    }


    /**
     * 同步合同号
     *
     * @return
     */
    @RequestMapping(value = TaskRouters.TASKS_SYN_CONTRACTNO, method = RequestMethod.POST)
    public JsonResponse synContractNo(@Valid @RequestBody FinancialNoAndConstractNoSyncDto financialNoAndConstractNoSyncDto, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            iTaskSynFinancialNoService.synContractNo(financialNoAndConstractNoSyncDto);
        };

        return doing.go(request, LOGGER);
    }
}
