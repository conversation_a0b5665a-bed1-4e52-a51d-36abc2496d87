package com.midea.logistics.otp.orderverify.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.midea.logistics.otp.bean.BusinessCateoryDto;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderAddressFeign;
import com.midea.logistics.otp.enums.BusinessCategory;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.enums.OrderOperateType;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.BusinessCategoryHelper;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.BusinessCategoryService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 业务大类
 */
@Component
@Slf4j
public class BusinessCategoryServiceImpl implements BusinessCategoryService {


    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private BusinessCategoryHelper businessCategoryHelper;
    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Override
    @ExpressFlow(node = OrderOperateType.BUSINESS_CATEGORY)
    public FlowListenerParam businessCategory(CustomerOrderInfoExt customerOrderInfoExt) {

        String orderNo = customerOrderInfoExt.getOrderNo();

        if(StringUtils.isNotBlank(customerOrderInfoExt.getBusinessCategory())){
            BusinessCategory businessCategory = BusinessCategory.getKey(customerOrderInfoExt.getBusinessCategory());
            return  FlowListenerParam.success("存在业务大类，无需解析:"+businessCategory==null?customerOrderInfoExt.getBusinessCategory():businessCategory.getValue());
        }

        //模糊订单无需解析业务大类
        if(null != customerOrderInfoExt.getPlanOrderFlag() && customerOrderInfoExt.getPlanOrderFlag().intValue() == CommonEnum.YES.getValue().intValue()){
            return FlowListenerParam.success("模糊订单，无需解析业务大类");
        }

        BusinessCateoryDto businessCateoryDto = new BusinessCateoryDto();
        BeanUtils.copyProperties(customerOrderInfoExt,businessCateoryDto);
        CustomerOrderAddress customerOrderAddress = orderFlowHelper.getCustomerOrderAddress(orderNo);
        if(null == customerOrderAddress){
            throw BusinessException.fail("根据orderNo获取地址异常单号："+orderNo);
        }
        businessCateoryDto.setSenderProvinceCode(customerOrderAddress.getSenderProvinceCode());
        businessCateoryDto.setReceiverProvinceCode(customerOrderAddress.getReceiverProvinceCode());

        BusinessCategory businessCategory = businessCategoryHelper.getBusinessCategory(businessCateoryDto);
        if(businessCategory!=null){
            log.info(" businessCategory orderNo:{},key:{}",orderNo,businessCategory.getKey());
            this.updateBusinessCategory(customerOrderInfoExt,businessCategory.getKey());
            return FlowListenerParam.success(businessCategory.getValue());
        }

        return  FlowListenerParam.success("订单无需业务大类解析");
    }

    /**
    * @description: 更新业务大类
    * @param: [customerOrderInfo, key]
    * @return: void
    * @author: 陈永培
    * @createtime: 2021/12/15 11:55
    */
    public void updateBusinessCategory(CustomerOrderInfo customerOrderInfo,String key){
        if (ToolUtils.isEmpty(customerOrderInfo)){
            return;
        }
        CustomerOrderInfo target = new CustomerOrderInfo();
        target.setId(customerOrderInfo.getId());
        target.setBusinessCategory(key);
        JsonResponse<Integer> update = customerOrderInfoFeign.updateByIdCanSetEmpty(target.getId(), target);
        CommonConstant.checkIsUpdateSuccess(update,"业务大类");
    }
}
