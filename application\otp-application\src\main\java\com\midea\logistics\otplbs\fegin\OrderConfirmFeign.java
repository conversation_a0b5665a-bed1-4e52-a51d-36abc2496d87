package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.bean.OrderConfirm;
import com.midea.logistics.otp.order.domain.bean.custom.OrderConfirmExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderConfirmName;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: OrderConfirmFeign
 * Author: liwende
 * Date: 2019-6-14 11:24:44
 * Description:订单确认表 feign
 */
@FeignClient(value = "logistics-otp-order-service", url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface OrderConfirmFeign {

    @RequestMapping(value = "/orderConfirm", method = RequestMethod.POST)
    JsonResponse create(@RequestBody OrderConfirm orderConfirm);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderConfirm/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderConfirm/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderConfirm orderConfirm);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderConfirm/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/orderConfirms", method = RequestMethod.GET)
    JsonResponse<PageResponse<OrderConfirmName>> search(@SpringQueryMap OrderConfirmExt orderConfirm);

    @RequestMapping(value = "/orderConfirmsCount", method = RequestMethod.GET)
    JsonResponse<Integer> orderConfirmsCount(@SpringQueryMap OrderConfirmExt orderConfirm);



    @RequestMapping(value = "/orderConfirmExts", method = RequestMethod.POST)
    JsonResponse<PageResponse<OrderConfirmExt>> searchOrderConfirmExts(@RequestBody OrderConfirmExt orderConfirm);

    @RequestMapping(value = "/orderConfirmList", method = RequestMethod.POST)
    JsonResponse bachUpdate(@RequestBody List<OrderConfirm> orderConfirms);

    @RequestMapping(value = "/orderConfirmOne", method = RequestMethod.POST)
    JsonResponse<OrderConfirm> selectOne(@RequestBody OrderConfirm orderConfirm);

    @RequestMapping(value = "/orderConfirm/updateByCustOrderNo", method = RequestMethod.PUT)
    public JsonResponse updateByCustOrderNo(@RequestBody OrderConfirmExt orderConfirm);
}
