package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.WhConfig;
import com.midea.logistics.otp.rule.domain.request.WhConfigRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: WhConfigFeign
 * Author: luoh
 * Date: 2019-6-3 16:30:57
 * Description:仓库配置 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface WhConfigFeign {

    @RequestMapping(value = OptRuleRouters.WH_CONFIG_SAVE, method = RequestMethod.POST)
    JsonResponse create(@RequestBody WhConfigRequest whConfigRequest);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WH_CONFIG_DETAIL, method = RequestMethod.GET)
    JsonResponse<WhConfig> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WH_CONFIG_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody WhConfig whConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WH_CONFIG_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = OptRuleRouters.WH_CONFIG_LIST, method = RequestMethod.GET)
    JsonResponse<PageResponse<WhConfig>> search(@SpringQueryMap WhConfigRequest whConfigRequest);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WH_CONFIG_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<WhConfigRequest> whConfigRequests);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.WH_CONFIG_BATCH_DELETE_BY_BUSINESSKEY, method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

    @RequestMapping(value = OptRuleRouters.WH_CONFIG_LIST_COUNT, method = RequestMethod.GET)
    JsonResponse<Integer> selectWhConfigPageCount(@SpringQueryMap WhConfig whConfig);
}
