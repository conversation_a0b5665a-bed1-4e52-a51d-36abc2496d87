<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="order-info-process-ys" name="子订单流程-ys" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0opo74t</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0opo74t" sourceRef="StartEvent_1" targetRef="Activity_0smupof" />
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_02zzxtp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_0fnxy33</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:exclusiveGateway id="Gateway_0smudh3" name="是否获取运单号" default="Flow_1e8tnvs">
      <bpmn:incoming>Flow_0r4gp1f</bpmn:incoming>
      <bpmn:incoming>Flow_09codrp</bpmn:incoming>
      <bpmn:outgoing>Flow_0ss94pu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1e8tnvs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Gateway_0xmv1e8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ss94pu" name="no" sourceRef="Gateway_0smudh3" targetRef="Activity_0hvdqty">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=((orderInfo.businessMode="B2B" and orderInfo.deliveryType="ZT") or (orderInfo.businessMode="B2C" and orderInfo.orderType="PO" and orderInfo.deliveryType="ZT") or (orderInfo.orderType="PO" and orderInfo.deliveryType="EXPRESS"))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e8tnvs" name="yes" sourceRef="Gateway_0smudh3" targetRef="Activity_1fv890g" />
    <bpmn:sequenceFlow id="Flow_1g901df" sourceRef="Activity_1fv890g" targetRef="Activity_0hvdqty" />
    <bpmn:serviceTask id="Activity_0tlqh7q" name="时效产品">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="aging" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/agingParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0evxotg</bpmn:incoming>
      <bpmn:incoming>Flow_09oyck8</bpmn:incoming>
      <bpmn:outgoing>Flow_0lv7i6b</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0smupof" name="大小电解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="orderOutCollabWh" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/setOrderOutCollabWh" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0opo74t</bpmn:incoming>
      <bpmn:outgoing>Flow_0fnxy33</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
          <zeebe:output source="=responseBody.deliveryType" target="orderInfo.deliveryType" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_0p8hiid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e8tnvs</bpmn:incoming>
      <bpmn:outgoing>Flow_1g901df</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0fnxy33" sourceRef="Activity_0smupof" targetRef="Gateway_0mzkgmp" />
    <bpmn:serviceTask id="Activity_0hvdqty" name="计费相关">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/anyBmsInfo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ss94pu</bpmn:incoming>
      <bpmn:incoming>Flow_1g901df</bpmn:incoming>
      <bpmn:outgoing>Flow_0ltk8av</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ltk8av" sourceRef="Activity_0hvdqty" targetRef="Gateway_14t3l3o" />
    <bpmn:serviceTask id="Activity_1kkl3ix" name="推送查单系统,bms,net,task">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bmsLotsNetTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0lv7i6b</bpmn:incoming>
      <bpmn:outgoing>Flow_02zzxtp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02zzxtp" sourceRef="Activity_1kkl3ix" targetRef="Event_14d5skw" />
    <bpmn:sequenceFlow id="Flow_0lv7i6b" sourceRef="Activity_0tlqh7q" targetRef="Activity_1kkl3ix" />
    <bpmn:serviceTask id="Activity_1abvm22" name="第三方解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="thirdPart" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/third/party/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vatmpz</bpmn:incoming>
      <bpmn:outgoing>Flow_0evxotg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0evxotg" sourceRef="Activity_1abvm22" targetRef="Activity_0tlqh7q" />
    <bpmn:exclusiveGateway id="Gateway_14t3l3o" name="是否第三方" default="Flow_0vatmpz">
      <bpmn:incoming>Flow_0ltk8av</bpmn:incoming>
      <bpmn:outgoing>Flow_0vatmpz</bpmn:outgoing>
      <bpmn:outgoing>Flow_09oyck8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0vatmpz" name="yes" sourceRef="Gateway_14t3l3o" targetRef="Activity_1abvm22" />
    <bpmn:sequenceFlow id="Flow_09oyck8" name="no" sourceRef="Gateway_14t3l3o" targetRef="Activity_0tlqh7q">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=orderInfo.thirdFlag!=null</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0p8hiid" sourceRef="Activity_087r4hi" targetRef="Gateway_0xmv1e8" />
    <bpmn:serviceTask id="Activity_18jnsq2" name="电商分类解析1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/ecommerceCategoriesAnalysisTag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fg26dp</bpmn:incoming>
      <bpmn:outgoing>Flow_0r4gp1f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0r4gp1f" sourceRef="Activity_18jnsq2" targetRef="Gateway_0smudh3" />
    <bpmn:exclusiveGateway id="Gateway_0xmv1e8" default="Flow_09codrp">
      <bpmn:incoming>Flow_0p8hiid</bpmn:incoming>
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:outgoing>Flow_0fg26dp</bpmn:outgoing>
      <bpmn:outgoing>Flow_09codrp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fg26dp" name="yes" sourceRef="Gateway_0xmv1e8" targetRef="Activity_18jnsq2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!=null and (orderInfo.orderType="DP" or orderInfo.orderType="SO")) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09codrp" sourceRef="Gateway_0xmv1e8" targetRef="Gateway_0smudh3" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="order-info-process-ys">
      <bpmndi:BPMNEdge id="Flow_0r4gp1f_di" bpmnElement="Flow_0r4gp1f">
        <di:waypoint x="760" y="177" />
        <di:waypoint x="793" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p8hiid_di" bpmnElement="Flow_0p8hiid">
        <di:waypoint x="560" y="177" />
        <di:waypoint x="585" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09oyck8_di" bpmnElement="Flow_09oyck8">
        <di:waypoint x="1100" y="202" />
        <di:waypoint x="1100" y="250" />
        <di:waypoint x="1280" y="250" />
        <di:waypoint x="1280" y="207" />
        <di:waypoint x="1296" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1083" y="264" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vatmpz_di" bpmnElement="Flow_0vatmpz">
        <di:waypoint x="1125" y="177" />
        <di:waypoint x="1163" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1137" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0evxotg_di" bpmnElement="Flow_0evxotg">
        <di:waypoint x="1263" y="177" />
        <di:waypoint x="1296" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lv7i6b_di" bpmnElement="Flow_0lv7i6b">
        <di:waypoint x="1396" y="177" />
        <di:waypoint x="1428" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02zzxtp_di" bpmnElement="Flow_02zzxtp">
        <di:waypoint x="1528" y="177" />
        <di:waypoint x="1568" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ltk8av_di" bpmnElement="Flow_0ltk8av">
        <di:waypoint x="1050" y="177" />
        <di:waypoint x="1075" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fnxy33_di" bpmnElement="Flow_0fnxy33">
        <di:waypoint x="330" y="177" />
        <di:waypoint x="374" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g901df_di" bpmnElement="Flow_1g901df">
        <di:waypoint x="901" y="230" />
        <di:waypoint x="901" y="117" />
        <di:waypoint x="970" y="117" />
        <di:waypoint x="970" y="137" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e8tnvs_di" bpmnElement="Flow_1e8tnvs">
        <di:waypoint x="818" y="202" />
        <di:waypoint x="818" y="270" />
        <di:waypoint x="851" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="829" y="240" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ss94pu_di" bpmnElement="Flow_0ss94pu">
        <di:waypoint x="843" y="177" />
        <di:waypoint x="950" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="856" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="399" y="152" />
        <di:waypoint x="399" y="110" />
        <di:waypoint x="590" y="110" />
        <di:waypoint x="590" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="400" y="122" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="424" y="177" />
        <di:waypoint x="460" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="427" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0opo74t_di" bpmnElement="Flow_0opo74t">
        <di:waypoint x="172" y="177" />
        <di:waypoint x="230" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fg26dp_di" bpmnElement="Flow_0fg26dp">
        <di:waypoint x="635" y="177" />
        <di:waypoint x="660" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="631" y="143" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09codrp_di" bpmnElement="Flow_09codrp">
        <di:waypoint x="610" y="202" />
        <di:waypoint x="610" y="250" />
        <di:waypoint x="800" y="250" />
        <di:waypoint x="800" y="184" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="136" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="143" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="1568" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1575" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="374" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="386" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0smudh3_di" bpmnElement="Gateway_0smudh3" isMarkerVisible="true">
        <dc:Bounds x="793" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="804" y="127" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tz6c6h_di" bpmnElement="Activity_0tlqh7q">
        <dc:Bounds x="1296" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_059zzl3_di" bpmnElement="Activity_0smupof">
        <dc:Bounds x="230" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="851" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0hvdqty_di" bpmnElement="Activity_0hvdqty">
        <dc:Bounds x="950" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kkl3ix_di" bpmnElement="Activity_1kkl3ix">
        <dc:Bounds x="1428" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1abvm22_di" bpmnElement="Activity_1abvm22">
        <dc:Bounds x="1163" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14t3l3o_di" bpmnElement="Gateway_14t3l3o" isMarkerVisible="true">
        <dc:Bounds x="1075" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1073" y="143" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_18jnsq2_di" bpmnElement="Activity_18jnsq2">
        <dc:Bounds x="660" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="460" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0xmv1e8_di" bpmnElement="Gateway_0xmv1e8" isMarkerVisible="true">
        <dc:Bounds x="585" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
