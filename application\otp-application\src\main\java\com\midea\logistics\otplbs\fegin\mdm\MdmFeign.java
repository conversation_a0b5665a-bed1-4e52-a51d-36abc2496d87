package com.midea.logistics.otplbs.fegin.mdm;

import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.CdWhBomHeader;
import com.midea.logistics.domain.mdm.domain.CdWhBomRelation;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.domain.mdm.domain.MidB2cCustomerControl;
import com.midea.logistics.domain.mdm.domain.MidB2cGoodsControl;
import com.midea.logistics.domain.mdm.domain.MidB2cGoodstypeControl;
import com.midea.logistics.domain.mdm.domain.MidB2cOrdertypeControl;
import com.midea.logistics.domain.mdm.domain.MidB2cSiteWhControl;
import com.midea.logistics.domain.mdm.domain.MidCustomerControl;
import com.midea.logistics.domain.mdm.domain.MidGoodsControl;
import com.midea.logistics.domain.mdm.domain.MidGoodstypeControl;
import com.midea.logistics.domain.mdm.domain.MidOrdertypeControl;
import com.midea.logistics.domain.mdm.domain.MidSiteWhControl;
import com.midea.logistics.otp.report.service.domain.request.InvoiceUnitRequest;
import com.midea.logistics.otp.report.service.domain.response.InvoiceUnitResponse;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ©Copyright
 * Author:
 * Date:     2021-4-16 17:00
 * Description:
 */
@FeignClient(value = "logistics-mdm-service")
public interface MdmFeign {

    @GetMapping("/cdWhBomRelation/search")
    JsonResponse<PageResponse<CdWhBomRelation>> searchCdWhBomRelation(@SpringQueryMap CdWhBomRelation cdWhBomRelation);

    @GetMapping("/cdWhBomHeaders")
    JsonResponse<PageResponse<CdWhBomHeader>> searchCdWhBomHeader(@RequestBody CdWhBomHeader cdWhBomHeader);

    @PostMapping(value = "/midSiteWhControls/searchPage")
    JsonResponse<PageResponse<MidSiteWhControl>> searchPage(@RequestBody MidSiteWhControl midSiteWhControl);

    @PostMapping(value = "/midCustomerControls/searchPage")
    JsonResponse<PageResponse<MidCustomerControl>> searchPage(@RequestBody MidCustomerControl midCustomerControl);

    @PostMapping(value = "/db/midOrdertypeControl")
    JsonResponse<PageResponse<MidOrdertypeControl>> searchPage(@RequestBody MidOrdertypeControl midOrdertypeControl);

    @PostMapping(value = "/db/midGoodsControl")
    JsonResponse<PageResponse<MidGoodsControl>> searchPage(@RequestBody MidGoodsControl midGoodsControl);

    @PostMapping(value = "/midGoodstypeControls/searchPage")
    JsonResponse<PageResponse<MidGoodstypeControl>> searchPage(@RequestBody MidGoodstypeControl midGoodstypeControl);

    @PostMapping(value = "/db/midB2cCustomerControl")
    JsonResponse<PageResponse<MidB2cCustomerControl>> searchPage(@RequestBody MidB2cCustomerControl midB2cCustomerControl);

    @PostMapping(value = "/db/midB2cSiteWhControl")
    JsonResponse<PageResponse<MidB2cSiteWhControl>> searchPage(@RequestBody MidB2cSiteWhControl midB2cSiteWhControl);

    @PostMapping(value = "/db/midB2cOrdertypeControl")
    JsonResponse<PageResponse<MidB2cOrdertypeControl>> searchPage(@RequestBody MidB2cOrdertypeControl midB2cOrdertypeControl);

    @PostMapping(value = "/db/midB2cGoodsControl")
    JsonResponse<PageResponse<MidB2cGoodsControl>> searchPage(@RequestBody MidB2cGoodsControl midB2cGoodsControl);

    @PostMapping(value = "/db/midB2cGoodstypeControl")
    JsonResponse<PageResponse<MidB2cGoodstypeControl>> searchPage(@RequestBody MidB2cGoodstypeControl midB2cGoodstypeControl);

    @GetMapping(value = "/ebCustomer/searchByEbCustomer")
    JsonResponse<PageResponse<EbCustomer>> searchPage(@SpringQueryMap EbCustomer ebCustomer);

    @GetMapping(value = "/cdWarehouse/search")
    JsonResponse<PageResponse<CdWarehouse>> searchPage(@SpringQueryMap CdWarehouse cdWarehouse);

    @GetMapping(value = "/esCompany/searchByCodeAndNames")
    JsonResponse<PageResponse<EsCompany>> searchByCodeAndNames(@RequestParam("escoCompanyNameCn") String escoCompanyNameCn);

    // 会计主体按实体id批量查询接口 https://cf.annto.com/pages/viewpage.action?pageId=60715950
    @PostMapping("/searchByOperatingUnitIdList")
    JsonResponse<PageResponse<InvoiceUnitResponse>> searchByOperatingUnitIdList(@RequestBody InvoiceUnitRequest invoiceUnitRequest);

}
