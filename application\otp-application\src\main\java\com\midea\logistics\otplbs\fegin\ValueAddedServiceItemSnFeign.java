package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.ValueAddedServiceItemSn;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ValueAddedServiceItemSnFeign
 * Author: lindq2
 * Date: 2019-5-14 11:40:46
 * Description:增值服务单商品SN feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface ValueAddedServiceItemSnFeign {

    @RequestMapping(value = "/valueAddedServiceItemSn", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ValueAddedServiceItemSn valueAddedServiceItemSn);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItemSn/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItemSn/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ValueAddedServiceItemSn valueAddedServiceItemSn);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItemSn/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/valueAddedServiceItemSns", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap ValueAddedServiceItemSn valueAddedServiceItemSn);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @param valueAddedServiceItemSns
     * @return
     */
    @PostMapping(OrderRouters.VALUE_ADDED_SERVICE_ITEM_SN_BATCH_CREATE_OR_UPDATE)
    JsonResponse batchCreateOrUpdate(@RequestBody List<ValueAddedServiceItemSn> valueAddedServiceItemSns);

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @DeleteMapping(OrderRouters.VALUE_ADDED_SERVICE_ITEM_SN_BATCH_DELETE_BY_BUSINESSKEY)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

    /**
     * 根据增值单号删除SN信息
     */
    @GetMapping(OrderRouters.VALUE_ADDED_SERVICE_ITEM_SN_DELETE_BY_VDORDERNO)
    JsonResponse deleteByVdOrderNo(@RequestParam("vdOrderNo") String vdOrderNo);
}
