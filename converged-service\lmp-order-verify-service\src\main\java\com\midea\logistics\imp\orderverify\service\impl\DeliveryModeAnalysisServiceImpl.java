package com.midea.logistics.imp.orderverify.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoItemFeign;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.order.common.fegin.OrderInfoFeign;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.dto.CustomerOrderInfoExtendConfDto;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.midea.logistics.imp.orderverify.helper.LmpDeliveryTypeHelper;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.DeliveryModeAnalysisService;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.DeliveryTypeHelper;
import com.midea.logistics.otp.order.common.helper.MultiplePerOrderDeliveryHelper;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.SmallPartsHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeliveryModeAnalysisServiceImpl implements DeliveryModeAnalysisService {

    @Autowired
    private LmpDeliveryTypeHelper lmpDeliveryTypeHelper;

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private SmallPartsHelper smallPartsHelper;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private MultiplePerOrderDeliveryHelper multiplePerOrderDeliveryHelper;
    @Autowired
    private DeliveryTypeHelper deliveryTypeHelper;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private BusinessControlParamHelper businessControlParamHelper;
    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    private ShippingTypeRuleHelper shippingTypeRuleHelper;
    @Autowired
    private OrderHelper orderHelper;

    /**
     * !@配送方式解析 - 中台
     * @param orderInfo
     * @return
     */
    @Override
    @ZeebeFlow("SHIPPING_TYPE")
    public OrderInfoExt updateDeliveryModeAnalysis(OrderInfoExt orderInfo) {
        try {
            
            //原始订单为C2M的逆向单默认自提
            if (orderFlowHelper.isC2MOriginalOrder(orderInfo.getOriginOrderNo())) {
                lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.ZT);
                FlowListenerParam.success("原始订单为C2M默认自提");
                return orderInfo;
            }
            //C2M的2C订单的配送方式配置为快递
            if (C2MType.isC2M(orderInfo.getC2mType()) && BusinessMode.B2C.getName().equals(orderInfo.getBusinessMode())) {
                lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.EXPRESS);
                FlowListenerParam.success(DeliveryType.EXPRESS.getValue());
                return orderInfo;
            }
            // [19,20] 查业务控制参数 设置B2C宅配
            List<BusinessControlParamDetail> businessControlParamDetails = eOProductList(orderInfo);
            if (CollectionUtils.isNotEmpty(businessControlParamDetails)) {
                String defaultDeliveryType = BusinessMode.isB2B(orderInfo.getBusinessMode()) ? DeliveryType.DELIVERY.getKey() : DeliveryType.DOT.getKey();
                String deliveryKey = StringUtils.defaultIfBlank(orderInfo.getDeliveryType(), defaultDeliveryType);
                orderInfo.setDeliveryType(deliveryKey);
                lmpOrderFlowHelper.updateOrderInfo(orderInfo, "配送方式");
                FlowListenerParam.success(DeliveryType.getName(orderInfo.getDeliveryType()));
                return orderInfo;
            }
            CustomerOrderInfo entity = lmpOrderFlowHelper.getCustomerOrderInfo(orderInfo.getParentOrderNo());
            String subDeliveryType = orderInfo.getDeliveryType();
            String parentDeliveryTYype = entity.getDeliveryType();
            if (!StringUtils.isEmpty(subDeliveryType)) {
                String tip = smallPartsHelper.tipSmallExpress(orderInfo);
                //父单有，子单也有，并且一样的配送方式，那就是分仓的时候继承了父单，这里就显示一下
                if (ToolUtils.isNotEmpty(subDeliveryType) && ToolUtils.isNotEmpty(parentDeliveryTYype) && parentDeliveryTYype.equals(subDeliveryType)) {
                    FlowListenerParam.success("继承客户单：" + DeliveryType.getDeliveryType(orderInfo.getDeliveryType()).getValue() + tip);
                }else{
                    FlowListenerParam.success(DeliveryType.getDeliveryType(orderInfo.getDeliveryType()).getValue() + tip);
                }
                return orderInfo;
            }
            String completeSetNo = null;
            CustomerOrderInfoExtend customerOrderInfoExtend = null;
            if ((SourceSystem.isOFC(orderInfo.getSourceSystem()) || SourceSystem.isMRP(orderInfo.getSourceSystem()))
                && OrderType.isPOOrder(orderInfo.getOrderType())) {
                customerOrderInfoExtend = orderHelper.getCustomerOrderInfoExtend(orderInfo.getParentOrderNo());
                if (customerOrderInfoExtend != null) {
                    String confObj = customerOrderInfoExtend.getConfObj();
                    if (StringUtils.isNotEmpty(confObj)) {
                        CustomerOrderInfoExtendConfDto customerOrderInfoExtendConfDto = JSON.parseObject(confObj, CustomerOrderInfoExtendConfDto.class);
                        completeSetNo = customerOrderInfoExtendConfDto.getCompleteSetNo();
                    }

                    //判断父单拓展表【场景标识】loan_scenario_code=101（超区快递发货）配送方式配置为快递
                    if (LoanScenarioCode.isExpressDeliveryOverRegion(customerOrderInfoExtend.getLoanScenarioCode())) {
                        lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.EXPRESS);
                        String pre = SourceSystem.isOFC(orderInfo.getSourceSystem()) ? "美云销"
                                : SourceSystem.isMRP(orderInfo.getSourceSystem()) ? "美的零售"
                                : "";
                        FlowListenerParam.success(pre + "超区发货订单，仅支持发快递");
                        //套送订单要清空套送标识号
                        if(ToolUtils.isNotEmpty(completeSetNo)){
                            try {
                                lmpDeliveryTypeHelper.clearCompleteSetNo(orderInfo.getOrderNo(), customerOrderInfoExtend, orderInfo.getParentOrderNo());
                            } catch (Exception e) {
                                log.error("配送方式解析-clearCompleteSetNo->orderNo:{}", orderInfo.getOrderNo(), e);
                            }
                        }
                        
                        return orderInfo;
                    }

                    if (StringUtils.isNotEmpty(confObj)){
                        CustomerOrderInfoExtendConfDto customerOrderInfoExtendConfDto = JSON.parseObject(confObj, CustomerOrderInfoExtendConfDto.class);
                        
                        //处理套送订单配送方式
                        completeSetNo = customerOrderInfoExtendConfDto.getCompleteSetNo();
                        if(ToolUtils.isNotEmpty(completeSetNo)){
                            //套送号不为空,先解析订单上的平台到收货地址的配送方式,如果是快递/自提/快递加送装,清空标识.
                            ShippingTypeRule shippingTypeRule = lmpDeliveryTypeHelper.getDeliveryRule(entity,orderInfo, BusinessMode.B2C, true, true);
                            if(shippingTypeRule != null 
                                    && shippingTypeRule.getDeliveryType() != null 
                                    && DeliveryType.getDeliveryType(shippingTypeRule.getDeliveryType()) != null
                                    && (DeliveryType.isZT(shippingTypeRule.getDeliveryType()) || DeliveryType.isEXPRESS(shippingTypeRule.getDeliveryType()) || DeliveryType.isEAD(shippingTypeRule.getDeliveryType()))) {
                                DeliveryType deliveryType = DeliveryType.getDeliveryType(shippingTypeRule.getDeliveryType());
                                //清空套送号
                                lmpDeliveryTypeHelper.clearCompleteSetNo(orderInfo.getOrderNo(), customerOrderInfoExtend, orderInfo.getParentOrderNo());
                                
                                log.info("配送方式解析-套送,orderNo={}, 套送号completeSetNo={}, 订单到收货地匹配的是{},不走套送", orderInfo.getOrderNo(), completeSetNo, shippingTypeRule.getDeliveryType());
                                lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, deliveryType);
                                FlowListenerParam.success(deliveryType.getValue() + "，不执行套送");
                                return orderInfo;
                            }
                            //重置特殊场景
                            orderInfo.setDeliveryTypeSpecialType(null);

                            //20250802 泓铄 同一套送号的订单,配送方式一致
                            String deliveryTypeKeyCache = redisHelper.getSetDeliveryDeliveryTypeCache(completeSetNo);
                            DeliveryType deliveryType = DeliveryType.getDeliveryType(deliveryTypeKeyCache);
                            if(ToolUtils.isNotEmpty(deliveryTypeKeyCache) && deliveryType != null){
                                log.info("配送方式解析-套送,orderNo={}, 采用套送号completeSetNo={}的缓存设置缓存配送方式={}", orderInfo.getOrderNo(), completeSetNo, deliveryType.getValue());
                                lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, deliveryType);
                                FlowListenerParam.success(deliveryType.getValue() + "，套送订单");
                                return orderInfo;
                            }
                        }
                    }
                    
                    //B2C，O2O订单/国补订单强制走网点
                    if (BusinessMode.isB2C(orderInfo.getBusinessMode())
                        && (OrderLabelEnum.isO2O(customerOrderInfoExtend.getOrderLabel()) || Objects.equals(3, orderInfo.getEmergenceFlag()))) {
                        List<BusinessControlParamDetail> params = businessParamHelper.getBusinessControlDetailByIndex(CommonConstant.O2O_TO_NET_WH, orderInfo);
                        if (!CollectionUtils.isEmpty(params)) {
                            lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.DOT);
                            FlowListenerParam.success(String.format("O2O/国补订单默认配送方式，%s", DeliveryType.DOT.getValue()));
                            return orderInfo;
                        }
                    }
                }
            }

            if (StringUtils.isEmpty(orderInfo.getOrderNo())) {
                throw BusinessException.fail("订单号不能为空!");
            }
            BusinessMode businessMode = BusinessMode.valueOf(orderInfo.getBusinessMode());
            OrderType orderType = OrderType.valueOf(orderInfo.getOrderType());
            DeliveryType deliveryType;

            //订单类型是ADI、ADO、TFI、TFO、TF 默认ZT 20210423  我已经没有时间改动了……
            if (OrderType.isADIOrder(orderInfo.getOrderType())
                || OrderType.isADOOrder(orderInfo.getOrderType())
                || OrderType.isTFIOrder(orderInfo.getOrderType())
                || OrderType.isTFOOrder(orderInfo.getOrderType())
                || OrderType.isTFOrder(orderInfo.getOrderType())) {

                orderInfo.setDeliveryType(DeliveryType.ZT.getKey());
                OrderInfo newOrderInfo = new OrderInfo();
                newOrderInfo.setId(orderInfo.getId());
                newOrderInfo.setVersion(orderInfo.getVersion());
                newOrderInfo.setDeliveryType(DeliveryType.ZT.getKey());
                newOrderInfo.setOrderNo(orderInfo.getOrderNo());
                newOrderInfo.setDeliveredVerifyFlag(orderInfo.getDeliveredVerifyFlag());
                orderFlowHelper.updateOrderInfo(newOrderInfo, "配送方式");
                FlowListenerParam.success(DeliveryType.ZT.getValue());
                return orderInfo;
            }


            if (StringUtils.isNotBlank(entity.getDeliveryType())) {
                // 自动拆单子单自提(一网无遗)
                if (OrderType.isPIOrder(orderInfo.getOrderType()) && DeliveryType.isDelivery(entity.getDeliveryType())
                    && null != entity.getHandleSplitFlag() && 9 == entity.getHandleSplitFlag()) {
                    deliveryType = DeliveryType.ZT;
                    lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.ZT);
                } else {
                    deliveryType = DeliveryType.getDeliveryType(entity.getDeliveryType());
                    if( null == deliveryType) {
                        throw BusinessException.fail("配送方式[" + entity.getDeliveryType() + "]不存在，请检查");
                    }
                    lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.getDeliveryType(entity.getDeliveryType()));
                }
                FlowListenerParam.success("继承客户单：" + deliveryType.getValue());
                return orderInfo;
            }
    
            //来源系统是OFC,service_type为3（退换机）,且关联单号为WHO开头的订单,配送方式默认为宅配
            //202405 泓铄 移除退换机固定宅配逻辑
//            if(orderFlowHelper.isOfcThjOrder(orderInfo)){
//                lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.DOT);
//                FlowListenerParam.success(DeliveryType.DOT.getValue());
//                return orderInfo;
//            }

            // 来源系统是MRP,订单标签为JSPS,默认为自提
            if (orderFlowHelper.isMrpJspsOrder(orderInfo)){
                lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, DeliveryType.ZT);
                FlowListenerParam.success(DeliveryType.ZT.getValue());
                return orderInfo;
            }

            ShippingTypeRule shippingTypeRule = null;
            //B2C配送方式解析
            if (BusinessMode.B2C == businessMode) {
                //2021年11月12日16:22:39 阳阳：先获取写死的规则，后查询配置
                deliveryType = lmpDeliveryTypeHelper.getDeliveryType(entity, null, BusinessMode.B2C);

                if (null == deliveryType) {

                    shippingTypeRule = lmpDeliveryTypeHelper.getDeliveryRule(entity,orderInfo, BusinessMode.B2C, true, false);
                    
                    if (shippingTypeRule == null && OrderType.YS == OrderType.valueOf(orderInfo.getOrderType()) && !StringUtils.isEmpty(entity.getWhCode())) {
                        shippingTypeRule = lmpDeliveryTypeHelper.getDeliveryRule(entity,orderInfo, BusinessMode.B2C, false, false);
                    }

                    if (shippingTypeRule == null) {
                        orderInfo.setNetDeliveryType(NetDeliveryTypeEnum.DOT.getKey());
                        //2022年3月7日17:22:19 李娟：YS 配置没读到 2C的默认直配，2B的默认为运输，
                        deliveryType = OrderType.YS == OrderType.valueOf(orderInfo.getOrderType()) ? DeliveryType.WAREHOUSEMATCHING : DeliveryType.DOT;
                        //2021年9月18日16:16:14 泓铄：小件配件类B2C订单实现系统识别为直配/快递
                        deliveryType = DeliveryType.getDeliveryType(smallPartsHelper.setSmallExpressSetDeliveryType(deliveryType.getKey(),entity,orderInfo));
                    } else {
                        orderInfo.setNetDeliveryType(shippingTypeRule.getNetDeliveryType());
                        String ruleDeliveryType = shippingTypeRule.getDeliveryType();
                        //2021年9月18日16:16:14 泓铄：小件配件类B2C订单实现系统识别为直配/快递
                        ruleDeliveryType = smallPartsHelper.setSmallExpressSetDeliveryType(ruleDeliveryType,entity,orderInfo);
                        deliveryType = DeliveryType.getDeliveryType(ruleDeliveryType);
                    }
                }

                //特殊场景不走固定逻辑
                if(ToolUtils.isEmpty(orderInfo.getDeliveryTypeSpecialType())){
                    //2023年5月22日14:47:36 泓铄 : (一单多件)CAINAO/OFC，PO单，子单数量大于等于【电商直配仓库配置EC_WAREHOUSEMATCHING_WH】参数值，配送方式宅配改为直配
                    deliveryType = multiplePerOrderDeliveryHelper.setMultiplePerOrder(orderInfo, deliveryType);

                    //202405 泓铄：去除掉高端品牌强制转宅配的逻辑，后续不需要查询配送方式是否在高端配送方式配置中
                    //deliveryType = deliveryTypeHelper.mappingVipServiceDeliveryType(orderInfo.getParentOrderNo(),orderInfo.getOrderNo(),orderInfo.getOrderType(),orderInfo.getSourceSystem(),deliveryType);

                }

                //202506 泓铄 送新拉旧订单，新机不经网点配送，导致网点取件时取不到旧机买赔，送新拉旧&先取后送订单需要经网点配送 https://cf.annto.com/pages/viewpage.action?pageId=80790073
                if (!CommonConstant.COMPLETE_SET_NO_YES.equals(orderInfo.getDeliveryTypeSpecialType())
                        && !CommonConstant.COMPLETE_SET_NO_NO.equals(orderInfo.getDeliveryTypeSpecialType())
                        && shippingTypeRuleHelper.isFetchSendOrSXQJOrder(orderInfo, deliveryType)) {
                    deliveryType = DeliveryType.DOT;
                    orderInfo.setDeliveryTypeSpecialType(CommonConstant.FETCHSEND_OR_SXQJ_TO_DOT);
                }
                
            }
            //B2B配送方式解析
            else {
                deliveryType = lmpDeliveryTypeHelper.getDeliveryType(null, orderInfo, BusinessMode.B2B);
                if (null == deliveryType) {
                    shippingTypeRule = lmpDeliveryTypeHelper.getDeliveryRule(entity,orderInfo, BusinessMode.B2B, true, false);
                    log.info("返回配送方式规则{}", JSON.toJSONString(shippingTypeRule));
                    if (shippingTypeRule == null && OrderType.YS == OrderType.valueOf(orderInfo.getOrderType()) && !StringUtils.isEmpty(orderInfo.getWhCode())) {
                        shippingTypeRule = lmpDeliveryTypeHelper.getDeliveryRule(entity,orderInfo, BusinessMode.B2B, false, false);
                    }
                    if (shippingTypeRule == null) {
                        deliveryType = OrderType.YS == OrderType.valueOf(orderInfo.getOrderType()) ? DeliveryType.YS : DeliveryType.DELIVERY;
                        orderInfo.setNetDeliveryType(OrderType.YS == OrderType.valueOf(orderInfo.getOrderType()) ? null : NetDeliveryTypeEnum.DELIVERY.getKey());
                    } else {
                        deliveryType = DeliveryType.getDeliveryType(shippingTypeRule.getDeliveryType());
                        orderInfo.setNetDeliveryType(shippingTypeRule.getNetDeliveryType());
                    }
                }
            }
            
            lmpDeliveryTypeHelper.updateDeliveryType(orderInfo, deliveryType);

            //设置套送配送方式缓存
            if (CommonConstant.COMPLETE_SET_NO_YES.equals(orderInfo.getDeliveryTypeSpecialType()) && completeSetNo != null 
                    && ToolUtils.isEmpty(redisHelper.getSetDeliveryDeliveryTypeCache(completeSetNo))) {
                log.info("配送方式解析-套送, 设置配送方式缓存, orderNo={}, completeSetNo={},设置缓存配送方式={}", orderInfo.getOrderNo(), completeSetNo, deliveryType.getValue());
                redisHelper.setSetDeliveryDeliveryTypeCache(completeSetNo, deliveryType.getKey());
            }
            
            String tipLeft1 = deliveryTypeHelper.getLeftTips(orderInfo, deliveryType.getKey());
            String tip = smallPartsHelper.tipSmallExpress(orderInfo);
//            String tip2 = multiplePerOrderDeliveryHelper.setMultiplePerOrderTip(orderInfo);
            String tip3 = redisHelper.getVipServiceDotDeliveryType(orderInfo.getOrderNo());
            log.info("IN2流程输出配送方式{}", JSON.toJSONString(deliveryType));
            String tipRightLeft = deliveryTypeHelper.getRightTips(orderInfo, deliveryType.getKey());
            FlowListenerParam.success(tipLeft1 + deliveryType.getValue() + tip + tip3 + tipRightLeft);
            return orderInfo;
        } catch (Exception e) {
            log.error("配送方式解析失败，失败原因:", e);
            throw BusinessException.fail("配送方式解析失败，失败原因:" + e.getMessage());
        }

    }

    /**
     * 产品需求：<a href="https://cf.annto.com/pages/viewpage.action?pageId=43527540">...</a>
     */
    private List<BusinessControlParamDetail> eOProductList(OrderInfoExt orderInfo) {
        boolean flag = (SourceSystem.isOFC(orderInfo.getSourceSystem()) || SourceSystem.isMRP(orderInfo.getSourceSystem()))
                        && OrderType.isPOAOOrder(orderInfo.getOrderType())
                        && ! OrderDistinctionFlag.isBase(orderInfo.getOrderDistinctionFlag())
                        && ServiceType.isToc(orderInfo.getServiceType());
        if (!flag) {
            return null;
        }
        List<OrderInfoItem> items = orderInfo.getOrderInfoItems();
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        Set<String> buCodeSet = items.stream().map(OrderInfoItem::getBuCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(buCodeSet) || buCodeSet.size() != 1) {
            log.info("免费工程TOC 客户订单{} bu_code事业部含多个，跳过", orderInfo.getCustomerOrderNo());
            return null;
        }
        String buCode = CollUtil.getFirst(buCodeSet);
        if (StringUtils.isBlank(buCode)) {
            return null;
        }
        String entityId = dictHelper.getDictVaule("ENTITY_ID_TRANS", buCode);
        BusinessControlParamDetail b = new BusinessControlParamDetail();
        b.setEntityId(StringUtils.defaultIfBlank(entityId, buCode));
        return businessParamHelper.getBusinessControlDetailByIndex("EO_Productquantity", b);
    }

}
