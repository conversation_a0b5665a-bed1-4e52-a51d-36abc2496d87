package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.common.helper.bean.Transfer;
import com.midea.logistics.otp.ordertask.converged.domain.TaskConvergedRouters;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 到车登记
 */

@FeignClient(value = "logistics-lcp-dc-atomic",url = BaseRouter.LOGISTICS_OTP_ORDERTASK_CONVERGED_SERVICE)
public interface DcFegin {

    /**
     * 保存到车登记信息
     *
     * @return
     */
    @GetMapping(TaskConvergedRouters.TRANSFER_PAGE)
    JsonResponse<PageResponse<Transfer>> search(@SpringQueryMap Transfer transfer) ;

}