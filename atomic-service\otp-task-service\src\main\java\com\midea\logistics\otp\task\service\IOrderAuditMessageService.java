package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.task.domain.bean.OrderAuditMessage;
import com.mideaframework.core.web.PageResponse;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: IOrderAuditMessageService
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:IOrderAuditMessageService服务接口
 */
public interface IOrderAuditMessageService {

    Integer saveOrderAuditMessage(OrderAuditMessage orderAuditMessage);

    Integer deleteOrderAuditMessage(OrderAuditMessage orderAuditMessage);

    Integer updateOrderAuditMessage(OrderAuditMessage orderAuditMessage);

    OrderAuditMessage selectOneOrderAuditMessage(OrderAuditMessage orderAuditMessage);

    PageResponse<OrderAuditMessage> selectOrderAuditMessagePage(OrderAuditMessage orderAuditMessage);

    List<OrderAuditMessage> selectOrderAuditMessageByIndex(OrderAuditMessage orderAuditMessage);

    int countOrderAuditMessageByIndex(OrderAuditMessage orderAuditMessage);

    Integer insertBatch(List<OrderAuditMessage> orderAuditMessages);

    Integer batchUpdate(List<OrderAuditMessage> orderAuditMessages);

    Integer deleteBatch(List<OrderAuditMessage> orderAuditMessages);
}