#### 系统建康状态 #####
management:
  endpoints:
    web:
      exposure:
        include: "*"
  server:
    servlet:
      context-path: /
    ssl:
      enabled: false
  endpoint:
    health:
      show-details: always

logging:
  file: "${spring.logistics-log.path}/${spring.application.name}.log"
  level:
    root: INFO

mdmcache:
  feignConfig:
    enabled: false

spring:
  cloud:
    alibaba:
      seata:
        tx-service-group: logistics_otp_tx_group
  mvc:
    date-format: yyyy-MM-dd HH:mm:ss
  jackson:
    #日期格式化
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      #格式化输出
      indent_output: true
      #忽略无法转换的对象
      fail_on_empty_beans: false
    #设置空如何序列化
    defaultPropertyInclusion: NON_NULL
    deserialization:
      #允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
    parser:
      #允许出现特殊字符和转义符
      allow_unquoted_control_chars: true
            #允许出现单引号
      allow_single_quotes: true

server:
  tomcat:
    basedir: ./tomcat

