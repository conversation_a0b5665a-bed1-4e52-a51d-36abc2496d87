package com.midea.logistics.otp.orderverify.service.impl;

import com.midea.logistics.otp.enums.OrderStatus;
import com.midea.logistics.otp.order.common.mq.producer.OrderAuditMessageProducer;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.ordertask.converged.domain.bean.OrderMq;
import com.midea.logistics.otp.orderverify.service.TaskService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {

    @Autowired
    private OrderAuditMessageProducer orderAuditMessageProducer;


    @Override
    public JsonResponse sentTask(OrderInfo orderInfo) {
        if (null == orderInfo || orderInfo.getOrderNo() == null) {
            throw BusinessException.fail("订单号为空");
        }

        String orderNo = orderInfo.getOrderNo();

        OrderMq orderMq = new OrderMq();
        orderMq.setOrderNo(orderNo);
        orderMq.setNode(OrderStatus.AUDITED.getKey());
        boolean sent = orderAuditMessageProducer.sent(orderMq);

        if (!sent) {
            throw BusinessException.fail("下发调用队列失败:"+orderNo);
        }

        return JsonResponse.success("");

    }

}




