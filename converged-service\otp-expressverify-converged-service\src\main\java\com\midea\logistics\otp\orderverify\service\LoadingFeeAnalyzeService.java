package com.midea.logistics.otp.orderverify.service;

import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.mideaframework.core.web.JsonResponse;

/**
 * @Program: logistics-otp
 * @ClassName: LoadingFeeAnalyzeService
 * @Author： <PERSON><PERSON><PERSON><PERSON>
 * @Date： 2019-06-10 17:36
 * @Description:
 */
public interface LoadingFeeAnalyzeService {
    JsonResponse loadingFeeAnalyzeRedis(OrderInfo orderInfo);
}
