package com.midea.logistics.imp.orderverify.rest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Before;
import org.junit.Test;

import com.midea.logistics.otp.common.utils.SUtils;
import com.midea.logistics.otp.order.common.helper.TransRelationVerifyHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目名称：logistics-otp
 * 功能说明：
 *
 * <AUTHOR>
 * @createtime 2024/1/8 13:47
 */
@Slf4j
public class AutoTransferFromOutTaskTest {
    
    @Before
    public void init() {
    
    }
    
    @Test
    public void first() {
    
    
        /**
         *
         情况一（同样的散件/套件，数量不同）
         A散件/套件   数量8    上游行号U001
         A散件/套件   数量7    上游行号U002
         A散件/套件   数量6    上游行号U003
     
         情况二（同样的散件/套件，数量相同）
         A散件/套件   数量8    上游行号U001
         A散件/套件   数量8    上游行号U002
         A散件/套件   数量8    上游行号U003
         
         情况三 单个散件/套机
         
         */
        
        //出库任务
        List<TaskExt> outTaskList = new ArrayList<>();
        
        //1、同样的散件/套件，数量不同
        TaskExt  t1  = new TaskExt();
        List<TaskItem> taskItemList = new ArrayList<>();
        TaskItem t_item_1 = new TaskItem();
        t_item_1.setItemCode("0001");
        t_item_1.setItemSuiteCode("S_0001");
        t_item_1.setUpperLineNo("U_0001");
        t_item_1.setPlanQty(new BigDecimal(2));
        taskItemList.add(t_item_1);
        TaskItem t_item_2 = new TaskItem();
        t_item_2.setItemCode("0001");
        t_item_2.setItemSuiteCode("S_0001");
        t_item_2.setUpperLineNo("U_0001");
        t_item_2.setPlanQty(new BigDecimal(6));
        taskItemList.add(t_item_2);
        t1.setTaskItems(taskItemList);
        outTaskList.add(t1);
    
        TaskExt  t2  = new TaskExt();
        List<TaskItem> taskItemList2 = new ArrayList<>();
        TaskItem t2_item_1 = new TaskItem();
        t2_item_1.setItemCode("0001");
        t2_item_1.setItemSuiteCode("S_0001");
        t2_item_1.setUpperLineNo("U_0002");
        t2_item_1.setPlanQty(new BigDecimal(5));
        taskItemList2.add(t2_item_1);
        TaskItem t2_item_2 = new TaskItem();
        t2_item_2.setItemCode("0001");
        t2_item_2.setItemSuiteCode("S_0001");
        t2_item_2.setUpperLineNo("U_0002");
        t2_item_2.setPlanQty(new BigDecimal(5));
        taskItemList2.add(t2_item_2);
        t2.setTaskItems(taskItemList2);
        outTaskList.add(t2);
        
        //2、单个散件
        TaskExt  t3  = new TaskExt();
        List<TaskItem> taskItemList3 = new ArrayList<>();
        TaskItem t3_item_1 = new TaskItem();
        t3_item_1.setItemCode("0003");
        t3_item_1.setUpperLineNo("U_0003");
        //t3_item_1.setItemSuiteCode("S_0001");
        t3_item_1.setPlanQty(new BigDecimal(4));
        taskItemList3.add(t3_item_1);
        t3.setTaskItems(taskItemList3);
        outTaskList.add(t3);
    
        //3、同样的散件/套件，数量同
        TaskExt  t4  = new TaskExt();
        List<TaskItem> taskItemList4 = new ArrayList<>();
        TaskItem t4_item_1 = new TaskItem();
        t4_item_1.setItemCode("0004");
        t4_item_1.setItemSuiteCode("S_0004");
        t4_item_1.setUpperLineNo("U_0004");
        t4_item_1.setPlanQty(new BigDecimal(1));
        taskItemList4.add(t4_item_1);
        TaskItem t4_item_2 = new TaskItem();
        t4_item_2.setItemCode("0004");
        t4_item_2.setItemSuiteCode("S_0004");
        t4_item_2.setUpperLineNo("U_0004");
        t4_item_2.setPlanQty(new BigDecimal(3));
        taskItemList4.add(t4_item_2);
        t4.setTaskItems(taskItemList4);
        outTaskList.add(t4);
    
        TaskExt  t5  = new TaskExt();
        List<TaskItem> taskItemList5 = new ArrayList<>();
        TaskItem t5_item_1 = new TaskItem();
        t5_item_1.setItemCode("0004");
        t5_item_1.setItemSuiteCode("S_0004");
        t5_item_1.setUpperLineNo("U_0004");
        //t5_item_1.setUpperLineNo("U_0004");
        t5_item_1.setPlanQty(new BigDecimal(2));
        taskItemList5.add(t5_item_1);
        TaskItem t5_item_2 = new TaskItem();
        t5_item_2.setItemCode("0004");
        t5_item_2.setItemSuiteCode("S_0004");
        t5_item_2.setUpperLineNo("U_0004");
        //t5_item_2.setUpperLineNo("U_0004");
        t5_item_2.setPlanQty(new BigDecimal(2));
        taskItemList5.add(t5_item_2);
        t5.setTaskItems(taskItemList5);
        outTaskList.add(t5);
        
        //4、单个套机
        TaskExt  t6  = new TaskExt();
        List<TaskItem> taskItemListy6 = new ArrayList<>();
        TaskItem t6_item_1 = new TaskItem();
        t6_item_1.setItemCode("0006");
        t6_item_1.setItemSuiteCode("S_0006");
        t6_item_1.setUpperLineNo("U_0006");
        t6_item_1.setPlanQty(new BigDecimal(4));
        taskItemListy6.add(t6_item_1);
        t6.setTaskItems(taskItemListy6);
        outTaskList.add(t6);
        
        //入库父单的所有明细
        List<CustomerOrderItem> inCustomerOrderItems = new ArrayList<>();
        
        //1、同样的散件/套件，数量不同
        CustomerOrderItem item_1 = new CustomerOrderItem();
        item_1.setId(1l);
        item_1.setItemCode("0001");
        item_1.setItemSuiteCode("S_0001");
        item_1.setPlanQty(new BigDecimal(10));
        inCustomerOrderItems.add(item_1);
        
        CustomerOrderItem item_2 = new CustomerOrderItem();
        item_2.setId(2l);
        item_2.setItemCode("0001");
        item_2.setItemSuiteCode("S_0001");
        item_2.setPlanQty(new BigDecimal(8));
        inCustomerOrderItems.add(item_2);
    
        //2、单个散件
        CustomerOrderItem item_3 = new CustomerOrderItem();
        item_3.setId(3l);
        item_3.setItemCode("0003");
        item_3.setPlanQty(new BigDecimal(4));
        inCustomerOrderItems.add(item_3);
    
    
        //3、同样的散件/套件，数量相同
        CustomerOrderItem item_4 = new CustomerOrderItem();
        item_4.setId(4l);
        item_4.setItemCode("0004");
        item_4.setItemSuiteCode("S_0004");
        item_4.setPlanQty(new BigDecimal(4));
        inCustomerOrderItems.add(item_4);
    
        CustomerOrderItem item_5 = new CustomerOrderItem();
        item_5.setId(5l);
        item_5.setItemCode("0004");
        item_5.setItemSuiteCode("S_0004");
        item_5.setPlanQty(new BigDecimal(4));
        inCustomerOrderItems.add(item_5);
    
        //4、单个套机
        CustomerOrderItem item_6 = new CustomerOrderItem();
        item_6.setId(6l);
        item_6.setItemCode("0006");
        item_6.setItemSuiteCode("S_0006");
        item_6.setPlanQty(new BigDecimal(4));
        inCustomerOrderItems.add(item_6);
        
        autoTranfer(outTaskList, inCustomerOrderItems);
    
    }
    
    private void autoTranfer(List<TaskExt> outTaskList, List<CustomerOrderItem> inCustomerOrderItems) {
        List<TaskItem> outTaskAllItems = TransRelationVerifyHelper.getAllTaskItems(outTaskList);
        //按照[商品编码+套机编码]分组入库单明细
        Map<String, List<CustomerOrderItem>> inCustomerItemCensus = SUtils.census(inCustomerOrderItems, this::suiteItemCode, this::getCustomerItem);
        //按照[商品编码+套机编码+上游商品行号]分组出库的任务明细
        Map<String, List<TaskItem>> outTaskItemCensus = SUtils.census(outTaskAllItems, this::taskSuiteItemCodeUpperLine,this::getTaskItem);
        //按照[商品编码+套机编码+上游商品行号]分组出库的任务明细的数量
        Map<String, BigDecimal> outTaskItemPlanQtyCensus = TransRelationVerifyHelper.getOutTaskItemPlanQty(outTaskItemCensus);
        //出库明细占用入库父单明细的信息[商品编码+套机编码+上游商品行号+父单明细ID]=>占用
        Map<String, Triple<String,BigDecimal,CustomerOrderItem>> taskItemOccupyInCustomerItemInfo = new HashMap<String, Triple<String,BigDecimal,CustomerOrderItem>>();
        for (TaskExt out : outTaskList) {
            List<TaskItem> outTaskItems = out.getTaskItems();
            for (TaskItem taskItem : outTaskItems) {
                //通过一些复杂的算法，根据出库明细找出唯一的入库父单商品
                CustomerOrderItem matchCustomerOrderItem = TransRelationVerifyHelper.getUniqueCustomerItem("Lbx0001", inCustomerItemCensus, outTaskItemPlanQtyCensus, taskItemOccupyInCustomerItemInfo, taskItem);
                if (null == matchCustomerOrderItem) {
                    System.out.println(taskItem.getItemCode()+":找不到唯一匹配的");
                }else{
                    System.err.println(taskItem.getItemCode()+"找到了===>"+matchCustomerOrderItem.getId());
                }
            }
        }
    }
    
    public String suiteItemCode(CustomerOrderItem item){
        return item.getItemCode()+"_"+item.getItemSuiteCode();
    }
    public String taskSuiteItemCodeUpperLine(TaskItem item){
        return item.getItemCode()+"_"+item.getItemSuiteCode()+"_"+item.getUpperLineNo();
    }
    
    public CustomerOrderItem getCustomerItem(CustomerOrderItem item){
        return item;
    }
    public TaskItem getTaskItem(TaskItem item){
        return item;
    }
}
