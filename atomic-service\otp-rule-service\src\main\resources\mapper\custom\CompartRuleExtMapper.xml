<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.CompartRuleMapper">

    <select id="searchByInOutTypesAndSiteCodes"
            resultType="com.midea.logistics.otp.rule.domain.bean.CompartRule">
        select
            <include refid="searchFieldsSql"/>
        from compart_rule
        where delete_flag=0
        and in_out_type in (
            <foreach collection="inOutTypes" separator="," item="inOutType">
                #{inOutType}
            </foreach>
        )
        and site_code in (
            <foreach collection="siteCodes" separator="," item="siteCode">
                #{siteCode}
            </foreach>
        )
    </select>

</mapper>