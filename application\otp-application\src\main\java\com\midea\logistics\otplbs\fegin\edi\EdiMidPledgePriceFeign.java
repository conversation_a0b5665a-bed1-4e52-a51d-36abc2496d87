package com.midea.logistics.otplbs.fegin.edi;

import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: EdiMidPledgePriceFeign
 * @description:
 * @author: kongly1
 * @date: 2021-9-23 10:24
 */
@FeignClient(value = "logistics-edi-agg-service")
public interface EdiMidPledgePriceFeign {

    /**
     * edi 质押价格同步
     *
     * @return
     */
    @GetMapping("/pledgPriceSyn")
    JsonResponse<Boolean> pledgPriceSyn();
}
