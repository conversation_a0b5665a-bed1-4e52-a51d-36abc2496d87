package com.midea.logistics;

import com.alibaba.fastjson.JSONObject;
import com.annto.framework.easyjunit.disable.apollo.EasyJunitDisableApollo;
import com.annto.framework.easyjunit.mariadb.EasyJunitMariadb;
import com.annto.framework.easyjunit.redis.EasyJunitRedis;
import com.annto.framework.easyjunit.rocketmq.EasyJunitRocketMQ;
import com.google.common.collect.Lists;
import com.midea.logistics.otp.rule.RuleServiceApplication;
import com.midea.logistics.otp.rule.domain.bean.AssembleRuleConfig;
import com.midea.logistics.otp.rule.domain.request.IssueRuleRequest;
import com.midea.logistics.otp.rule.rest.IssueRuleRest;
import com.midea.logistics.otp.rule.service.IAssembleRuleConfigService;
import com.mideaframework.core.web.JsonResponse;
import javax.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.List;


/**
 *<pre>
 * 单元测试原则：
 *
 * 1. 隔离线上环境，需要屏蔽(apollo、db、mq)
 *
 * 2. 只测 public 方法
 *
 * 3. 只测一层调用单元（private除外),测试方法的内部调用链路直接 mock
 *
 * 4. 覆盖当前单元所有 if\switch 分支
 *
 * 5. 结果用 Assertions 代码校验，严禁人工校验
 *
 *</pre>
 *
 * <AUTHOR>
 * @date 2024/6/4-18:03
 */
@EasyJunitDisableApollo
@EasyJunitRocketMQ
@EasyJunitMariadb
@EasyJunitRedis
@ActiveProfiles("local")
@TestPropertySource(locations = "classpath:local.properties")
@SpringBootTest(classes = RuleServiceApplication.class)
public class LogisticsIssueRuleTest {

    @Autowired
    private IssueRuleRest issueRuleRest;
    private static final Logger log = LoggerFactory.getLogger(LogisticsIssueRuleTest.class);
    @Autowired
    private IAssembleRuleConfigService iAssembleRuleConfigService;
    
    @Test
    public void batchCreateOrUpdate() {
        HttpServletRequest request =null;
        IssueRuleRequest issueRuleRequest = new IssueRuleRequest();

        issueRuleRequest = JSONObject.parseObject("{\"orderType\":\"CZF\",\"logisticMode\":\"TW\",\"pickFlag\":0,\"taskType\":\"IN\",\"taskStatus\":\"50\",\"issueSystem\":\"LWMS\",\"whCode\":\"W204550\",\"whName\":\"佛山富景物流园大宝仓\",\"systemCategory\":\"W\",\"siteName\":\"佛山电商中心\",\"siteCode\":\"54394914138\",\"version\":1,\"id\":1058}",IssueRuleRequest.class);
        JsonResponse jsonResponse = issueRuleRest.batchCreateOrUpdate(Lists.newArrayList(issueRuleRequest),request);
        Assertions.assertTrue(jsonResponse.judgeSuccess());
        log.info("finish");
    }

    @Test
    public void batchCreateOrUpdateAssembleConfigRule() {
        String jsonArray = "";
        AssembleRuleConfig assembleRuleConfig = new AssembleRuleConfig();
        assembleRuleConfig.setId(40L);
        assembleRuleConfig.setAddressControl(-1);
        List<AssembleRuleConfig> list = new ArrayList<AssembleRuleConfig>() ;
        list.add(assembleRuleConfig);
        iAssembleRuleConfigService.saveOrUpdateBatch(list);
    }
    
}
