package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.service.EcommerceCategoriesAnalysisTag;
import com.midea.logistics.otp.enums.CommerceCategoriesEnum;
import com.midea.logistics.otp.enums.OrderOperateType;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.OrderLogHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.service.CommerceCategoriesTagService;
import com.midea.logistics.otp.order.common.service.CommerceCategoriesTaskService;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class EcommerceCategoriesAnalysisImpl implements EcommerceCategoriesAnalysisTag {

    @Autowired
    private CommerceCategoriesTagService commerceCategoriesTagService;
    @Autowired
    private CommerceCategoriesTaskService commerceCategoriesTaskService;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private OrderLogHelper orderLogHelper;

    @Override
    // @ZeebeFlow("ECOMMERCE_CATEGORIES_ANALYSIS")   去除 ZeebeFlow, 用不上切面的逻辑
    public OrderInfoExt analysis(OrderInfoExt orderInfo) {

        boolean dpOrSo = commerceCategoriesTaskService.isDpOrSo(orderInfo);
        if (!dpOrSo) {
            return orderInfo;
        }

        OrderOperateType operateType = OrderOperateType.ECOMMERCE_CATEGORIES_ANALYSIS;
        try {
            // 调用 获取字段
            String commerceCategories = commerceCategoriesTaskService.processOrder(orderInfo);

            // 记录日志
            orderLogHelper.saveLog(orderInfo, operateType, null, CommerceCategoriesEnum.getName(commerceCategories));

        } catch (Exception e) {
            log.error("电商分类解析解析失败，失败原因: {}", e.getMessage());
            orderLogHelper.saveLog(orderInfo, operateType, null, "解析失败" + e.getMessage());
        }

        return orderInfo;
    }

    /**
     * 异步电商分类打标， gl:不需要记录日志 , 针对 wft.bpmn
     * @param orderInfo
     * @return
     */
    @Override
    public CustomerOrderInfoExt asyncAnalysis(CustomerOrderInfoExt customerOrderInfo) {
        boolean dpOrSo = commerceCategoriesTaskService.isDpOrSo(customerOrderInfo);
        if (!dpOrSo) {
            return customerOrderInfo;
        }
        CompletableFuture.runAsync(() -> {
            try {
                //1、查询所有的子单
                String parentOrderNo = customerOrderInfo.getOrderNo();
                List<OrderInfo> orderInfoList = orderverifyHelper.getOrderInfoByParentNo(parentOrderNo);
                if (CollectionUtils.isEmpty(orderInfoList)) {
                    return;
                }
                // 调用 获取字段
                for (OrderInfo orderInfo : orderInfoList) {
                    commerceCategoriesTaskService.processOrder(orderInfo);
                }
            } catch (Exception e) {
                log.error("电商分类解析解析失败，失败原因: {}", e.getMessage());
            }
        });

        return customerOrderInfo;
    }
}
