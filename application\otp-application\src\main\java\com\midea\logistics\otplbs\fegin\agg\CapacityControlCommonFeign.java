package com.midea.logistics.otplbs.fegin.agg;
import com.midea.logistics.iflow.mbmp.domain.dto.RespCreateDraftDto;
import com.midea.logistics.otp.bean.agg.CapacityControlDto;
import com.midea.logistics.otplbs.bean.CapacityControlMipDetailDto;
import com.midea.logistics.otplbs.bean.ReqCreateDraftExtDto;
import com.mideaframework.core.web.JsonResponse;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: dumg
 * @Date: 2024-04-16-9:31
 * Description:
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "CapacityControlCommonFeign"/*,url = "http://localhost:12007"*/)
public interface CapacityControlCommonFeign {
    @PostMapping("/capacity/control/searchForPage")
    JsonResponse capacityControlPage(@RequestBody CapacityControlDto entity);

    @PostMapping("/capacity/control/addOrUpdate")
    JsonResponse capacityControlAddOrUpdate(@RequestBody List<CapacityControlDto> entitys);

    @PostMapping("/capacity/control/enableOrUnable")
    JsonResponse<Integer> capacityControlEnableOrUnable(@RequestBody CapacityControlDto entity);

    @PostMapping("/capacity/control/createIflowDraft")
    JsonResponse<RespCreateDraftDto> createIflowDraft(@RequestBody ReqCreateDraftExtDto dto);
    @GetMapping("/capacity/control/mipDetail")
    JsonResponse<CapacityControlMipDetailDto> capacityControlMipDetail(@RequestParam("mipApplyNo") String mipApplyNo);

    @PostMapping("/capacity/tempSave")
    JsonResponse tempSave(@RequestBody CapacityControlDto dto);
}
