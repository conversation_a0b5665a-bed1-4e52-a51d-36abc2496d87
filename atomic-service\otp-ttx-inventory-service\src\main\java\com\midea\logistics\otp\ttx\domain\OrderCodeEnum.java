package com.midea.logistics.otp.ttx.domain;

/**
 * <AUTHOR>
 * @date 2019-8-26 16:07
 */
public enum OrderCodeEnum  {
    UPLOAD("0", "已上传"),
    SAVING("1", "保存中"),
    SAVED("2", "已保存"),
    ERROR("3", "保存异常"),
    UPING("4", "下发中"),
    UPED("5", "已下发"),
    UPERROR("6", "下发异常");

    private String code;
    private String msg;

    OrderCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public String getCode() {
        return code;
    }
}
