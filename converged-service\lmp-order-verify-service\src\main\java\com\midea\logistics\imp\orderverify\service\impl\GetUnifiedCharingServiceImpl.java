package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.CdCommonMaterialManager;
import com.midea.logistics.domain.mdm.domain.CdCommonMaterial;
import com.midea.logistics.domain.mdm.domain.MdmDataDictionaryDetail;
import com.midea.logistics.imp.orderverify.service.GetUnifiedCharingService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.order.BopServiceFeign;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.request.QueryCenterRequest;
import com.midea.logistics.otp.order.domain.response.QueryCenterResponse;
import com.midea.logistics.otp.rule.domain.bean.TcRule;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;


/**
 * 美云销统配计费
 */
@Slf4j
@Service
public class GetUnifiedCharingServiceImpl implements GetUnifiedCharingService {
    @Autowired
    private BopServiceFeign bopServiceFeign;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    CdCommonMaterialManager cdCommonMaterialManager;

    @ZeebeFlow("UNIFIED_CHARING")
    @Override
    public CustomerOrderInfoExt doWork(CustomerOrderInfoExt dto) {
        log.info("无需解析统配计费类型,订单号：{},来源系统:{},营销中心编码：{},收货客户编码：{}, 事业部主体：{}",dto.getOrderNo(),dto.getSourceSystem(),dto.getSalesCenterCode(), dto.getUpperReceiverCode(), dto.getEntityId());
        //gsc:在收货单位解析之后增加以下逻辑判断,覆盖掉上游给的 printBoxLabel 的值
        this.handlePrintBoxLabel(dto);

        //SCC的销售出库单：默认为中心计费
        if (SourceSystem.isSCC(dto.getSourceSystem()) && OrderType.isPOOrder(dto.getOrderType())) {
            FlowListenerParam.success("SCC销售出库单,默认为中心计费" );
            dto.setTcFlag(TCFlag.C.getKey());
            return dto;
        }

        //非OFC、缺少必要参数 不解析
        if (!SourceSystem.OFC.getKey().equals(dto.getSourceSystem())) {
            FlowListenerParam.success("无需解析统配计费类型" );
            return dto;
        }

        Integer entityId = dto.getEntityId();
        //2021-12-28 纯运输需要查询数据字典CUSTOMER_CODE_ENTITY_RELATION 获取对应的事业部id进行判断
        if (OrderType.isYSOrder(dto.getOrderType())) {
            String vaule = dictHelper.getDictVaule(TCFlag.CUSTOMER_CODE_ENTITY_RELATION, dto.getCustomerCode());
            entityId = StringUtils.isBlank(vaule) ? null : Integer.parseInt(vaule);
        }
        //缺少必要参数 不解析
        if (null == entityId) {
            FlowListenerParam.success("无需解析统配计费类型" );
            return dto;
        }
        //非B2B 出库 不解析
        if (!BusinessMode.isB2B(dto.getBusinessMode())) {
            FlowListenerParam.success("无需解析统配计费类型" );
            return dto;
        }
        if (OrderType.isRIOrder(dto.getOrderType())) {
            if (DeliveryType.isZT(dto.getDeliveryType())) {
                FlowListenerParam.success("无需解析统配计费类型" );
                return dto;
            }
            //上撤样的订单/商超的订单为中心计费
            if (CommonEnum.Y.getKey().equals(dto.getSpecimenType()) || CommonEnum.N.getKey().equals(dto.getSpecimenType()) || CommonConstant.FLAG_YES.equals(dto.getScPosFlag())) {
                FlowListenerParam.success("上撤样或商超订单,默认为中心计费" );
                dto.setTcFlag(TCFlag.C.getKey());
                return dto;
            } else {
                //glh:非上撤样或商超，履约中心（soucrceSystem=OFC）+退货入库（orderType=RI）+非自提（delivery_type=’‘）事业部是冰洗和洗衣机（entity_id=12，13），读取统配配置
                if (SourceSystem.isOFC(dto.getSourceSystem()) && !DeliveryType.isZT(dto.getDeliveryType()) && Lists.newArrayList(12,13).contains(entityId)) {
                    String tcFlag= getRiTcFlag(dto, entityId);
                    if (StringUtils.isNotBlank(tcFlag)) {
                        FlowListenerParam.success(TCFlag.getValue(tcFlag));
                        dto.setTcFlag(tcFlag);
                        return dto;
                    }
                }

                FlowListenerParam.success("退货入库非上撤样或商超订单,默认为统配计费" );
                dto.setTcFlag(TCFlag.T.getKey());
                return dto;
            }
        }
        //非出库、分拨、纯运输 不解析
        if (!InOutType.OUT.getName().equals(OrderType.getBigType(dto.getOrderType())) && !OrderType.DO.getKey().equals(dto.getOrderType()) && !OrderType.YS.getKey().equals(dto.getOrderType())) {
            FlowListenerParam.success("无需解析统配计费类型" );
            return dto;
        }

        //glh:履约中心（soucrceSystem=OFC）+调拨出库（orderType=AO）+非自提 + 事业部是冰洗和洗衣机（entity_id=12，13）+目标财务仓名称含“样机”，默认为中心计费（tc_flag=C）,日志提示“样机默认中心计费”
        if (SourceSystem.isOFC(dto.getSourceSystem()) && OrderType.isAOOrder(dto.getOrderType()) && !DeliveryType.isZT(dto.getDeliveryType()) && Lists.newArrayList(12,13).contains(entityId)
            && StringUtils.isNotBlank(dto.getUpperTargeWhName()) && dto.getUpperTargeWhName().contains("样机")
        ) {
            FlowListenerParam.success("样机默认中心计费" );
            dto.setTcFlag(TCFlag.C.getKey());
            return dto;
        }

        //收货单位为空的：默认统配计费
        if (StringUtils.isBlank(dto.getUpperReceiverCode())) {
            FlowListenerParam.success("收货单位为空,默认中心计费" );
            dto.setTcFlag(TCFlag.C.getKey());
            return dto;
        }

        //上撤样的订单/商超的订单为中心计费
        if (CommonEnum.Y.getKey().equals(dto.getSpecimenType()) || CommonEnum.N.getKey().equals(dto.getSpecimenType()) || CommonConstant.FLAG_YES.equals(dto.getScPosFlag())) {
            FlowListenerParam.success("上撤样或商超订单,默认为中心计费" );
            dto.setTcFlag(TCFlag.C.getKey());
            return dto;
        }

        //收货单位编码非C编码开头的：且不是上撤样的订单/商超的 为统配计费
        if (!dto.getUpperReceiverCode().startsWith("C")) {
            FlowListenerParam.success("收货单位编码非C编码开头,且不是上撤样或商超订单,默认统配计费");
            dto.setTcFlag(TCFlag.T.getKey());
            return dto;
        }

//        String tcFlag = "";
//        switch (entityId) {
//            //洗衣机事业部
//            case 13:
//            case 23:
//                tcFlag = setWashingMachineTcFlag(dto);
//                FlowListenerParam.success(TCFlag.getValue(tcFlag));
//                break;
//            //家用空调事业部
//            case 10:
//            case 28:
//                tcFlag = setHouseholdAirConditionerTcFlag(dto);
//                FlowListenerParam.success(TCFlag.getValue(tcFlag));
//                break;
//            //冰箱事业部
//            case 12:
//            case 29:
//                tcFlag = setRefrigeratorTcFlag(dto);
//                FlowListenerParam.success(TCFlag.getValue(tcFlag));
//                break;
//            default:
//                FlowListenerParam.success("无需解析统配计费类型" );
//        }

        String tcFlag = this.getTcFlag(dto,entityId);

        dto.setTcFlag(tcFlag);
        return dto;

    }

    /**
     * 1.	洗衣机事业部（entityId 13 或者23）B2B的出库订单计费：
     * 通过营销中心编码salesCenterCode+收货客户编码upperReceiverCode 去 商户中心拿到客户营销小类，
     * 客户营销小类是（零售客户 Retail） 则标记统配计费 T，其他为中心计费 C； 打标后将标识传TMS和WMS；如果商户中心接口调不通或者过程异常则 标记为统配计费，不卡单
     */
    private String setWashingMachineTcFlag(CustomerOrderInfoExt dto) {
        CustomerOrderInfo updateDto = new CustomerOrderInfo();
        updateDto.setId(dto.getId());

        if (StringUtils.isBlank(dto.getSalesCenterCode()) || StringUtils.isBlank(dto.getUpperReceiverCode())) {
            updateDto.setTcFlag(TCFlag.T.getKey());
            return updateDto.getTcFlag();
        }

        QueryCenterRequest queryCenterRequest = new QueryCenterRequest();
        queryCenterRequest.setCenter(dto.getSalesCenterCode());
        queryCenterRequest.setNextCusCode(dto.getUpperReceiverCode());
        queryCenterRequest.setCustomerOrderNo(dto.getCustomerOrderNo());
        queryCenterRequest.setOrderNo(dto.getOrderNo());
        JsonResponse<List<QueryCenterResponse>> jsonResponse = bopServiceFeign.queryContractListByOneLevel(queryCenterRequest);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            updateDto.setTcFlag(TCFlag.T.getKey());
            return updateDto.getTcFlag();
        }

        if (!CollectionUtils.isEmpty(jsonResponse.getData()) && jsonResponse.getData().stream().anyMatch(a -> StringUtils.isNotBlank(a.getCooperationModeMin()) && TCFlag.RETAIL.equals(a.getCooperationModeMin()))) {
            //客户营销小类是（零售客户 Retail) 则标记统配计费 T ，其他为中心计费 C ?
            updateDto.setTcFlag(TCFlag.T.getKey());
            return updateDto.getTcFlag();
        }

        updateDto.setTcFlag(TCFlag.C.getKey());

        return updateDto.getTcFlag();
    }

    /**
     * 2.	家用空调事业部（entityId 10 或者28）的B2B的出库订单计费：配置数据字典：KT_TP_BMS_RULE 家用空调统配计费关系；
     * 字典编码为营销中心编码+字典名称为 收货客户编码（集团C编码），订单去数据字典匹配，匹配到的就为中心计费，其他为统配计费；
     */
    private String setHouseholdAirConditionerTcFlag(CustomerOrderInfoExt dto) {
        CustomerOrderInfo updateDto = new CustomerOrderInfo();
        updateDto.setId(dto.getId());

        if (StringUtils.isBlank(dto.getSalesCenterCode()) || StringUtils.isBlank(dto.getUpperReceiverCode())) {
            updateDto.setTcFlag(TCFlag.T.getKey());
            return updateDto.getTcFlag();
        }

        LinkedMultiValueMap map = dictHelper.getMdmDataDictionaryMultiValue(TCFlag.KT_TP_BMS_RULE);
        if (!MapUtils.isEmpty(map) && map.containsKey(dto.getSalesCenterCode())) {
            List list = map.get(dto.getSalesCenterCode());
            if (!CollectionUtils.isEmpty(list) && list.contains(dto.getUpperReceiverCode())) {
                updateDto.setTcFlag(TCFlag.C.getKey());
                return updateDto.getTcFlag();
            }
        }

        updateDto.setTcFlag(TCFlag.T.getKey());

        return updateDto.getTcFlag();
    }

    /**
     * 3.	冰箱事业部（entityId 12 或者29 冰箱销售公司）B2B的出库订单计费：
     * 通过营销中心编码salesCenterCode+收货客户编码upperReceiverCode 去 商户中心拿到客户营销小类，客户营销小类是（零售客户 Retail或者旗舰店 FlagshipStore）
     * 则标记统配计费 T，其他为中心计费 C； 打标后将标识传TMS和WMS；如果商户中心接口调不通或者过程异常则 标记为统配计费，不卡单；
     */
    private String setRefrigeratorTcFlag(CustomerOrderInfoExt dto) {
        CustomerOrderInfo updateDto = new CustomerOrderInfo();
        updateDto.setId(dto.getId());

        if (StringUtils.isBlank(dto.getSalesCenterCode()) || StringUtils.isBlank(dto.getUpperReceiverCode())) {
            updateDto.setTcFlag(TCFlag.T.getKey());
            return updateDto.getTcFlag();
        }

        QueryCenterRequest queryCenterRequest = new QueryCenterRequest();
        queryCenterRequest.setCenter(dto.getSalesCenterCode());
        queryCenterRequest.setNextCusCode(dto.getUpperReceiverCode());
        queryCenterRequest.setCustomerOrderNo(dto.getCustomerOrderNo());
        queryCenterRequest.setOrderNo(dto.getOrderNo());
        JsonResponse<List<QueryCenterResponse>> jsonResponse = bopServiceFeign.queryContractListByOneLevel(queryCenterRequest);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            updateDto.setTcFlag(TCFlag.T.getKey());
            return updateDto.getTcFlag();
        }

        if (!CollectionUtils.isEmpty(jsonResponse.getData()) && jsonResponse.getData().stream().anyMatch(a -> StringUtils.isNotBlank(a.getCooperationModeMin()) && (TCFlag.RETAIL.equals(a.getCooperationModeMin()) || TCFlag.FLAG_SHIP_STORE.equals(a.getCooperationModeMin())))) {
            updateDto.setTcFlag(TCFlag.T.getKey());
            return updateDto.getTcFlag();
        }

        updateDto.setTcFlag(TCFlag.C.getKey());

        return updateDto.getTcFlag();
    }

    /**
     *   家用空调：按照 事业部编码+收货客户编码+营销中心编码 进行匹配
     *   冰箱、洗衣机: 事业部编码+收货客户编码 进行匹配
     *  如果系统匹配存在多条（重复性数据）则取其中一条的
     * @param dto
     * @param entityId
     */
    private String getTcFlag(CustomerOrderInfoExt dto, Integer entityId) {
        String tcFlag = "";
        String buCode = null;
        //先根据entityId 查数据字典匹配属于哪个事业部
        List<MdmDataDictionaryDetail> dicts = dictHelper.getDict(BuInfoType.DICT_CODE.getKey());
        if (CollectionUtils.isEmpty(dicts)) {
            FlowListenerParam.success("数据字典为空,无需解析统配计费类型" );
            return tcFlag;
        }
        for (MdmDataDictionaryDetail dc : dicts) {
            if (CommonConstant.FLAG_YES.equals(dc.getEnableFlag()) && StringUtils.isNotBlank(dc.getCode()) && ArrayUtils.contains(dc.getCode().split(","), String.valueOf(entityId)) && dc.getSortNo() != null) {
                buCode = dc.getSortNo().toString();
                break;
            }
        }
        if (StringUtils.isBlank(buCode)) {
            FlowListenerParam.success("未配置的事业部,无需解析统配计费类型" );
            return tcFlag;
        }

        //下面开始默认统配
        tcFlag = TCFlag.T.getKey();
        //配置了的事业部,营销中心编码为空时走默认
        if (StringUtils.isBlank(dto.getSalesCenterCode())) {
            FlowListenerParam.success("营销中心编码为空,默认统配计费" );
            return tcFlag;
        }
        TcRule tcRule = new TcRule();
        tcRule.setBuCode(buCode);
        tcRule.setUpperReceiverCode(dto.getUpperReceiverCode());
        tcRule.setSalesCenterCode(dto.getSalesCenterCode());

        log.info("匹配统配计费规则orderNo:{},tcRule:{}",dto.getOrderNo(), JSON.toJSONString(tcRule));
        List<TcRule> tcRules = businessParamHelper.getTcRulesNull(tcRule);
        if (!CollectionUtils.isEmpty(tcRules)) {
            tcRule = tcRules.get(0);
            tcFlag = tcRule.getTcFlag();
            log.info("订单[{}]匹配统配计费规则:id{},bucode:{},upperReceiverCode{},salesCenterCode{}，tcFlag{}",dto.getCustomerOrderNo(),tcRule.getId(),buCode,tcRule.getUpperReceiverCode(),tcRule.getSalesCenterCode(),tcFlag);
        }
        FlowListenerParam.success(TCFlag.getValue(tcFlag));
        return tcFlag;
    }

    /**
     * gsc:在收货单位解析之后增加以下逻辑判断,覆盖掉上游给的 printBoxLabel 的值
     */
    private void handlePrintBoxLabel(CustomerOrderInfoExt dto) {
        CustomerOrderInfoExtend customerOrderInfoExtend = dto.getCustomerOrderInfoExtend();
        List<CustomerOrderItem> customerOrderItems = dto.getCustomerOrderItems();
        Integer printBoxLabel = null;
        boolean updateFlag = false;

        if (customerOrderInfoExtend == null) {
            customerOrderInfoExtend = businessParamHelper.getCustomerOrderInfoExtend(dto.getOrderNo());
        }

        if (customerOrderInfoExtend == null) {
            return;
        }
        //来源是美云销OFC 且 美云销给明确的打标标识 printBoxLabel =1
        if (SourceSystem.isOFC(dto.getSourceSystem()) && CommonEnum.Y.getValue().equals(customerOrderInfoExtend.getPrintBoxLabel())) {
            //业务类型B2B、订单类型（销售出库、调拨出库）& 物流判断是否是收货仓是安得（订单是有收货平台 target_site_code ，收货仓非安得的才贴; target_site_code =null & 商品状态是正品Y的才贴(商品状态都一样，取其中一个)
            if (BusinessMode.isB2B(dto.getBusinessMode())
                && (OrderType.isPOOrder(dto.getOrderType()) || OrderType.isAOOrder(dto.getOrderType()))
                && dto.getTargetSiteCode() == null
                && !CollectionUtils.isEmpty(customerOrderItems) &&  ItemStatus.Y.getKey().equals(customerOrderItems.get(0).getItemStatus())
            ) {
                printBoxLabel = CommonEnum.Y.getValue();
            }
        }
        //来源是美的零售MRP 且 商品所属事业部是家用空调（商品明细中的bu_code =01 家用空调事业部）
        if (SourceSystem.isMRP(dto.getSourceSystem()) && !CollectionUtils.isEmpty(customerOrderItems)) {
            HashMap<String, String> dictToMap = dictHelper.getDictToMap(CommonConstant.BU_CODE_PRINT_BOX);
            if (dictToMap != null && dictToMap.size() > 0) {
                if (customerOrderItems.stream().anyMatch(i -> dictToMap.containsKey(i.getBuCode()))) {
                    //业务类型B2B、订单类型（销售出库、调拨出库）& 物流判断是否是收货仓是安得（订单是有收货平台 target_site_code ，收货仓非安得的才贴; target_site_code =null & 商品状态是正品Y的才贴(商品状态都一样，取其中一个)
                    if (BusinessMode.isB2B(dto.getBusinessMode())
                        && (OrderType.isPOOrder(dto.getOrderType()) || OrderType.isAOOrder(dto.getOrderType()))
                        && dto.getTargetSiteCode() == null
                        &&  ItemStatus.Y.getKey().equals(customerOrderItems.get(0).getItemStatus())
                    ) {
                        printBoxLabel = CommonEnum.Y.getValue();
                    }
                }
            }
        }
        //如果MDM返回的所有商品的是否扫码（userdefined15 OFC）字段为Y ,订单拓展表的打印箱贴是print_box_label=1时，把print_box_label更新为0
        if(CommonEnum.Y.getValue().equals(printBoxLabel) && !CollectionUtils.isEmpty(customerOrderItems)){
            boolean udpatePrintBoxLabel = true;
            for(CustomerOrderItem customerOrderItem : customerOrderItems){
               CdCommonMaterial cdCommonMaterial =  cdCommonMaterialManager.getCdCommonMaterialCache(customerOrderItem.getItemCode());
               if(cdCommonMaterial!=null && !CommonEnum.Y.getKey().equals(cdCommonMaterial.getUserdefined15())){
                   udpatePrintBoxLabel = false;
                  break;
               }
           }
            //都是N更新为0
            if(udpatePrintBoxLabel){
                printBoxLabel = CommonEnum.N.getValue();
            }
        }
        log.info("订单：{}，getPrintBoxLabel：{}",customerOrderInfoExtend.getOrderNo(),customerOrderInfoExtend.getPrintBoxLabel());
        //与原来不一致才更新 排除掉2的
        if (!Objects.equals(printBoxLabel, customerOrderInfoExtend.getPrintBoxLabel()) && !Objects.equals(2, customerOrderInfoExtend.getPrintBoxLabel())) {
            if (!CommonEnum.Y.getValue().equals(printBoxLabel)) {
                printBoxLabel = CommonEnum.N.getValue();
            }
            updateFlag = true;
        }

        if (updateFlag) {
            CustomerOrderInfoExtend updateExtend = new CustomerOrderInfoExtend(customerOrderInfoExtend.getId());
            updateExtend.setPrintBoxLabel(printBoxLabel);
            businessParamHelper.updateCustomerOrderInfoExtend(updateExtend);
        }
        customerOrderInfoExtend.setPrintBoxLabel(printBoxLabel);
    }

    /**
     * 根据统配规则获取退货入库单统配标识
     * @param dto
     * @return
     */
    private String getRiTcFlag(CustomerOrderInfoExt dto , Integer entityId) {
        String tcFlag = "";
        String buCode = null;
        //先根据entityId 查数据字典匹配属于哪个事业部
        if (null == entityId) {
            return tcFlag;
        }
        List<MdmDataDictionaryDetail> dicts = dictHelper.getDict(BuInfoType.DICT_CODE.getKey());
        if (CollectionUtils.isEmpty(dicts)) {
            return tcFlag;
        }
        for (MdmDataDictionaryDetail dc : dicts) {
            if (CommonConstant.FLAG_YES.equals(dc.getEnableFlag()) && StringUtils.isNotBlank(dc.getCode()) && ArrayUtils.contains(dc.getCode().split(","), String.valueOf(entityId)) && dc.getSortNo() != null) {
                buCode = dc.getSortNo().toString();
                break;
            }
        }

        if (StringUtils.isBlank(buCode)) {
            return tcFlag;
        }

        if (StringUtils.isBlank(dto.getUpperReceiverCode())) {
            return tcFlag;
        }

        if (!dto.getUpperReceiverCode().startsWith("C")) {
            return tcFlag;
        }

        if (StringUtils.isBlank(dto.getSalesCenterCode())) {
            return tcFlag;
        }
        TcRule tcRule = new TcRule();
        tcRule.setBuCode(buCode);
        tcRule.setUpperReceiverCode(dto.getUpperReceiverCode());
        tcRule.setSalesCenterCode(dto.getSalesCenterCode());

        log.info("匹配统配计费规则orderNo:{},tcRule:{}",dto.getOrderNo(), JSON.toJSONString(tcRule));
        List<TcRule> tcRules = businessParamHelper.getTcRulesNull(tcRule);
        if (!CollectionUtils.isEmpty(tcRules)) {
            tcRule = tcRules.get(0);
            tcFlag = tcRule.getTcFlag();
            log.info("订单[{}]匹配统配计费规则:id{},bucode:{},upperReceiverCode{},salesCenterCode{}，tcFlag{}",dto.getCustomerOrderNo(),tcRule.getId(),buCode,tcRule.getUpperReceiverCode(),tcRule.getSalesCenterCode(),tcFlag);
        }
        return tcFlag;
    }
}
