package com.midea.logistics.otp.task.service.impl;
import com.midea.logistics.otp.task.mapper.common.TaskExtendMapper;
import com.midea.logistics.otp.task.service.ITaskExtendService;
import com.mideaframework.core.web.PageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.midea.logistics.otp.task.domain.bean.TaskExtend;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.List;

/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: TaskExtendServiceImpl
* Author: 刘院民
* Date: 2023-3-21 11:34:11
* Description:TaskExtendServiceImpl类
*/
@Service
public class TaskExtendServiceImpl implements ITaskExtendService {

    private static final Logger logger = LoggerFactory.getLogger( TaskExtendServiceImpl.class );

    @Autowired
    private TaskExtendMapper taskExtendMapper;

    @Override
    @Transactional(readOnly = false)
    public Integer saveTaskExtend(TaskExtend taskExtend) {

        if( null == taskExtend) {
            logger.warn("save taskExtend, but taskExtend is null...");
            return 0;
            }

        return taskExtendMapper.save(taskExtend);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteTaskExtend(TaskExtend taskExtend) {

        if( null == taskExtend || null == taskExtend.getId()) {
            logger.warn("delete taskExtend, but taskExtend is null  or taskExtend id is null...");
            return 0;
        }

        return taskExtendMapper.deleteById(taskExtend);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateTaskExtend(TaskExtend taskExtend) {

        if( null == taskExtend || null == taskExtend.getId()) {
            logger.warn("update taskExtend, but taskExtend is null  or taskExtend id is null...");
            return 0;
        }

        return taskExtendMapper.updateById(taskExtend);
    }

    @Override
    public TaskExtend selectOneTaskExtend(TaskExtend taskExtend) {
        if( taskExtend == null) {
            logger.warn("select taskExtend one, but taskExtend is null ...");
            return null;
        }
        taskExtend = taskExtendMapper.selectOne( taskExtend );
        return taskExtend;
    }

    @Override
    public PageResponse<TaskExtend> selectTaskExtendPage(TaskExtend taskExtend) {
        PageResponse<TaskExtend> pagingData = new PageResponse<>();

        if( null == taskExtend ) {
            logger.warn("select taskExtend page, but taskExtend is null...");
            return pagingData;
        }

        Integer queryCount = taskExtendMapper.selectByIndexCount( taskExtend );
        if( null != queryCount && queryCount <= 0 ) {
            logger.info("select taskExtend page , but count {} == 0 ...",queryCount);
            return pagingData;
        }

        List<TaskExtend> taskExtends =  selectTaskExtendByIndex( taskExtend );
        pagingData.init(taskExtend.getPageNo(),taskExtend.getPageSize(),queryCount,taskExtends);
        return pagingData;
    }

    @Override
    public List<TaskExtend> selectTaskExtendByIndex(TaskExtend taskExtend) {
        List<TaskExtend> taskExtends = new ArrayList<>();
        if( taskExtend == null) {
            logger.warn("select taskExtend by index, but taskExtend is null ...");
            return taskExtends;
        }

        taskExtends = taskExtendMapper.selectByIndex( taskExtend );

        return taskExtends;
    }

    @Override
    public int countTaskExtendByIndex(TaskExtend taskExtend) {
        int count = 0;
        if( taskExtend == null) {
            logger.warn("count taskExtend by index, but taskExtend is null ...");
            return count;
        }

        count = taskExtendMapper.selectByIndexCount( taskExtend );

        return count;
    }

    @Override
    public Integer insertBatch(List<TaskExtend> taskExtends) {
        if (CollectionUtils.isEmpty(taskExtends)) {
            logger.warn("insertBatch taskExtends, but taskExtends is null ...");
            return 0;
        }
        return taskExtendMapper.insertBatch(taskExtends);
    }

    @Override
    public Integer batchUpdate(List<TaskExtend> taskExtends) {
        if (CollectionUtils.isEmpty(taskExtends)) {
            logger.warn("batchUpdate taskExtends, but taskExtends is null ...");
            return 0;
        }
        return taskExtendMapper.batchUpdate(taskExtends);
    }

    @Override
    public Integer deleteBatch(List<TaskExtend> taskExtends) {
        if (CollectionUtils.isEmpty(taskExtends)) {
            logger.warn("deleteBatch taskExtends, but taskExtends is null ...");
            return 0;
        }
        return taskExtendMapper.deleteBatch(taskExtends);
    }

    @Override
    public List<TaskExtend> listByTaskNos(List<String> taskNos) {
        if (CollectionUtils.isEmpty(taskNos)) {
            logger.warn("listByTaskNos, but taskNos is null ...");
            return null;
        }
        return taskExtendMapper.queryByTaskNos(taskNos);
    }

    @Override
    public Integer updateByIdCanSetEmpty(TaskExtend taskExtend) {
        if (taskExtend == null) {
            logger.warn("batchUpdate taskExtends, but taskExtends is null ...");
            return 0;
        }
        return taskExtendMapper.updateByIdCanSetEmpty(taskExtend);
    }

    @Override
    public Integer updateBatchCanSetEmpty(List<TaskExtend> list) {
        if (CollectionUtils.isEmpty(list)) {
            logger.warn("batchUpdate taskExtends, but taskExtends is null ...");
            return 0;
        }
        return taskExtendMapper.updateBatchCanSetEmpty(list);
    }

    @Override
    public Integer deleteByTaskNos(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            logger.warn("deleteByTaskNos taskExtends, but taskExtends is null ...");
            return 0;
        }
        return taskExtendMapper.deleteByTaskNos(list);
    }
}