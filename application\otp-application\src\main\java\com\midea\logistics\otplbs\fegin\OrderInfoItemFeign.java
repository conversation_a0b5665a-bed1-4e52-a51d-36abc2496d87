package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: OrderInfoItemFeign
 * Author: lindq2
 * Date: 2019-5-23 9:52:11
 * Description:子订单明细表 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface OrderInfoItemFeign {

    @RequestMapping(value = "/orderInfoItem", method = RequestMethod.POST)
    JsonResponse<Integer> create(@RequestBody OrderInfoItem orderInfoItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderInfoItem/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderInfoItem/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderInfoItem orderInfoItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderInfoItem/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/orderInfoItems", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap OrderInfoItem orderInfoItem);
}
