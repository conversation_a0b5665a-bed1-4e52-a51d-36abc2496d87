<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.ElectronicSignatureConfigMapper">

    <sql id="searchFieldsSql1">
            `business_mode` AS  businessMode,
            `site_code` AS  siteCode,
            `create_user_code` AS  createUserCode,
            `customer_code` AS  customerCode,
            `site_name` AS  siteName,
            `update_time` AS  updateTime,
            `remark` AS  remark,
            `version` AS  version,
            `customer_name` AS  customerName,
            `upper_receiver_name` AS  upperReceiverName,
            `delete_flag` AS  deleteFlag,
            `upper_receiver_code` AS  upperReceiverCode,
            `update_user_code` AS  updateUserCode,
            `wh_code` AS  whCode,
            `create_time` AS  createTime,
            `id` AS  id,
            `enable_flag` AS  enableFlag
    </sql>

    <sql id="whereFieldsSql1">
        where delete_flag=0
                    <if test="businessMode !=null">
                        and `business_mode` =#{businessMode}
                    </if>
                    <if test="siteCode !=null ">
                        and `site_code` =#{siteCode}
                    </if>
                    <if test="customerCode !=null ">
                        and `customer_code` =#{customerCode}
                    </if>
                    <if test="siteName !=null ">
                        and `site_name` =#{siteName}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="remark !=null">
                        and `remark` =#{remark}
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        and `customer_name` =#{customerName}
                    </if>
                    <if test="upperReceiverName !=null and upperReceiverName != ''">
                        and `upper_receiver_name` =#{upperReceiverName}
                    </if>
                    <if test="upperReceiverCode !=null ">
                        and `upper_receiver_code` =#{upperReceiverCode}
                    </if>
                    <if test="whCode !=null ">
                        and `wh_code` =#{whCode}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="enableFlag !=null">
                        and `enable_flag` =#{enableFlag}
                    </if>
    </sql>


    <select id="selectByIndexByCheck" resultType="com.midea.logistics.otp.rule.domain.bean.custom.ElectronicSignatureConfig">
        select
        <include refid="searchFieldsSql"/>
        from electronic_signature_config t
        <include refid="whereFieldsSql1"/>
    </select>

    <update id="updateByIdCanSetEmptyNoVersion">
        update
        electronic_signature_config t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
        `id` = #{id}
    </update>

</mapper>