package com.midea.logistics.imp.orderverify.mq.consumer;

import com.midea.logistics.imp.orderverify.service.ZeebeLmpService;
import com.midea.logistics.otp.constants.MessageQueueDefine;
import com.mideaframework.transactionservice.mq.MqConsumer;
import com.mideaframework.transactionservice.mq.consumer.MQMessageHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 触发zeebe子订单流程
 * @date 2020-10-28
 */
@Component
@MqConsumer(topic = MessageQueueDefine.ZEEBE_ORDER_TOPIC, tag = MessageQueueDefine.ZEEBE_ORDER_TAG)
public class ZeebeOrderConsumer implements MQMessageHandler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ZeebeLmpService zeebeLmpService;

    @Override
    public void handlerMessage(MessageExt messageExt) {

        MDC.put("traceId", UUID.randomUUID().toString());
        String orderNo = new String(messageExt.getBody());
        logger.info(" mq消息 "+ MessageQueueDefine.ZEEBE_ORDER_TOPIC +"   消费内容 message =  {}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            logger.warn("子订单号为空：{}", messageExt.getMsgId());
            return;
        }

        zeebeLmpService.trigger(orderNo, false);

    }

}


