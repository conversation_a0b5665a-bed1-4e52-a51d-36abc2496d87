package com.midea.logistics.otplbs.log;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.otp.log.dto.ApplicationLogDto;
import com.midea.logistics.otplbs.mq.producer.ApplictionLogProducer;
import com.mideaframework.core.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年08月05日 上午14:47
 */
//@Aspect
@Component
@Slf4j
public class SystemLogAspect {

    @Autowired
    ApplictionLogProducer applictionLogProducer;


    @Pointcut("execution(* com.midea.logistics.otplbs.rest..*(..))")
    public void allMethod() {

    }

    /**
     * @Description 异常通知 用于拦截service层记录异常日志
     * @date 2018年9月3日 下午5:43
     */
    @AfterThrowing(pointcut = "allMethod()", throwing = "ex")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable ex) {
        ApplicationLogDto applicationLogDto;
        try {
         applicationLogDto = getApplicationLogDto(joinPoint,ex);
        }catch (Exception  e){
            log.info( e.getMessage());
            return;
        }

        applictionLogProducer.sent(applicationLogDto);
    }


    @AfterReturning(value = "allMethod()", returning = "returnValue")
    public void afterRunningLog(JoinPoint point, Object returnValue) {
        ApplicationLogDto applicationLogDto  = null;
        try {
             applicationLogDto = getApplicationLogDto(point, returnValue);
            if (null == applicationLogDto){
                return;
            }
        }catch (Exception  ex){
            log.info( ex.getMessage());
            return;
        }

        applictionLogProducer.sent(applicationLogDto);
    }

    private ApplicationLogDto getApplicationLogDto(JoinPoint joinPoint, Object returnValue) {

        String userCode = "";
        //通过MDC 获取 userCode
        String traceId = MDC.get("traceId");
        if (!StringUtils.isEmpty(traceId)) {
            userCode = traceId.substring(traceId.indexOf("@") + 1, traceId.length());
        }

        RequestAttributes req = RequestContextHolder.getRequestAttributes();
        if (null == req) {
            return null;
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = request.getSession();
        //获取请求ip
        String ip = IpUtils.INSTANCE.getIpAddress(request);

        ApplicationLogDto applicationLogDto = new ApplicationLogDto();
        applicationLogDto.setCreateUserCode(userCode);
        //获取用户请求方法的参数并序列化为JSON格式字符串

        Object[] args = joinPoint.getArgs();
        //序列化时过滤掉request和response
        List<Object> logArgs = streamOf(args)
            .filter(arg -> (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse)))
            .collect(Collectors.toList());

        String params = JSON.toJSONString(logArgs);

        StringBuffer url = request.getRequestURL();

        applicationLogDto.setReqParams(params);

        applicationLogDto.setRequestUrl(url.toString());
        applicationLogDto.setResult(JSON.toJSONString(returnValue));
        applicationLogDto.setClientIp(ip);
        applicationLogDto.setServiceName("otp-appliction");
        return applicationLogDto;
    }

    public static <T> Stream<T> streamOf(T[] array) {
        return ArrayUtils.isEmpty(array) ? Stream.empty() : Arrays.asList(array).stream();
    }

    /**
     * @Description 获取注解中对方法的描述信息 用于service层注解
     * @date 2018年9月3日 下午5:05
     */
    public static String getServiceMethodDescription(JoinPoint joinPoint) throws Exception {
        String targetName = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
        Object[] arguments = joinPoint.getArgs();
        Class targetClass = Class.forName(targetName);
        Method[] methods = targetClass.getMethods();
        String description = "";
        return description;
    }

}
