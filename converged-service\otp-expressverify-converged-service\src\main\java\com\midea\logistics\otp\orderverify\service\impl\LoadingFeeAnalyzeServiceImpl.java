package com.midea.logistics.otp.orderverify.service.impl;

import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.FeeType;
import com.midea.logistics.otp.order.common.fegin.LoadingFeeRuleRestFegin;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.orderverify.service.LoadingFeeAnalyzeService;
import com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

import static com.midea.logistics.otp.enums.JoinType.*;
import static com.midea.logistics.otp.enums.OrderType.*;

/**
 * @Program: logistics-otp
 * @ClassName: LoadingFeeAnalyzeServiceImpl
 * @Author： JiaJun
 * @Date： 2019-06-10 18:01
 * @Description:
 */
@Service
@Slf4j
public class LoadingFeeAnalyzeServiceImpl implements LoadingFeeAnalyzeService {

    @Autowired
    private LoadingFeeRuleRestFegin loadingFeeRuleRestFegin;
    @Autowired
    private RedisHelper redisHelper;

    @Override
    public JsonResponse loadingFeeAnalyzeRedis(OrderInfo orderInfo) {
        JsonResponse<LoadingFeeRule> response = new JsonResponse<>();
        String customerCode = orderInfo.getCustomerCode();
        String orderType = orderInfo.getOrderType();
        String deliveryType = orderInfo.getDeliveryType();
        String siteCode = orderInfo.getSiteCode();
        String whCode = orderInfo.getWhCode();
        String joinType = orderInfo.getJoinType();

        String redisKey = "_AN3_" + customerCode + "_" + orderType + "_" + deliveryType + "_" + siteCode + "_" + whCode + "_" + joinType;
        String redisValue = redisHelper.getLoadingFeeRule(redisKey);
        if(StringUtils.isNotBlank(redisValue)) {
            orderInfo.setOrderRpFlag(redisValue);
        } else {
            response = loadingFeeAnalyze(orderInfo);
            redisHelper.setLoadingFeeRule(redisKey, orderInfo.getOrderRpFlag());

            log.info("AN3_loadingFeeRule redisValue:{}", redisValue);
            log.info("AN3_loadingFeeRule orderInfo:{}", orderInfo.getOrderRpFlag());
        }

        return response;
    }


    public JsonResponse<LoadingFeeRule> loadingFeeAnalyze(OrderInfo orderInfo) {
        JsonResponse<LoadingFeeRule> response = new JsonResponse<>();

        String customerCode = orderInfo.getCustomerCode();
        String orderType = orderInfo.getOrderType();
        String deliveryType = orderInfo.getDeliveryType();
        String siteCode = orderInfo.getSiteCode();
        String whCode = orderInfo.getWhCode();
        String joinType = orderInfo.getJoinType();

        /**
         * （1）order_info的join_type字段为“SHARE”或“1300”，或“TRANS_INV”时 -> 不产生收入成本
         */
        if ( Arrays.asList(SHARE.getKey(),SHARE1300.getKey(),TRANS_INV.getKey()).contains(joinType)){
            orderInfo.setOrderRpFlag(FeeType.N.getKey());
            return response;
        }

        LoadingFeeRule loadingFeeRule = new LoadingFeeRule();
        loadingFeeRule.setCustomerCode(customerCode);
        loadingFeeRule.setOrderType(orderType);
        loadingFeeRule.setSiteCode(siteCode);
        loadingFeeRule.setWhCode(whCode);

        /**
         *  根据customerCode、orderType、deliveryType、siteCode、whCode、配送方式查询计费规则,精确查询
         */
        if(StringUtils.isNotEmpty(deliveryType)){
            loadingFeeRule.setDeliveryType(deliveryType);
            response = loadingFeeRuleRestFegin.getLoadingFeeRuleConfig(loadingFeeRule);
            if (BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && null != response.getData()) {
                loadingFeeRule = response.getData();
                orderInfo.setOrderRpFlag(loadingFeeRule.getFeeType());
                return response;
            }
        }

        //找不到配置，清空
        loadingFeeRule.setDeliveryType("");

        if (DeliveryType.isZT(deliveryType)) {
            loadingFeeRule.setPickFlag(CommonEnum.Y.getKey());
        } else {
            loadingFeeRule.setPickFlag(CommonEnum.N.getKey());
        }
        /**
         * （2）根据customerCode、orderType、deliveryType、siteCode、whCode、pickFlag去查询
         */
        response = loadingFeeRuleRestFegin.getLoadingFeeRuleConfig(loadingFeeRule);
        if (BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && null != response.getData()) {
            loadingFeeRule = response.getData();
            orderInfo.setOrderRpFlag(loadingFeeRule.getFeeType());
            return response;
        }

        /**
         *  如果查询不到去掉pickFlag,继续查询
         */
        loadingFeeRule.setPickFlag("");
        response = loadingFeeRuleRestFegin.getLoadingFeeRuleConfig(loadingFeeRule);
        if (BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && null != response.getData()) {
            loadingFeeRule = response.getData();
            orderInfo.setOrderRpFlag(loadingFeeRule.getFeeType());
            return response;
        }

        /**
         * (3) 自定义
         */
        // 1.状态调整单、状态调整出库单、状态调整入库单、数量调整出库单、数量调整入库单 -> 不产生收入成本
        if ( Arrays.asList(TF.getKey(),TFO.getKey(),TFI.getKey(),ADO.getKey(),ADI.getKey()).contains(orderType)){
            orderInfo.setOrderRpFlag(FeeType.N.getKey());
            return response;
        }

        /**
         * （4）其他，默认产生收入成本
         */
        orderInfo.setOrderRpFlag(FeeType.Y.getKey());

        return response;
    }
}
