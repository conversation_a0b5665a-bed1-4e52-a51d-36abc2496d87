package com.midea.logistics.otp.order.common.fegin.orderAgg;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.mideaframework.core.web.JsonResponse;

@FeignClient(value = "logistics-order-agg-service")
public interface OrderAggFeign {
    
    
    /**
     * @description: 审核信息转换(父单)
     * @param: [param]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2024/7/4 17:12
     */
    @PostMapping(value = "/verifyFailMsgConvertByCustOrder")
    public JsonResponse<String> verifyFailMsgConvertByCustOrder(@RequestBody CustomerOrderInfo customerOrderInfo);
    
    /**
     * @description: 审核信息转换(子单)
     * @param: [param]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2024/7/4 17:12
     */
    @PostMapping(value = "/verifyFailMsgConvertBySubOrder")
    public JsonResponse<String> verifyFailMsgConvertBySubOrder(@RequestBody OrderInfo orderInfo);
    
    /**
     * @description: 获取审核失败人员(父单)
     * @param: [param]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2024/7/4 17:12
     */
    @PostMapping(value = "/getVerifyFailPersonByCustOrder")
    public JsonResponse<String> getVerifyFailPersonByCustOrder(@RequestBody CustomerOrderInfo customerOrderInfo);
    
    /**
     * @description: 获取审核失败人员(子单)
     * @param: [param]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2024/7/4 17:12
     */
    @PostMapping(value = "/getVerifyFailPersonBySubOrder")
    public JsonResponse<String> getVerifyFailPersonBySubOrder(@RequestBody OrderInfo orderInfo);
}
