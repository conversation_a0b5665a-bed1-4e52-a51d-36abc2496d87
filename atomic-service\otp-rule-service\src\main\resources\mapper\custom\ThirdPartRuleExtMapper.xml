<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.ThirdPartRuleExtMapper">

    <sql id="searchFieldsSql">
        `delete_flag` AS  deleteFlag,
        `third_part` AS  thirdPart,
        `update_user_code` AS  updateUserCode,
        `wh_code` AS  whCode,
        `create_time` AS  createTime,
        `create_user_code` AS  createUserCode,
        `customer_code` AS  customerCode,
        `update_time` AS  updateTime,
        `remark` AS  remark,
        `id` AS  id,
        `version` AS  version,
        `midea_flag` AS  mideaFlag
    </sql>

    <select id="getThirdPartRule" resultType="com.midea.logistics.otp.rule.domain.bean.ThirdPartRule">
        SELECT
        wh_code,
        customer_code,
        midea_flag,
        third_part
        FROM
        third_part_rule
        WHERE
        delete_flag = 0
        <if test="customerCode !=null and customerCode != ''">
            AND customer_code = #{customerCode}
        </if>
        <if test="customerCode ==null or customerCode == ''">
            AND customer_code IS NULL
        </if>
        <if test="whCode !=null and whCode != ''">
            AND wh_code = #{whCode}
        </if>
        <if test="whCode ==null or whCode == ''">
            AND wh_code IS NULL
        </if>
        LIMIT 1
    </select>

</mapper>