package com.midea.logistics.otp.order.common.fegin.lastmile;

import com.midea.logistics.otp.order.common.bean.NetInfo;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: NetInfoFeign
* Author: zhangquan
* Date: 2019-11-15 15:15:37
* Description:网点信息表 feign
*/
@FeignClient(value = "logistics-mdm-service")
public interface NetInfoFeign {

    /**
    *
    *
    */
    @PostMapping(value = "/netInfo")
    JsonResponse create(@RequestBody NetInfo netInfo);

    /**
    * @param id
    * @return
    */
    @GetMapping(value = "/netInfo/{id}")
    JsonResponse<NetInfo> queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @PutMapping(value = "/netInfo/{id}")
    JsonResponse update(@PathVariable("id") Long id, @RequestBody NetInfo netInfo);

    /**
    * @param id
    * @return
    */
    @DeleteMapping(value = "/netInfo/{id}")
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @PostMapping(value = "/netInfos")
    JsonResponse<PageResponse<NetInfo>> search(@RequestBody NetInfo netInfo);

    /**
     *
     *
     */
    @PostMapping(value = "/receiveNetInfo")
    public JsonResponse receiveNetInfo(@RequestBody List<NetInfo> netInfos);
}
