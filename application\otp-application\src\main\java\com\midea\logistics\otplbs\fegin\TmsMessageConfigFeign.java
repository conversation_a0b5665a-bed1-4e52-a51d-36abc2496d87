package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.common.helper.bean.TmsMessageConfigExcelRequest;
import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.TmsMessageConfig;
import com.midea.logistics.otp.rule.domain.bean.custom.TmsMessageConfigDto;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: TmsMessageConfigFeign
* Author: james
* Date: 2021-7-2 14:43:14
* Description:tms短信配置表 feign
*/
@FeignClient(value = "logistics-otp-rule-service",url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface TmsMessageConfigFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/tmsMessageConfig", method = RequestMethod.POST)
    JsonResponse create(@RequestBody TmsMessageConfig tmsMessageConfig);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/tmsMessageConfig/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/tmsMessageConfig/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody TmsMessageConfig tmsMessageConfig);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/tmsMessageConfig/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/tmsMessageConfigs", method = RequestMethod.POST)
    JsonResponse search(@RequestBody TmsMessageConfig tmsMessageConfig);

    /**
     * 短信批量导入
     */
    @RequestMapping(value = OptRuleRouters.TMS_MESSAGE_CONFIG_IMPORT_BATCH_INSERT,method = RequestMethod.POST)
    JsonResponse importBatchInsert(@RequestBody List<TmsMessageConfigExcelRequest> requestList);

    /**
     * 批量新增或者保存
     */
    @PostMapping(OptRuleRouters.TMS_MESSAGE_CONFIG_BATCH_CREATE_OR_UPDATE)
    JsonResponse batchCreateOrUpdate(@RequestBody List<TmsMessageConfigDto> configDtos);

    /**
     * 批量删除
     */
    @RequestMapping(value = OptRuleRouters.TMS_MESSAGE_CONFIG_DELETE_BY_IDS, method = RequestMethod.DELETE)
    JsonResponse batchDeleteById(@RequestBody List<Long> ids);
}
