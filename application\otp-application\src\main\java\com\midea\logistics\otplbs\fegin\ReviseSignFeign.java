package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.converged.domain.request.ReviseSignRequest;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * FileName: ReviseSignFeign
 * Author: ludh
 * Date: 2022-07-08
 * Description:改签 feign
 */
@FeignClient(value = "logistics-otp-order-converged-service")
public interface ReviseSignFeign {

    @PostMapping(value = OrderRouters.REVISE_SIGN_PACKAGE_STATUS)
    JsonResponse reviseSignPackageStatus(@RequestBody ReviseSignRequest reviseSignRequest);

    @PostMapping(value = OrderRouters.REVISE_SIGN_CONFIRM)
    JsonResponse reviseSignConfirm(@RequestBody List<ReviseSignRequest> reviseSignRequest);

}
