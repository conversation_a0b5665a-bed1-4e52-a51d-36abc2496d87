<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskMapper">

    <sql id="searchFieldsSql">
            `id` AS  id,
            `order_type` AS  orderType,
            `require_out_time` AS  requireOutTime,
            `consignee_time_from` AS  consigneeTimeFrom,
            `company_name` AS  companyName,
            `order_wh_code` AS  orderWhCode,
            `main_waybill_no` AS  mainWaybillNo,
            `total_gross_weight` AS  totalGrossWeight,
            `delivered_verify_flag` AS  deliveredVerifyFlag,
            `wh_name` AS  whName,
            `pay_type` AS  payType,
            `upper_customer_code` AS  upperCustomerCode,
            `targe_site_code` AS  targeSiteCode,
            `if_up_stairs` AS  ifUpStairs,
            `install_flag` AS  installFlag,
            `target_customer_code` AS  targetCustomerCode,
            `task_status` AS  taskStatus,
            `qz_wareh_area_flag` AS  qzWarehAreaFlag,
            `gray_flag` AS  grayFlag,
            `aging_product_code` AS  agingProductCode,
            `receiver_sale_stype` AS  receiverSaleStype,
            `next_distribution_wh_code` AS  nextDistributionWhCode,
            `order_confirm_time` AS  orderConfirmTime,
            `order_site_name` AS  orderSiteName,
            `total_pkg_qty` AS  totalPkgQty,
            `relation_order_no` AS  relationOrderNo,
            `invoice_unit_name` AS  invoiceUnitName,
            `version` AS  version,
            `expect_arrive_start_time` AS  expectArriveStartTime,
            `targe_wh_code` AS  targeWhCode,
            `line_aging` AS  lineAging,
            `customer_name` AS  customerName,
            `excute_status` AS  excuteStatus,
            `upper_receiver_code` AS  upperReceiverCode,
            `line_cost` AS  lineCost,
            `cloud_wh_flag` AS  cloudWhFlag,
            `upper_reference_id` AS  upperReferenceId,
            `freight_basis` AS  freightBasis,
            `carrier_code` AS  carrierCode,
            `deliverypay_type` AS  deliverypayType,
            `total_net_weight` AS  totalNetWeight,
            `zone_code` AS  zoneCode,
            `plan_order_flag` AS  planOrderFlag,
            `distribution_wh_name` AS  distributionWhName,
            `upstream_doc_type` AS  upstreamDocType,
            `load_type` AS  loadType,
            `unit_area_name` AS  unitAreaName,
            `source_system` AS  sourceSystem,
            `distribution_flag` AS  distributionFlag,
            `contract_no` AS  contractNo,
            `customer_code` AS  customerCode,
            `site_name` AS  siteName,
            `line_name` AS  lineName,
            `supplier_code` AS  supplierCode,
            `order_value` AS  orderValue,
            `equipment_type` AS  equipmentType,
            `line_mileage` AS  lineMileage,
            `spec_carrier_flag` AS  specCarrierFlag,
            `upper_order_type` AS  upperOrderType,
            `return_reason` AS  returnReason,
            `task_no` AS  taskNo,
            `project_classify` AS  projectClassify,
            `order_end_time` AS  orderEndTime,
            `cancel_reason` AS  cancelReason,
            `specimen_type` AS  specimenType,
            `in_wh_time` AS  inWhTime,
            `work_order_no` AS  workOrderNo,
            `oaid` AS  oaid,
            `business_mode` AS  businessMode,
            `dispatch_no` AS  dispatchNo,
            `expect_install_start_time` AS  expectInstallStartTime,
            `collection_amount` AS  collectionAmount,
            `third_flag` AS  thirdFlag,
            `print_barcode` AS  printBarcode,
            `create_user_code` AS  createUserCode,
            `expect_install_type` AS  expectInstallType,
            `arrive_appointment_require_flag` AS  arriveAppointmentRequireFlag,
            `update_time` AS  updateTime,
            `customer_total_qty` AS  customerTotalQty,
            `expect_arrive_end_time` AS  expectArriveEndTime,
            `operations_center_code` AS  operationsCenterCode,
            `create_time` AS  createTime,
            `upper_wh_name` AS  upperWhName,
            `upper_sender_name` AS  upperSenderName,
            `delivery_cycle` AS  deliveryCycle,
            `business_category` AS  businessCategory,
            `distribution_last_flag` AS  distributionLastFlag,
            `customer_order_no` AS  customerOrderNo,
            `customer_aging_code` AS  customerAgingCode,
            `pay_date` AS  payDate,
            `total_volume` AS  totalVolume,
            `service_type` AS  serviceType,
            `total_qty` AS  totalQty,
            `pay_time` AS  payTime,
            `station_type` AS  stationType,
            `order_site_code` AS  orderSiteCode,
            `distribution_num` AS  distributionNum,
            `consolidation_num` AS  consolidationNum,
            `parent_order_no` AS  parentOrderNo,
            `distribution_site_code` AS  distributionSiteCode,
            `delete_flag` AS  deleteFlag,
            `buyer_remark` AS  buyerRemark,
            `is_back` AS  isBack,
            `driver_queue_code` AS  driverQueueCode,
            `order_time` AS  orderTime,
            `invoice_unit_code` AS  invoiceUnitCode,
            `jp_order_no` AS  jpOrderNo,
            `order_rp_flag` AS  orderRpFlag,
            `equipment_name` AS  equipmentName,
            `shop_id` AS  shopId,
            `shop_guide_tel` AS  shopGuideTel,
            `expect_install_end_time` AS  expectInstallEndTime,
            `mileage` AS  mileage,
            `company_code` AS  companyCode,
            `order_wh_name` AS  orderWhName,
            `invoice_flag` AS  invoiceFlag,
            `appointment_type` AS  appointmentType,
            `order_no` AS  orderNo,
            `customer_group` AS  customerGroup,
            `cn_dispatch` AS  cnDispatch,
            `collection_flag` AS  collectionFlag,
            `print_price_flag` AS  printPriceFlag,
            `shop_guide_name` AS  shopGuideName,
            `order_source_platform` AS  orderSourcePlatform,
            `outsource_flag` AS  outsourceFlag,
            `service_order_no` AS  serviceOrderNo,
            `update_user_code` AS  updateUserCode,
            `wh_code` AS  whCode,
            `setup_type` AS  setupType,
            `emergence_flag` AS  emergenceFlag,
            `upper_aging_code` AS  upperAgingCode,
            `appoint_order_no` AS  appointOrderNo,
            `transfer_flag` AS  transferFlag,
            `order_status` AS  orderStatus,
            `shop_name` AS  shopName,
            `remark` AS  remark,
            `tenant_code` AS  tenantCode,
            `pick_flag` AS  pickFlag,
            `distribution_station_type` AS  distributionStationType,
            `appointment_reason` AS  appointmentReason,
            `print_notax_price_flag` AS  printNotaxPriceFlag,
            `consolidation_order_no` AS  consolidationOrderNo,
            `upper_receiver_name` AS  upperReceiverName,
            `line_code` AS  lineCode,
            `task_type` AS  taskType,
            `in_out_type` AS  inOutType,
            `pledge_type` AS  pledgeType,
            `logistic_mode` AS  logisticMode,
            `sc_pos_flag` AS  scPosFlag,
            `upper_sender_code` AS  upperSenderCode,
            `appointment_time` AS  appointmentTime,
            `wh_system` AS  whSystem,
            `distribution_wh_code` AS  distributionWhCode,
            `transport_type` AS  transportType,
            `withdrawal_reason` AS  withdrawalReason,
            `zone_name` AS  zoneName,
            `upper_source_customer_code` AS  upperSourceCustomerCode,
            `professional_company` AS  professionalCompany,
            `order_source` AS  orderSource,
            `site_code` AS  siteCode,
            `upper_wh_code` AS  upperWhCode,
            `delivery_type` AS  deliveryType,
            `entity_id` AS  entityId,
            `distribution_site_name` AS  distributionSiteName,
            `hold_flag` AS  holdFlag,
            `origin_order_no` AS  originOrderNo,
            `upper_customer_name` AS  upperCustomerName,
            `platform_order_no` AS  platformOrderNo,
            `join_type` AS  joinType,
            `consignee_time_to` AS  consigneeTimeTo,
            `c2m_type` AS  c2mType,
            `business_type` AS  businessType,
            `tc_flag` AS  tcFlag,
            `next_distribution_site_code` AS  nextDistributionSiteCode,
            `waybill_no` AS  waybillNo,
            `order_start_time` AS  orderStartTime,
            `transport_system` AS  transportSystem,
            `transport_order_no` AS  transportOrderNo,
            `train_number` AS  trainNumber
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="orderType !=null and orderType != ''">
                        and `order_type` =#{orderType}
                    </if>
                    <if test="requireOutTime !=null">
                        and `require_out_time` =#{requireOutTime}
                    </if>
                    <if test="consigneeTimeFrom !=null">
                        and `consignee_time_from` =#{consigneeTimeFrom}
                    </if>
                    <if test="companyName !=null and companyName != ''">
                        and `company_name` =#{companyName}
                    </if>
                    <if test="orderWhCode !=null and orderWhCode != ''">
                        and `order_wh_code` =#{orderWhCode}
                    </if>
                    <if test="mainWaybillNo !=null and mainWaybillNo != ''">
                        and `main_waybill_no` =#{mainWaybillNo}
                    </if>
                    <if test="totalGrossWeight !=null">
                        and `total_gross_weight` =#{totalGrossWeight}
                    </if>
                    <if test="deliveredVerifyFlag !=null">
                        and `delivered_verify_flag` =#{deliveredVerifyFlag}
                    </if>
                    <if test="whName !=null and whName != ''">
                        and `wh_name` =#{whName}
                    </if>
                    <if test="payType !=null and payType != ''">
                        and `pay_type` =#{payType}
                    </if>
                    <if test="upperCustomerCode !=null and upperCustomerCode != ''">
                        and `upper_customer_code` =#{upperCustomerCode}
                    </if>
                    <if test="targeSiteCode !=null and targeSiteCode != ''">
                        and `targe_site_code` =#{targeSiteCode}
                    </if>
                    <if test="ifUpStairs !=null and ifUpStairs != ''">
                        and `if_up_stairs` =#{ifUpStairs}
                    </if>
                    <if test="installFlag !=null">
                        and `install_flag` =#{installFlag}
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="targetCustomerCode !=null and targetCustomerCode != ''">
                        and `target_customer_code` =#{targetCustomerCode}
                    </if>
                    <if test="taskStatus !=null">
                        and `task_status` =#{taskStatus}
                    </if>
                    <if test="qzWarehAreaFlag !=null and qzWarehAreaFlag != ''">
                        and `qz_wareh_area_flag` =#{qzWarehAreaFlag}
                    </if>
                    <if test="grayFlag !=null and grayFlag != ''">
                        and `gray_flag` =#{grayFlag}
                    </if>
                    <if test="agingProductCode !=null and agingProductCode != ''">
                        and `aging_product_code` =#{agingProductCode}
                    </if>
                    <if test="receiverSaleStype !=null and receiverSaleStype != ''">
                        and `receiver_sale_stype` =#{receiverSaleStype}
                    </if>
                    <if test="nextDistributionWhCode !=null and nextDistributionWhCode != ''">
                        and `next_distribution_wh_code` =#{nextDistributionWhCode}
                    </if>
                    <if test="orderConfirmTime !=null">
                        and `order_confirm_time` =#{orderConfirmTime}
                    </if>
                    <if test="orderSiteName !=null and orderSiteName != ''">
                        and `order_site_name` =#{orderSiteName}
                    </if>
                    <if test="totalPkgQty !=null">
                        and `total_pkg_qty` =#{totalPkgQty}
                    </if>
                    <if test="relationOrderNo !=null and relationOrderNo != ''">
                        and `relation_order_no` =#{relationOrderNo}
                    </if>
                    <if test="invoiceUnitName !=null and invoiceUnitName != ''">
                        and `invoice_unit_name` =#{invoiceUnitName}
                    </if>
                    <if test="expectArriveStartTime !=null">
                        and `expect_arrive_start_time` =#{expectArriveStartTime}
                    </if>
                    <if test="targeWhCode !=null and targeWhCode != ''">
                        and `targe_wh_code` =#{targeWhCode}
                    </if>
                    <if test="lineAging !=null">
                        and `line_aging` =#{lineAging}
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        and `customer_name` =#{customerName}
                    </if>
                    <if test="excuteStatus !=null">
                        and `excute_status` =#{excuteStatus}
                    </if>
                    <if test="upperReceiverCode !=null and upperReceiverCode != ''">
                        and `upper_receiver_code` =#{upperReceiverCode}
                    </if>
                    <if test="lineCost !=null">
                        and `line_cost` =#{lineCost}
                    </if>
                    <if test="cloudWhFlag !=null and cloudWhFlag != ''">
                        and `cloud_wh_flag` =#{cloudWhFlag}
                    </if>
                    <if test="upperReferenceId !=null and upperReferenceId != ''">
                        and `upper_reference_id` =#{upperReferenceId}
                    </if>
                    <if test="freightBasis !=null and freightBasis != ''">
                        and `freight_basis` =#{freightBasis}
                    </if>
                    <if test="carrierCode !=null and carrierCode != ''">
                        and `carrier_code` =#{carrierCode}
                    </if>
                    <if test="deliverypayType !=null">
                        and `deliverypay_type` =#{deliverypayType}
                    </if>
                    <if test="totalNetWeight !=null">
                        and `total_net_weight` =#{totalNetWeight}
                    </if>
                    <if test="zoneCode !=null and zoneCode != ''">
                        and `zone_code` =#{zoneCode}
                    </if>
                    <if test="planOrderFlag !=null">
                        and `plan_order_flag` =#{planOrderFlag}
                    </if>
                    <if test="distributionWhName !=null and distributionWhName != ''">
                        and `distribution_wh_name` =#{distributionWhName}
                    </if>
                    <if test="upstreamDocType !=null and upstreamDocType != ''">
                        and `upstream_doc_type` =#{upstreamDocType}
                    </if>
                    <if test="loadType !=null and loadType != ''">
                        and `load_type` =#{loadType}
                    </if>
                    <if test="unitAreaName !=null and unitAreaName != ''">
                        and `unit_area_name` =#{unitAreaName}
                    </if>
                    <if test="sourceSystem !=null and sourceSystem != ''">
                        and `source_system` =#{sourceSystem}
                    </if>
                    <if test="distributionFlag !=null">
                        and `distribution_flag` =#{distributionFlag}
                    </if>
                    <if test="contractNo !=null and contractNo != ''">
                        and `contract_no` =#{contractNo}
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        and `customer_code` =#{customerCode}
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        and `site_name` =#{siteName}
                    </if>
                    <if test="lineName !=null and lineName != ''">
                        and `line_name` =#{lineName}
                    </if>
                    <if test="supplierCode !=null and supplierCode != ''">
                        and `supplier_code` =#{supplierCode}
                    </if>
                    <if test="orderValue !=null">
                        and `order_value` =#{orderValue}
                    </if>
                    <if test="equipmentType !=null and equipmentType != ''">
                        and `equipment_type` =#{equipmentType}
                    </if>
                    <if test="lineMileage !=null">
                        and `line_mileage` =#{lineMileage}
                    </if>
                    <if test="specCarrierFlag !=null and specCarrierFlag != ''">
                        and `spec_carrier_flag` =#{specCarrierFlag}
                    </if>
                    <if test="upperOrderType !=null and upperOrderType != ''">
                        and `upper_order_type` =#{upperOrderType}
                    </if>
                    <if test="returnReason !=null and returnReason != ''">
                        and `return_reason` =#{returnReason}
                    </if>
                    <if test="taskNo !=null and taskNo != ''">
                        and `task_no` =#{taskNo}
                    </if>
                    <if test="projectClassify !=null and projectClassify != ''">
                        and `project_classify` =#{projectClassify}
                    </if>
                    <if test="orderEndTime !=null">
                        and `order_end_time` =#{orderEndTime}
                    </if>
                    <if test="cancelReason !=null and cancelReason != ''">
                        and `cancel_reason` =#{cancelReason}
                    </if>
                    <if test="specimenType !=null and specimenType != ''">
                        and `specimen_type` =#{specimenType}
                    </if>
                    <if test="inWhTime !=null">
                        and `in_wh_time` =#{inWhTime}
                    </if>
                    <if test="workOrderNo !=null and workOrderNo != ''">
                        and `work_order_no` =#{workOrderNo}
                    </if>
                    <if test="oaid !=null and oaid != ''">
                        and `oaid` =#{oaid}
                    </if>
                    <if test="businessMode !=null and businessMode != ''">
                        and `business_mode` =#{businessMode}
                    </if>
                    <if test="dispatchNo !=null and dispatchNo != ''">
                        and `dispatch_no` =#{dispatchNo}
                    </if>
                    <if test="expectInstallStartTime !=null">
                        and `expect_install_start_time` =#{expectInstallStartTime}
                    </if>
                    <if test="collectionAmount !=null">
                        and `collection_amount` =#{collectionAmount}
                    </if>
                    <if test="thirdFlag !=null and thirdFlag != ''">
                        and `third_flag` =#{thirdFlag}
                    </if>
                    <if test="printBarcode !=null">
                        and `print_barcode` =#{printBarcode}
                    </if>
                    <if test="expectInstallType !=null and expectInstallType != ''">
                        and `expect_install_type` =#{expectInstallType}
                    </if>
                    <if test="arriveAppointmentRequireFlag !=null">
                        and `arrive_appointment_require_flag` =#{arriveAppointmentRequireFlag}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="customerTotalQty !=null">
                        and `customer_total_qty` =#{customerTotalQty}
                    </if>
                    <if test="expectArriveEndTime !=null">
                        and `expect_arrive_end_time` =#{expectArriveEndTime}
                    </if>
                    <if test="operationsCenterCode !=null and operationsCenterCode != ''">
                        and `operations_center_code` =#{operationsCenterCode}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="upperWhName !=null and upperWhName != ''">
                        and `upper_wh_name` =#{upperWhName}
                    </if>
                    <if test="upperSenderName !=null and upperSenderName != ''">
                        and `upper_sender_name` =#{upperSenderName}
                    </if>
                    <if test="deliveryCycle !=null and deliveryCycle != ''">
                        and `delivery_cycle` =#{deliveryCycle}
                    </if>
                    <if test="businessCategory !=null and businessCategory != ''">
                        and `business_category` =#{businessCategory}
                    </if>
                    <if test="distributionLastFlag !=null">
                        and `distribution_last_flag` =#{distributionLastFlag}
                    </if>
                    <if test="customerOrderNo !=null and customerOrderNo != ''">
                        and `customer_order_no` =#{customerOrderNo}
                    </if>
                    <if test="customerAgingCode !=null and customerAgingCode != ''">
                        and `customer_aging_code` =#{customerAgingCode}
                    </if>
                    <if test="payDate !=null">
                        and `pay_date` =#{payDate}
                    </if>
                    <if test="totalVolume !=null">
                        and `total_volume` =#{totalVolume}
                    </if>
                    <if test="serviceType !=null and serviceType != ''">
                        and `service_type` =#{serviceType}
                    </if>
                    <if test="totalQty !=null">
                        and `total_qty` =#{totalQty}
                    </if>
                    <if test="payTime !=null">
                        and `pay_time` =#{payTime}
                    </if>
                    <if test="stationType !=null and stationType != ''">
                        and `station_type` =#{stationType}
                    </if>
                    <if test="orderSiteCode !=null and orderSiteCode != ''">
                        and `order_site_code` =#{orderSiteCode}
                    </if>
                    <if test="distributionNum !=null">
                        and `distribution_num` =#{distributionNum}
                    </if>
                    <if test="consolidationNum !=null">
                        and `consolidation_num` =#{consolidationNum}
                    </if>
                    <if test="parentOrderNo !=null and parentOrderNo != ''">
                        and `parent_order_no` =#{parentOrderNo}
                    </if>
                    <if test="distributionSiteCode !=null and distributionSiteCode != ''">
                        and `distribution_site_code` =#{distributionSiteCode}
                    </if>
                    <if test="buyerRemark !=null and buyerRemark != ''">
                        and `buyer_remark` =#{buyerRemark}
                    </if>
                    <if test="isBack !=null">
                        and `is_back` =#{isBack}
                    </if>
                    <if test="driverQueueCode !=null and driverQueueCode != ''">
                        and `driver_queue_code` =#{driverQueueCode}
                    </if>
                    <if test="orderTime !=null">
                        and `order_time` =#{orderTime}
                    </if>
                    <if test="invoiceUnitCode !=null and invoiceUnitCode != ''">
                        and `invoice_unit_code` =#{invoiceUnitCode}
                    </if>
                    <if test="jpOrderNo !=null and jpOrderNo != ''">
                        and `jp_order_no` =#{jpOrderNo}
                    </if>
                    <if test="orderRpFlag !=null and orderRpFlag != ''">
                        and `order_rp_flag` =#{orderRpFlag}
                    </if>
                    <if test="equipmentName !=null and equipmentName != ''">
                        and `equipment_name` =#{equipmentName}
                    </if>
                    <if test="shopId !=null and shopId != ''">
                        and `shop_id` =#{shopId}
                    </if>
                    <if test="shopGuideTel !=null and shopGuideTel != ''">
                        and `shop_guide_tel` =#{shopGuideTel}
                    </if>
                    <if test="expectInstallEndTime !=null">
                        and `expect_install_end_time` =#{expectInstallEndTime}
                    </if>
                    <if test="mileage !=null">
                        and `mileage` =#{mileage}
                    </if>
                    <if test="companyCode !=null and companyCode != ''">
                        and `company_code` =#{companyCode}
                    </if>
                    <if test="orderWhName !=null and orderWhName != ''">
                        and `order_wh_name` =#{orderWhName}
                    </if>
                    <if test="invoiceFlag !=null and invoiceFlag != ''">
                        and `invoice_flag` =#{invoiceFlag}
                    </if>
                    <if test="appointmentType !=null and appointmentType != ''">
                        and `appointment_type` =#{appointmentType}
                    </if>
                    <if test="orderNo !=null and orderNo != ''">
                        and `order_no` =#{orderNo}
                    </if>
                    <if test="customerGroup !=null and customerGroup != ''">
                        and `customer_group` =#{customerGroup}
                    </if>
                    <if test="cnDispatch !=null">
                        and `cn_dispatch` =#{cnDispatch}
                    </if>
                    <if test="collectionFlag !=null and collectionFlag != ''">
                        and `collection_flag` =#{collectionFlag}
                    </if>
                    <if test="printPriceFlag !=null">
                        and `print_price_flag` =#{printPriceFlag}
                    </if>
                    <if test="shopGuideName !=null and shopGuideName != ''">
                        and `shop_guide_name` =#{shopGuideName}
                    </if>
                    <if test="orderSourcePlatform !=null and orderSourcePlatform != ''">
                        and `order_source_platform` =#{orderSourcePlatform}
                    </if>
                    <if test="outsourceFlag !=null and outsourceFlag != ''">
                        and `outsource_flag` =#{outsourceFlag}
                    </if>
                    <if test="serviceOrderNo !=null and serviceOrderNo != ''">
                        and `service_order_no` =#{serviceOrderNo}
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        and `wh_code` =#{whCode}
                    </if>
                    <if test="setupType !=null and setupType != ''">
                        and `setup_type` =#{setupType}
                    </if>
                    <if test="emergenceFlag !=null and emergenceFlag != ''">
                        and `emergence_flag` =#{emergenceFlag}
                    </if>
                    <if test="upperAgingCode !=null and upperAgingCode != ''">
                        and `upper_aging_code` =#{upperAgingCode}
                    </if>
                    <if test="appointOrderNo !=null and appointOrderNo != ''">
                        and `appoint_order_no` =#{appointOrderNo}
                    </if>
                    <if test="transferFlag !=null">
                        and `transfer_flag` =#{transferFlag}
                    </if>
                    <if test="orderStatus !=null">
                        and `order_status` =#{orderStatus}
                    </if>
                    <if test="shopName !=null and shopName != ''">
                        and `shop_name` =#{shopName}
                    </if>
                    <if test="remark !=null">
                        and `remark` =#{remark}
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        <!--            and `tenant_code` =#{tenantCode}-->
                    </if>
                    <if test="pickFlag !=null and pickFlag != ''">
                        and `pick_flag` =#{pickFlag}
                    </if>
                    <if test="distributionStationType !=null and distributionStationType != ''">
                        and `distribution_station_type` =#{distributionStationType}
                    </if>
                    <if test="appointmentReason !=null and appointmentReason != ''">
                        and `appointment_reason` =#{appointmentReason}
                    </if>
                    <if test="printNotaxPriceFlag !=null">
                        and `print_notax_price_flag` =#{printNotaxPriceFlag}
                    </if>
                    <if test="consolidationOrderNo !=null and consolidationOrderNo != ''">
                        and `consolidation_order_no` =#{consolidationOrderNo}
                    </if>
                    <if test="upperReceiverName !=null and upperReceiverName != ''">
                        and `upper_receiver_name` =#{upperReceiverName}
                    </if>
                    <if test="lineCode !=null and lineCode != ''">
                        and `line_code` =#{lineCode}
                    </if>
                    <if test="taskType !=null and taskType != ''">
                        and `task_type` =#{taskType}
                    </if>
                    <if test="inOutType !=null and inOutType != ''">
                        and `in_out_type` =#{inOutType}
                    </if>
                    <if test="pledgeType !=null and pledgeType != ''">
                        and `pledge_type` =#{pledgeType}
                    </if>
                    <if test="logisticMode !=null and logisticMode != ''">
                        and `logistic_mode` =#{logisticMode}
                    </if>
                    <if test="scPosFlag !=null">
                        and `sc_pos_flag` =#{scPosFlag}
                    </if>
                    <if test="upperSenderCode !=null and upperSenderCode != ''">
                        and `upper_sender_code` =#{upperSenderCode}
                    </if>
                    <if test="appointmentTime !=null">
                        and `appointment_time` =#{appointmentTime}
                    </if>
                    <if test="whSystem !=null and whSystem != ''">
                        and `wh_system` =#{whSystem}
                    </if>
                    <if test="distributionWhCode !=null and distributionWhCode != ''">
                        and `distribution_wh_code` =#{distributionWhCode}
                    </if>
                    <if test="transportType !=null and transportType != ''">
                        and `transport_type` =#{transportType}
                    </if>
                    <if test="withdrawalReason !=null and withdrawalReason != ''">
                        and `withdrawal_reason` =#{withdrawalReason}
                    </if>
                    <if test="zoneName !=null and zoneName != ''">
                        and `zone_name` =#{zoneName}
                    </if>
                    <if test="upperSourceCustomerCode !=null and upperSourceCustomerCode != ''">
                        and `upper_source_customer_code` =#{upperSourceCustomerCode}
                    </if>
                    <if test="professionalCompany !=null and professionalCompany != ''">
                        and `professional_company` =#{professionalCompany}
                    </if>
                    <if test="orderSource !=null and orderSource != ''">
                        and `order_source` =#{orderSource}
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        and `site_code` =#{siteCode}
                    </if>
                    <if test="upperWhCode !=null and upperWhCode != ''">
                        and `upper_wh_code` =#{upperWhCode}
                    </if>
                    <if test="deliveryType !=null and deliveryType != ''">
                        and `delivery_type` =#{deliveryType}
                    </if>
                    <if test="entityId !=null">
                        and `entity_id` =#{entityId}
                    </if>
                    <if test="distributionSiteName !=null and distributionSiteName != ''">
                        and `distribution_site_name` =#{distributionSiteName}
                    </if>
                    <if test="holdFlag !=null">
                        and `hold_flag` =#{holdFlag}
                    </if>
                    <if test="originOrderNo !=null and originOrderNo != ''">
                        and `origin_order_no` =#{originOrderNo}
                    </if>
                    <if test="upperCustomerName !=null and upperCustomerName != ''">
                        and `upper_customer_name` =#{upperCustomerName}
                    </if>
                    <if test="platformOrderNo !=null and platformOrderNo != ''">
                        and `platform_order_no` =#{platformOrderNo}
                    </if>
                    <if test="joinType !=null and joinType != ''">
                        and `join_type` =#{joinType}
                    </if>
                    <if test="consigneeTimeTo !=null">
                        and `consignee_time_to` =#{consigneeTimeTo}
                    </if>
                    <if test="c2mType !=null and c2mType != ''">
                        and `c2m_type` =#{c2mType}
                    </if>
                    <if test="businessType !=null and businessType != ''">
                        and `business_type` =#{businessType}
                    </if>
                    <if test="tcFlag !=null and tcFlag != ''">
                        and `tc_flag` =#{tcFlag}
                    </if>
                    <if test="nextDistributionSiteCode !=null and nextDistributionSiteCode != ''">
                        and `next_distribution_site_code` =#{nextDistributionSiteCode}
                    </if>
                    <if test="waybillNo !=null and waybillNo != ''">
                        and `waybill_no` =#{waybillNo}
                    </if>
                    <if test="orderStartTime !=null">
                        and `order_start_time` =#{orderStartTime}
                    </if>
                    <if test="transportSystem !=null">
                        and `transport_system` =#{transportSystem}
                    </if>
                    <if test="transportOrderNo !=null">
                        and `transport_order_no` =#{transportOrderNo}
                    </if>
                    <if test="trainNumber !=null and trainNumber != ''">
                        and `train_number` =#{trainNumber}
                    </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
                        <if test="orderType !=null and orderType != ''">
                            `order_type` = #{orderType},
                        </if>
                        <if test="requireOutTime != null">
                            `require_out_time` = #{requireOutTime},
                        </if>
                        <if test="consigneeTimeFrom != null">
                            `consignee_time_from` = #{consigneeTimeFrom},
                        </if>
                        <if test="companyName !=null and companyName != ''">
                            `company_name` = #{companyName},
                        </if>
                        <if test="orderWhCode !=null and orderWhCode != ''">
                            `order_wh_code` = #{orderWhCode},
                        </if>
                        <if test="mainWaybillNo !=null and mainWaybillNo != ''">
                            `main_waybill_no` = #{mainWaybillNo},
                        </if>
                        <if test="totalGrossWeight != null">
                            `total_gross_weight` = #{totalGrossWeight},
                        </if>
                        <if test="deliveredVerifyFlag != null">
                            `delivered_verify_flag` = #{deliveredVerifyFlag},
                        </if>
                        <if test="whName !=null ">
                            `wh_name` = #{whName},
                        </if>
                        <if test="payType !=null and payType != ''">
                            `pay_type` = #{payType},
                        </if>
                        <if test="upperCustomerCode !=null and upperCustomerCode != ''">
                            `upper_customer_code` = #{upperCustomerCode},
                        </if>
                        <if test="targeSiteCode !=null and targeSiteCode != ''">
                            `targe_site_code` = #{targeSiteCode},
                        </if>
                        <if test="ifUpStairs !=null and ifUpStairs != ''">
                            `if_up_stairs` = #{ifUpStairs},
                        </if>
                        <if test="installFlag != null">
                            `install_flag` = #{installFlag},
                        </if>
                        <if test="targetCustomerCode !=null and targetCustomerCode != ''">
                            `target_customer_code` = #{targetCustomerCode},
                        </if>
                        <if test="taskStatus != null">
                            `task_status` = #{taskStatus},
                        </if>
                        <if test="qzWarehAreaFlag !=null and qzWarehAreaFlag != ''">
                            `qz_wareh_area_flag` = #{qzWarehAreaFlag},
                        </if>
                        <if test="grayFlag !=null and grayFlag != ''">
                            `gray_flag` = #{grayFlag},
                        </if>
                        <if test="agingProductCode !=null and agingProductCode != ''">
                            `aging_product_code` = #{agingProductCode},
                        </if>
                        <if test="receiverSaleStype !=null and receiverSaleStype != ''">
                            `receiver_sale_stype` = #{receiverSaleStype},
                        </if>
                        <if test="nextDistributionWhCode !=null and nextDistributionWhCode != ''">
                            `next_distribution_wh_code` = #{nextDistributionWhCode},
                        </if>
                        <if test="orderConfirmTime != null">
                            `order_confirm_time` = #{orderConfirmTime},
                        </if>
                        <if test="orderSiteName !=null and orderSiteName != ''">
                            `order_site_name` = #{orderSiteName},
                        </if>
                        <if test="totalPkgQty != null">
                            `total_pkg_qty` = #{totalPkgQty},
                        </if>
                        <if test="relationOrderNo !=null and relationOrderNo != ''">
                            `relation_order_no` = #{relationOrderNo},
                        </if>
                        <if test="invoiceUnitName !=null and invoiceUnitName != ''">
                            `invoice_unit_name` = #{invoiceUnitName},
                        </if>
                        <if test="expectArriveStartTime != null">
                            `expect_arrive_start_time` = #{expectArriveStartTime},
                        </if>
                        <if test="targeWhCode !=null and targeWhCode != ''">
                            `targe_wh_code` = #{targeWhCode},
                        </if>
                        <if test="lineAging != null">
                            `line_aging` = #{lineAging},
                        </if>
                        <if test="customerName !=null and customerName != ''">
                            `customer_name` = #{customerName},
                        </if>
                        <if test="excuteStatus != null">
                            `excute_status` = #{excuteStatus},
                        </if>
                        <if test="upperReceiverCode !=null and upperReceiverCode != ''">
                            `upper_receiver_code` = #{upperReceiverCode},
                        </if>
                        <if test="lineCost != null">
                            `line_cost` = #{lineCost},
                        </if>
                        <if test="cloudWhFlag !=null and cloudWhFlag != ''">
                            `cloud_wh_flag` = #{cloudWhFlag},
                        </if>
                        <if test="upperReferenceId !=null and upperReferenceId != ''">
                            `upper_reference_id` = #{upperReferenceId},
                        </if>
                        <if test="freightBasis !=null and freightBasis != ''">
                            `freight_basis` = #{freightBasis},
                        </if>
                        <if test="carrierCode !=null and carrierCode != ''">
                            `carrier_code` = #{carrierCode},
                        </if>
                        <if test="deliverypayType != null">
                            `deliverypay_type` = #{deliverypayType},
                        </if>
                        <if test="totalNetWeight != null">
                            `total_net_weight` = #{totalNetWeight},
                        </if>
                        <if test="zoneCode !=null and zoneCode != ''">
                            `zone_code` = #{zoneCode},
                        </if>
                        <if test="planOrderFlag != null">
                            `plan_order_flag` = #{planOrderFlag},
                        </if>
                        <if test="distributionWhName !=null ">
                            `distribution_wh_name` = #{distributionWhName},
                        </if>
                        <if test="upstreamDocType !=null and upstreamDocType != ''">
                            `upstream_doc_type` = #{upstreamDocType},
                        </if>
                        <if test="loadType !=null and loadType != ''">
                            `load_type` = #{loadType},
                        </if>
                        <if test="unitAreaName !=null and unitAreaName != ''">
                            `unit_area_name` = #{unitAreaName},
                        </if>
                        <if test="sourceSystem !=null and sourceSystem != ''">
                            `source_system` = #{sourceSystem},
                        </if>
                        <if test="distributionFlag != null">
                            `distribution_flag` = #{distributionFlag},
                        </if>
                        <if test="contractNo !=null and contractNo != ''">
                            `contract_no` = #{contractNo},
                        </if>
                        <if test="customerCode !=null and customerCode != ''">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="siteName !=null and siteName != ''">
                            `site_name` = #{siteName},
                        </if>
                        <if test="lineName !=null and lineName != ''">
                            `line_name` = #{lineName},
                        </if>
                        <if test="supplierCode !=null and supplierCode != ''">
                            `supplier_code` = #{supplierCode},
                        </if>
                        <if test="orderValue != null">
                            `order_value` = #{orderValue},
                        </if>
                        <if test="equipmentType !=null and equipmentType != ''">
                            `equipment_type` = #{equipmentType},
                        </if>
                        <if test="lineMileage != null">
                            `line_mileage` = #{lineMileage},
                        </if>
                        <if test="specCarrierFlag !=null and specCarrierFlag != ''">
                            `spec_carrier_flag` = #{specCarrierFlag},
                        </if>
                        <if test="upperOrderType !=null and upperOrderType != ''">
                            `upper_order_type` = #{upperOrderType},
                        </if>
                        <if test="returnReason !=null and returnReason != ''">
                            `return_reason` = #{returnReason},
                        </if>
                        <if test="taskNo !=null and taskNo != ''">
                            `task_no` = #{taskNo},
                        </if>
                        <if test="projectClassify !=null and projectClassify != ''">
                            `project_classify` = #{projectClassify},
                        </if>
                        <if test="orderEndTime != null">
                            `order_end_time` = #{orderEndTime},
                        </if>
                        <if test="cancelReason !=null and cancelReason != ''">
                            `cancel_reason` = #{cancelReason},
                        </if>
                        <if test="specimenType !=null and specimenType != ''">
                            `specimen_type` = #{specimenType},
                        </if>
                        <if test="inWhTime != null">
                            `in_wh_time` = #{inWhTime},
                        </if>
                        <if test="workOrderNo !=null and workOrderNo != ''">
                            `work_order_no` = #{workOrderNo},
                        </if>
                        <if test="oaid !=null and oaid != ''">
                            `oaid` = #{oaid},
                        </if>
                        <if test="businessMode !=null and businessMode != ''">
                            `business_mode` = #{businessMode},
                        </if>
                        <if test="dispatchNo !=null and dispatchNo != ''">
                            `dispatch_no` = #{dispatchNo},
                        </if>
                        <if test="expectInstallStartTime != null">
                            `expect_install_start_time` = #{expectInstallStartTime},
                        </if>
                        <if test="collectionAmount != null">
                            `collection_amount` = #{collectionAmount},
                        </if>
                        <if test="thirdFlag !=null and thirdFlag != ''">
                            `third_flag` = #{thirdFlag},
                        </if>
                        <if test="printBarcode != null">
                            `print_barcode` = #{printBarcode},
                        </if>
                        <if test="expectInstallType !=null and expectInstallType != ''">
                            `expect_install_type` = #{expectInstallType},
                        </if>
                        <if test="arriveAppointmentRequireFlag != null">
                            `arrive_appointment_require_flag` = #{arriveAppointmentRequireFlag},
                        </if>
                        <if test="customerTotalQty != null">
                            `customer_total_qty` = #{customerTotalQty},
                        </if>
                        <if test="expectArriveEndTime != null">
                            `expect_arrive_end_time` = #{expectArriveEndTime},
                        </if>
                        <if test="operationsCenterCode !=null and operationsCenterCode != ''">
                            `operations_center_code` = #{operationsCenterCode},
                        </if>
                        <if test="upperWhName !=null and upperWhName != ''">
                            `upper_wh_name` = #{upperWhName},
                        </if>
                        <if test="upperSenderName !=null and upperSenderName != ''">
                            `upper_sender_name` = #{upperSenderName},
                        </if>
                        <if test="deliveryCycle !=null and deliveryCycle != ''">
                            `delivery_cycle` = #{deliveryCycle},
                        </if>
                        <if test="businessCategory !=null and businessCategory != ''">
                            `business_category` = #{businessCategory},
                        </if>
                        <if test="distributionLastFlag != null">
                            `distribution_last_flag` = #{distributionLastFlag},
                        </if>
                        <if test="customerOrderNo !=null and customerOrderNo != ''">
                            `customer_order_no` = #{customerOrderNo},
                        </if>
                        <if test="customerAgingCode !=null and customerAgingCode != ''">
                            `customer_aging_code` = #{customerAgingCode},
                        </if>
                        <if test="payDate != null">
                            `pay_date` = #{payDate},
                        </if>
                        <if test="totalVolume != null">
                            `total_volume` = #{totalVolume},
                        </if>
                        <if test="serviceType !=null and serviceType != ''">
                            `service_type` = #{serviceType},
                        </if>
                        <if test="totalQty != null">
                            `total_qty` = #{totalQty},
                        </if>
                        <if test="payTime != null">
                            `pay_time` = #{payTime},
                        </if>
                        <if test="stationType !=null and stationType != ''">
                            `station_type` = #{stationType},
                        </if>
                        <if test="orderSiteCode !=null and orderSiteCode != ''">
                            `order_site_code` = #{orderSiteCode},
                        </if>
                        <if test="distributionNum != null">
                            `distribution_num` = #{distributionNum},
                        </if>
                        <if test="consolidationNum != null">
                            `consolidation_num` = #{consolidationNum},
                        </if>
                        <if test="parentOrderNo !=null and parentOrderNo != ''">
                            `parent_order_no` = #{parentOrderNo},
                        </if>
                        <if test="distributionSiteCode !=null ">
                            `distribution_site_code` = #{distributionSiteCode},
                        </if>
                        <if test="buyerRemark !=null and buyerRemark != ''">
                            `buyer_remark` = #{buyerRemark},
                        </if>
                        <if test="isBack != null">
                            `is_back` = #{isBack},
                        </if>
                        <if test="driverQueueCode !=null and driverQueueCode != ''">
                            `driver_queue_code` = #{driverQueueCode},
                        </if>
                        <if test="orderTime != null">
                            `order_time` = #{orderTime},
                        </if>
                        <if test="invoiceUnitCode !=null and invoiceUnitCode != ''">
                            `invoice_unit_code` = #{invoiceUnitCode},
                        </if>
                        <if test="jpOrderNo !=null and jpOrderNo != ''">
                            `jp_order_no` = #{jpOrderNo},
                        </if>
                        <if test="orderRpFlag !=null and orderRpFlag != ''">
                            `order_rp_flag` = #{orderRpFlag},
                        </if>
                        <if test="equipmentName !=null and equipmentName != ''">
                            `equipment_name` = #{equipmentName},
                        </if>
                        <if test="shopId !=null and shopId != ''">
                            `shop_id` = #{shopId},
                        </if>
                        <if test="shopGuideTel !=null and shopGuideTel != ''">
                            `shop_guide_tel` = #{shopGuideTel},
                        </if>
                        <if test="expectInstallEndTime != null">
                            `expect_install_end_time` = #{expectInstallEndTime},
                        </if>
                        <if test="mileage != null">
                            `mileage` = #{mileage},
                        </if>
                        <if test="companyCode !=null and companyCode != ''">
                            `company_code` = #{companyCode},
                        </if>
                        <if test="orderWhName !=null and orderWhName != ''">
                            `order_wh_name` = #{orderWhName},
                        </if>
                        <if test="invoiceFlag !=null and invoiceFlag != ''">
                            `invoice_flag` = #{invoiceFlag},
                        </if>
                        <if test="appointmentType !=null and appointmentType != ''">
                            `appointment_type` = #{appointmentType},
                        </if>
                        <if test="orderNo !=null and orderNo != ''">
                            `order_no` = #{orderNo},
                        </if>
                        <if test="customerGroup !=null and customerGroup != ''">
                            `customer_group` = #{customerGroup},
                        </if>
                        <if test="cnDispatch != null">
                            `cn_dispatch` = #{cnDispatch},
                        </if>
                        <if test="collectionFlag !=null and collectionFlag != ''">
                            `collection_flag` = #{collectionFlag},
                        </if>
                        <if test="printPriceFlag != null">
                            `print_price_flag` = #{printPriceFlag},
                        </if>
                        <if test="shopGuideName !=null and shopGuideName != ''">
                            `shop_guide_name` = #{shopGuideName},
                        </if>
                        <if test="orderSourcePlatform !=null and orderSourcePlatform != ''">
                            `order_source_platform` = #{orderSourcePlatform},
                        </if>
                        <if test="outsourceFlag !=null and outsourceFlag != ''">
                            `outsource_flag` = #{outsourceFlag},
                        </if>
                        <if test="serviceOrderNo !=null and serviceOrderNo != ''">
                            `service_order_no` = #{serviceOrderNo},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="whCode !=null">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="setupType !=null and setupType != ''">
                            `setup_type` = #{setupType},
                        </if>
                        <if test="emergenceFlag !=null and emergenceFlag != ''">
                            `emergence_flag` = #{emergenceFlag},
                        </if>
                        <if test="upperAgingCode !=null and upperAgingCode != ''">
                            `upper_aging_code` = #{upperAgingCode},
                        </if>
                        <if test="appointOrderNo !=null and appointOrderNo != ''">
                            `appoint_order_no` = #{appointOrderNo},
                        </if>
                        <if test="transferFlag != null">
                            `transfer_flag` = #{transferFlag},
                        </if>
                        <if test="orderStatus != null">
                            `order_status` = #{orderStatus},
                        </if>
                        <if test="shopName !=null and shopName != ''">
                            `shop_name` = #{shopName},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="tenantCode !=null and tenantCode != ''">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="pickFlag !=null and pickFlag != ''">
                            `pick_flag` = #{pickFlag},
                        </if>
                        <if test="distributionStationType !=null and distributionStationType != ''">
                            `distribution_station_type` = #{distributionStationType},
                        </if>
                        <if test="appointmentReason !=null and appointmentReason != ''">
                            `appointment_reason` = #{appointmentReason},
                        </if>
                        <if test="printNotaxPriceFlag != null">
                            `print_notax_price_flag` = #{printNotaxPriceFlag},
                        </if>
                        <if test="consolidationOrderNo !=null and consolidationOrderNo != ''">
                            `consolidation_order_no` = #{consolidationOrderNo},
                        </if>
                        <if test="upperReceiverName !=null and upperReceiverName != ''">
                            `upper_receiver_name` = #{upperReceiverName},
                        </if>
                        <if test="lineCode !=null and lineCode != ''">
                            `line_code` = #{lineCode},
                        </if>
                        <if test="taskType !=null and taskType != ''">
                            `task_type` = #{taskType},
                        </if>
                        <if test="inOutType !=null and inOutType != ''">
                            `in_out_type` = #{inOutType},
                        </if>
                        <if test="pledgeType !=null and pledgeType != ''">
                            `pledge_type` = #{pledgeType},
                        </if>
                        <if test="logisticMode !=null and logisticMode != ''">
                            `logistic_mode` = #{logisticMode},
                        </if>
                        <if test="scPosFlag != null">
                            `sc_pos_flag` = #{scPosFlag},
                        </if>
                        <if test="upperSenderCode !=null and upperSenderCode != ''">
                            `upper_sender_code` = #{upperSenderCode},
                        </if>
                        <if test="appointmentTime != null">
                            `appointment_time` = #{appointmentTime},
                        </if>
                        <if test="whSystem !=null and whSystem != ''">
                            `wh_system` = #{whSystem},
                        </if>
                        <if test="distributionWhCode !=null ">
                            `distribution_wh_code` = #{distributionWhCode},
                        </if>
                        <if test="transportType !=null and transportType != ''">
                            `transport_type` = #{transportType},
                        </if>
                        <if test="withdrawalReason !=null and withdrawalReason != ''">
                            `withdrawal_reason` = #{withdrawalReason},
                        </if>
                        <if test="zoneName !=null and zoneName != ''">
                            `zone_name` = #{zoneName},
                        </if>
                        <if test="upperSourceCustomerCode !=null and upperSourceCustomerCode != ''">
                            `upper_source_customer_code` = #{upperSourceCustomerCode},
                        </if>
                        <if test="professionalCompany !=null and professionalCompany != ''">
                            `professional_company` = #{professionalCompany},
                        </if>
                        <if test="orderSource !=null and orderSource != ''">
                            `order_source` = #{orderSource},
                        </if>
                        <if test="siteCode !=null and siteCode != ''">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="upperWhCode !=null and upperWhCode != ''">
                            `upper_wh_code` = #{upperWhCode},
                        </if>
                        <if test="deliveryType !=null and deliveryType != ''">
                            `delivery_type` = #{deliveryType},
                        </if>
                        <if test="entityId != null">
                            `entity_id` = #{entityId},
                        </if>
                        <if test="distributionSiteName !=null ">
                            `distribution_site_name` = #{distributionSiteName},
                        </if>
                        <if test="holdFlag != null">
                            `hold_flag` = #{holdFlag},
                        </if>
                        <if test="originOrderNo !=null and originOrderNo != ''">
                            `origin_order_no` = #{originOrderNo},
                        </if>
                        <if test="upperCustomerName !=null and upperCustomerName != ''">
                            `upper_customer_name` = #{upperCustomerName},
                        </if>
                        <if test="platformOrderNo !=null and platformOrderNo != ''">
                            `platform_order_no` = #{platformOrderNo},
                        </if>
                        <if test="joinType !=null and joinType != ''">
                            `join_type` = #{joinType},
                        </if>
                        <if test="consigneeTimeTo != null">
                            `consignee_time_to` = #{consigneeTimeTo},
                        </if>
                        <if test="c2mType !=null and c2mType != ''">
                            `c2m_type` = #{c2mType},
                        </if>
                        <if test="businessType !=null and businessType != ''">
                            `business_type` = #{businessType},
                        </if>
                        <if test="tcFlag !=null and tcFlag != ''">
                            `tc_flag` = #{tcFlag},
                        </if>
                        <if test="nextDistributionSiteCode !=null and nextDistributionSiteCode != ''">
                            `next_distribution_site_code` = #{nextDistributionSiteCode},
                        </if>
                        <if test="waybillNo !=null and waybillNo != ''">
                            `waybill_no` = #{waybillNo},
                        </if>
                        <if test="orderStartTime != null">
                            `order_start_time` = #{orderStartTime},
                        </if>
                        <if test="transportSystem != null">
                            `transport_system` = #{transportSystem},
                        </if>
                        <if test="transportOrderNo != null">
                            `transport_order_no` = #{transportOrderNo},
                        </if>
                        <if test="trainNumber != null and trainNumber != ''">
                            `train_number` = #{trainNumber},
                        </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="orderType !=null">
                            `order_type` = #{orderType},
                        </if>
                        <if test="requireOutTime != null">
                            `require_out_time` = #{requireOutTime},
                        </if>
                        <if test="consigneeTimeFrom != null">
                            `consignee_time_from` = #{consigneeTimeFrom},
                        </if>
                        <if test="companyName !=null">
                            `company_name` = #{companyName},
                        </if>
                        <if test="orderWhCode !=null">
                            `order_wh_code` = #{orderWhCode},
                        </if>
                        <if test="mainWaybillNo !=null">
                            `main_waybill_no` = #{mainWaybillNo},
                        </if>
                        <if test="totalGrossWeight != null">
                            `total_gross_weight` = #{totalGrossWeight},
                        </if>
                        <if test="deliveredVerifyFlag != null">
                            `delivered_verify_flag` = #{deliveredVerifyFlag},
                        </if>
                        <if test="whName !=null">
                            `wh_name` = #{whName},
                        </if>
                        <if test="payType !=null">
                            `pay_type` = #{payType},
                        </if>
                        <if test="upperCustomerCode !=null">
                            `upper_customer_code` = #{upperCustomerCode},
                        </if>
                        <if test="targeSiteCode !=null">
                            `targe_site_code` = #{targeSiteCode},
                        </if>
                        <if test="ifUpStairs !=null">
                            `if_up_stairs` = #{ifUpStairs},
                        </if>
                        <if test="installFlag != null">
                            `install_flag` = #{installFlag},
                        </if>
                        <if test="targetCustomerCode !=null">
                            `target_customer_code` = #{targetCustomerCode},
                        </if>
                        <if test="taskStatus != null">
                            `task_status` = #{taskStatus},
                        </if>
                        <if test="qzWarehAreaFlag !=null">
                            `qz_wareh_area_flag` = #{qzWarehAreaFlag},
                        </if>
                        <if test="grayFlag !=null">
                            `gray_flag` = #{grayFlag},
                        </if>
                        <if test="agingProductCode !=null">
                            `aging_product_code` = #{agingProductCode},
                        </if>
                        <if test="receiverSaleStype !=null">
                            `receiver_sale_stype` = #{receiverSaleStype},
                        </if>
                        <if test="nextDistributionWhCode !=null">
                            `next_distribution_wh_code` = #{nextDistributionWhCode},
                        </if>
                        <if test="orderConfirmTime != null">
                            `order_confirm_time` = #{orderConfirmTime},
                        </if>
                        <if test="orderSiteName !=null">
                            `order_site_name` = #{orderSiteName},
                        </if>
                        <if test="totalPkgQty != null">
                            `total_pkg_qty` = #{totalPkgQty},
                        </if>
                        <if test="relationOrderNo !=null">
                            `relation_order_no` = #{relationOrderNo},
                        </if>
                        <if test="invoiceUnitName !=null">
                            `invoice_unit_name` = #{invoiceUnitName},
                        </if>
                        <if test="expectArriveStartTime != null">
                            `expect_arrive_start_time` = #{expectArriveStartTime},
                        </if>
                        <if test="targeWhCode !=null">
                            `targe_wh_code` = #{targeWhCode},
                        </if>
                        <if test="lineAging != null">
                            `line_aging` = #{lineAging},
                        </if>
                        <if test="customerName !=null">
                            `customer_name` = #{customerName},
                        </if>
                        <if test="excuteStatus != null">
                            `excute_status` = #{excuteStatus},
                        </if>
                        <if test="upperReceiverCode !=null">
                            `upper_receiver_code` = #{upperReceiverCode},
                        </if>
                        <if test="lineCost != null">
                            `line_cost` = #{lineCost},
                        </if>
                        <if test="cloudWhFlag !=null">
                            `cloud_wh_flag` = #{cloudWhFlag},
                        </if>
                        <if test="upperReferenceId !=null">
                            `upper_reference_id` = #{upperReferenceId},
                        </if>
                        <if test="freightBasis !=null">
                            `freight_basis` = #{freightBasis},
                        </if>
                        <if test="carrierCode !=null">
                            `carrier_code` = #{carrierCode},
                        </if>
                        <if test="deliverypayType != null">
                            `deliverypay_type` = #{deliverypayType},
                        </if>
                        <if test="totalNetWeight != null">
                            `total_net_weight` = #{totalNetWeight},
                        </if>
                        <if test="zoneCode !=null">
                            `zone_code` = #{zoneCode},
                        </if>
                        <if test="planOrderFlag != null">
                            `plan_order_flag` = #{planOrderFlag},
                        </if>
                        <if test="distributionWhName !=null">
                            `distribution_wh_name` = #{distributionWhName},
                        </if>
                        <if test="upstreamDocType !=null">
                            `upstream_doc_type` = #{upstreamDocType},
                        </if>
                        <if test="loadType !=null">
                            `load_type` = #{loadType},
                        </if>
                        <if test="unitAreaName !=null">
                            `unit_area_name` = #{unitAreaName},
                        </if>
                        <if test="sourceSystem !=null">
                            `source_system` = #{sourceSystem},
                        </if>
                        <if test="distributionFlag != null">
                            `distribution_flag` = #{distributionFlag},
                        </if>
                        <if test="contractNo !=null">
                            `contract_no` = #{contractNo},
                        </if>
                        <if test="customerCode !=null">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="siteName !=null">
                            `site_name` = #{siteName},
                        </if>
                        <if test="lineName !=null">
                            `line_name` = #{lineName},
                        </if>
                        <if test="supplierCode !=null">
                            `supplier_code` = #{supplierCode},
                        </if>
                        <if test="orderValue != null">
                            `order_value` = #{orderValue},
                        </if>
                        <if test="equipmentType !=null">
                            `equipment_type` = #{equipmentType},
                        </if>
                        <if test="lineMileage != null">
                            `line_mileage` = #{lineMileage},
                        </if>
                        <if test="specCarrierFlag !=null">
                            `spec_carrier_flag` = #{specCarrierFlag},
                        </if>
                        <if test="upperOrderType !=null">
                            `upper_order_type` = #{upperOrderType},
                        </if>
                        <if test="returnReason !=null">
                            `return_reason` = #{returnReason},
                        </if>
                        <if test="taskNo !=null">
                            `task_no` = #{taskNo},
                        </if>
                        <if test="projectClassify !=null">
                            `project_classify` = #{projectClassify},
                        </if>
                        <if test="orderEndTime != null">
                            `order_end_time` = #{orderEndTime},
                        </if>
                        <if test="cancelReason !=null">
                            `cancel_reason` = #{cancelReason},
                        </if>
                        <if test="specimenType !=null">
                            `specimen_type` = #{specimenType},
                        </if>
                        <if test="inWhTime != null">
                            `in_wh_time` = #{inWhTime},
                        </if>
                        <if test="workOrderNo !=null">
                            `work_order_no` = #{workOrderNo},
                        </if>
                        <if test="oaid !=null">
                            `oaid` = #{oaid},
                        </if>
                        <if test="businessMode !=null">
                            `business_mode` = #{businessMode},
                        </if>
                        <if test="dispatchNo !=null">
                            `dispatch_no` = #{dispatchNo},
                        </if>
                        <if test="expectInstallStartTime != null">
                            `expect_install_start_time` = #{expectInstallStartTime},
                        </if>
                        <if test="collectionAmount != null">
                            `collection_amount` = #{collectionAmount},
                        </if>
                        <if test="thirdFlag !=null">
                            `third_flag` = #{thirdFlag},
                        </if>
                        <if test="printBarcode != null">
                            `print_barcode` = #{printBarcode},
                        </if>
                        <if test="expectInstallType !=null">
                            `expect_install_type` = #{expectInstallType},
                        </if>
                        <if test="arriveAppointmentRequireFlag != null">
                            `arrive_appointment_require_flag` = #{arriveAppointmentRequireFlag},
                        </if>
                        <if test="customerTotalQty != null">
                            `customer_total_qty` = #{customerTotalQty},
                        </if>
                        <if test="expectArriveEndTime != null">
                            `expect_arrive_end_time` = #{expectArriveEndTime},
                        </if>
                        <if test="operationsCenterCode !=null">
                            `operations_center_code` = #{operationsCenterCode},
                        </if>
                        <if test="upperWhName !=null">
                            `upper_wh_name` = #{upperWhName},
                        </if>
                        <if test="upperSenderName !=null">
                            `upper_sender_name` = #{upperSenderName},
                        </if>
                        <if test="deliveryCycle !=null">
                            `delivery_cycle` = #{deliveryCycle},
                        </if>
                        <if test="businessCategory !=null">
                            `business_category` = #{businessCategory},
                        </if>
                        <if test="distributionLastFlag != null">
                            `distribution_last_flag` = #{distributionLastFlag},
                        </if>
                        <if test="customerOrderNo !=null">
                            `customer_order_no` = #{customerOrderNo},
                        </if>
                        <if test="customerAgingCode !=null">
                            `customer_aging_code` = #{customerAgingCode},
                        </if>
                        <if test="payDate != null">
                            `pay_date` = #{payDate},
                        </if>
                        <if test="totalVolume != null">
                            `total_volume` = #{totalVolume},
                        </if>
                        <if test="serviceType !=null">
                            `service_type` = #{serviceType},
                        </if>
                        <if test="totalQty != null">
                            `total_qty` = #{totalQty},
                        </if>
                        <if test="payTime != null">
                            `pay_time` = #{payTime},
                        </if>
                        <if test="stationType !=null">
                            `station_type` = #{stationType},
                        </if>
                        <if test="orderSiteCode !=null">
                            `order_site_code` = #{orderSiteCode},
                        </if>
                        <if test="distributionNum != null">
                            `distribution_num` = #{distributionNum},
                        </if>
                        <if test="consolidationNum != null">
                            `consolidation_num` = #{consolidationNum},
                        </if>
                        <if test="parentOrderNo !=null">
                            `parent_order_no` = #{parentOrderNo},
                        </if>
                        <if test="distributionSiteCode !=null">
                            `distribution_site_code` = #{distributionSiteCode},
                        </if>
                        <if test="buyerRemark !=null">
                            `buyer_remark` = #{buyerRemark},
                        </if>
                        <if test="isBack != null">
                            `is_back` = #{isBack},
                        </if>
                        <if test="driverQueueCode !=null">
                            `driver_queue_code` = #{driverQueueCode},
                        </if>
                        <if test="orderTime != null">
                            `order_time` = #{orderTime},
                        </if>
                        <if test="invoiceUnitCode !=null">
                            `invoice_unit_code` = #{invoiceUnitCode},
                        </if>
                        <if test="jpOrderNo !=null">
                            `jp_order_no` = #{jpOrderNo},
                        </if>
                        <if test="orderRpFlag !=null">
                            `order_rp_flag` = #{orderRpFlag},
                        </if>
                        <if test="equipmentName !=null">
                            `equipment_name` = #{equipmentName},
                        </if>
                        <if test="shopId !=null">
                            `shop_id` = #{shopId},
                        </if>
                        <if test="shopGuideTel !=null">
                            `shop_guide_tel` = #{shopGuideTel},
                        </if>
                        <if test="expectInstallEndTime != null">
                            `expect_install_end_time` = #{expectInstallEndTime},
                        </if>
                        <if test="mileage != null">
                            `mileage` = #{mileage},
                        </if>
                        <if test="companyCode !=null">
                            `company_code` = #{companyCode},
                        </if>
                        <if test="orderWhName !=null">
                            `order_wh_name` = #{orderWhName},
                        </if>
                        <if test="invoiceFlag !=null">
                            `invoice_flag` = #{invoiceFlag},
                        </if>
                        <if test="appointmentType !=null">
                            `appointment_type` = #{appointmentType},
                        </if>
                        <if test="orderNo !=null">
                            `order_no` = #{orderNo},
                        </if>
                        <if test="customerGroup !=null">
                            `customer_group` = #{customerGroup},
                        </if>
                        <if test="cnDispatch != null">
                            `cn_dispatch` = #{cnDispatch},
                        </if>
                        <if test="collectionFlag !=null">
                            `collection_flag` = #{collectionFlag},
                        </if>
                        <if test="printPriceFlag != null">
                            `print_price_flag` = #{printPriceFlag},
                        </if>
                        <if test="shopGuideName !=null">
                            `shop_guide_name` = #{shopGuideName},
                        </if>
                        <if test="orderSourcePlatform !=null">
                            `order_source_platform` = #{orderSourcePlatform},
                        </if>
                        <if test="outsourceFlag !=null">
                            `outsource_flag` = #{outsourceFlag},
                        </if>
                        <if test="serviceOrderNo !=null">
                            `service_order_no` = #{serviceOrderNo},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="whCode !=null">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="setupType !=null">
                            `setup_type` = #{setupType},
                        </if>
                        <if test="emergenceFlag !=null">
                            `emergence_flag` = #{emergenceFlag},
                        </if>
                        <if test="upperAgingCode !=null">
                            `upper_aging_code` = #{upperAgingCode},
                        </if>
                        <if test="appointOrderNo !=null">
                            `appoint_order_no` = #{appointOrderNo},
                        </if>
                        <if test="transferFlag != null">
                            `transfer_flag` = #{transferFlag},
                        </if>
                        <if test="orderStatus != null">
                            `order_status` = #{orderStatus},
                        </if>
                        <if test="shopName !=null">
                            `shop_name` = #{shopName},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="tenantCode !=null">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="pickFlag !=null">
                            `pick_flag` = #{pickFlag},
                        </if>
                        <if test="distributionStationType !=null">
                            `distribution_station_type` = #{distributionStationType},
                        </if>
                        <if test="appointmentReason !=null">
                            `appointment_reason` = #{appointmentReason},
                        </if>
                        <if test="printNotaxPriceFlag != null">
                            `print_notax_price_flag` = #{printNotaxPriceFlag},
                        </if>
                        <if test="consolidationOrderNo !=null">
                            `consolidation_order_no` = #{consolidationOrderNo},
                        </if>
                        <if test="upperReceiverName !=null">
                            `upper_receiver_name` = #{upperReceiverName},
                        </if>
                        <if test="lineCode !=null">
                            `line_code` = #{lineCode},
                        </if>
                        <if test="taskType !=null">
                            `task_type` = #{taskType},
                        </if>
                        <if test="inOutType !=null">
                            `in_out_type` = #{inOutType},
                        </if>
                        <if test="pledgeType !=null">
                            `pledge_type` = #{pledgeType},
                        </if>
                        <if test="logisticMode !=null">
                            `logistic_mode` = #{logisticMode},
                        </if>
                        <if test="scPosFlag != null">
                            `sc_pos_flag` = #{scPosFlag},
                        </if>
                        <if test="upperSenderCode !=null">
                            `upper_sender_code` = #{upperSenderCode},
                        </if>
                        <if test="appointmentTime != null">
                            `appointment_time` = #{appointmentTime},
                        </if>
                        <if test="whSystem !=null">
                            `wh_system` = #{whSystem},
                        </if>
                        <if test="distributionWhCode !=null">
                            `distribution_wh_code` = #{distributionWhCode},
                        </if>
                        <if test="transportType !=null">
                            `transport_type` = #{transportType},
                        </if>
                        <if test="withdrawalReason !=null">
                            `withdrawal_reason` = #{withdrawalReason},
                        </if>
                        <if test="zoneName !=null">
                            `zone_name` = #{zoneName},
                        </if>
                        <if test="upperSourceCustomerCode !=null">
                            `upper_source_customer_code` = #{upperSourceCustomerCode},
                        </if>
                        <if test="professionalCompany !=null">
                            `professional_company` = #{professionalCompany},
                        </if>
                        <if test="orderSource !=null">
                            `order_source` = #{orderSource},
                        </if>
                        <if test="siteCode !=null">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="upperWhCode !=null">
                            `upper_wh_code` = #{upperWhCode},
                        </if>
                        <if test="deliveryType !=null">
                            `delivery_type` = #{deliveryType},
                        </if>
                        <if test="entityId != null">
                            `entity_id` = #{entityId},
                        </if>
                        <if test="distributionSiteName !=null">
                            `distribution_site_name` = #{distributionSiteName},
                        </if>
                        <if test="holdFlag != null">
                            `hold_flag` = #{holdFlag},
                        </if>
                        <if test="originOrderNo !=null">
                            `origin_order_no` = #{originOrderNo},
                        </if>
                        <if test="upperCustomerName !=null">
                            `upper_customer_name` = #{upperCustomerName},
                        </if>
                        <if test="platformOrderNo !=null">
                            `platform_order_no` = #{platformOrderNo},
                        </if>
                        <if test="joinType !=null">
                            `join_type` = #{joinType},
                        </if>
                        <if test="consigneeTimeTo != null">
                            `consignee_time_to` = #{consigneeTimeTo},
                        </if>
                        <if test="c2mType !=null">
                            `c2m_type` = #{c2mType},
                        </if>
                        <if test="businessType !=null">
                            `business_type` = #{businessType},
                        </if>
                        <if test="tcFlag !=null">
                            `tc_flag` = #{tcFlag},
                        </if>
                        <if test="nextDistributionSiteCode !=null">
                            `next_distribution_site_code` = #{nextDistributionSiteCode},
                        </if>
                        <if test="waybillNo !=null">
                            `waybill_no` = #{waybillNo},
                        </if>
                        <if test="orderStartTime != null">
                            `order_start_time` = #{orderStartTime},
                        </if>
                        <if test="transportSystem != null">
                            `transport_system` = #{transportSystem},
                        </if>
                        <if test="transportOrderNo != null">
                            `transport_order_no` = #{transportOrderNo},
                        </if>
                        <if test="trainNumber != null">
                            `train_number` = #{trainNumber},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from task t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        from task t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">${orderByType}</if>
        </if>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from task t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        from task t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
${orderByType}            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        task t
        <include refid="setFieldsSql"/>
        where
             `id` = #{id}
        <!--                and `version` = #{version}-->
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        task t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        task t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.Task" useGeneratedKeys="true" keyProperty="id">
        insert into task
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="orderType !=null and orderType != ''">
                        `order_type`,
                    </if>

                    <if test="requireOutTime != null">
                        `require_out_time`,
                    </if>

                    <if test="consigneeTimeFrom != null">
                        `consignee_time_from`,
                    </if>

                    <if test="companyName !=null and companyName != ''">
                        `company_name`,
                    </if>

                    <if test="orderWhCode !=null and orderWhCode != ''">
                        `order_wh_code`,
                    </if>

                    <if test="mainWaybillNo !=null and mainWaybillNo != ''">
                        `main_waybill_no`,
                    </if>

                    <if test="totalGrossWeight != null">
                        `total_gross_weight`,
                    </if>

                    <if test="deliveredVerifyFlag != null">
                        `delivered_verify_flag`,
                    </if>

                    <if test="whName !=null and whName != ''">
                        `wh_name`,
                    </if>

                    <if test="payType !=null and payType != ''">
                        `pay_type`,
                    </if>

                    <if test="upperCustomerCode !=null and upperCustomerCode != ''">
                        `upper_customer_code`,
                    </if>

                    <if test="targeSiteCode !=null and targeSiteCode != ''">
                        `targe_site_code`,
                    </if>

                    <if test="ifUpStairs !=null and ifUpStairs != ''">
                        `if_up_stairs`,
                    </if>

                    <if test="installFlag != null">
                        `install_flag`,
                    </if>

                    <if test="targetCustomerCode !=null and targetCustomerCode != ''">
                        `target_customer_code`,
                    </if>

                    <if test="taskStatus != null">
                        `task_status`,
                    </if>

                    <if test="qzWarehAreaFlag !=null and qzWarehAreaFlag != ''">
                        `qz_wareh_area_flag`,
                    </if>

                    <if test="grayFlag !=null and grayFlag != ''">
                        `gray_flag`,
                    </if>

                    <if test="agingProductCode !=null and agingProductCode != ''">
                        `aging_product_code`,
                    </if>

                    <if test="receiverSaleStype !=null and receiverSaleStype != ''">
                        `receiver_sale_stype`,
                    </if>

                    <if test="nextDistributionWhCode !=null and nextDistributionWhCode != ''">
                        `next_distribution_wh_code`,
                    </if>

                    <if test="orderConfirmTime != null">
                        `order_confirm_time`,
                    </if>

                    <if test="orderSiteName !=null and orderSiteName != ''">
                        `order_site_name`,
                    </if>

                    <if test="totalPkgQty != null">
                        `total_pkg_qty`,
                    </if>

                    <if test="relationOrderNo !=null and relationOrderNo != ''">
                        `relation_order_no`,
                    </if>

                    <if test="invoiceUnitName !=null and invoiceUnitName != ''">
                        `invoice_unit_name`,
                    </if>

                    <if test="expectArriveStartTime != null">
                        `expect_arrive_start_time`,
                    </if>

                    <if test="targeWhCode !=null and targeWhCode != ''">
                        `targe_wh_code`,
                    </if>

                    <if test="lineAging != null">
                        `line_aging`,
                    </if>

                    <if test="customerName !=null and customerName != ''">
                        `customer_name`,
                    </if>

                    <if test="excuteStatus != null">
                        `excute_status`,
                    </if>

                    <if test="upperReceiverCode !=null and upperReceiverCode != ''">
                        `upper_receiver_code`,
                    </if>

                    <if test="lineCost != null">
                        `line_cost`,
                    </if>

                    <if test="cloudWhFlag !=null and cloudWhFlag != ''">
                        `cloud_wh_flag`,
                    </if>

                    <if test="upperReferenceId !=null and upperReferenceId != ''">
                        `upper_reference_id`,
                    </if>

                    <if test="freightBasis !=null and freightBasis != ''">
                        `freight_basis`,
                    </if>

                    <if test="carrierCode !=null and carrierCode != ''">
                        `carrier_code`,
                    </if>

                    <if test="deliverypayType != null">
                        `deliverypay_type`,
                    </if>

                    <if test="totalNetWeight != null">
                        `total_net_weight`,
                    </if>

                    <if test="zoneCode !=null and zoneCode != ''">
                        `zone_code`,
                    </if>

                    <if test="planOrderFlag != null">
                        `plan_order_flag`,
                    </if>

                    <if test="distributionWhName !=null and distributionWhName != ''">
                        `distribution_wh_name`,
                    </if>

                    <if test="upstreamDocType !=null and upstreamDocType != ''">
                        `upstream_doc_type`,
                    </if>

                    <if test="loadType !=null and loadType != ''">
                        `load_type`,
                    </if>

                    <if test="unitAreaName !=null and unitAreaName != ''">
                        `unit_area_name`,
                    </if>

                    <if test="sourceSystem !=null and sourceSystem != ''">
                        `source_system`,
                    </if>

                    <if test="distributionFlag != null">
                        `distribution_flag`,
                    </if>

                    <if test="contractNo !=null and contractNo != ''">
                        `contract_no`,
                    </if>

                    <if test="customerCode !=null and customerCode != ''">
                        `customer_code`,
                    </if>

                    <if test="siteName !=null and siteName != ''">
                        `site_name`,
                    </if>

                    <if test="lineName !=null and lineName != ''">
                        `line_name`,
                    </if>

                    <if test="supplierCode !=null and supplierCode != ''">
                        `supplier_code`,
                    </if>

                    <if test="orderValue != null">
                        `order_value`,
                    </if>

                    <if test="equipmentType !=null and equipmentType != ''">
                        `equipment_type`,
                    </if>

                    <if test="lineMileage != null">
                        `line_mileage`,
                    </if>

                    <if test="specCarrierFlag !=null and specCarrierFlag != ''">
                        `spec_carrier_flag`,
                    </if>

                    <if test="upperOrderType !=null and upperOrderType != ''">
                        `upper_order_type`,
                    </if>

                    <if test="returnReason !=null and returnReason != ''">
                        `return_reason`,
                    </if>

                    <if test="taskNo !=null and taskNo != ''">
                        `task_no`,
                    </if>

                    <if test="projectClassify !=null and projectClassify != ''">
                        `project_classify`,
                    </if>

                    <if test="orderEndTime != null">
                        `order_end_time`,
                    </if>

                    <if test="cancelReason !=null and cancelReason != ''">
                        `cancel_reason`,
                    </if>

                    <if test="specimenType !=null and specimenType != ''">
                        `specimen_type`,
                    </if>

                    <if test="inWhTime != null">
                        `in_wh_time`,
                    </if>

                    <if test="workOrderNo !=null and workOrderNo != ''">
                        `work_order_no`,
                    </if>

                    <if test="oaid !=null and oaid != ''">
                        `oaid`,
                    </if>

                    <if test="businessMode !=null and businessMode != ''">
                        `business_mode`,
                    </if>

                    <if test="dispatchNo !=null and dispatchNo != ''">
                        `dispatch_no`,
                    </if>

                    <if test="expectInstallStartTime != null">
                        `expect_install_start_time`,
                    </if>

                    <if test="collectionAmount != null">
                        `collection_amount`,
                    </if>

                    <if test="thirdFlag !=null and thirdFlag != ''">
                        `third_flag`,
                    </if>

                    <if test="printBarcode != null">
                        `print_barcode`,
                    </if>

                    <if test="createUserCode != null">
                        `create_user_code`,
                    </if>

                    <if test="expectInstallType !=null and expectInstallType != ''">
                        `expect_install_type`,
                    </if>

                    <if test="arriveAppointmentRequireFlag != null">
                        `arrive_appointment_require_flag`,
                    </if>

                    <if test="customerTotalQty != null">
                        `customer_total_qty`,
                    </if>

                    <if test="expectArriveEndTime != null">
                        `expect_arrive_end_time`,
                    </if>

                    <if test="operationsCenterCode !=null and operationsCenterCode != ''">
                        `operations_center_code`,
                    </if>

                    <if test="upperWhName !=null and upperWhName != ''">
                        `upper_wh_name`,
                    </if>

                    <if test="upperSenderName !=null and upperSenderName != ''">
                        `upper_sender_name`,
                    </if>

                    <if test="deliveryCycle !=null and deliveryCycle != ''">
                        `delivery_cycle`,
                    </if>

                    <if test="businessCategory !=null and businessCategory != ''">
                        `business_category`,
                    </if>

                    <if test="distributionLastFlag != null">
                        `distribution_last_flag`,
                    </if>

                    <if test="customerOrderNo !=null and customerOrderNo != ''">
                        `customer_order_no`,
                    </if>

                    <if test="customerAgingCode !=null and customerAgingCode != ''">
                        `customer_aging_code`,
                    </if>

                    <if test="payDate != null">
                        `pay_date`,
                    </if>

                    <if test="totalVolume != null">
                        `total_volume`,
                    </if>

                    <if test="serviceType !=null and serviceType != ''">
                        `service_type`,
                    </if>

                    <if test="totalQty != null">
                        `total_qty`,
                    </if>

                    <if test="payTime != null">
                        `pay_time`,
                    </if>

                    <if test="stationType !=null and stationType != ''">
                        `station_type`,
                    </if>

                    <if test="orderSiteCode !=null and orderSiteCode != ''">
                        `order_site_code`,
                    </if>

                    <if test="distributionNum != null">
                        `distribution_num`,
                    </if>

                    <if test="consolidationNum != null">
                        `consolidation_num`,
                    </if>

                    <if test="parentOrderNo !=null and parentOrderNo != ''">
                        `parent_order_no`,
                    </if>

                    <if test="distributionSiteCode !=null and distributionSiteCode != ''">
                        `distribution_site_code`,
                    </if>

                    <if test="buyerRemark !=null and buyerRemark != ''">
                        `buyer_remark`,
                    </if>

                    <if test="isBack != null">
                        `is_back`,
                    </if>

                    <if test="driverQueueCode !=null and driverQueueCode != ''">
                        `driver_queue_code`,
                    </if>

                    <if test="orderTime != null">
                        `order_time`,
                    </if>

                    <if test="invoiceUnitCode !=null and invoiceUnitCode != ''">
                        `invoice_unit_code`,
                    </if>

                    <if test="jpOrderNo !=null and jpOrderNo != ''">
                        `jp_order_no`,
                    </if>

                    <if test="orderRpFlag !=null and orderRpFlag != ''">
                        `order_rp_flag`,
                    </if>

                    <if test="equipmentName !=null and equipmentName != ''">
                        `equipment_name`,
                    </if>

                    <if test="shopId !=null and shopId != ''">
                        `shop_id`,
                    </if>

                    <if test="shopGuideTel !=null and shopGuideTel != ''">
                        `shop_guide_tel`,
                    </if>

                    <if test="expectInstallEndTime != null">
                        `expect_install_end_time`,
                    </if>

                    <if test="mileage != null">
                        `mileage`,
                    </if>

                    <if test="companyCode !=null and companyCode != ''">
                        `company_code`,
                    </if>

                    <if test="orderWhName !=null and orderWhName != ''">
                        `order_wh_name`,
                    </if>

                    <if test="invoiceFlag !=null and invoiceFlag != ''">
                        `invoice_flag`,
                    </if>

                    <if test="appointmentType !=null and appointmentType != ''">
                        `appointment_type`,
                    </if>

                    <if test="orderNo !=null and orderNo != ''">
                        `order_no`,
                    </if>

                    <if test="customerGroup !=null and customerGroup != ''">
                        `customer_group`,
                    </if>

                    <if test="cnDispatch != null">
                        `cn_dispatch`,
                    </if>

                    <if test="collectionFlag !=null and collectionFlag != ''">
                        `collection_flag`,
                    </if>

                    <if test="printPriceFlag != null">
                        `print_price_flag`,
                    </if>

                    <if test="shopGuideName !=null">
                        `shop_guide_name`,
                    </if>

                    <if test="orderSourcePlatform !=null and orderSourcePlatform != ''">
                        `order_source_platform`,
                    </if>

                    <if test="outsourceFlag !=null and outsourceFlag != ''">
                        `outsource_flag`,
                    </if>

                    <if test="serviceOrderNo !=null and serviceOrderNo != ''">
                        `service_order_no`,
                    </if>

                    <if test="updateUserCode != null">
                        `update_user_code`,
                    </if>

                    <if test="whCode !=null and whCode != ''">
                        `wh_code`,
                    </if>

                    <if test="setupType !=null and setupType != ''">
                        `setup_type`,
                    </if>

                    <if test="emergenceFlag !=null and emergenceFlag != ''">
                        `emergence_flag`,
                    </if>

                    <if test="upperAgingCode !=null and upperAgingCode != ''">
                        `upper_aging_code`,
                    </if>

                    <if test="appointOrderNo !=null and appointOrderNo != ''">
                        `appoint_order_no`,
                    </if>

                    <if test="transferFlag != null">
                        `transfer_flag`,
                    </if>

                    <if test="orderStatus != null">
                        `order_status`,
                    </if>

                    <if test="shopName !=null and shopName != ''">
                        `shop_name`,
                    </if>

                    <if test="remark != null">
                        `remark`,
                    </if>

                    <if test="tenantCode !=null and tenantCode != ''">
                        `tenant_code`,
                    </if>

                    <if test="pickFlag !=null and pickFlag != ''">
                        `pick_flag`,
                    </if>

                    <if test="distributionStationType !=null and distributionStationType != ''">
                        `distribution_station_type`,
                    </if>

                    <if test="appointmentReason !=null and appointmentReason != ''">
                        `appointment_reason`,
                    </if>

                    <if test="printNotaxPriceFlag != null">
                        `print_notax_price_flag`,
                    </if>

                    <if test="consolidationOrderNo !=null and consolidationOrderNo != ''">
                        `consolidation_order_no`,
                    </if>

                    <if test="upperReceiverName !=null and upperReceiverName != ''">
                        `upper_receiver_name`,
                    </if>

                    <if test="lineCode !=null and lineCode != ''">
                        `line_code`,
                    </if>

                    <if test="taskType !=null and taskType != ''">
                        `task_type`,
                    </if>

                    <if test="inOutType !=null and inOutType != ''">
                        `in_out_type`,
                    </if>

                    <if test="pledgeType !=null and pledgeType != ''">
                        `pledge_type`,
                    </if>

                    <if test="logisticMode !=null and logisticMode != ''">
                        `logistic_mode`,
                    </if>

                    <if test="scPosFlag != null">
                        `sc_pos_flag`,
                    </if>

                    <if test="upperSenderCode !=null and upperSenderCode != ''">
                        `upper_sender_code`,
                    </if>

                    <if test="appointmentTime != null">
                        `appointment_time`,
                    </if>

                    <if test="whSystem !=null and whSystem != ''">
                        `wh_system`,
                    </if>

                    <if test="distributionWhCode !=null and distributionWhCode != ''">
                        `distribution_wh_code`,
                    </if>

                    <if test="transportType !=null and transportType != ''">
                        `transport_type`,
                    </if>

                    <if test="withdrawalReason !=null and withdrawalReason != ''">
                        `withdrawal_reason`,
                    </if>

                    <if test="zoneName !=null and zoneName != ''">
                        `zone_name`,
                    </if>

                    <if test="upperSourceCustomerCode !=null and upperSourceCustomerCode != ''">
                        `upper_source_customer_code`,
                    </if>

                    <if test="professionalCompany !=null and professionalCompany != ''">
                        `professional_company`,
                    </if>

                    <if test="orderSource !=null and orderSource != ''">
                        `order_source`,
                    </if>

                    <if test="siteCode !=null and siteCode != ''">
                        `site_code`,
                    </if>

                    <if test="upperWhCode !=null and upperWhCode != ''">
                        `upper_wh_code`,
                    </if>

                    <if test="deliveryType !=null and deliveryType != ''">
                        `delivery_type`,
                    </if>

                    <if test="entityId != null">
                        `entity_id`,
                    </if>

                    <if test="distributionSiteName !=null and distributionSiteName != ''">
                        `distribution_site_name`,
                    </if>

                    <if test="holdFlag != null">
                        `hold_flag`,
                    </if>

                    <if test="originOrderNo !=null and originOrderNo != ''">
                        `origin_order_no`,
                    </if>

                    <if test="upperCustomerName !=null and upperCustomerName != ''">
                        `upper_customer_name`,
                    </if>

                    <if test="platformOrderNo !=null and platformOrderNo != ''">
                        `platform_order_no`,
                    </if>

                    <if test="joinType !=null and joinType != ''">
                        `join_type`,
                    </if>

                    <if test="consigneeTimeTo != null">
                        `consignee_time_to`,
                    </if>

                    <if test="c2mType !=null and c2mType != ''">
                        `c2m_type`,
                    </if>

                    <if test="businessType !=null and businessType != ''">
                        `business_type`,
                    </if>

                    <if test="tcFlag !=null and tcFlag != ''">
                        `tc_flag`,
                    </if>

                    <if test="nextDistributionSiteCode !=null and nextDistributionSiteCode != ''">
                        `next_distribution_site_code`,
                    </if>

                    <if test="waybillNo !=null and waybillNo != ''">
                        `waybill_no`,
                    </if>

                    <if test="orderStartTime != null">
                        `order_start_time`,
                    </if>

                    <if test="transportSystem != null">
                        `transport_system`,
                    </if>

                    <if test="transportOrderNo != null">
                        `transport_order_no`,
                    </if>

                    <if test="trainNumber != null and trainNumber != ''">
                        `train_number`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="orderType !=null and orderType != ''">
                        #{orderType},
                    </if>
                    <if test="requireOutTime != null">
                        #{requireOutTime},
                    </if>
                    <if test="consigneeTimeFrom != null">
                        #{consigneeTimeFrom},
                    </if>
                    <if test="companyName !=null and companyName != ''">
                        #{companyName},
                    </if>
                    <if test="orderWhCode !=null and orderWhCode != ''">
                        #{orderWhCode},
                    </if>
                    <if test="mainWaybillNo !=null and mainWaybillNo != ''">
                        #{mainWaybillNo},
                    </if>
                    <if test="totalGrossWeight != null">
                        #{totalGrossWeight},
                    </if>
                    <if test="deliveredVerifyFlag != null">
                        #{deliveredVerifyFlag},
                    </if>
                    <if test="whName !=null and whName != ''">
                        #{whName},
                    </if>
                    <if test="payType !=null and payType != ''">
                        #{payType},
                    </if>
                    <if test="upperCustomerCode !=null and upperCustomerCode != ''">
                        #{upperCustomerCode},
                    </if>
                    <if test="targeSiteCode !=null and targeSiteCode != ''">
                        #{targeSiteCode},
                    </if>
                    <if test="ifUpStairs !=null and ifUpStairs != ''">
                        #{ifUpStairs},
                    </if>
                    <if test="installFlag != null">
                        #{installFlag},
                    </if>
                    <if test="targetCustomerCode !=null and targetCustomerCode != ''">
                        #{targetCustomerCode},
                    </if>
                    <if test="taskStatus != null">
                        #{taskStatus},
                    </if>
                    <if test="qzWarehAreaFlag !=null and qzWarehAreaFlag != ''">
                        #{qzWarehAreaFlag},
                    </if>
                    <if test="grayFlag !=null and grayFlag != ''">
                        #{grayFlag},
                    </if>
                    <if test="agingProductCode !=null and agingProductCode != ''">
                        #{agingProductCode},
                    </if>
                    <if test="receiverSaleStype !=null and receiverSaleStype != ''">
                        #{receiverSaleStype},
                    </if>
                    <if test="nextDistributionWhCode !=null and nextDistributionWhCode != ''">
                        #{nextDistributionWhCode},
                    </if>
                    <if test="orderConfirmTime != null">
                        #{orderConfirmTime},
                    </if>
                    <if test="orderSiteName !=null and orderSiteName != ''">
                        #{orderSiteName},
                    </if>
                    <if test="totalPkgQty != null">
                        #{totalPkgQty},
                    </if>
                    <if test="relationOrderNo !=null and relationOrderNo != ''">
                        #{relationOrderNo},
                    </if>
                    <if test="invoiceUnitName !=null and invoiceUnitName != ''">
                        #{invoiceUnitName},
                    </if>
                    <if test="expectArriveStartTime != null">
                        #{expectArriveStartTime},
                    </if>
                    <if test="targeWhCode !=null and targeWhCode != ''">
                        #{targeWhCode},
                    </if>
                    <if test="lineAging != null">
                        #{lineAging},
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        #{customerName},
                    </if>
                    <if test="excuteStatus != null">
                        #{excuteStatus},
                    </if>
                    <if test="upperReceiverCode !=null and upperReceiverCode != ''">
                        #{upperReceiverCode},
                    </if>
                    <if test="lineCost != null">
                        #{lineCost},
                    </if>
                    <if test="cloudWhFlag !=null and cloudWhFlag != ''">
                        #{cloudWhFlag},
                    </if>
                    <if test="upperReferenceId !=null and upperReferenceId != ''">
                        #{upperReferenceId},
                    </if>
                    <if test="freightBasis !=null and freightBasis != ''">
                        #{freightBasis},
                    </if>
                    <if test="carrierCode !=null and carrierCode != ''">
                        #{carrierCode},
                    </if>
                    <if test="deliverypayType != null">
                        #{deliverypayType},
                    </if>
                    <if test="totalNetWeight != null">
                        #{totalNetWeight},
                    </if>
                    <if test="zoneCode !=null and zoneCode != ''">
                        #{zoneCode},
                    </if>
                    <if test="planOrderFlag != null">
                        #{planOrderFlag},
                    </if>
                    <if test="distributionWhName !=null and distributionWhName != ''">
                        #{distributionWhName},
                    </if>
                    <if test="upstreamDocType !=null and upstreamDocType != ''">
                        #{upstreamDocType},
                    </if>
                    <if test="loadType !=null and loadType != ''">
                        #{loadType},
                    </if>
                    <if test="unitAreaName !=null and unitAreaName != ''">
                        #{unitAreaName},
                    </if>
                    <if test="sourceSystem !=null and sourceSystem != ''">
                        #{sourceSystem},
                    </if>
                    <if test="distributionFlag != null">
                        #{distributionFlag},
                    </if>
                    <if test="contractNo !=null and contractNo != ''">
                        #{contractNo},
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        #{customerCode},
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        #{siteName},
                    </if>
                    <if test="lineName !=null and lineName != ''">
                        #{lineName},
                    </if>
                    <if test="supplierCode !=null and supplierCode != ''">
                        #{supplierCode},
                    </if>
                    <if test="orderValue != null">
                        #{orderValue},
                    </if>
                    <if test="equipmentType !=null and equipmentType != ''">
                        #{equipmentType},
                    </if>
                    <if test="lineMileage != null">
                        #{lineMileage},
                    </if>
                    <if test="specCarrierFlag !=null and specCarrierFlag != ''">
                        #{specCarrierFlag},
                    </if>
                    <if test="upperOrderType !=null and upperOrderType != ''">
                        #{upperOrderType},
                    </if>
                    <if test="returnReason !=null and returnReason != ''">
                        #{returnReason},
                    </if>
                    <if test="taskNo !=null and taskNo != ''">
                        #{taskNo},
                    </if>
                    <if test="projectClassify !=null and projectClassify != ''">
                        #{projectClassify},
                    </if>
                    <if test="orderEndTime != null">
                        #{orderEndTime},
                    </if>
                    <if test="cancelReason !=null and cancelReason != ''">
                        #{cancelReason},
                    </if>
                    <if test="specimenType !=null and specimenType != ''">
                        #{specimenType},
                    </if>
                    <if test="inWhTime != null">
                        #{inWhTime},
                    </if>
                    <if test="workOrderNo !=null and workOrderNo != ''">
                        #{workOrderNo},
                    </if>
                    <if test="oaid !=null and oaid != ''">
                        #{oaid},
                    </if>
                    <if test="businessMode !=null and businessMode != ''">
                        #{businessMode},
                    </if>
                    <if test="dispatchNo !=null and dispatchNo != ''">
                        #{dispatchNo},
                    </if>
                    <if test="expectInstallStartTime != null">
                        #{expectInstallStartTime},
                    </if>
                    <if test="collectionAmount != null">
                        #{collectionAmount},
                    </if>
                    <if test="thirdFlag !=null and thirdFlag != ''">
                        #{thirdFlag},
                    </if>
                    <if test="printBarcode != null">
                        #{printBarcode},
                    </if>
                    <if test="createUserCode != null">
                        #{createUserCode},
                    </if>
                    <if test="expectInstallType !=null and expectInstallType != ''">
                        #{expectInstallType},
                    </if>
                    <if test="arriveAppointmentRequireFlag != null">
                        #{arriveAppointmentRequireFlag},
                    </if>
                    <if test="customerTotalQty != null">
                        #{customerTotalQty},
                    </if>
                    <if test="expectArriveEndTime != null">
                        #{expectArriveEndTime},
                    </if>
                    <if test="operationsCenterCode !=null and operationsCenterCode != ''">
                        #{operationsCenterCode},
                    </if>
                    <if test="upperWhName !=null and upperWhName != ''">
                        #{upperWhName},
                    </if>
                    <if test="upperSenderName !=null and upperSenderName != ''">
                        #{upperSenderName},
                    </if>
                    <if test="deliveryCycle !=null and deliveryCycle != ''">
                        #{deliveryCycle},
                    </if>
                    <if test="businessCategory !=null and businessCategory != ''">
                        #{businessCategory},
                    </if>
                    <if test="distributionLastFlag != null">
                        #{distributionLastFlag},
                    </if>
                    <if test="customerOrderNo !=null and customerOrderNo != ''">
                        #{customerOrderNo},
                    </if>
                    <if test="customerAgingCode !=null and customerAgingCode != ''">
                        #{customerAgingCode},
                    </if>
                    <if test="payDate != null">
                        #{payDate},
                    </if>
                    <if test="totalVolume != null">
                        #{totalVolume},
                    </if>
                    <if test="serviceType !=null and serviceType != ''">
                        #{serviceType},
                    </if>
                    <if test="totalQty != null">
                        #{totalQty},
                    </if>
                    <if test="payTime != null">
                        #{payTime},
                    </if>
                    <if test="stationType !=null and stationType != ''">
                        #{stationType},
                    </if>
                    <if test="orderSiteCode !=null and orderSiteCode != ''">
                        #{orderSiteCode},
                    </if>
                    <if test="distributionNum != null">
                        #{distributionNum},
                    </if>
                    <if test="consolidationNum != null">
                        #{consolidationNum},
                    </if>
                    <if test="parentOrderNo !=null and parentOrderNo != ''">
                        #{parentOrderNo},
                    </if>
                    <if test="distributionSiteCode !=null and distributionSiteCode != ''">
                        #{distributionSiteCode},
                    </if>
                    <if test="buyerRemark !=null and buyerRemark != ''">
                        #{buyerRemark},
                    </if>
                    <if test="isBack != null">
                        #{isBack},
                    </if>
                    <if test="driverQueueCode !=null and driverQueueCode != ''">
                        #{driverQueueCode},
                    </if>
                    <if test="orderTime != null">
                        #{orderTime},
                    </if>
                    <if test="invoiceUnitCode !=null and invoiceUnitCode != ''">
                        #{invoiceUnitCode},
                    </if>
                    <if test="jpOrderNo !=null and jpOrderNo != ''">
                        #{jpOrderNo},
                    </if>
                    <if test="orderRpFlag !=null and orderRpFlag != ''">
                        #{orderRpFlag},
                    </if>
                    <if test="equipmentName !=null and equipmentName != ''">
                        #{equipmentName},
                    </if>
                    <if test="shopId !=null and shopId != ''">
                        #{shopId},
                    </if>
                    <if test="shopGuideTel !=null and shopGuideTel != ''">
                        #{shopGuideTel},
                    </if>
                    <if test="expectInstallEndTime != null">
                        #{expectInstallEndTime},
                    </if>
                    <if test="mileage != null">
                        #{mileage},
                    </if>
                    <if test="companyCode !=null and companyCode != ''">
                        #{companyCode},
                    </if>
                    <if test="orderWhName !=null and orderWhName != ''">
                        #{orderWhName},
                    </if>
                    <if test="invoiceFlag !=null and invoiceFlag != ''">
                        #{invoiceFlag},
                    </if>
                    <if test="appointmentType !=null and appointmentType != ''">
                        #{appointmentType},
                    </if>
                    <if test="orderNo !=null and orderNo != ''">
                        #{orderNo},
                    </if>
                    <if test="customerGroup !=null and customerGroup != ''">
                        #{customerGroup},
                    </if>
                    <if test="cnDispatch != null">
                        #{cnDispatch},
                    </if>
                    <if test="collectionFlag !=null and collectionFlag != ''">
                        #{collectionFlag},
                    </if>
                    <if test="printPriceFlag != null">
                        #{printPriceFlag},
                    </if>
                    <if test="shopGuideName !=null">
                        #{shopGuideName},
                    </if>
                    <if test="orderSourcePlatform !=null and orderSourcePlatform != ''">
                        #{orderSourcePlatform},
                    </if>
                    <if test="outsourceFlag !=null and outsourceFlag != ''">
                        #{outsourceFlag},
                    </if>
                    <if test="serviceOrderNo !=null and serviceOrderNo != ''">
                        #{serviceOrderNo},
                    </if>
                    <if test="updateUserCode != null">
                        #{updateUserCode},
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        #{whCode},
                    </if>
                    <if test="setupType !=null and setupType != ''">
                        #{setupType},
                    </if>
                    <if test="emergenceFlag !=null and emergenceFlag != ''">
                        #{emergenceFlag},
                    </if>
                    <if test="upperAgingCode !=null and upperAgingCode != ''">
                        #{upperAgingCode},
                    </if>
                    <if test="appointOrderNo !=null and appointOrderNo != ''">
                        #{appointOrderNo},
                    </if>
                    <if test="transferFlag != null">
                        #{transferFlag},
                    </if>
                    <if test="orderStatus != null">
                        #{orderStatus},
                    </if>
                    <if test="shopName !=null and shopName != ''">
                        #{shopName},
                    </if>
                    <if test="remark != null">
                        #{remark},
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        #{tenantCode},
                    </if>
                    <if test="pickFlag !=null and pickFlag != ''">
                        #{pickFlag},
                    </if>
                    <if test="distributionStationType !=null and distributionStationType != ''">
                        #{distributionStationType},
                    </if>
                    <if test="appointmentReason !=null and appointmentReason != ''">
                        #{appointmentReason},
                    </if>
                    <if test="printNotaxPriceFlag != null">
                        #{printNotaxPriceFlag},
                    </if>
                    <if test="consolidationOrderNo !=null and consolidationOrderNo != ''">
                        #{consolidationOrderNo},
                    </if>
                    <if test="upperReceiverName !=null and upperReceiverName != ''">
                        #{upperReceiverName},
                    </if>
                    <if test="lineCode !=null and lineCode != ''">
                        #{lineCode},
                    </if>
                    <if test="taskType !=null and taskType != ''">
                        #{taskType},
                    </if>
                    <if test="inOutType !=null and inOutType != ''">
                        #{inOutType},
                    </if>
                    <if test="pledgeType !=null and pledgeType != ''">
                        #{pledgeType},
                    </if>
                    <if test="logisticMode !=null and logisticMode != ''">
                        #{logisticMode},
                    </if>
                    <if test="scPosFlag != null">
                        #{scPosFlag},
                    </if>
                    <if test="upperSenderCode !=null and upperSenderCode != ''">
                        #{upperSenderCode},
                    </if>
                    <if test="appointmentTime != null">
                        #{appointmentTime},
                    </if>
                    <if test="whSystem !=null and whSystem != ''">
                        #{whSystem},
                    </if>
                    <if test="distributionWhCode !=null and distributionWhCode != ''">
                        #{distributionWhCode},
                    </if>
                    <if test="transportType !=null and transportType != ''">
                        #{transportType},
                    </if>
                    <if test="withdrawalReason !=null and withdrawalReason != ''">
                        #{withdrawalReason},
                    </if>
                    <if test="zoneName !=null and zoneName != ''">
                        #{zoneName},
                    </if>
                    <if test="upperSourceCustomerCode !=null and upperSourceCustomerCode != ''">
                        #{upperSourceCustomerCode},
                    </if>
                    <if test="professionalCompany !=null and professionalCompany != ''">
                        #{professionalCompany},
                    </if>
                    <if test="orderSource !=null and orderSource != ''">
                        #{orderSource},
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        #{siteCode},
                    </if>
                    <if test="upperWhCode !=null and upperWhCode != ''">
                        #{upperWhCode},
                    </if>
                    <if test="deliveryType !=null and deliveryType != ''">
                        #{deliveryType},
                    </if>
                    <if test="entityId != null">
                        #{entityId},
                    </if>
                    <if test="distributionSiteName !=null and distributionSiteName != ''">
                        #{distributionSiteName},
                    </if>
                    <if test="holdFlag != null">
                        #{holdFlag},
                    </if>
                    <if test="originOrderNo !=null and originOrderNo != ''">
                        #{originOrderNo},
                    </if>
                    <if test="upperCustomerName !=null and upperCustomerName != ''">
                        #{upperCustomerName},
                    </if>
                    <if test="platformOrderNo !=null and platformOrderNo != ''">
                        #{platformOrderNo},
                    </if>
                    <if test="joinType !=null and joinType != ''">
                        #{joinType},
                    </if>
                    <if test="consigneeTimeTo != null">
                        #{consigneeTimeTo},
                    </if>
                    <if test="c2mType !=null and c2mType != ''">
                        #{c2mType},
                    </if>
                    <if test="businessType !=null and businessType != ''">
                        #{businessType},
                    </if>
                    <if test="tcFlag !=null and tcFlag != ''">
                        #{tcFlag},
                    </if>
                    <if test="nextDistributionSiteCode !=null and nextDistributionSiteCode != ''">
                        #{nextDistributionSiteCode},
                    </if>
                    <if test="waybillNo !=null and waybillNo != ''">
                        #{waybillNo},
                    </if>
                    <if test="orderStartTime != null">
                        #{orderStartTime},
                    </if>
                    <if test="transportSystem != null">
                        #{transportSystem},
                    </if>
                    <if test="transportOrderNo != null">
                        #{transportOrderNo},
                    </if>
                    <if test="trainNumber != null and trainNumber != ''">
                        #{trainNumber},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                `order_type`,
                `require_out_time`,
                `consignee_time_from`,
                `company_name`,
                `order_wh_code`,
                `main_waybill_no`,
                `total_gross_weight`,
                `delivered_verify_flag`,
                `wh_name`,
                `pay_type`,
                `upper_customer_code`,
                `targe_site_code`,
                `if_up_stairs`,
                `install_flag`,
                `target_customer_code`,
                `task_status`,
                `qz_wareh_area_flag`,
                `gray_flag`,
                `aging_product_code`,
                `receiver_sale_stype`,
                `next_distribution_wh_code`,
                `order_confirm_time`,
                `order_site_name`,
                `total_pkg_qty`,
                `relation_order_no`,
                `invoice_unit_name`,
                `expect_arrive_start_time`,
                `targe_wh_code`,
                `line_aging`,
                `customer_name`,
                `excute_status`,
                `upper_receiver_code`,
                `line_cost`,
                `cloud_wh_flag`,
                `upper_reference_id`,
                `freight_basis`,
                `carrier_code`,
                `deliverypay_type`,
                `total_net_weight`,
                `zone_code`,
                `plan_order_flag`,
                `distribution_wh_name`,
                `upstream_doc_type`,
                `load_type`,
                `unit_area_name`,
                `source_system`,
                `distribution_flag`,
                `contract_no`,
                `customer_code`,
                `site_name`,
                `line_name`,
                `supplier_code`,
                `order_value`,
                `equipment_type`,
                `line_mileage`,
                `spec_carrier_flag`,
                `upper_order_type`,
                `return_reason`,
                `task_no`,
                `project_classify`,
                `order_end_time`,
                `cancel_reason`,
                `specimen_type`,
                `in_wh_time`,
                `work_order_no`,
                `oaid`,
                `business_mode`,
                `dispatch_no`,
                `expect_install_start_time`,
                `collection_amount`,
                `third_flag`,
                `print_barcode`,
                `create_user_code`,
                `expect_install_type`,
                `arrive_appointment_require_flag`,
                `customer_total_qty`,
                `expect_arrive_end_time`,
                `operations_center_code`,
                `upper_wh_name`,
                `upper_sender_name`,
                `delivery_cycle`,
                `business_category`,
                `distribution_last_flag`,
                `customer_order_no`,
                `customer_aging_code`,
                `pay_date`,
                `total_volume`,
                `service_type`,
                `total_qty`,
                `pay_time`,
                `station_type`,
                `order_site_code`,
                `distribution_num`,
                `consolidation_num`,
                `parent_order_no`,
                `distribution_site_code`,
                `buyer_remark`,
                `is_back`,
                `driver_queue_code`,
                `order_time`,
                `invoice_unit_code`,
                `jp_order_no`,
                `order_rp_flag`,
                `equipment_name`,
                `shop_id`,
                `shop_guide_tel`,
                `expect_install_end_time`,
                `mileage`,
                `company_code`,
                `order_wh_name`,
                `invoice_flag`,
                `appointment_type`,
                `order_no`,
                `customer_group`,
                `cn_dispatch`,
                `collection_flag`,
                `print_price_flag`,
                `shop_guide_name`,
                `order_source_platform`,
                `outsource_flag`,
                `service_order_no`,
                `update_user_code`,
                `wh_code`,
                `setup_type`,
                `emergence_flag`,
                `upper_aging_code`,
                `appoint_order_no`,
                `transfer_flag`,
                `order_status`,
                `shop_name`,
                `remark`,
                `tenant_code`,
                `pick_flag`,
                `distribution_station_type`,
                `appointment_reason`,
                `print_notax_price_flag`,
                `consolidation_order_no`,
                `upper_receiver_name`,
                `line_code`,
                `task_type`,
                `in_out_type`,
                `pledge_type`,
                `logistic_mode`,
                `sc_pos_flag`,
                `upper_sender_code`,
                `appointment_time`,
                `wh_system`,
                `distribution_wh_code`,
                `transport_type`,
                `withdrawal_reason`,
                `zone_name`,
                `upper_source_customer_code`,
                `professional_company`,
                `order_source`,
                `site_code`,
                `upper_wh_code`,
                `delivery_type`,
                `entity_id`,
                `distribution_site_name`,
                `hold_flag`,
                `origin_order_no`,
                `upper_customer_name`,
                `platform_order_no`,
                `join_type`,
                `consignee_time_to`,
                `c2m_type`,
                `business_type`,
                `tc_flag`,
                `next_distribution_site_code`,
                `waybill_no`,
                `order_start_time`,
                `transport_system`,
                `transport_order_no`,
                `train_number`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.orderType},
                #{item.requireOutTime},
                #{item.consigneeTimeFrom},
                #{item.companyName},
                #{item.orderWhCode},
                #{item.mainWaybillNo},
                #{item.totalGrossWeight},
                #{item.deliveredVerifyFlag},
                #{item.whName},
                #{item.payType},
                #{item.upperCustomerCode},
                #{item.targeSiteCode},
                #{item.ifUpStairs},
                #{item.installFlag},
                #{item.targetCustomerCode},
                #{item.taskStatus},
                #{item.qzWarehAreaFlag},
                #{item.grayFlag},
                #{item.agingProductCode},
                #{item.receiverSaleStype},
                #{item.nextDistributionWhCode},
                #{item.orderConfirmTime},
                #{item.orderSiteName},
                #{item.totalPkgQty},
                #{item.relationOrderNo},
                #{item.invoiceUnitName},
                #{item.expectArriveStartTime},
                #{item.targeWhCode},
                #{item.lineAging},
                #{item.customerName},
                #{item.excuteStatus},
                #{item.upperReceiverCode},
                #{item.lineCost},
                #{item.cloudWhFlag},
                #{item.upperReferenceId},
                #{item.freightBasis},
                #{item.carrierCode},
                #{item.deliverypayType},
                #{item.totalNetWeight},
                #{item.zoneCode},
                #{item.planOrderFlag},
                #{item.distributionWhName},
                #{item.upstreamDocType},
                #{item.loadType},
                #{item.unitAreaName},
                #{item.sourceSystem},
                #{item.distributionFlag},
                #{item.contractNo},
                #{item.customerCode},
                #{item.siteName},
                #{item.lineName},
                #{item.supplierCode},
                #{item.orderValue},
                #{item.equipmentType},
                #{item.lineMileage},
                #{item.specCarrierFlag},
                #{item.upperOrderType},
                #{item.returnReason},
                #{item.taskNo},
                #{item.projectClassify},
                #{item.orderEndTime},
                #{item.cancelReason},
                #{item.specimenType},
                #{item.inWhTime},
                #{item.workOrderNo},
                null,
                #{item.businessMode},
                #{item.dispatchNo},
                #{item.expectInstallStartTime},
                #{item.collectionAmount},
                #{item.thirdFlag},
                #{item.printBarcode},
                #{item.createUserCode},
                #{item.expectInstallType},
                #{item.arriveAppointmentRequireFlag},
                #{item.customerTotalQty},
                #{item.expectArriveEndTime},
                #{item.operationsCenterCode},
                #{item.upperWhName},
                #{item.upperSenderName},
                #{item.deliveryCycle},
                #{item.businessCategory},
                #{item.distributionLastFlag},
                #{item.customerOrderNo},
                #{item.customerAgingCode},
                #{item.payDate},
                #{item.totalVolume},
                #{item.serviceType},
                #{item.totalQty},
                #{item.payTime},
                #{item.stationType},
                #{item.orderSiteCode},
                #{item.distributionNum},
                #{item.consolidationNum},
                #{item.parentOrderNo},
                #{item.distributionSiteCode},
                #{item.buyerRemark},
                #{item.isBack},
                #{item.driverQueueCode},
                #{item.orderTime},
                #{item.invoiceUnitCode},
                #{item.jpOrderNo},
                #{item.orderRpFlag},
                #{item.equipmentName},
                #{item.shopId},
                #{item.shopGuideTel},
                #{item.expectInstallEndTime},
                #{item.mileage},
                #{item.companyCode},
                #{item.orderWhName},
                #{item.invoiceFlag},
                #{item.appointmentType},
                #{item.orderNo},
                #{item.customerGroup},
                #{item.cnDispatch},
                #{item.collectionFlag},
                #{item.printPriceFlag},
                #{item.shopGuideName},
                #{item.orderSourcePlatform},
                #{item.outsourceFlag},
                #{item.serviceOrderNo},
                #{item.updateUserCode},
                #{item.whCode},
                #{item.setupType},
                #{item.emergenceFlag},
                #{item.upperAgingCode},
                #{item.appointOrderNo},
                #{item.transferFlag},
                #{item.orderStatus},
                #{item.shopName},
                #{item.remark},
                #{item.tenantCode},
                #{item.pickFlag},
                #{item.distributionStationType},
                #{item.appointmentReason},
                #{item.printNotaxPriceFlag},
                #{item.consolidationOrderNo},
                #{item.upperReceiverName},
                #{item.lineCode},
                #{item.taskType},
                #{item.inOutType},
                #{item.pledgeType},
                #{item.logisticMode},
                #{item.scPosFlag},
                #{item.upperSenderCode},
                #{item.appointmentTime},
                #{item.whSystem},
                #{item.distributionWhCode},
                #{item.transportType},
                #{item.withdrawalReason},
                #{item.zoneName},
                #{item.upperSourceCustomerCode},
                #{item.professionalCompany},
                #{item.orderSource},
                #{item.siteCode},
                #{item.upperWhCode},
                #{item.deliveryType},
                #{item.entityId},
                #{item.distributionSiteName},
                #{item.holdFlag},
                #{item.originOrderNo},
                #{item.upperCustomerName},
                #{item.platformOrderNo},
                #{item.joinType},
                #{item.consigneeTimeTo},
                #{item.c2mType},
                #{item.businessType},
                #{item.tcFlag},
                #{item.nextDistributionSiteCode},
                #{item.waybillNo},
                #{item.orderStartTime},
                #{item.transportSystem},
                #{item.transportOrderNo},
                #{item.trainNumber},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        task
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.orderType !=null and item.orderType != ''">
                     `order_type`  = #{item.orderType},
                 </if>
                 <if test="item.requireOutTime != null">
                     `require_out_time`  = #{item.requireOutTime},
                 </if>
                 <if test="item.consigneeTimeFrom != null">
                     `consignee_time_from`  = #{item.consigneeTimeFrom},
                 </if>
                 <if test="item.companyName !=null and item.companyName != ''">
                     `company_name`  = #{item.companyName},
                 </if>
                 <if test="item.orderWhCode !=null and item.orderWhCode != ''">
                     `order_wh_code`  = #{item.orderWhCode},
                 </if>
                 <if test="item.mainWaybillNo !=null and item.mainWaybillNo != ''">
                     `main_waybill_no`  = #{item.mainWaybillNo},
                 </if>
                 <if test="item.totalGrossWeight != null">
                     `total_gross_weight`  = #{item.totalGrossWeight},
                 </if>
                 <if test="item.deliveredVerifyFlag != null">
                     `delivered_verify_flag`  = #{item.deliveredVerifyFlag},
                 </if>
                 <if test="item.whName !=null and item.whName != ''">
                     `wh_name`  = #{item.whName},
                 </if>
                 <if test="item.payType !=null and item.payType != ''">
                     `pay_type`  = #{item.payType},
                 </if>
                 <if test="item.upperCustomerCode !=null and item.upperCustomerCode != ''">
                     `upper_customer_code`  = #{item.upperCustomerCode},
                 </if>
                 <if test="item.targeSiteCode !=null and item.targeSiteCode != ''">
                     `targe_site_code`  = #{item.targeSiteCode},
                 </if>
                 <if test="item.ifUpStairs !=null and item.ifUpStairs != ''">
                     `if_up_stairs`  = #{item.ifUpStairs},
                 </if>
                 <if test="item.installFlag != null">
                     `install_flag`  = #{item.installFlag},
                 </if>
                 <if test="item.targetCustomerCode !=null and item.targetCustomerCode != ''">
                     `target_customer_code`  = #{item.targetCustomerCode},
                 </if>
                 <if test="item.taskStatus != null">
                     `task_status`  = #{item.taskStatus},
                 </if>
                 <if test="item.qzWarehAreaFlag !=null and item.qzWarehAreaFlag != ''">
                     `qz_wareh_area_flag`  = #{item.qzWarehAreaFlag},
                 </if>
                 <if test="item.grayFlag !=null and item.grayFlag != ''">
                     `gray_flag`  = #{item.grayFlag},
                 </if>
                 <if test="item.agingProductCode !=null and item.agingProductCode != ''">
                     `aging_product_code`  = #{item.agingProductCode},
                 </if>
                 <if test="item.receiverSaleStype !=null and item.receiverSaleStype != ''">
                     `receiver_sale_stype`  = #{item.receiverSaleStype},
                 </if>
                 <if test="item.nextDistributionWhCode !=null and item.nextDistributionWhCode != ''">
                     `next_distribution_wh_code`  = #{item.nextDistributionWhCode},
                 </if>
                 <if test="item.orderConfirmTime != null">
                     `order_confirm_time`  = #{item.orderConfirmTime},
                 </if>
                 <if test="item.orderSiteName !=null and item.orderSiteName != ''">
                     `order_site_name`  = #{item.orderSiteName},
                 </if>
                 <if test="item.totalPkgQty != null">
                     `total_pkg_qty`  = #{item.totalPkgQty},
                 </if>
                 <if test="item.relationOrderNo !=null and item.relationOrderNo != ''">
                     `relation_order_no`  = #{item.relationOrderNo},
                 </if>
                 <if test="item.invoiceUnitName !=null and item.invoiceUnitName != ''">
                     `invoice_unit_name`  = #{item.invoiceUnitName},
                 </if>
                 <if test="item.expectArriveStartTime != null">
                     `expect_arrive_start_time`  = #{item.expectArriveStartTime},
                 </if>
                 <if test="item.targeWhCode !=null and item.targeWhCode != ''">
                     `targe_wh_code`  = #{item.targeWhCode},
                 </if>
                 <if test="item.lineAging != null">
                     `line_aging`  = #{item.lineAging},
                 </if>
                 <if test="item.customerName !=null and item.customerName != ''">
                     `customer_name`  = #{item.customerName},
                 </if>
                 <if test="item.excuteStatus != null">
                     `excute_status`  = #{item.excuteStatus},
                 </if>
                 <if test="item.upperReceiverCode !=null and item.upperReceiverCode != ''">
                     `upper_receiver_code`  = #{item.upperReceiverCode},
                 </if>
                 <if test="item.lineCost != null">
                     `line_cost`  = #{item.lineCost},
                 </if>
                 <if test="item.cloudWhFlag !=null and item.cloudWhFlag != ''">
                     `cloud_wh_flag`  = #{item.cloudWhFlag},
                 </if>
                 <if test="item.upperReferenceId !=null and item.upperReferenceId != ''">
                     `upper_reference_id`  = #{item.upperReferenceId},
                 </if>
                 <if test="item.freightBasis !=null and item.freightBasis != ''">
                     `freight_basis`  = #{item.freightBasis},
                 </if>
                 <if test="item.carrierCode !=null and item.carrierCode != ''">
                     `carrier_code`  = #{item.carrierCode},
                 </if>
                 <if test="item.deliverypayType != null">
                     `deliverypay_type`  = #{item.deliverypayType},
                 </if>
                 <if test="item.totalNetWeight != null">
                     `total_net_weight`  = #{item.totalNetWeight},
                 </if>
                 <if test="item.zoneCode !=null and item.zoneCode != ''">
                     `zone_code`  = #{item.zoneCode},
                 </if>
                 <if test="item.planOrderFlag != null">
                     `plan_order_flag`  = #{item.planOrderFlag},
                 </if>
                 <if test="item.distributionWhName !=null and item.distributionWhName != ''">
                     `distribution_wh_name`  = #{item.distributionWhName},
                 </if>
                 <if test="item.upstreamDocType !=null and item.upstreamDocType != ''">
                     `upstream_doc_type`  = #{item.upstreamDocType},
                 </if>
                 <if test="item.loadType !=null and item.loadType != ''">
                     `load_type`  = #{item.loadType},
                 </if>
                 <if test="item.unitAreaName !=null and item.unitAreaName != ''">
                     `unit_area_name`  = #{item.unitAreaName},
                 </if>
                 <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                     `source_system`  = #{item.sourceSystem},
                 </if>
                 <if test="item.distributionFlag != null">
                     `distribution_flag`  = #{item.distributionFlag},
                 </if>
                 <if test="item.contractNo !=null and item.contractNo != ''">
                     `contract_no`  = #{item.contractNo},
                 </if>
                 <if test="item.customerCode !=null and item.customerCode != ''">
                     `customer_code`  = #{item.customerCode},
                 </if>
                 <if test="item.siteName !=null and item.siteName != ''">
                     `site_name`  = #{item.siteName},
                 </if>
                 <if test="item.lineName !=null and item.lineName != ''">
                     `line_name`  = #{item.lineName},
                 </if>
                 <if test="item.supplierCode !=null and item.supplierCode != ''">
                     `supplier_code`  = #{item.supplierCode},
                 </if>
                 <if test="item.orderValue != null">
                     `order_value`  = #{item.orderValue},
                 </if>
                 <if test="item.equipmentType !=null and item.equipmentType != ''">
                     `equipment_type`  = #{item.equipmentType},
                 </if>
                 <if test="item.lineMileage != null">
                     `line_mileage`  = #{item.lineMileage},
                 </if>
                 <if test="item.specCarrierFlag !=null and item.specCarrierFlag != ''">
                     `spec_carrier_flag`  = #{item.specCarrierFlag},
                 </if>
                 <if test="item.upperOrderType !=null and item.upperOrderType != ''">
                     `upper_order_type`  = #{item.upperOrderType},
                 </if>
                 <if test="item.returnReason !=null and item.returnReason != ''">
                     `return_reason`  = #{item.returnReason},
                 </if>
                 <if test="item.taskNo !=null and item.taskNo != ''">
                     `task_no`  = #{item.taskNo},
                 </if>
                 <if test="item.projectClassify !=null and item.projectClassify != ''">
                     `project_classify`  = #{item.projectClassify},
                 </if>
                 <if test="item.orderEndTime != null">
                     `order_end_time`  = #{item.orderEndTime},
                 </if>
                 <if test="item.cancelReason !=null and item.cancelReason != ''">
                     `cancel_reason`  = #{item.cancelReason},
                 </if>
                 <if test="item.specimenType !=null and item.specimenType != ''">
                     `specimen_type`  = #{item.specimenType},
                 </if>
                 <if test="item.inWhTime != null">
                     `in_wh_time`  = #{item.inWhTime},
                 </if>
                 <if test="item.workOrderNo !=null and item.workOrderNo != ''">
                     `work_order_no`  = #{item.workOrderNo},
                 </if>
                 <if test="item.oaid !=null and item.oaid != ''">
                     `oaid`  = null,
                 </if>
                 <if test="item.businessMode !=null and item.businessMode != ''">
                     `business_mode`  = #{item.businessMode},
                 </if>
                 <if test="item.dispatchNo !=null and item.dispatchNo != ''">
                     `dispatch_no`  = #{item.dispatchNo},
                 </if>
                 <if test="item.expectInstallStartTime != null">
                     `expect_install_start_time`  = #{item.expectInstallStartTime},
                 </if>
                 <if test="item.collectionAmount != null">
                     `collection_amount`  = #{item.collectionAmount},
                 </if>
                 <if test="item.thirdFlag !=null and item.thirdFlag != ''">
                     `third_flag`  = #{item.thirdFlag},
                 </if>
                 <if test="item.printBarcode != null">
                     `print_barcode`  = #{item.printBarcode},
                 </if>
                 <if test="item.expectInstallType !=null and item.expectInstallType != ''">
                     `expect_install_type`  = #{item.expectInstallType},
                 </if>
                 <if test="item.arriveAppointmentRequireFlag != null">
                     `arrive_appointment_require_flag`  = #{item.arriveAppointmentRequireFlag},
                 </if>
                 <if test="item.customerTotalQty != null">
                     `customer_total_qty`  = #{item.customerTotalQty},
                 </if>
                 <if test="item.expectArriveEndTime != null">
                     `expect_arrive_end_time`  = #{item.expectArriveEndTime},
                 </if>
                 <if test="item.operationsCenterCode !=null and item.operationsCenterCode != ''">
                     `operations_center_code`  = #{item.operationsCenterCode},
                 </if>
                 <if test="item.upperWhName !=null and item.upperWhName != ''">
                     `upper_wh_name`  = #{item.upperWhName},
                 </if>
                 <if test="item.upperSenderName !=null and item.upperSenderName != ''">
                     `upper_sender_name`  = #{item.upperSenderName},
                 </if>
                 <if test="item.deliveryCycle !=null and item.deliveryCycle != ''">
                     `delivery_cycle`  = #{item.deliveryCycle},
                 </if>
                 <if test="item.businessCategory !=null and item.businessCategory != ''">
                     `business_category`  = #{item.businessCategory},
                 </if>
                 <if test="item.distributionLastFlag != null">
                     `distribution_last_flag`  = #{item.distributionLastFlag},
                 </if>
                 <if test="item.customerOrderNo !=null and item.customerOrderNo != ''">
                     `customer_order_no`  = #{item.customerOrderNo},
                 </if>
                 <if test="item.customerAgingCode !=null and item.customerAgingCode != ''">
                     `customer_aging_code`  = #{item.customerAgingCode},
                 </if>
                 <if test="item.payDate != null">
                     `pay_date`  = #{item.payDate},
                 </if>
                 <if test="item.totalVolume != null">
                     `total_volume`  = #{item.totalVolume},
                 </if>
                 <if test="item.serviceType !=null and item.serviceType != ''">
                     `service_type`  = #{item.serviceType},
                 </if>
                 <if test="item.totalQty != null">
                     `total_qty`  = #{item.totalQty},
                 </if>
                 <if test="item.payTime != null">
                     `pay_time`  = #{item.payTime},
                 </if>
                 <if test="item.stationType !=null and item.stationType != ''">
                     `station_type`  = #{item.stationType},
                 </if>
                 <if test="item.orderSiteCode !=null and item.orderSiteCode != ''">
                     `order_site_code`  = #{item.orderSiteCode},
                 </if>
                 <if test="item.distributionNum != null">
                     `distribution_num`  = #{item.distributionNum},
                 </if>
                 <if test="item.consolidationNum != null">
                     `consolidation_num`  = #{item.consolidationNum},
                 </if>
                 <if test="item.parentOrderNo !=null and item.parentOrderNo != ''">
                     `parent_order_no`  = #{item.parentOrderNo},
                 </if>
                 <if test="item.distributionSiteCode !=null and item.distributionSiteCode != ''">
                     `distribution_site_code`  = #{item.distributionSiteCode},
                 </if>
                 <if test="item.buyerRemark !=null and item.buyerRemark != ''">
                     `buyer_remark`  = #{item.buyerRemark},
                 </if>
                 <if test="item.isBack != null">
                     `is_back`  = #{item.isBack},
                 </if>
                 <if test="item.driverQueueCode !=null and item.driverQueueCode != ''">
                     `driver_queue_code`  = #{item.driverQueueCode},
                 </if>
                 <if test="item.orderTime != null">
                     `order_time`  = #{item.orderTime},
                 </if>
                 <if test="item.invoiceUnitCode !=null and item.invoiceUnitCode != ''">
                     `invoice_unit_code`  = #{item.invoiceUnitCode},
                 </if>
                 <if test="item.jpOrderNo !=null and item.jpOrderNo != ''">
                     `jp_order_no`  = #{item.jpOrderNo},
                 </if>
                 <if test="item.orderRpFlag !=null and item.orderRpFlag != ''">
                     `order_rp_flag`  = #{item.orderRpFlag},
                 </if>
                 <if test="item.equipmentName !=null and item.equipmentName != ''">
                     `equipment_name`  = #{item.equipmentName},
                 </if>
                 <if test="item.shopId !=null and item.shopId != ''">
                     `shop_id`  = #{item.shopId},
                 </if>
                 <if test="item.shopGuideTel !=null and item.shopGuideTel != ''">
                     `shop_guide_tel`  = #{item.shopGuideTel},
                 </if>
                 <if test="item.expectInstallEndTime != null">
                     `expect_install_end_time`  = #{item.expectInstallEndTime},
                 </if>
                 <if test="item.mileage != null">
                     `mileage`  = #{item.mileage},
                 </if>
                 <if test="item.companyCode !=null and item.companyCode != ''">
                     `company_code`  = #{item.companyCode},
                 </if>
                 <if test="item.orderWhName !=null and item.orderWhName != ''">
                     `order_wh_name`  = #{item.orderWhName},
                 </if>
                 <if test="item.invoiceFlag !=null and item.invoiceFlag != ''">
                     `invoice_flag`  = #{item.invoiceFlag},
                 </if>
                 <if test="item.appointmentType !=null and item.appointmentType != ''">
                     `appointment_type`  = #{item.appointmentType},
                 </if>
                 <if test="item.orderNo !=null and item.orderNo != ''">
                     `order_no`  = #{item.orderNo},
                 </if>
                 <if test="item.customerGroup !=null and item.customerGroup != ''">
                     `customer_group`  = #{item.customerGroup},
                 </if>
                 <if test="item.cnDispatch != null">
                     `cn_dispatch`  = #{item.cnDispatch},
                 </if>
                 <if test="item.collectionFlag !=null and item.collectionFlag != ''">
                     `collection_flag`  = #{item.collectionFlag},
                 </if>
                 <if test="item.printPriceFlag != null">
                     `print_price_flag`  = #{item.printPriceFlag},
                 </if>
                 <if test="item.shopGuideName !=null and item.shopGuideName != ''">
                     `shop_guide_name`  = #{item.shopGuideName},
                 </if>
                 <if test="item.orderSourcePlatform !=null and item.orderSourcePlatform != ''">
                     `order_source_platform`  = #{item.orderSourcePlatform},
                 </if>
                 <if test="item.outsourceFlag !=null and item.outsourceFlag != ''">
                     `outsource_flag`  = #{item.outsourceFlag},
                 </if>
                 <if test="item.serviceOrderNo !=null and item.serviceOrderNo != ''">
                     `service_order_no`  = #{item.serviceOrderNo},
                 </if>
                 <if test="item.updateUserCode != null">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.whCode !=null and item.whCode != ''">
                     `wh_code`  = #{item.whCode},
                 </if>
                 <if test="item.setupType !=null and item.setupType != ''">
                     `setup_type`  = #{item.setupType},
                 </if>
                 <if test="item.emergenceFlag !=null and item.emergenceFlag != ''">
                     `emergence_flag`  = #{item.emergenceFlag},
                 </if>
                 <if test="item.upperAgingCode !=null and item.upperAgingCode != ''">
                     `upper_aging_code`  = #{item.upperAgingCode},
                 </if>
                 <if test="item.appointOrderNo !=null and item.appointOrderNo != ''">
                     `appoint_order_no`  = #{item.appointOrderNo},
                 </if>
                 <if test="item.transferFlag != null">
                     `transfer_flag`  = #{item.transferFlag},
                 </if>
                 <if test="item.orderStatus != null">
                     `order_status`  = #{item.orderStatus},
                 </if>
                 <if test="item.shopName !=null and item.shopName != ''">
                     `shop_name`  = #{item.shopName},
                 </if>
                 <if test="item.remark != null">
                     `remark`  = #{item.remark},
                 </if>
                 <if test="item.tenantCode !=null and item.tenantCode != ''">
                     `tenant_code`  = #{item.tenantCode},
                 </if>
                 <if test="item.pickFlag !=null and item.pickFlag != ''">
                     `pick_flag`  = #{item.pickFlag},
                 </if>
                 <if test="item.distributionStationType !=null and item.distributionStationType != ''">
                     `distribution_station_type`  = #{item.distributionStationType},
                 </if>
                 <if test="item.appointmentReason !=null and item.appointmentReason != ''">
                     `appointment_reason`  = #{item.appointmentReason},
                 </if>
                 <if test="item.printNotaxPriceFlag != null">
                     `print_notax_price_flag`  = #{item.printNotaxPriceFlag},
                 </if>
                 <if test="item.consolidationOrderNo !=null and item.consolidationOrderNo != ''">
                     `consolidation_order_no`  = #{item.consolidationOrderNo},
                 </if>
                 <if test="item.upperReceiverName !=null and item.upperReceiverName != ''">
                     `upper_receiver_name`  = #{item.upperReceiverName},
                 </if>
                 <if test="item.lineCode !=null and item.lineCode != ''">
                     `line_code`  = #{item.lineCode},
                 </if>
                 <if test="item.taskType !=null and item.taskType != ''">
                     `task_type`  = #{item.taskType},
                 </if>
                 <if test="item.inOutType !=null and item.inOutType != ''">
                     `in_out_type`  = #{item.inOutType},
                 </if>
                 <if test="item.pledgeType !=null and item.pledgeType != ''">
                     `pledge_type`  = #{item.pledgeType},
                 </if>
                 <if test="item.logisticMode !=null and item.logisticMode != ''">
                     `logistic_mode`  = #{item.logisticMode},
                 </if>
                 <if test="item.scPosFlag != null">
                     `sc_pos_flag`  = #{item.scPosFlag},
                 </if>
                 <if test="item.upperSenderCode !=null and item.upperSenderCode != ''">
                     `upper_sender_code`  = #{item.upperSenderCode},
                 </if>
                 <if test="item.appointmentTime != null">
                     `appointment_time`  = #{item.appointmentTime},
                 </if>
                 <if test="item.whSystem !=null and item.whSystem != ''">
                     `wh_system`  = #{item.whSystem},
                 </if>
                 <if test="item.distributionWhCode !=null and item.distributionWhCode != ''">
                     `distribution_wh_code`  = #{item.distributionWhCode},
                 </if>
                 <if test="item.transportType !=null and item.transportType != ''">
                     `transport_type`  = #{item.transportType},
                 </if>
                 <if test="item.withdrawalReason !=null and item.withdrawalReason != ''">
                     `withdrawal_reason`  = #{item.withdrawalReason},
                 </if>
                 <if test="item.zoneName !=null and item.zoneName != ''">
                     `zone_name`  = #{item.zoneName},
                 </if>
                 <if test="item.upperSourceCustomerCode !=null and item.upperSourceCustomerCode != ''">
                     `upper_source_customer_code`  = #{item.upperSourceCustomerCode},
                 </if>
                 <if test="item.professionalCompany !=null and item.professionalCompany != ''">
                     `professional_company`  = #{item.professionalCompany},
                 </if>
                 <if test="item.orderSource !=null and item.orderSource != ''">
                     `order_source`  = #{item.orderSource},
                 </if>
                 <if test="item.siteCode !=null and item.siteCode != ''">
                     `site_code`  = #{item.siteCode},
                 </if>
                 <if test="item.upperWhCode !=null and item.upperWhCode != ''">
                     `upper_wh_code`  = #{item.upperWhCode},
                 </if>
                 <if test="item.deliveryType !=null and item.deliveryType != ''">
                     `delivery_type`  = #{item.deliveryType},
                 </if>
                 <if test="item.entityId != null">
                     `entity_id`  = #{item.entityId},
                 </if>
                 <if test="item.distributionSiteName !=null and item.distributionSiteName != ''">
                     `distribution_site_name`  = #{item.distributionSiteName},
                 </if>
                 <if test="item.holdFlag != null">
                     `hold_flag`  = #{item.holdFlag},
                 </if>
                 <if test="item.originOrderNo !=null and item.originOrderNo != ''">
                     `origin_order_no`  = #{item.originOrderNo},
                 </if>
                 <if test="item.upperCustomerName !=null and item.upperCustomerName != ''">
                     `upper_customer_name`  = #{item.upperCustomerName},
                 </if>
                 <if test="item.platformOrderNo !=null and item.platformOrderNo != ''">
                     `platform_order_no`  = #{item.platformOrderNo},
                 </if>
                 <if test="item.joinType !=null and item.joinType != ''">
                     `join_type`  = #{item.joinType},
                 </if>
                 <if test="item.consigneeTimeTo != null">
                     `consignee_time_to`  = #{item.consigneeTimeTo},
                 </if>
                 <if test="item.c2mType !=null and item.c2mType != ''">
                     `c2m_type`  = #{item.c2mType},
                 </if>
                 <if test="item.businessType !=null and item.businessType != ''">
                     `business_type`  = #{item.businessType},
                 </if>
                 <if test="item.tcFlag !=null and item.tcFlag != ''">
                     `tc_flag`  = #{item.tcFlag},
                 </if>
                 <if test="item.nextDistributionSiteCode !=null and item.nextDistributionSiteCode != ''">
                     `next_distribution_site_code`  = #{item.nextDistributionSiteCode},
                 </if>
                 <if test="item.waybillNo !=null and item.waybillNo != ''">
                     `waybill_no`  = #{item.waybillNo},
                 </if>
                 <if test="item.orderStartTime != null">
                     `order_start_time`  = #{item.orderStartTime},
                 </if>
                 <if test="item.transportSystem != null">
                     `transport_system`  = #{item.transportSystem},
                 </if>
                 <if test="item.transportOrderNo != null">
                     `transport_order_no`  = #{item.transportOrderNo},
                 </if>
                 <if test="item.trainNumber != null and item.trainNumber != ''">
                     `train_number`  = #{item.trainNumber},
                 </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.orderType !=null and item.orderType != ''">
                        and  `order_type`  =#{item.orderType}
                    </if>
                    <if test="item.requireOutTime !=null">
                        and  `require_out_time`  =#{item.requireOutTime}
                    </if>
                    <if test="item.consigneeTimeFrom !=null">
                        and  `consignee_time_from`  =#{item.consigneeTimeFrom}
                    </if>
                    <if test="item.companyName !=null and item.companyName != ''">
                        and  `company_name`  =#{item.companyName}
                    </if>
                    <if test="item.orderWhCode !=null and item.orderWhCode != ''">
                        and  `order_wh_code`  =#{item.orderWhCode}
                    </if>
                    <if test="item.mainWaybillNo !=null and item.mainWaybillNo != ''">
                        and  `main_waybill_no`  =#{item.mainWaybillNo}
                    </if>
                    <if test="item.totalGrossWeight !=null">
                        and  `total_gross_weight`  =#{item.totalGrossWeight}
                    </if>
                    <if test="item.deliveredVerifyFlag !=null">
                        and  `delivered_verify_flag`  =#{item.deliveredVerifyFlag}
                    </if>
                    <if test="item.whName !=null and item.whName != ''">
                        and  `wh_name`  =#{item.whName}
                    </if>
                    <if test="item.payType !=null and item.payType != ''">
                        and  `pay_type`  =#{item.payType}
                    </if>
                    <if test="item.upperCustomerCode !=null and item.upperCustomerCode != ''">
                        and  `upper_customer_code`  =#{item.upperCustomerCode}
                    </if>
                    <if test="item.targeSiteCode !=null and item.targeSiteCode != ''">
                        and  `targe_site_code`  =#{item.targeSiteCode}
                    </if>
                    <if test="item.ifUpStairs !=null and item.ifUpStairs != ''">
                        and  `if_up_stairs`  =#{item.ifUpStairs}
                    </if>
                    <if test="item.installFlag !=null">
                        and  `install_flag`  =#{item.installFlag}
                    </if>
                    <if test="item.targetCustomerCode !=null and item.targetCustomerCode != ''">
                        and  `target_customer_code`  =#{item.targetCustomerCode}
                    </if>
                    <if test="item.taskStatus !=null">
                        and  `task_status`  =#{item.taskStatus}
                    </if>
                    <if test="item.qzWarehAreaFlag !=null and item.qzWarehAreaFlag != ''">
                        and  `qz_wareh_area_flag`  =#{item.qzWarehAreaFlag}
                    </if>
                    <if test="item.grayFlag !=null and item.grayFlag != ''">
                        and  `gray_flag`  =#{item.grayFlag}
                    </if>
                    <if test="item.agingProductCode !=null and item.agingProductCode != ''">
                        and  `aging_product_code`  =#{item.agingProductCode}
                    </if>
                    <if test="item.receiverSaleStype !=null and item.receiverSaleStype != ''">
                        and  `receiver_sale_stype`  =#{item.receiverSaleStype}
                    </if>
                    <if test="item.nextDistributionWhCode !=null and item.nextDistributionWhCode != ''">
                        and  `next_distribution_wh_code`  =#{item.nextDistributionWhCode}
                    </if>
                    <if test="item.orderConfirmTime !=null">
                        and  `order_confirm_time`  =#{item.orderConfirmTime}
                    </if>
                    <if test="item.orderSiteName !=null and item.orderSiteName != ''">
                        and  `order_site_name`  =#{item.orderSiteName}
                    </if>
                    <if test="item.totalPkgQty !=null">
                        and  `total_pkg_qty`  =#{item.totalPkgQty}
                    </if>
                    <if test="item.relationOrderNo !=null and item.relationOrderNo != ''">
                        and  `relation_order_no`  =#{item.relationOrderNo}
                    </if>
                    <if test="item.invoiceUnitName !=null and item.invoiceUnitName != ''">
                        and  `invoice_unit_name`  =#{item.invoiceUnitName}
                    </if>
                    <if test="item.version !=null">
                        and  `version`  =#{item.version}
                    </if>
                    <if test="item.expectArriveStartTime !=null">
                        and  `expect_arrive_start_time`  =#{item.expectArriveStartTime}
                    </if>
                    <if test="item.targeWhCode !=null and item.targeWhCode != ''">
                        and  `targe_wh_code`  =#{item.targeWhCode}
                    </if>
                    <if test="item.lineAging !=null">
                        and  `line_aging`  =#{item.lineAging}
                    </if>
                    <if test="item.customerName !=null and item.customerName != ''">
                        and  `customer_name`  =#{item.customerName}
                    </if>
                    <if test="item.excuteStatus !=null">
                        and  `excute_status`  =#{item.excuteStatus}
                    </if>
                    <if test="item.upperReceiverCode !=null and item.upperReceiverCode != ''">
                        and  `upper_receiver_code`  =#{item.upperReceiverCode}
                    </if>
                    <if test="item.lineCost !=null">
                        and  `line_cost`  =#{item.lineCost}
                    </if>
                    <if test="item.cloudWhFlag !=null and item.cloudWhFlag != ''">
                        and  `cloud_wh_flag`  =#{item.cloudWhFlag}
                    </if>
                    <if test="item.upperReferenceId !=null and item.upperReferenceId != ''">
                        and  `upper_reference_id`  =#{item.upperReferenceId}
                    </if>
                    <if test="item.freightBasis !=null and item.freightBasis != ''">
                        and  `freight_basis`  =#{item.freightBasis}
                    </if>
                    <if test="item.carrierCode !=null and item.carrierCode != ''">
                        and  `carrier_code`  =#{item.carrierCode}
                    </if>
                    <if test="item.deliverypayType !=null">
                        and  `deliverypay_type`  =#{item.deliverypayType}
                    </if>
                    <if test="item.totalNetWeight !=null">
                        and  `total_net_weight`  =#{item.totalNetWeight}
                    </if>
                    <if test="item.zoneCode !=null and item.zoneCode != ''">
                        and  `zone_code`  =#{item.zoneCode}
                    </if>
                    <if test="item.planOrderFlag !=null">
                        and  `plan_order_flag`  =#{item.planOrderFlag}
                    </if>
                    <if test="item.distributionWhName !=null and item.distributionWhName != ''">
                        and  `distribution_wh_name`  =#{item.distributionWhName}
                    </if>
                    <if test="item.upstreamDocType !=null and item.upstreamDocType != ''">
                        and  `upstream_doc_type`  =#{item.upstreamDocType}
                    </if>
                    <if test="item.loadType !=null and item.loadType != ''">
                        and  `load_type`  =#{item.loadType}
                    </if>
                    <if test="item.unitAreaName !=null and item.unitAreaName != ''">
                        and  `unit_area_name`  =#{item.unitAreaName}
                    </if>
                    <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                        and  `source_system`  =#{item.sourceSystem}
                    </if>
                    <if test="item.distributionFlag !=null">
                        and  `distribution_flag`  =#{item.distributionFlag}
                    </if>
                    <if test="item.contractNo !=null and item.contractNo != ''">
                        and  `contract_no`  =#{item.contractNo}
                    </if>
                    <if test="item.customerCode !=null and item.customerCode != ''">
                        and  `customer_code`  =#{item.customerCode}
                    </if>
                    <if test="item.siteName !=null and item.siteName != ''">
                        and  `site_name`  =#{item.siteName}
                    </if>
                    <if test="item.lineName !=null and item.lineName != ''">
                        and  `line_name`  =#{item.lineName}
                    </if>
                    <if test="item.supplierCode !=null and item.supplierCode != ''">
                        and  `supplier_code`  =#{item.supplierCode}
                    </if>
                    <if test="item.orderValue !=null">
                        and  `order_value`  =#{item.orderValue}
                    </if>
                    <if test="item.equipmentType !=null and item.equipmentType != ''">
                        and  `equipment_type`  =#{item.equipmentType}
                    </if>
                    <if test="item.lineMileage !=null">
                        and  `line_mileage`  =#{item.lineMileage}
                    </if>
                    <if test="item.specCarrierFlag !=null and item.specCarrierFlag != ''">
                        and  `spec_carrier_flag`  =#{item.specCarrierFlag}
                    </if>
                    <if test="item.upperOrderType !=null and item.upperOrderType != ''">
                        and  `upper_order_type`  =#{item.upperOrderType}
                    </if>
                    <if test="item.returnReason !=null and item.returnReason != ''">
                        and  `return_reason`  =#{item.returnReason}
                    </if>
                    <if test="item.taskNo !=null and item.taskNo != ''">
                        and  `task_no`  =#{item.taskNo}
                    </if>
                    <if test="item.projectClassify !=null and item.projectClassify != ''">
                        and  `project_classify`  =#{item.projectClassify}
                    </if>
                    <if test="item.orderEndTime !=null">
                        and  `order_end_time`  =#{item.orderEndTime}
                    </if>
                    <if test="item.cancelReason !=null and item.cancelReason != ''">
                        and  `cancel_reason`  =#{item.cancelReason}
                    </if>
                    <if test="item.specimenType !=null and item.specimenType != ''">
                        and  `specimen_type`  =#{item.specimenType}
                    </if>
                    <if test="item.inWhTime !=null">
                        and  `in_wh_time`  =#{item.inWhTime}
                    </if>
                    <if test="item.workOrderNo !=null and item.workOrderNo != ''">
                        and  `work_order_no`  =#{item.workOrderNo}
                    </if>
                    <if test="item.oaid !=null and item.oaid != ''">
                        and  `oaid`  =#{item.oaid}
                    </if>
                    <if test="item.businessMode !=null and item.businessMode != ''">
                        and  `business_mode`  =#{item.businessMode}
                    </if>
                    <if test="item.dispatchNo !=null and item.dispatchNo != ''">
                        and  `dispatch_no`  =#{item.dispatchNo}
                    </if>
                    <if test="item.expectInstallStartTime !=null">
                        and  `expect_install_start_time`  =#{item.expectInstallStartTime}
                    </if>
                    <if test="item.collectionAmount !=null">
                        and  `collection_amount`  =#{item.collectionAmount}
                    </if>
                    <if test="item.thirdFlag !=null and item.thirdFlag != ''">
                        and  `third_flag`  =#{item.thirdFlag}
                    </if>
                    <if test="item.printBarcode !=null">
                        and  `print_barcode`  =#{item.printBarcode}
                    </if>
                    <if test="item.expectInstallType !=null and item.expectInstallType != ''">
                        and  `expect_install_type`  =#{item.expectInstallType}
                    </if>
                    <if test="item.arriveAppointmentRequireFlag !=null">
                        and  `arrive_appointment_require_flag`  =#{item.arriveAppointmentRequireFlag}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.customerTotalQty !=null">
                        and  `customer_total_qty`  =#{item.customerTotalQty}
                    </if>
                    <if test="item.expectArriveEndTime !=null">
                        and  `expect_arrive_end_time`  =#{item.expectArriveEndTime}
                    </if>
                    <if test="item.operationsCenterCode !=null and item.operationsCenterCode != ''">
                        and  `operations_center_code`  =#{item.operationsCenterCode}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.upperWhName !=null and item.upperWhName != ''">
                        and  `upper_wh_name`  =#{item.upperWhName}
                    </if>
                    <if test="item.upperSenderName !=null and item.upperSenderName != ''">
                        and  `upper_sender_name`  =#{item.upperSenderName}
                    </if>
                    <if test="item.deliveryCycle !=null and item.deliveryCycle != ''">
                        and  `delivery_cycle`  =#{item.deliveryCycle}
                    </if>
                    <if test="item.businessCategory !=null and item.businessCategory != ''">
                        and  `business_category`  =#{item.businessCategory}
                    </if>
                    <if test="item.distributionLastFlag !=null">
                        and  `distribution_last_flag`  =#{item.distributionLastFlag}
                    </if>
                    <if test="item.customerOrderNo !=null and item.customerOrderNo != ''">
                        and  `customer_order_no`  =#{item.customerOrderNo}
                    </if>
                    <if test="item.customerAgingCode !=null and item.customerAgingCode != ''">
                        and  `customer_aging_code`  =#{item.customerAgingCode}
                    </if>
                    <if test="item.payDate !=null">
                        and  `pay_date`  =#{item.payDate}
                    </if>
                    <if test="item.totalVolume !=null">
                        and  `total_volume`  =#{item.totalVolume}
                    </if>
                    <if test="item.serviceType !=null and item.serviceType != ''">
                        and  `service_type`  =#{item.serviceType}
                    </if>
                    <if test="item.totalQty !=null">
                        and  `total_qty`  =#{item.totalQty}
                    </if>
                    <if test="item.payTime !=null">
                        and  `pay_time`  =#{item.payTime}
                    </if>
                    <if test="item.stationType !=null and item.stationType != ''">
                        and  `station_type`  =#{item.stationType}
                    </if>
                    <if test="item.orderSiteCode !=null and item.orderSiteCode != ''">
                        and  `order_site_code`  =#{item.orderSiteCode}
                    </if>
                    <if test="item.distributionNum !=null">
                        and  `distribution_num`  =#{item.distributionNum}
                    </if>
                    <if test="item.consolidationNum !=null">
                        and  `consolidation_num`  =#{item.consolidationNum}
                    </if>
                    <if test="item.parentOrderNo !=null and item.parentOrderNo != ''">
                        and  `parent_order_no`  =#{item.parentOrderNo}
                    </if>
                    <if test="item.distributionSiteCode !=null and item.distributionSiteCode != ''">
                        and  `distribution_site_code`  =#{item.distributionSiteCode}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.buyerRemark !=null and item.buyerRemark != ''">
                        and  `buyer_remark`  =#{item.buyerRemark}
                    </if>
                    <if test="item.isBack !=null">
                        and  `is_back`  =#{item.isBack}
                    </if>
                    <if test="item.driverQueueCode !=null and item.driverQueueCode != ''">
                        and  `driver_queue_code`  =#{item.driverQueueCode}
                    </if>
                    <if test="item.orderTime !=null">
                        and  `order_time`  =#{item.orderTime}
                    </if>
                    <if test="item.invoiceUnitCode !=null and item.invoiceUnitCode != ''">
                        and  `invoice_unit_code`  =#{item.invoiceUnitCode}
                    </if>
                    <if test="item.jpOrderNo !=null and item.jpOrderNo != ''">
                        and  `jp_order_no`  =#{item.jpOrderNo}
                    </if>
                    <if test="item.orderRpFlag !=null and item.orderRpFlag != ''">
                        and  `order_rp_flag`  =#{item.orderRpFlag}
                    </if>
                    <if test="item.equipmentName !=null and item.equipmentName != ''">
                        and  `equipment_name`  =#{item.equipmentName}
                    </if>
                    <if test="item.shopId !=null and item.shopId != ''">
                        and  `shop_id`  =#{item.shopId}
                    </if>
                    <if test="item.shopGuideTel !=null and item.shopGuideTel != ''">
                        and  `shop_guide_tel`  =#{item.shopGuideTel}
                    </if>
                    <if test="item.expectInstallEndTime !=null">
                        and  `expect_install_end_time`  =#{item.expectInstallEndTime}
                    </if>
                    <if test="item.mileage !=null">
                        and  `mileage`  =#{item.mileage}
                    </if>
                    <if test="item.companyCode !=null and item.companyCode != ''">
                        and  `company_code`  =#{item.companyCode}
                    </if>
                    <if test="item.orderWhName !=null and item.orderWhName != ''">
                        and  `order_wh_name`  =#{item.orderWhName}
                    </if>
                    <if test="item.invoiceFlag !=null and item.invoiceFlag != ''">
                        and  `invoice_flag`  =#{item.invoiceFlag}
                    </if>
                    <if test="item.appointmentType !=null and item.appointmentType != ''">
                        and  `appointment_type`  =#{item.appointmentType}
                    </if>
                    <if test="item.orderNo !=null and item.orderNo != ''">
                        and  `order_no`  =#{item.orderNo}
                    </if>
                    <if test="item.customerGroup !=null and item.customerGroup != ''">
                        and  `customer_group`  =#{item.customerGroup}
                    </if>
                    <if test="item.cnDispatch !=null">
                        and  `cn_dispatch`  =#{item.cnDispatch}
                    </if>
                    <if test="item.collectionFlag !=null and item.collectionFlag != ''">
                        and  `collection_flag`  =#{item.collectionFlag}
                    </if>
                    <if test="item.printPriceFlag !=null">
                        and  `print_price_flag`  =#{item.printPriceFlag}
                    </if>
                    <if test="item.shopGuideName !=null and item.shopGuideName != ''">
                        and  `shop_guide_name`  =#{item.shopGuideName}
                    </if>
                    <if test="item.orderSourcePlatform !=null and item.orderSourcePlatform != ''">
                        and  `order_source_platform`  =#{item.orderSourcePlatform}
                    </if>
                    <if test="item.outsourceFlag !=null and item.outsourceFlag != ''">
                        and  `outsource_flag`  =#{item.outsourceFlag}
                    </if>
                    <if test="item.serviceOrderNo !=null and item.serviceOrderNo != ''">
                        and  `service_order_no`  =#{item.serviceOrderNo}
                    </if>
                    <if test="item.whCode !=null and item.whCode != ''">
                        and  `wh_code`  =#{item.whCode}
                    </if>
                    <if test="item.setupType !=null and item.setupType != ''">
                        and  `setup_type`  =#{item.setupType}
                    </if>
                    <if test="item.emergenceFlag !=null and item.emergenceFlag != ''">
                        and  `emergence_flag`  =#{item.emergenceFlag}
                    </if>
                    <if test="item.upperAgingCode !=null and item.upperAgingCode != ''">
                        and  `upper_aging_code`  =#{item.upperAgingCode}
                    </if>
                    <if test="item.appointOrderNo !=null and item.appointOrderNo != ''">
                        and  `appoint_order_no`  =#{item.appointOrderNo}
                    </if>
                    <if test="item.transferFlag !=null">
                        and  `transfer_flag`  =#{item.transferFlag}
                    </if>
                    <if test="item.orderStatus !=null">
                        and  `order_status`  =#{item.orderStatus}
                    </if>
                    <if test="item.shopName !=null and item.shopName != ''">
                        and  `shop_name`  =#{item.shopName}
                    </if>
                    <if test="item.remark !=null">
                        and  `remark`  =#{item.remark}
                    </if>
                    <if test="item.tenantCode !=null and item.tenantCode != ''">
                        and  `tenant_code`  =#{item.tenantCode}
                    </if>
                    <if test="item.pickFlag !=null and item.pickFlag != ''">
                        and  `pick_flag`  =#{item.pickFlag}
                    </if>
                    <if test="item.distributionStationType !=null and item.distributionStationType != ''">
                        and  `distribution_station_type`  =#{item.distributionStationType}
                    </if>
                    <if test="item.appointmentReason !=null and item.appointmentReason != ''">
                        and  `appointment_reason`  =#{item.appointmentReason}
                    </if>
                    <if test="item.printNotaxPriceFlag !=null">
                        and  `print_notax_price_flag`  =#{item.printNotaxPriceFlag}
                    </if>
                    <if test="item.consolidationOrderNo !=null and item.consolidationOrderNo != ''">
                        and  `consolidation_order_no`  =#{item.consolidationOrderNo}
                    </if>
                    <if test="item.upperReceiverName !=null and item.upperReceiverName != ''">
                        and  `upper_receiver_name`  =#{item.upperReceiverName}
                    </if>
                    <if test="item.lineCode !=null and item.lineCode != ''">
                        and  `line_code`  =#{item.lineCode}
                    </if>
                    <if test="item.taskType !=null and item.taskType != ''">
                        and  `task_type`  =#{item.taskType}
                    </if>
                    <if test="item.inOutType !=null and item.inOutType != ''">
                        and  `in_out_type`  =#{item.inOutType}
                    </if>
                    <if test="item.pledgeType !=null and item.pledgeType != ''">
                        and  `pledge_type`  =#{item.pledgeType}
                    </if>
                    <if test="item.logisticMode !=null and item.logisticMode != ''">
                        and  `logistic_mode`  =#{item.logisticMode}
                    </if>
                    <if test="item.scPosFlag !=null">
                        and  `sc_pos_flag`  =#{item.scPosFlag}
                    </if>
                    <if test="item.upperSenderCode !=null and item.upperSenderCode != ''">
                        and  `upper_sender_code`  =#{item.upperSenderCode}
                    </if>
                    <if test="item.appointmentTime !=null">
                        and  `appointment_time`  =#{item.appointmentTime}
                    </if>
                    <if test="item.whSystem !=null and item.whSystem != ''">
                        and  `wh_system`  =#{item.whSystem}
                    </if>
                    <if test="item.distributionWhCode !=null and item.distributionWhCode != ''">
                        and  `distribution_wh_code`  =#{item.distributionWhCode}
                    </if>
                    <if test="item.transportType !=null and item.transportType != ''">
                        and  `transport_type`  =#{item.transportType}
                    </if>
                    <if test="item.withdrawalReason !=null and item.withdrawalReason != ''">
                        and  `withdrawal_reason`  =#{item.withdrawalReason}
                    </if>
                    <if test="item.zoneName !=null and item.zoneName != ''">
                        and  `zone_name`  =#{item.zoneName}
                    </if>
                    <if test="item.upperSourceCustomerCode !=null and item.upperSourceCustomerCode != ''">
                        and  `upper_source_customer_code`  =#{item.upperSourceCustomerCode}
                    </if>
                    <if test="item.professionalCompany !=null and item.professionalCompany != ''">
                        and  `professional_company`  =#{item.professionalCompany}
                    </if>
                    <if test="item.orderSource !=null and item.orderSource != ''">
                        and  `order_source`  =#{item.orderSource}
                    </if>
                    <if test="item.siteCode !=null and item.siteCode != ''">
                        and  `site_code`  =#{item.siteCode}
                    </if>
                    <if test="item.upperWhCode !=null and item.upperWhCode != ''">
                        and  `upper_wh_code`  =#{item.upperWhCode}
                    </if>
                    <if test="item.deliveryType !=null and item.deliveryType != ''">
                        and  `delivery_type`  =#{item.deliveryType}
                    </if>
                    <if test="item.entityId !=null">
                        and  `entity_id`  =#{item.entityId}
                    </if>
                    <if test="item.distributionSiteName !=null and item.distributionSiteName != ''">
                        and  `distribution_site_name`  =#{item.distributionSiteName}
                    </if>
                    <if test="item.holdFlag !=null">
                        and  `hold_flag`  =#{item.holdFlag}
                    </if>
                    <if test="item.originOrderNo !=null and item.originOrderNo != ''">
                        and  `origin_order_no`  =#{item.originOrderNo}
                    </if>
                    <if test="item.upperCustomerName !=null and item.upperCustomerName != ''">
                        and  `upper_customer_name`  =#{item.upperCustomerName}
                    </if>
                    <if test="item.platformOrderNo !=null and item.platformOrderNo != ''">
                        and  `platform_order_no`  =#{item.platformOrderNo}
                    </if>
                    <if test="item.joinType !=null and item.joinType != ''">
                        and  `join_type`  =#{item.joinType}
                    </if>
                    <if test="item.consigneeTimeTo !=null">
                        and  `consignee_time_to`  =#{item.consigneeTimeTo}
                    </if>
                    <if test="item.c2mType !=null and item.c2mType != ''">
                        and  `c2m_type`  =#{item.c2mType}
                    </if>
                    <if test="item.businessType !=null and item.businessType != ''">
                        and  `business_type`  =#{item.businessType}
                    </if>
                    <if test="item.tcFlag !=null and item.tcFlag != ''">
                        and  `tc_flag`  =#{item.tcFlag}
                    </if>
                    <if test="item.nextDistributionSiteCode !=null and item.nextDistributionSiteCode != ''">
                        and  `next_distribution_site_code`  =#{item.nextDistributionSiteCode}
                    </if>
                    <if test="item.waybillNo !=null and item.waybillNo != ''">
                        and  `waybill_no`  =#{item.waybillNo}
                    </if>
                    <if test="item.orderStartTime !=null">
                        and  `order_start_time`  =#{item.orderStartTime}
                    </if>
                    <if test="item.transportSystem !=null">
                        and  `transport_system`  =#{item.transportSystem}
                    </if>
                    <if test="item.transportOrderNo !=null">
                        and  `transport_order_no`  =#{item.transportOrderNo}
                    </if>
                    <if test="item.trainNumber !=null and item.trainNumber != ''">
                        and  `train_number`  =#{item.trainNumber}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE task
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
#{item.id}
        </foreach>
    </update>


    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE task
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>

</mapper>