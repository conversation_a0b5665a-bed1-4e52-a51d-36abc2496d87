<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.AddressToSiteMapper">

    <sql id="searchFieldsSql">
            `order_type` AS  orderType,
            `town_name` AS  townName,
            `site_code` AS  siteCode,
            `district_code` AS  districtCode,
            `district_name` AS  districtName,
            `town_code` AS  townCode,
            `province_code` AS  provinceCode,
            `city_code` AS  cityCode,
            `create_user_code` AS  createUserCode,
            `customer_code` AS  customerCode,
            `site_name` AS  siteName,
            `update_time` AS  updateTime,
            `remark` AS  remark,
            `VERSION` AS  version,
            `customer_name` AS  customerName,
            `delete_flag` AS  deleteFlag,
            `update_user_code` AS  updateUserCode,
            `wh_name` AS  whName,
            `wh_code` AS  whCode,
            `city_name` AS  cityName,
            `create_time` AS  createTime,
            `id` AS  id,
            `province_name` AS  provinceName
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="orderType !=null and orderType != ''">
                        and `order_type` =#{orderType}
                    </if>
                    <if test="townName !=null and townName != ''">
                        and `town_name` =#{townName}
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        and `site_code` =#{siteCode}
                    </if>
                    <if test="districtCode !=null and districtCode != ''">
                        and `district_code` =#{districtCode}
                    </if>
                    <if test="districtName !=null and districtName != ''">
                        and `district_name` =#{districtName}
                    </if>
                    <if test="townCode !=null and townCode != ''">
                        and `town_code` =#{townCode}
                    </if>
                    <if test="provinceCode !=null and provinceCode != ''">
                        and `province_code` =#{provinceCode}
                    </if>
                    <if test="cityCode !=null and cityCode != ''">
                        and `city_code` =#{cityCode}
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        and `customer_code` =#{customerCode}
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        and `site_name` =#{siteName}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="remark !=null">
                        and `remark` =#{remark}
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        and `customer_name` =#{customerName}
                    </if>
                    <if test="whName !=null and whName != ''">
                        and `wh_name` =#{whName}
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        and `wh_code` =#{whCode}
                    </if>
                    <if test="cityName !=null and cityName != ''">
                        and `city_name` =#{cityName}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="provinceName !=null and provinceName != ''">
                        and `province_name` =#{provinceName}
                    </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
                        <if test="orderType !=null and orderType != ''">
                            `order_type` = #{orderType},
                        </if>
                        <if test="townName !=null and townName != ''">
                            `town_name` = #{townName},
                        </if>
                        <if test="siteCode !=null and siteCode != ''">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="districtCode !=null and districtCode != ''">
                            `district_code` = #{districtCode},
                        </if>
                        <if test="districtName !=null and districtName != ''">
                            `district_name` = #{districtName},
                        </if>
                        <if test="townCode !=null and townCode != ''">
                            `town_code` = #{townCode},
                        </if>
                        <if test="provinceCode !=null and provinceCode != ''">
                            `province_code` = #{provinceCode},
                        </if>
                        <if test="cityCode !=null and cityCode != ''">
                            `city_code` = #{cityCode},
                        </if>
                        <if test="customerCode !=null and customerCode != ''">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="siteName !=null and siteName != ''">
                            `site_name` = #{siteName},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="customerName !=null and customerName != ''">
                            `customer_name` = #{customerName},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="whName !=null and whName != ''">
                            `wh_name` = #{whName},
                        </if>
                        <if test="whCode !=null and whCode != ''">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="cityName !=null and cityName != ''">
                            `city_name` = #{cityName},
                        </if>
                        <if test="provinceName !=null and provinceName != ''">
                            `province_name` = #{provinceName},
                        </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="orderType !=null">
                            `order_type` = #{orderType},
                        </if>
                        <if test="townName !=null">
                            `town_name` = #{townName},
                        </if>
                        <if test="siteCode !=null">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="districtCode !=null">
                            `district_code` = #{districtCode},
                        </if>
                        <if test="districtName !=null">
                            `district_name` = #{districtName},
                        </if>
                        <if test="townCode !=null">
                            `town_code` = #{townCode},
                        </if>
                        <if test="provinceCode !=null">
                            `province_code` = #{provinceCode},
                        </if>
                        <if test="cityCode !=null">
                            `city_code` = #{cityCode},
                        </if>
                        <if test="customerCode !=null">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="siteName !=null">
                            `site_name` = #{siteName},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="customerName !=null">
                            `customer_name` = #{customerName},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="whName !=null">
                            `wh_name` = #{whName},
                        </if>
                        <if test="whCode !=null">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="cityName !=null">
                            `city_name` = #{cityName},
                        </if>
                        <if test="provinceName !=null">
                            `province_name` = #{provinceName},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from address_to_site t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.AddressToSite">
        select
        <include refid="searchFieldsSql"/>
        from address_to_site t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.AddressToSite">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from address_to_site t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.AddressToSite">
        select
        <include refid="searchFieldsSql"/>
        from address_to_site t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
${orderByType}            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        address_to_site t
        <include refid="setFieldsSql"/>
        where
                `VERSION` = #{version}
            and `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        address_to_site t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
                `VERSION` = #{version}
            and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        address_to_site t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.AddressToSite" useGeneratedKeys="true" keyProperty="id">
        insert into address_to_site
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="orderType !=null and orderType != ''">
                        `order_type`,
                    </if>

                    <if test="townName !=null and townName != ''">
                        `town_name`,
                    </if>

                    <if test="siteCode !=null and siteCode != ''">
                        `site_code`,
                    </if>

                    <if test="districtCode !=null and districtCode != ''">
                        `district_code`,
                    </if>

                    <if test="districtName !=null and districtName != ''">
                        `district_name`,
                    </if>

                    <if test="townCode !=null and townCode != ''">
                        `town_code`,
                    </if>

                    <if test="provinceCode !=null and provinceCode != ''">
                        `province_code`,
                    </if>

                    <if test="cityCode !=null and cityCode != ''">
                        `city_code`,
                    </if>

                    <if test="createUserCode != null">
                        `create_user_code`,
                    </if>

                    <if test="customerCode !=null and customerCode != ''">
                        `customer_code`,
                    </if>

                    <if test="siteName !=null and siteName != ''">
                        `site_name`,
                    </if>

                    <if test="remark != null">
                        `remark`,
                    </if>

                    <if test="customerName !=null and customerName != ''">
                        `customer_name`,
                    </if>

                    <if test="updateUserCode != null">
                        `update_user_code`,
                    </if>

                    <if test="whName !=null and whName != ''">
                        `wh_name`,
                    </if>

                    <if test="whCode !=null and whCode != ''">
                        `wh_code`,
                    </if>

                    <if test="cityName !=null and cityName != ''">
                        `city_name`,
                    </if>

                    <if test="provinceName !=null and provinceName != ''">
                        `province_name`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="orderType !=null and orderType != ''">
                        #{orderType},
                    </if>
                    <if test="townName !=null and townName != ''">
                        #{townName},
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        #{siteCode},
                    </if>
                    <if test="districtCode !=null and districtCode != ''">
                        #{districtCode},
                    </if>
                    <if test="districtName !=null and districtName != ''">
                        #{districtName},
                    </if>
                    <if test="townCode !=null and townCode != ''">
                        #{townCode},
                    </if>
                    <if test="provinceCode !=null and provinceCode != ''">
                        #{provinceCode},
                    </if>
                    <if test="cityCode !=null and cityCode != ''">
                        #{cityCode},
                    </if>
                    <if test="createUserCode != null">
                        #{createUserCode},
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        #{customerCode},
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        #{siteName},
                    </if>
                    <if test="remark != null">
                        #{remark},
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        #{customerName},
                    </if>
                    <if test="updateUserCode != null">
                        #{updateUserCode},
                    </if>
                    <if test="whName !=null and whName != ''">
                        #{whName},
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        #{whCode},
                    </if>
                    <if test="cityName !=null and cityName != ''">
                        #{cityName},
                    </if>
                    <if test="provinceName !=null and provinceName != ''">
                        #{provinceName},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                `order_type`,
                `town_name`,
                `site_code`,
                `district_code`,
                `district_name`,
                `town_code`,
                `province_code`,
                `city_code`,
                `create_user_code`,
                `customer_code`,
                `site_name`,
                `remark`,
                `customer_name`,
                `update_user_code`,
                `wh_name`,
                `wh_code`,
                `city_name`,
                `province_name`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.orderType},
                #{item.townName},
                #{item.siteCode},
                #{item.districtCode},
                #{item.districtName},
                #{item.townCode},
                #{item.provinceCode},
                #{item.cityCode},
                #{item.createUserCode},
                #{item.customerCode},
                #{item.siteName},
                #{item.remark},
                #{item.customerName},
                #{item.updateUserCode},
                #{item.whName},
                #{item.whCode},
                #{item.cityName},
                #{item.provinceName},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        address_to_site
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.orderType !=null and item.orderType != ''">
                     `order_type`  = #{item.orderType},
                 </if>
                 <if test="item.townName !=null and item.townName != ''">
                     `town_name`  = #{item.townName},
                 </if>
                 <if test="item.siteCode !=null and item.siteCode != ''">
                     `site_code`  = #{item.siteCode},
                 </if>
                 <if test="item.districtCode !=null and item.districtCode != ''">
                     `district_code`  = #{item.districtCode},
                 </if>
                 <if test="item.districtName !=null and item.districtName != ''">
                     `district_name`  = #{item.districtName},
                 </if>
                 <if test="item.townCode !=null and item.townCode != ''">
                     `town_code`  = #{item.townCode},
                 </if>
                 <if test="item.provinceCode !=null and item.provinceCode != ''">
                     `province_code`  = #{item.provinceCode},
                 </if>
                 <if test="item.cityCode !=null and item.cityCode != ''">
                     `city_code`  = #{item.cityCode},
                 </if>
                 <if test="item.customerCode !=null and item.customerCode != ''">
                     `customer_code`  = #{item.customerCode},
                 </if>
                 <if test="item.siteName !=null and item.siteName != ''">
                     `site_name`  = #{item.siteName},
                 </if>
                 <if test="item.remark != null">
                     `remark`  = #{item.remark},
                 </if>
                 <if test="item.customerName !=null and item.customerName != ''">
                     `customer_name`  = #{item.customerName},
                 </if>
                 <if test="item.updateUserCode != null">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.whName !=null and item.whName != ''">
                     `wh_name`  = #{item.whName},
                 </if>
                 <if test="item.whCode !=null and item.whCode != ''">
                     `wh_code`  = #{item.whCode},
                 </if>
                 <if test="item.cityName !=null and item.cityName != ''">
                     `city_name`  = #{item.cityName},
                 </if>
                 <if test="item.provinceName !=null and item.provinceName != ''">
                     `province_name`  = #{item.provinceName},
                 </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.orderType !=null and item.orderType != ''">
                        and  `order_type`  =#{item.orderType}
                    </if>
                    <if test="item.townName !=null and item.townName != ''">
                        and  `town_name`  =#{item.townName}
                    </if>
                    <if test="item.siteCode !=null and item.siteCode != ''">
                        and  `site_code`  =#{item.siteCode}
                    </if>
                    <if test="item.districtCode !=null and item.districtCode != ''">
                        and  `district_code`  =#{item.districtCode}
                    </if>
                    <if test="item.districtName !=null and item.districtName != ''">
                        and  `district_name`  =#{item.districtName}
                    </if>
                    <if test="item.townCode !=null and item.townCode != ''">
                        and  `town_code`  =#{item.townCode}
                    </if>
                    <if test="item.provinceCode !=null and item.provinceCode != ''">
                        and  `province_code`  =#{item.provinceCode}
                    </if>
                    <if test="item.cityCode !=null and item.cityCode != ''">
                        and  `city_code`  =#{item.cityCode}
                    </if>
                    <if test="item.customerCode !=null and item.customerCode != ''">
                        and  `customer_code`  =#{item.customerCode}
                    </if>
                    <if test="item.siteName !=null and item.siteName != ''">
                        and  `site_name`  =#{item.siteName}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.remark !=null">
                        and  `remark`  =#{item.remark}
                    </if>
                    <if test="item.version !=null">
                        and  `VERSION`  =#{item.version}
                    </if>
                    <if test="item.customerName !=null and item.customerName != ''">
                        and  `customer_name`  =#{item.customerName}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.whName !=null and item.whName != ''">
                        and  `wh_name`  =#{item.whName}
                    </if>
                    <if test="item.whCode !=null and item.whCode != ''">
                        and  `wh_code`  =#{item.whCode}
                    </if>
                    <if test="item.cityName !=null and item.cityName != ''">
                        and  `city_name`  =#{item.cityName}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.provinceName !=null and item.provinceName != ''">
                        and  `province_name`  =#{item.provinceName}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE address_to_site
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
#{item.id}
        </foreach>
    </update>


    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE address_to_site
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>

    <update id="updatePlace">
        update address_to_site
        <set>
            `version` = `version` + 1 ,
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `district_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `town_code` = #{busCode}
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>

    <update id="dcReplacePlace">
        update address_to_site
        <set>
            `version` = `version` + 1 ,
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `province_code` = #{newBusCode},
                    `province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `city_code` = #{newBusCode},
                    `city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `district_code` = #{newBusCode},
                    `district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `town_code` = #{newBusCode},
                    `town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `district_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `town_code` = #{busCode}
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>

</mapper>