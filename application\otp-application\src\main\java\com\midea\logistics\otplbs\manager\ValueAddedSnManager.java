package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.bean.ValueAddedSnExcelRequest;
import com.midea.logistics.otp.enums.CNInventoryType;
import com.midea.logistics.otp.enums.ValuedStatus;
import com.midea.logistics.otp.order.domain.bean.ValueAddedService;
import com.midea.logistics.otp.order.domain.bean.ValueAddedServiceItemSn;
import com.midea.logistics.otp.order.domain.bean.custom.ValueAddedServiceExt;
import com.midea.logistics.otplbs.fegin.ValueAddedServiceFeign;
import com.midea.logistics.otplbs.fegin.ValueAddedServiceItemSnFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Author:  ex_dengzj4
 * Date:     2020-4-22 12:00
 * Description: 增值服务SN信息导入
 */

@Slf4j
@Component
public class ValueAddedSnManager {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ValueAddedServiceItemSnFeign valueAddedServiceItemSnFeign;
    @Autowired
    private ValueAddedServiceFeign valueAddedServiceFeign;

    /**
     * 模板导入
     */
    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) throws Exception {
        log.info("----------->增值服务SN信息开始导入:");
        ImportExcel importExcel = new ImportExcel(file,1,0,applicationContext);
        int lastCellNum = importExcel.getLastCellNum();
        if (lastCellNum != 10){
            throw BusinessException.fail("当前使用导入模板版本错误,请点击导出重新下载最新版本的模板.");
        }
        List<ValueAddedSnExcelRequest> requestList = importExcel.getDataList(ValueAddedSnExcelRequest.class,null);
        List<ValueAddedServiceItemSn> dataList = Lists.newArrayList();
        List<CNInventoryType> list = Arrays.asList(CNInventoryType.values());
        List<String> inventoryTypeList = list.stream().map(CNInventoryType::getValue).collect(Collectors.toList());
        final int[] row = {1};
        for (ValueAddedSnExcelRequest valueAddedSnExcelRequest : requestList) {
            row[0]++;
            if (ToolUtils.isEmpty(valueAddedSnExcelRequest.getId())) {
                throw BusinessException.fail("第"+ row[0] +"行id不能为空;当前使用导入模板版本错误,请点击导出重新下载最新版本的模板.");
            }
            //商品状态转换
            if (StringUtils.isBlank(valueAddedSnExcelRequest.getInventoryType())) {
                throw BusinessException.fail("第"+ row[0] + "行，库存类型请输入：" + inventoryTypeList);
            }
            Optional<CNInventoryType> first = list.stream().filter(o -> o.getValue().equals(valueAddedSnExcelRequest.getInventoryType())).findFirst();
            if (first.isPresent()) {
                valueAddedSnExcelRequest.setInventoryType(first.get().getKey());
            } else {
                throw BusinessException.fail("第"+ row[0] + "行，库存类型请输入：" + inventoryTypeList);
            }
            ValueAddedServiceItemSn valueAddedServiceItemSn = new ValueAddedServiceItemSn(valueAddedSnExcelRequest.getId().longValue());
            BeanUtils.copyProperties(valueAddedSnExcelRequest,valueAddedServiceItemSn,"itemLineNo","vdOrderNo");
            dataList.add(valueAddedServiceItemSn);
        }
        JsonResponse<Integer> jsonResponse = valueAddedServiceItemSnFeign.batchCreateOrUpdate(dataList);
        if (null != jsonResponse && BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode()) && null != jsonResponse.getData()) {
            if (jsonResponse.getData() == 0) {
            throw BusinessException.fail("导入失败！当前使用导入模板版本错误,请点击导出重新下载最新版本的模板");
            }
        } else  {
            log.info("----------->查询ValueAddedSn信息失败.");
        }
        log.info("----------->增值服务SN信息导入结束.");
        return jsonResponse;
    }

    public JsonResponse resetSn(String vdOrderNo) {
        ValueAddedServiceExt valueAddedService = new ValueAddedServiceExt();
        valueAddedService.setVdOrderNo(vdOrderNo);
        List<ValueAddedService> list = valueAddedServiceFeign.search(valueAddedService).getData().getList();
        if (CollectionUtils.isEmpty(list) || !ValuedStatus.NEW.getKey().equals(list.get(0).getOrderStatus())) {
            throw BusinessException.fail("非新增状态下不能清空SN");
        }
        return valueAddedServiceItemSnFeign.deleteByVdOrderNo(vdOrderNo);
    }
}
