package com.midea.logistics.otp.order.common.fegin.fallback;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.otp.order.converged.domain.request.ArriveAreaRequest;
import com.midea.logistics.otp.order.converged.domain.request.OnwayAreaRequest;
import com.midea.logistics.otp.order.converged.domain.request.OutAreaRequest;
import com.midea.logistics.otp.order.common.fegin.bop.OrderItemAreaFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import feign.hystrix.FallbackFactory;


/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: OrderItemAreaFeignFallback
 * @author: crystal
 * @date: 2019/7/12 11:29
 * @description: ${DESCRIPTION}
 */
public class OrderItemAreaFeignFallback  implements FallbackFactory<OrderItemAreaFeign> {

    @Override
    public OrderItemAreaFeign create(Throwable throwable) {
        return new OrderItemAreaFeign() {
            @Override
            public String cdOverFlowOnway(OnwayAreaRequest bean) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return JSON.toJSONString(jsonResponse);
            }

            @Override
            public String cdOverFlowCheckArea(OutAreaRequest request) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return JSON.toJSONString(jsonResponse);
            }

            @Override
            public String cdOverFlowArrive(ArriveAreaRequest request) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return JSON.toJSONString(jsonResponse);
            }

            @Override
            public String cdOverFlowCheckAreaLMDM(OutAreaRequest request) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return JSON.toJSONString(jsonResponse);
            }

            @Override
            public String cdOverFlowOnwayLMDM(OnwayAreaRequest bean) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return JSON.toJSONString(jsonResponse);
            }

            @Override
            public String cdOverFlowArriveLMDM(ArriveAreaRequest request) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return JSON.toJSONString(jsonResponse);
            }
        };
    }
}
