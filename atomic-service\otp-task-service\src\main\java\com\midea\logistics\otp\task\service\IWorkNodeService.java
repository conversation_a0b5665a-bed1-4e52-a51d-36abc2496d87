package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.task.domain.bean.WorkNode;
import com.mideaframework.core.web.PageResponse;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: IWorkNodeService
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:IWorkNodeService服务接口
 */
public interface IWorkNodeService {

    Integer saveWorkNode(WorkNode workNode);

    Integer deleteWorkNode(WorkNode workNode);

    Integer updateWorkNode(WorkNode workNode);

    WorkNode selectOneWorkNode(WorkNode workNode);

    PageResponse<WorkNode> selectWorkNodePage(WorkNode workNode);

    List<WorkNode> selectWorkNodeByIndex(WorkNode workNode);

    int countWorkNodeByIndex(WorkNode workNode);

    Integer insertBatch(List<WorkNode> workNodes);

    Integer batchUpdate(List<WorkNode> workNodes);

    Integer deleteBatch(List<WorkNode> workNodes);
}