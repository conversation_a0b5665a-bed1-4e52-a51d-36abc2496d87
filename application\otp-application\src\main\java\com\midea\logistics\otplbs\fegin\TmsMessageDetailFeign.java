package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.dispatch.domain.bean.TmsMessageDetail;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: TmsMessageDetailFeign
* Author: james
* Date: 2021-7-13 19:06:26
* Description:tms短信配置表 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface TmsMessageDetailFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/tmsMessageDetail", method = RequestMethod.POST)
    JsonResponse create(@RequestBody TmsMessageDetail tmsMessageDetail);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/tmsMessageDetail/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/tmsMessageDetail/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody TmsMessageDetail tmsMessageDetail);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/tmsMessageDetail/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/tmsMessageDetails", method = RequestMethod.POST)
    JsonResponse search(@RequestBody TmsMessageDetail tmsMessageDetail);
}
