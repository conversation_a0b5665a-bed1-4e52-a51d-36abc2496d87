package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;

/**
 * <AUTHOR> 396171264.qq.com
 * @ClassName: PledgeFlowService
 * @Description: 抵押功能
 * @date 2019年5月28日 上午11:50:31
 */
public interface PledgeFlowService {

    /**
     * 事业部出库质押校验 判断是否能够质押 true 为可以质押 false  为不能质押
     *
     * @param  orderInfoExt
     */
    OrderInfoExt pledgeCheck(OrderInfoExt orderInfoExt);

}
