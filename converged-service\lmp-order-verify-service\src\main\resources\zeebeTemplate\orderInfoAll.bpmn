<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd" name="子订单流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0opo74t</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0opo74t" sourceRef="StartEvent_1" targetRef="Activity_0smupof" />
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_032c74y</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_04pcpkq" name="是否菜鸟" default="Flow_0j1zxrb">
      <bpmn:incoming>Flow_1r51i37</bpmn:incoming>
      <bpmn:outgoing>Flow_0j1zxrb</bpmn:outgoing>
      <bpmn:outgoing>Flow_0o9uiqd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1r51i37" sourceRef="Activity_1xd43z3" targetRef="Gateway_04pcpkq" />
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_0j1zxrb</bpmn:incoming>
      <bpmn:incoming>Flow_1fsp131</bpmn:incoming>
      <bpmn:incoming>Flow_15zlv1x</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0j1zxrb" name="no" sourceRef="Gateway_04pcpkq" targetRef="Gateway_0mzkgmp" />
    <bpmn:sequenceFlow id="Flow_0o9uiqd" name="yes" sourceRef="Gateway_04pcpkq" targetRef="Activity_01uqrk2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.sourceSystem!=null and orderInfo.sourceSystem="CAINIAO") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:sequenceFlow id="Flow_1fsp131" sourceRef="Activity_01uqrk2" targetRef="Gateway_0mzkgmp" />
    <bpmn:exclusiveGateway id="Gateway_0smudh3" name="是否获取运单号" default="Flow_1e8tnvs">
      <bpmn:incoming>Flow_0himf1s</bpmn:incoming>
      <bpmn:incoming>Flow_1oa4esd</bpmn:incoming>
      <bpmn:outgoing>Flow_0ss94pu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1e8tnvs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0n3a8um" name="是否宅配" default="Flow_0asrtcu">
      <bpmn:incoming>Flow_0ss94pu</bpmn:incoming>
      <bpmn:incoming>Flow_12k6qn7</bpmn:incoming>
      <bpmn:outgoing>Flow_0asrtcu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1riwlkp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ss94pu" name="no" sourceRef="Gateway_0smudh3" targetRef="Gateway_0n3a8um">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=((orderInfo.businessMode="B2B" and orderInfo.deliveryType="ZT") or (orderInfo.businessMode="B2C" and orderInfo.orderType="PO" and orderInfo.deliveryType="ZT") or (orderInfo.orderType="PO" and orderInfo.deliveryType="EXPRESS"))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e8tnvs" name="yes" sourceRef="Gateway_0smudh3" targetRef="Activity_1fv890g" />
    <bpmn:sequenceFlow id="Flow_1g901df" sourceRef="Activity_1fv890g" targetRef="Activity_1uuyg0i" />
    <bpmn:exclusiveGateway id="Gateway_14op3u3" name="是否手工宅配单" default="Flow_0ishgn5">
      <bpmn:incoming>Flow_0asrtcu</bpmn:incoming>
      <bpmn:incoming>Flow_1ri2qfz</bpmn:incoming>
      <bpmn:outgoing>Flow_1di3v18</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ishgn5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0asrtcu" sourceRef="Gateway_0n3a8um" targetRef="Gateway_14op3u3" />
    <bpmn:sequenceFlow id="Flow_1di3v18" name="yes" sourceRef="Gateway_14op3u3" targetRef="Activity_03pnpsd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.sourceSystem="HANDLE" and orderInfo.deliveryType="DOT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1riwlkp" name="yes" sourceRef="Gateway_0n3a8um" targetRef="Activity_0sog07d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="AA") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ri2qfz" sourceRef="Activity_0sog07d" targetRef="Gateway_14op3u3" />
    <bpmn:exclusiveGateway id="Gateway_0exerix" name="是否纯运输" default="Flow_1t7g5e2">
      <bpmn:incoming>Flow_1xqgrkh</bpmn:incoming>
      <bpmn:incoming>Flow_0ishgn5</bpmn:incoming>
      <bpmn:outgoing>Flow_0y71mjc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t7g5e2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xqgrkh" sourceRef="Activity_03pnpsd" targetRef="Gateway_0exerix" />
    <bpmn:sequenceFlow id="Flow_0ishgn5" name="no" sourceRef="Gateway_14op3u3" targetRef="Gateway_0exerix" />
    <bpmn:sequenceFlow id="Flow_0y71mjc" name="yes" sourceRef="Gateway_0exerix" targetRef="Activity_1cv7k8o">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!="YS") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_02vnb2c" name="非自提" default="Flow_15pwtiu">
      <bpmn:incoming>Flow_1t7g5e2</bpmn:incoming>
      <bpmn:incoming>Flow_1wybauv</bpmn:incoming>
      <bpmn:outgoing>Flow_1hgo4ci</bpmn:outgoing>
      <bpmn:outgoing>Flow_15pwtiu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1t7g5e2" name="no" sourceRef="Gateway_0exerix" targetRef="Gateway_02vnb2c" />
    <bpmn:sequenceFlow id="Flow_1hgo4ci" name="yes" sourceRef="Gateway_02vnb2c" targetRef="Activity_0qzipw6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wybauv" sourceRef="Activity_1cv7k8o" targetRef="Gateway_02vnb2c" />
    <bpmn:sequenceFlow id="Flow_15pwtiu" name="no" sourceRef="Gateway_02vnb2c" targetRef="Activity_0f3fs9t" />
    <bpmn:sequenceFlow id="Flow_0n0vsfo" sourceRef="Activity_0qzipw6" targetRef="Activity_0f3fs9t" />
    <bpmn:sequenceFlow id="Flow_01mqa55" sourceRef="Activity_0f3fs9t" targetRef="Gateway_195jet3" />
    <bpmn:sequenceFlow id="Flow_039eniu" sourceRef="Activity_0tlqh7q" targetRef="Gateway_1izoota" />
    <bpmn:sequenceFlow id="Flow_12krfqa" sourceRef="Gateway_1hquluh" targetRef="Activity_16okhsc" />
    <bpmn:sequenceFlow id="Flow_1cof20d" sourceRef="Gateway_1hquluh" targetRef="Activity_195th7r" />
    <bpmn:sequenceFlow id="Flow_1fnx882" sourceRef="Activity_0wtgpmy" targetRef="Gateway_1n1cwyd" />
    <bpmn:sequenceFlow id="Flow_032c74y" sourceRef="Gateway_1n1cwyd" targetRef="Event_14d5skw" />
    <bpmn:sequenceFlow id="Flow_0mvp66j" sourceRef="Activity_16okhsc" targetRef="Gateway_1n1cwyd" />
    <bpmn:sequenceFlow id="Flow_0slmma7" sourceRef="Activity_1lzf3b0" targetRef="Gateway_1n1cwyd" />
    <bpmn:sequenceFlow id="Flow_13j2a01" sourceRef="Activity_195th7r" targetRef="Gateway_1n1cwyd" />
    <bpmn:serviceTask id="Activity_1es0apr" name="质押">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="pledge" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/pledgeCheck" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fnxy33</bpmn:incoming>
      <bpmn:outgoing>Flow_0l9vyri</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:parallelGateway id="Gateway_1hquluh" name="并行">
      <bpmn:incoming>Flow_1gehrtc</bpmn:incoming>
      <bpmn:outgoing>Flow_12krfqa</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cof20d</bpmn:outgoing>
      <bpmn:outgoing>Flow_03g4a4v</bpmn:outgoing>
      <bpmn:outgoing>Flow_1jqb5dn</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Activity_0tlqh7q" name="时效产品">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="aging" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/agingParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_161nz2d</bpmn:incoming>
      <bpmn:outgoing>Flow_039eniu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0smupof" name="大小电解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="orderOutCollabWh" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/setOrderOutCollabWh" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0opo74t</bpmn:incoming>
      <bpmn:outgoing>Flow_0fnxy33</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1xd43z3" name="爆仓">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="isOutArea" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/isOutArea" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0mx8vye</bpmn:incoming>
      <bpmn:outgoing>Flow_1r51i37</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_0p8hiid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_01uqrk2" name="项目分类解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="projectType" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisProjectClassify" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0o9uiqd</bpmn:incoming>
      <bpmn:outgoing>Flow_1fsp131</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e8tnvs</bpmn:incoming>
      <bpmn:outgoing>Flow_1g901df</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0sog07d" name="计费组解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bmsGroup" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/materialGroup5Verify" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1riwlkp</bpmn:incoming>
      <bpmn:outgoing>Flow_1ri2qfz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_03pnpsd" name="第三方解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="thirdPart" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/third/party/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1di3v18</bpmn:incoming>
      <bpmn:outgoing>Flow_1xqgrkh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1cv7k8o" name="装卸计费规则">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bmsFee" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/loadingFeeAnalyze" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0y71mjc</bpmn:incoming>
      <bpmn:outgoing>Flow_1wybauv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0qzipw6" name="计费业务类型解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="busineesFee" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/analysisBusineesFee" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hgo4ci</bpmn:incoming>
      <bpmn:outgoing>Flow_0n0vsfo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0f3fs9t" name="合同校验">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/contractVerification" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_15pwtiu</bpmn:incoming>
      <bpmn:incoming>Flow_0n0vsfo</bpmn:incoming>
      <bpmn:outgoing>Flow_01mqa55</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0wtgpmy" name="同步BMS">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bms" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/syncBms" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1jqb5dn</bpmn:incoming>
      <bpmn:outgoing>Flow_1fnx882</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_16okhsc" name="推送查单系统">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/pushLots" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_12krfqa</bpmn:incoming>
      <bpmn:outgoing>Flow_0mvp66j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1lzf3b0" name="下发末端配送">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="net" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/orderIssued" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_03g4a4v</bpmn:incoming>
      <bpmn:outgoing>Flow_0slmma7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_195th7r" name="生成任务">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="task" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/sentTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1cof20d</bpmn:incoming>
      <bpmn:outgoing>Flow_13j2a01</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1n1cwyd" name="或门&#10;&#10;">
      <bpmn:incoming>Flow_1fnx882</bpmn:incoming>
      <bpmn:incoming>Flow_0mvp66j</bpmn:incoming>
      <bpmn:incoming>Flow_0slmma7</bpmn:incoming>
      <bpmn:incoming>Flow_13j2a01</bpmn:incoming>
      <bpmn:outgoing>Flow_032c74y</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fnxy33" sourceRef="Activity_0smupof" targetRef="Activity_1es0apr" />
    <bpmn:exclusiveGateway id="Gateway_195jet3" name="是否自提" default="Flow_161nz2d">
      <bpmn:incoming>Flow_01mqa55</bpmn:incoming>
      <bpmn:outgoing>Flow_161nz2d</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mwtbnu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161nz2d" name="no" sourceRef="Gateway_195jet3" targetRef="Activity_0tlqh7q" />
    <bpmn:sequenceFlow id="Flow_1mwtbnu" name="yes" sourceRef="Gateway_195jet3" targetRef="Gateway_1izoota">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and (orderInfo.orderType="DO" or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="PI") or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="AI"))) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1izoota" name="中转">
      <bpmn:incoming>Flow_039eniu</bpmn:incoming>
      <bpmn:incoming>Flow_1mwtbnu</bpmn:incoming>
      <bpmn:outgoing>Flow_1gehrtc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1gehrtc" sourceRef="Gateway_1izoota" targetRef="Gateway_1hquluh" />
    <bpmn:serviceTask id="Activity_09m8o30" name="网点解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisNetRange" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_19skgsw</bpmn:incoming>
      <bpmn:outgoing>Flow_1uwckdr</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0aeq2y6" name="是否网点配送" default="Flow_06arwgx">
      <bpmn:incoming>Flow_0p8hiid</bpmn:incoming>
      <bpmn:outgoing>Flow_19skgsw</bpmn:outgoing>
      <bpmn:outgoing>Flow_06arwgx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_19skgsw" name="yes" sourceRef="Gateway_0aeq2y6" targetRef="Activity_09m8o30">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="NET") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_03g4a4v" sourceRef="Gateway_1hquluh" targetRef="Activity_1lzf3b0" />
    <bpmn:exclusiveGateway id="Gateway_1dv10ik" default="Flow_0mx8vye">
      <bpmn:incoming>Flow_0l9vyri</bpmn:incoming>
      <bpmn:outgoing>Flow_0mx8vye</bpmn:outgoing>
      <bpmn:outgoing>Flow_15zlv1x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0mx8vye" name="no" sourceRef="Gateway_1dv10ik" targetRef="Activity_1xd43z3" />
    <bpmn:sequenceFlow id="Flow_15zlv1x" name="yes" sourceRef="Gateway_1dv10ik" targetRef="Gateway_0mzkgmp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.sourceSystem!=null and orderInfo.sourceSystem="PDD") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_1uuyg0i" name="绑定虚拟号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bindingVirtualPhone" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1g901df</bpmn:incoming>
      <bpmn:outgoing>Flow_12k6qn7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_12k6qn7" sourceRef="Activity_1uuyg0i" targetRef="Gateway_0n3a8um" />
    <bpmn:sequenceFlow id="Flow_1jqb5dn" sourceRef="Gateway_1hquluh" targetRef="Activity_0wtgpmy" />
    <bpmn:sequenceFlow id="Flow_0l9vyri" sourceRef="Activity_1es0apr" targetRef="Gateway_1dv10ik" />
    <bpmn:sequenceFlow id="Flow_0p8hiid" sourceRef="Activity_087r4hi" targetRef="Gateway_0aeq2y6" />
    <bpmn:serviceTask id="Activity_0b3mnec" name="电商分类解析3">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/ecommerceCategoriesAnalysisTag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1yhef2t</bpmn:incoming>
      <bpmn:outgoing>Flow_0himf1s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0himf1s" sourceRef="Activity_0b3mnec" targetRef="Gateway_0smudh3" />
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Gateway_1kxnutg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_06arwgx" name="no" sourceRef="Gateway_0aeq2y6" targetRef="Gateway_1kxnutg" />
    <bpmn:exclusiveGateway id="Gateway_1kxnutg" default="Flow_1oa4esd">
      <bpmn:incoming>Flow_1uwckdr</bpmn:incoming>
      <bpmn:incoming>Flow_06arwgx</bpmn:incoming>
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:outgoing>Flow_1yhef2t</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oa4esd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1uwckdr" sourceRef="Activity_09m8o30" targetRef="Gateway_1kxnutg" />
    <bpmn:sequenceFlow id="Flow_1yhef2t" name="yes" sourceRef="Gateway_1kxnutg" targetRef="Activity_0b3mnec">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!=null and (orderInfo.orderType="DP" or orderInfo.orderType="SO")) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1oa4esd" sourceRef="Gateway_1kxnutg" targetRef="Gateway_0smudh3" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd">
      <bpmndi:BPMNEdge id="Flow_0himf1s_di" bpmnElement="Flow_0himf1s">
        <di:waypoint x="1520" y="177" />
        <di:waypoint x="1555" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p8hiid_di" bpmnElement="Flow_0p8hiid">
        <di:waypoint x="1000" y="177" />
        <di:waypoint x="1165" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l9vyri_di" bpmnElement="Flow_0l9vyri">
        <di:waypoint x="440" y="177" />
        <di:waypoint x="475" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jqb5dn_di" bpmnElement="Flow_1jqb5dn">
        <di:waypoint x="997" y="497" />
        <di:waypoint x="997" y="435" />
        <di:waypoint x="839" y="435" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12k6qn7_di" bpmnElement="Flow_12k6qn7">
        <di:waypoint x="1920" y="271" />
        <di:waypoint x="1971" y="271" />
        <di:waypoint x="1971" y="183" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15zlv1x_di" bpmnElement="Flow_15zlv1x">
        <di:waypoint x="500" y="152" />
        <di:waypoint x="500" y="110" />
        <di:waypoint x="840" y="110" />
        <di:waypoint x="840" y="168" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="664" y="92" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mx8vye_di" bpmnElement="Flow_0mx8vye">
        <di:waypoint x="525" y="177" />
        <di:waypoint x="556" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="533" y="177" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03g4a4v_di" bpmnElement="Flow_03g4a4v">
        <di:waypoint x="997" y="547" />
        <di:waypoint x="997" y="610" />
        <di:waypoint x="839" y="610" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06arwgx_di" bpmnElement="Flow_06arwgx">
        <di:waypoint x="1215" y="177" />
        <di:waypoint x="1345" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1329" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gehrtc_di" bpmnElement="Flow_1gehrtc">
        <di:waypoint x="1050" y="522" />
        <di:waypoint x="1022" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mwtbnu_di" bpmnElement="Flow_1mwtbnu">
        <di:waypoint x="1310" y="497" />
        <di:waypoint x="1310" y="460" />
        <di:waypoint x="1100" y="460" />
        <di:waypoint x="1100" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1220" y="442" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161nz2d_di" bpmnElement="Flow_161nz2d">
        <di:waypoint x="1285" y="522" />
        <di:waypoint x="1235" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1254" y="504" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fnxy33_di" bpmnElement="Flow_0fnxy33">
        <di:waypoint x="310" y="177" />
        <di:waypoint x="340" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13j2a01_di" bpmnElement="Flow_13j2a01">
        <di:waypoint x="739" y="716" />
        <di:waypoint x="641" y="716" />
        <di:waypoint x="641" y="547" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0slmma7_di" bpmnElement="Flow_0slmma7">
        <di:waypoint x="739" y="610" />
        <di:waypoint x="641" y="610" />
        <di:waypoint x="641" y="547" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mvp66j_di" bpmnElement="Flow_0mvp66j">
        <di:waypoint x="739" y="522" />
        <di:waypoint x="666" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_032c74y_di" bpmnElement="Flow_032c74y">
        <di:waypoint x="616" y="522" />
        <di:waypoint x="535" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fnx882_di" bpmnElement="Flow_1fnx882">
        <di:waypoint x="739" y="435" />
        <di:waypoint x="641" y="435" />
        <di:waypoint x="641" y="497" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cof20d_di" bpmnElement="Flow_1cof20d">
        <di:waypoint x="997" y="547" />
        <di:waypoint x="997" y="716" />
        <di:waypoint x="839" y="716" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12krfqa_di" bpmnElement="Flow_12krfqa">
        <di:waypoint x="972" y="522" />
        <di:waypoint x="839" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_039eniu_di" bpmnElement="Flow_039eniu">
        <di:waypoint x="1135" y="522" />
        <di:waypoint x="1100" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01mqa55_di" bpmnElement="Flow_01mqa55">
        <di:waypoint x="1370" y="522" />
        <di:waypoint x="1335" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n0vsfo_di" bpmnElement="Flow_0n0vsfo">
        <di:waypoint x="1490" y="632" />
        <di:waypoint x="1420" y="632" />
        <di:waypoint x="1420" y="562" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15pwtiu_di" bpmnElement="Flow_15pwtiu">
        <di:waypoint x="1585" y="522" />
        <di:waypoint x="1470" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1522" y="504" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wybauv_di" bpmnElement="Flow_1wybauv">
        <di:waypoint x="1715" y="632" />
        <di:waypoint x="1630" y="632" />
        <di:waypoint x="1630" y="527" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hgo4ci_di" bpmnElement="Flow_1hgo4ci">
        <di:waypoint x="1610" y="547" />
        <di:waypoint x="1610" y="632" />
        <di:waypoint x="1590" y="632" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1617" y="587" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t7g5e2_di" bpmnElement="Flow_1t7g5e2">
        <di:waypoint x="1845" y="522" />
        <di:waypoint x="1635" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1734" y="504" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y71mjc_di" bpmnElement="Flow_0y71mjc">
        <di:waypoint x="1870" y="547" />
        <di:waypoint x="1870" y="632" />
        <di:waypoint x="1815" y="632" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1877" y="591" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ishgn5_di" bpmnElement="Flow_0ishgn5">
        <di:waypoint x="2170" y="202" />
        <di:waypoint x="2170" y="522" />
        <di:waypoint x="1895" y="522" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2309" y="399" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xqgrkh_di" bpmnElement="Flow_1xqgrkh">
        <di:waypoint x="2320" y="217" />
        <di:waypoint x="2320" y="522" />
        <di:waypoint x="1895" y="522" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ri2qfz_di" bpmnElement="Flow_1ri2qfz">
        <di:waypoint x="2120" y="271" />
        <di:waypoint x="2155" y="271" />
        <di:waypoint x="2155" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1riwlkp_di" bpmnElement="Flow_1riwlkp">
        <di:waypoint x="1990" y="202" />
        <di:waypoint x="1990" y="271" />
        <di:waypoint x="2020" y="271" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1992" y="253" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1di3v18_di" bpmnElement="Flow_1di3v18">
        <di:waypoint x="2195" y="177" />
        <di:waypoint x="2270" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2225" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0asrtcu_di" bpmnElement="Flow_0asrtcu">
        <di:waypoint x="2015" y="177" />
        <di:waypoint x="2145" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g901df_di" bpmnElement="Flow_1g901df">
        <di:waypoint x="1770" y="271" />
        <di:waypoint x="1820" y="271" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e8tnvs_di" bpmnElement="Flow_1e8tnvs">
        <di:waypoint x="1580" y="202" />
        <di:waypoint x="1580" y="271" />
        <di:waypoint x="1670" y="271" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1581" y="232" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ss94pu_di" bpmnElement="Flow_0ss94pu">
        <di:waypoint x="1605" y="177" />
        <di:waypoint x="1965" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1846" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="856" y="152" />
        <di:waypoint x="856" y="110" />
        <di:waypoint x="1350" y="110" />
        <di:waypoint x="1350" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="857" y="122" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fsp131_di" bpmnElement="Flow_1fsp131">
        <di:waypoint x="822" y="271" />
        <di:waypoint x="839" y="271" />
        <di:waypoint x="839" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="881" y="177" />
        <di:waypoint x="900" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="879" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o9uiqd_di" bpmnElement="Flow_0o9uiqd">
        <di:waypoint x="706" y="202" />
        <di:waypoint x="706" y="271" />
        <di:waypoint x="722" y="271" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="708" y="214" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0j1zxrb_di" bpmnElement="Flow_0j1zxrb">
        <di:waypoint x="731" y="177" />
        <di:waypoint x="831" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r51i37_di" bpmnElement="Flow_1r51i37">
        <di:waypoint x="656" y="177" />
        <di:waypoint x="681" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0opo74t_di" bpmnElement="Flow_0opo74t">
        <di:waypoint x="185" y="177" />
        <di:waypoint x="210" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uwckdr_di" bpmnElement="Flow_1uwckdr">
        <di:waypoint x="1320" y="271" />
        <di:waypoint x="1350" y="271" />
        <di:waypoint x="1350" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yhef2t_di" bpmnElement="Flow_1yhef2t">
        <di:waypoint x="1395" y="177" />
        <di:waypoint x="1420" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1399" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oa4esd_di" bpmnElement="Flow_1oa4esd">
        <di:waypoint x="1370" y="202" />
        <di:waypoint x="1370" y="270" />
        <di:waypoint x="1560" y="270" />
        <di:waypoint x="1560" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19skgsw_di" bpmnElement="Flow_19skgsw">
        <di:waypoint x="1190" y="202" />
        <di:waypoint x="1190" y="271" />
        <di:waypoint x="1220" y="271" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1197" y="234" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="149" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="156" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="499" y="504" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="506" y="547" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04pcpkq_di" bpmnElement="Gateway_04pcpkq" isMarkerVisible="true">
        <dc:Bounds x="681" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="686" y="122" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="831" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="843" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0smudh3_di" bpmnElement="Gateway_0smudh3" isMarkerVisible="true">
        <dc:Bounds x="1555" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1567" y="127" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0n3a8um_di" bpmnElement="Gateway_0n3a8um" isMarkerVisible="true">
        <dc:Bounds x="1965" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1969" y="128" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14op3u3_di" bpmnElement="Gateway_14op3u3" isMarkerVisible="true">
        <dc:Bounds x="2145" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2135" y="122" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0exerix_di" bpmnElement="Gateway_0exerix" isMarkerVisible="true">
        <dc:Bounds x="1845" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1878" y="495" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02vnb2c_di" bpmnElement="Gateway_02vnb2c" isMarkerVisible="true">
        <dc:Bounds x="1585" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1595" y="467" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01lp2qq_di" bpmnElement="Activity_1es0apr">
        <dc:Bounds x="340" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02b8aw8_di" bpmnElement="Gateway_1hquluh">
        <dc:Bounds x="972" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="999" y="554" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tz6c6h_di" bpmnElement="Activity_0tlqh7q">
        <dc:Bounds x="1135" y="482" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_059zzl3_di" bpmnElement="Activity_0smupof">
        <dc:Bounds x="210" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1q12own_di" bpmnElement="Activity_1xd43z3">
        <dc:Bounds x="556" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="900" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0n6oi1w_di" bpmnElement="Activity_01uqrk2">
        <dc:Bounds x="722" y="231" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="1670" y="231" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17q9w4e_di" bpmnElement="Activity_0sog07d">
        <dc:Bounds x="2020" y="231" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lelji9_di" bpmnElement="Activity_03pnpsd">
        <dc:Bounds x="2270" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lmvyr1_di" bpmnElement="Activity_1cv7k8o">
        <dc:Bounds x="1715" y="592" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_023kpgh_di" bpmnElement="Activity_0qzipw6">
        <dc:Bounds x="1490" y="592" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0h86k46_di" bpmnElement="Activity_0f3fs9t">
        <dc:Bounds x="1370" y="482" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09uaaug_di" bpmnElement="Activity_0wtgpmy">
        <dc:Bounds x="739" y="395" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ia4nmi_di" bpmnElement="Activity_16okhsc">
        <dc:Bounds x="739" y="482" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xvdpcc_di" bpmnElement="Activity_1lzf3b0">
        <dc:Bounds x="739" y="570" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1aomcsq_di" bpmnElement="Activity_195th7r">
        <dc:Bounds x="739" y="676" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_193q3lp_di" bpmnElement="Gateway_1n1cwyd" isMarkerVisible="true">
        <dc:Bounds x="616" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="644" y="554" width="21" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_195jet3_di" bpmnElement="Gateway_195jet3" isMarkerVisible="true">
        <dc:Bounds x="1285" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1289" y="554" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1izoota_di" bpmnElement="Gateway_1izoota" isMarkerVisible="true">
        <dc:Bounds x="1050" y="497" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1064" y="554" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1dv10ik_di" bpmnElement="Gateway_1dv10ik" isMarkerVisible="true">
        <dc:Bounds x="475" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1uuyg0i_di" bpmnElement="Activity_1uuyg0i">
        <dc:Bounds x="1820" y="231" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0b3mnec_di" bpmnElement="Activity_0b3mnec">
        <dc:Bounds x="1420" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1kxnutg_di" bpmnElement="Gateway_1kxnutg" isMarkerVisible="true">
        <dc:Bounds x="1345" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0aeq2y6_di" bpmnElement="Gateway_0aeq2y6" isMarkerVisible="true">
        <dc:Bounds x="1165" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1160" y="122" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09m8o30_di" bpmnElement="Activity_09m8o30">
        <dc:Bounds x="1220" y="231" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
