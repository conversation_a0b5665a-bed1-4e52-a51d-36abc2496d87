package com.midea.logistics.otplbs.fegin;

/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: ReportFeign
 * @author: abel
 * @date: 2019-09-03 20:26
 * @description:
 */

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.common.helper.bean.OverdueRemarkBatch;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.order.domain.bean.CustomerAgingConfig;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoInMapping;
import com.midea.logistics.otp.order.domain.request.CenterInvQueryReq;
import com.midea.logistics.otp.report.service.domain.ReportRouters;
import com.midea.logistics.otp.report.service.domain.request.CostOrderInfoImportFileRequest;
import com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig;
import com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@FeignClient(value = "logistics-otp-report-service", url = BaseRouter.LOGISTICS_OTP_REPORT_SERVICE)
public interface ReportFeign {

    /**
     * 库存中心导出
     */
    @GetMapping(value = ReportRouters.CENTER_INV)
    JsonResponse centerInvExport(@SpringQueryMap CenterInvQueryReq centerInvQueryReq);

    /**
     * 订单导出
     */
    @GetMapping(value = ReportRouters.EXPORT_ORDER)
    JsonResponse orderExport(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);

    /**
     * 配送方式规则
     *
     * @param shippingTypeRule
     * @throws IOException
     */
    @GetMapping(ReportRouters.EXPORT_SHIPPINGTYPE)
    JsonResponse excelExport(@SpringQueryMap ShippingTypeRule shippingTypeRule);

    /**
     * 任务中心
     *
     * @param searchTaskRequest
     * @throws IOException
     */
    @GetMapping(value = ReportRouters.EXPORT_TASK)
    JsonResponse exportTask(@SpringQueryMap SearchTaskRequest searchTaskRequest);

    /**
     * 客户时效
     *
     * @param customerAgingConfig
     * @return
     */
    @GetMapping(value = ReportRouters.EXPORT_CUSTOMERAGINGCONFIG)
    JsonResponse customerAgingConfig(@SpringQueryMap CustomerAgingConfig customerAgingConfig);


    @GetMapping(ReportRouters.EXPORT_LOADING_FEE_RULE)
    JsonResponse excelExportLoadingFeeRule(@SpringQueryMap LoadingFeeRule loadingFeeRule);

    @GetMapping(ReportRouters.EXPORT_BUSINEES_FEE_CONFIG)
    JsonResponse excelExportBusineesFeeConfig(@SpringQueryMap BusineesFeeConfig busineesFeeConfig);


    /**
     * 客评查询导出
     *
     * @param ids ids
     * @return
     */
    @PostMapping(ReportRouters.EXPORT_PORTALADVICE)
    JsonResponse exportPortalAdvice(@RequestBody List<String> ids);

    /**
     * 菜鸟增值SN信息导出
     */
    @GetMapping(ReportRouters.EXPORT_VALUEADDEDSN)
    JsonResponse exportValueAddedSn(@RequestParam("vdOrderNo") String vdOrderNo);


    /**
     * 运作单位配置表导出
     */
    @PostMapping(ReportRouters.EXPORT_OPERATION_PLATFORM_CONFIG)
    JsonResponse exportOperationPlatformConfig(@RequestBody List<String> ids);

    /**
     * 纯计费订单导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_COST_ORDER_INFO, consumes = "multipart/form-data")
    JsonResponse importCostOrderInfo(MultipartFile file);

    /**
     * 纯计费导入-异步
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_COST_ORDER_INFO_ASYNC)
    JsonResponse importCostOrderInfoAsync(@RequestBody CostOrderInfoImportFileRequest request);

    /**
     * 快递服务单导入-异步
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_EXPRESS_SERVICE_ORDER_INFO_ASYNC)
    JsonResponse importExpressServiceOrderInfoAsync(@RequestBody CostOrderInfoImportFileRequest request);

    /**
     * 业务类型导入-report服务
     * @param request
     * @return
     */
    @PostMapping(ReportRouters.REPORT_IMPORT_BUSINESS_FEE_CONFIG)
    JsonResponse importBusinessFeeConfig(CostOrderInfoImportFileRequest request);

    /**
     * 服务产品规则导入-report服务
     * @param request
     * @return
     */
    @PostMapping(ReportRouters.REPORT_IMPORT_PRODUCT_CONFIG)
    JsonResponse importProductConfig(CostOrderInfoImportFileRequest request);

    /**
     * 订单中心导入-report服务
     * @param request
     * @return
     */
    @PostMapping(ReportRouters.REPORT_IMPORT_ORDER)
    JsonResponse importOrder(CostOrderInfoImportFileRequest request);


    /**
     * 时效导入-report服务
     * @param request
     * @return
     */
    @PostMapping(ReportRouters.CUSTOMER_AGING_CONFIG_IMPORT)
    JsonResponse importCustomerAgingConfig(CostOrderInfoImportFileRequest request);

    /**
     * 前置预约仓库配置导入-report服务
     * @param request
     * @return
     */
    @PostMapping(ReportRouters.PRE_APPOINTMENT_CONFIG_IMPORT)
    JsonResponse importPreApptConfig(@RequestBody ImportFileRequest request);

    /**
     * 运作单位配置导入-report服务
     * @param request
     * @return
     */
    @PostMapping(ReportRouters.OPERATION_PLATFORM_CONFIG_IMPORT)
    JsonResponse importOperationPlatformConfig(@RequestBody ImportFileRequest request);

    /**
     * VIP客户配置导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_VIP_CUSTOMER_CONFIG)
    JsonResponse importVipCustomerConfig(@RequestBody ImportFileRequest request);

    /**
     * 配送方式规则导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_SHIPPING_TYPE_RULE)
    JsonResponse importShippingTypeRule(@RequestBody ImportFileRequest request);

    /**
     * 装卸计费规则导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_LOADING_FEE_RULE)
    JsonResponse importLoadingFeeRule(@RequestBody ImportFileRequest request);
    
    /**
     * 发货短信配置导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_MESSAGE_CONFIG)
    JsonResponse importMessageConfig(@RequestBody ImportFileRequest request);

    /**
     * 地址覆盖范围导入-report服务
     * @param request
     * @return
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_ADDRESS_TO_SITE)
    JsonResponse importAddressToSite(@RequestBody ImportFileRequest request);

    /**
     * 更改订单导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_CHANGE_APPLY)
    JsonResponse importChangeApply(@RequestBody ImportFileRequest request);
    /**
     * 时效导入-report服务
     * @param request
     * @return
     */
    @PostMapping(value = ReportRouters.LINE_AGING_IMPORT)
    JsonResponse importLineAging(CostOrderInfoImportFileRequest request);
    
    /**
     * 客户时效规则导入-report服务
     * @param request
     * @return
     */
    @PostMapping(ReportRouters.CUSTOMER_AGING_RULE_CONFIG_IMPORT)
    JsonResponse customerAgingRuleConfigImport(CostOrderInfoImportFileRequest request);

    /**
     * !@导入 - 统配计费规则导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_TC_RULE)
    JsonResponse importTcRule(@RequestBody ImportFileRequest request);

    /**
     * 逾期备注
     * @return
     */
    @PostMapping(value = ReportRouters.OVERDUE_REMARK)
    JsonResponse overdueRemark(@RequestBody OverdueRemarkBatch overdueRemarkBatch);
    /**
     * !@导入 - 厨热-店铺预算部门设置(非京东)导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_KCN_ENTY_EC_STLPT_CFG)
    JsonResponse importKcnEntyEcStlptCfg(@RequestBody ImportFileRequest request);
    /**
     * !@导入 - 厨热-店铺预算部门设置(京东)导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_KCN_ENTY_EC_STLPT_JD_CFG)
    JsonResponse importKcnEntyEcStlptJdCfg(@RequestBody ImportFileRequest request);
    /**
     * 导入客户合同时效配置
     * @Date: 2023/9/13
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_CUSTOMER_CONTRACT_AGING_CONFIG)
    JsonResponse importCustomerContractAgingConfig(CostOrderInfoImportFileRequest request);

    /**
     * 精准预约配置导入
     */
    @PostMapping(value = "/report/import/accurate/appoint")
    JsonResponse importAccurateAppoint(@RequestBody ImportFileRequest request);

    /**
     * 增值服务订单导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_VD_ORDER)
    JsonResponse importVdOrder(@RequestBody ImportFileRequest request);


    /**
     * 时效容量管控导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_CAPACITY_CONTROL)
    JsonResponse capacityControl(@RequestBody ImportFileRequest request);

    /**
     * vp客户配置导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_VP_CUSTOMER_CONFIG)
    JsonResponse importVpCustomerConfig(@RequestBody ImportFileRequest request);
    /**
     * 标签导入-report服务
     * @param request
     * @return
     */
    @PostMapping(value = ReportRouters.TAG_IMPORT)
    JsonResponse importTag(CostOrderInfoImportFileRequest request);

    /**
     * 跨区时效配置导入
     */
    @PostMapping(value = ReportRouters.REPORT_IMPORT_OVERAREA_AGING_CONFIG)
    JsonResponse importOverareaAgingConfig(@RequestBody ImportFileRequest request);

    /**
     * 3PL售达方配置导入
     */
    @PostMapping(value = ReportRouters.REPORT_CUSTOMER_SOLD_TO_CONFIG)
    JsonResponse importCustomerSoldToConfig(@RequestBody ImportFileRequest request);
}
