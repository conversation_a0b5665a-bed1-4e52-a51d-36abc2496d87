package com.midea.logistics.otplbs.fegin.agg;

import com.midea.logistics.otp.bean.agg.StandardAgingNode;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "StandardAgingNodeCommonFeign")
public interface StandardAgingNodeCommonFeign {

    @GetMapping("/standard/aging/node/list")
    JsonResponse<List<StandardAgingNode>> standardAgingNodeList(@SpringQueryMap StandardAgingNode entity);

    @GetMapping("/standard/aging/node/page")
    JsonResponse standardAgingNodePage(@SpringQueryMap StandardAgingNode entity);

    @GetMapping("/standard/aging/node/count")
    JsonResponse<BaseDomain> standardAgingNodeCount(@SpringQueryMap StandardAgingNode entity);

    @GetMapping("/standard/aging/node/detail")
    JsonResponse<StandardAgingNode> standardAgingNodeDetail(@RequestParam("id") Long id);

    @GetMapping("/standard/aging/node/detail")
    JsonResponse<StandardAgingNode> standardAgingNodeDetail(@SpringQueryMap StandardAgingNode entity);

    @PostMapping("/standard/aging/node/add")
    JsonResponse<BaseDomain> standardAgingNodeAdd(@RequestBody StandardAgingNode entity);

    @PutMapping("/standard/aging/node/update")
    JsonResponse<Integer> standardAgingNodeUpdate(@RequestBody StandardAgingNode entity);

    @DeleteMapping("/standard/aging/node/remove")
    JsonResponse<Integer> standardAgingNodeRemove(@RequestBody StandardAgingNode entity);


    @GetMapping("/standardAgingNode/tree")
    JsonResponse agingNodeTree(@SpringQueryMap StandardAgingNode req);

    @GetMapping("/standardAgingNode/page")
    JsonResponse agingNodePage(@SpringQueryMap StandardAgingNode req);

    @PostMapping("/standardAgingNode/update")
    JsonResponse update(@RequestBody List<Map<String,Object>> req);
}

