package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName:WmsOwnerTransferConfirmRequest
 * Author: caixz
 * Date: 2019-7-26 11:26
 * Description:菜鸟货权转移请求实体
 */
@Data
public class WmsOwnerTransferConfirmRequest implements Serializable {

    private String orderCode;
    private String serviceCode;
    private String outBizCode;
    private Integer confirmType;
    private Date orderConfirmTime;
    private String timeZone;
    private List<WmsTransferOrderConfirmPairItem> transferItems;
    private List<CheckItem> checkItems;

    @Data
    public class  WmsTransferOrderConfirmPairItem{
        /**
         * 数量
         */
        private Integer quantity;
        /**
         * 货权转移单明细ID
         */
        private String orderItemId;
        /**
         *  原商品信息
         */
        private WmsTransferOrderConfirmOrderItem sourceItem;
        /**
         *  目标商品
         */
        private WmsTransferOrderConfirmOrderItem targetItem;
    }

    @Data
    public class CheckItem{

        private String orderItemId;
        private int sourceQuantity;
        private int targetQuantity;
    }
    @Data
    public class WmsTransferOrderConfirmOrderItem{
        /**
         * 库存类型（1良品；101残品）
         */
        private Integer inventoryType;
        /**
         * 批次号
         */
        private String batchCode;
        /**
         * 失效日期
         */
        private Date dueDate;
        /**
         * 生产日期
         */
        private Date produceDate;
        /**
         * 生产编码，同一商品可能因商家不同有不同编码
         */
        private String produceCode;
        private String snCode;
    }

}
