package com.midea.logistics.imp.orderverify.helper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderExceptionFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderTraceFeign;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.es.EsOrderLogService;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.OrderStatusCheckHelper;
import com.midea.logistics.otp.order.common.mq.producer.LogisticsRouteToLotsProducer;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.request.OrderTraceRequest;
import com.midea.logistics.otp.order.domain.response.OrderExcuteCountRespone;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.thread.ThreadLocals;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

import static com.midea.logistics.otp.enums.OrderOperateType.APART;

/**
 * 订单日志记录辅助类
 */
@Component
public class LmpOrderLogHelper {

    private static final Logger logger = LoggerFactory.getLogger(LmpOrderLogHelper.class);

    @Autowired
    private OrderTraceFeign orderTraceFeign;
    @Autowired
    private OrderExceptionFeign orderExceptionFeign;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private LogisticsRouteToLotsProducer logisticsRouteToLotsProducer;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private EsOrderLogService esOrderLogServiceImpl;
    @Autowired
    private OrderStatusCheckHelper orderStatusCheckHelper;

    /**
     * 插入订单日志【成功和失败都记录】
     */
    public void createOrderLog(CustomerOrderInfo customerOrderInfo, FlowListenerParam param, String operateType) {
        OrderLog orderLog = new OrderLog();
        orderLog.setOperateFlag(param.getFlowStatus() == FlowStatus.SUCCESS ? "Y" : "N");
        orderLog.setOperateContent(param.getErrorMsg());
        orderLog.setOperateType(operateType);
        orderLog.setParentOrderNo(customerOrderInfo.getOrderNo());
        orderLog.setCustomerCode(customerOrderInfo.getCustomerCode());
        orderLog.setCustomerOrderNo(customerOrderInfo.getCustomerOrderNo());
        esOrderLogServiceImpl.saveLog(orderLog);
    }

    public void createOrderLog(OrderInfo orderInfo, FlowListenerParam param, String operateType) {
        OrderLog orderLog = new OrderLog();
        orderLog.setOperateFlag(param.getFlowStatus() == FlowStatus.SUCCESS ? "Y" : "N");
        orderLog.setOperateContent(param.getErrorMsg());
        if (OrderOperateType.AGING_PRODUCT.getKey().equals(operateType)){
            Object operateType1 = ThreadLocals.get("operateType");
            operateType = ""+operateType1;
        }
        orderLog.setOperateType(operateType);
        orderLog.setOrderNo(orderInfo.getOrderNo());
        orderLog.setParentOrderNo(orderInfo.getParentOrderNo());
        orderLog.setCustomerCode(orderInfo.getCustomerCode());
        orderLog.setCustomerOrderNo(orderInfo.getCustomerOrderNo());
        esOrderLogServiceImpl.saveLog(orderLog);
    }

    /**
     * 插入订单路由信息【用于推送路由信息】
     */
    public void createOrderTrace(CustomerOrderInfo customerOrderInfo, OrderStatus orderStatus) {
        // 只要指定的节点
        if (!(orderStatus == OrderStatus.NEW || orderStatus == OrderStatus.AUDITED)) {
            logger.info("=========> 订单路由无需推送, 客户单号:{}, 订单状态节点:{}", customerOrderInfo.getCustomerOrderNo(), orderStatus.getValue());
            return;
        }
        logger.info("=========> 记录 trace 信息, 客户单号:{}, 订单状态节点:{}", customerOrderInfo.getCustomerOrderNo(), orderStatus.getValue());
        OrderTrace orderTrace = new OrderTrace();
        orderTrace.setParentOrderNo(customerOrderInfo.getOrderNo());
        orderTrace.setCustomerOrderNo(customerOrderInfo.getCustomerOrderNo());
        orderTrace.setBusinessTime(new Date());
        orderTrace.setWhCode(customerOrderInfo.getWhCode());
        orderTrace.setWhName(customerOrderInfo.getWhName());
        orderTrace.setOrderStatus(orderStatus.getKey());
        orderTrace.setSourceSystem(customerOrderInfo.getSourceSystem());
        JsonResponse<Integer> integerJsonResponse = orderTraceFeign.create(orderTrace);
        if (integerJsonResponse == null || !"0".equals(integerJsonResponse.getCode())) {
            throw BusinessException.fail("路由保存失败");
        }

    }

    /**
     * 插入订单异常记录
     */
    private void createOrderException(CustomerOrderInfo customerOrderInfo, String operateType, String exceptionType, String errMsg) {
        ExceptionType eType = ExceptionType.UNKNOW;
        if (EnumUtils.isValidEnum(ExceptionType.class, exceptionType)) {
            eType = ExceptionType.valueOf(exceptionType);
        }
        if (!ExceptionType.NATURAL.name().equals(exceptionType)) {
            OrderException orderException = new OrderException();
            orderException.setCustomerOrderNo(customerOrderInfo.getCustomerOrderNo());
            orderException.setParentOrderNo(customerOrderInfo.getOrderNo());
            orderException.setOperateContent(errMsg);
            orderException.setOperateType(operateType);
            orderException.setExceptionStatus(OrderExceptionStatus.NEW.getKey());
            orderException.setSiteCode(customerOrderInfo.getSiteCode());
            orderException.setSiteName(customerOrderInfo.getSiteName());
            orderException.setWhCode(customerOrderInfo.getWhCode());
            orderException.setWhName(customerOrderInfo.getWhName());
            orderException.setExceptionType(eType.getKey());
            orderException.setExceptionDesc(eType == ExceptionType.UNKNOW ? eType.getValue() + ":" + errMsg : eType.getValue());

            JsonResponse jsonResponse = orderExceptionFeign.create(orderException);
            if (jsonResponse == null || !"0".equals(jsonResponse.getCode())) {
                throw BusinessException.fail("日志保存失败");
            }
        }
    }

    private void createOrderException(OrderInfo orderInfo, String operateType, String exceptionType, String errMsg) {
        ExceptionType eType = ExceptionType.UNKNOW;
        if (EnumUtils.isValidEnum(ExceptionType.class, exceptionType)) {
            eType = ExceptionType.valueOf(exceptionType);
        }
        if (!ExceptionType.NATURAL.name().equals(exceptionType)) {
            OrderException orderException = new OrderException();
            orderException.setCustomerOrderNo(orderInfo.getCustomerOrderNo());
            orderException.setOrderNo(orderInfo.getOrderNo());
            orderException.setParentOrderNo(orderInfo.getParentOrderNo());
            orderException.setOperateContent(errMsg);
            orderException.setOperateType(operateType);
            orderException.setExceptionStatus(OrderExceptionStatus.NEW.getKey());
            orderException.setSiteCode(orderInfo.getSiteCode());
            orderException.setSiteName(orderInfo.getSiteName());
            orderException.setWhCode(orderInfo.getWhCode());
            orderException.setWhName(orderInfo.getWhName());
            orderException.setExceptionType(eType.getKey());
            orderException.setExceptionDesc(eType == ExceptionType.UNKNOW ? eType.getValue() + ":" + errMsg : eType.getValue());

            JsonResponse jsonResponse = orderExceptionFeign.create(orderException);
            if (jsonResponse == null || !"0".equals(jsonResponse.getCode())) {
                throw BusinessException.fail("日志保存失败");
            }
        }

    }


    /**
     * 客户订单状态更新
     *
     * @param customerOrderInfo
     * @param param
     * @param operateType
     */
    public void changeOrderStatus(CustomerOrderInfo customerOrderInfo, FlowListenerParam param, String operateType) {
        boolean changeFlag = false;
        if (StringUtils.isNotBlank(param.getExceptionType())) {
            createOrderException(customerOrderInfo, operateType, param.getExceptionType(), param.getErrorMsg());
        }
        if (SourceSystem.SCC.getKey().equals(customerOrderInfo.getSourceSystem())
                && WhCodeEnum.W00524.getKey().equals(customerOrderInfo.getWhCode())
                && OrderStatus.INBOUND.getKey().equals(customerOrderInfo.getOrderStatus())
                && ExcuteStatus.CLOSED.getKey().equals(customerOrderInfo.getExcuteStatus())) {
            // 当前情况跳出更新
            logger.info("zeebe_flow_{}_{}_orderStatus: {}, excuteStatus: {}", operateType, customerOrderInfo.getOrderNo(), customerOrderInfo.getOrderStatus(), customerOrderInfo.getExcuteStatus());
            return ;
        }
        // 此次异常
        if (param.getExceptionType() != null) {
            customerOrderInfo.setExceptionType(param.getExceptionType());
            customerOrderInfo.setExceptionDesc(param.getErrorMsg());
            customerOrderInfo.setOrderStatus(OrderStatus.AUDIT_FAILE.getKey());
            customerOrderInfo.setExcuteStatus(OrderStatus.AUDIT_FAILE.getKey());
            changeFlag = true;
        }
        // 异常转正常
        if (param.getExceptionType() == null) {
            if (!"-".equals(customerOrderInfo.getExceptionType())) {
                customerOrderInfo.setExceptionType("-");
                //2024年8月28日18:52:24 如果是分仓过来，不设置 -
                if (! APART.getKey().equals(operateType)){
                    customerOrderInfo.setExceptionDesc("-");
                }
                changeFlag = true;
            }
            // 正常
            if (!customerOrderInfo.getOrderStatus().equals(200) && !customerOrderInfo.getOrderStatus().equals(150)) {
                customerOrderInfo.setOrderStatus(OrderStatus.AUDITING.getKey());
                changeFlag = true;
            }
            if (!customerOrderInfo.getExcuteStatus().equals(200) && !customerOrderInfo.getExcuteStatus().equals(150)) {
                customerOrderInfo.setExcuteStatus(OrderStatus.AUDITING.getKey());
                changeFlag = true;
            }
            // 已审核
            if (CommonConstant.APARTED.equals(customerOrderInfo.getApartStatus())) {
                customerOrderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
                customerOrderInfo.setExcuteStatus(OrderStatus.AUDITED.getKey());
                changeFlag = true;
            }
        }

        if (changeFlag) {
            logger.info("zeebe_flow_{}_{}_orderStatus: {}, excuteStatus: {}", operateType, customerOrderInfo.getOrderNo(), customerOrderInfo.getOrderStatus(), customerOrderInfo.getExcuteStatus());
            CustomerOrderInfo statusParam = new CustomerOrderInfo();
            statusParam.setId(customerOrderInfo.getId());
            statusParam.setOrderNo(customerOrderInfo.getOrderNo());
            statusParam.setOrderStatus(customerOrderInfo.getOrderStatus());
            statusParam.setExcuteStatus(customerOrderInfo.getExcuteStatus());
            statusParam.setExceptionType(customerOrderInfo.getExceptionType());
            statusParam.setExceptionDesc(customerOrderInfo.getExceptionDesc());
            orderInfoFeign.updateCustomerOrderInfoStatusByFlow(statusParam);

            param.setExcuteStatus(statusParam.getExcuteStatus());
        }

    }

    /**
     * 子订单状态更新
     *
     * @param orderInfo
     * @param param
     * @param operateType
     */
    public void changeOrderStatus(OrderInfo orderInfo, FlowListenerParam param, String operateType) {
        boolean changeFlag = false;
        if (StringUtils.isNotBlank(param.getExceptionType())) {
            createOrderException(orderInfo, operateType, param.getExceptionType(), param.getErrorMsg());
        }
        Integer oldOrderStatus = orderInfo.getOrderStatus();
        //获取流程状态
        Integer flowStatus = orderStatusCheckHelper.getOrderInfoStatus(orderInfo.getOrderNo());
        boolean isIssuedServiceFlag = OrderOperateType.ISSUED_SERVICE.getKey().equals(operateType);
        // 此次异常
        if (param.getExceptionType() != null) {
            orderInfo.setExceptionType(param.getExceptionType());
            orderInfo.setExceptionDesc(param.getErrorMsg());
            orderInfo.setOrderStatus(OrderStatus.AUDIT_FAILE.getKey());
            orderInfo.setExcuteStatus(OrderStatus.AUDIT_FAILE.getKey());
            changeFlag = true;
            //下发网点新增网点接单失败 下发csp的时候已经更新此处不更新
            if (OrderStatus.TO_NET_FAILE.getKey().equals(flowStatus)) {
                return;
            }
        }
        // 异常转正常
        if (param.getExceptionType() == null) {
            if (!"-".equals(orderInfo.getExceptionType())) {
                orderInfo.setExceptionType("-");
                orderInfo.setExceptionDesc("-");
                changeFlag = true;
            }
            // 正常
            if (!orderInfo.getOrderStatus().equals(200) && !orderInfo.getOrderStatus().equals(150)) {
                orderInfo.setOrderStatus(OrderStatus.AUDITING.getKey());
                changeFlag = true;
            }
            if (!orderInfo.getExcuteStatus().equals(200) && !orderInfo.getExcuteStatus().equals(150)) {
                orderInfo.setExcuteStatus(OrderStatus.AUDITING.getKey());
                changeFlag = true;
            }
            // 已审核
            if ("ISSUED_SERVICE".equals(operateType) || "ISSUED_TASK".equals(operateType) || "SYNC_BMS".equals(operateType) || "PUSH_LOTS".equals(operateType)) {
                orderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
                orderInfo.setExcuteStatus(OrderStatus.AUDITED.getKey());
                changeFlag = true;
                //推查单
                if ("PUSH_LOTS".equals(operateType)) {
                    syncLots(orderInfo);
                }
                //下发网点成功 下发csp的时候已经更新此处不更新
                if (isIssuedServiceFlag) {
                    return;
                }
            }
        }
        //下发售后订单若下发过，状态就不改成小于下发售后的状态
        if (orderStatusCheckHelper.checkNetReceiveStatusYet(oldOrderStatus,orderInfo.getOrderStatus(),flowStatus)) {
            return;
        }

        if (changeFlag) {
            logger.info("zeebe_flow_{}_{}_orderStatus: {}, excuteStatus: {}", operateType, orderInfo.getOrderNo(), orderInfo.getOrderStatus(), orderInfo.getExcuteStatus());
            OrderInfo statusParam = new OrderInfo();
            statusParam.setId(orderInfo.getId());
            statusParam.setOrderNo(orderInfo.getOrderNo());
            statusParam.setOrderStatus(orderInfo.getOrderStatus());
            statusParam.setExcuteStatus(orderInfo.getExcuteStatus());
            statusParam.setExceptionType(orderInfo.getExceptionType());
            statusParam.setExceptionDesc(orderInfo.getExceptionDesc());
            orderInfoFeign.updateOrderInfoStatusByFlow(statusParam);

            //下发网点这里不更新状态了，因为取的线程变量的状态会覆盖数据库状态
            if (isIssuedServiceFlag) {
                return;
            }

            /**
             * 【执行状态】
             * 子单审核失败，成功的时候，要考虑父单的情况，规则：
             *
             * 1、子单全部审核成功，父单状态为审核成功【一定是完成拆单的情况，如果未完成拆单，仍然是审核中】
             * 2、子单只有其中一个失败的，父单显示为部分审核失败
             * 3、部分成功，部分审核中，状态为审核中
             * 4、子单全部失败的，显示为审核失败 【未完成拆单的也是这样显示】
             *
             * 状态：
             * 1. 分仓完成
             *      1.1 子单全部成功：审核成功
             *      1.2 子单全部失败：审核失败
             *      1.3 子单部分失败：审核部分失败
             *      1.4 子单审核中（无失败）：审核中
             *
             * 2. 分仓未完成
             *      2.1 子单全部成功：审核中
             *      2.2 子单全部失败：审核部分失败
             *      2.3 子单部分失败：审核部分失败
             *      2.4 子单审核中（无失败）：审核中
             */

            // 查询父单状态
            CustomerOrderInfo customerOrderInfo = orderFlowHelper.getCustomerOrderInfo(orderInfo.getParentOrderNo());
            ExcuteStatus customerExcuteStatus = ExcuteStatus.getStatus(customerOrderInfo.getExcuteStatus());

            // 统计子单成功失败数量
            JsonResponse<OrderExcuteCountRespone> statusRep = orderInfoFeign.countOrderExcuteStatus(customerOrderInfo.getOrderNo());
            if(null == statusRep || null == statusRep.getData()){
                return;
            }
            OrderExcuteCountRespone data = statusRep.getData();
            Integer excuteFaild = data.getExcuteFaild();
            Integer excuteSuccess = data.getExcuteSuccess();
            // Integer excuteProcessing = data.getExcuteProcessing();
            Integer total = data.getTotal();
            boolean updateFlag = false;

            // 1. 分仓完成
            if (CommonConstant.APARTED.equalsIgnoreCase(customerOrderInfo.getApartStatus())) {
                //  1.1 子单全部成功：审核成功
                if (total.equals(excuteSuccess) && customerExcuteStatus != ExcuteStatus.AUDITED) {
                    customerOrderInfo.setExceptionDesc("-");
                    customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITED.getKey());
                    updateFlag = true;
                }
                // 1.2 子单全部失败：审核失败
//                if (total.equals(excuteFaild) && customerExcuteStatus != ExcuteStatus.FAILD) {
//                    customerOrderInfo.setExcuteStatus(ExcuteStatus.FAILD.getKey());
//                    updateFlag = true;
//                }
                // 1.3 子单部分失败：审核部分失败
//                if (!total.equals(excuteSuccess) && !total.equals(excuteFaild) && excuteFaild != 0 && customerExcuteStatus != ExcuteStatus.AUDIT_PART_FAILE) {
//                    customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDIT_PART_FAILE.getKey());
//                    updateFlag = true;
//                }
                //cyy:子单审核节点出现异常，则更新父单的【订单状态】、【审核状态】为【审核失败-199】,不需要判断是否为部分异常，去掉审核状态【155-部分审核失败】
                 if (excuteFaild != 0 && customerExcuteStatus != ExcuteStatus.FAILD) {
                    customerOrderInfo.setExcuteStatus(ExcuteStatus.FAILD.getKey());
                    updateFlag = true;
                }
                // 1.4 子单审核中（无失败）：审核中
                if (!total.equals(excuteSuccess) && excuteFaild == 0 && customerExcuteStatus != ExcuteStatus.AUDITING) {
                    customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
                    updateFlag = true;
                }
            }
            // 2. 分仓未完成
            if (!CommonConstant.APARTED.equalsIgnoreCase(customerOrderInfo.getApartStatus())) {
                //  2.1 子单全部成功：审核中
                if (total.equals(excuteSuccess) && customerExcuteStatus != ExcuteStatus.AUDITING) {
                    customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
                    updateFlag = true;
                }
                //  2.2 子单全部失败：审核部分失败
//                if (total.equals(excuteFaild) && customerExcuteStatus != ExcuteStatus.AUDIT_PART_FAILE) {
//                    customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDIT_PART_FAILE.getKey());
//                    updateFlag = true;
//                }
//                //  2.3 子单部分失败：审核部分失败
//                if (!total.equals(excuteSuccess) && !total.equals(excuteFaild) && excuteFaild != 0 && customerExcuteStatus != ExcuteStatus.AUDIT_PART_FAILE) {
//                    customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDIT_PART_FAILE.getKey());
//                    updateFlag = true;
//                }
                //cyy:子单审核节点出现异常，则更新父单的【订单状态】、【审核状态】为【审核失败-199】,不需要判断是否为部分异常，去掉审核状态【155-部分审核失败】
                if (excuteFaild != 0 && customerExcuteStatus != ExcuteStatus.FAILD) {
                    customerOrderInfo.setExcuteStatus(ExcuteStatus.FAILD.getKey());
                    updateFlag = true;
                }
                // 2.4 子单审核中（无失败）：审核中
                if (!total.equals(excuteSuccess) && excuteFaild == 0 && customerExcuteStatus != ExcuteStatus.AUDITING) {
                    customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
                    updateFlag = true;
                }
            }


            if (updateFlag) {
                logger.info("=========> 子单{}审核状态变更，父单{}将置为新状态{}", orderInfo.getOrderNo(), customerOrderInfo.getOrderNo(), customerOrderInfo.getExcuteStatus());
                CustomerOrderInfo info = new CustomerOrderInfo();
                info.setId(customerOrderInfo.getId());
                info.setOrderNo(customerOrderInfo.getOrderNo());
                info.setOrderStatus(customerOrderInfo.getOrderStatus());
                info.setExcuteStatus(customerOrderInfo.getExcuteStatus());
                info.setExceptionType(customerOrderInfo.getExceptionType());
                info.setExceptionDesc(customerOrderInfo.getExceptionDesc());
                orderInfoFeign.updateCustomerOrderInfoStatusByFlow(info);
                //更新完状态要清一下线程变量,否则若是一个节点包含多个流程的,这些流程更新状态的时候判断的是进入节点时候的状态
                orderFlowHelper.clear(orderInfo.getParentOrderNo());

                param.setExcuteStatus(info.getExcuteStatus());
            }
        }
    }


    /**
     * @description: 保存取消日志
     * @param: [orderInfo, type, stringFlagYes]
     * @return: void
     * @author: 陈永培
     * @createtime: 2019/9/3 11:11
     */
    @Deprecated
    public void saveLog(OrderInfo orderInfo, OrderOperateType type, String operateFlag, String message) {
        String msg = "";
        if (StringUtils.isNotEmpty(operateFlag)) {
            msg = CommonConstant.STRING_FLAG_YES.equals(operateFlag) ? "成功" : "失败  ";
        }
        String otherMsg = "";
        if (StringUtils.isNotEmpty(message)) {
            otherMsg = message;
        }
        OrderLog orderLog = new OrderLog();
        orderLog.setParentOrderNo(orderInfo.getOrderNo());
        BeanUtils.copyProperties(orderInfo, orderLog);
        orderLog.setId(null);
        orderLog.setCreateUserCode(null);
        orderLog.setCreateTime(null);
        orderLog.setOperateType(type.getKey());
        orderLog.setOperateContent(msg + otherMsg);
        orderLog.setOperateFlag(operateFlag);
        esOrderLogServiceImpl.saveLog(orderLog);
    }

    public void syncLots(OrderInfo orderInfo) {
        OrderTrace orderTraceParam = new OrderTrace();
        orderTraceParam.setParentOrderNo(orderInfo.getParentOrderNo());
        orderTraceParam.setPageSize(Integer.MAX_VALUE);
        JsonResponse<PageResponse<OrderTrace>> search = orderTraceFeign.search(orderTraceParam);
        if (search == null || !"0".equals(search.getCode())) {
            throw BusinessException.fail("查询trace 失败");
        }
        List<OrderTrace> traces = search.data.list;
        traces = traces.stream().filter(trace -> trace.getOrderStatus() <= 200).distinct().collect(Collectors.toList());

        logger.info("=========> 记录 trace 信息, 客户单号:{}, 订单状态节点:{}, 节点数:{}",
            orderInfo.getCustomerOrderNo(),
            OrderStatus.AUDITED.getValue(),
            traces.size()
        );

        List<OrderTrace> traceList = new ArrayList<>();
        for (OrderTrace trace : traces) {
            // 过滤掉撤回的子单
            if(StringUtils.isBlank(trace.getOrderNo()) || Objects.equals(trace.getOrderNo(),orderInfo.getParentOrderNo()) || Objects.equals(trace.getOrderNo() ,orderInfo.getOrderNo())){
                trace.setOrderNo(orderInfo.getOrderNo());
                traceList.add(trace);
            }
        }

        for (OrderTrace trace : traceList) {
            OrderTraceRequest orderTraceRequest = new OrderTraceRequest();
            BeanUtils.copyProperties(trace, orderTraceRequest);
            boolean sent = logisticsRouteToLotsProducer.sent(orderTraceRequest);
            if (sent) {
                logger.info("=========> 订单路由推送查单成功, 单号:{}, 订单状态节点:{}", trace.getOrderNo(), OrderStatus.AUDITED.getValue());
            } else {
                String format = String.format("订单路由推送查单失败, 单号:%s, 订单状态节点:%s", trace.getOrderNo(), OrderStatus.AUDITED.getValue());
                logger.error(format);
                OrderLog orderLog = new OrderLog();
                orderLog.setOperateFlag(CommonConstant.STRING_FLAG_YES);
                orderLog.setOperateContent(format);
                orderLog.setOrderNo(orderInfo.getOrderNo());
                orderLog.setParentOrderNo(orderInfo.getParentOrderNo());
                orderLog.setOperateType(OrderOperateType.PUSH_LOTS_NODE.getKey());
                orderLog.setCustomerCode(orderInfo.getCustomerCode());
                orderLog.setCustomerOrderNo(orderInfo.getCustomerOrderNo());
                esOrderLogServiceImpl.saveLog(orderLog);
                throw BusinessException.fail(format);
            }
        }

    }

    /**
     * @description: 保存日志
     * @param: [task, orderInfo]
     * @return: void
     * @author: 陈永培
     * @createtime: 2019/9/1 15:23
     */
    @Deprecated
    public void saveLog(Task task, OrderOperateType type, String operateFlag, String message) {
        String msg = "";
        if (StringUtils.isNotEmpty(operateFlag)) {
            msg = CommonConstant.STRING_FLAG_YES.equals(operateFlag) ? "成功" : "失败  ";
        }
        String otherMsg = "";
        if (StringUtils.isNotEmpty(message)) {
            otherMsg = message;
        }
        OrderLog orderLog = new OrderLog();
        orderLog.setCustomerOrderNo(task.getCustomerOrderNo());
        orderLog.setCustomerCode(task.getCustomerCode());
        orderLog.setOrderNo(task.getOrderNo());
        orderLog.setParentOrderNo(task.getParentOrderNo());
        orderLog.setTaskNo(task.getTaskNo());
        orderLog.setOperateType(type.getKey());
        orderLog.setOperateContent(msg + otherMsg);
        orderLog.setOperateFlag(operateFlag);
        esOrderLogServiceImpl.saveLog(orderLog);
    }


}
