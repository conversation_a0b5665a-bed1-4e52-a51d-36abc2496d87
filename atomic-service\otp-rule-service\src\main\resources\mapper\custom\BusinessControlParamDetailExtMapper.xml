<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.BusinessControlParamDetailExtMapper">

    <sql id="searchFieldsSql">
            `order_type` AS  orderType,
            `source_system` AS  sourceSystem,
            `dimension_code` AS  dimensionCode,
            `site_name` AS  siteName,
            `customer_code` AS  customerCode,
            `remark` AS  remark,
            `parameter_value` AS  parameterValue,
            `delete_flag` AS  deleteFlag,
            `task_type` AS  taskType,
            `wh_name` AS  whName,
            `logistic_mode` AS  logisticMode,
            `id` AS  id,
            `enable_flag` AS  enableFlag,
            `business_mode` AS  businessMode,
            `site_code` AS  siteCode,
            `addr_control` AS  addrControl,
            `create_user_code` AS  createUserCode,
            `delivery_type` AS  deliveryType,
            `update_time` AS  updateTime,
            `entity_id` AS  entityId,
            `version` AS  version,
            `dimension_name` AS  dimensionName,
            `customer_name` AS  customerName,
            `sort_no` AS  sortNo,
            `update_user_code` AS  updateUserCode,
            `wh_code` AS  whCode,
            `create_time` AS  createTime,
            `param_code` AS  paramCode,
            `tms_site_code` AS tmsSiteCode,
            `tms_site_name` AS tmsSiteName,
            `tms_customer_code` AS tmsCustomerCode,
            `tms_customer_name` AS tmsCustomerName,
            `tms_company_code` AS tmsCompanyCode,
            `tms_company_name` AS tmsCompanyName,
            `csp_customer_code` AS cspCustomerCode,
            `csp_customer_name` AS cspCustomerName
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="orderType !=null and orderType != ''">
                        and `order_type` =#{orderType}
                    </if>
                    <if test="sourceSystem !=null and sourceSystem != ''">
                        and `source_system` =#{sourceSystem}
                    </if>
                    <if test="dimensionCode !=null and dimensionCode != ''">
                        and `dimension_code` =#{dimensionCode}
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        and `site_name` =#{siteName}
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        and `customer_code` =#{customerCode}
                    </if>
                    <if test="remark !=null and remark != ''">
                        and `remark` =#{remark}
                    </if>
                    <if test="parameterValue !=null and parameterValue != ''">
                        and `parameter_value` =#{parameterValue}
                    </if>
                    <if test="taskType !=null and taskType != ''">
                        and `task_type` =#{taskType}
                    </if>
                    <if test="whName !=null and whName != ''">
                        and `wh_name` =#{whName}
                    </if>
                    <if test="logisticMode !=null and logisticMode != ''">
                        and `logistic_mode` =#{logisticMode}
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="enableFlag !=null">
                        and `enable_flag` =#{enableFlag}
                    </if>
                    <if test="businessMode !=null and businessMode != ''">
                        and `business_mode` =#{businessMode}
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        and `site_code` =#{siteCode}
                    </if>
                    <if test="addrControl !=null">
                        and `addr_control` =#{addrControl}
                    </if>
                    <if test="deliveryType !=null and deliveryType != ''">
                        and `delivery_type` =#{deliveryType}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="entityId !=null and entityId != ''">
                        and `entity_id` =#{entityId}
                    </if>
                    <if test="dimensionName !=null and dimensionName != ''">
                        and `dimension_name` =#{dimensionName}
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        and `customer_name` =#{customerName}
                    </if>
                    <if test="sortNo !=null">
                        and `sort_no` =#{sortNo}
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        and `wh_code` =#{whCode}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="paramCode !=null and paramCode != ''">
                        and `param_code` =#{paramCode}
                    </if>

                    <if test="tmsSiteCode != null and tmsSiteCode != ''">
                        and `tms_site_code` = #{tmsSiteCode}
                    </if>
                    <if test="tmsSiteName != null and tmsSiteName != ''">
                        and `tms_site_name` = #{tmsSiteName}
                    </if>
                    <if test="tmsCustomerCode != null and tmsCustomerCode != ''">
                        and `tms_customer_code` = #{tmsCustomerCode}
                    </if>
                    <if test="tmsCustomerName != null and tmsCustomerName != ''">
                        and `tms_customer_name` = #{tmsCustomerName}
                    </if>
                    <if test="tmsCompanyCode != null and tmsCompanyCode != ''">
                        and `tms_company_code` = #{tmsCompanyCode}
                    </if>
                    <if test="tmsCompanyName != null and tmsCompanyName != ''">
                        and `tms_company_name` = #{tmsCompanyName}
                    </if>
                    <if test="cspCustomerCode != null and cspCustomerCode != ''">
                        and `csp_customer_code` = #{cspCustomerCode}
                    </if>
                    <if test="cspCustomerName != null and cspCustomerName != ''">
                        and `csp_customer_name` = #{cspCustomerName}
                    </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
                        <if test="businessMode !=null and businessMode != ''">
                            `business_mode` = #{businessMode},
                        </if>
                        <if test="orderType !=null and orderType != ''">
                            `order_type` = #{orderType},
                        </if>
                        <if test="siteCode !=null and siteCode != ''">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="addrControl != null">
                            `addr_control` = #{addrControl},
                        </if>
                        <if test="sourceSystem !=null and sourceSystem != ''">
                            `source_system` = #{sourceSystem},
                        </if>
                        <if test="deliveryType !=null and deliveryType != ''">
                            `delivery_type` = #{deliveryType},
                        </if>
                        <if test="dimensionCode !=null and dimensionCode != ''">
                            `dimension_code` = #{dimensionCode},
                        </if>
                        <if test="siteName !=null and siteName != ''">
                            `site_name` = #{siteName},
                        </if>
                        <if test="customerCode !=null and customerCode != ''">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="remark !=null and remark != ''">
                            `remark` = #{remark},
                        </if>
                        <if test="entityId !=null and entityId != ''">
                            `entity_id` = #{entityId},
                        </if>
                        <if test="parameterValue !=null and parameterValue != ''">
                            `parameter_value` = #{parameterValue},
                        </if>
                        <if test="dimensionName !=null and dimensionName != ''">
                            `dimension_name` = #{dimensionName},
                        </if>
                        <if test="customerName !=null and customerName != ''">
                            `customer_name` = #{customerName},
                        </if>
                        <if test="sortNo != null">
                            `sort_no` = #{sortNo},
                        </if>
                        <if test="updateUserCode !=null and updateUserCode != ''">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="taskType !=null and taskType != ''">
                            `task_type` = #{taskType},
                        </if>
                        <if test="whName !=null and whName != ''">
                            `wh_name` = #{whName},
                        </if>
                        <if test="whCode !=null and whCode != ''">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="logisticMode !=null and logisticMode != ''">
                            `logistic_mode` = #{logisticMode},
                        </if>
                        <if test="paramCode !=null and paramCode != ''">
                            `param_code` = #{paramCode},
                        </if>
                        <if test="enableFlag != null">
                            `enable_flag` = #{enableFlag},
                        </if>
                        `tms_site_code` = #{tmsSiteCode},
                        `tms_site_name` = #{tmsSiteName},
                        `tms_customer_code` = #{tmsCustomerCode},
                        `tms_customer_name` = #{tmsCustomerName},
                        `tms_company_code` = #{tmsCompanyCode},
                        `tms_company_name` = #{tmsCompanyName},
                        `csp_customer_code` = #{cspCustomerCode},
                        `csp_customer_name` = #{cspCustomerName},
                        <if test="wmsSiteCode !=null">
                            `wms_site_code` = #{wmsSiteCode},
                        </if>
                        <if test="wmsSiteName !=null">
                            `wms_site_name` = #{wmsSiteName},
                        </if>
                        <if test="wmsCompanyCode !=null">
                            `wms_company_code` = #{wmsCompanyCode},
                        </if>
                        <if test="wmsCompanyName !=null">
                            `wms_company_name` = #{wmsCompanyName},
                        </if>

        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="businessMode !=null">
                            `business_mode` = #{businessMode},
                        </if>
                        <if test="orderType !=null">
                            `order_type` = #{orderType},
                        </if>
                        <if test="siteCode !=null">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="addrControl != null">
                            `addr_control` = #{addrControl},
                        </if>
                        <if test="sourceSystem !=null">
                            `source_system` = #{sourceSystem},
                        </if>
                        <if test="deliveryType !=null">
                            `delivery_type` = #{deliveryType},
                        </if>
                        <if test="dimensionCode !=null">
                            `dimension_code` = #{dimensionCode},
                        </if>
                        <if test="siteName !=null">
                            `site_name` = #{siteName},
                        </if>
                        <if test="customerCode !=null">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="entityId !=null">
                            `entity_id` = #{entityId},
                        </if>
                        <if test="parameterValue !=null">
                            `parameter_value` = #{parameterValue},
                        </if>
                        <if test="dimensionName !=null">
                            `dimension_name` = #{dimensionName},
                        </if>
                        <if test="customerName !=null">
                            `customer_name` = #{customerName},
                        </if>
                        <if test="sortNo != null">
                            `sort_no` = #{sortNo},
                        </if>
                        <if test="updateUserCode !=null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="taskType !=null">
                            `task_type` = #{taskType},
                        </if>
                        <if test="whName !=null">
                            `wh_name` = #{whName},
                        </if>
                        <if test="whCode !=null">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="logisticMode !=null">
                            `logistic_mode` = #{logisticMode},
                        </if>
                        <if test="paramCode !=null">
                            `param_code` = #{paramCode},
                        </if>
                        <if test="enableFlag != null">
                            `enable_flag` = #{enableFlag},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from business_control_param_detail t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail">
        select
        <include refid="searchFieldsSql"/>
        from business_control_param_detail t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">${orderByType}</if>
        </if>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from business_control_param_detail t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail">
        select
        <include refid="searchFieldsSql"/>
        from business_control_param_detail t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
${orderByType}            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        business_control_param_detail t
        <include refid="setFieldsSql"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        business_control_param_detail t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        business_control_param_detail t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail" useGeneratedKeys="true" keyProperty="id">
        insert into business_control_param_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="businessMode !=null and businessMode != ''">
                        `business_mode`,
                    </if>

                    <if test="orderType !=null and orderType != ''">
                        `order_type`,
                    </if>

                    <if test="siteCode !=null and siteCode != ''">
                        `site_code`,
                    </if>

                    <if test="addrControl != null">
                        `addr_control`,
                    </if>

                    <if test="sourceSystem !=null and sourceSystem != ''">
                        `source_system`,
                    </if>

                    <if test="createUserCode !=null and createUserCode != ''">
                        `create_user_code`,
                    </if>

                    <if test="deliveryType !=null and deliveryType != ''">
                        `delivery_type`,
                    </if>

                    <if test="dimensionCode !=null and dimensionCode != ''">
                        `dimension_code`,
                    </if>

                    <if test="siteName !=null and siteName != ''">
                        `site_name`,
                    </if>

                    <if test="customerCode !=null and customerCode != ''">
                        `customer_code`,
                    </if>

                    <if test="remark !=null and remark != ''">
                        `remark`,
                    </if>

                    <if test="entityId !=null and entityId != ''">
                        `entity_id`,
                    </if>

                    <if test="parameterValue !=null and parameterValue != ''">
                        `parameter_value`,
                    </if>

                    <if test="dimensionName !=null and dimensionName != ''">
                        `dimension_name`,
                    </if>

                    <if test="customerName !=null and customerName != ''">
                        `customer_name`,
                    </if>

                    <if test="sortNo != null">
                        `sort_no`,
                    </if>

                    <if test="updateUserCode !=null and updateUserCode != ''">
                        `update_user_code`,
                    </if>

                    <if test="taskType !=null and taskType != ''">
                        `task_type`,
                    </if>

                    <if test="whName !=null and whName != ''">
                        `wh_name`,
                    </if>

                    <if test="whCode !=null and whCode != ''">
                        `wh_code`,
                    </if>

                    <if test="logisticMode !=null and logisticMode != ''">
                        `logistic_mode`,
                    </if>

                    <if test="paramCode !=null and paramCode != ''">
                        `param_code`,
                    </if>

                    <if test="enableFlag != null">
                        `enable_flag`,
                    </if>
                    <if test="tmsSiteCode != null and tmsSiteCode != ''">
                        `tms_site_code`,
                    </if>
                    <if test="tmsSiteName != null and tmsSiteName != ''">
                        `tms_site_name`,
                    </if>
                    <if test="tmsCustomerCode != null and tmsCustomerCode != ''">
                        `tms_customer_code`,
                    </if>
                    <if test="tmsCustomerName != null and tmsCustomerName != ''">
                        `tms_customer_name`,
                    </if>
                    <if test="tmsCompanyCode != null and tmsCompanyCode != ''">
                        `tms_company_code`,
                    </if>
                    <if test="tmsCompanyName != null and tmsCompanyName != ''">
                        `tms_company_name`,
                    </if>
                    <if test="cspCustomerCode != null and cspCustomerCode != ''">
                        `csp_customer_code`,
                    </if>
                    <if test="cspCustomerName != null and cspCustomerName != ''">
                        `csp_customer_name`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="businessMode !=null and businessMode != ''">
                        #{businessMode},
                    </if>
                    <if test="orderType !=null and orderType != ''">
                        #{orderType},
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        #{siteCode},
                    </if>
                    <if test="addrControl != null">
                        #{addrControl},
                    </if>
                    <if test="sourceSystem !=null and sourceSystem != ''">
                        #{sourceSystem},
                    </if>
                    <if test="createUserCode !=null and createUserCode != ''">
                        #{createUserCode},
                    </if>
                    <if test="deliveryType !=null and deliveryType != ''">
                        #{deliveryType},
                    </if>
                    <if test="dimensionCode !=null and dimensionCode != ''">
                        #{dimensionCode},
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        #{siteName},
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        #{customerCode},
                    </if>
                    <if test="remark !=null and remark != ''">
                        #{remark},
                    </if>
                    <if test="entityId !=null and entityId != ''">
                        #{entityId},
                    </if>
                    <if test="parameterValue !=null and parameterValue != ''">
                        #{parameterValue},
                    </if>
                    <if test="dimensionName !=null and dimensionName != ''">
                        #{dimensionName},
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        #{customerName},
                    </if>
                    <if test="sortNo != null">
                        #{sortNo},
                    </if>
                    <if test="updateUserCode !=null and updateUserCode != ''">
                        #{updateUserCode},
                    </if>
                    <if test="taskType !=null and taskType != ''">
                        #{taskType},
                    </if>
                    <if test="whName !=null and whName != ''">
                        #{whName},
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        #{whCode},
                    </if>
                    <if test="logisticMode !=null and logisticMode != ''">
                        #{logisticMode},
                    </if>
                    <if test="paramCode !=null and paramCode != ''">
                        #{paramCode},
                    </if>
                    <if test="enableFlag != null">
                        #{enableFlag},
                    </if>
                    <if test="tmsSiteCode != null and tmsSiteCode != ''">
                        #{tmsSiteCode},
                    </if>
                    <if test="tmsSiteName != null and tmsSiteName != ''">
                        #{tmsSiteName},
                    </if>
                    <if test="tmsCustomerCode != null and tmsCustomerCode != ''">
                        #{tmsCustomerCode},
                    </if>
                    <if test="tmsCustomerName != null and tmsCustomerName != ''">
                        #{tmsCustomerName},
                    </if>
                    <if test="tmsCompanyCode != null and tmsCompanyCode != ''">
                        #{tmsCompanyCode},
                    </if>
                    <if test="tmsCompanyName != null and tmsCompanyName != ''">
                        #{tmsCompanyName},
                    </if>
                    <if test="cspCustomerCode != null and cspCustomerCode != ''">
                        #{cspCustomerCode},
                    </if>
                    <if test="cspCustomerName != null and cspCustomerName != ''">
                        #{cspCustomerName},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                `business_mode`,
                `order_type`,
                `site_code`,
                `addr_control`,
                `source_system`,
                `create_user_code`,
                `delivery_type`,
                `dimension_code`,
                `site_name`,
                `customer_code`,
                `remark`,
                `entity_id`,
                `parameter_value`,
                `dimension_name`,
                `customer_name`,
                `sort_no`,
                `update_user_code`,
                `task_type`,
                `wh_name`,
                `wh_code`,
                `logistic_mode`,
                `param_code`,
                `enable_flag`,
                `tms_site_code`,
                `tms_site_name`,
                `tms_customer_code`,
                `tms_customer_name`,
                `tms_company_code`,
                `tms_company_name`,
                `csp_customer_code`,
                `csp_customer_name`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.businessMode},
                #{item.orderType},
                #{item.siteCode},
                #{item.addrControl},
                #{item.sourceSystem},
                #{item.createUserCode},
                #{item.deliveryType},
                #{item.dimensionCode},
                #{item.siteName},
                #{item.customerCode},
                #{item.remark},
                #{item.entityId},
                #{item.parameterValue},
                #{item.dimensionName},
                #{item.customerName},
                #{item.sortNo},
                #{item.updateUserCode},
                #{item.taskType},
                #{item.whName},
                #{item.whCode},
                #{item.logisticMode},
                #{item.paramCode},
                #{item.enableFlag},
                #{item.tmsSiteCode},
                #{item.tmsSiteName},
                #{item.tmsCustomerCode},
                #{item.tmsCustomerName},
                #{item.tmsCompanyCode},
                #{item.tmsCompanyName},
                #{item.cspCustomerCode},
                #{item.cspCustomerName},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        business_control_param_detail
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.businessMode !=null and item.businessMode != ''">
                     `business_mode`  = #{item.businessMode},
                 </if>
                 <if test="item.orderType !=null and item.orderType != ''">
                     `order_type`  = #{item.orderType},
                 </if>
                 <if test="item.siteCode !=null and item.siteCode != ''">
                     `site_code`  = #{item.siteCode},
                 </if>
                 <if test="item.addrControl != null">
                     `addr_control`  = #{item.addrControl},
                 </if>
                 <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                     `source_system`  = #{item.sourceSystem},
                 </if>
                 <if test="item.deliveryType !=null and item.deliveryType != ''">
                     `delivery_type`  = #{item.deliveryType},
                 </if>
                 <if test="item.dimensionCode !=null and item.dimensionCode != ''">
                     `dimension_code`  = #{item.dimensionCode},
                 </if>
                 <if test="item.siteName !=null and item.siteName != ''">
                     `site_name`  = #{item.siteName},
                 </if>
                 <if test="item.customerCode !=null and item.customerCode != ''">
                     `customer_code`  = #{item.customerCode},
                 </if>
                 <if test="item.remark !=null and item.remark != ''">
                     `remark`  = #{item.remark},
                 </if>
                 <if test="item.entityId !=null and item.entityId != ''">
                     `entity_id`  = #{item.entityId},
                 </if>
                 <if test="item.parameterValue !=null and item.parameterValue != ''">
                     `parameter_value`  = #{item.parameterValue},
                 </if>
                 <if test="item.dimensionName !=null and item.dimensionName != ''">
                     `dimension_name`  = #{item.dimensionName},
                 </if>
                 <if test="item.customerName !=null and item.customerName != ''">
                     `customer_name`  = #{item.customerName},
                 </if>
                 <if test="item.sortNo != null">
                     `sort_no`  = #{item.sortNo},
                 </if>
                 <if test="item.updateUserCode !=null and item.updateUserCode != ''">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.taskType !=null and item.taskType != ''">
                     `task_type`  = #{item.taskType},
                 </if>
                 <if test="item.whName !=null and item.whName != ''">
                     `wh_name`  = #{item.whName},
                 </if>
                 <if test="item.whCode !=null and item.whCode != ''">
                     `wh_code`  = #{item.whCode},
                 </if>
                 <if test="item.logisticMode !=null and item.logisticMode != ''">
                     `logistic_mode`  = #{item.logisticMode},
                 </if>
                 <if test="item.paramCode !=null and item.paramCode != ''">
                     `param_code`  = #{item.paramCode},
                 </if>
                 <if test="item.enableFlag != null">
                     `enable_flag`  = #{item.enableFlag},
                 </if>
        </set>
    </sql>

    <sql id="setBatchFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
            <if test="item.businessMode !=null">
                `business_mode`  = #{item.businessMode},
            </if>
            <if test="item.orderType !=null">
                `order_type`  = #{item.orderType},
            </if>
            <if test="item.siteCode !=null">
                `site_code`  = #{item.siteCode},
            </if>
            <if test="item.addrControl != null">
                `addr_control` = #{item.addrControl},
            </if>
            <if test="item.sourceSystem !=null">
                `source_system`  = #{item.sourceSystem},
            </if>
            <if test="item.deliveryType !=null">
                `delivery_type` = #{item.deliveryType},
            </if>
            <if test="item.dimensionCode !=null">
                `dimension_code`  = #{item.dimensionCode},
            </if>
            <if test="item.siteName !=null">
                `site_name`  = #{item.siteName},
            </if>
            <if test="item.customerCode !=null">
                `customer_code`  = #{item.customerCode},
            </if>
            <if test="item.remark !=null">
                `remark`  = #{item.remark},
            </if>
            <if test="item.parameterValue !=null">
                `parameter_value`  = #{item.parameterValue},
            </if>
            <if test="item.entityId != null">
                `entity_id` = #{item.entityId},
            </if>
            <if test="item.dimensionName !=null">
                `dimension_name`  = #{item.dimensionName},
            </if>
            <if test="item.customerName !=null">
                `customer_name`  = #{item.customerName},
            </if>
            <if test="item.sortNo != null">
                `sort_no`  = #{item.sortNo},
            </if>
            <if test="item.updateUserCode !=null">
                `update_user_code`  = #{item.updateUserCode},
            </if>
            <if test="item.taskType !=null">
                `task_type` = #{item.taskType},
            </if>
            <if test="item.whName !=null">
                `wh_name`  = #{item.whName},
            </if>
            <if test="item.whCode !=null">
                `wh_code`  = #{item.whCode},
            </if>
            <if test="item.logisticMode !=null">
                `logistic_mode` = #{item.logisticMode},
            </if>
            <if test="item.paramCode !=null">
                `param_code`  = #{item.paramCode},
            </if>
            <if test="item.enableFlag != null">
                `enable_flag`  = #{item.enableFlag},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.orderType !=null and item.orderType != ''">
                        and  `order_type`  =#{item.orderType}
                    </if>
                    <if test="item.sourceSystem !=null and item.sourceSystem != ''">
                        and  `source_system`  =#{item.sourceSystem}
                    </if>
                    <if test="item.dimensionCode !=null and item.dimensionCode != ''">
                        and  `dimension_code`  =#{item.dimensionCode}
                    </if>
                    <if test="item.siteName !=null and item.siteName != ''">
                        and  `site_name`  =#{item.siteName}
                    </if>
                    <if test="item.customerCode !=null and item.customerCode != ''">
                        and  `customer_code`  =#{item.customerCode}
                    </if>
                    <if test="item.remark !=null">
                        and  `remark`  =#{item.remark}
                    </if>
                    <if test="item.parameterValue !=null and item.parameterValue != ''">
                        and  `parameter_value`  =#{item.parameterValue}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.taskType !=null and item.taskType != ''">
                        and  `task_type`  =#{item.taskType}
                    </if>
                    <if test="item.whName !=null and item.whName != ''">
                        and  `wh_name`  =#{item.whName}
                    </if>
                    <if test="item.logisticMode !=null and item.logisticMode != ''">
                        and  `logistic_mode`  =#{item.logisticMode}
                    </if>
                    <if test="item.enableFlag !=null">
                        and  `enable_flag`  =#{item.enableFlag}
                    </if>
                    <if test="item.businessMode !=null and item.businessMode != ''">
                        and  `business_mode`  =#{item.businessMode}
                    </if>
                    <if test="item.siteCode !=null and item.siteCode != ''">
                        and  `site_code`  =#{item.siteCode}
                    </if>
                    <if test="item.addrControl !=null">
                        and  `addr_control`  =#{item.addrControl}
                    </if>
                    <if test="item.deliveryType !=null and item.deliveryType != ''">
                        and  `delivery_type`  =#{item.deliveryType}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.entityId !=null and item.entityId != ''">
                        and  `entity_id`  =#{item.entityId}
                    </if>
                    <if test="item.version !=null">
                        and  `version`  =#{item.version}
                    </if>
                    <if test="item.dimensionName !=null and item.dimensionName != ''">
                        and  `dimension_name`  =#{item.dimensionName}
                    </if>
                    <if test="item.customerName !=null and item.customerName != ''">
                        and  `customer_name`  =#{item.customerName}
                    </if>
                    <if test="item.sortNo !=null">
                        and  `sort_no`  =#{item.sortNo}
                    </if>
                    <if test="item.whCode !=null and item.whCode != ''">
                        and  `wh_code`  =#{item.whCode}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.paramCode !=null and item.paramCode != ''">
                        and  `param_code`  =#{item.paramCode}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE business_control_param_detail
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
#{item.id}
        </foreach>
    </update>


    <update id="deleteBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE business_control_param_detail
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>

</mapper>