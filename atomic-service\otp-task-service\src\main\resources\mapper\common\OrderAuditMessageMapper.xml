<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.OrderAuditMessageMapper">

    <sql id="searchFieldsSql">
            `delete_flag` AS  deleteFlag,
            `update_user_code` AS  updateUserCode,
            `order_no` AS  orderNo,
            `create_time` AS  createTime,
            `create_user_code` AS  createUserCode,
            `msg_id` AS  msgId,
            `remark` AS  remark,
            `update_time` AS  updateTime,
            `id` AS  id,
            `version` AS  version,
            `consume_status` AS  consumeStatus,
            `consume_times` AS  consumeTimes
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="orderNo !=null and orderNo != ''">
            and `order_no` =#{orderNo}
        </if>
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="msgId !=null and msgId != ''">
            and `msg_id` =#{msgId}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="consumeStatus !=null">
            and `consume_status` =#{consumeStatus}
        </if>
        <if test="consumeTimes !=null">
            and `consume_times` =#{consumeTimes}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="orderNo !=null and orderNo != ''">
                `order_no` = #{orderNo},
            </if>
            <if test="msgId !=null and msgId != ''">
                `msg_id` = #{msgId},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="consumeStatus != null">
                `consume_status` = #{consumeStatus},
            </if>
            <if test="consumeTimes != null">
                `consume_times` = #{consumeTimes},
            </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from order_audit_message t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.OrderAuditMessage">
        select
        <include refid="searchFieldsSql"/>
        from order_audit_message t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.OrderAuditMessage">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from order_audit_message t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.OrderAuditMessage">
        select
        <include refid="searchFieldsSql"/>
        from order_audit_message t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        order_audit_message t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>

    <update id="deleteById">
        update
        order_audit_message t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.OrderAuditMessage" useGeneratedKeys="true"
            keyProperty="id">
        insert into order_audit_message
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="orderNo !=null and orderNo != ''">
                `order_no`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="msgId !=null and msgId != ''">
                `msg_id`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="consumeStatus != null">
                `consume_status`,
            </if>

            <if test="consumeTimes != null">
                `consume_times`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="orderNo !=null and orderNo != ''">
                #{orderNo},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="msgId !=null and msgId != ''">
                #{msgId},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="consumeStatus != null">
                #{consumeStatus},
            </if>
            <if test="consumeTimes != null">
                #{consumeTimes},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `update_user_code`,
            `order_no`,
            `create_user_code`,
            `msg_id`,
            `remark`,
            `consume_status`,
            `consume_times`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.updateUserCode},
            #{item.orderNo},
            #{item.createUserCode},
            #{item.msgId},
            #{item.remark},
            #{item.consumeStatus},
            #{item.consumeTimes},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        order_audit_message
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.orderNo !=null and item.orderNo != ''">
                `order_no` = #{item.orderNo},
            </if>
            <if test="item.msgId !=null and item.msgId != ''">
                `msg_id` = #{item.msgId},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.consumeStatus != null">
                `consume_status` = #{item.consumeStatus},
            </if>
            <if test="item.consumeTimes != null">
                `consume_times` = #{item.consumeTimes},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.deleteFlag !=null">
                and `delete_flag` =#{item.deleteFlag}
            </if>
            <if test="item.orderNo !=null and item.orderNo != ''">
                and `order_no` =#{item.orderNo}
            </if>
            <if test="item.createTime !=null">
                and `create_time` =#{item.createTime}
            </if>
            <if test="item.msgId !=null and item.msgId != ''">
                and `msg_id` =#{item.msgId}
            </if>
            <if test="item.remark !=null">
                and `remark` =#{item.remark}
            </if>
            <if test="item.updateTime !=null">
                and `update_time` =#{item.updateTime}
            </if>
            <if test="item.version !=null">
                and `version` =#{item.version}
            </if>
            <if test="item.consumeStatus !=null">
                and `consume_status` =#{item.consumeStatus}
            </if>
            <if test="item.consumeTimes !=null">
                and `consume_times` =#{item.consumeTimes}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE order_audit_message
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>


    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE order_audit_message
            set `delete_flag`=1
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

</mapper>