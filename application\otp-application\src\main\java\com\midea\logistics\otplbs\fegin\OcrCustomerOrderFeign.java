package com.midea.logistics.otplbs.fegin;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: OcrCustomerOrderFeign
 * @description: OCR 创建订单接口
 * @author: kongly1
 * @date: 2021-6-17 14:12
 */

import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.domain.request.OcrCustomerOrderRequest;
import com.midea.logistics.otp.order.domain.response.CustomOrderOcrRespone;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "logistics-otp-order-converged-service")
public interface OcrCustomerOrderFeign {


    /**
     * @description: 新增或者修改安得订单
     * @param: [customerOrderNos]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2019/6/10 10:46
     */
    @PostMapping(OrderConvergedRouters.OCR_CUSTOMER_ORDER)
    JsonResponse<CustomOrderOcrRespone> createCustomerOrder(@RequestBody OcrCustomerOrderRequest customerOrderRequest);


    /**
     * 商品编码转换
     * @param customOrderOcrRespone
     * @return
     */
    @PostMapping(OrderConvergedRouters.OCR_CUSTOMER_ORDER_ITEM_CODE)
    JsonResponse<CustomOrderOcrRespone> getItemCode(@RequestBody CustomOrderOcrRespone customOrderOcrRespone);
}
