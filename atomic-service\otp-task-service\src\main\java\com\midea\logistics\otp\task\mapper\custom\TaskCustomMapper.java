package com.midea.logistics.otp.task.mapper.custom;

import com.midea.logistics.otp.bean.JobFlowControlRequest;
import com.midea.logistics.otp.bean.JobRequest;
import com.midea.logistics.otp.bean.OrderRequest;
import com.midea.logistics.otp.bean.TaskList;
import com.midea.logistics.otp.order.converged.domain.dto.TaskStatusUpdateBatch;
import com.midea.logistics.otp.order.domain.bean.custom.OrderBatchSearchRequest;
import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialNoAndConstractNoSyncDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialOrderSyncItemDto;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDeliveryType;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDetailResponse;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Mapper
public interface TaskCustomMapper {

    List<Task> selectTask(List<String> orderNoes);

    List<Task> listByParentOrderNos(List<String> orderNoes);

    Integer update(@RequestBody Task task);

    Integer updateStatusByTaskNos(TaskStatusUpdateBatch statusUpdateBatch);

    int batchUpdateDriverQueueCode(
            @Param("taskIds") List<Long> taskIds,
            @Param("driverQueueCode") String driverQueueCode);


    Task deliveryCount(@RequestBody Task task);

    List<Task> getTasksByOrderNos(List<String> orderNoes);

    List<Task> getTasksByTaskNos(List<String> taskNos);

    Integer searchTaskListCount(SearchTaskRequest searchTaskRequest);

    List<TaskDetailsResponse> searchTaskList(SearchTaskRequest searchTaskRequest);

    Integer batchUpdateByTask(List<Task> tasks);

    Integer batchUpdateTaskForNodeStatus(List<Task> tasks);

    Integer synFinancialNo(List<FinancialOrderSyncItemDto> itemDtoList);

    List<TaskDetailsResponse> getTasksAndAddrByOrderNos(List<String> orderNos);

    List<String> taskNewList(Task task);

    Task queryByTaskLast(Task taskQr);

    Task queryByTaskLastNow(Task taskQr);

    void synContractNo(FinancialNoAndConstractNoSyncDto financialNoAndConstractNoSyncDto);

    List<Task> searchTaskListByTaskNos(List<String> taskNo);

    Integer clearDistribution(Task task);

    Integer updateDistribution(Task task);

    Integer updateDispatchNo(List<Task> tasks);

    Integer updateLine(List<Task> tasks);

    List<Task> searchTaskListDoRdo(SearchTaskRequest searchTaskRequest);

    Integer searchTaskOutListCount(SearchTaskRequest searchTaskRequest);

    Integer searchTaskListCountTaskTable(Task searchTaskRequest);

    List<TaskDetailsResponse> searchTaskOutList(SearchTaskRequest searchTaskRequest);

    String selectAgingProductNameByCode(String code);

    Task selectOneByRDIorRDO(Task task);

    Task selectOneByRDIorRDOorYS(Task task);

    Task selectOneByRDIorRDOorIT(Task task);

    List<String> taskHoldList(Task task);

    List<String> taskFlowControlList(JobFlowControlRequest jobRequest);

    List<Task> taskApptList(Task task);
    List<TaskList> listTaskByPhoneNo(OrderRequest orderRequest);

    List<TaskItem> listItemBytaskNo(List<String> taskNoList);

    List<TaskAddress> listAddrBytaskNo(List<String> taskNoList);

    List<Task> tasksByTaskNos(List<String> taskNos);

    List<TaskDeliveryType> tasksDeliveryTypeByTaskNos(List<String> taskNos);

    List<String> listDeliveredTaskNo(JobRequest jobRequest);

    Integer searchTaskListByTaskNosCount(OrderBatchSearchRequest searchRequest);

    List<TaskDetailResponse> searchTaskListByTaskNosIndex(OrderBatchSearchRequest searchRequest);

    List<TaskExt> listByTaskNosAndAuth(SearchTaskRequest searchTaskRequest);

    List<Task> getTasksByCustomerOrderNos(List<String> customerOrderNos);
}
