package com.midea.logistics.otp.orderverify.aop;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderCancelFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoFeign;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.enums.ExceptionType;
import com.midea.logistics.otp.enums.ExcuteStatus;
import com.midea.logistics.otp.enums.OrderOperateType;
import com.midea.logistics.otp.enums.OrderStatus;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.OrderLogHelper;
import com.midea.logistics.otp.order.common.helper.OrderStatusCheckHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.service.OrderLogService;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.response.OrderExcuteCountRespone;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;

@Aspect
@Component
public class ExpressFlowAspect {

    private static final Logger logger = LoggerFactory.getLogger(ExpressFlowAspect.class);
    private final String POINT_CUT = "@annotation(com.midea.logistics.otp.orderverify.aop.ExpressFlow)";
    private static final String ORDER_LOCK_KEY_PREFIX = "ORDER_LOCK_";
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private OrderLogHelper orderLogHelper;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private OrderInfoFeign orderInfoFeign;

    @Autowired
    private OrderStatusCheckHelper orderStatusCheckHelper;
    @Autowired
    private OrderCancelFeign orderCancelFeign;
    @Autowired
    private OrderLogService orderLogService;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;


    /**
     * : @Around环绕通知
     * : @Before通知执行
     * : @Before通知执行结束
     * : @Around环绕通知执行结束
     * : @After后置通知执行了!
     * : @AfterReturning第一个后置返回通知的返回值：18
     */
    @Pointcut(POINT_CUT)
    public void pointCut() {
    }

    /**
     * 环绕通知：
     * 注意:Spring AOP的环绕通知会影响到AfterThrowing通知的运行,不要同时使用
     * <p>
     * 环绕通知非常强大，可以决定目标方法是否执行，什么时候执行，执行时是否需要替换方法参数，执行完毕是否需要替换返回值。
     * 环绕通知第一个参数必须是org.aspectj.lang.ProceedingJoinPoint类型
     */
    @Around(value = POINT_CUT)
    public Object doAroundAdvice(ProceedingJoinPoint point) throws Throwable {

        Object[] args = point.getArgs();
        List<CustomerOrderInfoExt> customerOrderInfos = null;
        List<OrderInfoExt> orderInfos = Arrays.stream(args).filter(arg -> arg instanceof OrderInfoExt).map(arg -> (OrderInfoExt) arg).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderInfos)) {
            customerOrderInfos = Arrays.stream(args).filter(arg -> arg instanceof CustomerOrderInfoExt).map(arg -> (CustomerOrderInfoExt) arg).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(orderInfos) && CollectionUtils.isEmpty(customerOrderInfos)) {
            throw BusinessException.fail("流程入参不是父单也不是子单，无法执行");
        }

        CustomerOrderInfoExt customerOrderInfoExt = CollectionUtils.isEmpty(customerOrderInfos) ? null : customerOrderInfos.get(0);
        OrderInfoExt orderInfoExt = CollectionUtils.isEmpty(orderInfos) ? null : orderInfos.get(0);

        //检查父单
        if (null != customerOrderInfoExt){
            CustomerOrderAddress address = customerOrderInfoExt.getCustomerOrderAddress();
            if (Objects.isNull(address)) {
                throw BusinessException.fail("父单地址为空!");
            }
            List<CustomerOrderItem> customerOrderItems = customerOrderInfoExt.getCustomerOrderItems();
            if (Objects.isNull(customerOrderItems)) {
                throw BusinessException.fail("父单明细为空!");
            }
        }

        //检查子单
        if (null != orderInfoExt){
            List<OrderInfoItem> orderInfoItems = orderInfoExt.getOrderInfoItems();
            if (Objects.isNull(orderInfoItems)) {
                throw BusinessException.fail("子单明细为空!");
            }
        }


        String ticket = null;
        ticket = orderInfoExt == null ? ticket : orderInfoExt.getOrderNo();
        ticket = customerOrderInfoExt == null ? ticket : customerOrderInfoExt.getOrderNo();
        if(StringUtils.isBlank(ticket)){
            throw BusinessException.fail("流程出参找不到该订单信息");
        }

        Signature signature = point.getSignature();
        ExpressFlow expressFlow = null;

        if (signature instanceof MethodSignature) {
            MethodSignature methodSignature = (MethodSignature) signature;
            // 被切的方法
            Method method = methodSignature.getMethod();
            // 返回类型
            Class<?> methodReturnType = method.getReturnType();
            //if (methodReturnType != CustomerOrderInfoExt.class && methodReturnType != OrderInfoExt.class) {
            //    throw BusinessException.fail("流程出参不是父单也不是子单，无法执行");
            //}
            expressFlow = methodSignature.getMethod().getAnnotation(ExpressFlow.class);
        }

        if(null == expressFlow){
            throw BusinessException.fail("流程出参找不到该节点信息");
        }
        OrderOperateType node = expressFlow.node();
        String nodeKey = node.getKey();

        //检查一下redis是否已经存在这个key，说明这个节点已经走过来，直接跳过
        boolean nodeExist = redisHelper.isExpressFlowNodeExist(ticket, nodeKey);
        if (nodeExist){
            return FlowListenerParam.success(node.getValue()+"成功");
        }

        Method method = ((MethodSignature) point.getSignature()).getMethod();
        String function = method.getDeclaringClass().getName() + "." + method.getName();

        // 重复检测 && 取消检测
        this.checkRepeatAndCancelLock(customerOrderInfoExt, orderInfoExt, ticket, function,node);

        FlowListenerParam obj = null;
        Throwable exception = null;
        long t1 = System.currentTimeMillis();
        try {
            obj = (FlowListenerParam) point.proceed();
            obj.setNodeCode(nodeKey);
            FlowStatus flowStatus = obj.getFlowStatus();
            //logger.info("express_flow: {} 结果分析 {} flowStatus：{}", ticket, nodeKey,flowStatus.getValue());
            //失败后，写失败日志
            if (FlowStatus.FAILD == flowStatus || FlowStatus.TIME_OUT == flowStatus ) {
                saveErrorLog(customerOrderInfoExt, orderInfoExt, node, obj.getErrorMsg());
                exception = new BusinessException(obj.getErrorMsg());
            //取消
            }else if (FlowStatus.CANCEL == flowStatus ) {
                saveCancelLog(customerOrderInfoExt, orderInfoExt, node, obj.getErrorMsg());
                exception = new BusinessException(obj.getErrorMsg());
            //成功之后，写成功日志并且写入Redis
            }else if (FlowStatus.SUCCESS == flowStatus) {
                saveSuccessLog(customerOrderInfoExt, orderInfoExt, node, obj.getErrorMsg());
                redisHelper.setExpressFlowNode(ticket,node.getKey());
            }
        } catch (Throwable throwable) {
            exception = throwable;
            String message = throwable.getMessage();
            long t2 = System.currentTimeMillis();
            logger.info("express_flow: {} 结果分析 {} 耗时 {}ms，express_flow_exception：{}", ticket, nodeKey, (t2 - t1),exception);
            //异常后，写失败日志
            saveErrorLog(customerOrderInfoExt, orderInfoExt, node, message);
        }

        long t2 = System.currentTimeMillis();
        logger.info("express_flow: {} 执行 {} 耗时 {}ms 结果 {}", ticket, nodeKey, (t2 - t1));

        if (null != exception){
            throw exception;
        }

        return obj;
    }

    /**
     * @description: 保存异常信息
     * @param: [customerOrderInfoExt, orderInfoExt, node, exception, message]
     * @return: void
     * @author: 陈永培
     * @createtime: 2021/10/10 22:20
     */
    private void saveCancelLog(CustomerOrderInfoExt customerOrderInfoExt, OrderInfoExt orderInfoExt, OrderOperateType node, String message) {
        if (null != customerOrderInfoExt) {
            orderLogHelper.saveLog(customerOrderInfoExt, node, CommonConstant.STRING_FLAG_NO, message);
        } else if (null != orderInfoExt) {
            orderLogHelper.saveLog(orderInfoExt, node, CommonConstant.STRING_FLAG_NO, message);
        }
    }
    /**
    * @description: 保存异常信息
    * @param: [customerOrderInfoExt, orderInfoExt, node, exception, message]
    * @return: void
    * @author: 陈永培
    * @createtime: 2021/10/10 22:20
    */
    private void saveErrorLog(CustomerOrderInfoExt customerOrderInfoExt, OrderInfoExt orderInfoExt, OrderOperateType node, String message)  {
        FlowListenerParam param = new FlowListenerParam();
        param.setErrorMsg(message);
        param.setFlowStatus(FlowStatus.FAILD);
        if (null != customerOrderInfoExt){
            //2025年3月28日14:48:47 嘉龙 订单映射，在解析平台节点前出现审核失败时，如果平台为空，需调用平台对照解析出平台 https://cf.annto.com/x/nI9_B
            orderLogService.orderMappingFailReSearchSiteInfo(customerOrderInfoExt,param,node);
            message = orderLogService.errorMsgConvert(param,customerOrderInfoExt);
            orderLogHelper.saveLog(customerOrderInfoExt, node, CommonConstant.STRING_FLAG_NO, message);
        }else if(null != orderInfoExt){
            //logger.info("express_flow: {} saveLog..", orderInfoExt.getOrderNo());
            message = orderLogService.errorMsgConvert(param,orderInfoExt);
            orderLogHelper.saveLog(orderInfoExt, node, CommonConstant.STRING_FLAG_NO, message);
        }

        //父流程更新状态
        if (null != customerOrderInfoExt) {
            CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
            customerOrderInfo.setId(customerOrderInfoExt.getId());
            customerOrderInfo.setOrderNo(customerOrderInfoExt.getOrderNo());
            customerOrderInfo.setExceptionDesc(message);
            customerOrderInfo.setOrderStatus(OrderStatus.AUDIT_FAILE.getKey());
            customerOrderInfo.setExcuteStatus(OrderStatus.AUDIT_FAILE.getKey());
            
            //如果是地址解析异常的，前端需要这个exceptionType显示纠错按钮，更新父单的值
            if (node == OrderOperateType.ADDRESS_ANALYSIS){
                customerOrderInfo.setExceptionType(ExceptionType.RECEIVER_ADDRESS_FAILED.getKey());
            }
    
            //2024年9月14日15:43:21 浩明：如果是订单映射异常，需要把解析到平台和客户都更新上
            if (node == OrderOperateType.ORDER_MAPPING){
                customerOrderInfo.setOrderType(customerOrderInfoExt.getOrderType());
                customerOrderInfo.setSiteCode(customerOrderInfoExt.getSiteCode());
                customerOrderInfo.setSiteName(customerOrderInfoExt.getSiteName());
                customerOrderInfo.setCustomerCode(customerOrderInfoExt.getCustomerCode());
                customerOrderInfo.setCustomerName(customerOrderInfoExt.getCustomerName());
                customerOrderInfoFeign.update(customerOrderInfo.getId(), customerOrderInfo);
            }else{
                orderInfoFeign.updateCustomerOrderInfoStatusByFlow(customerOrderInfo);
            }
        }

        //子流程更新状态
        if (null != orderInfoExt) {

            //更新子单状态
            OrderInfo statusParam = new OrderInfo();
            statusParam.setId(orderInfoExt.getId());
            statusParam.setOrderNo(orderInfoExt.getOrderNo());
            statusParam.setExceptionType(orderInfoExt.getExceptionType());
            statusParam.setExceptionDesc(message);
            statusParam.setOrderStatus(OrderStatus.AUDIT_FAILE.getKey());
            statusParam.setExcuteStatus(OrderStatus.AUDIT_FAILE.getKey());
            orderInfoFeign.updateOrderInfoStatusByFlow(statusParam);

            //子流程更新父单状态
            subFlowUpdataCustomerExcutetatus(orderInfoExt);
        }

    }

    /**
    * @description: 子流程更新父单状态
    * @param: [orderInfoExt]
    * @return: void
    * @author: 陈永培
    * @createtime: 2021/10/13 16:30
    */
    private void subFlowUpdataCustomerExcutetatus(OrderInfoExt orderInfoExt) {
        CustomerOrderInfo customerOrderInfo = getCustomerOrderInfo(orderInfoExt);
        ExcuteStatus customerExcuteStatus = ExcuteStatus.getStatus(customerOrderInfo.getExcuteStatus());
        // 统计子单成功失败数量
        JsonResponse<OrderExcuteCountRespone> statusRep = orderInfoFeign.countOrderExcuteStatus(customerOrderInfo.getOrderNo());
        OrderExcuteCountRespone data = statusRep.getData();
        Integer excuteFaild = data.getExcuteFaild();
        Integer excuteSuccess = data.getExcuteSuccess();
        
        //2025年5月26日20:30:20 永培：这就原来就不会为空，因为新增了质押节点，然后取消分仓后，导致 excuteFaild、excuteSuccess都为空了，所以这种就不需要更新了
        if (excuteFaild == null) {
            return;
        }
        
        // Integer excuteProcessing = data.getExcuteProcessing();
        Integer total = data.getTotal();
        boolean updateFlag = false;

        //  1.1 子单全部成功：审核成功
        if (total.equals(excuteSuccess) && customerExcuteStatus != ExcuteStatus.AUDITED) {
            
            // 1. 分仓完成
            if (CommonConstant.APARTED.equalsIgnoreCase(customerOrderInfo.getApartStatus())) {
                customerOrderInfo.setExceptionDesc("-");
                customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITED.getKey());
                customerOrderInfo.setExceptionType("-");
                //写入orderTrace
                orderverifyHelper.createOrderTrace(orderInfoExt, OrderStatus.AUDITED);
                updateFlag = true;
            }
            
        }
        // 1.2 子单全部失败：审核失败
//        if (total.equals(excuteFaild) && customerExcuteStatus != ExcuteStatus.FAILD) {
//            customerOrderInfo.setExcuteStatus(ExcuteStatus.FAILD.getKey());
//            updateFlag = true;
//        }
        // 1.3 子单部分失败：审核部分失败
//        if (!total.equals(excuteSuccess) && !total.equals(excuteFaild) && excuteFaild != 0 && customerExcuteStatus != ExcuteStatus.AUDIT_PART_FAILE) {
//            customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDIT_PART_FAILE.getKey());
//            updateFlag = true;
//        }
        //cyy:子单审核节点出现异常，则更新父单的【订单状态】、【审核状态】为【审核失败-199】,不需要判断是否为部分异常，去掉审核状态【155-部分审核失败】
        if (excuteFaild != 0 && customerExcuteStatus != ExcuteStatus.FAILD) {
            customerOrderInfo.setExcuteStatus(ExcuteStatus.FAILD.getKey());
            updateFlag = true;
        }
        // 1.4 子单审核中（无失败）：审核中
        if (!total.equals(excuteSuccess) && excuteFaild == 0 && customerExcuteStatus != ExcuteStatus.AUDITING) {
            customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
            updateFlag = true;
        }

        //更新父单状态
        if (updateFlag) {
            logger.info("ExpressFlowAspect =========> 子单{}审核状态变更，父单{}将置为新状态{}", orderInfoExt.getOrderNo(), customerOrderInfo.getOrderNo(), customerOrderInfo.getExcuteStatus());
            CustomerOrderInfo info = new CustomerOrderInfo();
            info.setId(customerOrderInfo.getId());
            info.setOrderNo(customerOrderInfo.getOrderNo());
            info.setOrderStatus(customerOrderInfo.getOrderStatus());
            info.setExcuteStatus(customerOrderInfo.getExcuteStatus());
            info.setExceptionType(customerOrderInfo.getExceptionType());
            info.setExceptionDesc(customerOrderInfo.getExceptionDesc());
            orderInfoFeign.updateCustomerOrderInfoStatusByFlow(info);
        }
    }

    /**
     * @description: 保存成功信息
     * @param: [customerOrderInfoExt, orderInfoExt, node, exception, message]
     * @return: void
     * @author: 陈永培
     * @createtime: 2021/10/10 22:20
     */
    private void saveSuccessLog(CustomerOrderInfoExt customerOrderInfoExt, OrderInfoExt orderInfoExt, OrderOperateType node, String message) throws Throwable {
        if (null != customerOrderInfoExt){
            orderLogHelper.saveLog(customerOrderInfoExt, node, CommonConstant.STRING_FLAG_YES, message);
        }else if(null != orderInfoExt){
            orderLogHelper.saveLog(orderInfoExt, node, CommonConstant.STRING_FLAG_YES, message);
        }

        //父单流程
        if (null != customerOrderInfoExt) {
            //订单映射
            if (node == OrderOperateType.ORDER_MAPPING){
                //写入orderTrace
                orderverifyHelper.createOrderTrace(customerOrderInfoExt, OrderStatus.NEW);
            }
        }

        //子流程
        if (null != orderInfoExt) {
            //合同计费信息
            // 加入时效，改为时效
            if (node == OrderOperateType.AGING_PRODUCT){
                //子单最后一个节点通过后，要处理父单异常的状态
                subFlowUpdataCustomerExcutetatus(orderInfoExt);
            }
        }
    }


    /** 
    * @description: 检测重复 以及 取消
    * @param: [customerOrderInfoExt, orderInfoExt, ticket, function, node] 
    * @return: void 
    * @author: 陈永培
    * @createtime: 2021/10/21 11:28
    */ 
    private void checkRepeatAndCancelLock(CustomerOrderInfoExt customerOrderInfoExt, OrderInfoExt orderInfoExt,String ticket, String function,OrderOperateType node) {
        String lockKey = ticket + "_" + function;
        // 检测重复调用
        boolean b = redisLockHelper.tryLock(lockKey);
        if (!b) {
            String message = "10秒内不允许重复调用,请稍后再试:" + function;
            logger.error("expressflow {} node {} is running, do not to run again!", ticket, function);
            if (null != customerOrderInfoExt){
                orderLogHelper.saveLog(customerOrderInfoExt, node, CommonConstant.STRING_FLAG_YES, message);
            }else if(null != orderInfoExt){
                orderLogHelper.saveLog(orderInfoExt, node, CommonConstant.STRING_FLAG_YES, message);
            }
            throw BusinessException.fail("repeat_" +message);
        }

        //检测取消锁
        String message = "检测到订单已经取消，流程终止！";
        if (null != customerOrderInfoExt){
            boolean  orderLock = orderStatusCheckHelper.checkMultipyOrderLock(customerOrderInfoExt.getOrderNo());
            if (orderLock){
                logger.info("=========> 检测订单取消锁成功，发现订单已加锁, 父单号： {}", customerOrderInfoExt.getOrderNo());
                orderStatusCheckHelper.updateCancel(customerOrderInfoExt,orderInfoExt);
                orderLogHelper.saveLog(customerOrderInfoExt, node, CommonConstant.STRING_FLAG_YES, message);
                throw BusinessException.fail("cancel_" + function);
            }
        }else if(null != orderInfoExt){
            boolean  orderLock = orderStatusCheckHelper.checkMultipyOrderLock(orderInfoExt.getParentOrderNo(),orderInfoExt.getOrderNo());
            if (orderLock){
                logger.info("=========> 检测订单取消锁成功，发现订单已加锁, 父单号： {}, 子单号 ：{}", orderInfoExt.getParentOrderNo(),orderInfoExt.getOrderNo());
                orderStatusCheckHelper.updateCancel(customerOrderInfoExt,orderInfoExt);
                orderLogHelper.saveLog(orderInfoExt, node, CommonConstant.STRING_FLAG_YES, message);
                throw BusinessException.fail("cancel_" + function);
            }
        }
    }




    private void unLock(String ticket, String node) {
        String lockKey = ticket + "_" + node;
        boolean b = redisLockHelper.unLock(lockKey);
    }

    /**
     * @description: 获取父单
     * @param: [orderInfoExt]
     * @return: com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo
     * @author: 陈永培
     * @createtime: 2021/10/13 15:21
     */
    private CustomerOrderInfo getCustomerOrderInfo(OrderInfoExt orderInfoExt) {
        //查询父单
        String parentOrderNo = orderInfoExt.getParentOrderNo();
        CustomerOrderInfo customerOrderInfo = orderverifyHelper.getCustomerOrderInfo(parentOrderNo);
        if(null == customerOrderInfo){
            throw BusinessException.fail("ExpressFlowAspect 找不到父单信息");
        }
        return customerOrderInfo;
    }

}