package com.midea.logistics.otplbs.fegin.agg;

import cn.hutool.db.PageResult;
import com.midea.logistics.otp.bean.agg.AgingRuleMileageConfig;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "AgingRuleMileageConfigCommonFeign")
public interface AgingRuleMileageConfigCommonFeign {

    @GetMapping("/aging/rule/mileage/config/list")
    JsonResponse<List<AgingRuleMileageConfig>> agingRuleMileageConfigList(@SpringQueryMap AgingRuleMileageConfig entity);

    @GetMapping("/aging/rule/mileage/config/page")
    JsonResponse agingRuleMileageConfigPage(@SpringQueryMap AgingRuleMileageConfig entity);

    @GetMapping("/aging/rule/mileage/config/count")
    JsonResponse<BaseDomain> agingRuleMileageConfigCount(@SpringQueryMap AgingRuleMileageConfig entity);

    @GetMapping("/aging/rule/mileage/config/detail")
    JsonResponse<AgingRuleMileageConfig> agingRuleMileageConfigDetail(@RequestParam("id") Long id);

    @GetMapping("/aging/rule/mileage/config/detail")
    JsonResponse<AgingRuleMileageConfig> agingRuleMileageConfigDetail(@SpringQueryMap AgingRuleMileageConfig entity);

    @PostMapping("/aging/rule/mileage/config/add")
    JsonResponse<BaseDomain> agingRuleMileageConfigAdd(@RequestBody AgingRuleMileageConfig entity);

    @PutMapping("/aging/rule/mileage/config/update")
    JsonResponse<Integer> agingRuleMileageConfigUpdate(@RequestBody AgingRuleMileageConfig entity);

    @DeleteMapping("/aging/rule/mileage/config/remove")
    JsonResponse<Integer> agingRuleMileageConfigRemove(@RequestBody AgingRuleMileageConfig entity);
    @PostMapping("/custom/agingRuleMileageConfig/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<AgingRuleMileageConfig> list);
}

