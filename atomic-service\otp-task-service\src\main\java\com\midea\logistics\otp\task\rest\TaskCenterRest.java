package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.bean.JobFlowControlRequest;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDeliveryType;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.service.ITaskCenterService;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.RestDoing;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskController
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:任务中心控制层
 */
@RestController
public class TaskCenterRest extends BaseController<Task> {

    private Logger logger = LoggerFactory.getLogger(TaskCenterRest.class);

    @Autowired
    private ITaskCenterService iTaskCenterService;


    /**
     * @param task
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_DETAIL_BY_TASK, method = RequestMethod.GET)
    public JsonResponse<TaskExt> queryByTask(Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            if (null == task || StringUtils.isBlank(task.getTaskNo())) {
                throw new BusinessException("任务号为空！");
            }
            String taskNo = task.getTaskNo();
            Task taskQr = new Task();
            taskQr.setTaskNo(taskNo);
            TaskExt taskDetail = iTaskCenterService.selectOneTaskDetail(taskQr);
            jsonResponse.data = taskDetail;
        };
        return doing.go(request, logger);

    }
    
    /**
     * 根据父单号查询所有的任务详情
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXT_BY_PARENT_ORDER_NO, method = RequestMethod.GET)
    public JsonResponse<List<TaskExt>> taskExtByParentOrderNo(@SpringQueryMap Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            if (null == task || StringUtils.isBlank(task.getParentOrderNo())) {
                throw new BusinessException("父单号为空！");
            }
            String parentOrderNo = task.getParentOrderNo();
            Task taskQr = new Task();
            taskQr.setParentOrderNo(parentOrderNo);
            List<TaskExt> taskDetails = iTaskCenterService.taskExtByParentOrderNo(taskQr);
            jsonResponse.data = taskDetails;
        };
        return doing.go(request, logger);

    }


    /**
     * @param task
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_DETAIL_SEARCH, method = RequestMethod.GET)
    public JsonResponse<TaskExt> queryByTaskSearch(Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            if (null == task) {
                throw new BusinessException("任务号、客户订单号为空！");
            }
            String taskNo = task.getTaskNo();
            Task taskQr = new Task();
            taskQr.setTaskNo(taskNo);
            taskQr.setCustomerOrderNo(task.getCustomerOrderNo());
            taskQr.setTaskType(task.getTaskType());
            if(StringUtils.isNotBlank(task.getOrderType())){
                taskQr.setOrderType(task.getOrderType());
            }
            if(StringUtils.isNotBlank(task.getDispatchNo())){
                taskQr.setDispatchNo(task.getDispatchNo());
            }
            TaskExt taskDetail = iTaskCenterService.selectOneTaskDetail(taskQr);
            jsonResponse.data = taskDetail;
        };
        return doing.go(request, logger);

    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_DETAIL, method = RequestMethod.GET)
    public JsonResponse<TaskExt> queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task();
            taskQr.setId(id);
            TaskExt taskDetail = iTaskCenterService.selectOneTaskDetail(taskQr);
            jsonResponse.data = taskDetail;
        };
        return doing.go(request, logger);

    }

    /**
     * 查询新建任务号
     * @param task
     * @param request
     * @return
     */
    @GetMapping(value = TaskRouters.TASK_NEW_LIST)
    public JsonResponse<List<String>> taskNewList(@SpringQueryMap Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<String> list = iTaskCenterService.taskNewList(task);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);

    }


    /**
     * 查询hold单需要下发任务号
     * @param task
     * @param request
     * @return
     */
    @GetMapping(value = TaskRouters.TASK_HOLD_LIST)
    public JsonResponse<List<String>> taskHoldList(@SpringQueryMap Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<String> list = iTaskCenterService.taskHoldList(task);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);

    }

    @GetMapping(value = TaskRouters.TASK_FLOW_CONTROL_LIST)
    public JsonResponse<List<String>> taskFlowControlList(@SpringQueryMap JobFlowControlRequest jobFlowControlRequest, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<String> list = iTaskCenterService.taskFlowControlList(jobFlowControlRequest);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);

    }

    @GetMapping(value = TaskRouters.TASK_APPT_LIST)
    public JsonResponse<List<Task>> taskApptList(@RequestParam("taskNo") String taskNo, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskCenterService.taskApptList(taskNo);
        };
        return doing.go(request, logger);

    }

    /**
     * @param task
     * @return
     */
    @RequestMapping(value = TaskRouters.TASKEXT_BY_TASKNO, method = RequestMethod.POST)
    public JsonResponse<TaskExt> queryTaskExtByTask(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            if (null == task || StringUtils.isBlank(task.getTaskNo())) {
                jsonResponse.fail2("任务号为空！");
            }
            String taskNo = task.getTaskNo();
            Task taskQr = new Task();
            taskQr.setTaskNo(taskNo);
            TaskExt taskDetail = iTaskCenterService.selectOneTaskExt(taskQr);
            jsonResponse.data = taskDetail;
        };
        return doing.go(request, logger);

    }



    /**
     * @param taskNos
     * @return
     */
    @RequestMapping(value = "/tasksByTaskNos", method = RequestMethod.POST)
    public JsonResponse<List<Task>> tasksByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            if (CollectionUtils.isEmpty(taskNos)) {
                jsonResponse.fail2("任务号为空！");
            }
            if (taskNos.size()>30000) {
                jsonResponse.fail2("批量查询超过3万！");
            }
            List<Task> tasks = iTaskCenterService.tasksByTaskNos(taskNos);
            jsonResponse.data = tasks;
        };
        return doing.go(request, logger);

    }

    /**
     * @param taskNos
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_DELIVERY_TYPE_BY_TASKNOS, method = RequestMethod.POST)
    public JsonResponse<List<TaskDeliveryType>> tasksDeliveryTypeByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            if (CollectionUtils.isEmpty(taskNos)) {
                jsonResponse.fail2("任务号为空！");
                return;
            }
            if (taskNos.size()>30000) {
                jsonResponse.fail2("批量查询超过3万！");
                return;
            }
            List<TaskDeliveryType> tasks = iTaskCenterService.tasksDeliveryTypeByTaskNos(taskNos);
            jsonResponse.data = tasks;
        };
        return doing.go(request, logger);

    }

    /**
     * @param task
     * @return
     */
    @RequestMapping(value = TaskRouters.SELECT_LAST_TASKEXT, method = RequestMethod.POST)
    public JsonResponse<TaskExt> selectLastTaskExt(@RequestBody TaskExt task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            if (null == task || (StringUtils.isBlank(task.getOrderNo()) && StringUtils.isBlank(task.getTaskNo()))) {
                return;
            }
            TaskExt taskDetail = iTaskCenterService.selectLastTaskExt(task);
            jsonResponse.data = taskDetail;
        };
        return doing.go(request, logger);
    }

    /**
     * 查询拦截任务和最新的一个任务
     */
    @RequestMapping(value = TaskRouters.SELECT_INTERCEPT_AND_LAST_TASKEXT, method = RequestMethod.POST)
    public JsonResponse<List<TaskExt>> selectInterceptAndLastTaskExt(@RequestBody TaskExt task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            if (null == task || (StringUtils.isBlank(task.getOrderNo()) && StringUtils.isBlank(task.getTaskNo()))) {
                return;
            }
            List<TaskExt> taskDetails = iTaskCenterService.selectInterceptAndLastTaskExt(task);
            jsonResponse.data = taskDetails;
        };
        return doing.go(request, logger);
    }

    /**
     * 查询最早第一段任务信息
     * @Date: 2023/9/11
     */
    @RequestMapping(value = TaskRouters.SELECT_DISTRIBUTION_NUM_ORDER_INFO,method = RequestMethod.POST)
    public JsonResponse<TaskExt> selectDistributionNumOrder(@RequestBody TaskExt task,HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            if (null == task || (StringUtils.isBlank(task.getOrderNo()))) {
                return;
            }
            TaskExt taskDetail = iTaskCenterService.selectDistributionNumOrderWithOrderBy(task);
            jsonResponse.data = taskDetail;
        };
        return doing.go(request, logger);
    }

    /**
     *根据关联单号，发车单号查询出库单
     * @Date: 2023/9/20
     */
    @RequestMapping(value = TaskRouters.SELECT_RELATION_OUT_ORDER_INFO,method = RequestMethod.POST)
    public JsonResponse<TaskExt> selectrelationOutOrder(@RequestBody TaskExt task,HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            if (null == task ||null == task.getCustomerOrderNo()) {
                return;
            }
            TaskExt taskDetail = iTaskCenterService.selectDistributionNumOrder(task);
            jsonResponse.data = taskDetail;
        };
        return doing.go(request, logger);
    }

    /**
     * 任务详情查询
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXT_BY_INDEX, method = RequestMethod.POST)
    public JsonResponse<List<TaskExt>> taskExtByIndex(@RequestBody TaskExt condition, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            List<TaskExt> taskDetails = iTaskCenterService.taskExtByIndex(condition);
            jsonResponse.data = taskDetails;
        };
        return doing.go(request, logger);

    }
}
