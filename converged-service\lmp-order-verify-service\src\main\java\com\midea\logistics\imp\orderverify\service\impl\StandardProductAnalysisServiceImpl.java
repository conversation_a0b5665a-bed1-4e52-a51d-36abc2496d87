package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.imp.orderverify.service.StandardProductAnalysisService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.order.PmsFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.BusinessControlParamHelper;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.service.CoverageAreaService;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.SyncOrderInfoToPmsDto;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 标准产品解析
 */
@Service
@Slf4j
public class StandardProductAnalysisServiceImpl implements StandardProductAnalysisService {
    @Autowired
    private PmsFeign pmsFeign;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private BusinessControlParamHelper businessControlParamHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private CoverageAreaService coverageAreaService;
    
    
    /**
     !@标准产品解析 - 入口（中台）
     */
    @Override
    @ZeebeFlow("STANDARD_PRODUCT_ANALYSIS")
    public CustomerOrderInfoExt standardProductAnalysis(CustomerOrderInfoExt customerOrderInfo) {
        if(customerOrderInfo == null || StringUtils.isEmpty(customerOrderInfo.getOrderNo())) {
            throw BusinessException.fail("标准产品解析,订单号为空");
        }
        this.issuePms(customerOrderInfo);
        String result = this.installMarking(customerOrderInfo);
        updateCustomerOrderInfo(customerOrderInfo);

        try{
            //电商覆盖范围解析
            coverageAreaService.coverageAreaAnalysis(customerOrderInfo);
        }catch (Exception e){
            log.info("{}电商覆盖范围解析异常：{}",customerOrderInfo.getOrderNo(),e.getMessage());
            //套送订单必须要解析
            if (businessHelper.isSetDeliveryOrder(customerOrderInfo)) {
                throw BusinessException.fail("覆盖范围解析异常：" + e.getMessage());
            }
        }

        FlowListenerParam.success("标准产品解析成功"+result);
        return customerOrderInfo;
    }

    /**
     * 下发pms
     * @param customerOrderInfo
     * @return
     */
    private CustomerOrderInfoExt issuePms(CustomerOrderInfoExt customerOrderInfo) {
        //cyy:所有非自提的订单都调【!（deliveryType=ZT  or（orderType=RI and deliveryType=EXPRESS））】
//        if (DeliveryType.isZT(customerOrderInfo.getDeliveryType()) || (OrderType.isRIOrder(customerOrderInfo.getOrderType()) && DeliveryType.isEXPRESS(customerOrderInfo.getDeliveryType()))) {
//            return customerOrderInfo;
//        }
        SyncOrderInfoToPmsDto syncOrderInfoToPmsDto = new SyncOrderInfoToPmsDto();
        syncOrderInfoToPmsDto.setOrderNo(customerOrderInfo.getOrderNo());
        syncOrderInfoToPmsDto.setParentOrderNo(customerOrderInfo.getOrderNo());
        syncOrderInfoToPmsDto.setCustomerOrderInfoExt(customerOrderInfo);
        syncOrderInfoToPmsDto.setPmsMsgType(PmsMsgType.RECEIVE.getKey());
        JsonResponse jsonResponse = pmsFeign.syncOrderInfoToPms(syncOrderInfoToPmsDto);
        if (jsonResponse != null && jsonResponse.judgeSuccess() && jsonResponse.getData() != null) {
            syncOrderInfoToPmsDto = JSONObject.parseObject(JSON.toJSONString(jsonResponse.getData()), SyncOrderInfoToPmsDto.class);
            customerOrderInfo.setProductCode(syncOrderInfoToPmsDto.getProductCode());
        }
        return customerOrderInfo;
    }

    /**
     * 送装服务打标
     * @param customerOrderInfo
     * @return
     */
    private String installMarking(CustomerOrderInfoExt customerOrderInfo) {
        if(DeliveryType.isZT(customerOrderInfo.getDeliveryType())){
            return "";
        }
        //仅大件电商服务平台需要解析
        if (!businessControlParamHelper.isBigBcSiteList(customerOrderInfo)) {
            return "";
        }
        StringBuilder msg = new StringBuilder("，送装服务打标成功，标识为：");
        //pms返回头等舱，打上标识为1
        if(CommonConstant.PRODUCT_CODE_TDC.equals(customerOrderInfo.getProductCode())){
            customerOrderInfo.setInstallFlag(CommonConstant.FLAG_YES);
            msg.append(CommonConstant.YES);
            return msg.toString();
        }
        //cyy:B2C销售出库订单才需要执行
        if (BusinessMode.isB2C(customerOrderInfo.getBusinessMode()) && OrderType.isPOOrder(customerOrderInfo.getOrderType())) {
            Integer vipService = businessHelper.getVipService(customerOrderInfo.getOrderNo(), customerOrderInfo.getCustomerOrderInfoExtend());
            //高端机打上标识为1
            //202405 泓铄 vipService==3 也打上送装标识=是
            if(VipServiceEnum.VIP_COLMO.getCode().equals(vipService) || VipServiceEnum.VIP_TOSHIBA.getCode().equals(vipService)){
                customerOrderInfo.setInstallFlag(CommonConstant.FLAG_YES);
                msg.append(CommonConstant.YES);
                return msg.toString();
            }

            if (Lists.newArrayList(ServiceType.PICKUP.getKey(), ServiceType.THJ.getKey(), ServiceType.BIG_SZYT.getKey(), ServiceType.SXQJ.getKey(), ServiceType.SXHS.getKey()).contains(customerOrderInfo.getServiceType())) {
                customerOrderInfo.setInstallFlag(CommonConstant.FLAG_YES);
                msg.append(CommonConstant.YES);
            } else {
                customerOrderInfo.setInstallFlag(CommonConstant.FLAG_NO);
                msg.append(CommonConstant.NO);
                //非第三方
                orderFlowHelper.judgeAndSetInstall(customerOrderInfo);
            }
            return msg.toString();
        }

        return "";
    }

    private void updateCustomerOrderInfo(CustomerOrderInfoExt customerOrderInfoExt) {
        CustomerOrderInfo updateDto = new CustomerOrderInfo();
        updateDto.setId(customerOrderInfoExt.getId());
        updateDto.setInstallFlag(customerOrderInfoExt.getInstallFlag());
        orderHelper.updateCustomerOrderInfo(updateDto,"标准产品解析");
    }

}
