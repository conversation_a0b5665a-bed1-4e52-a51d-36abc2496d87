package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder;
import com.midea.logistics.otp.ttx.inventory.domain.TTXRouters;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * ©Copyright 入库单
 * FileName: InboundOrderFeign
 * Author: yaowl
 * Date: 2019-6-2 21:46:18
 * Description:入库单
 */
@FeignClient(value = "logistics-otp-ttx-inventory-service")
public interface InboundOrderFeign {

    /** 
    * @Description: 上传，即新增入库单 
    * @Param: [inboundOrder]
    * @return: com.mideaframework.core.web.JsonResponse 
    * @Author: yaowl
    * @Date: 2019-8-29 
    */
    @PostMapping(value = TTXRouters.TTX_UPLOAD_INBOUNDORDER, consumes = "multipart/form-data")
    public JsonResponse inboundOrderTableUpload(@RequestBody MultipartFile file);

    /** 
    * @Description: 查询入库单
    * @Param: [inboundOrder]
    * @return: com.mideaframework.core.web.JsonResponse<com.mideaframework.core.web.PageResponse<com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder>>
    * @Author: yaowl
    * @Date: 2019-8-29 
    */
    @PostMapping(value = TTXRouters.TTX_SEARCH_INBOUNDORDER)
    public JsonResponse<PageResponse<InboundOrder>> search(@RequestBody InboundOrder inboundOrder) ;


    @RequestMapping(value =TTXRouters.TTX_DOWNLOAD_RECHECKISSHELF , method = RequestMethod.GET)
    public JsonResponse reCheckIsShelf(@PathVariable("id") Long id) ;



    /** 
    * @Description: 下载模板 
    * @Param:ScanningCodeFeign
    * @return: void 
    * @Author: yaowl
    * @Date: 2019-8-29 
    */
    @RequestMapping(value = TTXRouters.TTX_DOWNLOAD_INBOUNDORDERTEMPLATE, method = RequestMethod.GET)
    public byte[] inboundOrderTableDown( );
   
}
