package com.midea.logistics.otp.order.common.fegin.receive;


import com.midea.logistics.otp.order.converged.domain.dto.AllOrderInfoDto;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoAndDetail;
import com.midea.logistics.otp.order.domain.request.WfrV3LogRequest;
import com.mideaframework.core.web.JsonResponse;

import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 订单表 feign
 */
@FeignClient(value = "logistics-otp-order-receive-service")
public interface MidCustomerOrderInfoFeign {
    
    
    @GetMapping("/wfrV3OrderReVerifyGetOrder")
    JsonResponse<List<AllOrderInfoDto>> wfrV3OrderReVerifyGetOrder(@RequestParam("batchNo") String batchNo);
    
    @PostMapping("/wfrV3UpdateBuyerRemark")
    JsonResponse updateBuyerRemark(@RequestBody @Valid WfrV3LogRequest request);
    
    @RequestMapping(value = "/midCustomerOrderInfo", method = RequestMethod.POST)
    JsonResponse createMidCustomerOrderInfo(@Valid @RequestBody CustomerOrderInfo midCustomerOrderInfo);

    @RequestMapping(value = "/midCustomerOrderItem/insertBatch", method = RequestMethod.POST)
    JsonResponse insertBatchMidCustomerOrderItem(@RequestBody List<CustomerOrderItem> items);

    @RequestMapping(value = "/midCustomerOrderAddress", method = RequestMethod.POST)
    JsonResponse createMidCustomerOrderAddress(@RequestBody CustomerOrderAddress midCustomerOrderAddress);

    @PostMapping("/customerOrderInfo/saveMidCustomerOrderInfoAndDetail")
    JsonResponse saveMidCustomerOrderInfoAndDetail(@RequestBody CustomerOrderInfoAndDetail midCustomerOrderInfoAndDetail);

    @GetMapping("/customerOrderInfo/selectMidCustomerOrderInfoAndDetail")
    JsonResponse<CustomerOrderInfoAndDetail> selectMidCustomerOrderInfoAndDetail(@RequestParam("orderNo") String orderNo);

    /**
     * 查询列表
     *
     * @return
     */
    @PostMapping(value = "/midCustomerOrderInfo/list")
    JsonResponse<List<MidCustomerOrderInfo>> list(@RequestBody MidCustomerOrderInfo midCustomerOrderInfo);

    /**
     * 查询单个
     * @param midCustomerOrderInfo
     * @return
     */
    @PostMapping("/midCustomerOrderInfo/selectOne")
    JsonResponse<MidCustomerOrderInfo> selectOne(@RequestBody MidCustomerOrderInfo midCustomerOrderInfo);
    
    /**
     * @description: 根据多个订单查询
     * @param: [lis]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2025/4/19 16:55
     */
    @PostMapping(OrderRouters.SELECT_SOME_CUST_ORDER)
    public JsonResponse<List<CustomerOrderInfo>> selectSomeCustomerOrderInfo(@RequestBody List<CustomerOrderInfo> lis);

    /**
     * 根据IN单号修改接单表信息
     * @param midCustomerOrderInfo
     * @return
     */
    @RequestMapping(value = "/midCustomerOrderInfo/updateByOrderNo", method = RequestMethod.PUT)
    JsonResponse updateByOrderNo(@RequestBody MidCustomerOrderInfo midCustomerOrderInfo);

}
