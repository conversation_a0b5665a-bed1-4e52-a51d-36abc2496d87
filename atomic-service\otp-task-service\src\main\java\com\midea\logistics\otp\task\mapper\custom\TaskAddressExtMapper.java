package com.midea.logistics.otp.task.mapper.custom;

import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.mideaframework.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskAddressMapper
 * Author: lindq2
 * Date: 2019-6-28 10:56:27
 * Description:TaskAddressMapper服务接口
 */
@Mapper
public interface TaskAddressExtMapper extends BaseMapper<TaskAddress> {

    Integer batchUpdateNull(List<TaskAddress> taskAddresss);

    Integer batchUpdateByTaskNo(List<TaskAddress> taskAddresss);

    Integer batchUpdateCanSetEmptyByTaskNo(List<TaskAddress> taskAddresss);

    List<TaskAddress> searchLastTaskAddressListByOrderNos(@Param("orderNos") List<String> orderNos);
}