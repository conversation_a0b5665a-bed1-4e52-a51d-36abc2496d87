package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeServerRequestDto;
import com.mideaframework.core.web.JsonResponse;

public interface ZeebeLmpService {

    JsonResponse customerOrderVerifyZeebe(String orderNo);

    JsonResponse customerOrderVerifyZeebe(ZeebeServerRequestDto zeebeRequestDto);

    JsonResponse customerOrderVerifyZeebeCancel(String orderNo);

    JsonResponse orderVerifyZeebe(String orderNo);

    JsonResponse orderVerifyZeebe(ZeebeServerRequestDto zeebeRequestDto);

    JsonResponse zeebeSeparateWarehouse(String orderNo);

    JsonResponse zeebeCustomerOrderCancel(String key);

    JsonResponse zeebeRetry(String orderNo);

    JsonResponse zeebeCancelInstance(String orderNo);

    JsonResponse setVariables(String orderNo, Long key);

    JsonResponse resetZeebeParamter(String orderNo);

    JsonResponse searchIncident(Long instanceKey);

    boolean unLockCustomerOrder(String key);

    boolean unLockSubOrder(String orderNo);

    int trigger(String orderNo, boolean isParentOrder);

    JsonResponse verifyZeebeTest(String orderNo);
}