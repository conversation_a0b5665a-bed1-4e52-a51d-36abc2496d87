package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.PledgeInfo;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: PledgeInfoFeign
 * Author: lindq2
 * Date: 2019-5-23 9:52:11
 * Description:质押信息表 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface PledgeInfoFeign {

    @RequestMapping(value = "/pledgeInfo", method = RequestMethod.POST)
    JsonResponse create(@RequestBody PledgeInfo pledgeInfo);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/pledgeInfo/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/pledgeInfo/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody PledgeInfo pledgeInfo);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/pledgeInfo/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/pledgeInfos", method = RequestMethod.POST)
    JsonResponse search(@RequestBody PledgeInfo pledgeInfo);
}
