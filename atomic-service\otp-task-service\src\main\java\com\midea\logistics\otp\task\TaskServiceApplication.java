package com.midea.logistics.otp.task;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableEncryptableProperties
@EnableEurekaClient
@EnableFeignClients(value = {"com.midea.logistics.cache","com.midea.logistics.otp.common.feign"})
@ComponentScan(basePackages = {
    "com.mideaframework.core",
    "com.mideaframework.sdk",
    "com.midea.logistics",
})
@EnableAsync
public class TaskServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(TaskServiceApplication.class, args);
    }
}
