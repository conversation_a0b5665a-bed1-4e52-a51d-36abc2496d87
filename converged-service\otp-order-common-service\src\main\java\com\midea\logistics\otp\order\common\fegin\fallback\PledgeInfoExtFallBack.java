package com.midea.logistics.otp.order.common.fegin.fallback;

import com.midea.logistics.otp.order.domain.bean.PledgeInfo;
import com.midea.logistics.otp.order.domain.request.PledgeInfoBatchRequest;
import com.midea.logistics.otp.order.common.fegin.PledgeInfoExtFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import feign.hystrix.FallbackFactory;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年07月08日 上午08:56
 */
public class PledgeInfoExtFallBack implements FallbackFactory<PledgeInfoExtFeign> {
    @Override
    public PledgeInfoExtFeign create(Throwable throwable) {
        return new PledgeInfoExtFeign() {
            @Override
            public JsonResponse<List<PledgeInfo>> getByOrderNo(@Valid String orderNo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<List<PledgeInfo>> getPledgeByOrderNo(@Valid String orderNo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<List<PledgeInfo>> getPledgeByOrderNoAndSeccess(String orderNo, int pledgeFail) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<List<PledgeInfo>> getPledgeByOrderNo(String orderNo, Integer pledgeStatus) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<List<PledgeInfo>> getPledgeByOrderNo(String orderNo, Integer pledgeStatus, int pledgeFail) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> savePledgeInfoBatch(List<PledgeInfo> pledgeInfoList) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> updatePledgeInfoStatusBatch(List<PledgeInfoBatchRequest> pledgeInfoList) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> updateBatch(List<PledgeInfo> pledgeInfoList) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> cancelPledgeInfoBatch(List<PledgeInfo> pledgeInfoList) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<List<PledgeInfo>> getPledgeByParentOrderNo(String parentOrderNo, Integer pledgeStatus) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }
        };
    }
}
