package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.cache.manager.CdCommonMaterialManager;
import com.midea.logistics.domain.mdm.domain.CdCommonMaterial;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.imp.orderverify.service.IWaybillNoGenService;
import com.midea.logistics.otp.bean.ExpressCreateRequest;
import com.midea.logistics.otp.bean.ExpressCreateResponse;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.order.AcceptOrderApiMpFeign;
import com.midea.logistics.otp.common.feign.servicefeign.dispatch.BopFeign;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.helper.IdGenHelper;
import com.midea.logistics.otp.common.request.ExpressCreateWaybillRequest;
import com.midea.logistics.otp.common.response.ExpressCreateWaybillResponse;
import com.midea.logistics.otp.common.utils.ControlParamHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.common.utils.IfNullUtil;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.helper.TransUtils;
import com.midea.logistics.otp.order.common.fegin.BopServiceFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.CustomerOrderInfoHelper;
import com.midea.logistics.otp.order.common.helper.FaceSheetHelper;
import com.midea.logistics.otp.order.common.helper.OrderExceptionHelper;
import com.midea.logistics.otp.order.common.helper.OrderLogHelper;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.dto.IssuedItemExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@Service
@Slf4j
public class WaybillNoGenServiceImpl implements IWaybillNoGenService {
    private static final String UPPER_SYS_ORDER_ACCEPT_CONFIRM = "UPPER_SYS_ORDER_ACCEPT_CONFIRM";

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private AcceptOrderApiMpFeign  acceptOrderApiMpFeign;
    @Autowired
    private IdGenHelper idGenHelper;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private FaceSheetHelper faceSheetHelper;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private BopServiceFeign bopServiceFeign;
    @Autowired
    private OrderExceptionHelper orderExceptionHelper;
    @Autowired
    private CdCommonMaterialManager cdCommonMaterialManager;
    @Autowired
    private BopFeign bopFeign;
    @Autowired
    private OrderLogHelper orderLogHelper;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private ControlParamHelper controlParamHelper;
    @Autowired
    private CustomerOrderInfoHelper customerOrderInfoHelper;

    @Override
    @ZeebeFlow("GEN_WAYBILLNO")
    public OrderInfoExt genWaybillNo(OrderInfoExt orderInfo) {

        orderInfo = lmpOrderFlowHelper.getOrderInfoExt(orderInfo.getOrderNo(), orderInfo);
        String parentOrderNo = orderInfo.getParentOrderNo();
        if (ToolUtils.isEmpty(parentOrderNo)) {
            throw BusinessException.fail("父单号为空 ");
        }


        CustomerOrderAddress customerAddress = orderHelper.getCustomerAddress(parentOrderNo);
        if (ToolUtils.isEmpty(customerAddress)) {
            throw BusinessException.fail("父单地址查询失败 ");
        }

        if (StringUtils.isBlank(orderInfo.getDeliveryType())) {
            throw BusinessException.fail("配送方式未指定: " + JSONObject.toJSONString(orderInfo));
        }

        CustomerOrderInfo customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfo(orderInfo.getParentOrderNo());
        //C2M发送收货信息给快递服务获取运单号
        if (C2MType.isC2M(orderInfo.getC2mType())) {
            String msg = this.sendReceiveInfoForExpressNo(orderInfo, customerAddress, customerOrderInfo);
            FlowListenerParam.success(msg);
            return orderInfo;
        }
        //O2O发快递下发快递服务获取运单号
        if (SourceSystem.isMRP(orderInfo.getSourceSystem()) && ProjectClassifyEnum.isO2oExpress(orderInfo.getProjectClassify())) {
            String msg = this.createExpressWaybill(orderInfo, customerAddress, customerOrderInfo);
            FlowListenerParam.success(msg);
            return orderInfo;
        }
        orderInfo.setOaid(customerOrderInfo.getOaid());
        if (StringUtils.isNotBlank(orderInfo.getWaybillNo())){
            //2021年12月27日09:41:45 李娟
            FlowListenerParam hold = faceSheetHelper.checkHoldFlag(orderInfo, customerAddress);
            //2022-01-11 VIP 获取收件人（ 解密 ）并进行地址解析
            FlowListenerParam flowListenerParam = faceSheetHelper.updateReceiverAndExplainAddress(orderInfo, customerAddress);

            //2022年3月1日11:47:24 回传运单号给上游 &记录异常
            OrderInfoExt finalOrderInfo = orderInfo;
            CompletableFuture.runAsync(()->{
                String value = dictHelper.getDictVaule(UPPER_SYS_ORDER_ACCEPT_CONFIRM, finalOrderInfo.getSourceSystem());
                if (StringUtils.isNotEmpty(value)){
                    //已经有运单号，小米直配这个节点回传上游
                    if (SourceSystem.XiaoMi.getKey().equals(finalOrderInfo.getSourceSystem()) && DeliveryType.isWAREHOUSEMATCHING(finalOrderInfo.getDeliveryType())) {
                        this.orderAcceptConfirmUpperSystem(finalOrderInfo);
                    }
                }
            });

            if (null != flowListenerParam) {
                return orderInfo;
            }
            if (null == hold){
                 FlowListenerParam.success(orderInfo.getWaybillNo());
            }
            return orderInfo;
        }
        if(EnumUtils.getEnum(DeliveryType.class, orderInfo.getDeliveryType()) == DeliveryType.ZT){
             FlowListenerParam.success("配送方式为自提，无需生成运单");
            return orderInfo;
        }
        if (EnumUtils.getEnum(DeliveryType.class, orderInfo.getDeliveryType()) == DeliveryType.EXPRESS) {
            FlowListenerParam.success("快递运输单不生成运单号，等待WMS推送");
            return orderInfo;
        }
        //glh:基地订单（销售来源平台），运单生成的时候，不要去调快递服务接口,直接生成运单号
        CustomerOrderInfoExtend customerOrderInfoExtend = businessParamHelper.getCustomerOrderInfoExtend(parentOrderNo);
        if (customerOrderInfoExtend != null && OrderDistinctionFlag.isBoFRw(customerOrderInfoExtend.getOrderDistinctionFlag())) {
            orderInfo.setWaybillNo(customerOrderInfo != null && StringUtils.isNotBlank(customerOrderInfo.getExpressNo()) ? customerOrderInfo.getExpressNo() : idGenHelper.genAnntoWaybillNo());
            lmpOrderFlowHelper.updateOrderInfo(orderInfo, "更新运单号");
            FlowListenerParam.success(orderInfo.getWaybillNo());
            return orderInfo;
        }
        if (OrderType.PO.getKey().equals(orderInfo.getOrderType())&&!DeliveryType.ZT.getKey().equals(orderInfo.getDeliveryType()) && !DeliveryType.PO_GS.getKey().equals(orderInfo.getDeliveryType()) && !DeliveryType.EXPRESS.getKey().equals(orderInfo.getDeliveryType())) {
            JsonResponse<String> jsonResponse = acceptOrderApiMpFeign.waybillNoGen(orderInfo);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode()) || StringUtils.isEmpty(jsonResponse.getData())) {
                String errorMsg = "获取运单号失败，失败原因:" + JSONObject.toJSONString(jsonResponse);
                if(errorMsg.length()>150){
                    errorMsg = errorMsg.substring(0, 150);
                }
                throw BusinessException.fail(errorMsg);
            }
            orderInfo.setWaybillNo(jsonResponse.getData());
        } else {
            orderInfo.setWaybillNo(customerOrderInfo != null && StringUtils.isNotBlank(customerOrderInfo.getExpressNo()) ? customerOrderInfo.getExpressNo() : idGenHelper.genAnntoWaybillNo());
        }
        orderInfo.setOrderNo(orderInfo.getOrderNo());


        lmpOrderFlowHelper.updateOrderInfo(orderInfo, "更新运单号");

        if (SourceSystem.XiaoMi.getKey().equals(orderInfo.getSourceSystem())){
            customerOrderInfo.setWaybillNo(orderInfo.getWaybillNo());
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo,"更新运单号");
        }


        //2021年12月27日08:41:09 李娟：PDD平台下给ERP大件订单加密（收件人，收件电话，详细地址），导致订单无法运作，现通过电子面单接入解决详细地址加密问题
        FlowListenerParam hold = faceSheetHelper.hold(orderInfo, customerAddress);
        //2022-01-11 VIP 获取收件人（ 解密 ）并进行地址解析
        FlowListenerParam flowListenerParam = faceSheetHelper.updateReceiverAndExplainAddress(orderInfo, customerAddress);

        //2022年3月1日11:47:24 回传运单号给上游 &记录异常
        OrderInfoExt finalOrderInfo1 = orderInfo;
        CompletableFuture.runAsync(()->{
            String value = dictHelper.getDictVaule(UPPER_SYS_ORDER_ACCEPT_CONFIRM, finalOrderInfo1.getSourceSystem());
            if (StringUtils.isNotEmpty(value)){
                //小米宅配这个节点不回传上游，网点反馈后再回传
                if (SourceSystem.XiaoMi.getKey().equals(finalOrderInfo1.getSourceSystem()) && DeliveryType.isDOTType(finalOrderInfo1.getDeliveryType())) {
                    return;
                }
                this.orderAcceptConfirmUpperSystem(finalOrderInfo1);
            }
        });

        if (null != flowListenerParam) {
            return orderInfo;
        }

         if (null == hold){
             FlowListenerParam.success(orderInfo.getWaybillNo());
         }
         
        return orderInfo;

    }


    private void orderAcceptConfirmUpperSystem(OrderInfo orderInfo){
        JsonResponse<String> bopResponse = bopServiceFeign.orderAcceptConfirm(orderInfo);
        if (null == bopResponse || !BaseCodeEnum.SUCCESS.getCode().equals(bopResponse.getCode()) || StringUtils.isEmpty(bopResponse.getData())){
            if (SourceSystem.XiaoMi.getKey().equals(orderInfo.getSourceSystem())) {
                orderLogHelper.saveLog(orderInfo,OrderOperateType.XIOAMI_WAYBILLNO,CommonConstant.STRING_FLAG_NO,"异常");
            }
            //插入异常表
            orderExceptionHelper.saveException(orderInfo, ExceptionType.ORDER_ACCEPT_CONFIRM_CALL_BACK, OrderOperateType.ORDER_ACCEPT_CONFIRM_ROUTE_CALL_BACK,null);
            return;
        }
        String response = bopResponse.getData();
        JsonResponse businessResponse = JSON.parseObject(response, JsonResponse.class);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(businessResponse.getCode())){
            if (SourceSystem.XiaoMi.getKey().equals(orderInfo.getSourceSystem())) {
                orderLogHelper.saveLog(orderInfo,OrderOperateType.XIOAMI_WAYBILLNO,CommonConstant.STRING_FLAG_NO,"异常:" + bopResponse.getMsg());
            }
            //业务异常
            orderExceptionHelper.saveException(orderInfo, ExceptionType.ORDER_ACCEPT_CONFIRM_CALL_BACK, OrderOperateType.ORDER_ACCEPT_CONFIRM_ROUTE_CALL_BACK,businessResponse.getMsg());
            return;
        }
    }

    /**
     * OTP用收货信息去调用快递服务获取快递单号
     * @param customerAddress
     * @param customerOrderInfo
     */
    public String sendReceiveInfoForExpressNo(OrderInfoExt orderInfo, CustomerOrderAddress customerAddress, CustomerOrderInfo customerOrderInfo) {
        ExpressCreateRequest expressCreateRequest = new ExpressCreateRequest();
        expressCreateRequest.setOrderNo(orderInfo.getOrderNo());
        expressCreateRequest.setErpOrderNo(orderInfo.getCustomerOrderNo());
        expressCreateRequest.setThirdOrderNo(orderInfo.getWorkOrderNo());
        expressCreateRequest.setOrderType(orderInfo.getOrderType());
        expressCreateRequest.setWarehouseCode(orderInfo.getWhCode());
        expressCreateRequest.setCompanyCode(orderInfo.getCompanyCode());
        expressCreateRequest.setCustomerCode(orderInfo.getCustomerCode());
        expressCreateRequest.setStoreCode(orderInfo.getShopId());
        expressCreateRequest.setProjectClassify(orderInfo.getProjectClassify());
        expressCreateRequest.setSourceSystem(SourceSystem.OTP.getKey());
        expressCreateRequest.setTotalValue(orderInfo.getTotalAmount());
        expressCreateRequest.setTotalWeight(orderInfo.getTotalGrossWeight());
        expressCreateRequest.setTotalVolume(orderInfo.getTotalVolume());
        expressCreateRequest.setTotalPackageQty(orderInfo.getTotalPkgQty().intValue());
        expressCreateRequest.setCustomerRemark(orderInfo.getBuyerRemark());
        expressCreateRequest.setOrderSign(C2MType.C2M.getKey());
        expressCreateRequest.setReceiverName(customerAddress.getReceiverName());
        expressCreateRequest.setReceiverMobile(customerAddress.getReceiverMobile());
        expressCreateRequest.setReceiverPhone(customerAddress.getReceiverTel());
        expressCreateRequest.setReceiverProvince(customerAddress.getReceiverProvinceName());
        expressCreateRequest.setReceiverCity(customerAddress.getReceiverCityName());
        expressCreateRequest.setReceiverArea(customerAddress.getReceiverDistrictName());
        expressCreateRequest.setReceiverTown(customerAddress.getReceiverTownName());
        expressCreateRequest.setReceiverAddress(customerAddress.getReceiverDetailAddr());
        expressCreateRequest.setSecondReceiverName(customerAddress.getReceiverName());
        expressCreateRequest.setSecondReceiverMobile(customerAddress.getReceiverMobile());
        expressCreateRequest.setSecondReceiverPhone(customerAddress.getReceiverTel());
        expressCreateRequest.setSecondReceiverProvince(customerAddress.getReceiverProvinceName());
        expressCreateRequest.setSecondReceiverCity(customerAddress.getReceiverCityName());
        expressCreateRequest.setSecondReceiverArea(customerAddress.getReceiverDistrictName());
        expressCreateRequest.setSecondReceiverTown(customerAddress.getReceiverTownName());
        expressCreateRequest.setSecondReceiverAddress(customerAddress.getReceiverDetailAddr());

        if (!CollectionUtils.isEmpty(orderInfo.getOrderInfoItems())) {
            List<OrderInfoItem> list = orderInfo.getOrderInfoItems();
            List<ExpressCreateRequest.OrderItemsItem> orderItems = list.stream().map(i -> {
                ExpressCreateRequest.OrderItemsItem orderItemsItem = expressCreateRequest.new OrderItemsItem();
                orderItemsItem.setLineNo(i.getItemLineNo());
                orderItemsItem.setItemCode(i.getItemCode());
                orderItemsItem.setItemName(i.getItemName());
                orderItemsItem.setItemClassify(i.getItemClass());
                orderItemsItem.setOrderQty(IfNullUtil.getBigDecimal(i.getPlanQty()));
                orderItemsItem.setItemStatus(i.getItemStatus());
                orderItemsItem.setUnitDesc(i.getUnit());
                orderItemsItem.setUnitValue(IfNullUtil.getBigDecimal(i.getPrice()));
                orderItemsItem.setItemSuitCode(i.getItemSuiteCode());
                orderItemsItem.setUnitWeight(IfNullUtil.getBigDecimal(i.getSingleWeight(), i.getGrossWeight()));
                orderItemsItem.setUnitVolume(IfNullUtil.getBigDecimal(i.getSingleVolume(), i.getVolume()));
                CdCommonMaterial cdCommonMaterial = cdCommonMaterialManager.getCdCommonMaterialCache(i.getItemCode());
                orderItemsItem.setUnitLength(IfNullUtil.getBigDecimal(cdCommonMaterial.getCdcmLength()));
                orderItemsItem.setUnitWidth(IfNullUtil.getBigDecimal(cdCommonMaterial.getCdcmWidth()));
                orderItemsItem.setUnitHeight(IfNullUtil.getBigDecimal(cdCommonMaterial.getCdcmHeight()));
                if (null != i.getSetFlag()) {
                    orderItemsItem.setKitFlag(CommonEnum.YES.getValue().equals(i.getSetFlag()) ? CommonEnum.YES.getKey() : CommonEnum.N.getKey());
                }
                return orderItemsItem;
            }).collect(Collectors.toList());
            expressCreateRequest.setOrderItems(orderItems);
        }
        //调用快递服务获取快递单号
        JsonResponse jsonResponse = bopFeign.expressServiceUnmapping(expressCreateRequest);
        if (null == jsonResponse || null == jsonResponse.getData()) {
            throw BusinessException.fail(null == jsonResponse ? "调快递服务接口失败" : "调快递服务接口失败:" + jsonResponse.getMsg());
        }
        //承运商匹配状态及获取运单号状态都为成功时才获取运单号
        JsonResponse jsonResponseData = JSON.parseObject(jsonResponse.getData().toString(), JsonResponse.class);
        ExpressCreateResponse data = JSON.parseObject(jsonResponseData.getData().toString(), ExpressCreateResponse.class);
        if (!CommonConstant.MATCH.equals(data.getMatchStatus())) {
            throw BusinessException.fail("调快递服务接口失败:"+data.getMatchFailureReason());
        }
        if (!CommonConstant.MATCH.equals(data.getAcquireStatus())) {
            throw BusinessException.fail("调快递服务接口失败:"+data.getFailureReason());
        }
        String primaryWaybillCode = data.getPrimaryWaybillCode();
        String carrierCode = data.getCarrierCode();
        CustomerOrderInfo customerOrderInfo1 = new CustomerOrderInfo(customerOrderInfo.getId());
        customerOrderInfo1.setWaybillNo(primaryWaybillCode);
        customerOrderInfo1.setCarrierCode(carrierCode);
        lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo1,"更新运单号");

        OrderInfo newOrderInfo = new OrderInfo(orderInfo.getId());
        newOrderInfo.setWaybillNo(primaryWaybillCode);
        newOrderInfo.setCarrierCode(carrierCode);
        lmpOrderFlowHelper.updateOrderInfo(newOrderInfo,"更新运单号");

        orderInfo.setWaybillNo(primaryWaybillCode);
        orderInfo.setCarrierCode(carrierCode);
        return primaryWaybillCode;
    }

    /**
     * 调快递服务下发快递接口并获取运单号
     * @return
     */
    private String createExpressWaybill(OrderInfoExt orderInfo, CustomerOrderAddress customerAddress, CustomerOrderInfo customerOrderInfo) {
        HashMap<String, String> o2oExpressDict = dictHelper.getDictToMapNotNull(CommonConstant.O2O_EXPRESS_CUSTOMER);
        HashMap<String, String> o2oWpDict = dictHelper.getDictToMapNotNull(CommonConstant.O2O_EXPRESS_WP_CODE);
        ExpressCreateWaybillRequest request = new ExpressCreateWaybillRequest();
        BeanUtils.copyProperties(orderInfo, request);
        request.setCustomerOrderAddress(customerAddress);
        request.setAccountNo(controlParamHelper.getVaule(CommonConstant.O2O_EXPRESS_ACCOUNT));
        BigDecimal totalQty = orderInfo.getTotalQty();
        //如果订单商品超过1件默认下子母件，传ture，否则传false
        if (totalQty != null && totalQty.compareTo(BigDecimal.ONE) > 0) {
            request.setMultiPackagesShipment(true);
        }
        List<CustomerOrderItem> itemList = customerOrderInfoHelper.getCustomerOrderItemsByOrderNo(orderInfo.getParentOrderNo());
        if (ToolUtils.isNotEmpty(itemList)) {
            boolean b = itemList.stream().anyMatch(item -> {
                IssuedItemExt issuedItemExt = item.extendInfoFieldsToDto();
                return CommonConstant.Y.equals(issuedItemExt.getIsInsured());
            });
            if (b) {
                BigDecimal totalPrice = itemList.stream().map(item -> TransUtils.getBigDecimal(item.getPrice()).multiply(TransUtils.getBigDecimal(item.getPlanQty()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                request.setLogisticsServices(String.format("[{\"service_code\":\"insured\",\"service_value\":\"%s\"}]", TransUtils.stripZerosAndAbs(totalPrice)));
            }
            String snServiceCode = itemList.get(0).extendInfoFieldsToDto().getSnServiceCode();
            if (StringUtils.isNotBlank(snServiceCode) && !CommonConstant.N.equals(snServiceCode)) {
                request.setWpServiceCode(o2oWpDict.get(snServiceCode));
                request.setSubsidySource("merchant");
                request.setWpMerchantCode(o2oExpressDict.get("wpMerchantCode"));
            }
        }

        JsonResponse jsonResponse = bopFeign.createExpressWaybill(request);
        if (null == jsonResponse || null == jsonResponse.getData()) {
            throw BusinessException.fail(null == jsonResponse ? "调快递服务下发快递接口失败" : "调快递服务接口失败:" + jsonResponse.getMsg());
        }
        ExpressCreateWaybillResponse expressCreateWaybillResponse = JSON.parseObject(jsonResponse.getData().toString(), ExpressCreateWaybillResponse.class);
        String waybillNo = expressCreateWaybillResponse.getParentWaybillCode();
        if (StringUtils.isBlank(waybillNo)) {
            ExpressCreateWaybillResponse.WaybillCloudPrintResponse waybillCloudPrintResponse = expressCreateWaybillResponse.getWaybillCloudPrintResponse().get(0);
            waybillNo = waybillCloudPrintResponse.getMailNo();
            if (StringUtils.isBlank(waybillNo)) {
                throw BusinessException.fail("快递服务返回运单号为空");
            }
        }

        OrderInfo newOrderInfo = new OrderInfo(orderInfo.getId());
        newOrderInfo.setWaybillNo(waybillNo);
        lmpOrderFlowHelper.updateOrderInfo(newOrderInfo,"更新运单号");

        orderInfo.setWaybillNo(waybillNo);
        return waybillNo;
    }

}
