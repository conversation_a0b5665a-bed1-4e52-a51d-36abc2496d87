package com.midea.logistics.otp.order.common.fegin;


import com.midea.logistics.otp.order.common.bean.CountrySubsidyInfo;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskAggFeign
 * Author: lindq2
 * Description: feign
 */
@FeignClient(value = "logistics-task-agg-service")
public interface TaskAggFeign {
    @PostMapping(value = "/upDownLoadToImageOss")
    JsonResponse<List<String>> upDownLoadToImageOss(@RequestBody List<String> imgUrls);

    @PostMapping(value = "/pushCountrySubsidyInfo")
    JsonResponse pushCountrySubsidyInfo(@RequestBody CountrySubsidyInfo countrySubsidyInfo);
}
