package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.bean.ProjectClassifyConfig;
import com.midea.logistics.otp.rule.domain.request.ProjectClassifyConfigRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ProjectClassifyConfigFeign
 * Author: liwende
 * Date: 2019-5-24 16:15:55
 * Description:项目分类规则 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface ProjectClassifyConfigFeign {

    @RequestMapping(value = "/projectClassifyConfig", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ProjectClassifyConfig projectClassifyConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/projectClassifyConfig/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/projectClassifyConfig/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ProjectClassifyConfig projectClassifyConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/projectClassifyConfig/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/projectClassifyConfigs", method = RequestMethod.GET)
    public JsonResponse search(@SpringQueryMap ProjectClassifyConfigRequest projectClassifyConfigRequest);


    /**
     * 批量修改和批量新增统一保存
     *
     * @param
     * @param
     * @param
     * @return
     */
    @RequestMapping(value = "/projectClassifyConfig/batchCreateOrUpdate", method = RequestMethod.POST)
    public JsonResponse batchCreateOrUpdate(/**@Valid*/@RequestBody List<ProjectClassifyConfigRequest> projectClassifyConfigRequests);


    /**
     * 批量删除
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/projectClassifyConfigs/{id}", method = RequestMethod.DELETE)
    public JsonResponse batchDelete(@PathVariable("id") Long[] ids);

}
