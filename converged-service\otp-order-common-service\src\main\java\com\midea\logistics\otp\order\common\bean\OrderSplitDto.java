package com.midea.logistics.otp.order.common.bean;


import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;

import java.util.List;

/**
 * 分仓临时处理
 */
public class OrderSplitDto {

    private List<OrderInfoExt> orderInfoExts;

    private List<String> orderNos;
    private String errMsg;


    public List<OrderInfoExt> getOrderInfoExts() {
        return orderInfoExts;
    }

    public void setOrderInfoExts(List<OrderInfoExt> orderInfoExts) {
        this.orderInfoExts = orderInfoExts;
    }
    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }
    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    //手工成功返回单号
    public static OrderSplitDto successH(List<String> orderNos,List<OrderInfoExt> orderInfoExts){
        OrderSplitDto orderSplitDto = new OrderSplitDto();
        orderSplitDto.setOrderInfoExts(orderInfoExts);
        orderSplitDto.setOrderNos(orderNos);
        return orderSplitDto;
    }
    public static OrderSplitDto success(List<OrderInfoExt> orderInfoExts){
        OrderSplitDto orderSplitDto = new OrderSplitDto();
        orderSplitDto.setOrderInfoExts(orderInfoExts);
        return orderSplitDto;
    }
    public static OrderSplitDto fail(String errMsg){
        OrderSplitDto orderSplitDto = new OrderSplitDto();
        orderSplitDto.setErrMsg(errMsg);
        return orderSplitDto;
    }
}
