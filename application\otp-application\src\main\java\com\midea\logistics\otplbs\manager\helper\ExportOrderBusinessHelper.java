package com.midea.logistics.otplbs.manager.helper;

import com.google.common.collect.Lists;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.enums.OrderSource;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.order.converged.domain.response.CustomerOrderInfoResponse;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.request.VagueRelationRequest;
import com.midea.logistics.otplbs.fegin.CustomerOrderInfoFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
@Slf4j
public class ExportOrderBusinessHelper {
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    public CustomerOrderInfo searchSuitedRelateOrder(CustomerOrderInfoResponse customerOrderInfo) {
        log.info("searchSuitedRelateOrder:{}",customerOrderInfo);
        if (customerOrderInfo == null || StringUtils.isBlank(customerOrderInfo.getRelationOrderNo()) || CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag()) ) {
            return null;
        }
        CustomerOrderInfo queryCustomerOrderInfo = new CustomerOrderInfo();
        queryCustomerOrderInfo.setCustomerOrderNo(customerOrderInfo.getRelationOrderNo());
        JsonResponse<CustomerOrderInfo> customerOrderInfoJsonResponse = customerOrderInfoFeign.searchOne(queryCustomerOrderInfo.toString());
        //若存在关联单号，则判断下关联订单是否为同客户、手工单来源、未发车（orderStatus<370）、纯运输的模糊订单
        if (customerOrderInfoJsonResponse != null && BaseCodeEnum.SUCCESS.getCode().equals(customerOrderInfoJsonResponse.getCode())) {
            CustomerOrderInfo relateCustomerOrderInfo = customerOrderInfoJsonResponse.getData();
            if (relateCustomerOrderInfo != null){
                log.info("订单{}客户：{}，关联订单{}客户：{}",customerOrderInfo.getCustomerOrderNo(),customerOrderInfo.getCustomerCode(),relateCustomerOrderInfo.getCustomerOrderNo(),relateCustomerOrderInfo.getCustomerCode());
                if(customerOrderInfo.getCustomerCode().equals(relateCustomerOrderInfo.getCustomerCode())
                    && OrderSource.HANDLE.getKey().equals(relateCustomerOrderInfo.getOrderSource())
                    && relateCustomerOrderInfo.getOrderStatus() < 370
                    && OrderType.YS.getKey().equals(relateCustomerOrderInfo.getOrderType())
                    && relateCustomerOrderInfo.getPlanOrderFlag() == 1){
                    return relateCustomerOrderInfo;
                }
            }
        }
        return null;
    }

    @NotNull
    public ArrayList<VagueRelationRequest> getVagueRelationRequests(HashMap<String, String> relateOrderMap) {
        Set<Map.Entry<String, String>> entries = relateOrderMap.entrySet();
        ArrayList<VagueRelationRequest> vagueRelationRequests = Lists.newArrayList();
        for (Map.Entry<String, String> entry : entries) {
            String exactOrderNo = entry.getKey();
            String vagueOrderNo = entry.getValue();
            VagueRelationRequest vagueRelationRequest = new VagueRelationRequest();
            boolean hasSameVagueOrder = false;
            for (VagueRelationRequest relationRequest : vagueRelationRequests) {
                if(relationRequest.getVagueOrder().equals(vagueOrderNo)) {
                    relationRequest.getExactOrder().add(exactOrderNo);
                    hasSameVagueOrder = true;
                }
            }
            if(hasSameVagueOrder){
                continue;
            }
            ArrayList<String> newExactOrder = Lists.newArrayList();
            newExactOrder.add(exactOrderNo);
            vagueRelationRequest.setExactOrder(newExactOrder);
            vagueRelationRequest.setVagueOrder(vagueOrderNo);
            vagueRelationRequests.add(vagueRelationRequest);
        }
        return vagueRelationRequests;
    }
}
