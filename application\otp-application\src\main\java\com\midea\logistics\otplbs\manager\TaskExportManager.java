package com.midea.logistics.otplbs.manager;

import java.io.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.helper.ExportExcel;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.bean.CustomerAgingConfigExcelRequest;
import com.midea.logistics.otp.common.helper.bean.CustomerAgingConfigExcelResponse;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.order.domain.bean.CustomerAgingConfig;
import com.midea.logistics.otp.order.domain.bean.CustomerAgingConfigDatail;
import com.midea.logistics.otp.order.domain.bean.custom.BigCustomerAgingConfig;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.midea.logistics.otplbs.fegin.CustomerAgingConfigFeign;
import com.midea.logistics.otplbs.fegin.TaskFeign;
import com.midea.logistics.otplbs.manager.helper.CheckCustomerAgingConfigExcel;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.Encodes;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.wkclz.util.excel.Excel;
import com.wkclz.util.excel.ExcelException;
import com.wkclz.util.excel.ExcelRow;
import com.wkclz.util.excel.ExcelUtil;

/**
* @description: 任务导出
* @author: 陈永培
* @createtime: 2019/8/27 17:07
*/
@Component
public class TaskExportManager extends  BaseExportManager{

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private TaskFeign taskFeign;

    public void exportTask(SearchTaskRequest searchTaskRequest, HttpServletResponse response) throws IOException {
        String fileName = "任务信息.xlsx";
        searchTaskRequest.setIsExport(CommonConstant.STRING_FLAG_YES);
        searchTaskRequest.setDataAuthFlag(1);
        List<SearchTaskResponse> list = Lists.newArrayList();
        JsonResponse<PageResponse<SearchTaskResponse>>  search = taskFeign.searchTaskList(searchTaskRequest);
        if (search == null || search.data() == null) {
            list = Lists.newArrayList();
        }else{
            list = search.getData().getList();
        }

        Excel excel = new Excel();
        excel.setTitle("任务信息");

        String[] header = { "任务号", "客户订单号", "运单号", "任务类型", "状态执行状态", "客户", "平台",
                            "仓库", "订单类型", "项目分类", "配送类型","业务类型", "订单号", "总数量", "总体积",
                            "总重量", "收货地址", "发货地址", "网点名称", "网点编码", "网点地址", "是否分拨", "分拨仓",
                            "创建时间", "是否商超", "是否仓间调拨", "上撤样",
                            "卸货逾期一级原因", "卸货逾期二级原因", "卸货逾期登记时间","卸货逾期描述",
                            "到货逾期一级原因", "到货逾期二级原因", "到货逾期登记时间", "到货逾期联系人", "到货逾期联系电话", "到货逾期备注",
                            "发货逾期一级原因", "发货逾期二级原因", "发货逾期登记时间", "发货逾期联系人", "发货逾期联系电话", "发货到货逾期备注"};
        excel.setHeader(header);

        for (SearchTaskResponse object : list) {

            ExcelRow row = excel.createRow();
            // 任务号
            row.addCell(object.getTaskNo());
            //客户订单号
            row.addCell(object.getCustomerOrderNo());
            //运单号
            row.addCell(object.getWaybillNo());
            //任务类型
            row.addCell(object.getTaskTypeName());
            //状态执行状态
            row.addCell(object.getExcuteStatusName());
            //客户
            row.addCell(object.getCustomerName());
            //平台
            row.addCell(object.getSiteName());

            //仓库
            row.addCell(object.getWhName());
            //订单类型
            row.addCell(object.getOrderTypeName());
            //项目分类
            row.addCell(object.getProjectClassify());
            //配送类型
            row.addCell(object.getDeliveryTypeName());
            //业务类型
            row.addCell(object.getBusinessMode());
            //订单号
            row.addCell(object.getOrderNo());
            //总数量
            row.addCell(object.getTotalQty());
            //总体积
            row.addCell(object.getTotalVolume());

            //总重量
            row.addCell(object.getTotalNetWeight());
            //收货地址
            row.addCell(object.getReceiverProvinceName() + "/" + object.getReceiverCityName()+ "/" + object.getReceiverDistrictName()+ "/" + object.getReceiverTownName());
            //发货地址
            row.addCell(object.getSenderProvinceName() + "/" + object.getSenderCityName()+ "/" + object.getSenderDistrictName()+ "/" + object.getSenderTownName());
            //网点名称
            row.addCell(object.getNetworkName());
            //网点编码
            row.addCell(object.getNetworkCode());
            //网点地址
            row.addCell(object.getNetworkAddr());
            //是否分拨
            row.addCell(CommonEnum.commonDesc(object.getDistributionFlag()));
            //分拨仓
            row.addCell(object.getDistributionWhName());


            //创建时间
            row.addCell(object.getCreateTime());
            //是否分拨
            row.addCell(CommonEnum.commonDesc(object.getScPosFlag()));
            //是否仓间调拨
            row.addCell(CommonEnum.commonDesc(object.getUpstreamDocType()));
            //上撤样
            row.addCell(object.getSpecimenType());


            //卸货逾期一级原因
            row.addCell(object.getInOverdueReason1Name());
            //卸货逾期二级原因
            row.addCell(object.getInOverdueReason2Name());
            //卸货逾期登记时间
            row.addCell(object.getInOverdueTime());
            //卸货逾期描述
            row.addCell(object.getInOverdueRemark());

            //卸货逾期一级原因
            row.addCell(object.getArriveOverdueReason1Name());
            //卸货逾期二级原因
            row.addCell(object.getArriveOverdueReason2Name());
            //卸货逾期登记时间
            row.addCell(object.getArriveOverdueTime());
            //到货逾期联系人
            row.addCell(object.getArriveOverdueContactName());
            //到货逾期联系电话
            row.addCell(object.getArriveOverdueContactMobile());
            //到货逾期备注
            row.addCell(object.getArriveOverdueRemark());


            //发货逾期一级原因
            row.addCell(object.getOutOverdueReason1Name());
            //发货逾期二级原因
            row.addCell(object.getOutOverdueReason2Name());
            //发货逾期登记时间
            row.addCell(object.getOutOverdueTime());
            //发货逾期联系人
            row.addCell(object.getOutContactName());
            //发货逾期联系电话
            row.addCell(object.getOutContactMobile());
            //发货到货逾期备注
            row.addCell(object.getOutOverdueRemark());

        }

        //导出
       this.export(response, fileName, excel);

    }



}


