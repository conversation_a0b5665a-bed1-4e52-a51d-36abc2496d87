package com.midea.logistics.imp.orderverify.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.logistics.imp.orderverify.service.IOutAreaService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.OrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.PledgeInfoExtFeign;
import com.midea.logistics.otp.order.common.fegin.PledgeInfoFeign;
import com.midea.logistics.otp.order.common.fegin.bop.OrderItemAreaFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.BusinessControlParamHelper;
import com.midea.logistics.otp.order.common.helper.CountAreaHelper;
import com.midea.logistics.otp.order.common.service.IsOutAreaService;
import com.midea.logistics.otp.order.converged.domain.enums.PledgeFailEnum;
import com.midea.logistics.otp.order.converged.domain.request.OutAreaRequest;
import com.midea.logistics.otp.order.converged.domain.response.OutAreaResponse;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.PledgeInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParamDetail;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: OutAreaManager
 * @author: crystal
 * @date: 2019/7/3 14:24
 * @description: 入安得仓时，校验是否爆仓：validFlag  存储，是否需要在order_info表中增加字段
 */
@Component
public class OutAreaServiceImpl implements IOutAreaService {

    private Logger logger = LoggerFactory.getLogger(OutAreaServiceImpl.class);

    @Autowired
    private CountAreaHelper countAreaHelper;
    @Autowired
    private OrderItemAreaFeign orderItemAreaFeign;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private PledgeInfoExtFeign pledgeInfoExtFeign;
    @Autowired
    private PledgeInfoFeign pledgeInfoFeign;
    @Autowired
    private IsOutAreaService isOutAreaService;


    /**
     * 出库单：
     * 1、若是入安得仓的，则检验客户入库平台是否爆仓。判断是否入安得仓：targetSiteCode is not null
     * 2、调用判断爆仓的接口时，需要根据不同的bu_code计算商品的面积。
     * 3、每个order_info 下，有多个order_info_item，每个item的面积计算公式因单位、正品或者非正品的不同有差别；
     * item又需要根据其所属的bu_code进行group by。group by时，将其中的体积汇总，然后以bu_code为单位调用爆仓接口。
     * 4、若接口返回爆仓，update 该订单,order_status,exception_type,exception_dec
     * 5、将返回的标识validFlag存入订单中：Y:有效 N：无效
     * 无效时，则不需要增减到车、在途等面积
     */
    @Override
    @ZeebeFlow("PARENT_OVERFLOW_VERIFY")
    public CustomerOrderInfoExt isOutArea(CustomerOrderInfoExt customerOrderInfoExt) {
        CommonConstant.checkOrderInfo(customerOrderInfoExt);

        //状态调整出/入库、状态调整单、数量调整出/入，不校验爆仓
        String orderType = customerOrderInfoExt.getOrderType();

        //2021-1-5 19:29:15 阳阳：模糊订单的纯运输不需要校验爆仓
        if (CommonEnum.YES.getValue().equals(customerOrderInfoExt.getPlanOrderFlag()) && OrderType.YS.getKey().equals(orderType)) {
            FlowListenerParam.success("模糊订单orderType=纯运输，不校验是否爆仓");
            return customerOrderInfoExt;
        }
        //来源系统OFC的调拨出库AO的项目分类为外围云仓调拨【PCWA】的订单，跳过爆仓校验节点
        if(SourceSystem.OFC.getKey().equals(customerOrderInfoExt.getSourceSystem()) && OrderType.isAOOrder(orderType) && ProjectClassifyEnum.isPCWA(customerOrderInfoExt.getProjectClassify())){
            FlowListenerParam.success("外围云仓调拨订单不校验爆仓");
            return customerOrderInfoExt;
        }

        //针对OFC来源的订单审核【爆仓校验】，判断【site_code】与【target_site_code】一致，则跳过爆仓校验
        if (SourceSystem.isOFC(customerOrderInfoExt.getSourceSystem()) && StringUtils.equals(customerOrderInfoExt.getSiteCode(), customerOrderInfoExt.getTargetSiteCode())) {
            FlowListenerParam.success("同物流中心货权转移，不校验爆仓");
            return customerOrderInfoExt;
        }

        List<CustomerOrderItem> orderInfoItems = customerOrderInfoExt.getCustomerOrderItems();
        if (CollectionUtils.isEmpty(orderInfoItems)){
            throw BusinessException.fail("无法查询子单明细信息!");
        }


        if (OrderType.TFO.getKey().equals(orderType) || OrderType.ADO.getKey().equals(orderType)
            || OrderType.TFI.getKey().equals(orderType) || OrderType.ADI.getKey().equals(orderType)
            || OrderType.TF.getKey().equals(orderType) || OrderType.isMFOrder(orderType)){
            FlowListenerParam.success("orderType="+OrderType.getName(orderType)+"，不校验是否爆仓");
            return customerOrderInfoExt;
        }

        String targetSiteCode = customerOrderInfoExt.getTargetSiteCode();
        String targetCustomerCode = customerOrderInfoExt.getTargetCustomerCode();
        if (StringUtils.isEmpty(targetSiteCode) || StringUtils.isEmpty(targetCustomerCode) ){
            FlowListenerParam.success("收货平台或目标客户为空，不需要校验");
            return customerOrderInfoExt;
        }

        if ((!InOutType.OUT.getName().equals(customerOrderInfoExt.getInOutType()))) {
            FlowListenerParam.success("非入库到安得仓，无需检验是否爆仓!");
            return customerOrderInfoExt;
        }
        //join_type=SHARE的，不校验爆仓，直接返回节点执行成功
        if (Lists.newArrayList(JoinType.SHARE.getKey()).contains(customerOrderInfoExt.getJoinType())) {
            FlowListenerParam.success("调剂单，无需检验是否爆仓!");
            return customerOrderInfoExt;
        }

        // 无需校验爆仓
        if (isOutAreaService.noNeedToCheck(customerOrderInfoExt.getOrderDistinctionFlag(), customerOrderInfoExt.getCustomerCode())) {
            FlowListenerParam.success("无需检验是否爆仓!");
            return customerOrderInfoExt;
        }

        // 快仓退货出库订单不校验爆仓
        if (BusinessHelper.isMssROOrder(customerOrderInfoExt)) {
            FlowListenerParam.success("快仓退货订单，不校验爆仓");
            return customerOrderInfoExt;
        }

        // buCode 为空的不处理，过滤掉
        orderInfoItems = orderInfoItems.stream().filter(item -> item.getBuCode() != null).collect(Collectors.toList());

        Map<String, List<CustomerOrderItem>> buCodesGroup = orderInfoItems.stream().collect(Collectors.groupingBy(CustomerOrderItem::getBuCode));


        for(String buCode : buCodesGroup.keySet()) {
            List<CustomerOrderItem> items = buCodesGroup.get(buCode);

            BigDecimal totolArea = BigDecimal.ZERO;
            for (CustomerOrderItem item : items) {
                BigDecimal bigDecimal = countAreaHelper.countArea(item.getItemCode(), item.getPlanQty(), item.getItemStatus());
                totolArea = totolArea.add(bigDecimal);
            }

            if (totolArea.equals(BigDecimal.ZERO)) {
                continue;
            }

            OutAreaRequest bean = new OutAreaRequest();
            bean.setEntityId(buCode);
            bean.setSiteCode(targetSiteCode);
            bean.setCustCode(targetCustomerCode);
            bean.setCustOrderNo(customerOrderInfoExt.getCustomerOrderNo());
            bean.setOrderArea(totolArea);
            bean.setLogisticNo(customerOrderInfoExt.getOrderNo());
            bean.setWhCode(customerOrderInfoExt.getTargetWhCode());
            String jsonString = null;
            try {
                jsonString = orderItemAreaFeign.cdOverFlowCheckAreaLMDM(bean);
            } catch (Exception e) {
                logger.info("cdOverFlowCheckArea  order {} , error {}", customerOrderInfoExt.getOrderNo(), e.getMessage());
                throw BusinessException.fail("系统繁忙,请稍后重新点击审核");
            }
            logger.info("爆仓校验 ==cdOverFlowCheckArea=={}", jsonString);
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            if (!"0".equals(jsonObject.getString("code"))) {
                throw BusinessException.fail("调用鹊桥接口报错:" + jsonObject.getString("data"));
            }

            if (Objects.isNull(jsonObject.getString("data"))) {
                throw BusinessException.fail("调用鹊桥，数据不存在!");
            }
            JsonResponse jsonResponse = JSONObject.parseObject(jsonObject.getString("data")).toJavaObject(JsonResponse.class);
            OutAreaResponse outAreaResponse = JSONObject.parseObject(JSON.toJSONString(jsonResponse.getData())).toJavaObject(OutAreaResponse.class);
            String validFlag = outAreaResponse.getValidFlag();
            String outFlag = outAreaResponse.getOutFlag();

            if (!"Y".equals(validFlag)) {
                throw BusinessException.fail("查询失败或已经爆仓，参数：" + bean);
            }
            //Y:爆仓 N:未爆仓
            if ("Y".equals(outFlag)) {
                throw BusinessException.fail("已经爆仓，参数：" + bean);
            }
        }

        FlowListenerParam.success(StringUtils.join("爆仓校验通过, items:", orderInfoItems.size(), "; buCodes:", buCodesGroup.size()));
        return customerOrderInfoExt;
    }



    /**
     * 出库单：
     * 1、若是入安得仓的，则检验客户入库平台是否爆仓。判断是否入安得仓：targetSiteCode is not null
     * 2、调用判断爆仓的接口时，需要根据不同的bu_code计算商品的面积。
     * 3、每个order_info 下，有多个order_info_item，每个item的面积计算公式因单位、正品或者非正品的不同有差别；
     * item又需要根据其所属的bu_code进行group by。group by时，将其中的体积汇总，然后以bu_code为单位调用爆仓接口。
     * 4、若接口返回爆仓，update 该订单,order_status,exception_type,exception_dec
     * 5、将返回的标识validFlag存入订单中：Y:有效 N：无效
     * 无效时，则不需要增减到车、在途等面积
     */
    @Override
    @ZeebeFlow("OVERFLOW_VERIFY")
    public OrderInfoExt isOutArea(OrderInfoExt orderInfo) {

        CommonConstant.checkOrderInfo(orderInfo);

        //状态调整出/入库、状态调整单、数量调整出/入，不校验爆仓
        String orderType = orderInfo.getOrderType();

        //2021-1-5 19:29:15 阳阳：模糊订单的纯运输不需要校验爆仓
        if (CommonEnum.YES.getValue().equals(orderInfo.getPlanOrderFlag()) && OrderType.YS.getKey().equals(orderType)) {
            FlowListenerParam.success("模糊订单orderType=纯运输，不校验是否爆仓");
            return orderInfo;
        }

        List<OrderInfoItem> orderInfoItems = orderInfo.getOrderInfoItems();
        if (CollectionUtils.isEmpty(orderInfoItems)){
            throw BusinessException.fail("无法查询子单明细信息!");
        }

        //来源系统OFC的调拨出库AO的项目分类为外围云仓调拨【PCWA】的订单，跳过爆仓校验节点
        if(SourceSystem.OFC.getKey().equals(orderInfo.getSourceSystem()) && OrderType.isAOOrder(orderType) && ProjectClassifyEnum.isPCWA(orderInfo.getProjectClassify())){
            FlowListenerParam.success("外围云仓调拨订单不校验爆仓");
            return orderInfo;
        }

        //针对OFC来源的订单审核【爆仓校验】，判断【site_code】与【target_site_code】一致，则跳过爆仓校验
        if (SourceSystem.isOFC(orderInfo.getSourceSystem()) && StringUtils.equals(orderInfo.getSiteCode(), orderInfo.getTargetSiteCode())) {
            FlowListenerParam.success("同物流中心货权转移，不校验爆仓");
            return orderInfo;
        }

        if (OrderType.TFO.getKey().equals(orderType) || OrderType.ADO.getKey().equals(orderType)
            || OrderType.TFI.getKey().equals(orderType) || OrderType.ADI.getKey().equals(orderType)
            || OrderType.TF.getKey().equals(orderType) || OrderType.isMFOrder(orderType)){
            FlowListenerParam.success("orderType="+OrderType.getName(orderType)+"，不校验是否爆仓");
            return orderInfo;
        }

        String targetSiteCode = orderInfo.getTargetSiteCode();
        String targetCustomerCode = orderInfo.getTargetCustomerCode();
        if (StringUtils.isEmpty(targetSiteCode) || StringUtils.isEmpty(targetCustomerCode) ){
            FlowListenerParam.success("收货平台或目标客户为空，不需要校验");
            return orderInfo;
        }

        if ((!InOutType.OUT.getName().equals(orderInfo.getInOutType()))) {
            FlowListenerParam.success("非入库到安得仓，无需检验是否爆仓!");
            return orderInfo;
        }
        //join_type=SHARE的，不校验爆仓，直接返回节点执行成功
        if (Lists.newArrayList(JoinType.SHARE.getKey()).contains(orderInfo.getJoinType())) {
            FlowListenerParam.success("调剂单，无需检验是否爆仓!");
            return orderInfo;
        }
        String customerOrderNo = orderInfo.getCustomerOrderNo();
        String orderNo = orderInfo.getOrderNo();

        // 无需校验爆仓
        if (isOutAreaService.noNeedToCheck(orderInfo.getOrderDistinctionFlag(), orderInfo.getCustomerCode())) {
            FlowListenerParam.success("无需检验是否爆仓!");
            return orderInfo;
        }

        // 快仓退货出库订单不校验爆仓
        if (BusinessHelper.isMssROOrder(orderInfo)) {
            FlowListenerParam.success("快仓退货订单，不校验爆仓");
            return orderInfo;
        }

        // buCode 为空的不处理，过滤掉
        orderInfoItems = orderInfoItems.stream().filter(item -> item.getBuCode() != null).collect(Collectors.toList());

        Map<String, List<OrderInfoItem>> buCodesGroup = orderInfoItems.stream().collect(Collectors.groupingBy(OrderInfoItem::getBuCode));

        for(String buCode : buCodesGroup.keySet()){
            List<OrderInfoItem> items = buCodesGroup.get(buCode);

            BigDecimal totolArea = BigDecimal.ZERO;
            for (OrderInfoItem item:items) {
                BigDecimal bigDecimal = countAreaHelper.countOneArea(item);
                totolArea = totolArea.add(bigDecimal);
            }

            if (totolArea.equals(BigDecimal.ZERO)){
                continue;
            }

            OutAreaRequest bean = new OutAreaRequest();
            bean.setEntityId(buCode);
            bean.setSiteCode(targetSiteCode);
            bean.setCustCode(targetCustomerCode);
            bean.setCustOrderNo(customerOrderNo);
            bean.setOrderArea(totolArea);
            bean.setLogisticNo(orderInfo.getOrderNo());
            bean.setWhCode(orderInfo.getTargetWhCode());
            String jsonString = null;
            try {
                jsonString = orderItemAreaFeign.cdOverFlowCheckAreaLMDM(bean);
            } catch (Exception e) {
                logger.info("cdOverFlowCheckArea  order {} , error {}", orderInfo.getOrderNo(), e.getMessage());
               throw BusinessException.fail("系统繁忙,请稍后重新点击审核");
            }
            logger.info("爆仓校验 ==cdOverFlowCheckArea=={}",jsonString);
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            if ( !"0".equals(jsonObject.getString("code"))) {
               throw BusinessException.fail("调用鹊桥接口报错:"+ jsonObject.getString("data"));
            }

            if ( Objects.isNull(jsonObject.getString("data"))) {
               throw BusinessException.fail("调用鹊桥，数据不存在!");
            }
            JsonResponse jsonResponse = JSONObject.parseObject(jsonObject.getString("data")).toJavaObject(JsonResponse.class);
            OutAreaResponse outAreaResponse = JSONObject.parseObject(JSON.toJSONString(jsonResponse.getData())).toJavaObject(OutAreaResponse.class);
            String isSuccess = outAreaResponse.getIsSuccess();
            String validFlag = outAreaResponse.getValidFlag();
            String outFlag = outAreaResponse.getOutFlag();

//            if (StringUtils.isNotBlank(isSuccess) && "F".equals(isSuccess)) {
//               throw BusinessException.fail("查询失败或已经爆仓，参数："+bean);
//            }
            if (!"Y".equals(validFlag)) {
                throw BusinessException.fail("查询失败或已经爆仓，参数："+bean);
            }
            //Y:爆仓 N:未爆仓
            if ("Y".equals(outFlag)){
               throw BusinessException.fail("已经爆仓，参数："+bean);
            }

            if (StringUtils.isNotBlank(validFlag)) {
                JsonResponse<List<PledgeInfo>> byOrderNo = pledgeInfoExtFeign.getPledgeByOrderNo(orderNo, PledgeFailEnum.SUCCESS.getKey());

                if ( !"0".equals(byOrderNo.getCode())) {
                   throw BusinessException.fail("调用接口报错:"+ byOrderNo.getMsg());
                }

                if (CollectionUtils.isEmpty(byOrderNo.data)) {
                    List<PledgeInfo> pledgeInfoList = new ArrayList<>();
                    PledgeInfo pledgeInfo = new PledgeInfo();
                    pledgeInfo.setOrderNo(orderNo);
                    pledgeInfo.setOverflowFlag(validFlag);
                    pledgeInfo.setOrderAmount(BigDecimal.ZERO);
                    pledgeInfoList.add(pledgeInfo);
                    JsonResponse<Integer> integerJsonResponse = pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoList);
                    if (!BaseCodeEnum.SUCCESS.getCode().equals(integerJsonResponse.getCode()) || !integerJsonResponse.getData().equals(1)){
                       throw BusinessException.fail("保存质押数据接口异常!");
                    }
                }else {
                    for (int i = 0; i < byOrderNo.getData().size(); i++) {
                        PledgeInfo pledgeInfo = byOrderNo.getData().get(i);
                        pledgeInfo.setOverflowFlag(validFlag);
                        JsonResponse<Integer> update = pledgeInfoFeign.update(pledgeInfo.getId(), pledgeInfo);
                        if (!update.getCode().equals(BaseCodeEnum.SUCCESS.getCode())) {
                           throw BusinessException.fail("修改爆仓是否有效的接口异常!");
                        }
                    }
                }
            }
        }

        FlowListenerParam.success(StringUtils.join("爆仓校验通过, items:", orderInfoItems.size(), "; buCodes:", buCodesGroup.size()));
        return orderInfo;
    }
}
