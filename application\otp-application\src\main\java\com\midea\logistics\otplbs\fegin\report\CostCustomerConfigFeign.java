package com.midea.logistics.otplbs.fegin.report;
import com.midea.logistics.otp.cost.domain.bean.CostCustomerConfig;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: CostCustomerConfigFeign
* Author: 陈永培
* Date: 2023-2-6 8:58:32
* Description:纯计费客户配置表 feign
*/
@FeignClient(value = "logistics-otp-report-service")
public interface CostCustomerConfigFeign{

    /**
    * 创建或修改
    * @param costCustomerConfigs
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse<String> batchCreateOrUpdate(@RequestBody List<CostCustomerConfig> costCustomerConfigs);

    /**
    * 查询
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_DETAIL, method = RequestMethod.GET)
    JsonResponse<CostCustomerConfig> queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * 更新
    * @param id
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CostCustomerConfig costCustomerConfig);

    /**
    * 删除
    * @param deleteList
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@RequestBody List<CostCustomerConfig> deleteList);

    /**
    * 分页查询
    * @param costCustomerConfig
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_SEARCH, method = RequestMethod.GET)
    JsonResponse<PageResponse<CostCustomerConfig>> search(@SpringQueryMap CostCustomerConfig costCustomerConfig);

    /**
    * 查询单个
    * @param costCustomerConfig
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_SELECT_ONE, method = RequestMethod.GET)
    public JsonResponse<CostCustomerConfig> selectOne(@SpringQueryMap CostCustomerConfig costCustomerConfig);

    /**
    * 查询集合
    * @param costCustomerConfig
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_LIST, method = RequestMethod.GET)
    public JsonResponse<List<CostCustomerConfig>> list(@SpringQueryMap CostCustomerConfig costCustomerConfig);


    /**
    * 批量新增
    *
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_INSERT_BATCH, method = RequestMethod.POST)
    public JsonResponse insertBatch(@RequestBody List<CostCustomerConfig> list);


    /**
    * 批量修改
    *
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_UPDATE_BATCH, method = RequestMethod.POST)
    public JsonResponse updateBatch(@RequestBody List<CostCustomerConfig> list);


    /**
    * 批量删除
    *
    * @return
    */
    @RequestMapping(value = OrderRouters.COST_CUSTOMER_CONFIG_DELETE_BATCH, method = RequestMethod.POST)
    public JsonResponse deleteBatch(@RequestBody List<CostCustomerConfig> list);

}
