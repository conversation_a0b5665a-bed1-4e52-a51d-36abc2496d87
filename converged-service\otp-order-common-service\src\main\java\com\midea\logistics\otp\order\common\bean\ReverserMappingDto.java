package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

/**
 * Author:  ex_dengzj4
 * Date:     2021-6-27 14:45
 * Description: 映射类
 */
@Data
public class ReverserMappingDto {


    /**
     * B2B，B2C
     */
    private String businessMode;


    /**
     * 上游系统
     */
    private String sourceSystem;


    //-----
    /**
     * 上游客户编码
     */
    private String upperCustomerCode;


    /**
     * 上游订单类型
     */
    private String upperOrderType;


    /**
     * 上游仓库编码
     */
    private String upperWhCode;


    /**
     * 客户编码
     */
    private String customerCode;


    /**
     * 订单类型
     */
    private String orderType;


    /**
     * 仓库编码
     */
    private String whCode;


    //------明细
    /**
     * 上游客户商品编码
     */
    private String upperItemCode;


    /**
     * 上游商品状态
     */
    private String upperItemStatus;


    /**
     * 上游目标商品状态
     */
    private String upperItemStatusTo;


    /**
     * 客户商品id
     */
    private String customerItemCode;


    /**
     * 商品编码
     */
    private String itemCode;


    /**
     * 商品状态
     */
    private String itemStatus;


    /**
     * 目标商品状态
     */
    private String itemStatusTo;
}
