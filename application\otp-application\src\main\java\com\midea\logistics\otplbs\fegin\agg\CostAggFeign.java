package com.midea.logistics.otplbs.fegin.agg;

import com.midea.logistics.otp.report.service.domain.bean.common.CostOrderInfo;
import com.midea.logistics.otp.order.domain.bean.ExpressServiceOrderInfo;
import com.midea.logistics.otp.order.domain.bean.ExpressServiceOrderUpdateDto;
import com.midea.logistics.otp.order.domain.request.ExpressServiceOrderRequest;
import com.midea.logistics.otp.report.service.domain.request.CostOrderInfoRequest;
import com.midea.logistics.otp.report.service.domain.response.CostOrderInfoResponse;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Title: CostAggFeign
 * @Description: CostAggFeign
 * @author: dongxy31
 * @date: 2024年8月28日
 * @version V1.0
 *
 */
@FeignClient(value = "logistics-cost-agg-service", contextId = "CostAggFeign")
public interface CostAggFeign {

    /**
     * 分页查询
     * @param costOrderInfo
     * @return
     */
    @PostMapping(value = "/costOrderInfo/costOrderInfos")
    JsonResponse<PageResponse<CostOrderInfoResponse>> search(@RequestBody CostOrderInfoRequest costOrderInfo);

    /**
     * 批次凭证删除
     * @param costOrderInfo
     * @return
     */
    @PostMapping(value = "/costOrderInfo/transVoucherDelete")
    JsonResponse transVoucherDelete(@RequestBody CostOrderInfo costOrderInfo);

    /**
     * 批次凭证上传
     * @param updateUserCode
     * @param
     * @return
     */
    @PostMapping(value = "/costOrderInfo/transVoucherUpload")
    JsonResponse transVoucherUpload(@RequestParam("updateUserCode") String updateUserCode, @RequestParam("transId") String transId,@RequestParam("transVoucherUrl") String transVoucherUrl);

    @PostMapping(value = "/expressServiceOrderVerify")
    JsonResponse expressServiceOrderVerify(@RequestBody List<String> orderNos);

    @PostMapping(value = "/expressServiceOrderUpdate")
    JsonResponse expressServiceOrderUpdate(@RequestBody ExpressServiceOrderUpdateDto updateDto);

    @PostMapping(value = "/expressServiceOrderReVerify")
    JsonResponse expressServiceOrderReVerify(@RequestBody ExpressServiceOrderUpdateDto updateDto);

    @PostMapping(value = "/expressServiceOrderInfosCount")
    JsonResponse expressServiceOrderInfosCount(@RequestBody ExpressServiceOrderRequest request);

    @PostMapping(value = "/expressServiceOrderInfos")
    JsonResponse expressServiceOrderInfos(@RequestBody ExpressServiceOrderRequest request);


    @PostMapping("/cancelExpressServiceOrderInfos")
    JsonResponse orderCancel(List<ExpressServiceOrderInfo> expressServiceOrderInfoList);

    @PostMapping("/cancelExpressServiceOrderInfosByTransId")
    JsonResponse orderCancelByTransId(ExpressServiceOrderInfo expressServiceOrderInfo);
}
