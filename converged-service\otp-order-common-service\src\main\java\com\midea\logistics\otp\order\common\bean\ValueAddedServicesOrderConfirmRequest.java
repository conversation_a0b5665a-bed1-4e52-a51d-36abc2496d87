package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.util.Date;

/**
 * ClassName: ValueAddedServicesOrderConfirmRequest
 * Description:增值服务回传
 * <AUTHOR>
 * @date 2023/7/27 15:09
 */
@Data
public class ValueAddedServicesOrderConfirmRequest {
    private String orderCode;
    private String outBizCode;
    private String cpCode;
    private String storeCode;
    private String orderType;
    private Date orderConfirmTime;
    private Integer actQty;
    private String serviceCode;
    private String serviceName;
    private String fee;
    private String from_code;
    private String msg_type;
    private String msg_id;
    /**
     * 拓展字段
     */
    private String extendFields;
    /**
     * 备注
     */
    private String remark;
}
