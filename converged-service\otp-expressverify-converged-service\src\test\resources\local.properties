spring.logistics-log.path=./logs
sms.businessDepart=default
lc.metadata.env=sit
spring.rocketmq.producerGroup=junit
spring.rocketmq.retryAnotherBrokerWhenNotStoreOK=true
spring.rocketmq.consumerGroup=junit
spring.rocketmq.messageListener=CONCURRENTLY
spring.rocketmq.messageModel=CLUSTERING
spring.rocketmq.pullBatchSize=5
spring.rocketmq.pullInterval=500
rocketmq.client.logLevel=INFO
rocketmq.client.logRoot=/logs/rocketmqlogs
rocketmq.client.logFileMaxIndex=10
rocketmq.client.logFileName=rocketmq_client.log
rocketmq.client.logFileMaxSize=1073741824
rocketmq.client.logAsyncQueueSize=1024

