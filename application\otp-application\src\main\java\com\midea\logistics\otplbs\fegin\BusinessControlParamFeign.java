package com.midea.logistics.otplbs.fegin;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.BusinessControlParam;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: BusinessControlParamFeign
* Author: 陈永培
* Date: 2022-1-6 16:20:10
* Description:业务控制参数 feign
*/
@FeignClient(value = "logistics-otp-rule-service")
public interface BusinessControlParamFeign{

    /**
    * 创建
    * @param  businessControlParam
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_CREATE, method = RequestMethod.POST)
    JsonResponse<String> create(@RequestBody BusinessControlParam businessControlParam);

    /**
    * 查询
    * @param id
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DETAIL, method = RequestMethod.GET)
    JsonResponse<BusinessControlParam> queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * 更新
    * @param id
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody BusinessControlParam businessControlParam);

    /**
    * 删除
    * @param id
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    * 分页查询
    * @param
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_SEARCH, method = RequestMethod.GET)
    JsonResponse<PageResponse<BusinessControlParam>> search(@SpringQueryMap BusinessControlParam businessControlParam);

    /**
    * 查询单个
    * @param businessControlParam
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_SELECT_ONE, method = RequestMethod.GET)
    public JsonResponse<BusinessControlParam> selectOne(@SpringQueryMap BusinessControlParam businessControlParam);

    /**
    * 查询集合
    * @param businessControlParam
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_LIST, method = RequestMethod.GET)
    public JsonResponse<List<BusinessControlParam>> list(@SpringQueryMap BusinessControlParam businessControlParam);


    /**
    * 批量新增
    *
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_INSERT_BATCH, method = RequestMethod.POST)
    public JsonResponse insertBatch(@RequestBody List<BusinessControlParam> list);


    /**
    * 批量修改
    *
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_UPDATE_BATCH, method = RequestMethod.POST)
    public JsonResponse updateBatch(@RequestBody List<BusinessControlParam> list);


    /**
    * 批量删除
    *
    * @return
    */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DELETE_BATCH, method = RequestMethod.POST)
    public JsonResponse deleteBatch(@RequestBody List<BusinessControlParam> list);

    /**
     * 更新防止重复
     * @param businessControlParam
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_UPDATE_NO_SAME, method = RequestMethod.PUT)
    JsonResponse updateNoSame(@PathVariable("id") Long id, @RequestBody BusinessControlParam businessControlParam);

    /**
     * 新增防止重复
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_CREATE_NO_SAME, method = RequestMethod.POST)
    JsonResponse createNoSame(@RequestBody BusinessControlParam businessControlParam);

    /** 删除主表及明细表
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.BUSINESS_CONTROL_PARAM_DELETE_WITH_DETAILS, method = RequestMethod.DELETE)
    JsonResponse deleteWithDetailsByParamCode(@PathVariable("id") Long id, @RequestBody BusinessControlParam businessControlParam);
}
