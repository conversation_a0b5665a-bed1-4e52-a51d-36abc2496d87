package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.msgcenter.domain.bean.Message;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 到车登记
 */

@FeignClient(value = "midea-msgcenter-service",url = BaseRouter.LOGISTICS_OTP_ORDERTASK_CONVERGED_SERVICE)
public interface MasggerFegin {

    @PostMapping (value = "/sendMessage")
    JsonResponse sendMessage(@RequestBody Message msg) ;
}