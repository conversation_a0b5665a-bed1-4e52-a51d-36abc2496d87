spring:
  # public setting
  application:
    name: logistics-otp-task-service
  # active setting
  profiles:
    active: uat
  main:
    allow-bean-definition-overriding: true
server:
  port: 10135
  tomcat:
    max-threads: 500
    max-connections: 10000
    accept-count: 500


breaker:
  enable: false
  auto:
    webmvc: false
    feign: false
  sentinel:
    enable: false

mgp:
  carrier:
    discovery:
      system: c-loms-annto
      version: 1.0
      auto-host-ip: true
      enable: true

logging:
  level:
    # 新增配置, 过滤掉该服务治理SDK版本过多日志输出问题
    com.midea.mgp: warn

#dev
---
spring:
  profiles: dev
  logistics-log:
    path: ./logs
  zipkin:
    #    base-url: http://127.0.0.1:8002
    #    base-url: http://************:8002
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-dev.annto.com:15821/eureka/
  server:
    enable-self-preservation: false
mgp:
  carrier:
    discovery:
      env: dev

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 629194096a61423eb86b35d00ad3e884

#sit
---
spring:
  profiles: sit
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:9501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-sit.annto.com:15821/eureka/
  server:
    enable-self-preservation: false
mgp:
  carrier:
    discovery:
      env: sit
      registry: https://anapi-sit.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 7a59a947c19c425d930004151b200c90

#uat
---
spring:
  profiles: uat
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:19501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-uat.annto.com:15821/eureka/
mgp:
  carrier:
    discovery:
      env: uat
      registry: https://anapi-uat.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi
apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 6702ef75e3c54e12968168ef2b3a830e

#ver
---
spring:
  profiles: ver
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:19501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:yniDIct6W2CTdh6ktX2FCWZUAxQgDfGVCz6voiMuZuw=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi-ver.annto.com:15821/eureka/
mgp:
  carrier:
    discovery:
      env: ver
      registry: https://anapi-ver.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 6a0e5efb97574f959aad72b2556b6422

#prod
---
spring:
  profiles: prod
  logistics-log:
    path: ./logs
  zipkin:
    base-url: http://************:9501
    enabled: true
  sleuth:
    sampler:
      percentage: 1

lc:
  eureka:
    user: eureka-user
    password: cipher:l+3CmmYr4eejdJvTmip7S2dcGeEQej/7UUYR8gBQfWM=
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    prefer-ip-address: true
    instanceId: ${eureka.instance.hostname}:${spring.application.name}:${server.port}

  client:
    serviceUrl:
      defaultZone: http://${lc.eureka.user}:${lc.eureka.password}@anapi.annto.com:15821/eureka/

apollo:
  bootstrap:
    namespaces: an-application
  accesskey:
    # 环境秘钥. 每个服务 和 每个环境 都不一样,来自apollo-paas云-管理秘钥
    secret: 265e2ace572441da81e9375e1d029a86

mgp:
  carrier:
    discovery:
      env: prd
      registry: https://anapi.annto.com/T-GWAN/t-gwan-openapi/mgp-api/openapi
