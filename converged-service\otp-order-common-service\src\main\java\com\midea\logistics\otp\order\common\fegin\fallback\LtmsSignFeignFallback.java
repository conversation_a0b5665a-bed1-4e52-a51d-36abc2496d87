package com.midea.logistics.otp.order.common.fegin.fallback;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.otp.order.common.fegin.bop.LtmsSignFeign;
import com.midea.logistics.otp.order.converged.domain.request.OnwayAreaRequest;
import com.midea.logistics.otp.order.domain.dto.OrderForSignDto;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import feign.hystrix.FallbackFactory;


/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: LtmsSignFeignFallback
 * @author: gaozg1
 * @date: 2019/10/15 11:29
 * @description: ${DESCRIPTION}
 */
public class LtmsSignFeignFallback implements FallbackFactory<LtmsSignFeign> {

    @Override
    public LtmsSignFeign create(Throwable throwable) {
        return new LtmsSignFeign() {
            @Override
            public String autoSignHandler(OrderForSignDto bean) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return JSON.toJSONString(jsonResponse);
            }
        };
    }
}
