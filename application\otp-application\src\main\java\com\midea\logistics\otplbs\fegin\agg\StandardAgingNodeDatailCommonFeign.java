package com.midea.logistics.otplbs.fegin.agg;

import com.midea.logistics.otp.bean.agg.StandardAgingNodeDatail;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "StandardAgingNodeDatailCommonFeign")
public interface StandardAgingNodeDatailCommonFeign {

    @GetMapping("/standard/aging/node/datail/list")
    JsonResponse<List<StandardAgingNodeDatail>> standardAgingNodeDatailList(@SpringQueryMap StandardAgingNodeDatail entity);

    @GetMapping("/standard/aging/node/datail/page")
    JsonResponse standardAgingNodeDatailPage(@SpringQueryMap StandardAgingNodeDatail entity);

    @GetMapping("/standard/aging/node/datail/count")
    JsonResponse standardAgingNodeDatailCount(@SpringQueryMap StandardAgingNodeDatail entity);

    @GetMapping("/standard/aging/node/datail/detail")
    JsonResponse<StandardAgingNodeDatail> standardAgingNodeDatailDetail(@RequestParam("id") Long id);

    @GetMapping("/standard/aging/node/datail/detail")
    JsonResponse<StandardAgingNodeDatail> standardAgingNodeDatailDetail(@SpringQueryMap StandardAgingNodeDatail entity);

    @PostMapping("/standard/aging/node/datail/add")
    JsonResponse<BaseDomain> standardAgingNodeDatailAdd(@RequestBody StandardAgingNodeDatail entity);

    @PutMapping("/standard/aging/node/datail/update")
    JsonResponse<Integer> standardAgingNodeDatailUpdate(@RequestBody StandardAgingNodeDatail entity);

    @DeleteMapping("/standard/aging/node/datail/remove")
    JsonResponse<Integer> standardAgingNodeDatailRemove(@RequestBody StandardAgingNodeDatail entity);

}

