package com.midea.logistics.imp.orderverify.rest;

import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.CargoRightTransferService;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: CargoRightTransferRest
 * @description:
 * @author: kongly1
 * @date: 2020-11-13 15:30
 */
@RestController
public class CargoRightTransferRest {

    @Autowired
    private CargoRightTransferService cargoRightTransferServiceImpl;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @PostMapping("/lmp/validate/cargo/right/transfer")
    public JsonResponse cargoRightTransfer(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {

        try {
            return JsonResponse.success(cargoRightTransferServiceImpl.cargoRightTransfer(customerOrderInfoExt));
        } catch (Exception e) {
            throw BusinessException.fail("货权转移失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/lmp/validate/cargo/right/transfer/byOrderNo")
    public JsonResponse cargoRightTransferByOrderNo(@RequestBody CustomerOrderInfoExt customerOrderInfoExt) {

        try {
            customerOrderInfoExt = lmpOrderFlowHelper.getCustomerOrderInfoExt(customerOrderInfoExt.getOrderNo());
            cargoRightTransferServiceImpl.cargoRightTransfer(customerOrderInfoExt);
            Map<String, Object> map = new HashMap<>();
            map.put("deliveryType", customerOrderInfoExt.getDeliveryType());
            return JsonResponse.success(map);
        } catch (Exception e) {
            throw BusinessException.fail("货权转移失败，失败原因:" + e.getMessage());
        }
    }


}
