package com.midea.logistics.otp.order.common.bean;

import com.midea.logistics.otp.order.converged.domain.response.SeparateWarehouseSearchWarehouseResponse;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class TFSeparateWarehouseSearchItem {
    /**
     待分配数
     customer_order_info.plan_qty-customer_order_info.split_qty-customer_order_info.cancle_qty
     （字段后端提供）

     待分配数=计划数量-已分配数量-取消数量
     2	分配数
     人工填写
     3	库存数
     以商品编码+商品状态+服务平台编码调LWMS库存查询接口，根据返回值qty进行填充
     4	剩余库存数
     默认为空，当用户填写完分配数后，进行计算：剩余库存数=库存数-分配数
     5	客户商品编码	customer_order_item.customer_item_code
     6	商品名称	customer_order_item.item_name
     7	商品状态	customer_order_item.item_status
     8	仓库编码
     以LWMS返回whCode填充，无则不填充
     9	仓库名称
     以LWMS返回whName填充，无则不填充
     10	库位
     以LWMS返回lotCode填充，子单+任务的商品明细表需增加，无则不填充
     11	入库日期
     以LWMS返回lotCode填充，子单+任务的商品明细表需增加，无则不填充
     12	批次
     以LWMS返回lotNum填充，记录在batch_code字段，无则不填充
     13	traceId
     以LWMS返回tracleId填充，子单+任务的商品明细表需增加，无则不填充
     */
    private String customerOrderNo;
    private String companyCode;
    private String siteCode;
    private String customerCode;

    private String orderNo;

    /**
     * 产品名称
     */
    private String itemName;
    /**
     * 产品编码
     */
    private String itemCode;

    /**
     * 商品行号
     */
    private Integer itemLineNo;
    /**
     * 客户商品编码
     */
    private String customerItemCode;
    private String itemSuiteCode;
    /**
     * 套件数量关系
     */
    private Integer itemSuiteQty;
    /**
     * 产品状态
     */
    private String itemStatus;
    private String itemStatusName;
    private BigDecimal planQty;
    /**
     * 已分配
     */
    private BigDecimal allocatedNum;
    /**
     * 未分配
     */
    private BigDecimal unallocatedNum;

    /**
     * 目标商品状态
     */
    private String itemStatusTo;
    private String itemStatusToName;

    /**
     * 仓库
     */
    List<SeparateWarehouseSearchWarehouseResponse> warehouseList;

}
