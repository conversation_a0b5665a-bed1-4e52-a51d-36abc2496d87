package com.midea.logistics.otp.ttx.service;
import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrderHead;

import java.util.List;


/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: IInboundOrderHeadService
* Author: yaowl
* Date: 2019-9-17 14:15:59
* Description:IInboundOrderHeadService服务接口
*/
public interface IInboundOrderHeadService {

    InboundOrderHead save(InboundOrderHead inboundOrderHead);

    Integer insertBatch(List<InboundOrderHead> list);

    InboundOrderHead update(InboundOrderHead inboundOrderHead);

    Integer batchUpdate(List<InboundOrderHead> list);

    InboundOrderHead selectById(InboundOrderHead inboundOrderHead);

    List<InboundOrderHead> select(InboundOrderHead inboundOrderHead);


}