package com.midea.logistics.otp.task.service.impl;

import com.midea.logistics.otp.task.domain.bean.TaskOperLog;
import com.midea.logistics.otp.task.mapper.common.TaskOperLogMapper;
import com.midea.logistics.otp.task.service.ITaskOperLogService;
import com.mideaframework.core.web.PageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskOperLogServiceImpl
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:TaskOperLogServiceImpl类
 */
@Service
public class TaskOperLogServiceImpl implements ITaskOperLogService {


    private static final Logger logger = LoggerFactory.getLogger(TaskOperLogServiceImpl.class);

    @Autowired
    private TaskOperLogMapper taskOperLogMapper;


    @Override
    @Transactional(readOnly = false)
    public Integer saveTaskOperLog(TaskOperLog taskOperLog) {

        if (null == taskOperLog) {
            logger.warn("save taskOperLog, but taskOperLog is null...");
            return 0;
        }

        return taskOperLogMapper.save(taskOperLog);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteTaskOperLog(TaskOperLog taskOperLog) {

        if (null == taskOperLog || null == taskOperLog.getId()) {
            logger.warn("delete taskOperLog, but taskOperLog is null  or taskOperLog id is null...");
            return 0;
        }

        return taskOperLogMapper.deleteById(taskOperLog);
    }

    @Override
    @Transactional(readOnly = false)
    public Integer updateTaskOperLog(TaskOperLog taskOperLog) {

        if (null == taskOperLog || null == taskOperLog.getId()) {
            logger.warn("update taskOperLog, but taskOperLog is null  or taskOperLog id is null...");
            return 0;
        }

        return taskOperLogMapper.updateById(taskOperLog);
    }

    @Override
    public TaskOperLog selectOneTaskOperLog(TaskOperLog taskOperLog) {
        if (taskOperLog == null) {
            logger.warn("select taskOperLog one, but taskOperLog is null ...");
            return null;
        }
        taskOperLog = taskOperLogMapper.selectOne(taskOperLog);
        return taskOperLog;
    }

    @Override
    public PageResponse<TaskOperLog> selectTaskOperLogPage(TaskOperLog taskOperLog) {
        PageResponse<TaskOperLog> pagingData = new PageResponse<>();

        if (null == taskOperLog) {
            logger.warn("select taskOperLog page, but taskOperLog is null...");
            return pagingData;
        }

        Integer queryCount = taskOperLogMapper.selectByIndexCount(taskOperLog);
        if (null != queryCount && queryCount <= 0) {
            logger.info("select taskOperLog page , but count {} == 0 ...", queryCount);
            return pagingData;
        }

        List<TaskOperLog> taskOperLogs = selectTaskOperLogByIndex(taskOperLog);
        pagingData.init(taskOperLog.getPageNo(), taskOperLog.getPageSize(), queryCount, taskOperLogs);
        return pagingData;
    }

    @Override
    public List<TaskOperLog> selectTaskOperLogByIndex(TaskOperLog taskOperLog) {
        List<TaskOperLog> taskOperLogs = new ArrayList<>();
        if (taskOperLog == null) {
            logger.warn("select taskOperLog by index, but taskOperLog is null ...");
            return taskOperLogs;
        }

        taskOperLogs = taskOperLogMapper.selectByIndex(taskOperLog);

        return taskOperLogs;
    }

    @Override
    public int countTaskOperLogByIndex(TaskOperLog taskOperLog) {
        int count = 0;
        if (taskOperLog == null) {
            logger.warn("count taskOperLog by index, but taskOperLog is null ...");
            return count;
        }

        count = taskOperLogMapper.selectByIndexCount(taskOperLog);

        return count;
    }

    @Override
    public Integer insertBatch(List<TaskOperLog> taskOperLogs) {
        if (CollectionUtils.isEmpty(taskOperLogs)) {
            logger.warn("insertBatch taskOperLogs, but taskOperLogs is null ...");
            return 0;
        }
        return taskOperLogMapper.insertBatch(taskOperLogs);
    }

    @Override
    public Integer batchUpdate(List<TaskOperLog> taskOperLogs) {
        if (CollectionUtils.isEmpty(taskOperLogs)) {
            logger.warn("batchUpdate taskOperLogs, but taskOperLogs is null ...");
            return 0;
        }
        return taskOperLogMapper.batchUpdate(taskOperLogs);
    }

    @Override
    public Integer deleteBatch(List<TaskOperLog> taskOperLogs) {
        if (CollectionUtils.isEmpty(taskOperLogs)) {
            logger.warn("deleteBatch taskOperLogs, but taskOperLogs is null ...");
            return 0;
        }
        return taskOperLogMapper.deleteBatch(taskOperLogs);
    }

}