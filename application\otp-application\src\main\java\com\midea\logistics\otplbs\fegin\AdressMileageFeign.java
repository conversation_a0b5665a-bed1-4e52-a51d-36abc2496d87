package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.AdressMileage;
import com.midea.logistics.otp.rule.domain.request.AdressMileageRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: AdressMileageFeign
 * Author: luoh
 * Date: 2019-6-3 16:30:57
 * Description:里程 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface AdressMileageFeign {

    @RequestMapping(value = OptRuleRouters.ADRESS_MILEAGE_SAVE, method = RequestMethod.POST)
    JsonResponse create(@RequestBody AdressMileageRequest adressMileageRequest);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ADRESS_MILEAGE_DETAIL, method = RequestMethod.GET)
    JsonResponse<AdressMileage> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ADRESS_MILEAGE_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody AdressMileage adressMileage);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ADRESS_MILEAGE_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = OptRuleRouters.ADRESS_MILEAGE_LIST, method = RequestMethod.GET)
    JsonResponse<PageResponse<AdressMileage>> search(@SpringQueryMap AdressMileageRequest adressMileageRequest);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ADRESS_MILEAGE_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<AdressMileageRequest> adressMileageRequests);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ADRESS_MILEAGE_BATCH_DELETE_BY_BUSINESSKEY, method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
