package com.midea.logistics.otp.order.common.fegin.dc;

import com.midea.logistics.lcp.domain.dc.entity.AddressMapping;
import com.midea.logistics.lcp.domain.dc.entity.TransferCenterConfig;
import com.midea.logistics.lcp.domain.dc.entity.Warehouse;
import com.midea.logistics.otp.common.request.ContactsOms;
import com.midea.logistics.otp.order.common.bean.DcRouteSearchDto;
import com.midea.logistics.otp.order.common.bean.DcRouteSearchResponse;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Date:     2020-11-20 14:33
 * sit: url = "http://***********:8052"
 */
@FeignClient(value = "logistics-lcp-dc-atomic")
public interface DcAtomicFeign {

    @GetMapping(value = "/warehouse/list")
    JsonResponse<List<Warehouse>> listWarehouseByWhCode(
        @RequestParam("code") String code,
        @RequestHeader(name = "tenantCode") String tenantCode);

    /**
     * 中转站点查询
     * @param entity
     * @return
     */
    @GetMapping({"/transfer/center/config/detail"})
    JsonResponse<TransferCenterConfig> transferCenterConfigDetail(@SpringQueryMap TransferCenterConfig entity);

    @GetMapping("/address/mapping/list")
    JsonResponse<List<AddressMapping>> addressMappingList(@SpringQueryMap AddressMapping entity, @RequestHeader(name = "tenantCode") String tenantCode);

    /**
     * 查询整体的路由
     */
    @GetMapping("/route/config/list")
    JsonResponse<List<DcRouteSearchResponse>> getRoute(@SpringQueryMap DcRouteSearchDto dcRouteSearchDto,@RequestHeader(name = "tenantCode") String tenantCode);

    //GET /contacts/oms/page 。可以用feign调用，服务名logistics-lcp-dc-atomic
    @GetMapping("/contacts/oms/page")
    JsonResponse<PageResponse<ContactsOms>> contacts(@RequestParam("contactCode") String contactCode,@RequestHeader(name = "tenantCode") String tenantCode);
}
