package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.OrderAgingStation;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: OrderAgingStationFeign
* Author: ex_chenty1
* Date: 2020-12-8 11:26:08
* Description:订单站点表 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface OrderAgingStationFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/orderAgingStation", method = RequestMethod.POST)
    JsonResponse create(@RequestBody OrderAgingStation orderAgingStation);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/orderAgingStation/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/orderAgingStation/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderAgingStation orderAgingStation);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/orderAgingStation/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/orderAgingStations", method = RequestMethod.POST)
    JsonResponse search(@RequestBody OrderAgingStation orderAgingStation);
}
