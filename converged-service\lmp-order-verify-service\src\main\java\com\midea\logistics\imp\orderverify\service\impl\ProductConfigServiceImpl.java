package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EbLocationManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbLocation;
import com.midea.logistics.imp.orderverify.service.ProductConfigService;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderProductFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.ProductConfigFeign;
import com.midea.logistics.otp.enums.OrderTypeProductConfig;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderAddressFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderProduct;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.ProductConfig;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Service
public class ProductConfigServiceImpl implements ProductConfigService {


    @Autowired
    private CustomerOrderProductFeign customerOrderProductFeign;

    @Autowired
    private EbCustomerManager ebCustomerManager;

    @Autowired
    private CdWarehouseManager cdWarehouseManager;

    @Autowired
    private EbLocationManager ebLocationManager;

    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;

    @Autowired
    private ProductConfigFeign productConfigFeign;

    @Override
    @ZeebeFlow("PRODUCT_CONFIG")
    public CustomerOrderInfoExt checkProductConfig(CustomerOrderInfoExt customerOrderInfo){
        try{
            if (StringUtils.isEmpty(customerOrderInfo.getOrderNo())) {
                 FlowListenerParam.success("订单号不能为空");
                 return customerOrderInfo;
            }
            CustomerOrderProduct customerOrderProduct = new CustomerOrderProduct();
            customerOrderProduct.setParentOrderNo(customerOrderInfo.getOrderNo());
            customerOrderProduct.setCustomerOrderNo(customerOrderInfo.getCustomerOrderNo());
            JsonResponse<List<CustomerOrderProduct>> jsonResponse = customerOrderProductFeign.queryCustomerOrderProduct(customerOrderProduct);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
                 FlowListenerParam.success("获取订单产品表数据失败");
                return customerOrderInfo;
            }

            if (SourceSystem.HANDLE.getKey().equals(customerOrderInfo.getSourceSystem())&& CollectionUtils.isEmpty(jsonResponse.getData())) {
                 FlowListenerParam.success("无需服务产品解析");
                return customerOrderInfo;
            }
            if (!CollectionUtils.isEmpty(jsonResponse.getData()) && !StringUtils.isEmpty(jsonResponse.getData().get(0).getProductCode())) {
                 FlowListenerParam.success("服务产品规则已解析，解析产品为:" + jsonResponse.getData().get(0).getProductName());
                return customerOrderInfo;
            }
            if(!CollectionUtils.isEmpty(jsonResponse.getData())) {
                customerOrderProduct = jsonResponse.getData().get(0);
            }
             checkProductConfig( customerOrderInfo, customerOrderProduct);
        }catch (Exception e){
             FlowListenerParam.success("服务产品解析失败，失败原因:"+e.getMessage());
        }
        return customerOrderInfo;
    }


    public void checkProductConfig(CustomerOrderInfoExt customerOrderInfo,CustomerOrderProduct entity) {
        OrderTypeProductConfig orderTypeProductConfig = OrderTypeProductConfig.getOrderTypeProductConfig(customerOrderInfo.getOrderType());
        if (orderTypeProductConfig == null) {
             FlowListenerParam.success("服务产品规则订单类型匹配失败");
        }
        ProductConfig target = new ProductConfig();
        BeanUtils.copyProperties(customerOrderInfo, target);
        target.setNowOrderDate(new Date());
        CustomerOrderAddress customerOrderAddress = customerOrderInfo.getCustomerOrderAddress();
        if (null != orderTypeProductConfig && orderTypeProductConfig.getBigType().startsWith("WAREHOUSE")) {
            CdWarehouse cdWarehouse =null;
            if(StringUtils.isEmpty(customerOrderInfo.getWhCode())){
                cdWarehouse = getCdWarehouse(customerOrderInfo.getSiteCode());
            }else {
                cdWarehouse = cdWarehouseManager.getCdWarehouseCache(customerOrderInfo.getWhCode());
            }
            if(null != cdWarehouse) {
                EbLocation ebLocation = ebLocationManager.getEbLocationCache(cdWarehouse.getEblcCode());
                if(null != ebLocation) {
                    if (orderTypeProductConfig.getBigType().endsWith("RECEIVER")) {
                        target.setReceiverTownCode(ebLocation.getEblcTownCode());
                        target.setSenderTownCode(customerOrderAddress.getSenderTownCode());
                    } else {
                        target.setReceiverTownCode(customerOrderAddress.getReceiverTownCode());
                        target.setSenderTownCode(ebLocation.getEblcTownCode());
                    }
                }
            }
        } else {
            target.setReceiverTownCode(customerOrderAddress.getReceiverTownCode());
            target.setSenderTownCode(customerOrderAddress.getSenderTownCode());
        }
        target.setVolumeTo(customerOrderInfo.getTotalVolume());
        target.setVolumeFrom(customerOrderInfo.getTotalVolume());
        target.setWeightTo(customerOrderInfo.getTotalGrossWeight());
        target.setWeightFrom(customerOrderInfo.getTotalGrossWeight());
        if(StringUtils.isEmpty(target.getReceiverTownCode())){
            FlowListenerParam.success("服务产品解析失败，收货四级地址为空！");
        }
        if(StringUtils.isEmpty(target.getSenderTownCode())){
            FlowListenerParam.success("服务产品解析失败，发货四级地址为空！");
        }
        JsonResponse<List<ProductConfig>> productConfigJson = productConfigFeign.queryProductConfig(target);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(productConfigJson.getCode())) {
             FlowListenerParam.success("获取服务产品规则数据败，失败原因为:" + JSONObject.toJSONString(productConfigJson));
        }
        List<ProductConfig> productConfigList = productConfigJson.getData();
        if (CollectionUtils.isEmpty(productConfigList)) {
            target.setCustomerGroup(null);
            target.setCustomerCode(null);
            productConfigJson = productConfigFeign.queryProductConfig(target);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(productConfigJson.getCode())) {
                 FlowListenerParam.success("获取服务产品规则数据败，失败原因为:" + JSONObject.toJSONString(productConfigJson));
            }
            productConfigList = productConfigJson.getData();
        }
        if (CollectionUtils.isEmpty(productConfigList)) {
             FlowListenerParam.success("未匹配到服务产品");
        }else {
            BeanUtils.copyProperties(productConfigList.get(0), entity,"id");
            if (ToolUtils.isNotEmpty(entity.getId())) {
                customerOrderProductFeign.update(entity.getId(), entity);
            } else {
                customerOrderProductFeign.create(entity);
            }
            FlowListenerParam.success("服务产品规则已解析，服务产品为:" + entity.getProductName());
        }

    }


    private CdWarehouse getCdWarehouse(String siteCode){
        List<CdWarehouse> cdWarehouses = cdWarehouseManager.getCdWarehouseListCacheFromSiteCode(siteCode);
        if(CollectionUtils.isEmpty(cdWarehouses)){
            throw BusinessException.fail("根据平台获取仓库为空,平台编码:"+siteCode);
        }
        CdWarehouse  warehouse=null;
        //获取主仓
        for(CdWarehouse cdWarehouse : cdWarehouses){
            int enable = cdWarehouse.getCdwhIsStop()==null?1:cdWarehouse.getCdwhIsStop().intValue();
            if("1".equals(cdWarehouse.getIsMainWh()) && enable == 0){
                warehouse = cdWarehouse;
                break;
            }
            //无主仓库，随机取一仓库
            if(enable == 0){
                warehouse =cdWarehouse;
            }
        }
        return warehouse;
    }

}
