package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbPlaceManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.domain.mdm.request.CdWarehouseRequest;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.CdWarehouseFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EsCompanyFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.PreAppointmentConfigDetailFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.PreAppointmentConfigFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.common.helper.bean.PreAppointmentConfigExcelRequest;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.rule.domain.bean.PreAppointmentConfig;
import com.midea.logistics.otp.rule.domain.bean.PreAppointmentConfigDetail;
import com.midea.logistics.otplbs.manager.helper.CheckCustomerAgingConfigExcel;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


@Slf4j
@Component
public class PreAppointmentConfigManager {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    private PreAppointmentConfigFeign preAppointmentConfigFeign;
    @Autowired
    private PreAppointmentConfigDetailFeign preAppointmentConfigDetailFeign;
    @Autowired
    private EbPlaceManager ebPlaceManager;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private EsCompanyFeign esCompanyFeign;
    @Autowired
    private CdWarehouseFeign cdWarehouseFeign;

    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private EsCompanyManager esCompanyManager;

    @Autowired
    private  CheckCustomerAgingConfigExcel check;
    @Autowired
    private Validatorhelper validatorhelper;

    public JsonResponse<List<PreAppointmentConfigDetail>> excelImport(@RequestParam("file") MultipartFile file) throws InvalidFormatException, IOException,InstantiationException, IllegalAccessException, NoSuchFieldException {
        log.info("前置预约导入开始============================");
        ImportExcel ei = new ImportExcel(file, 1, 0,applicationContext);

        List<PreAppointmentConfigExcelRequest> list = ei.getDataList(PreAppointmentConfigExcelRequest.class, null);
        List<PreAppointmentConfigDetail> preAppointmentConfigDetails = Lists.newArrayList();
        List<String> fail = Lists.newArrayList();
        final int[] row = {1};
        list.stream().forEach(request -> {
            try{
                row[0] ++;
                check(request);
                //重新赋值
                PreAppointmentConfig preAppointmentConfig = new PreAppointmentConfig();
                preAppointmentConfig.setSiteCode(request.getSiteCode());
                preAppointmentConfig.setWhCode(request.getWhCode());
                PreAppointmentConfig data = preAppointmentConfigFeign.searchOne(preAppointmentConfig).data();
                BeanUtils.copyProperties(request, preAppointmentConfig);
                preAppointmentConfig.setEnableFlag(CommonConstant.NOT_ENABLED2.equals(request.getEnableFlagName())? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
                if (null != data){
                    preAppointmentConfig.setId(data.getId());
                }
                JsonResponse response = preAppointmentConfigFeign.batchCreateOrUpdate(Lists.newArrayList(preAppointmentConfig));
                if(!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())){
                    throw BusinessException.fail(response.getMsg());
                }
                PreAppointmentConfigDetail preAppointmentConfigDetail = new PreAppointmentConfigDetail();
                BeanUtils.copyProperties(request, preAppointmentConfigDetail);
                preAppointmentConfigDetail.setEnableFlag(CommonConstant.NOT_ENABLED2.equals(request.getEnableFlagName2())? CommonConstant.FLAG_NO : CommonConstant.FLAG_YES);
                preAppointmentConfigDetail.setId(request.getDetailId());
                JsonResponse response2 = preAppointmentConfigDetailFeign.batchCreateOrUpdate(Lists.newArrayList(preAppointmentConfigDetail));
                if(!BaseCodeEnum.SUCCESS.getCode().equals(response2.getCode())){
                    throw BusinessException.fail(response2.getMsg());
                }
                preAppointmentConfigDetails.add(preAppointmentConfigDetail);
            } catch (BusinessException ex){
                fail.add("第"+ row[0] +"行数据异常,"+ex.getMessage());
                log.error(ex.getMessage(), ex);
                return;
            }catch (Exception ep){
                fail.add("第"+ row[0] +"行数据异常,");
                log.error(ep.getMessage() ,ep);
                return;
            } finally {
                log.info("前置预约导入======第"+ row[0] +"行============导入中==========");
            }
        });
        log.info("前置预约导入结束============================");
        JsonResponse<List<PreAppointmentConfigDetail>> jsonResponse = new JsonResponse<>();
        jsonResponse.setData(preAppointmentConfigDetails);
        if (ToolUtils.isNotEmpty(fail)){
            jsonResponse.setMsg(JSON.toJSONString(fail));
            throw BusinessException.fail(JSON.toJSONString(fail));
        }
        return jsonResponse;
    }

    public void check(PreAppointmentConfigExcelRequest request) {
        List<String> validate = validatorhelper.validate(request);
        if (!CollectionUtils.isEmpty(validate)) {
            throw new BusinessException(BaseCodeEnum.PARAMETER_INVALID.getCode(), validate.toString());
        }
        //始发地
        String province = request.getProvinceName();
        String city = request.getCityName();
        String district = request.getDistrictName();
        List<String> addressCode = check.getAddressCode(Lists.newArrayList(province, city, district));
        if (addressCode != null) {
            String provinceCode = addressCode.get(0);
            String cityCode = addressCode.get(1);
            String districtCode = addressCode.get(2);
            request.setProvince(provinceCode);
            request.setCity(cityCode);
            request.setDistrict(districtCode);
            log.info("前置预约地址{}",JSON.toJSONString(addressCode));
        }

        // *服务平台
        String siteName = request.getSiteName();
        String siteCode = request.getSiteCode();
        if (StringUtils.isEmpty(siteCode)){
            String siteCode1 = querySiteCode(siteName);
            request.setSiteCode(siteCode1);
            if (ToolUtils.isEmpty(siteCode1)){
                throw BusinessException.fail("找不到平台");
            }

        }else{
            String esCompanyCache = esCompanyManager.getEsCompanyNameBySiteCode(siteCode);
            request.setSiteName(esCompanyCache);
            if (ToolUtils.isEmpty(esCompanyCache)){
                throw BusinessException.fail("找不到平台");
            }
        }

        // *仓库名称
        if (ToolUtils.isEmpty(request.getWhName()+request.getWhCode())){
            throw BusinessException.fail("仓库不能为空");
        }
        //装卸计费标识页面优化导入仓库解析逻辑:填写了仓库名称则解析仓库名称,未填写则按仓库编码解析
        String whName ="";
        String whCode = "";
        if (ToolUtils.isNotEmpty(request.getWhName())){

            whName = request.getWhName();
            whCode = queryWhCode(whName);
            if (ToolUtils.isEmpty(whCode)){
                throw BusinessException.fail("找不到仓库");
            }
            request.setWhCode(whCode);
        }else{
            whCode = request.getWhCode();
            whName = cdWarehouseManager.getCdWarehouseNameByWhCode(whCode);
            if (ToolUtils.isEmpty(whName)){
                throw BusinessException.fail("找不到仓库");
            }
            request.setWhName(whName);
        }
    }

    private String queryWhCode(String whName) {
        if(ToolUtils.isEmpty(whName)) {
            return null;
        }
        CdWarehouseRequest param = new CdWarehouseRequest();
        //cdwhIsStop=0
        param.setCdwhIsStop(0d);
        param.setCdwhName(whName);
        JsonResponse<PageResponse<CdWarehouse>> jsonResponse = cdWarehouseFeign.search(param);
        log.info("cdWarehouseFeign.search==={}",jsonResponse.toString());
        List<CdWarehouse> list = jsonResponse.data.list;
        CdWarehouse base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getWhCode();
    }

    private String querySiteCode(String siteName) {
        if(ToolUtils.isEmpty(siteName)) {
            return null;
        }
        JsonResponse<PageResponse<EsCompany>> jsonResponse = esCompanyFeign.searchByCodeAndNames(siteName);
        log.info("esCompanyFeign.searchByCodeAndNames==={}",jsonResponse.toString());
        List<EsCompany> list = jsonResponse.data.list;
        EsCompany base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getSiteCode();
    }

}


