package com.midea.logistics.otp.order.common.bean;

import com.midea.logistics.logisticsbopsdk.bean.OrderSynchronizationItemRequest;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: zhouhl
 * @date: 2023-3-6 10:16
 * @Description:
 */

@Data
public class OrderSynchronizationBmsItemRequest extends OrderSynchronizationItemRequest {
    /**
     * 成交价格
     */
    private BigDecimal finalPrice;

    /**
     * 计费组7
     */
    private String materialGroup7;

    /**
     * 计费组8
     */
    private String materialGroup8;

}
