package com.midea.logistics.otplbs.manager;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoInMapping;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoOutMapping;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.midea.logistics.otplbs.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otplbs.fegin.TaskFeign;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.wkclz.util.excel.Excel;
import com.wkclz.util.excel.ExcelRow;

/**
* @description: 订单
* @author: 陈永培
* @createtime: 2019/8/27 17:07
*/
@Component
public class OrderExportManager extends  BaseExportManager{

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    public void export(OrderInfoInMapping orderInfoInMapping, HttpServletResponse response) throws IOException {
        String fileName = "订单信息.xlsx";
        orderInfoInMapping.setIsExport(CommonConstant.STRING_FLAG_YES);
        List<OrderInfoOutMapping> list = Lists.newArrayList();

        JsonResponse<PageResponse<OrderInfoOutMapping>>  search = customerOrderInfoFeign.search(orderInfoInMapping);
        if (search == null || search.data() == null) {
            list = Lists.newArrayList();
        }else{
            list = search.getData().getList();
        }

        Excel excel = new Excel();
        excel.setTitle("订单信息");

        String[] header = { "客户订单号", "业务模式", "订单类型", "订单状态", "订单执行状态",
                            "客户", "平台", "仓库", "是否自提", "来源系统",
                            "订单来源", "目标客户", "项目分类", "收货省份", "收货市",
                            "收货县", "收货乡镇", "收货地址", "总毛重", "总体积",
                            "总数量", "上游收货单位", "上游发货单位", "上游原客户","订单来平台",
                            "店铺名称", "支付时间", "上游审核时间", "上游下单时间", "期望提货时间",
                            "承运商", "业务类型", "运作模式", "发货地址","合同号",
                            "发车单号", "上游仓库名称", "上游客户名称","关联单号","平台单号",
                            "原单号", "期望到货时间(起)", "期望到货时间(迄)","CCS工单号","买家备注",
                            "货值", "上游订单类型", "是否第三方","服务类型","创建时间",
                            "创建人", "修改时间", "修改人","订单号",
                            };
        excel.setHeader(header);

        for (OrderInfoOutMapping object : list) {

            ExcelRow row = excel.createRow();

            //客户订单号
            row.addCell(object.getCustomerOrderNo());
            //业务模式
            row.addCell(object.getBusinessTypeName());
            //订单类型
            row.addCell(object.getOrderTypeName());
            //订单状态
            row.addCell(object.getOrderStatusName());
            //订单执行状态
            row.addCell(object.getExcuteStatusName());


            //客户
            row.addCell(object.getCustomerName());
            //平台
            row.addCell(object.getSiteName());
            //仓库
            row.addCell(object.getWhName());
            //是否自提
            row.addCell(object.getDeliveryTypeName());
            //来源系统
            row.addCell(object.getSourceSystemName());


            //订单来源
            row.addCell(object.getOrderSourceName());
            //目标客户
            row.addCell(object.getTargetCustomerCode());
            //项目分类
            row.addCell(object.getProjectClassify());
            //收货省份
            row.addCell(object.getReceiverProvinceName());
            //收货市
            row.addCell(object.getReceiverCityName());

            //收货县
            row.addCell(object.getReceiverDistrictName());
            //收货乡镇
            row.addCell(object.getReceiverTownName());
            //收货地址
            row.addCell(object.getReceiverDetailAddr());
            //总毛重
            row.addCell(object.getTotalQty());
            //总体积
            row.addCell(object.getTotalVolume());


            //总数量
            row.addCell(object.getTotalQty());
            //上游收货单位
            row.addCell(object.getUpperReceiverName());
            //上游发货单位
            row.addCell(object.getUpperSenderName());
            //上游原客户
            row.addCell("");
            //订单来平台
            row.addCell(object.getOrderSourceName());

            //店铺名称
            row.addCell(object.getShopName());
            //支付时间
            row.addCell(object.getPayDate());
            //上游审核时间
            row.addCell(object.getUpperComfirmTime());
            //上游下单时间
            row.addCell(object.getUpperOrderTime());
            //期望提货时间
            row.addCell(object.getExpectPickTime());


            //承运商
            row.addCell(object.getCarrierCode());
            //业务类型
            row.addCell(object.getBusinessTypeName());
            //运作模式
            row.addCell(object.getLogisticMode());
            //发货地址
            row.addCell(object.getSenderDetailAddr());
            //合同号
            row.addCell(object.getContractNo());




            //发车单号
            row.addCell(object.getDispatchNo());
            //上游仓库名称
            row.addCell(object.getUpperWhName());
            //上游客户名称
            row.addCell(object.getUpperCustomerName());
            //关联单号
            row.addCell(object.getRelationOrderNo());
            //平台单号
            row.addCell(object.getPlatformOrderNo());

            //原单号"
            row.addCell(object.getOriginOrderNo());
            //期望到货时间(起)
            row.addCell(object.getExpectArriveStartTime());
            //期望到货时间(迄)
            row.addCell(object.getExpectArriveEndTime());
            //CCS工单号
            row.addCell(object.getOriginOrderNo());
            //买家备注
            row.addCell(object.getBuyerRemark());

            //货值
            row.addCell(object.getOrderValue());
            //上游订单类型
            row.addCell(object.getUpperOrderType());
            //是否第三方
            row.addCell(object.getThirdFlagName());
            //服务类型
            row.addCell(object.getServiceTypeName());
            //创建时间
            row.addCell(object.getCreateTime());

            //创建人
            row.addCell(object.getCreateUserName());
            //修改时间
            row.addCell(object.getUpdateTime());
            //修改人
            row.addCell(object.getUpdateUserName());
            //订单号
            row.addCell(object.getOrderNo());

        }

        //导出
       this.export(response, fileName, excel);

    }



}


