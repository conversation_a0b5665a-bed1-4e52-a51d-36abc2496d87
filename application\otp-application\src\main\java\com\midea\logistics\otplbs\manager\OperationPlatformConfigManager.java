package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EbPlace;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.EbPlaceMdmFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbCustomerFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EsCompanyFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.OperationPlatformConfigFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.bean.OperationPlatformConfigExcelRequest;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.rule.domain.bean.OperationPlatformConfig;
import com.midea.msgcenter.domain.bean.Message;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.MideaStringUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.msgcenter.starter.service.ISendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * Author:  ex_dengzj4
 * Date:     2021-1-18 17:40
 * Description: 运作单位导入
 */
@Slf4j
@Component
public class OperationPlatformConfigManager {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private EbCustomerManager ebCustomerManager;
    @Autowired
    private EbCustomerFeign ebCustomerFeign;
    @Autowired
    private EbPlaceMdmFeign ebPlaceMdmFeign;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private EsCompanyManager esCompanyManager;
    @Autowired
    private EsCompanyFeign esCompanyFeign;
    @Autowired
    private OperationPlatformConfigFeign operationPlatformConfigFeign;
    @Autowired
    private ISendMessageService sendMessageService;
    @Autowired
    private ISsoService iSsoService;

    //省
    public final static String PLACE_PROVINCE = "PLACE_PROVINCE";
    //市
    public final static String PLACE_CITY = "PLACE_CITY";

    private final String REPORT_OTP_OPERATION_PLATFORM = "REPORT_OTP_OPERATION_PLATFORM";


    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) {


        JsonResponse response = new JsonResponse();
        try {
            ImportExcel importExcel = new ImportExcel(file, 1, 0, applicationContext);
            int lastCellNum = importExcel.getLastCellNum();
            Class clazz = OperationPlatformConfigExcelRequest.class;
            if (lastCellNum != 9) {
                throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
            }
            List<OperationPlatformConfigExcelRequest> dataList = importExcel.getDataList(clazz, null);

            new ConfigExcelImportThread(dataList).run();

        } catch (Exception e) {
            response.setCode(BaseCodeEnum.FAILED.getCode());
            response.setMsg(e.getMessage());
        }

        return response;
    }

    /**
     * 异步调用
     */
    public class ConfigExcelImportThread implements Runnable {

        private List<OperationPlatformConfigExcelRequest> dataList;

        ConfigExcelImportThread(List<OperationPlatformConfigExcelRequest> dataList) {
            this.dataList = dataList;
        }

        @Override
        public void run() {
            try {
                Map<Integer,String> errorMap = Maps.newTreeMap();
                if (CollectionUtils.isEmpty(dataList)) {
                    sendMessage(BaseCodeEnum.SUCCESS.getCode(),"运作单位导入成功");
                    return;
                }
                List<OperationPlatformConfig> list = checkExcelDistantData(errorMap, dataList);
                if (!CollectionUtils.isEmpty(list)) {
                    JsonResponse response1 = operationPlatformConfigFeign.addOrUpdateOrDeleteList(list);
                    if (BaseCodeEnum.FAILED.getCode().equals(response1.getCode())) {
                        throw BusinessException.fail(response1.getMsg());
                    }
                }
                if (errorMap.size() != 0) {
                    throw BusinessException.fail(JSON.toJSONString(errorMap.values()));
                }
            } catch (Exception e) {
                sendMessage(BaseCodeEnum.FAILED.getCode(), e.getMessage());
                return;
            }
            sendMessage(BaseCodeEnum.SUCCESS.getCode(), "运作单位导入成功");
        }
    }

    /**
     * excel 重复校验
     */
    private List<OperationPlatformConfig> checkExcelDistantData(Map<Integer,String> errorMap, List<OperationPlatformConfigExcelRequest> dataList) {
        List<OperationPlatformConfig> list = new ArrayList<>();
        ArrayList<String> checkList = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            OperationPlatformConfig config = new OperationPlatformConfig();
            try {
                if (null == dataList.get(i).getId() && CommonConstant.YES.equals(dataList.get(i).getDeleteFlagName())) {
                    throw BusinessException.fail("无法进行删除,ID为空");
                }
                checkParams(dataList.get(i));
                if (StringUtils.isBlank(dataList.get(i).getDeleteFlagName())) {
                    dataList.get(i).setDeleteFlagName(CommonConstant.NO);
                }
//                for (int j = i + 1; j < dataList.size(); j++) {
//                    if (StringUtils.isBlank(dataList.get(j).getDeleteFlagName())) {
//                        dataList.get(j).setDeleteFlagName(CommonConstant.NO);
//                    }
//                    if (compare(dataList.get(i).getId(),(dataList.get(j).getId()))
//                        && compare(dataList.get(i).getDeleteFlagName(),dataList.get(j).getDeleteFlagName())
//                        && compare(dataList.get(i).getCustomerCode(),(dataList.get(j).getCustomerCode()))
//                        && compare(dataList.get(i).getCustomerName(),(dataList.get(j).getCustomerName()))
//                        && compare(dataList.get(i).getReceiverProvinceName(),(dataList.get(j).getReceiverProvinceName()))
//                        && compare(dataList.get(i).getReceiverCityName(),(dataList.get(j).getReceiverCityName()))
//                        && compare(dataList.get(i).getSourceSystem(),(dataList.get(j).getSourceSystem()))
//                        && compare(dataList.get(i).getSiteCode(),(dataList.get(j).getSiteCode()))
//                        && compare(dataList.get(i).getSiteName(),(dataList.get(j).getSiteName()))) {
//                        dataList.get(j).setDeleteFlagName(CommonConstant.YES);
//                    }
//                }
                checkIsExistsParams(dataList.get(i), config);
                //新增
                if (null == config.getId() && 0 == config.getDeleteFlag()) {
                    checkDistant(config);
                    checkDistantList(checkList, config, list);
                }
                //更新
                if (null != config.getId() && 0 == config.getDeleteFlag()) {
                    checkIsExistsData(config);
                    checkDistant(config);
                    checkDistantList(checkList, config, list);
                }
                //删除
                if (null != config.getId() && 1 == config.getDeleteFlag()) {
                    checkIsExistsData(config);
                    list.add(config);
                }
            } catch (Exception e) {
                errorMap.put(dataList.get(i).getRow(),"第"+dataList.get(i).getRow()+"行数据异常." + e.getMessage());
            }
        }
        return list;
    }

    /**
     * 过滤新增更新重复数据
     */
    private void checkDistantList(ArrayList<String> checkList, OperationPlatformConfig config, List<OperationPlatformConfig> list) {
        String key = config.getCustomerCode() + "_"
            + config.getReceiverProvinceCode() + "_"
            + config.getReceiverCityCode() + "_"
            + config.getSourceSystem() + "_"
            + config.getSiteCode()
            ;
        if (!checkList.contains(key)) {
            checkList.add(key);
            list.add(config);
        }
    }

    private static boolean compare(Object a1, Object a2) {
        if (a1 == null && a2 == null) {
            return true;
        }
        if (a1 != null) {
            return String.valueOf(a1).equals(String.valueOf(a2));
        }
        if (a2 != null) {
            return String.valueOf(a2).equals(String.valueOf(a1));
        }
        return false;
    }

    /**
     * 必填校验
     */
    private void checkParams(OperationPlatformConfigExcelRequest request) {
        StringBuffer sb = new StringBuffer();
        if (StringUtils.isBlank(request.getCustomerCode()) && StringUtils.isBlank(request.getCustomerName())) {
            sb.append("customerCode、customerName不可同时为空;");
        }
        if (StringUtils.isBlank(request.getReceiverProvinceName())) {
            sb.append("receiverProvinceName必填，不可为空;");
        }
        if (StringUtils.isBlank(request.getReceiverCityName())) {
            sb.append("receiverCityName必填，不可为空;");
        }
        if (StringUtils.isBlank(request.getSiteCode()) && StringUtils.isBlank(request.getSiteName())) {
            sb.append("siteCode、siteName不可同时为空;");
        }
        if (!StringUtils.isEmpty(sb.toString())) {
            throw BusinessException.fail(sb.toString());
        }
    }

    /**
     * 数据库校验重复
     */
    private void checkDistant(OperationPlatformConfig config) {
        JsonResponse<OperationPlatformConfig> response = operationPlatformConfigFeign.checkDistant(config);
        if (BaseCodeEnum.FAILED.getCode().equals(response.getCode())) {
            throw BusinessException.fail("调用rule服务失败." + response.getMsg() +  ";");
        }
        if (null != response.getData()) {
            throw BusinessException.fail("记录已存在ID为" + response.getData().getId() + "，完成新增;");
        }
    }

    /**
     * 数据库是否存在
     */
    private void checkIsExistsData(OperationPlatformConfig config) {
        JsonResponse<OperationPlatformConfig> response = operationPlatformConfigFeign.queryByBusinessKey(config.getId());
        if (BaseCodeEnum.FAILED.getCode().equals(response.getCode())) {
            throw BusinessException.fail("调用rule服务失败." + response.getMsg() + ";");
        }
        if (null == response.getData()) {
            throw BusinessException.fail("记录不存在或已经删除，无法进行更新、删除");
        }
        config.setVersion(response.getData().getVersion());
    }

    /**
     * 校验数据是否存在
     */
    private void checkIsExistsParams(OperationPlatformConfigExcelRequest request, OperationPlatformConfig config) {
        StringBuffer sb = new StringBuffer();
        config.setId(request.getId());
        config.setDeleteFlag(CommonConstant.YES.equals(request.getDeleteFlagName()) ? 1 : 0);
        String customerCode = request.getCustomerCode();
        String customerName = request.getCustomerName();
        String receiverProvinceName = request.getReceiverProvinceName();
        String receiverCityName = request.getReceiverCityName();
        String sourceSystem = request.getSourceSystem();
        String siteCode = request.getSiteCode();
        String siteName = request.getSiteName();
        if (StringUtils.isNotBlank(customerCode)) {
            EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(customerCode);
            if (ebCustomerCache == null || Double.valueOf(CommonConstant.Y).equals(ebCustomerCache.getRecStatus())) {
                sb.append("客户编码：" + customerCode + "不存在或已停用;");
            } else {
                config.setCustomerCode(request.getCustomerCode());
                config.setCustomerName(ebCustomerCache.getEbcuNameCn());
            }
        }
        if (StringUtils.isBlank(config.getCustomerCode()) && StringUtils.isNotBlank(customerName)) {
            EbCustomer ebCustomer = new EbCustomer();
            ebCustomer.setEbcuNameCn(request.getCustomerName());
            List<EbCustomer> list = ebCustomerFeign.searchByEbCustomer(ebCustomer).data.getList();
            if (CollectionUtils.isEmpty(list) || Double.valueOf(CommonConstant.Y).equals(list.get(0).getRecStatus())) {
                sb.append("客户名称：" + customerName + "不存在或已停用;");
            } else {
                config.setCustomerCode(list.get(0).getPmCode());
                config.setCustomerName(list.get(0).getEbcuNameCn());
            }
        }
        if (StringUtils.isNotBlank(receiverProvinceName)) {
            EbPlace ebPlace = matchMoreMDMStandard(receiverProvinceName, PLACE_PROVINCE, null);
            if (ebPlace == null || StringUtils.isBlank(ebPlace.getEbplCode())) {
                sb.append("省：" + receiverProvinceName + "匹配无结果;");
            } else {
                config.setReceiverProvinceCode(ebPlace.getEbplCode());
                config.setReceiverProvinceName(receiverProvinceName);
            }
        }
        if (StringUtils.isNotBlank(receiverCityName)) {
            EbPlace ebPlace = matchMoreMDMStandard(receiverCityName, PLACE_CITY, null);
            if (ebPlace == null || StringUtils.isBlank(ebPlace.getEbplCode())) {
                sb.append("市：" + receiverCityName + "匹配无结果;");
            } else {
                config.setReceiverCityCode(ebPlace.getEbplCode());
                config.setReceiverCityName(receiverCityName);
            }
        }
        if (StringUtils.isNotBlank(sourceSystem)) {
            String dictCodeByName = dictHelper.getDictCodeByName(SourceSystem.DICT_CODE.getValue(), sourceSystem);
            if (StringUtils.isBlank(dictCodeByName)) {
                sb.append("来源系统:" + sourceSystem + "查询字典("+SourceSystem.DICT_CODE.getValue()+")结果为空;");
            } else {
                config.setSourceSystem(dictCodeByName);
            }
        } else {
            config.setSourceSystem("");
        }
        if (StringUtils.isNotBlank(siteCode)) {
            EsCompany siteCache = esCompanyManager.getEsCompanyCache(siteCode);
            if(siteCache==null){
                throw BusinessException.fail("调用基础数据获取不到平台信息，平台编码："+siteCode+";");
            }
            else if(!"ENABLE".equals(siteCache.getEscoStatus())){
                throw BusinessException.fail("平台不可用,平台编码:"+siteCode + ";");
            }
            else {
                config.setSiteCode(siteCode);
                config.setSiteName(siteCache.getEscoCompanyNameCn());
            }
        }
        if (StringUtils.isBlank(config.getSiteCode()) && StringUtils.isNotBlank(siteName)) {
            List<EsCompany> list = esCompanyFeign.searchByCodeAndNames(siteName).data.list;
            if (list == null || CollectionUtils.isEmpty(list) || !"ENABLE".equals(list.get(0).getEscoStatus())) {
                sb.append("平台名称：" + siteName + "不存在或已停用;");
            } else {
                config.setSiteCode(list.get(0).getEscoCompanyNo());
                config.setSiteName(siteName);
            }
        }
        if (!StringUtils.isEmpty(sb.toString())) {
            throw BusinessException.fail(sb.toString());
        }
    }

    public EbPlace matchMoreMDMStandard(String ebplNameCn , String ebplType, String ebplParentPmCode){

        if (StringUtils.isEmpty(ebplNameCn)){
            return  null;
        }

        EbPlace ebPlace = new EbPlace();
        ebPlace.setEbplNameCn(this.getTwoAddr(ebplNameCn));
        ebPlace.setEbplType(ebplType);
        ebPlace.setEbplIsAble("ENABLE");
        ebPlace.setEbplParentPmCode(ebplParentPmCode);
        JsonResponse<List<EbPlace>> ebPlaceJsonResponse = ebPlaceMdmFeign.searchEbPlace(ebPlace);
        List<EbPlace> data = ebPlaceJsonResponse.data();
        if (MideaStringUtils.isNotEmpty(data)){
            EbPlace place = matchDistrictName(ebplNameCn, data);
            place = place == null ? data.get(0) : place;
            log.info("AddressHelper--->matchMDMStandard,查询类型["+ebplType+"],原地址名："+ebplNameCn+",匹配到的地址名："+place.getEbplNameCn());
            return place;
        }
        return null;
    }

    /**
     * @description: 获取前两位地址
     * @param: [name]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/4/21 15:10
     */
    public static String getTwoAddr(String name){
        if (StringUtils.isNotEmpty(name)) {
            //有可能 ( 连州市)，前面有个空格，导致只取了（ 连），所以需要trim一下
            name = trim(name);
            return name.substring(0,2);
        }
        return name;
    }

    public static String trim(String s){
        String result = "";
        if(null!=s && !"".equals(s)){
            s = s.trim();
            result = s.replaceAll("^[　\\u00A0]*", "").replaceAll("[　\\u00A0]*$", "");
        }
        return result;
    }

    public EbPlace matchDistrictName(String districtFullName, List<EbPlace> matchingList) {
        Map<Integer, Double> similarity = new HashMap<Integer, Double>();
        for (int i = 0; i <= matchingList.size() - 1; i++) {
            similarity.put(i,computeSimilarity(districtFullName, matchingList.get(i).getEbplNameCn()));
        }
        return matchingList.get(getMostSimilarity(similarity));
    }

    public Double computeSimilarity(String districtFullName, String matchingName) {
        if (StringUtils.isBlank(districtFullName) || StringUtils.isBlank(matchingName)) {
            return new Double(0);
        }
        for (int i = districtFullName.length(); i >= 1; i--) {
            if (matchingName.contains(districtFullName.substring(0, i))) {
                BigDecimal districtLength = new BigDecimal(i);
                BigDecimal similarity = districtLength.divide(new BigDecimal(matchingName.length()), 2, RoundingMode.HALF_UP);
                return similarity.doubleValue();
            }
        }
        return new Double(0);
    }

    public Integer getMostSimilarity(Map<Integer, Double> similarity) {
        int index = 0;
        Double maxSimilarity = new Double(0);
        for (Integer key : similarity.keySet()) {
            if (maxSimilarity.compareTo(similarity.get(key)) < 0) {
                maxSimilarity = similarity.get(key);
                index = key;
            }
        }
        return index;
    }

    private void sendMessage(String code, String msg) {
        //发送消息
        Message commonMessage = new Message();
        commonMessage.setTitle("运作单位导入" + (BaseCodeEnum.SUCCESS.getCode().equals(code) ? "成功" : "失败"));
        commonMessage.setMessageType(1);
        commonMessage.setContent(msg);
        ArrayList<String> list = new ArrayList<>();
        list.add(iSsoService.getUserCode());
        commonMessage.setReceiverList(list);
        commonMessage.setApplicationCode(dictHelper.getDictVaule("REPORT_OTP_OPERATION_PLATFORM","applicationCode"));
        commonMessage.setCreateTime(new Date());
        sendMessageService.sendMessage("3", commonMessage);
    }
}
