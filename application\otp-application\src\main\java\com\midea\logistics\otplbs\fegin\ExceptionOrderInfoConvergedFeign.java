package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.ExceptionOrderInfo;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * @author: zhouhl
 * @date: 2022-9-5 11:33
 * @Description:
 */

@FeignClient(value = "logistics-otp-order-converged-service")
public interface ExceptionOrderInfoConvergedFeign {
    @PostMapping("/converged/exceptionOrderInfos")
    JsonResponse createBatch(List<ExceptionOrderInfo> exceptionOrderInfos);
}
