package com.midea.logistics.otp.order.common.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.ThirdPartRule;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: ThirdPartRuleFeign
* Author: jiajun
* Date: 2019-7-10 11:03:44
* Description:第三方规则 feign
*/
@FeignClient(value = "logistics-otp-rule-service" ,url= BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface ThirdPartRuleFeign {

    @RequestMapping(value = "/thirdPartRule", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ThirdPartRule thirdPartRule);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/thirdPartRule/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/thirdPartRule/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ThirdPartRule thirdPartRule);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/thirdPartRule/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/thirdPartRules", method = RequestMethod.GET)
    JsonResponse<PageResponse<ThirdPartRule>> search(@SpringQueryMap ThirdPartRule thirdPartRule);
    
    /**
    * 批量新增或者保存，根据是否传id来判断
    *
    * @return
    */
    @RequestMapping(value = "/thirdPartRule/batchCreateOrUpdate", method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<ThirdPartRule> thirdPartRules);

   /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = "/thirdPartRule/batchDeleteByBusinessKey", method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

    @GetMapping(OptRuleRouters.THIRD_PART_RULE_FIND_ONE)
    JsonResponse<ThirdPartRule> getThirdPartRule(@SpringQueryMap ThirdPartRule thirdPartRule);
}
