<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all-cancel" name="父订单流程-兼容取消" isExecutable="true">
    <bpmn:startEvent id="Event_0cb5x12" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1tn3m8a</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:subProcess id="Activity_02ptrhy" name="父单审核流程">
      <bpmn:incoming>Flow_1tn3m8a</bpmn:incoming>
      <bpmn:outgoing>Flow_0guama2</bpmn:outgoing>
      <bpmn:startEvent id="Event_11b55zp" name="开始">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
          </zeebe:ioMapping>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_0hgospe</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:serviceTask id="Activity_1vinmjn" name="商品确认">
        <bpmn:extensionElements>
          <zeebe:taskDefinition type="http" retries="1" />
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mapping" />
            <zeebe:header key="method" value="POST" />
            <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
          </zeebe:taskHeaders>
          <zeebe:ioMapping>
            <zeebe:input source="= customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_16nqw9b</bpmn:incoming>
        <bpmn:incoming>Flow_0wpv44o</bpmn:incoming>
        <bpmn:outgoing>Flow_1p5x2tj</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_097tbpa" name="结束">
        <bpmn:incoming>Flow_0dzibne</bpmn:incoming>
        <bpmn:incoming>Flow_19dz5og</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:exclusiveGateway id="Gateway_0m6vomx" name="是否箱包转换" default="Flow_0wpv44o">
        <bpmn:incoming>Flow_0yovb73</bpmn:incoming>
        <bpmn:outgoing>Flow_0wpv44o</bpmn:outgoing>
        <bpmn:outgoing>Flow_148qz62</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="Activity_1edj9ug" name="箱包转换">
        <bpmn:extensionElements>
          <zeebe:taskDefinition type="http" retries="1" />
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/luggage/conversion" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_148qz62</bpmn:incoming>
        <bpmn:outgoing>Flow_16nqw9b</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="Activity_1poj2j7" name="订单映射">
        <bpmn:extensionElements>
          <zeebe:taskDefinition type="http" retries="1" />
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
            <zeebe:header key="method" value="POST" />
            <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
          </zeebe:taskHeaders>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0hgospe</bpmn:incoming>
        <bpmn:outgoing>Flow_0sle5mu</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="Activity_1v4t7zs" name="MIP流程">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/mipExecutionAudit" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:taskDefinition type="http" retries="1" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1bvgfzn</bpmn:incoming>
        <bpmn:outgoing>Flow_0hudqtc</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="Gateway_16w42cy" name="是否走mip流程" default="Flow_041n575">
        <bpmn:incoming>Flow_1qqsup3</bpmn:incoming>
        <bpmn:incoming>Flow_194n3d3</bpmn:incoming>
        <bpmn:outgoing>Flow_1bvgfzn</bpmn:outgoing>
        <bpmn:outgoing>Flow_041n575</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="Activity_1w60lrh" name="收货单位解析">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:taskDefinition type="http" retries="1" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0hudqtc</bpmn:incoming>
        <bpmn:incoming>Flow_041n575</bpmn:incoming>
        <bpmn:outgoing>Flow_0d5gssr</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="Activity_09m3vjm" name="仓间调拨解析">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/allocationParse" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:taskDefinition type="http" retries="1" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_182hruf</bpmn:incoming>
        <bpmn:outgoing>Flow_1mye7zv</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="Activity_1yrzds0" name="四级地址解析">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/address" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:taskDefinition type="http" retries="1" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_11s9iqo</bpmn:incoming>
        <bpmn:outgoing>Flow_0i9foc2</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="Activity_1t55l6z" name="货权转移校验">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/cargo/right/transfer" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:taskDefinition type="http" retries="1" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_01knaz0</bpmn:incoming>
        <bpmn:outgoing>Flow_0lm3kug</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask id="Activity_0aetv8p" name="分仓拆单">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:taskDefinition type="http" retries="1" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1k1104m</bpmn:incoming>
        <bpmn:outgoing>Flow_0e7m4rj</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="Gateway_1vn3zq3" name="是否仓间调拨" default="Flow_0x69gy8">
        <bpmn:incoming>Flow_0d5gssr</bpmn:incoming>
        <bpmn:outgoing>Flow_182hruf</bpmn:outgoing>
        <bpmn:outgoing>Flow_0x69gy8</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:exclusiveGateway id="Gateway_1mae8el" name="是否地址解析" default="Flow_032fwq9">
        <bpmn:incoming>Flow_1mye7zv</bpmn:incoming>
        <bpmn:incoming>Flow_0x69gy8</bpmn:incoming>
        <bpmn:outgoing>Flow_11s9iqo</bpmn:outgoing>
        <bpmn:outgoing>Flow_032fwq9</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:exclusiveGateway id="Gateway_18w43la" name="是否货权转移" default="Flow_0e0owrg">
        <bpmn:incoming>Flow_0i9foc2</bpmn:incoming>
        <bpmn:incoming>Flow_032fwq9</bpmn:incoming>
        <bpmn:outgoing>Flow_01knaz0</bpmn:outgoing>
        <bpmn:outgoing>Flow_0e0owrg</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:exclusiveGateway id="Gateway_1baj0se" name="是否分仓拆单">
        <bpmn:incoming>Flow_0lm3kug</bpmn:incoming>
        <bpmn:incoming>Flow_0e0owrg</bpmn:incoming>
        <bpmn:outgoing>Flow_1k1104m</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="Activity_0qjuob2" name="订单信息校验">
        <bpmn:extensionElements>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-order-agg-service/order/orderInfoConfirm" />
            <zeebe:header key="method" value="POST" />
            <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
          </zeebe:taskHeaders>
          <zeebe:taskDefinition type="http" retries="1" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0sle5mu</bpmn:incoming>
        <bpmn:outgoing>Flow_0yovb73</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="Gateway_0d3vdz8" name="手工分仓" default="Flow_0za6ra7">
        <bpmn:incoming>Flow_0e7m4rj</bpmn:incoming>
        <bpmn:outgoing>Flow_0za6ra7</bpmn:outgoing>
        <bpmn:outgoing>Flow_1hh572u</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="Activity_0qakb3x" name="客户订单审核完成&#10;触发子流程">
        <bpmn:extensionElements>
          <zeebe:taskDefinition type="http" retries="1" />
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/customerOrderVerifyEnd" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0za6ra7</bpmn:incoming>
        <bpmn:outgoing>Flow_0q052ez</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway id="Gateway_1sy6qgw" name="是否审核完成" default="Flow_0dzibne">
        <bpmn:incoming>Flow_0q052ez</bpmn:incoming>
        <bpmn:outgoing>Flow_0dzibne</bpmn:outgoing>
        <bpmn:outgoing>Flow_1hnbuti</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:exclusiveGateway id="Gateway_0auklqt" name="B2C" default="Flow_1qqsup3">
        <bpmn:incoming>Flow_1p5x2tj</bpmn:incoming>
        <bpmn:outgoing>Flow_1qqsup3</bpmn:outgoing>
        <bpmn:outgoing>Flow_11jeku6</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:serviceTask id="Activity_1dp0z7g" name="件型计算">
        <bpmn:extensionElements>
          <zeebe:taskDefinition type="http" retries="1" />
          <zeebe:ioMapping>
            <zeebe:input source="=customerOrderInfo" target="parameterBody" />
            <zeebe:output source="=responseBody" target="customerOrderInfo" />
          </zeebe:ioMapping>
          <zeebe:taskHeaders>
            <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/pieceAnalysis" />
            <zeebe:header key="method" value="POST" />
          </zeebe:taskHeaders>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_11jeku6</bpmn:incoming>
        <bpmn:outgoing>Flow_194n3d3</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0hgospe" sourceRef="Event_11b55zp" targetRef="Activity_1poj2j7" />
      <bpmn:sequenceFlow id="Flow_16nqw9b" sourceRef="Activity_1edj9ug" targetRef="Activity_1vinmjn" />
      <bpmn:sequenceFlow id="Flow_0wpv44o" name="no" sourceRef="Gateway_0m6vomx" targetRef="Activity_1vinmjn" />
      <bpmn:sequenceFlow id="Flow_1p5x2tj" sourceRef="Activity_1vinmjn" targetRef="Gateway_0auklqt" />
      <bpmn:sequenceFlow id="Flow_0dzibne" sourceRef="Gateway_1sy6qgw" targetRef="Event_097tbpa" />
      <bpmn:sequenceFlow id="Flow_0yovb73" sourceRef="Activity_0qjuob2" targetRef="Gateway_0m6vomx" />
      <bpmn:sequenceFlow id="Flow_148qz62" name="yes" sourceRef="Gateway_0m6vomx" targetRef="Activity_1edj9ug">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.sourceSystem!=null and customerOrderInfo.sourceSystem="KingDee-SD")=true</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0sle5mu" sourceRef="Activity_1poj2j7" targetRef="Activity_0qjuob2" />
      <bpmn:sequenceFlow id="Flow_1bvgfzn" name="yes" sourceRef="Gateway_16w42cy" targetRef="Activity_1v4t7zs">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.mipFlag!=null and customerOrderInfo.mipFlag=1)=true</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0hudqtc" sourceRef="Activity_1v4t7zs" targetRef="Activity_1w60lrh" />
      <bpmn:sequenceFlow id="Flow_1qqsup3" name="no" sourceRef="Gateway_0auklqt" targetRef="Gateway_16w42cy" />
      <bpmn:sequenceFlow id="Flow_194n3d3" sourceRef="Activity_1dp0z7g" targetRef="Gateway_16w42cy" />
      <bpmn:sequenceFlow id="Flow_041n575" name="no" sourceRef="Gateway_16w42cy" targetRef="Activity_1w60lrh" />
      <bpmn:sequenceFlow id="Flow_0d5gssr" sourceRef="Activity_1w60lrh" targetRef="Gateway_1vn3zq3" />
      <bpmn:sequenceFlow id="Flow_182hruf" name="yes" sourceRef="Gateway_1vn3zq3" targetRef="Activity_09m3vjm">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AI")))</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1mye7zv" sourceRef="Activity_09m3vjm" targetRef="Gateway_1mae8el" />
      <bpmn:sequenceFlow id="Flow_11s9iqo" sourceRef="Gateway_1mae8el" targetRef="Activity_1yrzds0">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and ((customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT") or (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO"))</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0i9foc2" sourceRef="Activity_1yrzds0" targetRef="Gateway_18w43la" />
      <bpmn:sequenceFlow id="Flow_01knaz0" name="yes" sourceRef="Gateway_18w43la" targetRef="Activity_1t55l6z">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CAINIAOPO","CCSJDPI","CIMSPO","CIMSPI","CIMSRI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0lm3kug" sourceRef="Activity_1t55l6z" targetRef="Gateway_1baj0se" />
      <bpmn:sequenceFlow id="Flow_1k1104m" name="no" sourceRef="Gateway_1baj0se" targetRef="Activity_0aetv8p" />
      <bpmn:sequenceFlow id="Flow_0e7m4rj" sourceRef="Activity_0aetv8p" targetRef="Gateway_0d3vdz8" />
      <bpmn:sequenceFlow id="Flow_0x69gy8" sourceRef="Gateway_1vn3zq3" targetRef="Gateway_1mae8el" />
      <bpmn:sequenceFlow id="Flow_032fwq9" name="no" sourceRef="Gateway_1mae8el" targetRef="Gateway_18w43la" />
      <bpmn:sequenceFlow id="Flow_0e0owrg" name="no" sourceRef="Gateway_18w43la" targetRef="Gateway_1baj0se" />
      <bpmn:sequenceFlow id="Flow_0za6ra7" name="no" sourceRef="Gateway_0d3vdz8" targetRef="Activity_0qakb3x" />
      <bpmn:sequenceFlow id="Flow_0q052ez" sourceRef="Activity_0qakb3x" targetRef="Gateway_1sy6qgw" />
      <bpmn:sequenceFlow id="Flow_11jeku6" name="yes" sourceRef="Gateway_0auklqt" targetRef="Activity_1dp0z7g">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo.businessMode="B2C")=true</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:subProcess id="Activity_15i6z34">
        <bpmn:incoming>Flow_1hh572u</bpmn:incoming>
        <bpmn:incoming>Flow_1hnbuti</bpmn:incoming>
        <bpmn:outgoing>Flow_19dz5og</bpmn:outgoing>
        <bpmn:startEvent id="Event_0t818k3" name="开始">
          <bpmn:extensionElements>
            <zeebe:ioMapping>
              <zeebe:output source="=orderNo" target="orderNo" />
            </zeebe:ioMapping>
          </bpmn:extensionElements>
          <bpmn:outgoing>Flow_1fvu3a2</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:serviceTask id="Activity_0v3lzr5" name="客户订单审核完成&#10;触发子流程">
          <bpmn:extensionElements>
            <zeebe:taskDefinition type="http" retries="1" />
            <zeebe:taskHeaders>
              <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/customerOrderVerifyEnd" />
              <zeebe:header key="method" value="POST" />
            </zeebe:taskHeaders>
            <zeebe:ioMapping>
              <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody" />
              <zeebe:output source="=responseBody" target="customerOrderInfo" />
            </zeebe:ioMapping>
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_02qbhxe</bpmn:incoming>
          <bpmn:outgoing>Flow_0annran</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:intermediateCatchEvent id="Event_0wafm93" name="手工分仓">
          <bpmn:extensionElements>
            <zeebe:ioMapping>
              <zeebe:output source="=orderNo" target="orderNo" />
            </zeebe:ioMapping>
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_1fvu3a2</bpmn:incoming>
          <bpmn:incoming>Flow_08vl6xu</bpmn:incoming>
          <bpmn:outgoing>Flow_02qbhxe</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_0a4hzqv" messageRef="Message_0vzd89y" />
        </bpmn:intermediateCatchEvent>
        <bpmn:sequenceFlow id="Flow_02qbhxe" sourceRef="Event_0wafm93" targetRef="Activity_0v3lzr5" />
        <bpmn:sequenceFlow id="Flow_1fvu3a2" sourceRef="Event_0t818k3" targetRef="Event_0wafm93" />
        <bpmn:endEvent id="Event_0ik8u5b" name="结束">
          <bpmn:incoming>Flow_08qv3cr</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="Flow_0annran" sourceRef="Activity_0v3lzr5" targetRef="Gateway_0lr35sb" />
        <bpmn:exclusiveGateway id="Gateway_0lr35sb" name="是否审核完成" default="Flow_08qv3cr">
          <bpmn:incoming>Flow_0annran</bpmn:incoming>
          <bpmn:outgoing>Flow_08qv3cr</bpmn:outgoing>
          <bpmn:outgoing>Flow_08vl6xu</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_08qv3cr" sourceRef="Gateway_0lr35sb" targetRef="Event_0ik8u5b" />
        <bpmn:sequenceFlow id="Flow_08vl6xu" name="yes" sourceRef="Gateway_0lr35sb" targetRef="Event_0wafm93">
          <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo.orderStatus&lt;200 and apartStatus="PART")=true</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
      </bpmn:subProcess>
      <bpmn:sequenceFlow id="Flow_1hnbuti" name="yes" sourceRef="Gateway_1sy6qgw" targetRef="Activity_15i6z34">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo.orderStatus&lt;200 and apartStatus="PART")=true</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1hh572u" name="yes" sourceRef="Gateway_0d3vdz8" targetRef="Activity_15i6z34">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo.isHandle</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_19dz5og" sourceRef="Activity_15i6z34" targetRef="Event_097tbpa" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_1tn3m8a" sourceRef="Event_0cb5x12" targetRef="Activity_02ptrhy" />
    <bpmn:boundaryEvent id="Event_0r7ph51" name="取消、拦截、冲销" attachedToRef="Activity_02ptrhy">
      <bpmn:outgoing>Flow_08fn26q</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1rjhiir" messageRef="Message_10ypi2b" />
    </bpmn:boundaryEvent>
    <bpmn:endEvent id="Event_0ztlhdj" name="结束">
      <bpmn:incoming>Flow_08fn26q</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_08fn26q" sourceRef="Event_0r7ph51" targetRef="Event_0ztlhdj" />
    <bpmn:endEvent id="Event_06boz42">
      <bpmn:incoming>Flow_0guama2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0guama2" sourceRef="Activity_02ptrhy" targetRef="Event_06boz42" />
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmn:message id="Message_10ypi2b" name="Message_2r6c91s">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo + customerOrderInfo.sourceSystem" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all-cancel">
      <bpmndi:BPMNEdge id="Flow_0guama2_di" bpmnElement="Flow_0guama2">
        <di:waypoint x="1800" y="339" />
        <di:waypoint x="1852" y="339" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08fn26q_di" bpmnElement="Flow_08fn26q">
        <di:waypoint x="480" y="748" />
        <di:waypoint x="480" y="792" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tn3m8a_di" bpmnElement="Flow_1tn3m8a">
        <di:waypoint x="188" y="183" />
        <di:waypoint x="230" y="183" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0cb5x12_di" bpmnElement="Event_0cb5x12">
        <dc:Bounds x="152" y="165" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="208" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ztlhdj_di" bpmnElement="Event_0ztlhdj">
        <dc:Bounds x="462" y="792" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="469" y="835" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_02ptrhy_di" bpmnElement="Activity_02ptrhy" isExpanded="true">
        <dc:Bounds x="230" y="70" width="1570" height="660" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0hgospe_di" bpmnElement="Flow_0hgospe">
        <di:waypoint x="288" y="231" />
        <di:waypoint x="336" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16nqw9b_di" bpmnElement="Flow_16nqw9b">
        <di:waypoint x="798" y="147" />
        <di:waypoint x="840" y="147" />
        <di:waypoint x="840" y="191" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wpv44o_di" bpmnElement="Flow_0wpv44o">
        <di:waypoint x="682" y="231" />
        <di:waypoint x="790" y="231" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="691" y="234" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p5x2tj_di" bpmnElement="Flow_1p5x2tj">
        <di:waypoint x="890" y="231" />
        <di:waypoint x="925" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dzibne_di" bpmnElement="Flow_0dzibne">
        <di:waypoint x="758" y="390" />
        <di:waypoint x="691" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yovb73_di" bpmnElement="Flow_0yovb73">
        <di:waypoint x="585" y="231" />
        <di:waypoint x="632" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_148qz62_di" bpmnElement="Flow_148qz62">
        <di:waypoint x="657" y="206" />
        <di:waypoint x="657" y="147" />
        <di:waypoint x="698" y="147" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="638" y="170" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sle5mu_di" bpmnElement="Flow_0sle5mu">
        <di:waypoint x="436" y="231" />
        <di:waypoint x="485" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bvgfzn_di" bpmnElement="Flow_1bvgfzn">
        <di:waypoint x="1116" y="206" />
        <di:waypoint x="1116" y="151" />
        <di:waypoint x="1164" y="151" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1120" y="176" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hudqtc_di" bpmnElement="Flow_0hudqtc">
        <di:waypoint x="1264" y="151" />
        <di:waypoint x="1314" y="151" />
        <di:waypoint x="1314" y="191" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qqsup3_di" bpmnElement="Flow_1qqsup3">
        <di:waypoint x="975" y="231" />
        <di:waypoint x="1091" y="231" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1027" y="213" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_194n3d3_di" bpmnElement="Flow_194n3d3">
        <di:waypoint x="1076" y="155" />
        <di:waypoint x="1096" y="155" />
        <di:waypoint x="1096" y="226" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_041n575_di" bpmnElement="Flow_041n575">
        <di:waypoint x="1141" y="231" />
        <di:waypoint x="1264" y="231" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1211" y="213" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d5gssr_di" bpmnElement="Flow_0d5gssr">
        <di:waypoint x="1364" y="231" />
        <di:waypoint x="1409" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_182hruf_di" bpmnElement="Flow_182hruf">
        <di:waypoint x="1434" y="206" />
        <di:waypoint x="1434" y="151" />
        <di:waypoint x="1477" y="151" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1439" y="176" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mye7zv_di" bpmnElement="Flow_1mye7zv">
        <di:waypoint x="1577" y="151" />
        <di:waypoint x="1613" y="151" />
        <di:waypoint x="1613" y="206" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11s9iqo_di" bpmnElement="Flow_11s9iqo">
        <di:waypoint x="1638" y="231" />
        <di:waypoint x="1713" y="231" />
        <di:waypoint x="1713" y="281" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i9foc2_di" bpmnElement="Flow_0i9foc2">
        <di:waypoint x="1713" y="361" />
        <di:waypoint x="1713" y="390" />
        <di:waypoint x="1638" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01knaz0_di" bpmnElement="Flow_01knaz0">
        <di:waypoint x="1613" y="415" />
        <di:waypoint x="1613" y="500" />
        <di:waypoint x="1562" y="500" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1620" y="455" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lm3kug_di" bpmnElement="Flow_0lm3kug">
        <di:waypoint x="1462" y="500" />
        <di:waypoint x="1423" y="500" />
        <di:waypoint x="1423" y="415" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k1104m_di" bpmnElement="Flow_1k1104m">
        <di:waypoint x="1398" y="390" />
        <di:waypoint x="1364" y="390" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1386" y="422" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e7m4rj_di" bpmnElement="Flow_0e7m4rj">
        <di:waypoint x="1264" y="390" />
        <di:waypoint x="1218" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x69gy8_di" bpmnElement="Flow_0x69gy8">
        <di:waypoint x="1459" y="231" />
        <di:waypoint x="1588" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_032fwq9_di" bpmnElement="Flow_032fwq9">
        <di:waypoint x="1613" y="256" />
        <di:waypoint x="1613" y="365" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1622" y="304" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e0owrg_di" bpmnElement="Flow_0e0owrg">
        <di:waypoint x="1588" y="390" />
        <di:waypoint x="1448" y="390" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1512" y="372" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0za6ra7_di" bpmnElement="Flow_0za6ra7">
        <di:waypoint x="1168" y="390" />
        <di:waypoint x="963" y="390" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1075" y="399" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hh572u_di" bpmnElement="Flow_1hh572u">
        <di:waypoint x="1193" y="415" />
        <di:waypoint x="1193" y="519" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1196" y="436" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hnbuti_di" bpmnElement="Flow_1hnbuti">
        <di:waypoint x="783" y="415" />
        <di:waypoint x="783" y="519" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="791" y="455" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q052ez_di" bpmnElement="Flow_0q052ez">
        <di:waypoint x="863" y="390" />
        <di:waypoint x="808" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11jeku6_di" bpmnElement="Flow_11jeku6">
        <di:waypoint x="950" y="206" />
        <di:waypoint x="950" y="155" />
        <di:waypoint x="976" y="155" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="957" y="174" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19dz5og_di" bpmnElement="Flow_19dz5og">
        <di:waypoint x="765" y="599" />
        <di:waypoint x="673" y="599" />
        <di:waypoint x="673" y="408" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_11b55zp_di" bpmnElement="Event_11b55zp">
        <dc:Bounds x="252" y="213" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="259" y="254" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1vinmjn_di" bpmnElement="Activity_1vinmjn">
        <dc:Bounds x="790" y="191" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_097tbpa_di" bpmnElement="Event_097tbpa">
        <dc:Bounds x="655" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="662" y="348" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0m6vomx_di" bpmnElement="Gateway_0m6vomx" isMarkerVisible="true">
        <dc:Bounds x="632" y="206" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="625" y="264" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1edj9ug_di" bpmnElement="Activity_1edj9ug">
        <dc:Bounds x="698" y="107" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1poj2j7_di" bpmnElement="Activity_1poj2j7">
        <dc:Bounds x="336" y="191" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1v4t7zs_di" bpmnElement="Activity_1v4t7zs">
        <dc:Bounds x="1164" y="111" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_16w42cy_di" bpmnElement="Gateway_16w42cy" isMarkerVisible="true">
        <dc:Bounds x="1091" y="206" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1083" y="263" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w60lrh_di" bpmnElement="Activity_1w60lrh">
        <dc:Bounds x="1264" y="191" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09m3vjm_di" bpmnElement="Activity_09m3vjm">
        <dc:Bounds x="1477" y="111" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1yrzds0_di" bpmnElement="Activity_1yrzds0">
        <dc:Bounds x="1663" y="281" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1t55l6z_di" bpmnElement="Activity_1t55l6z">
        <dc:Bounds x="1462" y="460" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0aetv8p_di" bpmnElement="Activity_0aetv8p">
        <dc:Bounds x="1264" y="350" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vn3zq3_di" bpmnElement="Gateway_1vn3zq3" isMarkerVisible="true">
        <dc:Bounds x="1409" y="206" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1404" y="263" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1mae8el_di" bpmnElement="Gateway_1mae8el" isMarkerVisible="true">
        <dc:Bounds x="1588" y="206" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1621" y="204" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18w43la_di" bpmnElement="Gateway_18w43la" isMarkerVisible="true">
        <dc:Bounds x="1588" y="365" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1583" y="422" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1baj0se_di" bpmnElement="Gateway_1baj0se" isMarkerVisible="true">
        <dc:Bounds x="1398" y="365" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1392" y="344" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qjuob2_di" bpmnElement="Activity_0qjuob2">
        <dc:Bounds x="485" y="191" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0d3vdz8_di" bpmnElement="Gateway_0d3vdz8" isMarkerVisible="true">
        <dc:Bounds x="1168" y="365" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1171" y="341" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qakb3x_di" bpmnElement="Activity_0qakb3x">
        <dc:Bounds x="863" y="350" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1sy6qgw_di" bpmnElement="Gateway_1sy6qgw" isMarkerVisible="true">
        <dc:Bounds x="758" y="365" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="751" y="341" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0auklqt_di" bpmnElement="Gateway_0auklqt" isMarkerVisible="true">
        <dc:Bounds x="925" y="206" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="939" y="263" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1dp0z7g_di" bpmnElement="Activity_1dp0z7g">
        <dc:Bounds x="976" y="115" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_15i6z34_di" bpmnElement="Activity_15i6z34" isExpanded="true">
        <dc:Bounds x="765" y="519" width="440" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_02qbhxe_di" bpmnElement="Flow_02qbhxe">
        <di:waypoint x="1083" y="579" />
        <di:waypoint x="1046" y="579" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0annran_di" bpmnElement="Flow_0annran">
        <di:waypoint x="946" y="579" />
        <di:waypoint x="901" y="579" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fvu3a2_di" bpmnElement="Flow_1fvu3a2">
        <di:waypoint x="1147" y="579" />
        <di:waypoint x="1119" y="579" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08vl6xu_di" bpmnElement="Flow_08vl6xu">
        <di:waypoint x="876" y="604" />
        <di:waypoint x="876" y="649" />
        <di:waypoint x="1101" y="649" />
        <di:waypoint x="1101" y="597" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="980" y="631" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08qv3cr_di" bpmnElement="Flow_08qv3cr">
        <di:waypoint x="851" y="579" />
        <di:waypoint x="823" y="579" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0v3lzr5_di" bpmnElement="Activity_0v3lzr5">
        <dc:Bounds x="946" y="539" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0wafm93_di" bpmnElement="Event_0wafm93">
        <dc:Bounds x="1083" y="561" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1079" y="542" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0t818k3_di" bpmnElement="Event_0t818k3">
        <dc:Bounds x="1147" y="561" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1154" y="604" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lr35sb_di" bpmnElement="Gateway_0lr35sb" isMarkerVisible="true">
        <dc:Bounds x="851" y="554" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="844" y="537" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ik8u5b_di" bpmnElement="Event_0ik8u5b">
        <dc:Bounds x="787" y="561" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="794" y="604" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_06boz42_di" bpmnElement="Event_06boz42">
        <dc:Bounds x="1852" y="321" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18ai43p_di" bpmnElement="Event_0r7ph51">
        <dc:Bounds x="462" y="712" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="438" y="755" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
