package com.midea.logistics.imp.orderverify.service.impl;

import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.midea.logistics.imp.orderverify.aop.ZeebeAop;
import com.midea.logistics.imp.orderverify.helper.LmpOrderLogHelper;
import com.midea.logistics.imp.orderverify.service.OrderZeebeService;
import com.midea.logistics.imp.orderverify.service.ZeebeLmpService;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoItemFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.helper.FlowRuleHelper;
import com.midea.logistics.otp.common.helper.IdGenHelper;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.OrderFeign;
import com.midea.logistics.otp.order.common.fegin.bop.ZeebeBopFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeRecordIncidentDto;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeRecordIncidentValueDto;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeResponeDto;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeServerRequestDto;
import com.midea.logistics.zeebe.sdk.service.ZeebeService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.MapToBeanUtil;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;

import lombok.extern.slf4j.Slf4j;

/**
 !@ZEEBE流程图相关
* @author: 陈永培
* @createtime: 2022/9/21 21:59
*/
@Component
@Slf4j
public class ZeebeLmpServiceImpl implements ZeebeLmpService {

    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private OrderFeign orderFeign;
    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    private ZeebeService zeebeService;
    @Autowired
    private OrderZeebeService orderZeebeService;
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private LmpOrderLogHelper lmpOrderLogHelper;
    @Autowired
        private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;
    @Autowired
    private ZeebeBopFeign zeebeBopFeign;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private ZeebeAop zeebeAop;

    //父单流程id
    private static final String CUSTOMER_ORDER_FLOW_ID = "customer-order-process-all";
    private static final String CUSTOMER_ORDER_FLOW_ID_OFC = "customer-order-process-all-ofc";
    private static final String CUSTOMER_ORDER_FLOW_ID_PLAN_ORDER = "customer-order-process-all-plan";
    private static final String CUSTOMER_ORDER_FLOW_ID_YS_ORDER = "customer-order-process-ys";
    private static final String CUSTOMER_ORDER_FLOW_ID_TMALL = "customer-order-process-all-tmall";
    private static final String CUSTOMER_ORDER_FLOW_ID_GSC = "customer-order-process-gsc";
    private static final String CUSTOMER_ORDER_FLOW_ID_WMS_TI = "customer-order-process-wms-ti";
    private static final String CUSTOMER_ORDER_FLOW_ID_QP2C = "customer-order-process-all-qp2c";
    private static final String CUSTOMER_ORDER_FLOW_ID_TOMS = "customer-order-process-all-toms";
    private static final String CUSTOMER_ORDER_FLOW_ID_WFR = "customer-order-process-all-wfr";
    private static final String CUSTOMER_ORDER_FLOW_ID_O2O_EXPRESS = "customer-order-process-all-o2o-express";

    //父单兼容取消流程
    private static final String CUSTOMER_ORDER_CANCEL_FLOW_ID = "customer-order-process-all-cancel";
    //子单流程
    private static final String SUB_ORDER_FLOW_ID = "Process_0ul4aqd";
    private static final String SUB_ORDER_FLOW_ID_OFC = "Process_0ul4aqd-ofc";
    private static final String SUB_ORDER_FLOW_ID_PLAN_ORDER = "Process_0ul4aqd-plan";
    private static final String SUB_ORDER_FLOW_ID_YS_ORDER = "order-info-process-ys";
    private static final String SUB_ORDER_FLOW_ID_TMALL = "Process_0ul4aqd-tmall";
    private static final String SUB_ORDER_FLOW_ID_GSC = "Process_0ul4aqd-gsc";
    private static final String SUB_ORDER_FLOW_ID_WMS_TI = "Process_0ul4aqd-wms-ti";
    private static final String SUB_ORDER_FLOW_ID_TOMS = "Process_0ul4aqd-toms";
    private static final String SUB_ORDER_FLOW_ID_WFR = "order-info-process-wfr";
    private static final String SUB_ORDER_FLOW_ID_SCC = "Process_0ul4aqd-scc";
    private static final String SUB_ORDER_FLOW_ID_O2O_EXPRESS = "Process_0ul4aqd-o2o-express";

    //手工分仓触发事件
    private static final String SEPARATE_WAREHOUSE_MESSAGE_NAME = "Message_0hcjpga";
    //取消触发事件
    private static final String CUSTOMER_ORDER_CANCEL_MESSAGE_NAME = "Message_2r6c91s";

    //压测流程id
    private static final String TEST_FLOW_ID = "customer-order-process-all-test";

    private static final String KEY_ZEEBE_CUSTOMER = "CUSTOMERORDERVERIFY:ZEEBE";
    private static final String KEY_ZEEBE_SUB = "ORDERVERIFY:ZEEBE";


    /**
     * 触发父单流程
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse customerOrderVerifyZeebe(String orderNo) {
        String key = KEY_ZEEBE_CUSTOMER + orderNo;
        try {
            boolean lock = redisLockHelper.tryLock(key, 30L);
            if (!lock) {
                throw BusinessException.fail("触发父单流程过于频繁，请稍后再试！");
            }

            JsonResponse<CustomerOrderInfoExt> jsonResponseCustomerOrder = customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(orderNo);
            CustomerOrderInfoExt customerOrderInfoExt = jsonResponseCustomerOrder.data;

            if(customerOrderInfoExt.getExcuteStatus().compareTo(ExcuteStatus.AUDITED.getKey())>=0){
                throw BusinessException.fail("触发父订单流程失败,父单已审核完成");
            }

            if (null == customerOrderInfoExt) {
                throw BusinessException.fail("触发父订单流程失败,订单信息为空");
            }

            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("customerOrderInfo", MapToBeanUtil.convertBean2Map(customerOrderInfoExt));
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID);

            // 20240612 楼宇走OFC模板
            if (Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.OFC.getKey()) || Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.SCC.getKey())
                    || SourceSystem.isMSS(customerOrderInfoExt.getSourceSystem()) || SourceSystem.isMssStc(customerOrderInfoExt.getSourceSystem())) {
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_OFC);
            }

            //2022年6月8日11:19:19 李娟：暂时废弃天猫流程，改成走奇门IN1流程
            //if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.TMALL.getKey())){
            //    zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_TMALL);
            //}

            if (CommonEnum.YES.getValue().equals(customerOrderInfoExt.getPlanOrderFlag())){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_PLAN_ORDER);
            }

            if (SourceSystem.XiaoMi.getKey().equals(customerOrderInfoExt.getSourceSystem())){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_YS_ORDER);
            }

            if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.MRP.getKey())){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_TMALL);
            }

            if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.GSC.getKey())){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_GSC);
            }

            if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.WMS.getKey()) && Objects.equals(customerOrderInfoExt.getOrderType(), OrderType.TI.getKey())){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_WMS_TI);
            }

            if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.AWMS.getKey())
                && Arrays.asList(OrderType.ADI.getKey(), OrderType.ADO.getKey()).contains(customerOrderInfoExt.getOrderType())){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_WMS_TI);
            }

            if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.QP2C_WBKH.getKey())&& OrderType.isPOOrder(customerOrderInfoExt.getOrderType())){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_QP2C);
            }

            if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.BLUEMOON.getKey())){
                if (StringUtils.isNotEmpty(customerOrderInfoExt.getOrderNo()) && orderverifyHelper.isBlueMoonIdentifyMark(customerOrderInfoExt.getOrderNo())){
                    zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_QP2C);
                }
            }

            if(Objects.equals(customerOrderInfoExt.getSourceSystem(), SourceSystem.COP_DIO.getKey())
                          || (SourceSystem.isHandleSo(customerOrderInfoExt.getSourceSystem(), customerOrderInfoExt.getOrderType()))){
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_TOMS);
            }
            log.info("{}订单触发流程:{}", orderNo, zeebeRequestDto.getBpmnProcessId());

            
            if (BusinessHelper.isWFROrder(customerOrderInfoExt) || BusinessHelper.isSHTOrder(customerOrderInfoExt) || BusinessHelper.isCloudSHTOrder(customerOrderInfoExt)
            || BusinessHelper.isMssSHTOrder(customerOrderInfoExt)) {
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_WFR);
            }

            if (SourceSystem.isMRP(customerOrderInfoExt.getSourceSystem()) && ProjectClassifyEnum.isO2oExpress(customerOrderInfoExt.getProjectClassify())) {
                zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_FLOW_ID_O2O_EXPRESS);
            }

            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> instance = zeebeService.createInstance(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(instance.getCode())) {
                String msg = ToolUtils.isEmpty(instance.getErrMsg()) ? instance.getMsg() : instance.getErrMsg();
                throw BusinessException.fail("触发失败: " +msg);
            }
            orderZeebeService.saveWorkflowInstanceKey(orderNo, instance.getData());
            return instance;
        } catch (Exception e) {
            log.error("触发父订单流程失败：", e);
            throw BusinessException.fail("触发父订单流程失败：" + e.getMessage());
        } finally {
            redisLockHelper.unLock(key);
        }
    }


    /**
     * 触发父单流程带模板id
     *
     * @param zeebeRequestDto
     * @return
     */
    @Override
    public JsonResponse customerOrderVerifyZeebe(ZeebeServerRequestDto zeebeRequestDto) {
        String orderNo = zeebeRequestDto.getCorrelationKey();
        String key = KEY_ZEEBE_CUSTOMER + orderNo;
        try {
            boolean lock = redisLockHelper.tryLock(key, 30L);
            if (!lock) {
                throw BusinessException.fail("触发父单流程过于频繁，请稍后再试！");
            }

            JsonResponse<CustomerOrderInfoExt> jsonResponseCustomerOrder = customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(orderNo);
            CustomerOrderInfoExt customerOrderInfoExt = jsonResponseCustomerOrder.data;

            if (null == customerOrderInfoExt) {
                throw BusinessException.fail("触发父订单流程失败,订单信息为空");
            }

            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("customerOrderInfo", MapToBeanUtil.convertBean2Map(customerOrderInfoExt));
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> instance = zeebeService.createInstance(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(instance.getCode())) {
                throw BusinessException.fail("触发失败: " + instance.getErrMsg());
            }
            orderZeebeService.saveWorkflowInstanceKey(orderNo, instance.getData());
            return instance;
        } catch (Exception e) {
            throw BusinessException.fail("触发父订单流程失败：" + e.getMessage());
        } finally {
            redisLockHelper.unLock(key);
        }
    }


    /**
     * 触发父单流程
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse customerOrderVerifyZeebeCancel(String orderNo) {
        String key = KEY_ZEEBE_CUSTOMER + orderNo;
        try {
//            jsonResponse.data = zeebeCustomerOrderProducer.sent(orderNo);
            boolean lock = redisLockHelper.tryLock(key, 30L);
            if (!lock) {
                throw BusinessException.fail("触发父单流程过于频繁，请稍后再试！");
            }

            JsonResponse<CustomerOrderInfoExt> jsonResponseCustomerOrder = customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(orderNo);
            CustomerOrderInfoExt customerOrderInfoExt = jsonResponseCustomerOrder.data;

            if (null == customerOrderInfoExt) {
                throw BusinessException.fail("触发父订单流程失败,订单信息为空");
            }

            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("customerOrderInfo", MapToBeanUtil.convertBean2Map(customerOrderInfoExt));
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            zeebeRequestDto.setBpmnProcessId(CUSTOMER_ORDER_CANCEL_FLOW_ID);
            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> instance = zeebeService.createInstance(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(instance.getCode())) {
                throw BusinessException.fail("触发失败: " + instance.getErrMsg());
            }
            orderZeebeService.saveWorkflowInstanceKey(orderNo, instance.getData());
            return instance;
        } catch (Exception e) {
            throw BusinessException.fail("触发父订单流程失败：" + e.getMessage());
        } finally {
            redisLockHelper.unLock(key);
        }
    }


    /**
     * 触发子单流程
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse orderVerifyZeebe(String orderNo) {
        String key = KEY_ZEEBE_SUB + orderNo;
        try {
            boolean lock = redisLockHelper.tryLock(key, 30L);
            if (!lock) {
                throw BusinessException.fail("触发子单流程过于频繁，请稍后再试！");
            }

            JsonResponse<OrderInfo> jsonResponse1 = orderInfoFeign.getOrderInfoByOrderNo(orderNo);
            OrderInfo orderInfo = jsonResponse1.data;

            if(orderInfo.getExcuteStatus().compareTo(ExcuteStatus.AUDITED.getKey())>=0){
                throw BusinessException.fail("触发子订单流程失败,子单已审核完成");
            }

            if (null == orderInfo) {
                throw BusinessException.fail("触发子订单流程失败,订单信息为空");
            }

            JsonResponse jsonResponseOrder = orderInfoItemFeign.getOrderItem(orderNo);
            List<OrderInfoItem> orderInfoItems = (List<OrderInfoItem>) jsonResponseOrder.data;

            OrderInfoExt orderInfoExt = new OrderInfoExt();
            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);

            boolean isExtend = Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.OFC.getKey())
                    || SourceSystem.isCIMS(orderInfoExt.getSourceSystem())
                    || SourceSystem.isHANDLE(orderInfoExt.getSourceSystem())
                    || SourceSystem.isMSS(orderInfoExt.getSourceSystem()) || SourceSystem.isMssStc(orderInfoExt.getSourceSystem())
                    || SourceSystem.isCOPDIO(orderInfoExt.getSourceSystem());
            if(isExtend){
                //查询订单扩展表
                CustomerOrderInfoExtend customerOrderInfoExtend = new CustomerOrderInfoExtend();
                customerOrderInfoExtend.setOrderNo(orderInfoExt.getParentOrderNo());
                JsonResponse<CustomerOrderInfoExtend> jsonResponse = customerOrderInfoExtendFeign.selectOne(customerOrderInfoExtend);
                customerOrderInfoExtend = jsonResponse.data;
                if (null == customerOrderInfoExtend) {
                    throw BusinessException.fail("订单扩展表信息为空");
                }
                orderInfoExt.setOrderDistinctionFlag(customerOrderInfoExtend.getOrderDistinctionFlag());
            }

            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("orderInfo", MapToBeanUtil.convertBean2Map(orderInfoExt));
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID);

            if (Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.OFC.getKey())
                    || SourceSystem.isMSS(orderInfoExt.getSourceSystem()) || SourceSystem.isMssStc(orderInfoExt.getSourceSystem())) {
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_OFC);
            }

            if (Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.SCC.getKey())){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_SCC);
            }

            //2022年6月8日11:19:19 李娟：暂时废弃天猫流程，改成走奇门IN1流程
            //if(Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.TMALL.getKey())){
            //    zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_TMALL);
            //}

            if (CommonEnum.YES.getValue().equals(orderInfo.getPlanOrderFlag())){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_PLAN_ORDER);
            }

            if (SourceSystem.XiaoMi.getKey().equals(orderInfo.getSourceSystem())){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_YS_ORDER);
            }

            if(Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.MRP.getKey())){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_TMALL);
            }

            if(Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.GSC.getKey())){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_GSC);
            }

            if(Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.WMS.getKey()) && Objects.equals(orderInfoExt.getOrderType(), OrderType.TI.getKey())){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_WMS_TI);
            }

            if(Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.COP_DIO.getKey())
                    || (SourceSystem.isHandleSo(orderInfoExt.getSourceSystem(), orderInfoExt.getOrderType()))){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_TOMS);
            }
    
            if (BusinessHelper.isWFROrder(orderInfo)) {
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_WFR);
            }

            if(Objects.equals(orderInfoExt.getSourceSystem(), SourceSystem.AWMS.getKey())
                && Arrays.asList(OrderType.ADI.getKey(), OrderType.ADO.getKey()).contains(orderInfoExt.getOrderType())){
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_WMS_TI);
            }
            log.info("{}订单触发流程:{}", orderNo, zeebeRequestDto.getBpmnProcessId());

            if(SourceSystem.isMRP(orderInfoExt.getSourceSystem()) && ProjectClassifyEnum.isO2oExpress(orderInfoExt.getProjectClassify())) {
                zeebeRequestDto.setBpmnProcessId(SUB_ORDER_FLOW_ID_O2O_EXPRESS);
            }

            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> instance = zeebeService.createInstance(zeebeRequestDto);
            orderZeebeService.saveWorkflowInstanceKey(orderNo, instance.getData());
            return instance;
        } catch (Exception e) {
            throw BusinessException.fail("触发子订单流程失败：" + e.getMessage());
        } finally {
            redisLockHelper.unLock(key);
        }
    }


    /**
     * 触发子单流程带模板id
     *
     * @param zeebeRequestDto
     * @return
     */
    @Override
    public JsonResponse orderVerifyZeebe(ZeebeServerRequestDto zeebeRequestDto) {
        String orderNo = zeebeRequestDto.getCorrelationKey();
        String key = KEY_ZEEBE_SUB + orderNo;
        try {
            boolean lock = redisLockHelper.tryLock(key, 30L);
            if (!lock) {
                throw BusinessException.fail("触发子单流程过于频繁，请稍后再试！");
            }

            JsonResponse<OrderInfo> jsonResponse1 = orderInfoFeign.getOrderInfoByOrderNo(orderNo);
            OrderInfo orderInfo = jsonResponse1.data;

            if (null == orderInfo) {
                throw BusinessException.fail("触发子订单流程失败,订单信息为空");
            }

            JsonResponse jsonResponseOrder = orderInfoItemFeign.getOrderItem(orderNo);
            List<OrderInfoItem> orderInfoItems = (List<OrderInfoItem>) jsonResponseOrder.data;

            OrderInfoExt orderInfoExt = new OrderInfoExt();
            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);
            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("orderInfo", MapToBeanUtil.convertBean2Map(orderInfoExt));
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> instance = zeebeService.createInstance(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(instance.getCode())) {
                throw BusinessException.fail("触发失败: " + instance.getErrMsg());
            }
            orderZeebeService.saveWorkflowInstanceKey(orderNo, instance.getData());
            return instance;
        } catch (Exception e) {
            throw BusinessException.fail("触发子订单流程失败：" + e.getMessage());
        } finally {
            redisLockHelper.unLock(key);
        }
    }


    /**
     * 手工分仓触发父流程结束
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse zeebeSeparateWarehouse(String orderNo) {
        try {

            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("orderNo", orderNo);
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            zeebeRequestDto.setMessageName(SEPARATE_WAREHOUSE_MESSAGE_NAME);
            zeebeRequestDto.setCorrelationKey(orderNo);
            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> message = zeebeService.publishMessage(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(message.getCode())) {
                throw BusinessException.fail("触发失败: " + message.getErrMsg());
            }
            return message;
        } catch (Exception e) {
            throw BusinessException.fail("触发子订单流程失败：" + e.getMessage());
        }
    }


    /**
     * 父单取消
     *
     * @param key
     * @return
     */
    @Override
    public JsonResponse zeebeCustomerOrderCancel(String key) {
        try {

            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("key", key);
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            zeebeRequestDto.setMessageName(CUSTOMER_ORDER_CANCEL_MESSAGE_NAME);
            zeebeRequestDto.setCorrelationKey(key);
            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> message = zeebeService.publishMessage(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(message.getCode())) {
                throw BusinessException.fail("触发失败: " + message.getErrMsg());
            }
            return message;
        } catch (Exception e) {
            throw BusinessException.fail("触发取消订单流程失败：" + e.getMessage());
        }
    }


    /**
     * zeebe异常流程重试
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse zeebeRetry(String orderNo) {
        try {

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            Long instanceKey = orderZeebeService.getWorkflowInstanceKey(orderNo);
            zeebeRequestDto.setProcessInstanceKey(instanceKey);

            //重置流程参数
            setVariables(orderNo, instanceKey);
            //流程重试
            JsonResponse<ZeebeResponeDto> incident = zeebeService.resolveIncident(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(incident.getCode())) {
                throw BusinessException.fail("触发失败: " + incident.getErrMsg());
            }
            return incident;
        } catch (Exception e) {
            throw BusinessException.fail("触发订单流程失败：" + e.getMessage());
        }
    }


    /**
     * 取消zeebe实例
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse zeebeCancelInstance(String orderNo) {
        try {

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            Long instanceKey = orderZeebeService.getWorkflowInstanceKey(orderNo);
            zeebeRequestDto.setProcessInstanceKey(instanceKey);
            zeebeRequestDto.setElementInstanceKey(instanceKey);
            JsonResponse<ZeebeResponeDto> instance = zeebeService.cancelInstance(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(instance.getCode())) {
                throw BusinessException.fail("触发失败: " + instance.getErrMsg());
            }
            return instance;
        } catch (Exception e) {
            throw BusinessException.fail("触发取消zeebe实例失败：" + e.getMessage());
        }
    }


    /**
     * 重置线程变量
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse setVariables(String orderNo, Long key) {

        ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
        zeebeRequestDto.setProcessInstanceKey(key);
        zeebeRequestDto.setElementInstanceKey(key);
        Map<String, Object> paramMap = new HashMap<>();
        if(FlowRuleHelper.isZeebeSubFlow(orderNo)){

            JsonResponse<OrderInfo> jsonResponse1 = orderInfoFeign.getOrderInfoByOrderNo(orderNo);
            OrderInfo orderInfo = jsonResponse1.data;

            if (null == orderInfo) {
                throw BusinessException.fail("触发子订单流程失败,订单信息为空");
            }

            JsonResponse jsonResponseOrder = orderInfoItemFeign.getOrderItem(orderNo);
            List<OrderInfoItem> orderInfoItems = (List<OrderInfoItem>) jsonResponseOrder.data;

            OrderInfoExt orderInfoExt = new OrderInfoExt();
            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);
            try {
                paramMap.put("orderInfo", MapToBeanUtil.convertBean2Map(orderInfoExt));
                paramMap.put("parameterBody", MapToBeanUtil.convertBean2Map(orderInfoExt));
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }
        }
        if(orderNo.startsWith(IdGenHelper.PARENT_PREFIX_LMP)){
            JsonResponse<CustomerOrderInfoExt> jsonResponseCustomerOrder = customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(orderNo);
            CustomerOrderInfoExt customerOrderInfoExt = jsonResponseCustomerOrder.data;

            if (null == customerOrderInfoExt) {
                throw BusinessException.fail("触发父订单流程失败,订单信息为空");
            }

            try {
                paramMap.put("customerOrderInfo", MapToBeanUtil.convertBean2Map(customerOrderInfoExt));
                paramMap.put("parameterBody", MapToBeanUtil.convertBean2Map(customerOrderInfoExt));
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }
        }

        //查询异常元素id
        JsonResponse<List<ZeebeRecordIncidentDto>> incident = zeebeService.searchIncident(zeebeRequestDto);
        if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(incident.getCode())) {
            throw BusinessException.fail("触发失败: " + incident.getErrMsg());
        }
        List<ZeebeRecordIncidentDto> data = incident.getData();
        if(CollectionUtils.isNotEmpty(data)) {
            ZeebeRecordIncidentValueDto value = data.get(0).getValue();
            Long elementInstanceKey = value.getElementInstanceKey();
            zeebeRequestDto.setElementInstanceKey(elementInstanceKey);
        }
        zeebeRequestDto.setFormParam(paramMap);
        JsonResponse<ZeebeResponeDto> variables = zeebeService.setVariables(zeebeRequestDto);
        if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(variables.getCode())) {
            throw BusinessException.fail("触发失败: " + variables.getErrMsg());
        }
        return JsonResponse.success(zeebeRequestDto);
    }


    /**
     * 重置线程变量
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse resetZeebeParamter(String orderNo) {
        try {

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            Long instanceKey = orderZeebeService.getWorkflowInstanceKey(orderNo);
            zeebeRequestDto.setProcessInstanceKey(instanceKey);

            return setVariables(orderNo, instanceKey);
        } catch (Exception e) {
            throw BusinessException.fail("触发订单流程失败：" + e.getMessage());
        }
    }

    /**
     * 查询异常元素id
     *
     * @param instanceKey
     * @return
     */
    @Override
    public JsonResponse searchIncident(Long instanceKey) {
        try {

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            zeebeRequestDto.setProcessInstanceKey(instanceKey);
            JsonResponse<List<ZeebeRecordIncidentDto>> incident = zeebeService.searchIncident(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(incident.getCode())) {
                throw BusinessException.fail("触发失败: " + incident.getErrMsg());
            }
            return incident;
        } catch (Exception e) {
            throw BusinessException.fail("查询异常元素id失败：" + e.getMessage());
        }
    }


    /**
     * 解锁父单流程
     */
    @Override
    public boolean unLockCustomerOrder(String orderNo){
        String key = KEY_ZEEBE_CUSTOMER + orderNo;
        return redisLockHelper.unLock(key);
    }

    /**
     * 解锁子单流程
     */
    @Override
    public boolean unLockSubOrder(String orderNo){
        String key = KEY_ZEEBE_SUB + orderNo;
        return redisLockHelper.unLock(key);
    }


    /**
     * 判断是否已经创建流程再启动流程
     * @param orderNo
     * @param isParentOrder
     * @return
     */
    @Override
    public int trigger(String orderNo, boolean isParentOrder){
        ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
        try {
            Long instanceKey = orderZeebeService.getWorkflowInstanceKey(orderNo);
            zeebeRequestDto.setProcessInstanceKey(instanceKey);
        } catch (Exception e){
            // 新起zeebe线程
            if (isParentOrder) {
                customerOrderVerifyZeebe(orderNo);
            } else {
                orderVerifyZeebe(orderNo);
            }
        }

        if(null != zeebeRequestDto.getProcessInstanceKey()) {
            try {
                //查询流程是否结束，结果不为空则不再触发
                ZeebeServerRequestDto zeebeServerRequestDto = new ZeebeServerRequestDto();
                zeebeServerRequestDto.setProcessInstanceKey(zeebeRequestDto.getProcessInstanceKey());
                JsonResponse<List<ZeebeRecordIncidentDto>> response = zeebeService.searchWorkFlowEndInstant(zeebeServerRequestDto);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
                    throw BusinessException.fail(response.getMsg());
                }
                if (null != response.getData() && response.getData().size()>0) {
                    return 0;
                }

                //重置流程参数
                setVariables(orderNo, zeebeRequestDto.getProcessInstanceKey());
                zeebeService.resolveIncident(zeebeRequestDto);
            } catch (Exception e) {
//                //查询集团或安得订单zeebe流程
//                JsonResponse jsonResponse = zeebeBopFeign.searchIncident(zeebeRequestDto);
//                if(null != jsonResponse && null != jsonResponse.getData() && ToolUtils.isNotEmpty(jsonResponse.data)){
//                    List<ZeebeRecordIncidentDto> incident = JSON.parseArray(JSON.toJSONString(jsonResponse.data), ZeebeRecordIncidentDto.class);
//                    // 触发集团或安得审核
//                    if(ToolUtils.isNotEmpty(incident)) {
//                        zeebeBopFeign.orderverify(Lists.newArrayList(orderNo));
//                        return 0;
//                    }
//                }

                String join = String.format("订单 %s 流程 %s 已结束或者不存在：%s", orderNo, zeebeRequestDto.getProcessInstanceKey(), e.getMessage());
                log.error(join);
                // 记录日志
                FlowListenerParam param = new FlowListenerParam();
                param.setFlowStatus(FlowStatus.FAILD);
                param.setErrorMsg(join);
                if (isParentOrder) {
                    JsonResponse<CustomerOrderInfo> info = customerOrderInfoFeign.getOrderInfoByOrderNos(orderNo);
                    //2024年7月3日17:28:48 永培：由于取消和审核同时触发了，
                    //取消后会终止zeebe流程，终止完，订单映射也刚跑完，然后更新了父单状态为审核中，
                    //等待下一次触发的时候，流程已经终止了，切面那里无法判断已经取消了，无法更新父单为取消了
                    zeebeAop.checkCancelLock(info.getData(),null);
                    lmpOrderLogHelper.createOrderLog(info.getData(), param, OrderOperateType.FLOW_TRIGGER.name());
                } else {
                    JsonResponse<List<OrderInfo>> list = orderFeign.listByOrderNos(Arrays.asList(orderNo));
                    if (ToolUtils.isNotEmpty(list.getData())) {
                        OrderInfo orderInfo = list.getData().get(0);
                        //2024年7月3日17:28:48 永培：由于取消和审核同时触发了，
                        //取消后会终止zeebe流程，终止完，订单映射也刚跑完，然后更新了父单状态为审核中，
                        //等待下一次触发的时候，流程已经终止了，切面那里无法判断已经取消了，无法更新父单为取消了
                        zeebeAop.checkCancelLock(null,orderInfo);
                        lmpOrderLogHelper.createOrderLog(orderInfo, param, OrderOperateType.FLOW_TRIGGER.name());
                    }
                }
                throw BusinessException.fail(join);
            }
        }
        return 1;

    }



    /**
     * 触发压测流程
     *
     * @param orderNo
     * @return
     */
    @Override
    public JsonResponse verifyZeebeTest(String orderNo) {
        try {

            JsonResponse<CustomerOrderInfoExt> jsonResponseCustomerOrder = customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(orderNo);
            CustomerOrderInfoExt customerOrderInfoExt = jsonResponseCustomerOrder.data;

            Map<String, Object> paramMap = new HashMap<>();
            try {
                paramMap.put("customerOrderInfo", customerOrderInfoExt);
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage());
            }

            ZeebeServerRequestDto zeebeRequestDto = new ZeebeServerRequestDto();
            long nowMillis = System.currentTimeMillis();
            zeebeRequestDto.setBpmnProcessId(TEST_FLOW_ID + "_" + 0%5);
            zeebeRequestDto.setFormParam(paramMap);
            JsonResponse<ZeebeResponeDto> instance = zeebeService.createInstance(zeebeRequestDto);
            if (!BaseCodeEnum.SUCCESS.getCode().equalsIgnoreCase(instance.getCode())) {
                throw BusinessException.fail("触发失败: " + instance.getErrMsg());
            }
            return instance;
        } catch (Exception e) {
            throw BusinessException.fail("触发压测流程失败：" + e.getMessage());
        }
    }

}
