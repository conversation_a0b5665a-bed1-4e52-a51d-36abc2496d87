package com.midea.logistics.imp.orderverify.service.impl;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.CargoRightTransferService;
import com.midea.logistics.otp.bean.TransferVerifyDto;
import com.midea.logistics.otp.bean.ValidateSameWhDto;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.orderAgg.OrderAggFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.helper.FlowRuleHelper;
import com.midea.logistics.otp.common.helper.IdGenHelper;
import com.midea.logistics.otp.common.utils.ControlParamHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.common.service.CenterInvService;
import com.midea.logistics.otp.order.common.service.RelationOrderService;
import com.midea.logistics.otp.order.common.service.SeparateWarehouseService;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import static com.midea.logistics.otp.enums.UpperOrderType.MRP_FI;
import static com.midea.logistics.otp.enums.UpperOrderType.MRP_HI;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * Author:   zhangjh
 * Date:     2019-06-25 10:51
 * Description: 货权转移
 */
@Slf4j
@Component
public class CargoRightTransferServiceImpl implements CargoRightTransferService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CargoRightTransferServiceImpl.class);


    @Autowired
    private CenterInvService centerInvService;

    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;

    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;

    @Autowired
    private OmsSuContractFegin omsSuContractFegin;

    @Autowired
    private CdWarehouseManager cdWarehouseManager;

    @Autowired
    private SeparateWarehouseService separateWarehouseService;
    @Autowired
    private RelationOrderService relationOrderService;
    @Autowired
    private IdGenHelper idGenHelper;

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private FlowRuleHelper flowRuleHelper;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private ControlParamHelper controlParamHelper;
    @Autowired
    private TransRelationVerifyHelper transRelationVerifyHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private MDMHelper mdmHelper;
    @Autowired
    private CenterInvHelper centerInvHelper;
    @Autowired
    private WfrTransferHelper wfrTransferHelper;
    @Autowired
    private OrderAggFeign orderAggFeign;
    @Autowired
    private FlowOutOrderAutoTransferHelper flowAutoTransferHelper;
    
    /**
      !@货权转移校验 - 入口（中台）
     * @param customerOrderInfo
     * @return
     */
    @Override
    @ZeebeFlow("CARGO_RIGHT_TRANSFER")
    public CustomerOrderInfoExt cargoRightTransfer(CustomerOrderInfoExt customerOrderInfo) {
        //customerOrderInfo和orderInfo订单状态order_status设置为审核  --->  走到分仓拆单前，会自动变成审核成功状态 by wangkc4
        // customerOrderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
        //OFC+PO+云仓才走货权转移校验,走调剂的流程
//        boolean isOfcPoFlag = SourceSystem.isOFC(customerOrderInfo.getSourceSystem()) && OrderType.isPOOrder(customerOrderInfo.getOrderType());
//        if (isOfcPoFlag && !CommonEnum.Y.getKey().equals(customerOrderInfo.getCloudWhFlag())) {
//            FlowListenerParam.success("美云销销售出库单非云仓,不做货权转移");
//            return customerOrderInfo;
//        }

        if (SourceSystem.isMRP(customerOrderInfo.getSourceSystem())) {
            if (OrderType.isPOOrder(customerOrderInfo.getOrderType()) && !CommonConstant.YO.equals(customerOrderInfo.getUpperOrderType()) && !JoinType.isOnlyShare(customerOrderInfo.getJoinType())) {
                FlowListenerParam.success("零售销售出库单非调剂,不做货权转移");
                return customerOrderInfo;
            }
            if (OrderType.isAIOrder(customerOrderInfo.getOrderType())
                && !(CommonEnum.Y.getKey().equals(customerOrderInfo.getCloudWhFlag()) && mrpUpperOrderType(customerOrderInfo.getUpperOrderType()))) {
                FlowListenerParam.success("零售调拨入库单非云仓,不做货权转移");
                return customerOrderInfo;
            }

        }

        //设置货权转移
        String joinTypeSource = customerOrderInfo.getJoinType();
        if (ApartType.HANDLE.getKey().equals(customerOrderInfo.getApartType()) && ToolUtils.isNotEmpty(customerOrderInfo.getApartStatus())) {
            throw BusinessException.fail("库存不足跳过货权转移，请手工拆单");
        }
        //库存调剂-CIMS&电商货权转移单
        if (JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType()) || JoinType.SHARE1300.getKey().equals(customerOrderInfo.getJoinType())) {
            return inventoryAdjustment(customerOrderInfo ,customerOrderInfo.getJoinType());
        }
        //关联单
        CustomerOrderInfo relationCustomerOrderInfo = new CustomerOrderInfo();
        Boolean upperRelationOrderFlag = null;
        //财务单  或者  视图建单的JDPI||PI,CCS的JDPI
        if (OrderSource.FINANCE.getKey().equals(customerOrderInfo.getOrderSource()) || (OrderSource.VIEW.getKey().equals(customerOrderInfo.getOrderSource())
            && Lists.newArrayList(OrderType.JDPI.getKey(), OrderType.PI.getKey()).contains(customerOrderInfo.getOrderType()))
            || (SourceSystem.CCS.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.JDPI.getKey().equals(customerOrderInfo.getOrderType()))
            || (SourceSystem.CIMS.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.AI.getKey().equals(customerOrderInfo.getOrderType()))
            || (SourceSystem.CCS.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.PI.getKey().equals(customerOrderInfo.getOrderType()))
            || (SourceSystem.OFC.getKey().equals(customerOrderInfo.getSourceSystem()) && (OrderType.PI.getKey().equals(customerOrderInfo.getOrderType()) || OrderType.AI.getKey().equals(customerOrderInfo.getOrderType())))
            || (SourceSystem.PDD.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.PI.getKey().equals(customerOrderInfo.getOrderType()) && ProjectClassifyEnum.TRANS_INV.getKey().equals(customerOrderInfo.getProjectClassify()))
            || (SourceSystem.MRP.getKey().equals(customerOrderInfo.getSourceSystem()) && (OrderType.PI.getKey().equals(customerOrderInfo.getOrderType()) || mrpUpperOrderType(customerOrderInfo.getUpperOrderType())))
            //2024年12月9日19:53:05 丽红 新增MSS https://cf.annto.com/pages/viewpage.action?pageId=44889212
            || (SourceSystem.MSS.getKey().equals(customerOrderInfo.getSourceSystem()) && Lists.newArrayList(OrderType.PI.getKey(), OrderType.AI.getKey(), OrderType.RI.getKey()).contains(customerOrderInfo.getOrderType()))
            //2025年1月7日16:50:55 丽红 新增MSS-STC https://cf.annto.com/pages/viewpage.action?pageId=70101029
            //这里去掉 PI ,由模版去做控制就行，写太死了不利于拓展
            || (SourceSystem.MSS_STC.getKey().equals(customerOrderInfo.getSourceSystem())/* && Lists.newArrayList(OrderType.PI.getKey()).contains(customerOrderInfo.getOrderType())*/)
        ) {

            upperRelationOrderFlag = checkRelationOrder(customerOrderInfo, relationCustomerOrderInfo);//是否关联单
        }

        if (!SourceSystem.CIMS.getKey().equals(customerOrderInfo.getSourceSystem())
            && !SourceSystem.OFC.getKey().equals(customerOrderInfo.getSourceSystem())
            && !SourceSystem.MRP.getKey().equals(customerOrderInfo.getSourceSystem())
            ////2024年12月9日19:53:05 丽红 新增MSS https://cf.annto.com/pages/viewpage.action?pageId=44889212
            && !SourceSystem.MSS.getKey().equals(customerOrderInfo.getSourceSystem())
            //2025年1月7日16:50:55 丽红 新增MSS-STC https://cf.annto.com/pages/viewpage.action?pageId=70101029
            && !SourceSystem.MSS_STC.getKey().equals(customerOrderInfo.getSourceSystem())
            && !SourceSystem.PDD.getKey().equals(customerOrderInfo.getSourceSystem())
            && !(SourceSystem.CCS.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.PO.getKey().equals(customerOrderInfo.getOrderType()) && BusinessMode.B2B.getName().equals(customerOrderInfo.getBusinessMode()))
        ) {
            if (upperRelationOrderFlag == null) {
                FlowListenerParam.success("非CIMS订单，不做货权转移");
                return customerOrderInfo;
            }
            if (!upperRelationOrderFlag) {
                FlowListenerParam.success("关联单不是货权转移，不做货权转移");
                return customerOrderInfo;
            }
        }

        if (OrderType.DO.getKey().equals(customerOrderInfo.getOrderType())) {
            FlowListenerParam.success("CIMS分拨订单，不做货权转移");
            return customerOrderInfo;
        }
    
        
        
        if (InOutType.IN.getName().equals(customerOrderInfo.getInOutType())) {
            if (upperRelationOrderFlag == null) {
                FlowListenerParam.success("未查询到关联单，不做货权转移");
                return customerOrderInfo;
            }
            if (!upperRelationOrderFlag) {
                FlowListenerParam.success("关联单不是货权转移，不做货权转移");
                return customerOrderInfo;
            }
            //入库判断为货权转移时，需校验出库单是否关闭，若关联出库单是关闭状态（excute_status=关闭）的，则报错，卡单：此单关联出库单，已关闭，无法进行货权转移；
            if (ExcuteStatus.CLOSED.getKey().equals(relationCustomerOrderInfo.getOrderStatus())) {
                throw BusinessException.fail("此单关联出库单，已关闭，无法进行货权转移");
            }

            //关联单平台
            String relationOrderSiteCode = relationCustomerOrderInfo.getSiteCode();
            //发货平台
            String siteCode = customerOrderInfo.getSiteCode();
            //入库单校验关联单是否货权转移的时候，要判断入库单和出库单是否同一个平台，如果不是一个平台，原出库单又是货权转移的，异常，卡单
            if (StringUtils.isEmpty(relationOrderSiteCode) || StringUtils.isEmpty(siteCode) || !siteCode.equals(relationOrderSiteCode)) {
                throw BusinessException.fail("与关联单平台不一致，货权转移失败");
            }
    
            CustomerOrderInfoExtend relationOutCustomerOrderExtend = orderverifyHelper.getCustomerOrderExtend(relationCustomerOrderInfo.getOrderNo());
            boolean mulTaskTransfer = transRelationVerifyHelper.isMulTaskTransfer(relationOutCustomerOrderExtend);
    
            //1、如果出库单是多个任务货权转移
            if (mulTaskTransfer){
                log.info("transferAndSplitMulAnOrder-> ：orderNo:{}",customerOrderInfo.getOrderNo());
                TransferVerifyDto build = TransferVerifyDto.builder().relationOutCustomerOrderInfo(relationCustomerOrderInfo).inCustomerOrderInfoExt(customerOrderInfo).build();
                FlowListenerParam.success(transRelationVerifyHelper.transferAndSplitMulAnOrder(build).getErrorMsg());
                return customerOrderInfo;
                //2、出库单是单个任务货权转移，按照原来逻辑走
            }else {
                //2020-6-3 16:09:21 索超：加上同仓校验
                String customerCode = customerOrderInfo.getCustomerCode();
                String whCode = customerOrderInfo.getWhCode();
                String targetCustomerCode = relationCustomerOrderInfo.getCustomerCode();
                //            List<String> sameWhs = separateWarehouseService.validateSameWarehouseCode(customerCode, targetCustomerCode, Lists.newArrayList(whCode));
                ValidateSameWhDto validateSameWhDto = ValidateSameWhDto.builder()
                      .customerCode(customerCode)
                      .targetCustomerCode(targetCustomerCode)
                      .whCodes(Lists.newArrayList(whCode))
                      .orderNo(customerOrderInfo.getOrderNo()).siteCode(siteCode)
                      .build();
                //2024年8月31日15:25:01 丽红：统一调用 wms同仓校验，不区分基地标  https://cf.annto.com/pages/viewpage.action?pageId=53263643
                List<String> sameWhs = separateWarehouseService.onlyValidateSameWarehouseByWms(validateSameWhDto);
    
                //List<String> sameWhs = separateWarehouseService.validateSameWarehouseCodeSplit(validateSameWhDto);
                if (CollectionUtils.isEmpty(sameWhs)) {
                    LOGGER.warn("WMS同仓校验失败，orderNo:{}", customerOrderInfo.getOrderNo());
                    if (validateSameWhDto.isBaseOrder()) {
                        throw BusinessException.fail(String.format(CommonConstant.COL_WH_JUDGE_FORMAT, customerCode, targetCustomerCode));
                    }
                    //曹阳阳 订单判断为货权转移，校验WMS同仓失败，不卡单
                    throw BusinessException.fail("跳过货权转移,WMS同仓校验失败，请到WMS-【配置】-【出库配置】-【客户】维护协同关系[客户编码:" + customerCode + ",目标客户编码:" + targetCustomerCode + "]，orderNo:" + customerOrderInfo.getOrderNo());
                }
    
                customerOrderInfo.setJoinType(JoinType.TRANS_INV.getKey());
                if(ToolUtils.isEmpty(customerOrderInfo.getProjectClassify())){
                    customerOrderInfo.setProjectClassify(ProjectClassifyEnum.TRANS_INV.getKey());
                }
                //如果是关联单，直接生成子单
                if (upperRelationOrderFlag) {
                    //!@商品状态纠正 - 关联单根据出库单来纠正入库单
                    boolean flowListenerParam = transRelationVerifyHelper.correctInCustomerItems(customerOrderInfo.getOrderNo(), relationCustomerOrderInfo.getOrderNo(), customerOrderInfo);
                    if (flowListenerParam == false) {
                        return customerOrderInfo;
                    }
                    //生成子单库存
                    AnntoOrder anntoOrder = new AnntoOrder(customerOrderInfo.getOrderNo(), customerOrderInfo).invoke();
                    //保存订单数据
                    return invokeUpdate(Lists.newArrayList(anntoOrder), customerOrderInfo, joinTypeSource).data;
                }
            }
        }


        if (InOutType.OUT.getName().equals(customerOrderInfo.getInOutType())) {
            
            //如果是新出库单货权转移，则直接调用货权转移接口
            if(businessHelper.isOnNewOutOrderAutoTrans()) {
                flowAutoTransferHelper.outOrderAutoTransfer(customerOrderInfo);
                return customerOrderInfo;
            }
            
            //出库单校验货权转移时，需先判断商品状态是否正品，非正品不允许进行货权转移；
            List<CustomerOrderItem> customerOrderItems = customerOrderInfo.getCustomerOrderItems(); //lmpOrderFlowHelper.listCustomerOrderItem(customerOrderInfo.getOrderNo());
            for (CustomerOrderItem customerOrderItem : customerOrderItems) {
                if (!ItemStatus.Y.getKey().equals(customerOrderItem.getItemStatus())) {
                    FlowListenerParam.success("货权转移失败，存在非正品的商品,不允许进行货权转移");
                    return customerOrderInfo;
                }
            }

            //订单类型=退货出库单，来源系统=PDD，项目分类projectClassfiy= TRANS_INV时，目标仓库直接取上游
            if (SourceSystem.PDD.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.RO.getKey().equals(customerOrderInfo.getOrderType()) && ProjectClassifyEnum.TRANS_INV.getKey().equals(customerOrderInfo.getProjectClassify())) {
                customerOrderInfo.setTargetSiteCode(customerOrderInfo.getSiteCode());
                customerOrderInfo.setTargetWhCode(customerOrderInfo.getWhCode());
                //生成子单占用库存
                AnntoOrder anntoOrder = new AnntoOrder(customerOrderInfo.getOrderNo(), customerOrderInfo.getWhCode(), customerOrderInfo).invoke();
                //保存订单数据
                return invokeUpdate(Lists.newArrayList(anntoOrder),customerOrderInfo,joinTypeSource).data;
            }

            //收货平台
            String targetSiteCode = customerOrderInfo.getTargetSiteCode();
            //发货平台
            String siteCode = customerOrderInfo.getSiteCode();

            String sourceWh = customerOrderInfo.getWhCode();
            //判断收货平台(target_site_code)与发货平台()是否一致，一致则根据收货地址进行分仓，仓库地址与收货地址一样
            if (StringUtils.isEmpty(targetSiteCode) || StringUtils.isEmpty(siteCode) || !siteCode.equals(targetSiteCode)) {
                FlowListenerParam.success("收发货平台不一致，不做货权转移");
                customerOrderInfo.setJoinType(joinTypeSource);
                return customerOrderInfo;
            }
    
            //2024年12月17日14:47:06 丽红：货权转移出库和入库财务仓绑定的逻辑仓不一样不允许转移 https://cf.annto.com/pages/viewpage.action?pageId=67709231
            String targetWhCode = customerOrderInfo.getTargetWhCode();
            if (StringUtils.isNotEmpty(sourceWh) &&  StringUtils.isNotEmpty(targetWhCode) && !sourceWh.equals(targetWhCode)) {
                FlowListenerParam.success("发货仓【"+sourceWh+"】收货仓【"+targetWhCode+"】不一致，不支持转移");
                return customerOrderInfo;
            }
            //如果有目标仓库，父单仓库直接用目标仓库来做协同和分仓
            if (StringUtils.isEmpty(sourceWh) &&  StringUtils.isNotEmpty(targetWhCode)) {
                sourceWh = targetWhCode;
            }
            

            CustomerOrderAddress customerOrderAddress = customerOrderInfo.getCustomerOrderAddress();// lmpOrderFlowHelper.getCustomerOrderAddress(customerOrderInfo.getOrderNo());
            //收货地址
            String receiverDetailAddr = customerOrderAddress.getReceiverDetailAddr();
            //根据siteCode和receiverAddr查询whcode
            JsonResponse<List<String>> getWhCode = omsSuContractFegin.getWhCodeBySiteCodeAndReceiveAdress(siteCode, receiverDetailAddr);
            if (!"0".equals(getWhCode.getCode())) {
                throw BusinessException.fail("查询仓库信息失败:" + getWhCode.getMsg());
            }

            List<String> whcodes = getWhCode.data();

            if (CollectionUtils.isEmpty(whcodes)) {
                whcodes = mdmHelper.getAvailableCdWareHouseCode(siteCode);
                if(ToolUtils.isEmpty(whcodes)){
                    FlowListenerParam.success("取不到与上游收货地址一致的仓库且此["+siteCode+"]平台下无可用仓库，不做货权转移");
                    return customerOrderInfo;
                }
            }
            //gsc:如果订单对照解析的时候指定了逻辑仓，订单父单存在了逻辑仓的情况下，自动货权转移根据地址获取货权转移仓库的时候加一道判断，如果父单的逻辑仓在地址匹配的逻辑仓范围内，则用父单的逻辑仓作为货权转移仓， 如果不在范围内，则跳过
            if (StringUtils.isNotBlank(sourceWh)) {
                String finalSourceWh = sourceWh;
                whcodes = whcodes.stream().filter(w -> finalSourceWh.equals(w)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(whcodes)) {
                    FlowListenerParam.success("指定逻辑仓["+sourceWh+"]不在地址匹配的逻辑仓范围内，不做货权转移");
                    return customerOrderInfo;
                }
            }
            String customerCode = customerOrderInfo.getCustomerCode();
            String targetCustomerCode = customerOrderInfo.getTargetCustomerCode();
            //需判断targetCustomerCode是否为空，为空不做货权转移校验
            if (ToolUtils.isEmpty(targetCustomerCode)) {
                FlowListenerParam.success("目标客户为空，不做货权转移");
                return customerOrderInfo;
            }
//            List<String> sameWhs = separateWarehouseService.validateSameWarehouseCode(customerCode, targetCustomerCode, whcodes);
            ValidateSameWhDto validateSameWhDto = ValidateSameWhDto.builder()
                .customerCode(customerCode)
                .targetCustomerCode(targetCustomerCode)
                .whCodes(whcodes)
                .orderNo(customerOrderInfo.getOrderNo())
                .siteCode(siteCode)
                .build();
            //2024年8月31日15:25:01 丽红：统一调用 wms同仓校验，不区分基地标  https://cf.annto.com/pages/viewpage.action?pageId=53263643
            List<String> sameWhs = separateWarehouseService.onlyValidateSameWarehouseByWms(validateSameWhDto);
            //List<String> sameWhs = separateWarehouseService.validateSameWarehouseCodeSplit(validateSameWhDto);
            if (CollectionUtils.isEmpty(sameWhs)) {
                LOGGER.warn("WMS同仓校验失败，orderNo:{}", customerOrderInfo.getOrderNo());
                //曹阳阳 订单判断为货权转移，校验WMS同仓失败，不卡单
                FlowListenerParam.success("跳过货权转移,WMS同仓校验失败，请到WMS-【配置】-【出库配置】-【客户】维护协同关系[客户编码:"+ customerCode + ",目标客户编码:"+ targetCustomerCode +"]，orderNo:" + customerOrderInfo.getOrderNo());
                if (validateSameWhDto.isBaseOrder()) {
                    FlowListenerParam.success(String.format(CommonConstant.COL_WH_JUDGE_FORMAT, customerCode, targetCustomerCode));
                }
                return customerOrderInfo;
            }
            //customerOrderInfo.setWhCode(whCode);
            //生成子单占用库存
//            AnntoOrder anntoOrder = new AnntoOrder(customerOrderInfo.getOrderNo(), whCode, customerOrderInfo).invoke();
            List<AnntoOrder> anntoOrders = Lists.newArrayList();
            for (String whCode : sameWhs) {
                customerOrderInfo.setWhCode(whCode);
                AnntoOrder anntoOrder = new AnntoOrder(customerOrderInfo.getOrderNo(), customerOrderInfo).invoke();
                //设置货权转移
                anntoOrders.add(anntoOrder);
                //占用库存一次成功就行
                if (anntoOrder.getIsOccupy()) {
                    break;
                }
            }
            //保存订单数据
            return invokeUpdate(anntoOrders,customerOrderInfo,joinTypeSource).data;
        }

        FlowListenerParam.success("条件不符合货权转移，不做货权转移");
        return customerOrderInfo;

    }

    /**
     * 匹配 MRP 入库上的上游订单类型
     * @param upperOrderType
     * @return
     */
    private boolean mrpUpperOrderType(String upperOrderType) {
        return Lists.newArrayList(OrderType.DI.getKey(), CommonConstant.KI, MRP_HI.getKey()).contains(upperOrderType);
    }
    

    private Boolean checkRelationOrder(CustomerOrderInfo customerOrderInfo, CustomerOrderInfo relationCustomerOrderInfo) {
        return relationOrderService.checkRelationOrder(customerOrderInfo, relationCustomerOrderInfo);
    }

//
//    @Override
//    public FlowListenerParam cimsOutCargoRightTransfer(String orderNo) {
//        return cargoRightTransfer(orderNo);
//    }

//
//    @Override
//    public FlowListenerParam cimsInCargoRightTransfer(String orderNo) {
//        return cargoRightTransfer(orderNo);
//    }

    /**
     * 库存调剂
     *
     * @param customerOrderInfo
     * @return
     */
    private CustomerOrderInfoExt inventoryAdjustment(CustomerOrderInfoExt customerOrderInfo ,String joinTypeSource) {
        String orderType = customerOrderInfo.getOrderType();
        //发货通知单带调剂标识(order_type=PO)&电商货权转移()
        if (OrderType.PO.getKey().equals(orderType)) {
            //调用WMS接口
            String customerCode = customerOrderInfo.getCustomerCode();
            String targetCustomerCode = customerOrderInfo.getTargetCustomerCode();
            List<String> warehouseCodeList = null;
            //!@电商货权转移校验 - 入口（中台）
            if (JoinType.SHARE1300.getKey().equals(customerOrderInfo.getJoinType())
                && ToolUtils.isNotEmpty(customerOrderInfo.getWhCode())) {
                warehouseCodeList = Lists.newArrayList(customerOrderInfo.getWhCode());
            } else {
                List<CdWarehouse> cdWarehouseList = getCollabWh(customerOrderInfo);
                if (CollectionUtils.isEmpty(cdWarehouseList)) {
                    LOGGER.warn("没有找到协同仓，orderNo:{}", customerOrderInfo.getOrderNo());
                    throw BusinessException.fail("没有找到协同仓，orderNo:" + customerOrderInfo.getOrderNo());
                }
                warehouseCodeList = cdWarehouseList.stream().map(CdWarehouse::getWhCode).collect(Collectors.toList());
            }
//            List<String> sameWarehouseCode = separateWarehouseService.validateSameWarehouseCode(customerCode, targetCustomerCode, warehouseCodeList);
            // JsonResponse<List<String>> jsonResponse = otpService.validateWmsSameWhCodeValidate(whCodeValidateDto).data();
            ValidateSameWhDto validateSameWhDto = ValidateSameWhDto.builder()
                .customerCode(customerCode)
                .targetCustomerCode(targetCustomerCode)
                .whCodes(warehouseCodeList)
                .orderNo(customerOrderInfo.getOrderNo())
                .siteCode(customerOrderInfo.getSiteCode())
                .build();
            //2024年8月31日15:25:01 丽红：统一调用 wms同仓校验，不区分基地标  https://cf.annto.com/pages/viewpage.action?pageId=53263643
            List<String> sameWarehouseCode = separateWarehouseService.onlyValidateSameWarehouseByWms(validateSameWhDto);
            //List<String> sameWarehouseCode = separateWarehouseService.validateSameWarehouseCodeSplit(validateSameWhDto);
            if (CollectionUtils.isEmpty(sameWarehouseCode)) {
                LOGGER.warn("WMS同仓校验失败，orderNo:{}", customerOrderInfo.getOrderNo());
                if (validateSameWhDto.isBaseOrder()) {
                    throw BusinessException.fail(String.format(CommonConstant.COL_WH_JUDGE_FORMAT, customerCode, targetCustomerCode));
                }
                throw BusinessException.fail("WMS同仓校验失败，请到WMS-【配置】-【出库配置】-【客户】维护协同关系[客户编码:"+ customerCode + ",目标客户编码:"+ targetCustomerCode +"]，orderNo:" + customerOrderInfo.getOrderNo());
            }
            List<AnntoOrder> anntoOrders = Lists.newArrayList();
            for (String whCode : sameWarehouseCode) {
                customerOrderInfo.setWhCode(whCode);
                AnntoOrder anntoOrder = new AnntoOrder(customerOrderInfo.getOrderNo(), customerOrderInfo).invoke();
                //设置货权转移
                anntoOrders.add(anntoOrder);
                //占用库存一次成功就行
                if (anntoOrder.getIsOccupy()) {
                    break;
                }
            }
            return invokeUpdate(anntoOrders ,customerOrderInfo ,joinTypeSource).data;
        }
        //退货入库单带调剂标识(order_type=RI)
        if (OrderType.RI.getKey().equals(orderType)) {
            //设置货权转移
            //调用WMS接口
            List<String> warehouseCodeList = null;
            if (JoinType.SHARE1300.getKey().equals(customerOrderInfo.getJoinType())
                && ToolUtils.isNotEmpty(customerOrderInfo.getWhCode())) {
                warehouseCodeList = Lists.newArrayList(customerOrderInfo.getWhCode());
            } else {
                List<CdWarehouse> cdWarehouseList = getCollabWh(customerOrderInfo);
                if (CollectionUtils.isEmpty(cdWarehouseList)) {
                    LOGGER.warn("没有找到协同仓，orderNo:{}", customerOrderInfo.getOrderNo());

                    throw BusinessException.fail( "没有找到协同仓，orderNo:" + customerOrderInfo.getOrderNo());
                }
                warehouseCodeList = cdWarehouseList.stream().map(CdWarehouse::getWhCode).collect(Collectors.toList());
            }
            String orderNo = customerOrderInfo.getOrderNo();
            String customerCode = customerOrderInfo.getCustomerCode();
            String targetCustomerCode = customerOrderInfo.getTargetCustomerCode();
            // JsonResponse<OpenJsonResponse<List<String>>> jsonResponse = otpService.validateWmsSameWhCodeValidate(whCodeValidateDto);
//            List<String> sameWarehouseCode = separateWarehouseService.validateSameWarehouseCode(customerCode, targetCustomerCode, warehouseCodeList);
            ValidateSameWhDto validateSameWhDto = ValidateSameWhDto.builder()
                .customerCode(customerCode)
                .targetCustomerCode(targetCustomerCode)
                .whCodes(warehouseCodeList)
                .orderNo(customerOrderInfo.getOrderNo())
                .siteCode(customerOrderInfo.getSiteCode())
                .build();
            //2024年8月31日15:25:01 丽红：统一调用 wms同仓校验，不区分基地标  https://cf.annto.com/pages/viewpage.action?pageId=53263643
            List<String> sameWarehouseCode = separateWarehouseService.onlyValidateSameWarehouseByWms(validateSameWhDto);
            //List<String> sameWarehouseCode = separateWarehouseService.validateSameWarehouseCodeSplit(validateSameWhDto);
            if (CollectionUtils.isEmpty(sameWarehouseCode)) {
                LOGGER.warn("WMS同仓校验失败，orderNo:{}", orderNo);
                if (validateSameWhDto.isBaseOrder()) {
                    throw BusinessException.fail(String.format(CommonConstant.COL_WH_JUDGE_FORMAT, customerCode, targetCustomerCode));
                }
                throw BusinessException.fail("WMS同仓校验失败，请到WMS-【配置】-【出库配置】-【客户】维护协同关系[客户编码:"+ customerCode + ",目标客户编码:"+ targetCustomerCode +"]，orderNo:" + orderNo);
            }
            List<AnntoOrder> anntoOrders = Lists.newArrayList();
            for (String whCode : sameWarehouseCode) {
                customerOrderInfo.setWhCode(whCode);
                AnntoOrder anntoOrder = new AnntoOrder(orderNo, customerOrderInfo).invoke();
                anntoOrders.add(anntoOrder);
//                Boolean aBoolean = pledgeService.pledgeCheck(orderInfo.getOrderNo());
//                JsonResponse<Boolean> pledgeResp = pledgeService.pledgeCheck(orderInfo.getOrderNo());
//                if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeResp.getCode())) {
//                    throw new BusinessException(pledgeResp.getCode(), pledgeResp.getMsg());
//                }
                //占用库存一次成功就行
                if (anntoOrder.getIsOccupy()) {
                    break;
                }
            }
            return invokeUpdate(anntoOrders ,customerOrderInfo,joinTypeSource).data;
        }

        throw BusinessException.fail("调剂失败");
    }

    private List<CdWarehouse> getCollabWh(CustomerOrderInfo customerOrderInfo) {
        String siteCode = customerOrderInfo.getSiteCode();
        return getCollabWhBySiteCode(siteCode);
    }

    @Override
    public List<CdWarehouse> getCollabWhBySiteCode(String siteCode) {
        List<CdWarehouse> cdWarehouseListCacheFromSiteCode = cdWarehouseManager.getCdWarehouseListCacheFromSiteCode(siteCode);
        if (!CollectionUtils.isEmpty(cdWarehouseListCacheFromSiteCode)) {
            cdWarehouseListCacheFromSiteCode = cdWarehouseListCacheFromSiteCode.stream().filter(t ->
                t.getIsCollabWh() != null && t.getIsCollabWh().intValue() == 1
            ).collect(Collectors.toList());
        }
        return cdWarehouseListCacheFromSiteCode;
    }


    @Data
    private class AnntoOrder {
        private String orderNo;
        private CustomerOrderInfoExt customerOrderInfo;
        private OrderInfo orderInfo;
        private List<OrderInfoItem> orderInfoItems;
        private boolean isOccupy;
        private String whCode;

        private String msg;

        public AnntoOrder(String orderNo, CustomerOrderInfoExt customerOrderInfo) {
            this.orderNo = orderNo;
            this.customerOrderInfo = customerOrderInfo;
        }

        public AnntoOrder(String orderNo, String whCode, CustomerOrderInfoExt customerOrderInfo) {
            this.whCode = whCode;
            this.orderNo = orderNo;
            this.customerOrderInfo = customerOrderInfo;
        }

        public boolean getIsOccupy() {
            return isOccupy;
        }

        public AnntoOrder invoke() {
            orderInfo = new OrderInfo();
            //cyy:ofcPo判断为货权转移之后，要设置join_type=TRANS_INV，不然任务生成之后，无法根据这个表示hold单的
            if (businessHelper.isMarkTransInvFlag(customerOrderInfo)) {
                customerOrderInfo.setJoinType(JoinType.TRANS_INV.getKey());
            }
            //根据customerOrderInfo生成orderInfo及其商品明细，orderInfo.orderNo调用生成订单号方法，获取新的订单号；先不保存
            BeanUtils.copyProperties(customerOrderInfo, orderInfo, "id");
            if (ToolUtils.isNotEmpty(whCode)) {
                orderInfo.setWhCode(whCode);
            }
            orderInfo.setWhName(cdWarehouseManager.getCdWarehouseNameByWhCode(orderInfo.getWhCode()));
            //设置orderInfo.parentOrderNo = customerOrderInfo.orderNo
            String customerOrderNo = customerOrderInfo.getCustomerOrderNo();
            orderInfo.setParentOrderNo(customerOrderInfo.getOrderNo());
            String newOrderNo = flowRuleHelper.getAnOrderNo(customerOrderInfo);
            orderInfo.setTenantCode(customerOrderInfo.getTenantCode());
            orderInfo.setOrderNo(newOrderNo);
            List<CustomerOrderItem> data = customerOrderInfo.getCustomerOrderItems();// lmpOrderFlowHelper.listCustomerOrderItem(orderNo);
            orderInfoItems = Lists.newArrayList();
            //数量
            BigDecimal totalQty = BigDecimal.ZERO;
            //体积
            BigDecimal totalVolume = BigDecimal.ZERO;
            //重量
            BigDecimal totalGrossWeight = BigDecimal.ZERO;
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(data)) {
                for (CustomerOrderItem customerOrderItem : data) {
                    OrderInfoItem orderInfoItem = new OrderInfoItem();
                    BeanUtils.copyProperties(customerOrderItem, orderInfoItem, "id", "cancleQty");

                    orderInfoItem.setOrderNo(newOrderNo);
                    orderInfoItem.setParentOrderNo(orderInfo.getParentOrderNo());
                    BigDecimal planQty = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                    if (ToolUtils.isNotEmpty(customerOrderItem.getCancleQty())) {
                        orderInfoItem.setRemark("取消的数量：" + ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                    }
                    //2024年10月8日20:17:21 王涛：自动货权转移，如果为0的明细不生成，否则会导致入库单无法关联（条数不正确）
                    //https://devsecops.midea.com/agile/workbench/work/pending/?systemId=ITS000000873&teamId=ID0000000410&demandId=AAEC051479&itemType=3&zone=yellow
                    if (BigDecimal.ZERO.compareTo(planQty) == 0) {
                        log.info("planQty is Zero-> parentOrderNo：{}",orderInfo.getParentOrderNo());
                        continue;
                    }
                    orderInfoItem.setPlanQty(planQty);
                    //计算订单
                    totalQty = totalQty.add(orderInfoItem.getPlanQty());
                    BigDecimal volumeTemp = orderInfoItem.getPlanQty().multiply(ObjectUtils.defaultIfNull(orderInfoItem.getVolume(), BigDecimal.ZERO));
                    if (orderInfoItem.getTotalVolume() != null) {
                        volumeTemp = orderInfoItem.getTotalVolume();
                    }
                    BigDecimal grossWeightTemp = orderInfoItem.getPlanQty().multiply(ObjectUtils.defaultIfNull(orderInfoItem.getGrossWeight(), BigDecimal.ZERO));
                    if (orderInfoItem.getTotalGrossWeight() != null) {
                        grossWeightTemp = orderInfoItem.getTotalGrossWeight();
                    }
                    totalVolume = totalVolume.add(volumeTemp);
                    totalGrossWeight = totalGrossWeight.add(grossWeightTemp);
                    orderInfoItems.add(orderInfoItem);

                }
            }
            //计算订单数量，体积，重量
            orderInfo.setTotalQty(totalQty);
            orderInfo.setTotalVolume(totalVolume);
            orderInfo.setTotalGrossWeight(totalGrossWeight);
            OrderInfoExt orderInfoExt = new OrderInfoExt();
            orderInfoExt.setOrderInfoItems(orderInfoItems);
            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);
            if (OrderType.RI.getKey().equals(customerOrderInfo.getOrderType())
                && JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType())) {
                orderInfoExt.setCustomerCode(orderInfoExt.getTargetCustomerCode());
            } else {
                if (InOutType.IN.getName().equals(customerOrderInfo.getInOutType())) {
                    isOccupy = true;
                    return this;
                }
            }
            isOccupy = isInventoryPreemption(orderInfoExt, this);
            return this;
        }

    }

    public JsonResponse<CustomerOrderInfoExt> invokeUpdate(List<AnntoOrder> list,CustomerOrderInfoExt customerOrderInfoSource ,String sourceJoinType) {
        if (ToolUtils.isEmpty(list)) {
            return new JsonResponse(BaseCodeEnum.FAILED.getCode(), "");
//            return FlowListenerParam.fail(ExceptionType.CARGO_RIGHT_TRANSFER_FAILED);
        }
        //先判断是否全部占用仓库成功
        List<AnntoOrder> unOccupyList = list.stream().filter(o -> !o.getIsOccupy()).collect(Collectors.toList());
        //全部占用失败，手工拆单
        if (ToolUtils.isNotEmpty(unOccupyList) && unOccupyList.size() == list.size()) {
//            CustomerOrderInfoExt customerOrderInfo = unOccupyList.get(0).getCustomerOrderInfo();
//            customerOrderInfo.setApartType(ApartType.HANDLE.getKey());
//            customerOrderInfo.setOrderStatus(OrderStatus.AUDIT_FAILE.getKey());
//            customerOrderInfo.setWhCode(null);
//            customerOrderInfo.setJoinType(null);
//            customerOrderInfoSource.setJoinType(sourceJoinType);
//            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "货权转移");
            //gsc:占用库存失败允许手工分仓
            CustomerOrderInfoExt customerOrderInfo = new CustomerOrderInfoExt();
            customerOrderInfo.setId(customerOrderInfoSource.getId());
            //2024年8月8日23:04:52 浩明：https://cf.annto.com/pages/resumedraft.action?draftId=46466128&draftShareId=11a5e11b-b739-4252-b70c-1d7a52b1395f&，往订单描述写备注
            customerOrderInfo.setExceptionDesc("402：订单"+customerOrderInfo.getCustomerOrderNo()+"，待分仓 "+orderAggFeign.getVerifyFailPersonByCustOrder(customerOrderInfo).data);
            customerOrderInfo.setApartType(ApartType.HANDLE.getKey());
            customerOrderInfo.setOrderStatus(OrderStatus.AUDIT_FAILE.getKey());
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "货权转移");
            throw BusinessException.fail("货权转移占用仓库失败,请手工分仓");
//
//            return new JsonResponse(BaseCodeEnum.FAILED.getCode(), ExceptionType.CARGO_RIGHT_TRANSFER_FAILED.getDesc());
        }
        List<OrderInfoItem> items = Lists.newArrayList();
        List<OrderInfo> orderInfos = Lists.newArrayList();
        //保存库存占用成功的单
        List<AnntoOrder> occupyList = list.stream().filter(o -> o.getIsOccupy()).collect(Collectors.toList());
        CustomerOrderInfoExt customerOrderInfoExt = list.get(0).getCustomerOrderInfo();
        occupyList.forEach(
            o -> {
                OrderInfo orderInfo = o.getOrderInfo();
                orderInfo.setUpperAgingCode(customerOrderInfoExt.getAgingProductCode());
                orderInfos.add(orderInfo);
                items.addAll(o.getOrderInfoItems());
            }
        );
        //批量新增
        JsonResponse jr1 = orderInfoFeign.batchCreateOrUpdate(orderInfos);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jr1.getCode())) {
            throw BusinessException.fail("子订单插入失败: " + jr1.getMsg());
        }
        JsonResponse jr2 = orderInfoItemFeign.batchCreateOrUpdate(items);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jr2.getCode())) {
            throw BusinessException.fail("子订单明细插入失败：" + jr2.getMsg());
        }
        customerOrderInfoExt.setApartStatus(CommonConstant.APARTED);
        //仓库名称
        String whName = mdmHelper.getWhNameNull(customerOrderInfoExt.getWhCode());
        customerOrderInfoExt.setWhName(whName);
        lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt,"货权转移");
        //生成任务
        List<String> orderNos = orderInfos.stream().map(o -> {
            //不用触发生成任务
//            OrderMq orderMq = new OrderMq();
//            orderMq.setOrderNo(o.getOrderNo());
//            orderMq.setNode(OrderStatus.AUDITED.getKey());
//            orderAuditMessageProducer.sent(orderMq);
            return o.getOrderNo();
        }).collect(Collectors.toList());

        JsonResponse temp = new JsonResponse(BaseCodeEnum.SUCCESS.getCode(), "货权转移成功" + orderNos.toString());
        FlowListenerParam.success("");
        temp.setData(customerOrderInfoExt);
        return temp;
    }

    private List<CustomerOrderInfo> getCustomOrdersByOrderNos(List<String> customerOrderNos) {
        //查询客户订单
        JsonResponse<List<CustomerOrderInfo>> customerOrderInfosResponse = customerOrderInfoFeign.listByOrderNos(customerOrderNos);
        if (!CollectionUtils.isEmpty(customerOrderInfosResponse.getData())) {
            return customerOrderInfosResponse.getData();
        }
        return Lists.newArrayList();
    }


    public boolean isInventoryPreemption(OrderInfoExt orderInfoExt, AnntoOrder anntoOrder) {
//        centerInvService.proHoldInv()
        //占用库存
        JsonResponse jsonResponse = null;
        //占用库存
        try {
            jsonResponse = centerInvService.proHoldInv(orderInfoExt, CenterInvType.PLAN_OMS_OUT.getKey());
            LOGGER.info("占用的-调用中央库存--------" + jsonResponse);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            JsonResponse jsonResponse2 = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
            LOGGER.info("取消占用的---------" + jsonResponse2);
            throw e;
        }
        //占用库存
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            Object data = jsonResponse.getData();
            if (ToolUtils.isNotEmpty(data)) {
                anntoOrder.setMsg(data.toString());
            }
            JsonResponse jsonResponse2 = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
            LOGGER.info("取消占用的---------" + jsonResponse2);
        }

        return BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode());
    }


    private List<CustomerOrderItem> listCustomOrderItemsByOrderNos(List<String> orderNos) {
        //查询客户订单
        JsonResponse<List<CustomerOrderItem>> customerOrderItemsResponse = customerOrderInfoFeign.listCustomOrderItemsByOrderNos(orderNos);
        if (!CollectionUtils.isEmpty(customerOrderItemsResponse.getData())) {
            return customerOrderItemsResponse.getData();
        }
        return Lists.newArrayList();
    }

    
    
    
    
    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (ToolUtils.isEmpty(srcValue)) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }
}
