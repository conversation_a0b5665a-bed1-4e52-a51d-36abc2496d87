package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.StandardAgingRuleConfigInfo;
import com.midea.logistics.otp.order.domain.bean.custom.StandardAgingRuleConfigInfoSaveRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©1968-2023 Midea Group,IT
 *
 * @Description: 中台-时效规则配置
 * @FileName: AgingRuleConfigInfoFeign
 * @Author: fengxw26
 * @Date: 2023/2/20 16:37
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "StandardAgingRuleConfigInfoFeign")
public interface StandardAgingRuleConfigInfoFeign {

    /**
     * 查询时效配置分页列表
     *
     * @param entity
     * @return
     */
    @PostMapping("/custom/standardAgingRuleConfigInfo/searchPage")
    JsonResponse searchPage(@RequestBody StandardAgingRuleConfigInfo entity);

    /**
     * 批量新增或修改
     *
     * @param entityList
     * @return
     */
    @PostMapping("/custom/standardAgingRuleConfigInfo/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<StandardAgingRuleConfigInfoSaveRequest> entityList);

    /**
     * 根据id删除
     *
     * @param agingRuleConfigInfoIds
     * @return
     */
    @PostMapping("/custom/standardAgingRuleConfigInfo/deleteBatchByIds")
    JsonResponse deleteBatchByIds(@RequestBody List<Long> agingRuleConfigInfoIds);
}
