package com.midea.logistics.otp.orderverify.service;

import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.mideaframework.core.web.JsonResponse;

/**
 * <AUTHOR>
 */
public interface IOrderInfoService {
    
    FlowListenerParam pledgeCheck(OrderInfoExt orderInfo);
    /**
     * @description: 快递子流程审核
     * @param: [orderInfo, oldOrderInfo]
     * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
     * @author: 陈永培
     * @createtime: 2021/10/12 17:51
     */
    void expressSubFlowVerify(OrderInfoExt orderInfo);

    /**
    * @description: 合同计费审核
    * @param: [orderInfo, oldOrderInfo]
    * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
    * @author: 陈永培
    * @createtime: 2021/10/12 17:51
    */
    FlowListenerParam contractCharge(OrderInfoExt orderInfo, OrderInfoExt oldOrderInfo);

    JsonResponse contractVerify(OrderInfoExt orderInfo);

    FlowListenerParam aging(OrderInfoExt orderInfo, OrderInfoExt oldOrderInfo);

    JsonResponse syncBms(OrderInfo orderInfo);

    JsonResponse pushLots(OrderInfo orderInfo);

    void updateOrderInfo(OrderInfo orderInfo, OrderInfo source);
}