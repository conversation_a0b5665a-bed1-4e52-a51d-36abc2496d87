package com.midea.logistics.imp.orderverify.service;

import java.util.List;

import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;

/**
* @description: 无忧零售货权转移
* @author: 陈永培
* @createtime: 2024/2/23 8:47
*/
public interface WfrTransferService {
    
    CustomerOrderInfoExt wfrCargoRightTransfer(CustomerOrderInfoExt customerOrderInfo);
    
    CustomerOrderInfoExt automatic(CustomerOrderInfoExt customerOrderInfo);
}