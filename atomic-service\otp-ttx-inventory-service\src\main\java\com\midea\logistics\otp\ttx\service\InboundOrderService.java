package com.midea.logistics.otp.ttx.service;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder;
import com.mideaframework.core.web.PageResponse;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: InboundOrderService
 * Author: yaowl
 * Date: 2019-8-21 19:21:37
 * Description:InboundOrderService服务接口
 */
public interface InboundOrderService {

    /**
     * Save integer.
     *
     * @param inboundOrder the inbound order
     * @return the integer
     */
    Integer save(InboundOrder inboundOrder);


    /**
     * Update inbound order.
     *
     * @param inboundOrder the inbound order
     * @return the inbound order
     */
    InboundOrder update(InboundOrder inboundOrder);


    Integer batchUpdate(List<InboundOrder> list);
    /**
     * Select by id inbound order.
     *
     * @param inboundOrder the inbound order
     * @return the inbound order
     */
    InboundOrder selectById(InboundOrder inboundOrder);

    /**
     * Select one inbound order.
     *
     * @param inboundOrder the inbound order
     * @return the inbound order
     */
    InboundOrder selectOne(InboundOrder inboundOrder);

    /**
     * Select list.
     *
     * @param inboundOrder the inbound order
     * @return the list
     */
    List<InboundOrder> select(InboundOrder inboundOrder);

    /**
     * Count int.
     *
     * @param inboundOrder the inbound order
     * @return the int
     */
    int count(InboundOrder inboundOrder);

    /**
     * Select page page response.
     *
     * @param inboundOrder the inbound order
     * @return the page response
     */
    PageResponse<InboundOrder> selectPage(InboundOrder inboundOrder);

    /**
     * Inbound order template download.
     *
     * @param request  the request
     * @param response the response
     */
    void inboundOrderTemplateDownload(HttpServletRequest request,HttpServletResponse response);

    ImportFileRequest inboundOrderUpload(@RequestParam("file") MultipartFile file);
}