package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.rule.domain.bean.DeliveryRule;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: DeliveryRuleFeign
 * Author: liwende
 * Date: 2019-5-29 14:55:50
 * Description:起运方量 feign
 */
@FeignClient(value = "logistics-otp-rule-service",url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface DeliveryRuleFeign {

    @RequestMapping(value = "/deliveryRule", method = RequestMethod.POST)
    JsonResponse create(@RequestBody DeliveryRule deliveryRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/deliveryRule/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/deliveryRule/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody DeliveryRule deliveryRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/deliveryRule/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     * 查询列表
     *
     * @param deliveryRule
     * @return
     */
    @RequestMapping(value = "/deliveryRules", method = RequestMethod.GET)
    JsonResponse<PageResponse<DeliveryRule>> search(@SpringQueryMap DeliveryRule deliveryRule);

    /**
     * 批量新增和批量修改统一保存
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/deliveryRule/batchCreateOrUpdate", method = RequestMethod.POST)
    public JsonResponse batchCreateOrUpdate(@Valid @RequestBody List<DeliveryRule> deliveryRules);

    /**
     * 批量删除
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/deliveryRules/{id}", method = RequestMethod.DELETE)
    public JsonResponse batchDelete(@PathVariable("id") Long[] ids);
}
