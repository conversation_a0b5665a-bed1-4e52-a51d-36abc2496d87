package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.domain.mdm.domain.CdCommonMaterial;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.ConvergedCustomerOrderFeign;
import com.midea.logistics.otp.common.feign.servicefeign.dc.DcAtomicFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.common.helper.bean.NewCustomerOrderInfoExcel;
import com.midea.logistics.otp.common.request.ContactsOms;
import com.midea.logistics.otp.common.utils.Assert;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.common.utils.ThreadPoolUtil;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.helper.CodeHelper;
import com.midea.logistics.otp.order.converged.domain.response.CustomerOrderInfoResponse;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.TmpAddress;
import com.midea.logistics.otp.order.domain.request.VagueRelationRequest;
import com.midea.logistics.otplbs.fegin.OrderFeign;
import com.midea.logistics.otplbs.manager.helper.CheckCustomerAgingConfigExcel;
import com.midea.logistics.otplbs.manager.helper.ExportOrderBusinessHelper;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *订单导入
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NewCustomerOrderInfoManager {

    private final ApplicationContext applicationContext;
    private final OrderFeign orderFeign;
    private final CustomerOrderInfoManager customerOrderInfoManager;
    private final Validatorhelper validatorhelper;
    private final EbCustomerManager ebCustomerManager;
    private final CdWarehouseManager cdWarehouseManager;
    private final DictHelper dictHelper;
    private final DcAtomicFeign dcAtomicFeign;
    private final CheckCustomerAgingConfigExcel checkCustomerAgingConfigExcel;
    @Autowired
    private CodeHelper codeHelper;

    @Autowired
    private ConvergedCustomerOrderFeign convergedCustomerOrderFeign;
    @Autowired
    private ExportOrderBusinessHelper exportOrderBusinessHelper;

    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) throws Exception {
        log.info("导入开始============================");
        int headerNum = 2;
        List<NewCustomerOrderInfoExcel> list = null;
        try {
            ImportExcel ei = new ImportExcel(file, headerNum, 0,applicationContext);
            int lastCellNum = ei.getLastCellNum();
            if (lastCellNum != 43){
                throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
            }
            list = ei.getDataList(NewCustomerOrderInfoExcel.class, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return JsonResponse.fail("读取文件失败,"+e.getMessage());
        }
        List<NewCustomerOrderInfoExcel> collect = list.stream().filter(e -> ToolUtils.isEmpty(e.getCustomerOrderNo())).collect(Collectors.toList());

        list = list.stream().filter(e -> ToolUtils.isNotEmpty(e.getCustomerOrderNo())).collect(Collectors.toList());
        Map<String, List<NewCustomerOrderInfoExcel>> map = list.stream().collect(Collectors.groupingBy(NewCustomerOrderInfoExcel::getCustomerOrderNo));

        List<NewCustomerOrderInfoExcel> order = Lists.newArrayList();
        List<String> fail = Lists.newArrayList();



        Map<Integer, Future> futureMap = new HashMap<>();
        for (Map.Entry<String, List<NewCustomerOrderInfoExcel>> e : map.entrySet()) {
            headerNum++;
            Integer value = e.getValue().get(0).getRow();
            Map<String, Object> contextMap = ThreadPoolUtil.getContextMap();
            Future<HashMap<String, String>> future = ThreadPoolUtil.submit(() -> {
                ThreadPoolUtil.setContextMap(contextMap);
                return this.checkOrderProperty(e.getValue());
            });
            futureMap.put(value,future);
        }

        HashMap<String, String> relateOrderMap = Maps.newHashMap();
        for (Map.Entry<Integer, Future> future : futureMap.entrySet()) {
            Integer row = future.getKey();
            try {
                HashMap<String, String> result = (HashMap<String, String>)future.getValue().get(30, TimeUnit.SECONDS);
                relateOrderMap.putAll(result);
            } catch (BusinessException ex) {
                fail.add("第" + row + "行数据异常," + ex.getMessage());
                log.error(ex.getMessage(), ex);
                continue;
            }catch (ExecutionException e) {
                fail.add("第" + row + "行数据异常," + e.getCause().getMessage());
                log.error(e.getCause().getMessage(),e.getCause());
                continue;
            }catch (Exception ep) {
                fail.add("第" + row + "行数据异常,");
                log.error(ep.getMessage(), ep);
                continue;
            } finally {
                log.info("导入======第" + row + "行============导入中==========");
            }
        }
        for (NewCustomerOrderInfoExcel newCustomerOrderInfoExcel : collect) {
            fail.add("第" + newCustomerOrderInfoExcel.getRow() + "行数据异常,客户订单号不能为空");
        }

        //关联订单,因为存在多条订单关联同一个模糊订单的情况，所以全部订单保存完后关联订单，
        ArrayList<VagueRelationRequest> vagueRelationRequests = exportOrderBusinessHelper.getVagueRelationRequests(relateOrderMap);
        vagueRelationRequests.forEach(vagueRelationRequest -> {
            convergedCustomerOrderFeign.vagueRelation(vagueRelationRequest);
        });

        log.info("导入结束============================");
        JsonResponse<List<NewCustomerOrderInfoExcel>> jsonResponse = new JsonResponse<>();
        jsonResponse.setData(order);
        if (ToolUtils.isNotEmpty(fail)){
            jsonResponse.setMsg(JSON.toJSONString(fail));
            throw BusinessException.fail(JSON.toJSONString(fail));
        }
        return jsonResponse;
    }



    public HashMap<String, String> checkOrderProperty(List<NewCustomerOrderInfoExcel> request) {
        NewCustomerOrderInfoExcel newCustomerOrderInfoExcel = request.get(0);
        Thread.currentThread().setName("import_"+newCustomerOrderInfoExcel.getCustomerOrderNo()+ "_" +  UUID.randomUUID().toString().replace("-",""));

        List<String> validate = validatorhelper.validate(newCustomerOrderInfoExcel);
        if (!CollectionUtils.isEmpty(validate)) {
            throw new BusinessException(BaseCodeEnum.PARAMETER_INVALID.getCode(), validate.toString());
        }
        CustomerOrderInfoResponse customerOrderInfoResponse = new CustomerOrderInfoResponse();
        BeanUtils.copyProperties(newCustomerOrderInfoExcel, customerOrderInfoResponse);

        this.checkCustomerCode(customerOrderInfoResponse);
        this.checkOrderType(customerOrderInfoResponse);
        String orderType = customerOrderInfoResponse.getOrderType();
        if (!Lists.newArrayList(OrderType.PO.getKey(),OrderType.YS.getKey()).contains(orderType)){
            throw BusinessException.fail(orderType+"的订单类型不能使用");
        }
        this.checkSite(customerOrderInfoResponse);
        this.checkWarehouse(customerOrderInfoResponse);
        this.checkBusinessType(customerOrderInfoResponse);
        this.checkDeliveryType(customerOrderInfoResponse);
        String deliveryType = customerOrderInfoResponse.getDeliveryType();

        this.checkFreightBasis(customerOrderInfoResponse);
        this.checkLoadType(customerOrderInfoResponse);
        this.checkBusinessMode(customerOrderInfoResponse);

        //地址
        CustomerOrderAddress address = new CustomerOrderAddress();
        BeanUtils.copyProperties(newCustomerOrderInfoExcel, address);
        this.completeAddress(customerOrderInfoResponse,address);
        //纯运输单必填
        if (OrderType.YS.getKey().equals(orderType)){
            this.checkSender(address);
        }
        //自提单非必填
        if (!DeliveryType.ZT.getKey().equals(deliveryType)){
            this.checkReceiver(address);
            customerOrderInfoResponse.setSelfMention(CommonConstant.FLAG_NO.toString());
        }else{
            customerOrderInfoResponse.setSelfMention(CommonConstant.FLAG_YES.toString());
        }
        customerOrderInfoResponse.setAddress(address);

        // 客户集拼号校验
        if(StringUtils.isNotBlank(customerOrderInfoResponse.getConsolidationOrderNo())){
            codeHelper.checkConsolidationOrderNo(customerOrderInfoResponse.getConsolidationOrderNo());
        }

        //商品
        ArrayList<CustomerOrderItem> items = Lists.newArrayList();
        for (NewCustomerOrderInfoExcel customerOrderInfoExcel : request) {
            CustomerOrderItem item = new CustomerOrderItem();
            BeanUtils.copyProperties(customerOrderInfoExcel, item);
            this.checkCustomerItemCode(customerOrderInfoResponse,item);
            this.checkCustomerItemStatus(item);
            items.add(item);
        }
        customerOrderInfoResponse.setItems(items);


        CustomerOrderInfoResponse customerOrderInfo = this.createOrUpdateOrder(customerOrderInfoResponse);

        HashMap<String, String> relateOrderMap = this.getRelateOrder(customerOrderInfoResponse, customerOrderInfo.getOrderNo());
        return relateOrderMap;

    }

    public void checkBusinessMode(CustomerOrderInfoResponse customerOrderInfoResponse) {
        String businessModeName = customerOrderInfoResponse.getBusinessMode();
        if (ToolUtils.isNotEmpty(businessModeName)) {
            Optional<BusinessMode> first = Lists.newArrayList(BusinessMode.values()).stream().filter(o -> o.getName().equals(businessModeName)).findFirst();
            if (!first.isPresent()){
                throw BusinessException.fail("所填业务模式不存在");
            }
        }
    }

    public void checkLoadType(CustomerOrderInfoResponse customerOrderInfoResponse) {
        //整车零担
        String loadTypeName = customerOrderInfoResponse.getLoadType();
        if (ToolUtils.isNotEmpty(loadTypeName)) {
            Optional<LoadType> first = Lists.newArrayList(LoadType.values()).stream().filter(o -> o.getValue().equals(loadTypeName)).findFirst();
            if (first.isPresent()){
                customerOrderInfoResponse.setLoadType(first.get().getKey());
            }
        }
    }

    public void checkFreightBasis(CustomerOrderInfoResponse customerOrderInfoResponse) {
        //计费标准
        String freightBasisName = customerOrderInfoResponse.getFreightBasis();
        if (ToolUtils.isNotEmpty(freightBasisName)) {
            Optional<FreightBasisEnum> first = Lists.newArrayList(FreightBasisEnum.values()).stream().filter(o -> o.getValue().equals(freightBasisName)).findFirst();
            if (first.isPresent()){
                customerOrderInfoResponse.setFreightBasis(first.get().getKey());
            }
        }
    }

    public void checkDeliveryType(CustomerOrderInfoResponse customerOrderInfoResponse) {
        Optional<DeliveryType> firstDeliveryType =
            Lists.newArrayList(DeliveryType.values()).stream().filter(o -> o.getValue().equals(customerOrderInfoResponse.getDeliveryTypeName())).findFirst();
        if (firstDeliveryType.isPresent()){
            customerOrderInfoResponse.setDeliveryType(firstDeliveryType.get().getKey());
        }
    }

    public void checkBusinessType(CustomerOrderInfoResponse customerOrderInfoResponse) {
        //业务类型
        String businessTypeName = customerOrderInfoResponse.getBusinessType();
        if (ToolUtils.isNotEmpty(businessTypeName)) {
            Optional<BusinessType> first = Lists.newArrayList(BusinessType.values()).stream().filter(o -> o.getValue().equals(businessTypeName)).findFirst();
            if (first.isPresent()){
                customerOrderInfoResponse.setBusinessType(first.get().getKey());
            }
        }
    }

    public void checkReceiver(CustomerOrderAddress address) {
        //校验收货
        if(ToolUtils.isEmpty(address.getReceiverProvinceCode())){
            throw BusinessException.fail("收货省为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverCityCode())){
            throw BusinessException.fail("收货市为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverDistrictCode())){
            throw BusinessException.fail("收货区县为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverTownCode())){
            throw BusinessException.fail("收货乡镇为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverDetailAddr())){
            throw BusinessException.fail("收货详细地址为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverMobile())){
            throw BusinessException.fail("收货手机号为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverName())){
            throw BusinessException.fail("收货人为空");
        }
    }


    public void checkReceiver2(CustomerOrderAddress address) {

        if(ToolUtils.isEmpty(address.getReceiverMobile())){
            throw BusinessException.fail("收货手机号为空");
        }
        if(ToolUtils.isEmpty(address.getReceiverName())){
            throw BusinessException.fail("收货人为空");
        }
    }


    public void checkSender(CustomerOrderAddress address) {
        //校验发货
        if(ToolUtils.isEmpty(address.getSenderProvinceCode())){
            throw BusinessException.fail("发货省为空");
        }
        if(ToolUtils.isEmpty(address.getSenderCityCode())){
            throw BusinessException.fail("发货市为空");
        }
        if(ToolUtils.isEmpty(address.getSenderDistrictCode())){
            throw BusinessException.fail("发货区县为空");
        }
        if(ToolUtils.isEmpty(address.getSenderTownCode())){
            throw BusinessException.fail("发货乡镇为空");
        }
        if(ToolUtils.isEmpty(address.getSenderDetailAddr())){
            throw BusinessException.fail("发货详细地址为空");
        }
        if(ToolUtils.isEmpty(address.getSenderMobile())){
            throw BusinessException.fail("发货手机号为空");
        }
        if(ToolUtils.isEmpty(address.getSenderName())){
            throw BusinessException.fail("发货人为空");
        }
    }


    public void checkSender2(CustomerOrderAddress address) {
        //校验发货
        if(ToolUtils.isEmpty(address.getSenderProvinceCode())){
            throw BusinessException.fail("发货省为空");
        }
        if(ToolUtils.isEmpty(address.getSenderCityCode())){
            throw BusinessException.fail("发货市为空");
        }

        if(ToolUtils.isEmpty(address.getSenderDetailAddr())){
            throw BusinessException.fail("发货详细地址为空");
        }
        if(ToolUtils.isEmpty(address.getSenderMobile())){
            throw BusinessException.fail("发货手机号为空");
        }
        if(ToolUtils.isEmpty(address.getSenderName())){
            throw BusinessException.fail("发货人为空");
        }
    }

    public void completeAddress(CustomerOrderInfoResponse customerOrderInfoResponse, CustomerOrderAddress address) {
        if (ToolUtils.isNotEmpty(customerOrderInfoResponse.getSenderUnitCode())){
            address.setSenderUnitCode(customerOrderInfoResponse.getSenderUnitCode());
            JsonResponse<PageResponse<ContactsOms>> contacts = dcAtomicFeign.contacts(customerOrderInfoResponse.getSenderUnitCode(),"annto");
            Assert.isTrue(contacts.judgeSuccess(), "获取发货联系人信息失败："+customerOrderInfoResponse.getSenderUnitCode());
            List<ContactsOms> list = contacts.data().getList();
            Assert.notEmpty(list, "联系人编码未在DC维护，请前往【DC(数据中心)-订单配置-收发货联系人】进行查询或清空该字段后填写具体联系人信息");
            ContactsOms contactsOms = list.get(0);
            address.setSenderProvinceCode(contactsOms.getProvinceCode());
            address.setSenderProvinceName(contactsOms.getProvinceName());
            address.setSenderCityCode(contactsOms.getCityCode());
            address.setSenderCityName(contactsOms.getCityName());
            address.setSenderDistrictCode(contactsOms.getDistrictCode());
            address.setSenderDistrictName(contactsOms.getDistrictName());
            address.setSenderTownCode(contactsOms.getTownCode());
            address.setSenderTownName(contactsOms.getTownName());
            address.setSenderDetailAddr(contactsOms.getDetailAddress());
            address.setSenderMobile(contactsOms.getContactMobile());
            address.setSenderTel(contactsOms.getContactMobile());
            address.setSenderName(contactsOms.getContact());
            address.setSenderUnitName(contactsOms.getContactCompanyName());
            customerOrderInfoResponse.setUpperSenderName(contactsOms.getContactCompanyName());
        }else{
            address.setSenderUnitName(customerOrderInfoResponse.getSenderUnitName());
            if(ToolUtils.isNotEmpty(customerOrderInfoResponse.getUpperSenderName())){
                address.setSenderUnitName(customerOrderInfoResponse.getUpperSenderName());
            }
            //customerOrderInfoResponse.setUpperSenderName(customerOrderInfoResponse.getSenderUnitName());
            String sendProvince = address.getSenderProvinceName();
            String sendCity = address.getSenderCityName();
            String sendDistrict = address.getSenderDistrictName();
            String sendTown = address.getSenderTownName();
            TmpAddress tmpSender = checkCustomerAgingConfigExcel.getAddCode(sendProvince, sendCity, sendDistrict, sendTown);
            if (tmpSender != null) {
                String sendProvinceCode = tmpSender.getProvince();
                String sendCityCode = tmpSender.getCity();
                String sendDistrictCode = tmpSender.getDistrict();
                String sendTownCode = tmpSender.getTown();
                address.setSenderProvinceCode(sendProvinceCode);
                address.setSenderCityCode(sendCityCode);
                address.setSenderDistrictCode(sendDistrictCode);
                address.setSenderTownCode(sendTownCode);
                log.info("发货地{}",JSON.toJSONString(tmpSender));
            }
        }

        if (ToolUtils.isNotEmpty(customerOrderInfoResponse.getReceiverUnitCode())){
            address.setReceiverUnitCode(customerOrderInfoResponse.getReceiverUnitCode());
            JsonResponse<PageResponse<ContactsOms>> contacts = dcAtomicFeign.contacts(customerOrderInfoResponse.getReceiverUnitCode(),"annto");
            Assert.isTrue(contacts.judgeSuccess(), "获取收货联系人信息失败："+customerOrderInfoResponse.getReceiverUnitCode());
            List<ContactsOms> list = contacts.data().getList();
            Assert.notEmpty(list, "联系人编码未在DC维护，请前往【DC(数据中心)-订单配置-收发货联系人】进行查询或清空该字段后填写具体联系人信息");
            ContactsOms contactsOms = list.get(0);
            address.setReceiverProvinceCode(contactsOms.getProvinceCode());
            address.setReceiverProvinceName(contactsOms.getProvinceName());
            address.setReceiverCityCode(contactsOms.getCityCode());
            address.setReceiverCityName(contactsOms.getCityName());
            address.setReceiverDistrictCode(contactsOms.getDistrictCode());
            address.setReceiverDistrictName(contactsOms.getDistrictName());
            address.setReceiverTownCode(contactsOms.getTownCode());
            address.setReceiverTownName(contactsOms.getTownName());
            address.setReceiverDetailAddr(contactsOms.getDetailAddress());
            address.setReceiverMobile(contactsOms.getContactMobile());
            address.setReceiverTel(contactsOms.getContactMobile());
            address.setReceiverName(contactsOms.getContact());
            address.setReceiverUnitName(contactsOms.getContactCompanyName());
            customerOrderInfoResponse.setUpperReceiverName(contactsOms.getContactCompanyName());
        }else{
            address.setReceiverUnitName(customerOrderInfoResponse.getReceiverUnitName());
            if(ToolUtils.isNotEmpty(customerOrderInfoResponse.getUpperReceiverName())){
                address.setReceiverUnitName(customerOrderInfoResponse.getUpperReceiverName());
            }
            //customerOrderInfoResponse.setUpperReceiverName(customerOrderInfoResponse.getReceiverUnitName());
            //目的地
            String receiveProvince = address.getReceiverProvinceName();
            String receiveCity = address.getReceiverCityName();
            String receiveDistrict = address.getReceiverDistrictName();
            String receiveTown = address.getReceiverTownName();
            TmpAddress tmpReceiver = checkCustomerAgingConfigExcel.getAddCode(receiveProvince, receiveCity, receiveDistrict, receiveTown);
            //收件地
            if (tmpReceiver != null) {
                String receiverProvinceCode = tmpReceiver.getProvince();
                String receiverCityCode = tmpReceiver.getCity();
                String receiverDistrictCode = tmpReceiver.getDistrict();
                String receiverTownCode = tmpReceiver.getTown();
                address.setReceiverProvinceCode(receiverProvinceCode);
                address.setReceiverCityCode(receiverCityCode);
                address.setReceiverDistrictCode(receiverDistrictCode);
                address.setReceiverTownCode(receiverTownCode);
                log.info("收货地{}",JSON.toJSONString(tmpReceiver));
            }
        }

    }

    public void checkCustomerItemStatus(CustomerOrderItem item) {
        //商品状态  商品状态不能为空
        String itemStatusName = item.getItemStatusName();
        if (StringUtils.isBlank(itemStatusName)) {
            throw BusinessException.fail("商品状态不能为空.客户商品编码：" + item.getCustomerItemCode());
        }
        Optional<ItemStatus> first = Lists.newArrayList(ItemStatus.values()).stream().filter(o -> o.getValue().equals(itemStatusName)).findFirst();
        String itemStatus = first.isPresent() ? first.get().getKey() : dictHelper.getDictCodeByName(ItemStatus.DICT_CODE.getValue(), itemStatusName);
        log.info("=====2=====状态名称:{},转换编码:{}",itemStatusName,itemStatus);
        if (StringUtils.isBlank(itemStatus)) {
            throw BusinessException.fail("该商品状态不存在.客户商品编码：" + item.getCustomerItemCode() + ".商品状态：" + itemStatusName);
        }
        item.setItemStatus(itemStatus);
    }

    public void checkCustomerItemCode(CustomerOrderInfoResponse customerOrderInfoResponse,CustomerOrderItem item) {
        CdCommonMaterial commonMaterial =
            customerOrderInfoManager.getCdcmMaterialNo(customerOrderInfoResponse.getCustomerCode(),
                item, customerOrderInfoResponse.getInOutType());
        if (ToolUtils.isEmpty(commonMaterial) || ToolUtils.isEmpty(commonMaterial.getCdcmMaterialNo())){
            throw BusinessException.fail("基础数据商品为空.客户商品编码" + item.getCustomerItemCode());
        }
        item.setItemCode(commonMaterial.getCdcmMaterialNo());
    }

    public void checkWarehouse(CustomerOrderInfoResponse customerOrderInfoResponse) {
        String whCode = customerOrderInfoResponse.getWhCode();
        //仓库
        String whName = customerOrderInfoResponse.getWhName();
        if (ToolUtils.isNotEmpty(whCode)){
            CdWarehouse cdWarehouseCache = cdWarehouseManager.getCdWarehouseCache(whCode);
            if (cdWarehouseCache != null) {
                if (!customerOrderInfoResponse.getSiteCode().equals(cdWarehouseCache.getSiteCode())) {
                    throw BusinessException.fail("仓库需要在对应服务平台下");
                }
                customerOrderInfoResponse.setWhName(cdWarehouseCache.getCdwhName());
            }
        }else{
            customerOrderInfoResponse.setWhCode(customerOrderInfoManager.queryWhCode(whName));
        }
    }

    public void checkSite(CustomerOrderInfoResponse customerOrderInfoResponse) {
        //平台
        String siteName = customerOrderInfoResponse.getSiteName();
        customerOrderInfoResponse.setSiteCode(customerOrderInfoManager.querySiteCode(siteName));
        String siteCode = customerOrderInfoResponse.getSiteCode();
        if (StringUtils.isBlank(siteCode)) {
            throw BusinessException.fail("服务平台查询结果为空.名称：" + siteName);
        }
    }

    public void checkOrderType(CustomerOrderInfoResponse customerOrderInfoResponse) {
        //订单类型
        String orderTypeName = customerOrderInfoResponse.getOrderType();
        if (ToolUtils.isNotEmpty(orderTypeName)) {
            Optional<OrderType> first = Lists.newArrayList(OrderType.values()).stream().filter(o -> o.getValue().equals(orderTypeName)).findFirst();
            if (first.isPresent()){
                customerOrderInfoResponse.setOrderType(first.get().getKey());
            }else {
                first = Lists.newArrayList(OrderType.values()).stream().filter(o -> o.getKey().equals(orderTypeName)).findFirst();
                if (first.isPresent()){
                    customerOrderInfoResponse.setOrderType(first.get().getKey());
                }
            }
            String orderType = customerOrderInfoResponse.getOrderType();
            if (ToolUtils.isEmpty(orderType)){
                throw BusinessException.fail("订单类型不存在，请检查订单类型");
            }
        }
    }


    public void checkCustomerCode(CustomerOrderInfoResponse customerOrderInfoResponse){
        if (ToolUtils.isNotEmpty(customerOrderInfoResponse.getCustomerCode())) {
            EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(customerOrderInfoResponse.getCustomerCode());
            if ( ebCustomerCache == null) {
                throw BusinessException.fail("客户不存在，请检查客户编码");
            }
            customerOrderInfoResponse.setCustomerName(ebCustomerCache.getEbcuNameCn());
        }
    }


    public CustomerOrderInfoResponse createOrUpdateOrder(CustomerOrderInfoResponse customerOrderInfoResponse) {
        JsonResponse<CustomerOrderInfoResponse> response = orderFeign.addOrUpdateCustomerOrderInfo(customerOrderInfoResponse);
        if (null == response){
            throw BusinessException.fail("订单保存失败");
        }
        if(!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())){
            throw BusinessException.fail(response.getMsg());
        }
        return response.data;
    }

    /**
     * 纯运输订单导入模板，当“是否模糊订单”选择“是”，
     * 则只需要录入以下字段（必填）：客户，客户订单号，
     * 服务平台，发货省，发货市，发货详细地址，发货联系人，发货联系电话，
     * 收货联系人，收货联系电话，总重量，总体积；2)总重量和总体积二选一填写即可，都不填则报错
     * @param customerOrderInfoResponse
     */
    public void savePlanOrder(CustomerOrderInfoResponse customerOrderInfoResponse) {
        customerOrderInfoResponse.setBusinessMode(BusinessMode.B2B.getName());
        customerOrderInfoResponse.setPlanOrderFlag(1);
        customerOrderInfoResponse.setOrderSource(OrderSource.HANDLE.getKey());
        if (ToolUtils.isEmpty(customerOrderInfoResponse.getCustomerCode())) {
            throw BusinessException.fail("客户不能为空");
        }
        this.checkCustomerCode(customerOrderInfoResponse);
        this.checkSite(customerOrderInfoResponse);
        //业务类型
        String deliveryTypeName = customerOrderInfoResponse.getDeliveryType();
        if (ToolUtils.isNotEmpty(deliveryTypeName)){
            customerOrderInfoResponse.setDeliveryTypeName(deliveryTypeName);
        }
        this.checkDeliveryType(customerOrderInfoResponse);
        this.checkBusinessType(customerOrderInfoResponse);

        customerOrderInfoResponse.setOrderType(OrderType.YS.getKey());
        CustomerOrderAddress address = customerOrderInfoResponse.getAddress();
        this.completeAddress(customerOrderInfoResponse,address);
        this.checkSender2(address);
        this.checkReceiver2(address);

        if((customerOrderInfoResponse.getTotalVolume()==null||StringUtils.isEmpty(customerOrderInfoResponse.getTotalVolume().toPlainString()))
            &&(customerOrderInfoResponse.getTotalGrossWeight()==null||StringUtils.isEmpty(customerOrderInfoResponse.getTotalGrossWeight().toPlainString()))
        ){
            throw BusinessException.fail("纯运输订单总重量、总体积不能全部为空");
        }

        this.createOrUpdateOrder(customerOrderInfoResponse);
    }


    private HashMap<String, String> getRelateOrder(CustomerOrderInfoResponse customerOrderInfoResponse,String orderNo) {
        //关联订单
        CustomerOrderInfo relateCustomerOrderInfo = exportOrderBusinessHelper.searchSuitedRelateOrder(customerOrderInfoResponse);
        HashMap<String, String> relateOrderMap = Maps.newHashMap();
        if (relateCustomerOrderInfo != null) {
            relateOrderMap.put(orderNo,relateCustomerOrderInfo.getOrderNo());
        }
        return relateOrderMap;
    }
}
