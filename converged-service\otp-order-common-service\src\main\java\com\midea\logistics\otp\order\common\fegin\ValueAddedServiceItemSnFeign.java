package com.midea.logistics.otp.order.common.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.bean.ValueAddedServiceItemSn;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ValueAddedServiceItemSnFeign
 * Author: lindq2
 * Date: 2019-5-14 11:40:46
 * Description:增值服务单商品SN feign
 */
@FeignClient(value = "logistics-otp-order-service",url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface ValueAddedServiceItemSnFeign {

    @RequestMapping(value = "/valueAddedServiceItemSn", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ValueAddedServiceItemSn valueAddedServiceItemSn);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItemSn/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItemSn/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ValueAddedServiceItemSn valueAddedServiceItemSn);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItemSn/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/valueAddedServiceItemSns", method = RequestMethod.GET)
    JsonResponse<PageResponse<ValueAddedServiceItemSn>> search(@SpringQueryMap ValueAddedServiceItemSn valueAddedServiceItemSn);

    @RequestMapping(value = "/valueAddedServiceItemSns/searchAll", method = RequestMethod.POST)
    JsonResponse<PageResponse<ValueAddedServiceItemSn>> searchAll(@RequestBody ValueAddedServiceItemSn valueAddedServiceItemSn);
}
