package com.midea.logistics.otp.order.common.fegin.bop;

import com.midea.logistics.otp.common.aop.MyFeignRetry;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeServerRequestDto;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @description: zeebe相关
 */
@FeignClient(value = "logistics-bop-service")
public interface ZeebeBopFeign {

    /**
     * 查询es  zeebe流程
     */
    @PostMapping(value = "/inner/T201904230000000014/udpApi/searchIncident")
    @MyFeignRetry(isRetry = false , isBop = true)
    JsonResponse searchIncident(@RequestBody ZeebeServerRequestDto zeebeRequestDto);

    /**
     * bop调审核服务审核
     */
    @PostMapping(value = "/inner/T201904230000000014/udpApi/orderverify")
    @MyFeignRetry(isRetry = false , isBop = true)
    JsonResponse orderverify(@RequestBody List<String> orderNos);
}
