package com.midea.logistics.otp.orderverify.mq.consumer;

import java.util.UUID;

import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.otp.constants.MessageQueueDefine;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.orderverify.service.IOrderInfoService;
import com.midea.logistics.otp.orderverify.service.LoadingFeeAnalyzeService;
import com.midea.logistics.otp.orderverify.service.TaskService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.transactionservice.mq.MqConsumer;
import com.mideaframework.transactionservice.mq.consumer.MQMessageHandler;

import lombok.extern.slf4j.Slf4j;

/**
* @description: 子单触发流程
* @author: lindq
* @createtime: 2021/10/8 14:39
*/
@Component
@Slf4j
@MqConsumer(topic = MessageQueueDefine.EXPRESS_ORDER_FLOW)
public class ExpressOrderFlowConsumer implements MQMessageHandler {

    @Autowired
    private LoadingFeeAnalyzeService loadingFeeAnalyze;
    @Autowired
    private IOrderInfoService orderInfoService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private OrderverifyHelper orderverifyHelper;

    @Override
    public void handlerMessage(MessageExt messageExt) {

        MDC.put("traceId", UUID.randomUUID().toString());
        String orderNo = new String(messageExt.getBody());
        OrderInfoExt orderInfo = orderverifyHelper.getOrderInfoExt(orderNo);

        log.info(" mq消息 "+ MessageQueueDefine.EXPRESS_ORDER_FLOW +"   消费内容 message =  {}", JSON.toJSON(orderInfo));

        if (StringUtils.isEmpty(orderNo)) {
            throw BusinessException.fail("订单号不能为空");
        }

        //快递审核子流程
        orderInfoService.expressSubFlowVerify(orderInfo);
    }
}


