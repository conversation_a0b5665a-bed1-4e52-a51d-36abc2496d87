package com.midea.logistics.otplbs.fegin.agg;


import cn.hutool.db.PageResult;
import com.midea.logistics.otp.bean.agg.AgingProductDatail;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "AgingProductDatailCommonFeign")
public interface AgingProductDatailCommonFeign {

    @GetMapping("/aging/product/datail/list")
    JsonResponse<List<AgingProductDatail>> agingProductDatailList(@SpringQueryMap AgingProductDatail entity);

    @GetMapping("/aging/product/datail/page")
    JsonResponse agingProductDatailPage(@SpringQueryMap AgingProductDatail entity);

    @GetMapping("/aging/product/datail/count")
    JsonResponse<BaseDomain> agingProductDatailCount(@SpringQueryMap AgingProductDatail entity);

    @GetMapping("/aging/product/datail/detail")
    JsonResponse<AgingProductDatail> agingProductDatailDetail(@RequestParam("id") Long id);

    @GetMapping("/aging/product/datail/detail")
    JsonResponse<AgingProductDatail> agingProductDatailDetail(@SpringQueryMap AgingProductDatail entity);

    @PostMapping("/aging/product/datail/add")
    JsonResponse<BaseDomain> agingProductDatailAdd(@RequestBody AgingProductDatail entity);

    @PutMapping("/aging/product/datail/update")
    JsonResponse<Integer> agingProductDatailUpdate(@RequestBody AgingProductDatail entity);

    @DeleteMapping("/aging/product/datail/remove")
    JsonResponse<Integer> agingProductDatailRemove(@RequestBody AgingProductDatail entity);
    @PostMapping("/custom/agingProductDatail/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<AgingProductDatail> list);
}

