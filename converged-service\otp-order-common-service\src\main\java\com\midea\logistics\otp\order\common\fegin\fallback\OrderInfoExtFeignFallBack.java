package com.midea.logistics.otp.order.common.fegin.fallback;

import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.common.fegin.OrderInfoExtFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年07月03日 上午17:16
 */
@Slf4j
public class OrderInfoExtFeignFallBack implements FallbackFactory<OrderInfoExtFeign> {
    @Override
    public OrderInfoExtFeign create(Throwable throwable) {
        log.warn("OrderInfoExtFeign fail {}" ,throwable.getMessage());
        return new OrderInfoExtFeign() {
            @Override
            public JsonResponse<OrderInfo> getOrderInfoByOrderNo(String orderNo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> countOrderInfoByIndex(OrderInfo orderInfo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }
        };
    }
}
