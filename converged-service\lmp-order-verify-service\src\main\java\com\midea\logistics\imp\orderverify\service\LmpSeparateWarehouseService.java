package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.converged.domain.request.SeparateWarehouseConfirmRequest;
import com.midea.logistics.otp.order.converged.domain.response.SeparateWarehouseSearchResponse;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LmpSeparateWarehouseService {

    SeparateWarehouseSearchResponse search(String orderNo);

    List<String> confirm(String orderNo, boolean compartFlag, List<SeparateWarehouseConfirmRequest> separateWarehouseConfirmRequestList);

    boolean cancel(List<String> orderNos);

    /**
     * 自动分仓
     *
     * @param customerOrderInfoExt
     * @return
     */
    CustomerOrderInfoExt automatic(CustomerOrderInfoExt customerOrderInfoExt);
	
	void createOrderExtend(String orderNo, List<OrderInfo> orderInfoList, CustomerOrderInfo customerOrderInfo);
	
	/**
     * 同仓校验
     *
     * @param customerCode
     * @param targetCustomerCode
     * @param whCodes
     * @return
     */
    List<String> validateSameWarehouseCode(String customerCode, String targetCustomerCode, List<String> whCodes);

    boolean callCenterInv(String orderNo);
}
