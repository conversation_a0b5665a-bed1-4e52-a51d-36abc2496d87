package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

/**
 * ClassName: QueryInstallItemDto
 * Description:查询是否需要安装明细
 *
 * <AUTHOR>
 * @date 2023/9/5 17:37
 */
@Data
public class QueryInstallItemDto {

    /**
     * 客户商品编码
     */
    private String customerItemCode;

    /**
     * 安装标识
     */
    private Integer installFlag;

    /**
     * 安装标识
     */
    private Integer ifInstall;

}
