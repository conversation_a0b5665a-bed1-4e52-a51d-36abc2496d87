package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.mideaframework.core.web.JsonResponse;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * Author:   zhangjh
 * Date:     2019-06-25 10:50
 * Description: 货权转移
 */
public interface CargoRightTransferService {

//    /**
//     * @description: CIMS出库货权转移校验
//     * @param: [orderNo]
//     * @return: void
//     * @author: 陈永培
//     * @createtime: 2019/6/6 17:04
//     */
//    FlowListenerParam cimsOutCargoRightTransfer(String orderNo);

//    /**
//     * @description: CCS、CIMS入库单货权转移校验【流程入口】：
//     * @param: [orderNo]
//     * @return: void
//     * @author: 王开存
//     * @createtime: 2019/6/23 18:15
//     */
//    FlowListenerParam auditItemsByFlow(String orderNo);

    /**
     * @description: CCS、CIMS入库单货权转移校验：
     * @param: [orderNo]
     * @return: void
     * @author: 陈永培
     * @createtime: 2019/6/6 17:04
     */
//    FlowListenerParam auditItems(List<String> orderNos) throws BusinessException;

//    /**
//     * 货权转移
//     *
//     * @return
//     */
//    FlowListenerParam cargoRightTransfer(String orderNo);



    /**
     * 货权转移
     *
     * @return
     */
    CustomerOrderInfoExt cargoRightTransfer(CustomerOrderInfoExt customerOrderInfoExt);

//    /**



    List<CdWarehouse> getCollabWhBySiteCode(String siteCode);
}