package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.InvStockDiff;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: InvStockDiffFeign
* Author: james
* Date: 2020-7-1 15:15:59
* Description:仓库库存差异 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface InvStockDiffFeign {

    /**
    *
    *
    */
    @RequestMapping(value = "/invStockDiff", method = RequestMethod.POST)
    JsonResponse create(@RequestBody InvStockDiff invStockDiff);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/invStockDiff/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/invStockDiff/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody InvStockDiff invStockDiff);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/invStockDiff/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/invStockDiffs", method = RequestMethod.POST)
    JsonResponse search(@RequestBody InvStockDiff invStockDiff);
}
