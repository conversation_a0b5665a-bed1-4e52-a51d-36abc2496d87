package com.midea.logistics.otp.orderverify.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.midea.logistics.lcp.domain.order.bean.PlaceBean;
import com.midea.logistics.otp.bean.AddressDto;
import com.midea.logistics.otp.bean.AddressResult;
import com.midea.logistics.otp.bean.GisDto;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.service.EncyptAddressService;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.AddressHelper;
import com.midea.logistics.otp.order.common.helper.GisHelper;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;

import lombok.extern.slf4j.Slf4j;
import static com.midea.logistics.otp.common.constants.CommonConstant.SUCCESS;
import static com.midea.logistics.otp.order.common.helper.AddressHelper.*;

/**
* @description: 上游下单的地址都加密解析逻辑
* @author: 陈永培
* @createtime: 2020/10/27 20:51
*/
@Service
@Slf4j
public class ExpressAddressImpl implements EncyptAddressService {


    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private AddressHelper addressHelper;
    @Autowired
    private GisHelper gisHelper;
    
    public static final String IN3_EXPRESS_GIS_CUSTOMER = "IN3_EXPRESS_GIS_CUSTOMER";
    
    @Override
    public AddressResult doWork(AddressDto dto) {

        String sourceFlag = dto.getSourceFlag();

        /**
         *  1.三级替换(DC地址映射)
         */
        addressHelper.setAddressMapping(dto);

        //重新获取一下，有可能DC对照上，然后替换掉
        String provinceName = dto.getProvinceName();
        String cityName = dto.getCityName();
        String districtName = dto.getDistrictName();
        boolean dcMapping = dto.isDcMapping();

        /**
         * 2.DC对照成功，用省、市、县Code（三级）去匹配一下标准库，主要看code有没有被禁用
         */
        if (dcMapping) {
            String provinceCode = dto.getProvinceCode();
            String cityCode = dto.getCityCode();
            String districtCode = dto.getDistrictCode();
            try {
                addressHelper.getAbleNameByCode(provinceCode);
                addressHelper.getAbleNameByCode(cityCode);
                boolean skipDistrict = dto.isSkipDistrict();
                if (! skipDistrict) {
                    addressHelper.getAbleNameByCode(districtCode);
                }
            } catch (Exception e) {
                throw BusinessException.fail(e.getMessage() + ",请先修改地址映射基础数据再手工审核订单");
            }
            //不需要掉gis，直接返回就好了
            return  AddressResult.success(dto);
        }

        /**
         * 3.DC对照不成功，用省市区Name（三级）去匹配标准库
         */
        AddressResult addressResult = null;
        try {
            addressResult = threeContrast(dto);
            //成功，直接返回
            if (null != addressResult && addressResult.getExplainStatus().equals(SUCCESS)) {
                return addressResult;
            }
            //不成功，判断是gis解析的客户,不是的话，直接返回报错
            if (!gisCustomer(dto))
                return addressResult;
        } catch (Exception e) {
            if (!gisCustomer(dto))
                throw BusinessException.fail(e.getMessage());
        }
    
        /**
         * 4.Gis解析，进行2级防呆
         */
        log.info("express  orderNo{},gis explain...",dto.getOrderNo());
        GisDto gisDto = null;
        String detailAddr = splicingAddress(dto);
        try {
            gisDto = gisHelper.serchGisObject(detailAddr);
        } catch (Exception ex) {
            throw BusinessException.fail("地图解析异常，"+ex.getMessage()+"，请在订单中心纠错");
        }
    
        String gisProvince = gisDto.getProvince();
        String gisCity = gisDto.getCity();
        
        String msg = "";
    
        if (StringUtils.isNotEmpty(provinceName) && provinceName.length()>=2 && ! getTwoAddr(provinceName).equals(getTwoAddr(gisProvince))) {
            throw BusinessException.fail("【地址解析】防呆未通过，"+sourceFlag+"省【"+getTwoAddr(provinceName)+"】，百度地图("+gisDto.getApiType()+")返回的省【"+getTwoAddr(gisProvince)+"】") ;
        }
        
        if (StringUtils.isNotEmpty(cityName)  && cityName.length()>=2 &&  ! getTwoAddr(cityName).equals(getTwoAddr(gisCity))) {
            throw BusinessException.fail("【地址解析】防呆未通过，"+sourceFlag+"市【"+getTwoAddr(cityName)+"】，百度地图("+gisDto.getApiType()+")返回的市【"+getTwoAddr(gisCity)+"】") ;
        }
    
        GisDto.setCode(dto,gisDto);
        
        return AddressResult.success(dto);
    }
    
    /**
     *
     * @param dto
     * @return
     */
    private boolean gisCustomer(AddressDto dto) {
        boolean gisCustomerCode = dictHelper.isInDict(dto.getCustomerCode(), IN3_EXPRESS_GIS_CUSTOMER);
        if (gisCustomerCode) {
            return true;
        }
        return false;
    }
    

    /**
     * @description: 三级对照
     * @param: [provinceName, cityName, districtName, townName]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/10/28 17:51
     */
    private AddressResult threeContrast(AddressDto dto) {

        String provinceName = dto.getProvinceName();
        String cityName = dto.getCityName();
        String districtName = dto.getDistrictName();
        String sourceFlag = dto.getSourceFlag();

        String tip =  ",请先维护地址映射基础数据再手审核订单";

        if (StringUtils.isEmpty(provinceName)) {
            return  AddressResult.fail(sourceFlag+"省为空") ;
        }
        if (StringUtils.isEmpty(cityName)) {
            return  AddressResult.fail(sourceFlag+"市为空") ;
        }
        if (StringUtils.isEmpty(districtName)) {
            return  AddressResult.fail(sourceFlag+"区为空") ;
        }


        /**
         * 一、简单的标准库获取，不进行防呆校验，也不调用GIS解析经纬度
         */

        //省(前两位)
        PlaceBean standerProvince = addressHelper.getAbleNameByTwoNameNotThrow(provinceName, PLACE_PROVINCE, null);
        String provinceParentCode = null;
        if (null != standerProvince) {
            provinceParentCode = standerProvince.getCode();
            dto.setProvinceCode(provinceParentCode);
            dto.setProvinceName(standerProvince.getNameCn());
        }else{
            return  AddressResult.fail(sourceFlag+"省【"+provinceName+"】不存在"+tip) ;
        }


        //市(前两位)
        PlaceBean standerCity = addressHelper.getAbleNameByTwoNameNotThrow(cityName, PLACE_CITY, provinceParentCode);
        String cityParentCode = null;
        if (null != standerCity) {
            cityParentCode = standerCity.getCode();
            dto.setCityCode(cityParentCode);
            dto.setCityName(standerCity.getNameCn());
        }else{
            return  AddressResult.fail(sourceFlag+"市【"+provinceName + "-" +cityName+"】不存在"+tip);
        }

        //2021年10月20日15:37:37 庐哥：如果区  以（[其它、其它开头的] || 市和区一样），不需要改动区的东西
        boolean isIn = CommonConstant.isIn(OTHER,districtName,true);
        if (isIn || ToolUtils.isEqual(cityName,districtName)){
            dto.setSkipDistrict(true);
            log.info("threeContrast->other orderNo:{},isIn:{},cityName：{},districtName：{}",dto.getOrderNo(),isIn,cityName,districtName);
            //不需要处理区地址，直接返回就好了
            return  AddressResult.success(dto);
        }

        //区
        PlaceBean standerDistrict = addressHelper.getAbleNameByTwoNameNotThrow(districtName, PLACE_DISTRICT, cityParentCode);
        String districtParentCode = null;
        if (null != standerDistrict) {
            districtParentCode = standerDistrict.getCode();
            dto.setDistrictCode(districtParentCode);
            dto.setDistrictName(standerDistrict.getNameCn());
        }else{
            return  AddressResult.fail(sourceFlag+"区【"+provinceName + "-" +cityName+"-"+districtName+"】不存在"+tip);
        }

        return  AddressResult.success(dto);
    }

}
