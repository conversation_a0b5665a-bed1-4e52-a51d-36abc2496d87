package com.midea.logistics.imp.orderverify.mq.producer;


import com.alibaba.fastjson.JSON;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderExceptionFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoFeign;
import com.midea.logistics.otp.constants.MessageQueueDefine;
import com.midea.logistics.otp.enums.ExceptionType;
import com.midea.logistics.otp.enums.OrderExceptionStatus;
import com.midea.logistics.otp.enums.OrderOperateType;
import com.midea.logistics.otp.order.domain.bean.OrderException;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.mideaframework.transactionservice.mq.MqFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderAgingPushProducer {

    @Autowired
    private MqFactory mqFactory;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private OrderExceptionFeign orderExceptionFeign;

    public void send(OrderInfo orderInfo) {
        if (null == orderInfo) {
            return;
        }
        String key = "order:aging:push:" + orderInfo.getOrderNo();
        String topic = MessageQueueDefine.ORDER_AGING_PUSH_TOPIC;
        //发送报文
        try {
            boolean result = mqFactory.getMqProducer().sendMsg(new Message(topic, null, key, JSON.toJSONString(orderInfo).getBytes()));
            if (!result) {
                log.info("时效推送mq发送失败.订单号:{}",orderInfo.getOrderNo());
                orderInfoFeign.orderAgingPush(orderInfo);
            }
        }catch (MQClientException e) {
            log.info("时效推送mq发送失败.订单号[{}]:{}",orderInfo.getOrderNo(),e.getMessage());
            saveExceptionLog(orderInfo,e.getMessage());
        }
    }

    private void saveExceptionLog(OrderInfo orderInfo, String msg){
        OrderException orderException = new OrderException();
        BeanUtils.copyProperties(orderInfo, orderException);
        orderException.setMessage(JSON.toJSONString(orderInfo));
        String errMsg = msg.length()>500?msg.substring(0,500):msg;
        orderException.setExceptionDesc(errMsg);
        orderException.setExceptionStatus(OrderExceptionStatus.NEW.getKey());
        orderException.setExceptionType(ExceptionType.ORDER_AGING_PUSH_FAILED.getKey());
        orderException.setOperateType(OrderOperateType.ORDER_AGING_PUSH.getKey());
        orderException.setOperateContent(OrderOperateType.ORDER_AGING_PUSH.getValue());
        orderExceptionFeign.create(orderException);
    }

}
