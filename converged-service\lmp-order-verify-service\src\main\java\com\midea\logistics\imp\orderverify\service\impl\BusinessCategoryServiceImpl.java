package com.midea.logistics.imp.orderverify.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.midea.logistics.imp.orderverify.helper.LmpDeliveryTypeHelper;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.BusinessCategoryService;
import com.midea.logistics.otp.bean.BusinessCateoryDto;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderAddressFeign;
import com.midea.logistics.otp.enums.BusinessCategory;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.BusinessCategoryHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class BusinessCategoryServiceImpl implements BusinessCategoryService {

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;
    @Autowired
    private BusinessCategoryHelper businessCategoryHelper;
    @Autowired
    private LmpDeliveryTypeHelper lmpDeliveryTypeHelper;

    @Override
    @ZeebeFlow("BUSINESS_CATEGORY")
    public CustomerOrderInfoExt businessCategory(CustomerOrderInfoExt customerOrderInfo) {
        if(customerOrderInfo == null || StringUtils.isEmpty(customerOrderInfo.getOrderNo())) {
            throw BusinessException.fail("订单号为空");
        }
        //2023年6月28日10:40:43 新隆下单过来的配送方式是假的 地址解析完之后要重新设置
        this.resetDeliveryType(customerOrderInfo);

        String orderNo = customerOrderInfo.getOrderNo();
        log.info("businessCategory customerOrderNo:{}",customerOrderInfo.getCustomerOrderNo());

        //模糊订单无需解析业务大类
        if(null != customerOrderInfo.getPlanOrderFlag() && customerOrderInfo.getPlanOrderFlag().intValue() == CommonEnum.YES.getValue().intValue()){
            FlowListenerParam.success("模糊订单，无需解析业务大类");
            return  customerOrderInfo;
        }
        if(StringUtils.isNotBlank(customerOrderInfo.getBusinessCategory())){
            BusinessCategory businessCategory = BusinessCategory.getKey(customerOrderInfo.getBusinessCategory());
            FlowListenerParam.success("存在业务大类，无需解析:"+(businessCategory==null?customerOrderInfo.getBusinessCategory():businessCategory.getValue()));
            return  customerOrderInfo;
        }
        if (SourceSystem.WMS.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.isTIOrder(customerOrderInfo.getOrderType())){
            FlowListenerParam.success("WMS 的 TI 订单类型无需解析");
            return  customerOrderInfo;
        }

        BusinessCateoryDto businessCateoryDto = new BusinessCateoryDto();
        BeanUtils.copyProperties(customerOrderInfo,businessCateoryDto);
        JsonResponse<CustomerOrderAddress> jsonResponse =  customerOrderAddressFeign.findByOrderNo(orderNo);
        if(jsonResponse == null || !BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode()) || null == jsonResponse.getData()){
            BusinessException.fail("根据orderNo获取地址异常:"+ JSON.toJSONString(jsonResponse)+"单号："+orderNo);
        }
        CustomerOrderAddress customerOrderAddress = jsonResponse.getData();
        businessCateoryDto.setSenderProvinceCode(customerOrderAddress.getSenderProvinceCode());
        businessCateoryDto.setReceiverProvinceCode(customerOrderAddress.getReceiverProvinceCode());

        businessCateoryDto.setCustomerOrderInfo(customerOrderInfo);
        BusinessCategory businessCategory = businessCategoryHelper.getBusinessCategory(businessCateoryDto);
        if(businessCategory!=null){
            customerOrderInfo.setBusinessCategory(businessCategory.getKey());
            FlowListenerParam.success(businessCategory.getValue());
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo,"业务大类解析");
            return customerOrderInfo;
        }

        FlowListenerParam.success("");
        return  customerOrderInfo;
    }


    /**
     * 新隆查配送方式配置 设置配送方式
     * @param customerOrderInfo
     */
    private void resetDeliveryType(CustomerOrderInfoExt customerOrderInfo) {
        String sourceSystem = customerOrderInfo.getSourceSystem();
        String orderType = customerOrderInfo.getOrderType();
        List<String> sourceSystems = Arrays.asList(SourceSystem.XINLONG.getKey());
        List<String> orderTypes = Arrays.asList(OrderType.PO.getKey(), OrderType.RI.getKey());
        boolean isReset = sourceSystems.contains(sourceSystem) && orderTypes.contains(orderType);
        if (!isReset){
            return;
        }
        String customerCode = customerOrderInfo.getCustomerCode();
        String siteCode = customerOrderInfo.getSiteCode();
        String whCode = customerOrderInfo.getWhCode();
        String businessMode = customerOrderInfo.getBusinessMode();
        CustomerOrderAddress customerOrderAddress = customerOrderInfo.getCustomerOrderAddress();
        if (StringUtils.isAnyEmpty(customerCode,siteCode,whCode,businessMode) || ObjectUtil.isEmpty(customerOrderAddress)){
            return;
        }

        ShippingTypeRule deliveryRule = lmpDeliveryTypeHelper.getDeliveryRule(customerOrderInfo, customerOrderAddress, businessMode, true);
        if (null != deliveryRule && !StringUtils.isEmpty(deliveryRule.getDeliveryType())){
            customerOrderInfo.setDeliveryType(deliveryRule.getDeliveryType());
        }
    }

}
