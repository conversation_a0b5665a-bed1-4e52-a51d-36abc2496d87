<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all" name="父订单流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1vr9m96</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0d15yei" name="商品确认">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0pi4um7</bpmn:incoming>
      <bpmn:incoming>Flow_15rp53s</bpmn:incoming>
      <bpmn:outgoing>Flow_0echylv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0echylv" sourceRef="Activity_0d15yei" targetRef="Gateway_1wccxzd" />
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_1fugrxi</bpmn:incoming>
      <bpmn:incoming>Flow_1nd5zoz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1vr9m96" sourceRef="StartEvent_1" targetRef="Activity_1k6ho42" />
    <bpmn:exclusiveGateway id="Gateway_1rjzhmw" name="是否箱包转换" default="Flow_15rp53s">
      <bpmn:incoming>Flow_0bqauri</bpmn:incoming>
      <bpmn:outgoing>Flow_0it4yuk</bpmn:outgoing>
      <bpmn:outgoing>Flow_15rp53s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0it4yuk" name="yes" sourceRef="Gateway_1rjzhmw" targetRef="Activity_1wq7s30">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.sourceSystem!=null and customerOrderInfo.sourceSystem="KingDee-SD")=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0pi4um7" sourceRef="Activity_1wq7s30" targetRef="Activity_0d15yei" />
    <bpmn:sequenceFlow id="Flow_15rp53s" name="no" sourceRef="Gateway_1rjzhmw" targetRef="Activity_0d15yei" />
    <bpmn:serviceTask id="Activity_1wq7s30" name="箱包转换">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/luggage/conversion" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0it4yuk</bpmn:incoming>
      <bpmn:outgoing>Flow_0pi4um7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1oyv8yd" sourceRef="Activity_1k6ho42" targetRef="Activity_0ur58zi" />
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1vr9m96</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyv8yd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0a0g75l" name="MIP流程">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/mipExecutionAudit" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zdzxft</bpmn:incoming>
      <bpmn:outgoing>Flow_0ap55e3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_12lns6w" name="是否走mip流程" default="Flow_1ajskls">
      <bpmn:incoming>Flow_01y8j5h</bpmn:incoming>
      <bpmn:incoming>Flow_02di4fa</bpmn:incoming>
      <bpmn:outgoing>Flow_1ajskls</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zdzxft</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ajskls" name="no" sourceRef="Gateway_12lns6w" targetRef="Activity_0nccbee" />
    <bpmn:sequenceFlow id="Flow_0zdzxft" name="yes" sourceRef="Gateway_12lns6w" targetRef="Activity_0a0g75l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.mipFlag!=null and customerOrderInfo.mipFlag=1)=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0nccbee" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ajskls</bpmn:incoming>
      <bpmn:incoming>Flow_0ap55e3</bpmn:incoming>
      <bpmn:outgoing>Flow_07qlm7g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ap55e3" sourceRef="Activity_0a0g75l" targetRef="Activity_0nccbee" />
    <bpmn:serviceTask id="Activity_119zmlg" name="仓间调拨解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/allocationParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1n6udmd</bpmn:incoming>
      <bpmn:outgoing>Flow_0v8tmtg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="四级地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/addressResolving" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k9k1b7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wi0xbe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kptmf2" name="货权转移校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/cargo/right/transfer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p7943l</bpmn:incoming>
      <bpmn:outgoing>Flow_1wuusbt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0kj1gz5</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1g2qvk8" name="是否仓间调拨" default="Flow_1odmwry">
      <bpmn:incoming>Flow_07qlm7g</bpmn:incoming>
      <bpmn:outgoing>Flow_1n6udmd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1odmwry</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07qlm7g" sourceRef="Activity_0nccbee" targetRef="Gateway_1g2qvk8" />
    <bpmn:sequenceFlow id="Flow_1n6udmd" name="yes" sourceRef="Gateway_1g2qvk8" targetRef="Activity_119zmlg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AI")))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_04dmfyf" name="是否地址解析" default="Flow_191u9lk">
      <bpmn:incoming>Flow_1odmwry</bpmn:incoming>
      <bpmn:incoming>Flow_0v8tmtg</bpmn:incoming>
      <bpmn:outgoing>Flow_191u9lk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k9k1b7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1odmwry" sourceRef="Gateway_1g2qvk8" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_0v8tmtg" sourceRef="Activity_119zmlg" targetRef="Gateway_04dmfyf" />
    <bpmn:exclusiveGateway id="Gateway_0fo5mo8" name="是否货权转移" default="Flow_1o83mga">
      <bpmn:incoming>Flow_191u9lk</bpmn:incoming>
      <bpmn:incoming>Flow_1wi0xbe</bpmn:incoming>
      <bpmn:outgoing>Flow_1o83mga</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p7943l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_191u9lk" name="no" sourceRef="Gateway_04dmfyf" targetRef="Gateway_0fo5mo8" />
    <bpmn:sequenceFlow id="Flow_1wi0xbe" sourceRef="Activity_1i377su" targetRef="Gateway_0fo5mo8" />
    <bpmn:exclusiveGateway id="Gateway_1b565p2" name="是否分仓拆单">
      <bpmn:incoming>Flow_1o83mga</bpmn:incoming>
      <bpmn:incoming>Flow_1wuusbt</bpmn:incoming>
      <bpmn:outgoing>Flow_0kj1gz5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o83mga" name="no" sourceRef="Gateway_0fo5mo8" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_1k9k1b7" sourceRef="Gateway_04dmfyf" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and ((customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT") or (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO"))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1p7943l" name="yes" sourceRef="Gateway_0fo5mo8" targetRef="Activity_0kptmf2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CAINIAOPO","CCSJDPI","CIMSPO","CIMSPI","CIMSRI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wuusbt" sourceRef="Activity_0kptmf2" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_0kj1gz5" name="no" sourceRef="Gateway_1b565p2" targetRef="Activity_0uruim6" />
    <bpmn:sequenceFlow id="Flow_1nd5zoz" sourceRef="Activity_0b21p68" targetRef="Event_09zbrsb" />
    <bpmn:sequenceFlow id="Flow_0bqauri" sourceRef="Activity_0ur58zi" targetRef="Gateway_1rjzhmw" />
    <bpmn:serviceTask id="Activity_0ur58zi" name="订单信息校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/orderInfoConfirm" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1oyv8yd</bpmn:incoming>
      <bpmn:outgoing>Flow_0bqauri</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1tp1sro" name="手工分仓" default="Flow_02dlmfn">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
      <bpmn:outgoing>Flow_02dlmfn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fugrxi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Gateway_1tp1sro" />
    <bpmn:sequenceFlow id="Flow_02dlmfn" name="no" sourceRef="Gateway_1tp1sro" targetRef="Activity_0b21p68" />
    <bpmn:sequenceFlow id="Flow_1fugrxi" name="yes" sourceRef="Gateway_1tp1sro" targetRef="Event_09zbrsb">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo.isHandle</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0b21p68" name="客户订单审核完成&#10;触发子流程">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/customerOrderVerifyEnd" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo.orderNo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_02dlmfn</bpmn:incoming>
      <bpmn:outgoing>Flow_1nd5zoz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1wccxzd" name="B2C" default="Flow_01y8j5h">
      <bpmn:incoming>Flow_0echylv</bpmn:incoming>
      <bpmn:outgoing>Flow_01y8j5h</bpmn:outgoing>
      <bpmn:outgoing>Flow_1xpxeay</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01y8j5h" name="no" sourceRef="Gateway_1wccxzd" targetRef="Gateway_12lns6w" />
    <bpmn:sequenceFlow id="Flow_1xpxeay" name="yes" sourceRef="Gateway_1wccxzd" targetRef="Activity_039ag6m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo.businessMode="B2C")=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_02di4fa" sourceRef="Activity_039ag6m" targetRef="Gateway_12lns6w" />
    <bpmn:serviceTask id="Activity_039ag6m" name="件型计算">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/pieceAnalysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xpxeay</bpmn:incoming>
      <bpmn:outgoing>Flow_02di4fa</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all">
      <bpmndi:BPMNEdge id="Flow_02di4fa_di" bpmnElement="Flow_02di4fa">
        <di:waypoint x="993" y="154" />
        <di:waypoint x="1013" y="154" />
        <di:waypoint x="1013" y="225" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xpxeay_di" bpmnElement="Flow_1xpxeay">
        <di:waypoint x="867" y="205" />
        <di:waypoint x="867" y="154" />
        <di:waypoint x="893" y="154" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="874" y="173" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01y8j5h_di" bpmnElement="Flow_01y8j5h">
        <di:waypoint x="892" y="230" />
        <di:waypoint x="1008" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="944" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fugrxi_di" bpmnElement="Flow_1fugrxi">
        <di:waypoint x="1110" y="445" />
        <di:waypoint x="1110" y="500" />
        <di:waypoint x="780" y="500" />
        <di:waypoint x="780" y="438" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1024" y="485" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02dlmfn_di" bpmnElement="Flow_02dlmfn">
        <di:waypoint x="1085" y="420" />
        <di:waypoint x="980" y="420" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1034" y="429" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="1181" y="420" />
        <di:waypoint x="1135" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bqauri_di" bpmnElement="Flow_0bqauri">
        <di:waypoint x="502" y="230" />
        <di:waypoint x="549" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nd5zoz_di" bpmnElement="Flow_1nd5zoz">
        <di:waypoint x="880" y="420" />
        <di:waypoint x="798" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kj1gz5_di" bpmnElement="Flow_0kj1gz5">
        <di:waypoint x="1315" y="420" />
        <di:waypoint x="1281" y="420" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1303" y="452" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wuusbt_di" bpmnElement="Flow_1wuusbt">
        <di:waypoint x="1379" y="530" />
        <di:waypoint x="1340" y="530" />
        <di:waypoint x="1340" y="445" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p7943l_di" bpmnElement="Flow_1p7943l">
        <di:waypoint x="1530" y="445" />
        <di:waypoint x="1530" y="530" />
        <di:waypoint x="1479" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1537" y="485" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9k1b7_di" bpmnElement="Flow_1k9k1b7">
        <di:waypoint x="1555" y="230" />
        <di:waypoint x="1630" y="230" />
        <di:waypoint x="1630" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o83mga_di" bpmnElement="Flow_1o83mga">
        <di:waypoint x="1505" y="420" />
        <di:waypoint x="1365" y="420" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1429" y="402" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wi0xbe_di" bpmnElement="Flow_1wi0xbe">
        <di:waypoint x="1630" y="380" />
        <di:waypoint x="1630" y="420" />
        <di:waypoint x="1555" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191u9lk_di" bpmnElement="Flow_191u9lk">
        <di:waypoint x="1530" y="255" />
        <di:waypoint x="1530" y="395" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1539" y="322" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v8tmtg_di" bpmnElement="Flow_0v8tmtg">
        <di:waypoint x="1494" y="150" />
        <di:waypoint x="1530" y="150" />
        <di:waypoint x="1530" y="205" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1odmwry_di" bpmnElement="Flow_1odmwry">
        <di:waypoint x="1376" y="230" />
        <di:waypoint x="1505" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n6udmd_di" bpmnElement="Flow_1n6udmd">
        <di:waypoint x="1351" y="205" />
        <di:waypoint x="1351" y="150" />
        <di:waypoint x="1394" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1356" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07qlm7g_di" bpmnElement="Flow_07qlm7g">
        <di:waypoint x="1281" y="230" />
        <di:waypoint x="1326" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ap55e3_di" bpmnElement="Flow_0ap55e3">
        <di:waypoint x="1181" y="150" />
        <di:waypoint x="1231" y="150" />
        <di:waypoint x="1231" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zdzxft_di" bpmnElement="Flow_0zdzxft">
        <di:waypoint x="1033" y="205" />
        <di:waypoint x="1033" y="150" />
        <di:waypoint x="1081" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1037" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ajskls_di" bpmnElement="Flow_1ajskls">
        <di:waypoint x="1058" y="230" />
        <di:waypoint x="1181" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1128" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyv8yd_di" bpmnElement="Flow_1oyv8yd">
        <di:waypoint x="353" y="230" />
        <di:waypoint x="402" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15rp53s_di" bpmnElement="Flow_15rp53s">
        <di:waypoint x="599" y="230" />
        <di:waypoint x="707" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="608" y="233" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pi4um7_di" bpmnElement="Flow_0pi4um7">
        <di:waypoint x="715" y="146" />
        <di:waypoint x="757" y="146" />
        <di:waypoint x="757" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0it4yuk_di" bpmnElement="Flow_0it4yuk">
        <di:waypoint x="574" y="205" />
        <di:waypoint x="574" y="146" />
        <di:waypoint x="615" y="146" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="555" y="169" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vr9m96_di" bpmnElement="Flow_1vr9m96">
        <di:waypoint x="205" y="230" />
        <di:waypoint x="253" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0echylv_di" bpmnElement="Flow_0echylv">
        <di:waypoint x="807" y="230" />
        <di:waypoint x="842" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="169" y="212" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="176" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xzxgxd_di" bpmnElement="Activity_0d15yei">
        <dc:Bounds x="707" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="762" y="402" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="378" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1rjzhmw_di" bpmnElement="Gateway_1rjzhmw" isMarkerVisible="true">
        <dc:Bounds x="549" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="542" y="263" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_119pnv5_di" bpmnElement="Activity_1wq7s30">
        <dc:Bounds x="615" y="106" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="253" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0eropt8_di" bpmnElement="Activity_0a0g75l">
        <dc:Bounds x="1081" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12lns6w_di" bpmnElement="Gateway_12lns6w" isMarkerVisible="true">
        <dc:Bounds x="1008" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1000" y="262" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g3mnwv_di" bpmnElement="Activity_0nccbee">
        <dc:Bounds x="1181" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s5niqa_di" bpmnElement="Activity_119zmlg">
        <dc:Bounds x="1394" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="1580" y="300" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07zfya5_di" bpmnElement="Activity_0kptmf2">
        <dc:Bounds x="1379" y="490" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="1181" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g2qvk8_di" bpmnElement="Gateway_1g2qvk8" isMarkerVisible="true">
        <dc:Bounds x="1326" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1321" y="262" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04dmfyf_di" bpmnElement="Gateway_04dmfyf" isMarkerVisible="true">
        <dc:Bounds x="1505" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1538" y="203" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fo5mo8_di" bpmnElement="Gateway_0fo5mo8" isMarkerVisible="true">
        <dc:Bounds x="1505" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1500" y="452" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b565p2_di" bpmnElement="Gateway_1b565p2" isMarkerVisible="true">
        <dc:Bounds x="1315" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1309" y="374" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0wx9b29_di" bpmnElement="Activity_0ur58zi">
        <dc:Bounds x="402" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tp1sro_di" bpmnElement="Gateway_1tp1sro" isMarkerVisible="true">
        <dc:Bounds x="1085" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1088" y="371" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_073e0kr_di" bpmnElement="Activity_0b21p68">
        <dc:Bounds x="880" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1wccxzd_di" bpmnElement="Gateway_1wccxzd" isMarkerVisible="true">
        <dc:Bounds x="842" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="856" y="262" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ztbyvv_di" bpmnElement="Activity_039ag6m">
        <dc:Bounds x="893" y="114" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
