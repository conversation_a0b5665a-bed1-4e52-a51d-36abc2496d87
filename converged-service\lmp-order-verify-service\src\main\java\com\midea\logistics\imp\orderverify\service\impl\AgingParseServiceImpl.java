package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.ControlParamManager;
import com.midea.logistics.domain.mdm.domain.ControlParam;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.AgingParseService;
import com.midea.logistics.otp.common.bean.CustomerAgingRuleConfig;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.bop.BopCrmFeign;
import com.midea.logistics.otp.common.feign.convergedfeign.bop.BopDcFeign;
import com.midea.logistics.otp.common.feign.servicefeign.aging.AgingFeign;
import com.midea.logistics.otp.common.feign.servicefeign.aging.CustomerAgingRuleConfigCommonFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderAgingFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.es.EsOrderLogService;
import com.midea.logistics.otp.order.common.fegin.CustomerAgingConfigFeign;
import com.midea.logistics.otp.order.common.fegin.ValueAddedServiceFeign;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.flow.constant.WorkflowConstant;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.OrderAgingHelper;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.mq.producer.LineParseProducer;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.bean.OrderAging;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderLog;
import com.midea.logistics.otp.order.domain.bean.ValueAddedService;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.dto.CustomerOrderInfoExtendConfDto;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.utils.thread.ThreadLocals;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class AgingParseServiceImpl implements AgingParseService {

    @Autowired
    private CustomerAgingConfigFeign customerAgingConfigFeign;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private LineParseProducer lineParseProducer;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private AgingFeign agingFeign;
    @Autowired
    private EsOrderLogService esOrderLogServiceImpl;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private CustomerAgingRuleConfigCommonFeign customerAgingRuleConfigCommonFeign;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;
    @Autowired
    private OrderAgingFeign orderAgingFeign;
    @Autowired
    private ControlParamManager controlParamManager;
    @Autowired
    private OrderAgingHelper orderAgingHelper;
    @Autowired
    private BopCrmFeign bopCrmFeign;
    @Autowired
    private BopDcFeign bopDcFeign;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private ValueAddedServiceFeign valueAddedServiceFeign;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    /**
     * !@时效解析 - 2.入口（中台）
     * @param orderInfoExt
     * @return
     */
    @Override
    @ZeebeFlow("AGING_PRODUCT")
    public OrderInfoExt agingParse(OrderInfoExt orderInfoExt) {
        ThreadLocals.put("operateType", OrderOperateType.AGING_PRODUCT.getKey());
        if (JoinType.isJoinType(orderInfoExt.getJoinType()) ) {
            FlowListenerParam.success("货权转移、调剂、电商货权转移不解析时效");
            //return FlowListenerParam.success("货权转移、调剂、电商货权转移不解析时效");
            return orderInfoExt;
        }
        //逆向时效解析的订单类型
        List<String> inverseAgingOrderTypes = Lists.newArrayList(OrderType.RI.getKey(),OrderType.AI.getKey(),OrderType.PI.getKey(),OrderType.TI.getKey());
        //自提订单能解析时效判断
        if(DeliveryType.isZT(orderInfoExt.getDeliveryType())){
            if(SourceSystem.isSCC(orderInfoExt.getSourceSystem()) && OrderType.isTFOrder(orderInfoExt.getOrderType())){
                //SCC的TF单，如果原单号关联了增值服务单，那么需要解析时效
                //SCC的TF单只有IN2
                if(!checkTFAllowAgingParse(orderInfoExt)){
                    FlowListenerParam.success("未关联增值服务单，跳过时效解析");
                    return orderInfoExt;
                }
            } else if(!inverseAgingOrderTypes.contains(orderInfoExt.getOrderType())){
                FlowListenerParam.success("自提订单，除退货入库、调拨入库、采购入库、中转入库单外不解析时效");
                return orderInfoExt;
            }
        }
        
        //202412 泓铄 调拨入库、采购入库自提订单在订单审核时不解析时效； https://cf.annto.com/pages/viewpage.action?pageId=59867008
        //202507 泓铄 菜鸟调拨订单计划入库时间来自上游（有的话） https://cf.annto.com/pages/viewpage.action?pageId=84294156 
        if (DeliveryType.isZT(orderInfoExt.getDeliveryType())
                && !(SourceSystem.isCAINIAO(orderInfoExt.getSourceSystem()) && OrderType.isAIOrder(orderInfoExt.getOrderType()))
                && (OrderType.isAIOrder(orderInfoExt.getOrderType()) || OrderType.isPIOrder(orderInfoExt.getOrderType()))) {
            FlowListenerParam.success("调拨入库、采购入库自提订单在到车登记时才解析时效");
            return orderInfoExt;
        }

        // 送新取旧发快递的上门取件单
        if (businessHelper.isSxqjExpress(orderInfoExt)) {
            FlowListenerParam.success("不解析时效");
            return orderInfoExt;
        }
        
        String orderNo = orderInfoExt.getOrderNo();

        List<CustomerOrderInfoExtend> data = customerOrderInfoExtendFeign.listByOrderNos(Lists.newArrayList(orderInfoExt.getParentOrderNo())).data();
        boolean xcyw = false;
        String orderDistinctionFlag = null;
        String completeSetNo = null;
        if(ToolUtils.isNotEmpty(data)){
            CustomerOrderInfoExtend extend = data.get(0);
            orderDistinctionFlag = extend.getOrderDistinctionFlag();
            if(null != orderDistinctionFlag) {
                xcyw = OrderDistinctionFlag.isXCYW(orderDistinctionFlag);
            }

            CustomerOrderInfoExtendConfDto confDto = extend.confObjJsonToDtoNotThrow();
            completeSetNo = confDto.getCompleteSetNo();
        }

        // 生活下沉走旧时效
        //20240221 高露：开放基地下沉不走标准时效的限制
//        if(!xcyw){
            //!@时效解析 - 2.1 标准时效解析(非生活下沉)
            JsonResponse standardAgingParse = agingFeign.standardAgingParse(orderNo);
            OrderInfo orderInfo = lmpOrderFlowHelper.getOrderInfo(orderInfoExt.getOrderNo());
            log.info("aging-agg-standardAgingParse:{} , 子单号：{}",JSON.toJSONString(standardAgingParse), orderNo);
            if (BaseCodeEnum.SUCCESS.getCode().equals(standardAgingParse.getCode())) {

                /**
                 * 2024年1月29日18:33:07 高露：
                 * 零售系统 & 精准配需要判断：比较解析出来的签收时间（plan_time）与上游下发的expectArriveEndTime的大小，若解析的时间大，则取解析的时间为签收时间（plan_time），
                 * 若上游传的大，则order_aging表生成用户签收(690)的时效节点时，将上游的expectArriveEndTime，作为签收节点的考核时间（plan_time）；
                 */
                mrpJzpUpdateSignTime(orderInfoExt);
    
                /**
                 * 2024年9月18日21:59:40 泓铄
                 * 菜鸟系统 & 精准配
                 * 泓铄： https://cf.annto.com/pages/resumedraft.action?draftId=56332769&draftShareId=f16afc3c-6e39-439c-a212-b4c15ceadee2&
                 */
                orderAgingHelper.cnJzpUpdateSignTime(orderInfoExt);

                orderInfo = lmpOrderFlowHelper.getOrderInfo(orderInfoExt.getOrderNo());
                BeanUtils.copyProperties(orderInfo, orderInfoExt);
                FlowListenerParam.success(""+standardAgingParse.getData());
                return orderInfoExt;
            }else {
                if(ToolUtils.isNotEmpty(completeSetNo)){
                    ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                    throw BusinessException.fail(standardAgingParse.getMsg());
                }
                
                //超区时效解析失败卡单
                if(DeliveryType.isEXPRESS(orderInfo.getDeliveryType()) && ToolUtils.isNotEmpty(data) 
                        && LoanScenarioCode.EXPRESS_DELIVERY_OVER_REGION.getKey().equals(data.get(0).getLoanScenarioCode())){
                    ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                    throw BusinessException.fail(standardAgingParse.getMsg());
                }
                
                if(ProjectClassifyEnum.isDBFC(orderInfo.getProjectClassify())
                        && OrderType.isAOOrder(orderInfo.getOrderType())
                        && !DeliveryType.isZT(orderInfo.getDeliveryType())){
                    //调拨返厂订单只解析标准时效
                    ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                    throw BusinessException.fail(standardAgingParse.getMsg());
                }
                
                if(DeliveryType.isZT(orderInfoExt.getDeliveryType()) 
                        && inverseAgingOrderTypes.contains(orderInfo.getOrderType())){
                    //自提逆向订单只解析标准时效
                    ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                    throw BusinessException.fail(standardAgingParse.getMsg());
                }
                
                if(DeliveryType.isZT(orderInfoExt.getDeliveryType())
                        && SourceSystem.isSCC(orderInfoExt.getSourceSystem())
                        && OrderType.isTFOrder(orderInfoExt.getOrderType())){
                    //SCC 原单号是绿色鉴定单的 TF单，只解析标准时效
                    ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                    throw BusinessException.fail(standardAgingParse.getMsg());
                }
                
                //mrp+紧急订单 只匹配标准时效
                boolean isMrpAndEmergence = SourceSystem.isMRP(orderInfo.getSourceSystem())
                        && CommonConstant.FLAG_YES.equals(orderInfo.getEmergenceFlag());
                if(isMrpAndEmergence){
                    ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                    throw BusinessException.fail("来自MRP的紧急订单标准时效解析异常：" + standardAgingParse.getMsg());
                }

                if (!xcyw && ToolUtils.isNotEmpty(orderDistinctionFlag)) {
                    ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                    throw BusinessException.fail("基地订单未解析到标准时效");
                }

                OrderLog orderLog = new OrderLog();
                orderLog.setOperateContent(standardAgingParse.getMsg());
                orderLog.setOperateFlag("Y");
                orderLog.setOperateType(OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                orderLog.setParentOrderNo(orderInfoExt.getParentOrderNo());
                orderLog.setOrderNo(orderNo);

                if (DeliveryType.isEXPRESS(orderInfoExt.getDeliveryType())) {
                    //202408 泓铄，RI+快递未配置BIG_EC规则，则不解析时效,如果配置了但解析失败，则卡单
                    if (OrderType.isRIOrder(orderInfo.getOrderType())) {
                        //202507 泓铄，去掉限制 https://cf.annto.com/pages/viewpage.action?pageId=78545760
//                        if(BaseCodeEnum.VALIDATE_ERROR.getCode().equals(standardAgingParse.getCode())){
//                            orderInfo = lmpOrderFlowHelper.getOrderInfo(orderInfoExt.getOrderNo());
//                            BeanUtils.copyProperties(orderInfo, orderInfoExt);
//                            FlowListenerParam.success("退货入库单，客户时效规则配置未配置大件电商时效规则，跳过时效解析");
//                            return orderInfoExt;
//                        }

                        ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                        throw BusinessException.fail(standardAgingParse.getMsg());
                    }

                    // 如果客户以时效规则是电商时效且配送方式不是快递的，直接走旧时效；或者规则是电商时效，但是未配置场景，则走旧时效；
                    // 配送方式是快递，未配置时效规则或场景或时效，都卡住
                    if (BaseCodeEnum.VALIDATE_ERROR.getCode().equals(standardAgingParse.getCode())) {
                        ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                        throw BusinessException.fail(standardAgingParse.getMsg());
                    }
                } else if(AgingRuleCodeEnum.BIG_EC.getKey().equals(standardAgingParse.getCode())){
                    //202408 泓铄 BIG_EC/SMALL_EC 标准时效解析失败结果处理
                    //增加系统控制参数：大件电商兜底时效开关【BIG_EC_AGING_FLAG】，1-开/0-关；
                    ControlParam controlParam = controlParamManager.getCache(CommonConstant.BIG_EC_AGING_FLAG);
                    if(controlParam != null && Objects.equals(CommonConstant.N, controlParam.getValue())){
                        ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                        throw BusinessException.fail(standardAgingParse.getMsg());
                    }
                } else if(xcyw){
                    //标准时效放开基地下沉，匹配不到走旧时效
                } else {

                    String siteCode = orderInfoExt.getSiteCode();
                    String customerCode = orderInfoExt.getCustomerCode();

                    CustomerAgingRuleConfig customerAgingRuleConfig = new CustomerAgingRuleConfig();
                    customerAgingRuleConfig.setSiteCode(siteCode);
                    customerAgingRuleConfig.setCustomerCode(customerCode);
                    customerAgingRuleConfig.setEnableFlag(1);
                    JsonResponse<List<CustomerAgingRuleConfig>> listJsonResponse = customerAgingRuleConfigCommonFeign.customerAgingRuleConfigList(customerAgingRuleConfig);

                    if (null != listJsonResponse.data() && ToolUtils.isNotEmpty(listJsonResponse.data())) {
                        //20240806 泓铄，增加配送方式字段匹配，匹配不到，按配送方式为空取
                        String deliveryType = orderInfo.getDeliveryType();
                        Optional<CustomerAgingRuleConfig> optional = listJsonResponse.data().stream()
                                .filter(c -> deliveryType != null && (deliveryType.equals(c.getDeliveryType()) || ToolUtils.isEmpty(c.getDeliveryType())))
                                .findFirst();
                        if(optional.isPresent()){
                            CustomerAgingRuleConfig customerAgingRuleConfig1 = optional.get();
                            String agingRuleCode = customerAgingRuleConfig1.getAgingRuleCode();
                            if (ToolUtils.isNotEmpty(agingRuleCode)) {
                                ThreadLocals.put("operateType", OrderOperateType.STANDARD_AGING_CONFIG.getKey());
                                throw BusinessException.fail("" + standardAgingParse.getMsg());
                            }
                        }
                    }
                }

                esOrderLogServiceImpl.saveLog(orderLog);
            }
//        }


        //!@时效解析 - 2.2 旧时效解析（里程时效+客户时效）
        JsonResponse agingParse = agingFeign.agingParse(orderNo);
        log.info("aging-agg-agingParse:"+JSON.toJSONString(agingParse));
        if (BaseCodeEnum.SUCCESS.getCode().equals(agingParse.getCode())) {
            orderInfo = lmpOrderFlowHelper.getOrderInfo(orderInfoExt.getOrderNo());
            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            FlowListenerParam.success(""+agingParse.getData());
            /**
             * 2024年9月18日21:59:40 泓铄
             * 菜鸟系统 & 精准配
             * 泓铄： https://cf.annto.com/pages/resumedraft.action?draftId=56332769&draftShareId=f16afc3c-6e39-439c-a212-b4c15ceadee2&
             */
            orderAgingHelper.cnJzpUpdateSignTime(orderInfoExt);
            return orderInfoExt;
        }else{
            OrderLog orderLog = new OrderLog();
            orderLog.setOperateContent(agingParse.getMsg());
            orderLog.setOperateFlag("Y");
            orderLog.setOperateType(OrderOperateType.AGING_CONFIG.getKey());
            orderLog.setParentOrderNo(orderInfoExt.getParentOrderNo());
            orderLog.setOrderNo(orderNo);
            esOrderLogServiceImpl.saveLog(orderLog);
        }

        //!@时效解析 - 2.3 客户时效解析
        JsonResponse<String> jsonResponse = customerAgingConfigFeign.agingParse(orderInfoExt.getOrderNo());
        boolean thj = orderverifyHelper.isTHJ(orderInfoExt);

        //来源系统为CAINIAO，业务模式为B2B的订单，若解析端到端时效时效失败，则直接报错
        boolean caiNiao2B = SourceSystem.CAINIAO.getKey().equals(orderInfoExt.getSourceSystem()) && BusinessMode.isB2B(orderInfoExt.getBusinessMode());
        //gl:下沉业务控制解析客户时效，不匹配区配时效或干线时效，无客户时效则提示进行配置
        boolean isOnlyCustAging = businessHelper.isXCYWFlag(orderInfoExt.getParentOrderNo(),orderInfoExt.getSourceSystem());
        String lineCode =null,reginonalAgingMsg =null;
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            boolean reginonalAging = false;
            String dictVaule = dictHelper.getDictVaule(CommonConstant.REGINONAL_AGING_DEFAULT, orderInfoExt.getSiteCode());
            if (ToolUtils.isNotEmpty(dictVaule) && !caiNiao2B && !isOnlyCustAging){
                reginonalAging = true;
            }
//            ControlParamManager controlParamManager = SpringContextHolder.getBean(ControlParamManager.class);
//            ControlParam controlParam = controlParamManager.getCache(CommonConstant.REGINONAL_AGING_DEFAULT);
//            if (null != controlParam) {
//                String reginonalAgingDefault = controlParam.getValue();
//                if (ToolUtils.isNotEmpty(reginonalAgingDefault)) {
//                    if (Lists.newArrayList(reginonalAgingDefault.split(",")).contains(orderInfoExt.getSiteCode())) {
//                        reginonalAging = true;
//                    }
//                }
//            }

            String businessCategory = orderverifyHelper.parseBusinessCategory(orderInfoExt);
            if (Lists.newArrayList(BusinessCategory.LTL_MAIN_LINE.getKey(),BusinessCategory.CARLOAD_MAIN_LINE.getKey())
                .contains(businessCategory) && jsonResponse.getMsg().startsWith("时效解析失败")
                && Lists.newArrayList(InOutType.OUT.getName(),InOutType.YS.getName()).contains(orderInfoExt.getInOutType())
                && !thj
                && !caiNiao2B
                && !isOnlyCustAging
            ){
                orderFlowHelper.clear(orderNo);
                orderFlowHelper.loadingOrder(orderNo, WorkflowConstant.ANNTO_ORDER);
                orderFlowHelper.clear(orderInfoExt.getParentOrderNo());
                orderFlowHelper.loadingOrder(orderInfoExt.getParentOrderNo(), WorkflowConstant.CUSTOMER_ORDER);

                lineCode = orderverifyHelper.linePares(orderNo, businessCategory);
            }else if (reginonalAging){
                reginonalAgingMsg =orderverifyHelper.reginonalAging(orderNo);
            }else{
                String msg = jsonResponse.getMsg().startsWith("时效解析失败") ? jsonResponse.getMsg(): "时效解析失败: " + jsonResponse.getMsg();
                throw BusinessException.fail(msg);
            }
        }

        /**
         * 2024年1月29日18:33:07 高露：
         * 零售系统 & 精准配需要判断：比较解析出来的签收时间（plan_time）与上游下发的expectArriveEndTime的大小，若解析的时间大，则取解析的时间为签收时间（plan_time），
         * 若上游传的大，则order_aging表生成用户签收(690)的时效节点时，将上游的expectArriveEndTime，作为签收节点的考核时间（plan_time）；
         */
        mrpJzpUpdateSignTime(orderInfoExt);
    
        /**
         * 2024年9月18日21:59:40 泓铄
         * 菜鸟系统 & 精准配
         * 泓铄： https://cf.annto.com/pages/resumedraft.action?draftId=56332769&draftShareId=f16afc3c-6e39-439c-a212-b4c15ceadee2&
         */
        orderAgingHelper.cnJzpUpdateSignTime(orderInfoExt);

        orderInfo = lmpOrderFlowHelper.getOrderInfo(orderInfoExt.getOrderNo());
        //线路解析
        //模糊订单不用线路解析
        if(!CommonConstant.FLAG_YES.equals(orderInfoExt.getPlanOrderFlag())) {
            lineParseProducer.sent(orderInfo);
        }

        BeanUtils.copyProperties(orderInfo, orderInfoExt);
        //return FlowListenerParam.success("时效: " + jsonResponse.getData());
        if (ToolUtils.isNotEmpty(lineCode)){
            FlowListenerParam.success("线路： " + lineCode);
            return orderInfoExt;
        }
        if (ToolUtils.isNotEmpty(reginonalAgingMsg)){
            FlowListenerParam.success(reginonalAgingMsg);
            return orderInfoExt;
        }
        FlowListenerParam.success("时效： " + jsonResponse.getData());
        return orderInfoExt;
    }

    private boolean checkTFAllowAgingParse(OrderInfoExt orderInfoExt) {
        boolean result = false;
        JsonResponse<CustomerOrderInfo> jsonResponse = customerOrderInfoFeign.getOrderInfoByOrderNos(orderInfoExt.getParentOrderNo());
        if (jsonResponse == null || !jsonResponse.judgeSuccess()) {
            throw BusinessException.fail("时效解析失败，未查询到父单");
        }
        CustomerOrderInfo customerOrderInfo = jsonResponse.getData();
        if (customerOrderInfo != null && ToolUtils.isNotEmpty(customerOrderInfo.getOriginOrderNo())) {
            ValueAddedService query = new ValueAddedService();
            query.setVdOrderNo(customerOrderInfo.getOriginOrderNo());
            query.setServiceType(ValueAddedServiceType.MEO.getKey());
            JsonResponse<ValueAddedService> jsonResponse1 = valueAddedServiceFeign.selectOne(query);
            if (jsonResponse1 != null && jsonResponse1.judgeSuccess() && jsonResponse1.getData() != null) {
                return true;
            }
        }
        return result;
    }


    /*
    !@时效解析 - 5.零售精准配设置签收时间
    零售系统 + 精准配需要判断：比较解析出来的签收时间（plan_time）与上游下发的expectArriveEndTime的大小，若解析的时间大，则取解析的时间为签收时间（plan_time），
     * 若上游传的大，则order_aging表生成用户签收(690)的时效节点时，将上游的expectArriveEndTime，作为签收节点的考核时间（plan_time）；
     */
    private void mrpJzpUpdateSignTime(OrderInfoExt orderInfoExt) {

        Date expectArriveEndTime = orderInfoExt.getExpectArriveEndTime();
        boolean jzp = CommonConstant.JZP.equals(orderInfoExt.getUpperAgingCode()) ;
        boolean mrpJzp = jzp && SourceSystem.isMRP(orderInfoExt.getSourceSystem()) && ToolUtils.isNotEmpty(expectArriveEndTime) ;
        if (!mrpJzp){
            return ;
        }

        OrderAging aging = new OrderAging();
        aging.setOrderNo(orderInfoExt.getOrderNo());
        aging.setOrderStatus(OrderStatus.SIGN.getKey());
        OrderAging signAging = orderAgingFeign.selectOne(aging).data();
        if (null == signAging){
            return ;
        }

        Date planTime = signAging.getPlanTime();
        try {
            boolean arriveAferSign = expectArriveEndTime.after(planTime);
            log.info("mrpJzpUpdateSignTime：orderNo:{},arriveAferSign:{}，expectArriveEndTime：{}，planTime：{} ", orderInfoExt.getOrderNo(),arriveAferSign,expectArriveEndTime,planTime);
            if (arriveAferSign){
                signAging.setPlanTime(expectArriveEndTime);
                orderAgingFeign.update(signAging.getId(),signAging);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
    
   
}
