package com.midea.logistics.otp.order.common.fegin.bop;

import com.midea.logistics.logisticsbopsdk.constants.RequestUrlConstant;
import com.midea.logistics.otp.order.common.fegin.fallback.LtmsSignFeignFallback;
import com.midea.logistics.otp.order.common.fegin.fallback.OrderItemAreaFeignFallback;
import com.midea.logistics.otp.order.converged.domain.request.ArriveAreaRequest;
import com.midea.logistics.otp.order.converged.domain.request.OnwayAreaRequest;
import com.midea.logistics.otp.order.converged.domain.request.OutAreaRequest;
import com.midea.logistics.otp.order.domain.dto.OrderForSignDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: LtmsSignFeign
 * @author: gaozg1
 * @date: 2019/10/15 10:24
 * @description: 签收  待接口对接
 */
@FeignClient(value = "logistics-bop-service", fallback = LtmsSignFeignFallback.class)
public interface LtmsSignFeign {


    /**
     * 签收出库单
     */
    @PostMapping(RequestUrlConstant.AUTO_SIGN_HANDLER)
    String autoSignHandler(@RequestBody OrderForSignDto bean);


}
