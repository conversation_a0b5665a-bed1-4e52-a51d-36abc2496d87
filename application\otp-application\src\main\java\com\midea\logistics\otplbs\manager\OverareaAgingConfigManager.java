package com.midea.logistics.otplbs.manager;

import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * ©Copyright ©1968-2021 Midea Group,IT
 * Description: 文件上传接口
 */
@Component
public class OverareaAgingConfigManager {

    private static final Logger logger = LoggerFactory.getLogger(OverareaAgingConfigManager.class);
    @Autowired
    private UpDownloadHelper upDownloadHelper;
    @Autowired
    private ReportFeign reportFeign;
    @Autowired
    private ISsoService iSsoService;

    public JsonResponse importOverareaAgingConfig(@RequestParam("file") MultipartFile file) {
        String userCode = iSsoService.getUserCode();
        if (org.apache.commons.lang.StringUtils.isBlank(userCode)) {
            throw BusinessException.fail("用户未登录");
        }
        ImportFileRequest request = upDownloadHelper.uploadFileToOss(file);
        return reportFeign.importOverareaAgingConfig(request);
    }
}
