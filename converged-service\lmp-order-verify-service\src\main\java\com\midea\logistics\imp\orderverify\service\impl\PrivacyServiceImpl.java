package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.service.PrivacyService;
import com.midea.logistics.otp.order.common.helper.PrivacyHelper;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PrivacyServiceImpl implements PrivacyService {
    @Autowired
    private PrivacyHelper privacyHelper;

    @Override
    @ZeebeFlow("BINDING_VIRTUAL_PHONE")
    public OrderInfoExt bindingVirtualPhone(OrderInfoExt orderInfo) {
        return  privacyHelper.bindingVirtualPhoneWithLock(orderInfo);
    }
}
