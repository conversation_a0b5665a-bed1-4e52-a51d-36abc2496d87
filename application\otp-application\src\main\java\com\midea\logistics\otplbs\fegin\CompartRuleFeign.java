package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.bean.CompartRule;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CompartRuleFeign
 * Author: jiajun
 * Date: 2019-5-27 13:59:00
 * Description:分仓规则 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface CompartRuleFeign {

    @RequestMapping(value = "/compartRule", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CompartRule compartRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/compartRule/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/compartRule/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CompartRule compartRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/compartRule/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/compartRules", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap CompartRule compartRule);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = "/compartRule/batchCreateOrUpdate", method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<CompartRule> compartRules);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = "/compartRule/batchDeleteByBusinessKey", method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
