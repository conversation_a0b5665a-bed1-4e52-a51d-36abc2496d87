package com.midea.logistics.otp.orderverify.rest;

import com.midea.logistics.otp.order.common.flow.cache.WorkflowNodeTemplateCache;
import com.midea.logistics.otp.order.common.flow.cache.WorkflowTemplateCache;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class FlowCacheRest {


    @GetMapping("/workflow/cache/clear")
    public JsonResponse workflowCacheClear(){
        WorkflowNodeTemplateCache.reset();
        WorkflowTemplateCache.reset();
        return JsonResponse.success("清除成功");
    }

}
