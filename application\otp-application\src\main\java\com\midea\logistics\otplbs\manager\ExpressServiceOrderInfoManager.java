package com.midea.logistics.otplbs.manager;

import com.midea.logistics.lc.file.sdk.builder.FileUploadBuilder;
import com.midea.logistics.lc.file.sdk.service.LcFileService;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.enums.CostOrderInfoStatusEnum;
import com.midea.logistics.otp.report.service.domain.request.CostOrderInfoImportFileRequest;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * ©Copyright ©1968-2021 Midea Group,IT
 * Author:   ex_dengzj4
 * Date:     2021-12-7 15:40
 * Description: 文件上传接口
 */
@Component
public class ExpressServiceOrderInfoManager {

    private static final Logger logger = LoggerFactory.getLogger(ExpressServiceOrderInfoManager.class);

    @Autowired
    private LcFileService lcFileService;
    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private ReportFeign reportFeign;

    public JsonResponse importExpressServiceOrderInfoAsync(@RequestParam("file") MultipartFile file) {
        String userCode = iSsoService.getUserCode();
        if (StringUtils.isBlank(userCode)) {
            throw BusinessException.fail("用户未登录");
        }

        CostOrderInfoImportFileRequest request = new CostOrderInfoImportFileRequest();
        request.setFileName(file.getOriginalFilename());
        request.setUserCode(userCode);

        String value = redisLockHelper.getValue(CostOrderInfoStatusEnum.EXPRESS_SERVICE_ORDER_IMPORT_LOCK + request.getLockKey());
        if (null != value) {
            throw BusinessException.fail("[" + file.getOriginalFilename() + "]系统正在导入，请稍等");
        }

        FileUploadBuilder build = FileUploadBuilder.newBuilder().file(file).bucket("c-loms").userCode(userCode).build();
        String upload = lcFileService.upload(build);
        if (StringUtils.isBlank(upload)) {
            throw BusinessException.fail("文件上传异常");
        }
        request.setFileUrl(upload);

        return reportFeign.importExpressServiceOrderInfoAsync(request);
    }
}
