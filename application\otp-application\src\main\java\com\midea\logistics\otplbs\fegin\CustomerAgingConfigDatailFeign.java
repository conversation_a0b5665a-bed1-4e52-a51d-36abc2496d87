package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.CustomerAgingConfigDatail;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CustomerAgingConfigDatailFeign
 * Author: luoh
 * Date: 2019-5-21 11:58:42
 * Description:客户节点时效详情 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface CustomerAgingConfigDatailFeign {

    @RequestMapping(value = "/customerAgingConfigDatail", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerAgingConfigDatail customerAgingConfigDatail);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerAgingConfigDatail/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerAgingConfigDatail/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerAgingConfigDatail customerAgingConfigDatail);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerAgingConfigDatail/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/customerAgingConfigDatails", method = RequestMethod.GET)
    JsonResponse<PageResponse<CustomerAgingConfigDatail>> search(@SpringQueryMap CustomerAgingConfigDatail customerAgingConfigDatail);

    @RequestMapping(value = "/customerAgingConfigDatail/batchCreate", method = RequestMethod.POST)
    JsonResponse batchCreate(@RequestBody List<CustomerAgingConfigDatail> customerAgingConfigDatails);

}
