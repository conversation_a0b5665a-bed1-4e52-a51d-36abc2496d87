package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord;
import com.midea.logistics.otp.task.domain.request.WarehouseCloseRecordReq;
import com.midea.logistics.otp.task.service.IWarehouseCloseRecordService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: WarehouseCloseRecordController
* Author: dongxy31
* Date: 2024-6-11 10:44:53
* Description:入库客户仓库记录表控制层
*/
@RestController
public class WarehouseCloseRecordRest extends BaseController<WarehouseCloseRecord> {

    private Logger logger = LoggerFactory.getLogger(WarehouseCloseRecordRest.class);

    @Autowired
    private IWarehouseCloseRecordService iWarehouseCloseRecordService;


    /**
    * 新增
    *
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord", method = RequestMethod.POST)
    public JsonResponse create(@Valid @RequestBody WarehouseCloseRecord warehouseCloseRecord, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer success = iWarehouseCloseRecordService.saveWarehouseCloseRecord(warehouseCloseRecord);
            if (1 == success){
                jsonResponse.data = warehouseCloseRecord.getId();
             }
        };
        return doing.go(request, logger);
    }


    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/{id}", method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            WarehouseCloseRecord warehouseCloseRecordQr = new WarehouseCloseRecord();
            warehouseCloseRecordQr.setId(id);
            WarehouseCloseRecord warehouseCloseRecord = iWarehouseCloseRecordService.selectOneWarehouseCloseRecord(warehouseCloseRecordQr);
            jsonResponse.data = warehouseCloseRecord;
        };
        return doing.go(request, logger);

    }

    /**
    * 更新
    * @param warehouseCloseRecord
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/{id}", method = RequestMethod.PUT)
    public JsonResponse update(@PathVariable("id") Long id,  /**@Valid*/ @RequestBody WarehouseCloseRecord warehouseCloseRecord, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            warehouseCloseRecord.setId(id);
            warehouseCloseRecord.setVersion(warehouseCloseRecord.getVersion());
            Integer i = iWarehouseCloseRecordService.updateWarehouseCloseRecord(warehouseCloseRecord);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/{id}", method = RequestMethod.DELETE)
    public JsonResponse deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            WarehouseCloseRecord warehouseCloseRecordQr = new WarehouseCloseRecord();
            warehouseCloseRecordQr.setId(id);
            Integer i = iWarehouseCloseRecordService.deleteWarehouseCloseRecord(warehouseCloseRecordQr);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);

    }



    /**
    * 查询集合（分页）
    * @param warehouseCloseRecord
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecords", method = RequestMethod.POST)
    public JsonResponse search(@RequestBody WarehouseCloseRecord warehouseCloseRecord, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<WarehouseCloseRecord> pageResponse = iWarehouseCloseRecordService.selectWarehouseCloseRecordPage(warehouseCloseRecord);
            jsonResponse.data = pageResponse;
        };
        return doing.go(request, logger);

    }


    /**
    * 查询集合
    * @param warehouseCloseRecord
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/list", method = RequestMethod.GET)
    public JsonResponse list(@SpringQueryMap WarehouseCloseRecord warehouseCloseRecord, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            warehouseCloseRecord.setPageSize(CommonConstant.LIST_PAGE_SIZE);
            List<WarehouseCloseRecord> list = iWarehouseCloseRecordService.selectWarehouseCloseRecordByIndex(warehouseCloseRecord);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);

    }


    /**
    * 查询一个
    * @param warehouseCloseRecord
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/selectOne", method = RequestMethod.GET)
    public JsonResponse selectOne(@SpringQueryMap WarehouseCloseRecord warehouseCloseRecord, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            WarehouseCloseRecord obj = iWarehouseCloseRecordService.selectOneWarehouseCloseRecord(warehouseCloseRecord);
            jsonResponse.data = obj;
        };
        return doing.go(request, logger);

    }


    /**
    * 批量新增
    *
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/insertBatch", method = RequestMethod.POST)
    public JsonResponse insertBatch(@RequestBody List<WarehouseCloseRecord> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iWarehouseCloseRecordService.insertBatch(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }


    /**
    * 批量修改
    *
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/updateBatch", method = RequestMethod.POST)
    public JsonResponse updateBatch(@RequestBody List<WarehouseCloseRecord> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iWarehouseCloseRecordService.batchUpdate(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
     * task批量修改
     *
     * @return
     */
    @RequestMapping(value = "/warehouseCloseRecord/updateBatchTask", method = RequestMethod.POST)
    public JsonResponse updateBatchTask(@RequestBody List<WarehouseCloseRecord> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iWarehouseCloseRecordService.batchUpdateTask(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
    * 批量删除
    *
    * @return
    */
    @RequestMapping(value = "/warehouseCloseRecord/deleteBatch", method = RequestMethod.POST)
    public JsonResponse deleteBatch(@RequestBody List<WarehouseCloseRecord> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iWarehouseCloseRecordService.deleteBatch(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
     * 查询集合
     * @param warehouseCloseRecord
     * @return
     */
    @RequestMapping(value = "/warehouseCloseRecord/queryListByUpdate", method = RequestMethod.POST)
    public JsonResponse<List<WarehouseCloseRecord>> queryListByUpdate(@RequestBody WarehouseCloseRecordReq warehouseCloseRecord) {

        JsonResponse jsonResponse = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        jsonResponse.setData(iWarehouseCloseRecordService.queryList(warehouseCloseRecord));
        jsonResponse.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS")));
        return jsonResponse;
    }

}
