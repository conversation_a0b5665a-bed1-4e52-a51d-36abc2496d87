package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.util.List;

/**
 * ClassName: QueryInstallResponse
 * Description:查询是否需要安装响应类
 *
 * <AUTHOR>
 * @date 2023/9/5 17:39
 */
@Data
public class QueryInstallResponse {

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 第三方标识
     */
    private Integer thirdFlag;

    /**
     * 订单明细
     */
    private List<QueryInstallItemDto> orderItemDtos;

    /**
     * 是否需要安装
     */
    private boolean needInstall;
}
