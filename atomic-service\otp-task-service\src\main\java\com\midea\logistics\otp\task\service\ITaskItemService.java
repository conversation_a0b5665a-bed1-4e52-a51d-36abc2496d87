package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.mideaframework.core.web.PageResponse;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: ITaskItemService
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:ITaskItemService服务接口
 */
public interface ITaskItemService {

    Integer saveTaskItem(TaskItem taskItem);

    Integer deleteTaskItem(TaskItem taskItem);

    Integer updateTaskItem(TaskItem taskItem);

    TaskItem selectOneTaskItem(TaskItem taskItem);

    PageResponse<TaskItem> selectTaskItemPage(TaskItem taskItem);

    List<TaskItem> selectTaskItemByIndex(TaskItem taskItem);

    int countTaskItemByIndex(TaskItem taskItem);

    Integer insertBatch(List<TaskItem> taskItems);

    Integer batchUpdate(List<TaskItem> taskItems);

    Integer batchUpdateQty(List<TaskItem> taskItems);

    Integer deleteBatch(List<TaskItem> taskItems);

    List<TaskItem> listByTaskNos(List<String> taskNos);

    List<TaskItem> sumActQtyByTaskNos(List<String> taskNos);
    
    List<TaskItem> listByParentOrderNo(String parentOrderNo);

    List<TaskItem> listByParentOrderNoAndDistributionNum(String parentOrderNo,Integer distributionNum);

    List<TaskItem> querySplitTasksByOrderNo(String orderNo);
}