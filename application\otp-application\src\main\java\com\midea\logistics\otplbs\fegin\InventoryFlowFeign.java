package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.bean.InventoryFlow;
import com.midea.logistics.otp.order.domain.bean.custom.InventoryFlowExt;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: InventoryFlowFeign
 * Author: lindq2
 * Date: 2019-5-14 11:40:46
 * Description:库存流水 feign
 */
@FeignClient(value = "logistics-otp-order-service",url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface InventoryFlowFeign {

    @PostMapping(value = "/inventoryFlow")
    JsonResponse create(@RequestBody InventoryFlow inventoryFlow);

    /**
     * @param id
     * @return
     */
    @GetMapping(value = "/inventoryFlow/{id}")
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @PutMapping(value = "/inventoryFlow/{id}")
    JsonResponse update(@PathVariable("id") Long id, @RequestBody InventoryFlow inventoryFlow);

    /**
     * @param id
     * @return
     */
    @DeleteMapping(value = "/inventoryFlow/{id}")
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @GetMapping(value = "/inventoryFlows")
    JsonResponse<PageResponse<InventoryFlowExt>> search(@SpringQueryMap InventoryFlowExt inventoryFlow);

    @GetMapping(value = "/inventoryFlowsCount")
    JsonResponse<Integer> inventoryFlowsCount(@SpringQueryMap InventoryFlowExt inventoryFlow);

}
