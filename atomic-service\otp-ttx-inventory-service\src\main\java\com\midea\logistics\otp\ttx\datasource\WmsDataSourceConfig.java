package com.midea.logistics.otp.ttx.datasource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2019-8-27 11:12
 */
@Configuration
@MapperScan(basePackages = {"com.midea.logistics.otp.ttx.mapper.common","com.midea.logistics.otp.ttx.mapper.custom"},
    sqlSessionFactoryRef="wmsSqlSessionFactory")
public class WmsDataSourceConfig {

    @Autowired
    @Qualifier("wmsDataSource")
    private DataSource wmsDataSource;

    @Bean(name="wmsSqlSessionFactory")
    @Primary
    public SqlSessionFactory wmsSqlSessionFactory(@Qualifier("wmsDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/**/*.xml"));
        bean.setTypeAliasesPackage("com.midea.logistics.otp.ttx.mapper.common,com.midea.logistics.otp.ttx.mapper.custom");
        return bean.getObject();
    }

    @Bean(name="wmsSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate wmsSqlSessionTemplate(@Qualifier("wmsSqlSessionFactory") SqlSessionFactory sqlSessionFactory)  {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
