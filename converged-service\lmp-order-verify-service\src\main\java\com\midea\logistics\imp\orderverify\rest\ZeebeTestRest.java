package com.midea.logistics.imp.orderverify.rest;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.mq.consumer.ZeebeReturyConsumer;
import com.midea.logistics.imp.orderverify.service.*;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.OrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.TaskFeign;
import com.midea.logistics.otp.order.common.helper.ScPosFlagHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.mip.core.util.BeanUtils;
import com.mideaframework.core.config.properties.AppProperties;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.constants.EnvEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RestController
public class ZeebeTestRest {

    @Autowired
    private IAddressResolvingService iAddressResolvingService;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private LoadingFeeAnalyzeService loadingFeeAnalyzeService;
    @Autowired
    private CSSOrderIssuedService cssOrderIssuedService;
    @Autowired
    private IOutAreaService iOutAreaService;
    @Autowired
    private ZeebeReturyConsumer zeebeReturyConsumer;
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private ZeebeLmpService zeebeLmpService;
    @Autowired
    private GetUnifiedCharingService getUnifiedCharingService;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private TaskFeign taskFeign;
    @Autowired
    private WarehouseAllocationService warehouseAllocationService;
    @Autowired
    private ScPosFlagHelper scPosFlagHelper;

    /**
    * @description: 地址解析(测试)
    * @param: [orderInfo]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2020/11/14 9:45
    */
    @PostMapping("/lmp/test/address")
    public JsonResponse address(@RequestBody CustomerOrderInfoExt customerOrderInfoExt){
        JsonResponse<CustomerOrderInfoExt> jsonResponse1 = customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(customerOrderInfoExt.getOrderNo());
        return JsonResponse.success(iAddressResolvingService.queryForCountry(jsonResponse1.data));
    }


    /**
     * @description: 装卸费规则
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 15:13
     */
    @PostMapping("/lmp/test/loadingFeeAnalyze")
    public JsonResponse loadingFeeAnalyze(@RequestBody OrderInfoExt orderInfo){
        return JsonResponse.success(loadingFeeAnalyzeService.loadingFeeAnalyze(orderInfo, true));
    }

    /**
     * @description: 下发售后
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 18:14
     */
    @PostMapping("/lmp/test/orderIssued")
    public JsonResponse orderIssued(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(cssOrderIssuedService.orderIssued(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("下发末端配送失败，失败原因:"+e.getMessage());
        }

    }

    /**
     * @description: 检验爆仓
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 9:44
     */
    @PostMapping("/lmp/test/isOutArea")
    public JsonResponse isOutArea(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(iOutAreaService.isOutArea(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("爆仓失败，失败原因:"+e.getMessage());
        }
    }

    @PutMapping("/lmp/test/lmpVerify")
    public JsonResponse orderVerify(HttpServletRequest request, @RequestBody List<String> orderNos) {
        if (EnvEnum.PROD == AppProperties.CURRENT_ENV) {
            return JsonResponse.fail(EnvEnum.PROD.getValue() + "不支持此功能");
        }
        List<String> lockedOrderNos = new ArrayList<>();
        // 中台订单触发审校
        for (String orderNo : orderNos) {
            // 重复审核,提示
            String lock = redisLockHelper.getValue(orderNo);
            if (lock != null) {
                lockedOrderNos.add(orderNo);
                continue;
            }
            MessageExt messageExt = new MessageExt();
            messageExt.setBody(orderNo.getBytes());
            zeebeReturyConsumer.handlerMessage(messageExt);
        }
        if (!CollectionUtils.isEmpty(lockedOrderNos)) {
            return JsonResponse.fail("重复审核" + JSON.toJSONString(lockedOrderNos));
        }
        return JsonResponse.success(null);
    }

    /**
     * @description: 压测流程
     * @param: [orderNo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/18 9:44
     */
    @GetMapping("/lmp/test/verifyZeebeTest")
    public JsonResponse verifyZeebeTest(@RequestParam String orderNo){
        try {
            return JsonResponse.success(zeebeLmpService.verifyZeebeTest(orderNo));
        }catch (Exception e){
            throw BusinessException.fail("压测流程失败原因:"+e.getMessage());
        }
    }

    @PostMapping("/lmp/test/getUnifiedCharing")
    public JsonResponse getUnifiedCharingTest(@RequestBody List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return JsonResponse.fail("订单号集合为空");
        }
        Integer len = 1000;
        Integer size = orderNoList.size();
        Double count = Math.ceil(size.floatValue()/len.floatValue());
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < count.intValue(); i++ ) {
            List<String> subList = orderNoList.subList(i * len, Math.min((i + 1) * len, size));
            JsonResponse<List<CustomerOrderInfo>> response = customerOrderInfoFeign.listByOrderNos(subList);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
                return JsonResponse.fail(response.getMsg());
            }
            if (null == response.getData()) {
                continue;
            }
            response.getData().forEach(customerOrderInfo -> {
                CustomerOrderInfoExt dto = new CustomerOrderInfoExt();
                Integer oldOrderStatus = customerOrderInfo.getOrderStatus();
                Integer oldExcuteStatus = customerOrderInfo.getExcuteStatus();
                String oldExceptionType = customerOrderInfo.getExceptionType();
                String oldExceptionDesc = customerOrderInfo.getExceptionDesc();
                BeanUtils.copyProperties(customerOrderInfo, dto);
                getUnifiedCharingService.doWork(dto);
                //更新父单
                CustomerOrderInfo customerOrderInfoUpdateDto = new CustomerOrderInfo();
                customerOrderInfoUpdateDto.setId(dto.getId());
//                    customerOrderInfo.setVersion(dto.getVersion());
                //由于zeebe切面会更新状态，需要还原订单原本的状态
                customerOrderInfoUpdateDto.setOrderStatus(oldOrderStatus);
                customerOrderInfoUpdateDto.setExcuteStatus(oldExcuteStatus);
                customerOrderInfoUpdateDto.setExceptionType(oldExceptionType);
                customerOrderInfoUpdateDto.setExceptionDesc(oldExceptionDesc);
                if (StringUtils.isNotBlank(dto.getOrderNo()) && StringUtils.isNotBlank(dto.getTcFlag())) {
                    customerOrderInfoUpdateDto.setTcFlag(dto.getTcFlag());
                    //更新子单
                    OrderInfo orderInfoUpdateDto = new OrderInfo();
                    orderInfoUpdateDto.setParentOrderNo(dto.getOrderNo());
                    orderInfoUpdateDto.setTcFlag(dto.getTcFlag());
                    JsonResponse response2 = orderInfoFeign.batchUpdateByParentOrderNo(Collections.singletonList(orderInfoUpdateDto));
                    if (!BaseCodeEnum.SUCCESS.getCode().equals(response2.getCode())) {
                        stringBuilder.append(dto.getOrderNo()).append("更新子单失败").append(response2.getMsg()).append(";");
                    }
                    //更新任务
                    Task taskUpdateDto = new Task();
                    taskUpdateDto.setParentOrderNo(dto.getOrderNo());
                    taskUpdateDto.setTcFlag(dto.getTcFlag());
                    JsonResponse response3 = taskFeign.batchUpdateByParentOrderNo(Collections.singletonList(taskUpdateDto));
                    if (!BaseCodeEnum.SUCCESS.getCode().equals(response3.getCode())) {
                        stringBuilder.append(dto.getOrderNo()).append("更新任务失败").append(response3.getMsg()).append(";");
                    }
                }
                JsonResponse<Integer> response1 = customerOrderInfoFeign.update(dto.getId(), customerOrderInfoUpdateDto);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(response1.getCode())) {
                    stringBuilder.append(dto.getOrderNo()).append("更新父单失败").append(response1.getMsg()).append(";");
                }
            });
        }
        return StringUtils.isBlank(stringBuilder.toString()) ? JsonResponse.success(null) : JsonResponse.fail(stringBuilder.toString());
    }

    @PostMapping("/lmp/test/allocationParse")
    public JsonResponse allocationParse(@RequestBody List<String> orderNoList){
        if (CollectionUtils.isEmpty(orderNoList)) {
            return JsonResponse.fail("订单号集合为空");
        }
        Integer len = 1000;
        Integer size = orderNoList.size();
        Double count = Math.ceil(size.floatValue()/len.floatValue());
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < count.intValue(); i++ ) {
            List<String> subList = orderNoList.subList(i * len, Math.min((i + 1) * len, size));
            JsonResponse<List<CustomerOrderInfo>> response = customerOrderInfoFeign.listByOrderNos(subList);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
                return JsonResponse.fail(response.getMsg());
            }
            if (null == response.getData()) {
                continue;
            }
            response.getData().forEach(customerOrderInfo -> {
                CustomerOrderInfoExt dto = new CustomerOrderInfoExt();
                Integer oldOrderStatus = customerOrderInfo.getOrderStatus();
                Integer oldExcuteStatus = customerOrderInfo.getExcuteStatus();
                String oldExceptionType = customerOrderInfo.getExceptionType();
                String oldExceptionDesc = customerOrderInfo.getExceptionDesc();
                BeanUtils.copyProperties(customerOrderInfo, dto);
                warehouseAllocationService.allocationParse(dto);
                //更新父单
                CustomerOrderInfo customerOrderInfoUpdateDto = new CustomerOrderInfo();
                customerOrderInfoUpdateDto.setId(dto.getId());
                //由于zeebe切面会更新状态，需要还原订单原本的状态
                customerOrderInfoUpdateDto.setOrderStatus(oldOrderStatus);
                customerOrderInfoUpdateDto.setExcuteStatus(oldExcuteStatus);
                customerOrderInfoUpdateDto.setExceptionType(oldExceptionType);
                customerOrderInfoUpdateDto.setExceptionDesc(oldExceptionDesc);
                if (StringUtils.isNotBlank(dto.getOrderNo())) {
                    //更新子单
                    OrderInfo orderInfoUpdateDto = new OrderInfo();
                    orderInfoUpdateDto.setParentOrderNo(dto.getOrderNo());
                    orderInfoUpdateDto.setUpstreamDocType(dto.getUpstreamDocType());
                    JsonResponse response2 = orderInfoFeign.batchUpdateByParentOrderNo(Collections.singletonList(orderInfoUpdateDto));
                    if (!BaseCodeEnum.SUCCESS.getCode().equals(response2.getCode())) {
                        stringBuilder.append(dto.getOrderNo()).append("更新子单失败").append(response2.getMsg()).append(";");
                    }
                    //更新任务
                    Task taskUpdateDto = new Task();
                    taskUpdateDto.setParentOrderNo(dto.getOrderNo());
                    taskUpdateDto.setUpstreamDocType(dto.getUpstreamDocType());
                    JsonResponse response3 = taskFeign.batchUpdateByParentOrderNo(Collections.singletonList(taskUpdateDto));
                    if (!BaseCodeEnum.SUCCESS.getCode().equals(response3.getCode())) {
                        stringBuilder.append(dto.getOrderNo()).append("更新任务失败").append(response3.getMsg()).append(";");
                    }
                }
                JsonResponse<Integer> response1 = customerOrderInfoFeign.update(dto.getId(), customerOrderInfoUpdateDto);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(response1.getCode())) {
                    stringBuilder.append(dto.getOrderNo()).append("更新父单失败").append(response1.getMsg()).append(";");
                }
            });
        }
        return StringUtils.isBlank(stringBuilder.toString()) ? JsonResponse.success(null) : JsonResponse.fail(stringBuilder.toString());
    }

    /**
     * @description: 压测流程商超标识
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: wuhc6
     * @createtime: 2024-04-17 13:38:42
     */
    @GetMapping("/lmp/test/getScPosFlagTest")
    public JsonResponse getScPosFlagTest(@RequestParam("upperReceiverCode") String upperReceiverCode,
                                         @RequestParam(value = "orderNo", required = false) String orderNo){
        return scPosFlagHelper.getScPosFlag(upperReceiverCode, orderNo);
    }
}
