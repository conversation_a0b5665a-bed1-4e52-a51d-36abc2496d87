package com.midea.logistics.imp.orderverify.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.OrderInfoFeign;
import com.midea.logistics.otp.common.helper.DiversionRuleHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;

@RestController
public class DiversionRuleRest {

    @Autowired
    private DiversionRuleHelper flowHelper;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    /**
    * @description: 选择流程
    * @param: [orderInfo]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2020/11/14 9:44
    */
    @PostMapping("/lmp/validate/getDiversionRuleByCustomerOrderInfo")
    public JsonResponse getDiversionRuleByCustomerOrderInfo(@RequestBody CustomerOrderInfo customerOrderNo){
        String flowVersion = flowHelper.getDiversionRule(customerOrderNo);
        return JsonResponse.success(flowVersion);
    }

    /**
     * @description: 选择流程
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 9:44
     */
    @PostMapping("/lmp/validate/getDiversionRuleByOrderInfo")
    public JsonResponse getDiversionRuleByOrderInfo(@RequestBody OrderInfo orderInfo){
        String flowVersion = flowHelper.getDiversionRule(orderInfo);
        return JsonResponse.success(flowVersion);
    }

    /**
     * @param orderNo
     * @return
     */
    @PostMapping("/getDiversionRule")
    public JsonResponse getDiversionRule(@RequestBody OrderInfo orderInfo) {
        String msg = "";
        try {
            String orderNo = orderInfo.getOrderNo();
            CustomerOrderInfo search = new CustomerOrderInfo();
            search.setOrderNo(orderNo);
            CustomerOrderInfo customerOrderInfo =  customerOrderInfoFeign.searchOne(search).data();
            if (ToolUtils.isEmpty(customerOrderInfo)) {
                OrderInfo data = orderInfoFeign.selectByOrderNo(orderNo).data();
                msg = flowHelper.getDiversionRule(data);
            }else{
                msg = flowHelper.getDiversionRule(customerOrderInfo);
            }
           return  JsonResponse.success(msg);
        } catch (Exception e) {
            throw BusinessException.fail("查询父单子单失败，失败原因:" + e.getMessage());
        }
    }

}
