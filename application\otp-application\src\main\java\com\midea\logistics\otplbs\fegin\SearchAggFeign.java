package com.midea.logistics.otplbs.fegin;



import com.midea.logistics.otp.bean.AuthInfo;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoStatisticalQuery;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoInMapping;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoOutMapping;
import com.midea.logistics.otp.order.domain.request.PreposeTaskSearchRequest;
import com.midea.logistics.otp.order.domain.response.PreposeTaskSearchResponse;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "logistics-search-agg-service")
public interface SearchAggFeign {

    /**
     * @return 根据条件（包含父订单表数据+子表+地址表）查询订单（包含父订单表数据+子表+地址表）列表
     */
    @RequestMapping(value = "/customerOrderInfos", method = RequestMethod.GET)
    public JsonResponse<PageResponse<OrderInfoOutMapping>> search(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);

    /**
     * @return 查询总数
     */
    @RequestMapping(value = "/customerOrderInfosCount", method = RequestMethod.GET)
    public JsonResponse<Integer> customerOrderInfosCount(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);

    /**
     * @description: 调度工作台订单统计查询
     * @param: [orderInfoInMapping]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 刘院民
     * @createtime: 2024/12/02 10:38
     */
    @RequestMapping(value = "/orderStatisticalQuery", method = RequestMethod.GET)
    public JsonResponse searchCustomerOrderInfoStatistical(@SpringQueryMap CustomerOrderInfoStatisticalQuery query);

    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskList(@SpringQueryMap SearchTaskRequest searchTaskRequest);

    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskListPost(@RequestBody SearchTaskRequest searchTaskRequest);

    /**
     * @return 查询数量
     */
    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_COUNT)
    public JsonResponse<Integer> searchTaskCount(@SpringQueryMap SearchTaskRequest searchTaskRequest);

    /**
     * @return 查询数量
     */
    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_COUNT)
    public JsonResponse<Integer> searchTaskCountPost(@RequestBody SearchTaskRequest searchTaskRequest);


    /**
     * @return 查询集拼集合
     */
    @RequestMapping(value = "/preposeTask/searchPage", method = RequestMethod.POST)
    public JsonResponse searchPage(@RequestBody PreposeTaskSearchRequest preposeTaskSearchRequest);

    /**
     * @description: 查询模糊订单
     * @author: dumg
     * @create: 2023/6/5
     **/
    @GetMapping(OrderRouters.VAGUE_ORDER_SEARCH_FRONT)
    public JsonResponse<PageResponse<OrderInfoOutMapping>> vagueOrderSearchFront(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);

    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/preposeTask/searchEsPage", method = RequestMethod.POST)
    JsonResponse searchEsPage(@RequestBody PreposeTaskSearchRequest preposeTaskSearchRequest);

    /**
     * 查询用户-客户权限
     */
    @PostMapping(value = OrderRouters.SEARCH_AUTH_CUSTOMER)
    JsonResponse<PageResponse<AuthInfo>> searchAuthCustomer(@RequestBody AuthInfo searchRequest);
}
