package com.midea.logistics.otp.order.common.fegin;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.midea.logistics.otp.order.domain.dto.AiVoiceAppointDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.WmsInChangeWhRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskFeign
 * Author: luoh
 * Date: 2019年5月31日 下午3:12:06
 * Description: feign
 */
@FeignClient(value = "logistics-otp-task-service", url = BaseRouter.LOGISTICS_OTP_TASK_SERVICE)
public interface TaskFeign {
    
    /**
     * @description: 根据taskNo查询任务，包含删除的
     * @param: [task]
     * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.otp.task.domain.bean.Task>
     * @author: 陈永培
     * @createtime: 2023/7/26 13:46
     */
    @PostMapping(value = TaskRouters.SELECT_TASK_BY_TASKNO_INCLUDEDEL)
    JsonResponse<Task> selectTaskByTaskNoIncludeDel(@RequestBody Task task);
    
    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST_BY_TASKNOS)
    JsonResponse<List<Task>> searchTaskListByTaskNos(@RequestBody List<String> taskNo);
    @GetMapping(value = TaskRouters.TASK_APPT_LIST)
    JsonResponse<List<Task>> taskApptList(@RequestParam("taskNo") String taskNo);

    @RequestMapping(value = TaskRouters.TASKS_LIST, method = RequestMethod.GET)
    JsonResponse<List<Task>> list(@SpringQueryMap Task task);

    @RequestMapping(value = "/task", method = RequestMethod.POST)
    JsonResponse<Integer> create(@RequestBody Task task);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.GET)
    JsonResponse<Task> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody Task task);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.DELETE)
    JsonResponse<Integer> deleteByBusinessKey(@PathVariable("id") Long id);
    
    @RequestMapping(value = "/deleteByIdAndIncrementOrderNo/{id}", method = RequestMethod.DELETE)
    public JsonResponse<Integer> deleteByIdAndIncrementOrderNo(@PathVariable("id") Long id);

    @RequestMapping(value = "/tasks", method = RequestMethod.GET)
    JsonResponse<PageResponse<Task>> search(@SpringQueryMap Task task);

    @RequestMapping(value = "/tasks", method = RequestMethod.PUT)
    JsonResponse update(@RequestBody List<Task> tasks);


    @GetMapping("/car")
    JsonResponse queryCarCodeList(@RequestParam("code") String code);

    @GetMapping("/carArrivedInfo")
    JsonResponse getCarArrivedInfo(@RequestParam("code") String code);

    @PostMapping("/carArrivedInfo")
    JsonResponse saveCarArrivedInfo(@RequestBody String info);

    @GetMapping("/taskInfos")
    JsonResponse tasks(@RequestParam("task") Task task);

    @GetMapping("/taskInfo/{id}")
    JsonResponse task(@PathVariable("id") Long id);

    @PutMapping("/taskInfo/{id}")
    JsonResponse updateTask(@PathVariable("id") Long id, @RequestBody String reqBody);

    //根据子单号查询子订单-任务状态
    @RequestMapping(value = "/selectTask", method = RequestMethod.POST)
    JsonResponse<List<Task>> selectTask(@RequestBody List<String> parentOrderNoes);

    /**
     * 根据订单号找到task
     *
     * @param orderNo
     * @return
     */
    @RequestMapping(value = TaskRouters.SELECT_TASK_BY_ORDER_NO, method = RequestMethod.GET)
    JsonResponse<Task> selectTaskByOrderNo(@RequestParam("orderNo") String orderNo);

    /**
     * 根据订单号找到task
     *
     * @param taskNo
     * @return
     */
    @RequestMapping(value = TaskRouters.SELECT_TASK_BY_TASK_NO, method = RequestMethod.GET)
    JsonResponse<Task> selectTaskByTaskNo(@RequestParam("taskNo") String taskNo);
    @PostMapping(TaskRouters.TASK_GET_TASKS_BY_TASKNOS)
    public JsonResponse<List<Task>> selectTaskListByTaskNoList(@RequestBody List<String> taskNos);
    /**
     * 修改任务关闭状态
     *
     * @param task
     * @return
     */
    @RequestMapping(value = "/task", method = RequestMethod.PUT)
    JsonResponse update(@RequestBody Task task);

    @PostMapping(TaskRouters.TASK_GET_TASKS_BY_ORDERNOS)
    public JsonResponse<List<Task>> getTasksByOrderNos(@RequestBody List<String> orderNos);

    @PostMapping(TaskRouters.TASK_GET_TASKS_AND_ADDR_BY_ORDERNOS)
    public JsonResponse<List<TaskDetailsResponse>> getTasksAndAddrByOrderNos(@RequestBody List<String> orderNos);

    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST_DO_RDO)
    public JsonResponse<List<Task>> searchTaskListDoRdo(@SpringQueryMap SearchTaskRequest searchTaskRequest);

    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskList(@SpringQueryMap SearchTaskRequest searchTaskRequest);

    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_OUT_LIST)
    PageResponse<SearchTaskResponse> searchTaskOutList(SearchTaskRequest req);

    @RequestMapping(value = TaskRouters.TASK_SEARCH_ONE, method = RequestMethod.GET)
    public JsonResponse<Task> selectOne(@SpringQueryMap Task search);

    @RequestMapping(value = TaskRouters.TASK_SEARCH_ONE_BYRDIORRDO, method = RequestMethod.GET)
    public JsonResponse<Task> selectOneByRDIorRDO(@SpringQueryMap Task search);

    @RequestMapping(value = TaskRouters.TASK_SEARCH_ONE_BYRDIORRDOYS, method = RequestMethod.GET)
    public JsonResponse<Task> selectOneByRDIorRDOorYS(@SpringQueryMap Task search);

    @PostMapping(value = "/tasks/batchUpdateByParentOrderNo")
    JsonResponse batchUpdateByParentOrderNo(@RequestBody List<Task> tasks);

    @PostMapping(value = "/tasks/listItemBytaskNo")
    JsonResponse<List<TaskItem>> listItemBytaskNo(@RequestBody List<String> taskNoList);
    @PostMapping(value = "/tasks/listAddrBytaskNo")
    JsonResponse<List<TaskAddress>> listAddrBytaskNo(@RequestBody List<String> taskNoList);

    @RequestMapping(value = TaskRouters.TASKS_LIST_INCLUDE_DEL, method = RequestMethod.GET)
    JsonResponse<List<Task>> searchTaskIncludeDel(@SpringQueryMap Task task);

    @RequestMapping(value = "/taskDeleteOfTI", method = RequestMethod.PUT)
    JsonResponse deleteTaskOfTI(@RequestBody Task task);

    @RequestMapping(value = TaskRouters.TASK_SEARCH_ONE_BYRDIORRDOIT, method = RequestMethod.GET)
    public JsonResponse<Task> selectOneByRDIorRDOorIT(@SpringQueryMap Task search);

    @PostMapping(TaskRouters.TASK_GET_TASKS_BY_CUSTOMERORDERNOS)
    public JsonResponse<List<Task>> getTasksByCustomerOrderNos(@RequestBody List<String> customerOrderNos);

    @RequestMapping(value = TaskRouters.TASK_DETAIL_BY_TASK, method = RequestMethod.GET)
    JsonResponse<TaskExt> queryByTask(@SpringQueryMap Task task);

    @PutMapping(TaskRouters.TASK_BATCH_UPDATE_BY_TASK_NO)
    JsonResponse batchUpdateByTaskNo(@RequestBody List<Task> tasks);

    /**
     * 根据父单号查询所有的任务详情
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXT_BY_PARENT_ORDER_NO, method = RequestMethod.GET)
    public JsonResponse<List<TaskExt>> taskExtByParentOrderNo(@SpringQueryMap Task task);

    @PostMapping(TaskRouters.HAND_OVER_TIME_SEARCH_TASKS)
    public JsonResponse<List<Task>> handOverTimeSearchTasks(@RequestBody AiVoiceAppointDto aiVoiceAppointDto);

    @PostMapping(TaskRouters.HOLD_APPOINT_SEARCH_TASKS)
    public JsonResponse<List<Task>> holdAppointSearchTasks(@RequestBody AiVoiceAppointDto aiVoiceAppointDto);

    @PostMapping(TaskRouters.WMS_IN_CHANGE_WH_BY_TASK)
    public JsonResponse<Integer> wmsInChangeWhByTask(@RequestBody WmsInChangeWhRequest whRequest);

    /**
     * 任务详情查询
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXT_BY_INDEX, method = RequestMethod.POST)
    JsonResponse<List<TaskExt>> taskExtByIndex(@RequestBody TaskExt condition);

    @PostMapping(TaskRouters.LIST_BY_PARENT_ORDER_NOS)
    JsonResponse<List<Task>> listByParentOrderNos(@RequestBody List<String> parentOrderNos);
}