package com.midea.logistics.imp.orderverify.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.imp.orderverify.service.LoadingFeeAnalyzeService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.FeeType;
import com.midea.logistics.otp.enums.ServiceType;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.order.common.fegin.LoadingFeeRuleRestFegin;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.LoadingFeeRule;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

import static com.midea.logistics.otp.enums.JoinType.*;
import static com.midea.logistics.otp.enums.OrderType.*;

/**
* @description: !@装卸费规则（中台）
* @author: 陈永培
* @createtime: 2020/11/14 13:54
*/
@Service
@Slf4j
public class LoadingFeeAnalyzeServiceImpl implements LoadingFeeAnalyzeService {

    @Autowired
    private LoadingFeeRuleRestFegin loadingFeeRuleRestFegin;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private BusinessHelper businessHelper;


    @Override
    @ZeebeFlow("CALCULATE_COST")
    public OrderInfoExt loadingFeeAnalyze(OrderInfoExt orderInfo, Boolean updateFlag){

        CommonConstant.checkOrderInfo(orderInfo);

        // 获取客户 订单类型 是否自提
        String customerCode = orderInfo.getCustomerCode();
        String orderType = orderInfo.getOrderType();
        String deliveryType = orderInfo.getDeliveryType();
        String siteCode = orderInfo.getSiteCode();
        String whCode = orderInfo.getWhCode();
        String joinType = orderInfo.getJoinType();

        if (OrderType.isSOOrder(orderInfo.getOrderType())) {
            FlowListenerParam.success("服务单不更新装卸计费规则");
            return orderInfo;
        }



        OrderInfo newOrderInfo = new OrderInfo();
        newOrderInfo.setId(orderInfo.getId());
        newOrderInfo.setVersion(0L);

        LoadingFeeRule loadingFeeRule = new LoadingFeeRule();
        loadingFeeRule.setCustomerCode(customerCode);
        loadingFeeRule.setOrderType(orderType);
        loadingFeeRule.setSiteCode(siteCode);
        loadingFeeRule.setWhCode(whCode);

        //  查询装卸计费规则
        String feeType = "";

        /**
         * （1）order_info的join_type字段为“SHARE”或“1300”，或“TRANS_INV”时 -> 不产生收入成本
         */
        if ( Arrays.asList(SHARE.getKey(),SHARE1300.getKey(),TRANS_INV.getKey()).contains(joinType)){
            feeType = FeeType.N.getKey();
        }

        // ofc ,DP , service_type = 66 不产生收入和成本
        boolean sxqjExpress = businessHelper.isSxqjExpress(orderInfo);
        if (sxqjExpress) {
            feeType = FeeType.N.getKey();
        }

        JsonResponse<LoadingFeeRule> response = null;
        /**
         *  根据customerCode、orderType、deliveryType、siteCode、whCode、配送方式查询计费规则,精确查询
         */
         if(StringUtils.isNotEmpty(deliveryType) && StringUtils.isEmpty(feeType)){
             loadingFeeRule.setDeliveryType(deliveryType);
             response = loadingFeeRuleRestFegin.getLoadingFeeRuleConfig(loadingFeeRule);
             if (BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && null != response.getData()) {
                 loadingFeeRule = response.getData();
                 feeType = loadingFeeRule.getFeeType();
             } else if(BaseCodeEnum.FAILED.getCode().equals(response.getCode())){
                 BusinessException.fail("查询装卸费配置异常:"+JSON.toJSONString(response));
             }
         }
         //找不到配置，清空
         if(StringUtils.isEmpty(feeType)){
             loadingFeeRule.setDeliveryType("");
         }

        if (DeliveryType.isZT(deliveryType)) {
            loadingFeeRule.setPickFlag(CommonEnum.Y.getKey());
        } else {
            loadingFeeRule.setPickFlag(CommonEnum.N.getKey());
        }
        /**
         * （2）根据customerCode、orderType、deliveryType、siteCode、whCode、pickFlag去查询
         */
        if (StringUtils.isEmpty(feeType)) {
            response = loadingFeeRuleRestFegin.getLoadingFeeRuleConfig(loadingFeeRule);
            if (BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && null != response.getData()) {
                loadingFeeRule = response.getData();
                feeType = loadingFeeRule.getFeeType();
            }else if(BaseCodeEnum.FAILED.getCode().equals(response.getCode())){
                BusinessException.fail("查询装卸费配置异常:"+JSON.toJSONString(response));
            }
        }

        /**
         *  如果查询不到去掉pickFlag,继续查询
         */
        if (StringUtils.isEmpty(feeType)) {
            loadingFeeRule.setPickFlag("");
            response = loadingFeeRuleRestFegin.getLoadingFeeRuleConfig(loadingFeeRule);
            if (BaseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && null != response.getData()) {
                loadingFeeRule = response.getData();
                feeType = loadingFeeRule.getFeeType();
            }else if(BaseCodeEnum.FAILED.getCode().equals(response.getCode())){
                BusinessException.fail("查询装卸费配置异常:"+JSON.toJSONString(response));
            }
        }

        /**
         * (3) 自定义
         */
        if (StringUtils.isEmpty(feeType)) {

            // 1.状态调整单、状态调整出库单、状态调整入库单、数量调整出库单、数量调整入库单 -> 不产生收入成本
            if ( Arrays.asList(TF.getKey(),TFO.getKey(),TFI.getKey(),ADO.getKey(),ADI.getKey()).contains(orderType)){
                feeType = FeeType.N.getKey();
            }

        }

        /**
         * （4）针对来源系统为MRP，紧急订单标识为是（emergence_flag=1）的订单，装卸计费解析节点直接默认：只产生成本（order_rp_flag=P）;
         */
        boolean emergenceOrder = SourceSystem.isMRP(orderInfo.getSourceSystem()) && ObjectUtil.equals(CommonConstant.FLAG_YES, orderInfo.getEmergenceFlag());
        if (emergenceOrder && (orderInfo.getIsMrpJspsOrder() == null || !orderInfo.getIsMrpJspsOrder())) {
            feeType = FeeType.P.getKey();
        }

        /**
         * （5）其他，默认产生收入成本
         */
        if (StringUtils.isEmpty(feeType)) {
            feeType = FeeType.Y.getKey();
        }

        newOrderInfo.setOrderRpFlag(feeType);
        newOrderInfo.setOrderNo(orderInfo.getOrderNo());
        if(updateFlag || emergenceOrder) {
            orderHelper.updateOrderInfo(newOrderInfo, "更新装卸计费规则");
        }

        orderInfo.setOrderRpFlag(feeType);
        FlowListenerParam.success(FeeType.valueOf(feeType).getValue());
        return orderInfo;

    }
}
