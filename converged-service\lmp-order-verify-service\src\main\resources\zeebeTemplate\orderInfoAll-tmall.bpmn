<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-tmall" name="子订单流程-tmall" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0opo74t</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0opo74t" sourceRef="StartEvent_1" targetRef="Activity_0smupof" />
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_02zzxtp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_1gpho6a</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:sequenceFlow id="Flow_0p8hiid" sourceRef="Activity_087r4hi" targetRef="Gateway_0aeq2y6" />
    <bpmn:exclusiveGateway id="Gateway_0smudh3" name="是否获取运单号" default="Flow_1e8tnvs">
      <bpmn:incoming>Flow_0cu1b06</bpmn:incoming>
      <bpmn:incoming>Flow_1txw8rq</bpmn:incoming>
      <bpmn:outgoing>Flow_1e8tnvs</bpmn:outgoing>
      <bpmn:outgoing>Flow_0odft3i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Gateway_1l97bv7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e8tnvs" name="yes" sourceRef="Gateway_0smudh3" targetRef="Activity_1fv890g" />
    <bpmn:sequenceFlow id="Flow_039eniu" sourceRef="Activity_0tlqh7q" targetRef="Activity_1kkl3ix" />
    <bpmn:serviceTask id="Activity_0tlqh7q" name="时效产品">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="aging" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/agingParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_161nz2d</bpmn:incoming>
      <bpmn:outgoing>Flow_039eniu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0smupof" name="大小电解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="orderOutCollabWh" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/setOrderOutCollabWh" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0opo74t</bpmn:incoming>
      <bpmn:outgoing>Flow_1r3l5rj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
          <zeebe:output source="=responseBody.deliveryType" target="orderInfo.deliveryType" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_0p8hiid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e8tnvs</bpmn:incoming>
      <bpmn:outgoing>Flow_0a7ykl5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_195jet3" name="是否自提" default="Flow_161nz2d">
      <bpmn:incoming>Flow_0kcmu9a</bpmn:incoming>
      <bpmn:outgoing>Flow_161nz2d</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mwtbnu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161nz2d" name="no" sourceRef="Gateway_195jet3" targetRef="Activity_0tlqh7q" />
    <bpmn:sequenceFlow id="Flow_1mwtbnu" name="yes" sourceRef="Gateway_195jet3" targetRef="Activity_1kkl3ix">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and (orderInfo.orderType="DO" or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="PI") or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="AI"))) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_09m8o30" name="网点解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisNetRange" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_19skgsw</bpmn:incoming>
      <bpmn:outgoing>Flow_19j4232</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0aeq2y6" name="是否网点配送" default="Flow_06arwgx">
      <bpmn:incoming>Flow_0p8hiid</bpmn:incoming>
      <bpmn:outgoing>Flow_06arwgx</bpmn:outgoing>
      <bpmn:outgoing>Flow_19skgsw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_06arwgx" name="no" sourceRef="Gateway_0aeq2y6" targetRef="Gateway_1l97bv7" />
    <bpmn:sequenceFlow id="Flow_19skgsw" name="yes" sourceRef="Gateway_0aeq2y6" targetRef="Activity_09m8o30">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="NET") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0hvdqty" name="计费相关">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/anyBmsInfo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0odft3i</bpmn:incoming>
      <bpmn:incoming>Flow_1p59hxp</bpmn:incoming>
      <bpmn:outgoing>Flow_0ltk8av</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ltk8av" sourceRef="Activity_0hvdqty" targetRef="Activity_1elh4r5" />
    <bpmn:serviceTask id="Activity_1kkl3ix" name="推送查单系统,bms,net,task">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bmsLotsNetTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1mwtbnu</bpmn:incoming>
      <bpmn:incoming>Flow_039eniu</bpmn:incoming>
      <bpmn:outgoing>Flow_02zzxtp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02zzxtp" sourceRef="Activity_1kkl3ix" targetRef="Event_14d5skw" />
    <bpmn:serviceTask id="Activity_1guiusy" name="项目分类解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="projectType" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisProjectClassify" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1m564m7</bpmn:incoming>
      <bpmn:incoming>Flow_10ee7dq</bpmn:incoming>
      <bpmn:outgoing>Flow_1gpho6a</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1gpho6a" sourceRef="Activity_1guiusy" targetRef="Gateway_0mzkgmp" />
    <bpmn:sequenceFlow id="Flow_0a7ykl5" sourceRef="Activity_1fv890g" targetRef="Activity_0azspmi" />
    <bpmn:sequenceFlow id="Flow_0odft3i" sourceRef="Gateway_0smudh3" targetRef="Activity_0hvdqty">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=((orderInfo.businessMode="B2B" and orderInfo.deliveryType="ZT") or (orderInfo.businessMode="B2C" and orderInfo.orderType="PO" and orderInfo.deliveryType="ZT") or (orderInfo.orderType="PO" and orderInfo.deliveryType="EXPRESS"))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_1elh4r5" name="第三方解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="thirdPart" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/third/party/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ltk8av</bpmn:incoming>
      <bpmn:outgoing>Flow_0kcmu9a</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0kcmu9a" sourceRef="Activity_1elh4r5" targetRef="Gateway_195jet3" />
    <bpmn:serviceTask id="Activity_0pw88tu" name="爆仓">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="isOutArea" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/isOutArea" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1j37sel</bpmn:incoming>
      <bpmn:outgoing>Flow_1m564m7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1m564m7" sourceRef="Activity_0pw88tu" targetRef="Activity_1guiusy" />
    <bpmn:exclusiveGateway id="Gateway_041997v" default="Flow_10ee7dq">
      <bpmn:incoming>Flow_0fyggu2</bpmn:incoming>
      <bpmn:incoming>Flow_1p17klx</bpmn:incoming>
      <bpmn:outgoing>Flow_1j37sel</bpmn:outgoing>
      <bpmn:outgoing>Flow_10ee7dq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1j37sel" name="yes" sourceRef="Gateway_041997v" targetRef="Activity_0pw88tu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["MRPEOAO"] satisfies x = orderInfo.sourceSystem + orderInfo.upperOrderType + orderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10ee7dq" name="no" sourceRef="Gateway_041997v" targetRef="Activity_1guiusy" />
    <bpmn:serviceTask id="Activity_1n31thd" name="质押">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="pledge" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/pledgeCheck" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ljj8i2</bpmn:incoming>
      <bpmn:outgoing>Flow_0fyggu2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0mhdzqf" default="Flow_1p17klx">
      <bpmn:incoming>Flow_1r3l5rj</bpmn:incoming>
      <bpmn:outgoing>Flow_0ljj8i2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p17klx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1r3l5rj" sourceRef="Activity_0smupof" targetRef="Gateway_0mhdzqf" />
    <bpmn:sequenceFlow id="Flow_0ljj8i2" name="yes" sourceRef="Gateway_0mhdzqf" targetRef="Activity_1n31thd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.sourceSystem!=null and orderInfo.sourceSystem="MRP") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0fyggu2" sourceRef="Activity_1n31thd" targetRef="Gateway_041997v" />
    <bpmn:sequenceFlow id="Flow_1p17klx" name="no" sourceRef="Gateway_0mhdzqf" targetRef="Gateway_041997v" />
    <bpmn:serviceTask id="Activity_0azspmi" name="绑定虚拟号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bindingVirtualPhone" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0a7ykl5</bpmn:incoming>
      <bpmn:outgoing>Flow_1p59hxp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1p59hxp" sourceRef="Activity_0azspmi" targetRef="Activity_0hvdqty" />
    <bpmn:serviceTask id="Activity_0nqw1x0" name="电商分类解析2">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/ecommerceCategoriesAnalysisTag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0c286fj</bpmn:incoming>
      <bpmn:outgoing>Flow_0cu1b06</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0cu1b06" sourceRef="Activity_0nqw1x0" targetRef="Gateway_0smudh3" />
    <bpmn:exclusiveGateway id="Gateway_1l97bv7" default="Flow_1txw8rq">
      <bpmn:incoming>Flow_19j4232</bpmn:incoming>
      <bpmn:incoming>Flow_06arwgx</bpmn:incoming>
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:outgoing>Flow_0c286fj</bpmn:outgoing>
      <bpmn:outgoing>Flow_1txw8rq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_19j4232" sourceRef="Activity_09m8o30" targetRef="Gateway_1l97bv7" />
    <bpmn:sequenceFlow id="Flow_0c286fj" name="yes" sourceRef="Gateway_1l97bv7" targetRef="Activity_0nqw1x0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!=null and (orderInfo.orderType="DP" or orderInfo.orderType="SO")) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1txw8rq" sourceRef="Gateway_1l97bv7" targetRef="Gateway_0smudh3" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-tmall">
      <bpmndi:BPMNEdge id="Flow_0cu1b06_di" bpmnElement="Flow_0cu1b06">
        <di:waypoint x="1860" y="177" />
        <di:waypoint x="1895" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p59hxp_di" bpmnElement="Flow_1p59hxp">
        <di:waypoint x="2250" y="270" />
        <di:waypoint x="2300" y="270" />
        <di:waypoint x="2300" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p17klx_di" bpmnElement="Flow_1p17klx">
        <di:waypoint x="400" y="152" />
        <di:waypoint x="400" y="90" />
        <di:waypoint x="640" y="90" />
        <di:waypoint x="640" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="514" y="72" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fyggu2_di" bpmnElement="Flow_0fyggu2">
        <di:waypoint x="580" y="177" />
        <di:waypoint x="635" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ljj8i2_di" bpmnElement="Flow_0ljj8i2">
        <di:waypoint x="425" y="177" />
        <di:waypoint x="480" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="444" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r3l5rj_di" bpmnElement="Flow_1r3l5rj">
        <di:waypoint x="330" y="177" />
        <di:waypoint x="375" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ee7dq_di" bpmnElement="Flow_10ee7dq">
        <di:waypoint x="660" y="152" />
        <di:waypoint x="660" y="90" />
        <di:waypoint x="960" y="90" />
        <di:waypoint x="960" y="137" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="804" y="72" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j37sel_di" bpmnElement="Flow_1j37sel">
        <di:waypoint x="685" y="177" />
        <di:waypoint x="770" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="719" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m564m7_di" bpmnElement="Flow_1m564m7">
        <di:waypoint x="870" y="177" />
        <di:waypoint x="910" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kcmu9a_di" bpmnElement="Flow_0kcmu9a">
        <di:waypoint x="2390" y="360" />
        <di:waypoint x="2390" y="510" />
        <di:waypoint x="2365" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0odft3i_di" bpmnElement="Flow_0odft3i">
        <di:waypoint x="1945" y="177" />
        <di:waypoint x="2250" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a7ykl5_di" bpmnElement="Flow_0a7ykl5">
        <di:waypoint x="2100" y="270" />
        <di:waypoint x="2150" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gpho6a_di" bpmnElement="Flow_1gpho6a">
        <di:waypoint x="1010" y="177" />
        <di:waypoint x="1045" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02zzxtp_di" bpmnElement="Flow_02zzxtp">
        <di:waypoint x="1790" y="510" />
        <di:waypoint x="1605" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ltk8av_di" bpmnElement="Flow_0ltk8av">
        <di:waypoint x="2350" y="177" />
        <di:waypoint x="2390" y="177" />
        <di:waypoint x="2390" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06arwgx_di" bpmnElement="Flow_06arwgx">
        <di:waypoint x="1545" y="177" />
        <di:waypoint x="1665" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1647" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mwtbnu_di" bpmnElement="Flow_1mwtbnu">
        <di:waypoint x="2340" y="485" />
        <di:waypoint x="2340" y="420" />
        <di:waypoint x="1840" y="420" />
        <di:waypoint x="1840" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2137" y="402" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161nz2d_di" bpmnElement="Flow_161nz2d">
        <di:waypoint x="2315" y="510" />
        <di:waypoint x="2080" y="510" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2357" y="492" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_039eniu_di" bpmnElement="Flow_039eniu">
        <di:waypoint x="1980" y="510" />
        <di:waypoint x="1890" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e8tnvs_di" bpmnElement="Flow_1e8tnvs">
        <di:waypoint x="1920" y="202" />
        <di:waypoint x="1920" y="270" />
        <di:waypoint x="2000" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2027" y="237" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="1070" y="152" />
        <di:waypoint x="1070" y="70" />
        <di:waypoint x="1670" y="70" />
        <di:waypoint x="1670" y="170" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1071" y="100" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p8hiid_di" bpmnElement="Flow_0p8hiid">
        <di:waypoint x="1320" y="177" />
        <di:waypoint x="1495" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="1095" y="177" />
        <di:waypoint x="1220" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1114" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0opo74t_di" bpmnElement="Flow_0opo74t">
        <di:waypoint x="188" y="177" />
        <di:waypoint x="230" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19skgsw_di" bpmnElement="Flow_19skgsw">
        <di:waypoint x="1520" y="202" />
        <di:waypoint x="1520" y="270" />
        <di:waypoint x="1560" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1527" y="234" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19j4232_di" bpmnElement="Flow_19j4232">
        <di:waypoint x="1660" y="270" />
        <di:waypoint x="1670" y="270" />
        <di:waypoint x="1670" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c286fj_di" bpmnElement="Flow_0c286fj">
        <di:waypoint x="1715" y="177" />
        <di:waypoint x="1760" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1729" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1txw8rq_di" bpmnElement="Flow_1txw8rq">
        <di:waypoint x="1690" y="202" />
        <di:waypoint x="1690" y="270" />
        <di:waypoint x="1900" y="270" />
        <di:waypoint x="1900" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="1569" y="492" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1577" y="535" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="1045" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1058" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0smudh3_di" bpmnElement="Gateway_0smudh3" isMarkerVisible="true">
        <dc:Bounds x="1895" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1907" y="127" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tz6c6h_di" bpmnElement="Activity_0tlqh7q">
        <dc:Bounds x="1980" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_059zzl3_di" bpmnElement="Activity_0smupof">
        <dc:Bounds x="230" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="1220" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="2000" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_195jet3_di" bpmnElement="Gateway_195jet3" isMarkerVisible="true">
        <dc:Bounds x="2315" y="485" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2320" y="542" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0hvdqty_di" bpmnElement="Activity_0hvdqty">
        <dc:Bounds x="2250" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kkl3ix_di" bpmnElement="Activity_1kkl3ix">
        <dc:Bounds x="1790" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1guiusy_di" bpmnElement="Activity_1guiusy">
        <dc:Bounds x="910" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1elh4r5_di" bpmnElement="Activity_1elh4r5">
        <dc:Bounds x="2340" y="280" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pw88tu_di" bpmnElement="Activity_0pw88tu">
        <dc:Bounds x="770" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_041997v_di" bpmnElement="Gateway_041997v" isMarkerVisible="true">
        <dc:Bounds x="635" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1n31thd_di" bpmnElement="Activity_1n31thd">
        <dc:Bounds x="480" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mhdzqf_di" bpmnElement="Gateway_0mhdzqf" isMarkerVisible="true">
        <dc:Bounds x="375" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0azspmi_di" bpmnElement="Activity_0azspmi">
        <dc:Bounds x="2150" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0nqw1x0_di" bpmnElement="Activity_0nqw1x0">
        <dc:Bounds x="1760" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0aeq2y6_di" bpmnElement="Gateway_0aeq2y6" isMarkerVisible="true">
        <dc:Bounds x="1495" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1490" y="122" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1l97bv7_di" bpmnElement="Gateway_1l97bv7" isMarkerVisible="true">
        <dc:Bounds x="1665" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09m8o30_di" bpmnElement="Activity_09m8o30">
        <dc:Bounds x="1560" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
