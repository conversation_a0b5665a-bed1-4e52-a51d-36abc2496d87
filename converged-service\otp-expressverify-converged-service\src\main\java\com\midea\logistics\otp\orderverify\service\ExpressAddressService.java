package com.midea.logistics.otp.orderverify.service;

import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;

/**
 * 项目名称：logistics-otp
 * 功能说明：
 *
 * <AUTHOR>
 * @createtime 2021/10/8 16:03
 */
public interface ExpressAddressService {
    public FlowListenerParam addressMapping(CustomerOrderInfoExt dto);
}
