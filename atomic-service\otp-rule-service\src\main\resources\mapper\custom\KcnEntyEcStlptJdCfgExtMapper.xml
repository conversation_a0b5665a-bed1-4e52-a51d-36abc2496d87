<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.KcnEntyEcStlptJdCfgMapper">

    <update id="batchUpdateCanSetEmpty">
        <foreach collection="list" item="item" separator=";">
            UPDATE kcn_enty_ec_stlpt_jd_cfg
            <include refid="setBatchFieldsSqlCanSetEmpty"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

    <select id="listByExt" resultType="com.midea.logistics.otp.rule.domain.bean.KcnEntyEcStlptJdCfg">
        select
        <include refid="searchFieldsSql"/>
        from kcn_enty_ec_stlpt_jd_cfg t
        <include refid="whereFieldsSql"/>
        <if test="brandCodes !=null and brandCodes.size()>0">
            and brand_code in
            <foreach collection="brandCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
                ${orderByType}            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <sql id="setBatchFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
            <if test="item.classCode !=null">
                `class_code` = #{item.classCode},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.brandName !=null">
                `brand_name` = #{item.brandName},
            </if>
            <if test="item.orderSourcePlatformName !=null">
                `order_source_platform_name` = #{item.orderSourcePlatformName},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.className !=null">
                `class_name` = #{item.className},
            </if>
            <if test="item.shopFinance !=null">
                `shop_finance` = #{item.shopFinance},
            </if>
            <if test="item.settlePartment !=null">
                `settle_partment` = #{item.settlePartment},
            </if>
            <if test="item.settleShopName !=null">
                `settle_shop_name` = #{item.settleShopName},
            </if>
            <if test="item.orderSourcePlatform !=null">
                `order_source_platform` = #{item.orderSourcePlatform},
            </if>
            <if test="item.brandCode !=null">
                `brand_code` = #{item.brandCode},
            </if>
        </set>
    </sql>
</mapper>