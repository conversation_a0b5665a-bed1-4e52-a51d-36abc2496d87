package com.midea.logistics.otp.orderverify.service;

import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName:CustomerOrderInfoMapping
 * Author: CAIXZ
 * Date: 2019-6-7 16:19
 * Description:客户订单映射
 */
public interface CustomerOrderInfoMapping {

    /**
    * @description: 订单映射
    * @param: [customerOrderInfoExt]
    * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
    * @author: 陈永培
    * @createtime: 2021/10/4 15:51
    */
    FlowListenerParam mappingCustomerOrderInfo(CustomerOrderInfoExt customerOrderInfoExt);

}
