package com.midea.logistics.otp.order.common.fegin.fallback;

import com.midea.logistics.domain.mdm.domain.MidSiteWhControl;
import com.midea.logistics.domain.mdm.request.WhSiteCodeRequest;
import com.midea.logistics.otp.order.common.fegin.SiteGoodsTypeFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年07月18日 上午16:57
 */
@Component
@Slf4j
public class SiteGoodsTypeFeignFallBack  implements FallbackFactory<SiteGoodsTypeFeign> {
    @Override
    public SiteGoodsTypeFeign create(Throwable throwable) {
        return  new SiteGoodsTypeFeign() {
            @Override
            public JsonResponse<MidSiteWhControl> getSiteCode(WhSiteCodeRequest whSiteCodeRequest) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }
        };
    }
}
