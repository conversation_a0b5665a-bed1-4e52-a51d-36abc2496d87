package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.custom.CustomerAgingConfigRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©1968-2023 Midea Group,IT
 *
 * @Description:
 * @FileName: CustomerAgingConfigCustomFeign
 * @Author: fengxw26
 * @Date: 2023/3/11 18:46
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "CustomerAgingConfigCustomFeign")
public interface CustomerAgingConfigCustomFeign {

    @PostMapping("/custom/customerAgingConfigs/searchPageCount")
    JsonResponse searchPageCount(@RequestBody CustomerAgingConfigRequest customerAgingConfig);

    @PostMapping("/custom/customerAgingConfigs/searchPage")
    JsonResponse searchPage(@RequestBody CustomerAgingConfigRequest customerAgingConfig);

    @PostMapping("/custom/customerAgingConfig/detail")
    JsonResponse detail(@RequestBody CustomerAgingConfigRequest customerAgingConfig);

    @PostMapping("/custom/customerAgingConfig/createOrUpdateToSave")
    JsonResponse createOrUpdateToSave(@RequestBody CustomerAgingConfigRequest customerAgingConfig);

    @PostMapping("/custom/customerAgingConfig/getStandardNodeDetailList")
    JsonResponse getStandardNodeDetail(@RequestBody CustomerAgingConfigRequest customerAgingConfig);

    @PostMapping("/custom/customerAgingConfig/getStandardRuleNodeDetailList")
    JsonResponse getStandardRuleNodeDetail(@RequestBody CustomerAgingConfigRequest customerAgingConfig);

    @PostMapping("/custom/customerAgingConfig/batchStart")
    JsonResponse batchStart(List<Long> ids);

    @PostMapping("/custom/customerAgingConfig/batchStop")
    JsonResponse batchStop(List<Long> ids);
}
