package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.LogisticModeService;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.LogisticMode;
import com.midea.logistics.otp.order.common.fegin.WhConfigFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.WhConfig;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Component
public class LoigsticsModeServiceImpl implements LogisticModeService {

    @Autowired
    private WhConfigFeign whConfigFeign;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    /**
     * 解析运作模式
     *
     * @param orderInfoExt
     * @return
     */
    @Deprecated
    @Override
    @ZeebeFlow("LOGISTIC_MODE")
    public OrderInfoExt ansyLogisticMode(OrderInfoExt orderInfoExt) {

        OrderInfo orderInfo = orderInfoExt;

        if (DeliveryType.ZT.getKey().equals(orderInfo.getDeliveryType())) {
            FlowListenerParam.success("自提单无需解析运作模式");
            return orderInfoExt;
        }

        //4.logistic_mode根据客户、仓库解析物流运作模式（仓配分离、仓配一体），找到取运作模式，若找不到，则默认为仓配一起
        WhConfig whConfigSearch = new WhConfig();
        whConfigSearch.setCustomerCode(orderInfo.getCustomerCode());
        // TODO 仓库为空呢
        whConfigSearch.setWhCode(orderInfo.getWhCode());
        JsonResponse<PageResponse<WhConfig>> jsonWhConfig = whConfigFeign.getWhConfigs(whConfigSearch);
        if (jsonWhConfig == null || !BaseCodeEnum.SUCCESS.getCode().equals(jsonWhConfig.getCode()) || jsonWhConfig.data == null) {
            throw BusinessException.fail("仓库查询失败");
        }

        orderInfo.setLogisticMode(LogisticMode.TW.getKey());

        List<WhConfig> whConfigs = jsonWhConfig.data.list;
        if (!CollectionUtils.isEmpty(whConfigs) && StringUtils.isNotBlank(whConfigs.get(0).getLogisitcMode())) {
            orderInfo.setLogisticMode(whConfigs.get(0).getLogisitcMode());
        }

        OrderInfo newOrderInfo = new OrderInfo();
        newOrderInfo.setLogisticMode(orderInfo.getLogisticMode());
        newOrderInfo.setId(orderInfo.getId());
        newOrderInfo.setVersion(orderInfo.getVersion());
        newOrderInfo.setOrderNo(orderInfo.getOrderNo());
        lmpOrderFlowHelper.updateOrderInfo(newOrderInfo, "更新运作模式");

        LogisticMode logisticMode = LogisticMode.valueOf(orderInfo.getLogisticMode());
        orderInfoExt.setLogisticMode(orderInfo.getLogisticMode());
        FlowListenerParam.success(logisticMode.getValue());
        return orderInfoExt;
    }
}
