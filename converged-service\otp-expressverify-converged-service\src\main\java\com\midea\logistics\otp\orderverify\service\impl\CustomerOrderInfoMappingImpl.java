package com.midea.logistics.otp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.*;
import com.midea.logistics.domain.mdm.domain.*;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.orderAgg.OrderAggFeign;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.OrderMappingHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.bean.OrderLabel;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.dto.CustomerOrderInfoExtendConfDto;
import com.midea.logistics.otp.order.domain.request.CancelCacheResult;
import com.midea.logistics.otp.order.domain.request.CustomerOrderInfoUpdateRequest;
import com.midea.logistics.otp.order.domain.request.SearchCancelCahce;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.CustomerOrderInfoMapping;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
* @description: 客户订单对照映射
* @param:
* @return:
* @author: 陈永培
* @createtime: 2021/10/4 17:57
*/
@Service
public class CustomerOrderInfoMappingImpl implements CustomerOrderInfoMapping {

    private static final Logger logger = LoggerFactory.getLogger(CustomerOrderInfoMappingImpl.class);
    @Autowired
    private MidB2cCustomerControlManager midB2cCustomerControlManager;
    @Autowired
    private MidB2cSiteWhControlManager midB2cSiteWhControlManager;
    @Autowired
    private MidB2cOrdertypeControlManager midB2cOrdertypeControlManager;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private EbCustomerManager ebCustomerManager;
    @Autowired
    private EsCompanyManager esCompanyManager;
    @Autowired
    private OrderAggFeign orderAggFeign;
    @Autowired
    private OrderMappingHelper orderMappingHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;



    @Override
    @ExpressFlow(node = OrderOperateType.ORDER_MAPPING)
    public FlowListenerParam mappingCustomerOrderInfo(CustomerOrderInfoExt customerOrderInfoExt) {

        String orderNo = customerOrderInfoExt.getOrderNo();
        logger.info(" mapping CustomerOrderInfo By Flow in otp-express-converged-service orderNo:{}",orderNo);

        //校验订单是否有缓存
        CancelCacheResult cancelCacheResult = orderverifyHelper.checkOrderCancelCache(SearchCancelCahce.toSearch(customerOrderInfoExt));
        if (ToolUtils.isNotEmpty(cancelCacheResult)) {
            if(cancelCacheResult.isWholeCancel()) {
                return FlowListenerParam.cancel("订单接单前已接收取消指令，订单取消，流程终止");
            }
        }

        StringBuffer errorMsg = new StringBuffer();
        //客户对照
        String sourceSystem = customerOrderInfoExt.getSourceSystem();
        String businessMode = customerOrderInfoExt.getBusinessMode();
        String upperCustCode = customerOrderInfoExt.getUpperCustomerCode();
        String upperWhCode = customerOrderInfoExt.getUpperWhCode();
        String upperOrerTypeCode = customerOrderInfoExt.getUpperOrderType();

        if (StringUtils.isEmpty(sourceSystem)) {
            throw BusinessException.fail("订单系统来源为空");
        }
        if (StringUtils.isEmpty(businessMode)) {
            throw BusinessException.fail("业务模式不能为空");
        }

       logger.info("customerOrderCode:{},sourceSys:{},businessMode:{},upperCustCode:{},upperWhCode:{},upperOrerTypeCode:{}",orderNo, sourceSystem,businessMode, upperCustCode, upperWhCode, upperOrerTypeCode);

       CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
       JsonResponse<CustomerOrderInfoUpdateRequest> response = null;

       response = toCOrderMapping(customerOrderInfoExt, errorMsg,sourceSystem, upperCustCode, upperWhCode, upperOrerTypeCode);

        if (response == null || !BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            customerOrderInfo.setId(customerOrderInfoExt.getId());
            customerOrderInfo.setOrderStatus(OrderStatus.ORDER_FAILED.getKey());
            //更新订单状态为失败状态
            updateOrderStatus(customerOrderInfo);
            String msg = response!=null&&response.getMsg()!=null?response.getMsg():"对照失败，请重试";
            throw BusinessException.fail("映射失败:"+msg);
        }

        return FlowListenerParam.success("地址映射成功");
    }

    private JsonResponse toCOrderMapping(CustomerOrderInfoExt customerOrderInfoExt, StringBuffer errorMsg, String sourceSys, String upperCustCode, String upperWhCode, String upperOrerTypeCode) {
        CustomerOrderInfo oldData = new CustomerOrderInfo();
        BeanUtils.copyProperties(customerOrderInfoExt, oldData);

        MidB2cCustomerControl midB2cCustomerControl = midB2cCustomerControlManager.getMidB2cCustControlCache(upperCustCode,sourceSys);
        if (midB2cCustomerControl == null || StringUtils.isEmpty(midB2cCustomerControl.getAnnCustomerCode())) {
            logger.warn("upperCustCode:{} is not mapping in midB2cCustomer mapping config.......", upperCustCode);
            //异常处理
            errorMsg.append("对照表找不到对应客户映射主数据. 上游客户编码:" + upperCustCode);
            throw BusinessException.fail("对照表找不到对应客户映射主数据. 上游客户编码:" + upperCustCode);
        }
        //校验状态
        validateStatus(midB2cCustomerControl.getStatus(),upperCustCode,"客户对照已经停用");
        customerOrderInfoExt.setCustomerCode(midB2cCustomerControl.getAnnCustomerCode());
        customerOrderInfoExt.setCustomerName(midB2cCustomerControl.getAnnCustomerName());
        //增加是否第三方 0720
        customerOrderInfoExt.setThirdFlag(midB2cCustomerControl.getMideaFlag());
        setCustomerName(customerOrderInfoExt);

        // 扩展表扩展信息查询
        CustomerOrderInfoExtendConfDto confDto = new CustomerOrderInfoExtendConfDto();

        //设置客户组信息
        setCustomerGroup(customerOrderInfoExt, midB2cCustomerControl.getAnnCustomerCode(), confDto);

        boolean isGoodsMapFlag = midB2cCustomerControl.getGoodsMapFlag().intValue() == 0 ? true : false;
        customerOrderInfoExt.setIsGoodsMapFlag(isGoodsMapFlag);

        //2C订单类型对照
        if(OrderType.YS.getKey().equals(customerOrderInfoExt.getUpperOrderType())){
            customerOrderInfoExt.setOrderType(OrderType.YS.getKey());
            customerOrderInfoExt.setInOutType(OrderType.getBigType(OrderType.YS.getKey()));
            // 二次配送打标逻辑: 更新上游订单类型名称
            updateUpperOrderTypeName(confDto, OrderType.YS.getKey());
        }else{
            //先用平台查询订单类型映射
            mappingOrderTypeForB2c(customerOrderInfoExt, errorMsg, sourceSys, upperCustCode, upperOrerTypeCode, confDto);
        }

        // 扩展表更新
        updateCustomerOrderInfoExtendConfDto(customerOrderInfoExt, confDto);

        //扩展点映射
        boolean whMappingFlag = orderMappingHelper.searchWhRelation(customerOrderInfoExt);
        boolean b = OrderType.YS.getKey().equals(customerOrderInfoExt.getOrderType()) && ProjectClassifyEnum.LP.getKey().equals(customerOrderInfoExt.getProjectClassify());


        if (!whMappingFlag && !b) {
            //设置允许对照表内仓库为空的来源系统
            List<String> whCodeCanEmptySys = Arrays.asList(SourceSystem.ECM.getKey(), SourceSystem.CIMS.getKey(), SourceSystem.CCS.getKey(), SourceSystem.OFC.getKey());
            //2C分公司和仓库对照，分公司和仓库对照、网点
            //先用大类平台查询 ECM来源平台允许安得仓库编码为空 fix 2021-01-11 from 索超
            MidB2cSiteWhControl midB2cSiteWhControl = midB2cSiteWhControlManager.getMidB2cSiteWhControlCache(sourceSys, upperWhCode);
            if (midB2cSiteWhControl != null && StringUtils.isNotEmpty(midB2cSiteWhControl.getCompanyCode()) && StringUtils.isNotEmpty(midB2cSiteWhControl.getSiteCode()) &&
                (StringUtils.isNotEmpty(midB2cSiteWhControl.getWhCode()) || StringUtils.isEmpty(midB2cSiteWhControl.getWhCode()) && whCodeCanEmptySys.contains(sourceSys))) {
                //校验状态
                validateStatus(midB2cSiteWhControl.getStatus(),upperWhCode,"仓库对照已经停用");
                setSiteWhControlB2cInfo(customerOrderInfoExt, midB2cSiteWhControl);
            } else {
                MidB2cSiteWhControl midB2cSiteWhControl2 = midB2cSiteWhControlManager.getMidB2cSiteWhControlCache(upperCustCode, upperWhCode);
                //来源平台是ECM的允许安得仓库编码为空 2021-01-11 from 索超
                if (midB2cSiteWhControl2 == null || StringUtils.isEmpty(midB2cSiteWhControl2.getCompanyCode()) || StringUtils.isEmpty(midB2cSiteWhControl2.getSiteCode())
                    || (StringUtils.isEmpty(midB2cSiteWhControl2.getWhCode()) && !whCodeCanEmptySys.contains(sourceSys))) {
                    logger.warn("upperWhCode:{} and upperCustCode:{} is not mapping in midB2cSiteWhControl mapping config.......", upperWhCode, upperCustCode);
                    errorMsg.append("对照表找不到对应仓库映射主数据. 上游仓库编码:" + upperWhCode);
                    throw BusinessException.fail("对照表找不到对应仓库映射主数据.上游客户:"+upperCustCode+",上游仓库编码:" + upperWhCode);
                }
                //校验状态
                validateStatus(midB2cSiteWhControl2.getStatus(),upperWhCode,"仓库对照已经停用");
                setSiteWhControlB2cInfo(customerOrderInfoExt, midB2cSiteWhControl2);
            }
        }

        // 联系电话校验
        // 地址信息，收货人手机号为空时直接干掉
        CustomerOrderAddress customerOrderAddress = customerOrderInfoExt.getCustomerOrderAddress();
        String orderType = customerOrderInfoExt.getOrderType();
        String inOutType = customerOrderInfoExt.getInOutType();
        String deliveryType = customerOrderInfoExt.getDeliveryType();
        String businessMode = customerOrderInfoExt.getBusinessMode();
        if (StringUtils.isBlank(inOutType)){
            throw BusinessException.fail("出入库类型为空，请上游重新下单接单");
        }
        if (ToolUtils.isEmpty(businessMode)) {
            throw BusinessException.fail(String.format("业务模式为空；订单:{%s}校验失败", customerOrderInfoExt.getOrderNo()));
        }
    
        //2023年7月11日15:58:41  期阳：BUG2023071138919  原来下面这段代码是放在地址解析的，但是地址解析如果报错了，手工修改后，就不更新地址信息了，所以有问题，需要提前到订单映射
       //手机电话如果为空的话相互赋值
        if (!(DeliveryType.ZT.getKey().equals(deliveryType))
                && (InOutType.OUT.getName() == inOutType || InOutType.YS.getName() == inOutType)
                && !( BusinessMode.B2C.name().equals(businessMode) && orderType == OrderType.AO.getKey() )   // B2C的调拨出库单, 不需要校验
        ) {
            if (StringUtils.isBlank(customerOrderAddress.getReceiverMobile())) {
                customerOrderAddress.setReceiverMobile(customerOrderAddress.getReceiverTel());
            }
            if (StringUtils.isBlank(customerOrderAddress.getReceiverTel())) {
                customerOrderAddress.setReceiverTel(customerOrderAddress.getReceiverMobile());
            }
            if (StringUtils.isBlank(customerOrderAddress.getReceiverMobile())) {
                throw BusinessException.fail(ExceptionType.RECEIVER_TEL_EMPTY.getValue());
            }
        }

        //收货人电话为空, 订单无法继续，请重新下单”检查通过后校验执行 vip打标
        customerOrderInfoExt.setVipFlag(orderverifyHelper.getVipFlag(customerOrderAddress.getReceiverName(), customerOrderAddress.getReceiverMobile(), customerOrderAddress.getReceiverDetailAddr(), customerOrderInfoExt.getOrderType(), customerOrderInfoExt));

        // 校验代收订单代收合同校验和代收金额不能超1万
        orderverifyHelper.collectionFlagVerify(customerOrderInfoExt.getInOutType(), customerOrderInfoExt.getCollectionFlag(), customerOrderInfoExt.getCollectionAmount());

        //更新映射
        CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
        customerOrderInfoExt.setOrderStatus(OrderStatus.NEW.getKey());
        BeanUtils.copyProperties(customerOrderInfoExt, customerOrderInfo);
        //更新地址
        orderverifyHelper.updateCustomerOrderAddress(customerOrderAddress);
        orderverifyHelper.updateCustomerOrderInfoAndVersion(customerOrderInfo, oldData, "订单信息");
        customerOrderAddress.setVersion(customerOrderAddress.getVersion()+1);

        return JsonResponse.success("订单映射成功");
    }

    private void setSiteWhControlB2cInfo(CustomerOrderInfoExt customerOrderInfo, MidB2cSiteWhControl midB2cSiteWhControl) {
        customerOrderInfo.setCompanyName(midB2cSiteWhControl.getCompanyName());
        customerOrderInfo.setCompanyCode(midB2cSiteWhControl.getCompanyCode());
        customerOrderInfo.setSiteCode(midB2cSiteWhControl.getSiteCode());
        customerOrderInfo.setSiteName(midB2cSiteWhControl.getSiteName());
        customerOrderInfo.setWhCode(midB2cSiteWhControl.getWhCode());
        customerOrderInfo.setWhName(midB2cSiteWhControl.getWhName());
        String siteCode = midB2cSiteWhControl.getSiteCode();
        if(StringUtils.isEmpty(siteCode)){
            throw BusinessException.fail("网点仓库对照中平台编码为空");
        }
        setCompanyInfoBySiteCode(siteCode,customerOrderInfo);
    }

    /**
     * 根据平台
     * @param sideCode
     */
    private void setCompanyInfoBySiteCode(String sideCode,CustomerOrderInfoExt customerOrderInfo){
        EsCompany esCompany = esCompanyManager.getEsCompanyCache(sideCode);
        if(esCompany==null){
            throw BusinessException.fail("调用基础数据获取不到平台信息");
        }
        if(esCompany!=null && !"ENABLE".equals(esCompany.getEscoStatus())){
            throw BusinessException.fail("平台不可用,平台编码:"+sideCode);
        }
        customerOrderInfo.setSiteName(esCompany.getEscoCompanyNameCn());
        String companyCode = esCompany.getCompanyCode();
        if(StringUtils.isEmpty(companyCode)){
            throw BusinessException.fail("根据平台找不到公司信息");
        }
        EsCompany company = esCompanyManager.getEsCompanyCache(companyCode);
        if(company==null){
            throw BusinessException.fail("调用基础数据获取不到公司信息");
        }
        if(company!=null && !"ENABLE".equals(company.getEscoStatus())){
            throw BusinessException.fail("平台不可用,公司编码:"+companyCode);
        }
        customerOrderInfo.setCompanyCode(companyCode);
        customerOrderInfo.setCompanyName(company.getEscoCompanyNameCn());
    }


    //B2C订单类型映射
    private void mappingOrderTypeForB2c(CustomerOrderInfoExt customerOrderInfoExt, StringBuffer errorMsg, String sourceSys, String upperCustCode, String upperOrerTypeCode, CustomerOrderInfoExtendConfDto confDto) {

        MidB2cOrdertypeControl midB2cOrdertypeControl = midB2cOrdertypeControlManager.getMidB2cOrdertypeControlCache(sourceSys, upperOrerTypeCode);
        if (midB2cOrdertypeControl != null && !StringUtils.isEmpty(midB2cOrdertypeControl.getAnnOrdertypeCode())) {
            //校验状态
            validateStatus(midB2cOrdertypeControl.getStatus(),upperOrerTypeCode,"订单类型对照已经停用");
            customerOrderInfoExt.setOrderType(midB2cOrdertypeControl.getAnnOrdertypeCode());
            customerOrderInfoExt.setInOutType(OrderType.getBigType(midB2cOrdertypeControl.getAnnOrdertypeCode()));
            // 二次配送打标逻辑: 更新上游订单类型名称
            updateUpperOrderTypeName(confDto, midB2cOrdertypeControl.getCustOrdertypeName());
        } else {
            MidB2cOrdertypeControl midB2cOrdertypeControl2 = midB2cOrdertypeControlManager.getMidB2cOrdertypeControlCache(upperCustCode, upperOrerTypeCode);
            if (midB2cOrdertypeControl2 == null || StringUtils.isEmpty(midB2cOrdertypeControl2.getAnnOrdertypeCode())) {
                logger.warn("upperOrerTypeCode:{} is not mapping in 2C mapping config.......", upperOrerTypeCode);
                //异常处理
                errorMsg.append("对照表找不到对应订单类型映射主数据.上游客户:"+upperCustCode+",上游客户订单号:" + upperOrerTypeCode);
                throw BusinessException.fail("对照表找不到对应订单类型映射主数据.客户编码:"+upperCustCode+",上游订单类型:" + upperOrerTypeCode);
            }
            //校验状态
            validateStatus(midB2cOrdertypeControl2.getStatus(),upperOrerTypeCode,"订单类型对照已经停用");
            customerOrderInfoExt.setOrderType(midB2cOrdertypeControl2.getAnnOrdertypeCode());
            customerOrderInfoExt.setInOutType(OrderType.getBigType(midB2cOrdertypeControl2.getAnnOrdertypeCode()));
            // 二次配送打标逻辑: 更新上游订单类型名称
            updateUpperOrderTypeName(confDto, midB2cOrdertypeControl2.getCustOrdertypeName());
        }

    }


    /**
     * 二次配送打标逻辑: 更新上游订单类型名称
     * @param confDto 父单
     * @param upperOrderTypeName 上游订单类型名称
     */
    public void updateUpperOrderTypeName(CustomerOrderInfoExtendConfDto confDto, String upperOrderTypeName) {
        confDto.setUpperOrderTypeName(upperOrderTypeName);
    }

    private void setCustomerGroup(CustomerOrderInfoExt customerOrderInfoExt, String customerNo, CustomerOrderInfoExtendConfDto confDto) {
        EbCustomer ebCustomer = ebCustomerManager.getEbCustomerCache(customerNo);
        if (ebCustomer != null) {
            customerOrderInfoExt.setCustomerGroup(ebCustomer.getEbcuCustomerGroupName());
            // industryType行业类型、channel内外渠
            confDto.setIndustryType(ebCustomer.getEbcuIndustryType());
            confDto.setChannel(ebCustomer.getChannel());
        }
    }


    public CustomerOrderInfoExtendConfDto selectCustomerOrderInfoExtendConfDto(CustomerOrderInfoExtend customerOrderInfoExtend) {

        if (customerOrderInfoExtend != null) {
            String confObj = customerOrderInfoExtend.getConfObj();
            if (StringUtils.isNotBlank(confObj)) {
                CustomerOrderInfoExtendConfDto confDto = JSON.parseObject(confObj, CustomerOrderInfoExtendConfDto.class);
                if (confDto == null) {
                    confDto = new CustomerOrderInfoExtendConfDto();
                }
                return confDto;
            }
        }

        return new CustomerOrderInfoExtendConfDto();
    }


    public void updateCustomerOrderInfoExtendConfDto(CustomerOrderInfoExt customerOrder, CustomerOrderInfoExtendConfDto confDto) {

        CustomerOrderInfoExtend customerOrderInfoExtend = new CustomerOrderInfoExtend();
        if(null != customerOrder.getCustomerOrderInfoExtend() && null != customerOrder.getCustomerOrderInfoExtend().getId()){
            customerOrderInfoExtend = customerOrder.getCustomerOrderInfoExtend();
        } else {
            customerOrderInfoExtend.setOrderNo(customerOrder.getOrderNo());
            customerOrderInfoExtend = customerOrderInfoExtendFeign.selectOne(customerOrderInfoExtend).data;
            customerOrder.setCustomerOrderInfoExtend(customerOrderInfoExtend);
        }

        if (customerOrderInfoExtend != null) {

            customerOrder.setCustomerOrderInfoExtend(customerOrderInfoExtend);
            CustomerOrderInfoExtendConfDto oldConfDto = selectCustomerOrderInfoExtendConfDto(customerOrderInfoExtend);

            // industryType行业类型、channel内外渠
            oldConfDto.setIndustryType(confDto.getIndustryType());
            oldConfDto.setChannel(confDto.getChannel());
            //  更新上游订单类型名称
            oldConfDto.setUpperOrderTypeName(confDto.getUpperOrderTypeName());
            customerOrderInfoExtend.setConfObj(JSON.toJSONString(oldConfDto));
            customerOrderInfoExtendFeign.update(customerOrderInfoExtend.getId(), customerOrderInfoExtend);
        }

        // 同步订单标签
        syncOrderLabel(customerOrder, confDto);
    }


    /**
     * 同步订单标签
     * @param confDto
     */
    private void syncOrderLabel(CustomerOrderInfoExt customerOrder, CustomerOrderInfoExtendConfDto confDto) {
        CompletableFuture.runAsync(()->{
            List<OrderLabel> orderLabels = Lists.newArrayList();
            if(StringUtils.isNotBlank(confDto.getIndustryType())){
                OrderLabel label = new OrderLabel();
                label.setLevelOne("orderLabel");
                label.setLevelTwo("customerType");
                label.setCustomerOrderNo(customerOrder.getCustomerOrderNo());
                label.setParentOrderNo(customerOrder.getOrderNo());
                label.setLabelKey("industry_type");
                label.setLabelValue(confDto.getIndustryType());
                orderLabels.add(label);
            }
            if(StringUtils.isNotBlank(confDto.getChannel())){
                OrderLabel label = new OrderLabel();
                label.setLevelOne("orderLabel");
                label.setLevelTwo("customerType");
                label.setCustomerOrderNo(customerOrder.getCustomerOrderNo());
                label.setParentOrderNo(customerOrder.getOrderNo());
                label.setLabelKey("channel");
                label.setLabelValue(confDto.getChannel());
                orderLabels.add(label);
            }

            if(CollectionUtils.isNotEmpty(orderLabels)) {
                orderAggFeign.orderLabelUpdateAndSave(orderLabels);
            }
        });
    }


    private void updateOrderStatus(CustomerOrderInfo customerOrderInfo) {
        JsonResponse jsonResponse = customerOrderInfoFeign.update(customerOrderInfo.getId(), customerOrderInfo);
        if (jsonResponse == null || !BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            throw BusinessException.fail("更新订单状态失败!");
        }
    }

    private void validateStatus(Integer status,String code,String msg){
        if (CommonMdmEnum.DISABLE.getValue().equals(String.valueOf(status))) {
            throw BusinessException.fail("["+msg +":"+ code + "]");
        }
    }

    private void setCustomerName(CustomerOrderInfoExt customerOrderInfoExt){
        EbCustomer ebCustomer = ebCustomerManager.getEbCustomerCache(customerOrderInfoExt.getCustomerCode());
        if(ebCustomer!=null &&  StringUtils.isNotEmpty(ebCustomer.getEbcuNameCn())){
            customerOrderInfoExt.setCustomerName(ebCustomer.getEbcuNameCn());
        }
    }

}
