<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all" name="父订单流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1vr9m96</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0d15yei" name="商品确认">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_165ux09</bpmn:incoming>
      <bpmn:outgoing>Flow_0bq44cb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1vr9m96" sourceRef="StartEvent_1" targetRef="Gateway_1heqigx" />
    <bpmn:serviceTask id="Activity_1wq7s30" name="箱包转换1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/luggage/conversion" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_18ap7bj</bpmn:incoming>
      <bpmn:outgoing>Flow_1n2ghnq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1oyv8yd" sourceRef="Activity_1k6ho42" targetRef="Activity_0ur58zi" />
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k3rco2</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyv8yd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0a0g75l" name="MIP流程">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/mipExecutionAudit" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zdzxft</bpmn:incoming>
      <bpmn:outgoing>Flow_0ap55e3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_12lns6w" name="是否走mip流程" default="Flow_1ajskls">
      <bpmn:incoming>Flow_0zl7ioc</bpmn:incoming>
      <bpmn:incoming>Flow_1n2ghnq</bpmn:incoming>
      <bpmn:outgoing>Flow_1ajskls</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zdzxft</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ajskls" name="no" sourceRef="Gateway_12lns6w" targetRef="Gateway_1yfd9e1" />
    <bpmn:sequenceFlow id="Flow_0zdzxft" name="yes" sourceRef="Gateway_12lns6w" targetRef="Activity_0a0g75l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.mipFlag!=null and customerOrderInfo.mipFlag=1)=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0nccbee" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="receive" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1uewv0z</bpmn:incoming>
      <bpmn:outgoing>Flow_07qlm7g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ap55e3" sourceRef="Activity_0a0g75l" targetRef="Gateway_1yfd9e1" />
    <bpmn:serviceTask id="Activity_119zmlg" name="仓间调拨解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/allocationParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="allocationParse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1n6udmd</bpmn:incoming>
      <bpmn:outgoing>Flow_0v8tmtg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/addressResolving" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k9k1b7</bpmn:incoming>
      <bpmn:outgoing>Flow_0k4iezn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kptmf2" name="货权转移校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/cargo/right/transfer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p7943l</bpmn:incoming>
      <bpmn:outgoing>Flow_1wuusbt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0e9epnv</bpmn:incoming>
      <bpmn:incoming>Flow_1h5cycp</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1g2qvk8" name="是否仓间调拨" default="Flow_1odmwry">
      <bpmn:incoming>Flow_07qlm7g</bpmn:incoming>
      <bpmn:incoming>Flow_1eh4qu3</bpmn:incoming>
      <bpmn:outgoing>Flow_1n6udmd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1odmwry</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07qlm7g" sourceRef="Activity_0nccbee" targetRef="Gateway_1g2qvk8" />
    <bpmn:sequenceFlow id="Flow_1n6udmd" name="yes" sourceRef="Gateway_1g2qvk8" targetRef="Activity_119zmlg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO") or (customerOrderInfo.sourceSystem="TTX" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AO")) or (customerOrderInfo.sourceSystem="OFC" and customerOrderInfo.orderType="AO"))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1odmwry" sourceRef="Gateway_1g2qvk8" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_0v8tmtg" sourceRef="Activity_119zmlg" targetRef="Gateway_04dmfyf" />
    <bpmn:exclusiveGateway id="Gateway_0fo5mo8" name="是否货权转移" default="Flow_1o83mga">
      <bpmn:incoming>Flow_13i1do9</bpmn:incoming>
      <bpmn:incoming>Flow_044v17i</bpmn:incoming>
      <bpmn:outgoing>Flow_1o83mga</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p7943l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1b565p2" name="是否分仓拆单">
      <bpmn:incoming>Flow_1o83mga</bpmn:incoming>
      <bpmn:incoming>Flow_1wuusbt</bpmn:incoming>
      <bpmn:outgoing>Flow_0pug8rq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o83mga" name="no" sourceRef="Gateway_0fo5mo8" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_1p7943l" name="yes" sourceRef="Gateway_0fo5mo8" targetRef="Activity_0kptmf2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CAINIAOPO","CCSJDPI","CCSPO","CCSPI","CIMSPO","CIMSAI","CIMSPI","CIMSRI","PDDRO","PDDPI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wuusbt" sourceRef="Activity_0kptmf2" targetRef="Gateway_1b565p2" />
    <bpmn:serviceTask id="Activity_0ur58zi" name="订单信息校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/orderInfoConfirm" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="orderInfoConfirm" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1oyv8yd</bpmn:incoming>
      <bpmn:incoming>Flow_1dkxcsb</bpmn:incoming>
      <bpmn:outgoing>Flow_051us7v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Event_09zbrsb" />
    <bpmn:exclusiveGateway id="Gateway_1heqigx" name="是否手工单" default="Flow_1k3rco2">
      <bpmn:incoming>Flow_1vr9m96</bpmn:incoming>
      <bpmn:outgoing>Flow_1k3rco2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dkxcsb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1k3rco2" name="NO" sourceRef="Gateway_1heqigx" targetRef="Activity_1k6ho42" />
    <bpmn:sequenceFlow id="Flow_1dkxcsb" name="yes" sourceRef="Gateway_1heqigx" targetRef="Activity_0ur58zi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.orderSource="HANDLE") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1yfd9e1" name="是否走收货单位解析" default="Flow_1eh4qu3">
      <bpmn:incoming>Flow_1ajskls</bpmn:incoming>
      <bpmn:incoming>Flow_0ap55e3</bpmn:incoming>
      <bpmn:outgoing>Flow_1uewv0z</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eh4qu3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1uewv0z" name="yes" sourceRef="Gateway_1yfd9e1" targetRef="Activity_0nccbee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CIMSPO","CCSPO","CAINIAOAO","CAINIAOYS","CIMSRI","OFCPO","OFCAO","SKYWORTHPO","SKYWORTHRI","SKYWORTHYS","COCO-WBKHPO","COCO-WBKHYS","COCO-WBKHRI","HANDLEPO","HANDLERI","HANDLEYS","TONGYI-WBKHPO","TONGYI-WBKHRI","TONGYI-WBKHYS","JOINTOWNPO","JOINTOWNRI","JOINTOWNYS"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1eh4qu3" name="no" sourceRef="Gateway_1yfd9e1" targetRef="Gateway_1g2qvk8" />
    <bpmn:sequenceFlow id="Flow_13i1do9" sourceRef="Activity_1iziya6" targetRef="Gateway_0fo5mo8" />
    <bpmn:serviceTask id="Activity_1iziya6" name="服务产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/productConfig" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ctnvkr</bpmn:incoming>
      <bpmn:outgoing>Flow_13i1do9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_07sc21e" name="禁用服务产品" default="Flow_044v17i">
      <bpmn:incoming>Flow_1g2opju</bpmn:incoming>
      <bpmn:outgoing>Flow_044v17i</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ctnvkr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ctnvkr" sourceRef="Gateway_07sc21e" targetRef="Activity_1iziya6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_044v17i" sourceRef="Gateway_07sc21e" targetRef="Gateway_0fo5mo8" />
    <bpmn:serviceTask id="Activity_06hwd3w" name="配送方式配置">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/customerDeliveryTypeReset" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vc381a</bpmn:incoming>
      <bpmn:outgoing>Flow_0e9epnv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0e9epnv" sourceRef="Activity_06hwd3w" targetRef="Activity_0uruim6" />
    <bpmn:sequenceFlow id="Flow_1k9k1b7" name="yes" sourceRef="Gateway_04dmfyf" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and (((customerOrderInfo.deliveryType!=null)=false or (customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT")) and (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO") and (customerOrderInfo.orderSource!=null and customerOrderInfo.orderSource!="HANDLE")))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_04dmfyf" name="是否地址解析" default="Flow_0x4bwzp">
      <bpmn:incoming>Flow_0v8tmtg</bpmn:incoming>
      <bpmn:incoming>Flow_1odmwry</bpmn:incoming>
      <bpmn:outgoing>Flow_1k9k1b7</bpmn:outgoing>
      <bpmn:outgoing>Flow_0x4bwzp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0x4bwzp" sourceRef="Gateway_04dmfyf" targetRef="Activity_0gmb5h1" />
    <bpmn:exclusiveGateway id="Gateway_1tixbxc" name="是否配置配送方式" default="Flow_1h5cycp">
      <bpmn:incoming>Flow_0pug8rq</bpmn:incoming>
      <bpmn:outgoing>Flow_1h5cycp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vc381a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0pug8rq" sourceRef="Gateway_1b565p2" targetRef="Gateway_1tixbxc" />
    <bpmn:sequenceFlow id="Flow_1h5cycp" name="no" sourceRef="Gateway_1tixbxc" targetRef="Activity_0uruim6" />
    <bpmn:sequenceFlow id="Flow_0vc381a" name="yes" sourceRef="Gateway_1tixbxc" targetRef="Activity_06hwd3w">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["XINLONGPODELIVERY","XINLONGRIDELIVERY"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType + customerOrderInfo.deliveryType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0k4iezn" sourceRef="Activity_1i377su" targetRef="Activity_0gmb5h1" />
    <bpmn:sequenceFlow id="Flow_1g2opju" sourceRef="Activity_0gmb5h1" targetRef="Gateway_07sc21e" />
    <bpmn:serviceTask id="Activity_0gmb5h1" name="标准产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/standardProductAnalysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0k4iezn</bpmn:incoming>
      <bpmn:incoming>Flow_0x4bwzp</bpmn:incoming>
      <bpmn:outgoing>Flow_1g2opju</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1rjzhmw" name="是否箱包转换" default="Flow_0zl7ioc">
      <bpmn:incoming>Flow_0bq44cb</bpmn:incoming>
      <bpmn:incoming>Flow_17ydl9v</bpmn:incoming>
      <bpmn:outgoing>Flow_18ap7bj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zl7ioc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0bq44cb" sourceRef="Activity_0d15yei" targetRef="Gateway_1rjzhmw" />
    <bpmn:sequenceFlow id="Flow_18ap7bj" name="yes" sourceRef="Gateway_1rjzhmw" targetRef="Activity_1wq7s30">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(((customerOrderInfo.sourceSystem + customerOrderInfo.customerCode) in ["KingDee-SD","PGA0014861","PGA0023924","HANDLEA0014861","HANDLEA0023924","COLGATEA202308212497","HANDLEA202411250083","HANDLEA202308212829"])=true or (customerOrderInfo.sourceSystem in ["ZP-WBKH","JBLSAP-WBKH","JBLPOP-WBKH"])=true)=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0zl7ioc" name="no" sourceRef="Gateway_1rjzhmw" targetRef="Gateway_12lns6w" />
    <bpmn:sequenceFlow id="Flow_1n2ghnq" sourceRef="Activity_1wq7s30" targetRef="Gateway_12lns6w" />
    <bpmn:sequenceFlow id="Flow_051us7v" sourceRef="Activity_0ur58zi" targetRef="Gateway_0tyl3vr" />
    <bpmn:exclusiveGateway id="Gateway_0tyl3vr" name="是否跳过商品确认" default="Flow_165ux09">
      <bpmn:incoming>Flow_051us7v</bpmn:incoming>
      <bpmn:outgoing>Flow_165ux09</bpmn:outgoing>
      <bpmn:outgoing>Flow_17ydl9v</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_165ux09" name="no" sourceRef="Gateway_0tyl3vr" targetRef="Activity_0d15yei" />
    <bpmn:sequenceFlow id="Flow_17ydl9v" name="yes" sourceRef="Gateway_0tyl3vr" targetRef="Gateway_1rjzhmw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.projectClassify="TRANSPORT_STANDARD") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all">
      <bpmndi:BPMNEdge id="Flow_17ydl9v_di" bpmnElement="Flow_17ydl9v">
        <di:waypoint x="620" y="255" />
        <di:waypoint x="620" y="310" />
        <di:waypoint x="860" y="310" />
        <di:waypoint x="860" y="255" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="732" y="292" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_165ux09_di" bpmnElement="Flow_165ux09">
        <di:waypoint x="645" y="230" />
        <di:waypoint x="690" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="661" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_051us7v_di" bpmnElement="Flow_051us7v">
        <di:waypoint x="570" y="230" />
        <di:waypoint x="595" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n2ghnq_di" bpmnElement="Flow_1n2ghnq">
        <di:waypoint x="990" y="150" />
        <di:waypoint x="1000" y="150" />
        <di:waypoint x="1000" y="226" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zl7ioc_di" bpmnElement="Flow_0zl7ioc">
        <di:waypoint x="885" y="230" />
        <di:waypoint x="996" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="934" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18ap7bj_di" bpmnElement="Flow_18ap7bj">
        <di:waypoint x="860" y="205" />
        <di:waypoint x="860" y="150" />
        <di:waypoint x="890" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="867" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bq44cb_di" bpmnElement="Flow_0bq44cb">
        <di:waypoint x="790" y="230" />
        <di:waypoint x="835" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g2opju_di" bpmnElement="Flow_1g2opju">
        <di:waypoint x="1830" y="400" />
        <di:waypoint x="1830" y="445" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k4iezn_di" bpmnElement="Flow_0k4iezn">
        <di:waypoint x="1830" y="270" />
        <di:waypoint x="1830" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vc381a_di" bpmnElement="Flow_0vc381a">
        <di:waypoint x="1300" y="495" />
        <di:waypoint x="1300" y="620" />
        <di:waypoint x="1250" y="620" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1307" y="555" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h5cycp_di" bpmnElement="Flow_1h5cycp">
        <di:waypoint x="1275" y="470" />
        <di:waypoint x="1140" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1201" y="452" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pug8rq_di" bpmnElement="Flow_0pug8rq">
        <di:waypoint x="1355" y="470" />
        <di:waypoint x="1325" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x4bwzp_di" bpmnElement="Flow_0x4bwzp">
        <di:waypoint x="1570" y="255" />
        <di:waypoint x="1570" y="360" />
        <di:waypoint x="1780" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9k1b7_di" bpmnElement="Flow_1k9k1b7">
        <di:waypoint x="1595" y="230" />
        <di:waypoint x="1780" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1627" y="213" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e9epnv_di" bpmnElement="Flow_0e9epnv">
        <di:waypoint x="1150" y="620" />
        <di:waypoint x="1090" y="620" />
        <di:waypoint x="1090" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_044v17i_di" bpmnElement="Flow_044v17i">
        <di:waypoint x="1830" y="495" />
        <di:waypoint x="1830" y="550" />
        <di:waypoint x="1590" y="550" />
        <di:waypoint x="1590" y="475" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ctnvkr_di" bpmnElement="Flow_0ctnvkr">
        <di:waypoint x="1805" y="470" />
        <di:waypoint x="1750" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13i1do9_di" bpmnElement="Flow_13i1do9">
        <di:waypoint x="1650" y="470" />
        <di:waypoint x="1595" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eh4qu3_di" bpmnElement="Flow_1eh4qu3">
        <di:waypoint x="1191" y="205" />
        <di:waypoint x="1191" y="150" />
        <di:waypoint x="1400" y="150" />
        <di:waypoint x="1400" y="225" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1289" y="132" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uewv0z_di" bpmnElement="Flow_1uewv0z">
        <di:waypoint x="1216" y="230" />
        <di:waypoint x="1258" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1229" y="207" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dkxcsb_di" bpmnElement="Flow_1dkxcsb">
        <di:waypoint x="270" y="255" />
        <di:waypoint x="270" y="310" />
        <di:waypoint x="520" y="310" />
        <di:waypoint x="520" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="387" y="292" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k3rco2_di" bpmnElement="Flow_1k3rco2">
        <di:waypoint x="295" y="230" />
        <di:waypoint x="320" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="300" y="212" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="1040" y="470" />
        <di:waypoint x="908" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wuusbt_di" bpmnElement="Flow_1wuusbt">
        <di:waypoint x="1419" y="620" />
        <di:waypoint x="1380" y="620" />
        <di:waypoint x="1380" y="495" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p7943l_di" bpmnElement="Flow_1p7943l">
        <di:waypoint x="1570" y="495" />
        <di:waypoint x="1570" y="620" />
        <di:waypoint x="1519" y="620" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1577" y="557" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o83mga_di" bpmnElement="Flow_1o83mga">
        <di:waypoint x="1545" y="470" />
        <di:waypoint x="1405" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1482" y="452" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v8tmtg_di" bpmnElement="Flow_0v8tmtg">
        <di:waypoint x="1551" y="151" />
        <di:waypoint x="1570" y="151" />
        <di:waypoint x="1570" y="205" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1odmwry_di" bpmnElement="Flow_1odmwry">
        <di:waypoint x="1445" y="230" />
        <di:waypoint x="1545" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n6udmd_di" bpmnElement="Flow_1n6udmd">
        <di:waypoint x="1420" y="205" />
        <di:waypoint x="1420" y="151" />
        <di:waypoint x="1451" y="151" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1425" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07qlm7g_di" bpmnElement="Flow_07qlm7g">
        <di:waypoint x="1358" y="230" />
        <di:waypoint x="1395" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ap55e3_di" bpmnElement="Flow_0ap55e3">
        <di:waypoint x="1150" y="150" />
        <di:waypoint x="1170" y="150" />
        <di:waypoint x="1170" y="226" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zdzxft_di" bpmnElement="Flow_0zdzxft">
        <di:waypoint x="1021" y="205" />
        <di:waypoint x="1021" y="150" />
        <di:waypoint x="1050" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1025" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ajskls_di" bpmnElement="Flow_1ajskls">
        <di:waypoint x="1046" y="230" />
        <di:waypoint x="1166" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1126" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyv8yd_di" bpmnElement="Flow_1oyv8yd">
        <di:waypoint x="420" y="230" />
        <di:waypoint x="470" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vr9m96_di" bpmnElement="Flow_1vr9m96">
        <di:waypoint x="188" y="230" />
        <di:waypoint x="245" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="212" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xzxgxd_di" bpmnElement="Activity_0d15yei">
        <dc:Bounds x="690" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="872" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="879" y="428" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_119pnv5_di" bpmnElement="Activity_1wq7s30">
        <dc:Bounds x="890" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="320" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0eropt8_di" bpmnElement="Activity_0a0g75l">
        <dc:Bounds x="1050" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12lns6w_di" bpmnElement="Gateway_12lns6w" isMarkerVisible="true">
        <dc:Bounds x="996" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="988" y="262" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g3mnwv_di" bpmnElement="Activity_0nccbee">
        <dc:Bounds x="1258" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s5niqa_di" bpmnElement="Activity_119zmlg">
        <dc:Bounds x="1451" y="111" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="1780" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07zfya5_di" bpmnElement="Activity_0kptmf2">
        <dc:Bounds x="1419" y="580" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="1040" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g2qvk8_di" bpmnElement="Gateway_1g2qvk8" isMarkerVisible="true">
        <dc:Bounds x="1395" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1390" y="262" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fo5mo8_di" bpmnElement="Gateway_0fo5mo8" isMarkerVisible="true">
        <dc:Bounds x="1545" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1537" y="421" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b565p2_di" bpmnElement="Gateway_1b565p2" isMarkerVisible="true">
        <dc:Bounds x="1355" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1349" y="424" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0wx9b29_di" bpmnElement="Activity_0ur58zi">
        <dc:Bounds x="470" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1heqigx_di" bpmnElement="Gateway_1heqigx" isMarkerVisible="true">
        <dc:Bounds x="245" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="242" y="181" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yfd9e1_di" bpmnElement="Gateway_1yfd9e1" isMarkerVisible="true">
        <dc:Bounds x="1166" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1154" y="262" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bhkf7k_di" bpmnElement="Activity_1iziya6">
        <dc:Bounds x="1650" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07sc21e_di" bpmnElement="Gateway_07sc21e" isMarkerVisible="true">
        <dc:Bounds x="1805" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1865" y="463" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06hwd3w_di" bpmnElement="Activity_06hwd3w">
        <dc:Bounds x="1150" y="580" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04dmfyf_di" bpmnElement="Gateway_04dmfyf" isMarkerVisible="true">
        <dc:Bounds x="1545" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1578" y="203" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tixbxc_di" bpmnElement="Gateway_1tixbxc" isMarkerVisible="true">
        <dc:Bounds x="1275" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1252" y="428" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_166f06r_di" bpmnElement="Activity_0gmb5h1">
        <dc:Bounds x="1780" y="320" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1rjzhmw_di" bpmnElement="Gateway_1rjzhmw" isMarkerVisible="true">
        <dc:Bounds x="835" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="828" y="263" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tyl3vr_di" bpmnElement="Gateway_0tyl3vr" isMarkerVisible="true">
        <dc:Bounds x="595" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="577" y="181" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
