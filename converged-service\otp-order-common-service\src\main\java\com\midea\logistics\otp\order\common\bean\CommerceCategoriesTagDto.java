package com.midea.logistics.otp.order.common.bean;

import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.otp.enums.ProfessionalCompanyEnum;
import lombok.Data;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: ex_wuhc6
 * Description: 电商分类打标
 */

@Data
public class CommerceCategoriesTagDto {

    /**
     * 订单号，系统生成
     */
    private String orderNo;

    /**
     * 父订单号
     */
    private String parentOrderNo;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 仓库编码
     */
    private String whCode;

    /**
     * CdWarehouse 仓库类型
     */
    private CdWarehouse cdWarehouse;

    /**
     * channel（客户属性）  ebcuIndustryType（行业大类） ebcuCustomerGroupName （客户系）
     */
    private EbCustomer ebCustomer;

    /**
     * 来源系统,例如：菜鸟 ECM 奇门等
     */
    private String sourceSystem;

    /**
     * 平台属性
     */
    private EsCompany esCompany;

    /**
     * 云仓标识 Y表示云仓,N表示非云
     */
    private String cloudWhFlag;

    /**
     * 专业公司
     */
    private String professionalCompany;

    /**
     * 配送方式
     */
    private String deliveryType;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 平台编码
     */
    private String siteCode;

    /**
     * 电商分类
     */
    // private String commerceCategories;
}

