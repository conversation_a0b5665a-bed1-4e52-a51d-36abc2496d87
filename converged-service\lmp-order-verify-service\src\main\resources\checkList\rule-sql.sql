/* Alter table in target */
ALTER TABLE `customer_config` 
	ADD COLUMN `apart_type` VARCHAR(100)  COLLATE utf8mb4_bin NULL COMMENT '拆单类型(SINGLE,N,HANDLE,分别对应单品单件拆单、不拆单、手工拆单)' AFTER `hold_flag` , 
	ADD COLUMN `weight_flag` TINYINT(4)   NULL COMMENT '重量是否为空配置' AFTER `apart_type` , 
	ADD COLUMN `volumn_flag` TINYINT(4)   NULL COMMENT '体积是否为空配置' AFTER `weight_flag` , 
	ADD COLUMN `single_weight_flag` TINYINT(4)   NULL COMMENT '单品重量是否为空配置' AFTER `volumn_flag` , 
	ADD COLUMN `single_volumn_flag` TINYINT(4)   NULL COMMENT '单品体积是否为空配置' AFTER `single_weight_flag` , 
	ADD COLUMN `fee_flag` TINYINT(4)   NULL COMMENT '校验计费组是否为空' AFTER `single_volumn_flag` ;

/*Alter table in target */
ALTER TABLE `customer_contract_config` 
	ADD COLUMN `upper_wh_code` varchar(32)  NULL COMMENT '上游仓库编码' after `wh_name` ;


/* Create table in target */
CREATE TABLE `piece_analysis`(
	`id` BIGINT(20) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT '主键' , 
	`create_user_code` VARCHAR(48) NULL  DEFAULT 'system' COMMENT '创建人,接口订单默认system,手工单默认客户id' , 
	`create_time` DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' , 
	`update_user_code` VARCHAR(48) NULL  DEFAULT 'system' COMMENT '修改人,接口订单默认system,手工单默认客户id' , 
	`update_time` DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' , 
	`delete_flag` SMALLINT(2) NOT NULL  DEFAULT 0 COMMENT '状态,0可用,1不可用' , 
	`version` INT(11) NOT NULL  DEFAULT 0 COMMENT '数据行版本号' , 
	`remark` VARCHAR(2048) NULL  COMMENT '备注' , 
	`tenant_code` VARCHAR(36) NULL  DEFAULT 'lcloud' COMMENT '租户编码' , 
	`customer_code` VARCHAR(32) NULL  DEFAULT '1' COMMENT '客户编码' , 
	`source_system` VARCHAR(16) NOT NULL  DEFAULT '1' COMMENT '来源系统' , 
	`upper_wh_code` VARCHAR(32) NULL  COMMENT '上游仓库编码' , 
	`calculation_type` VARCHAR(32) NULL  COMMENT '计算类型' , 
	`piece_type` VARCHAR(32) NULL  COMMENT '件型' , 
	`weight_to` DECIMAL(18,8) NULL  COMMENT '重量区间开始' , 
	`weight_from` DECIMAL(18,8) NULL  COMMENT '重量区间结束' , 
	`volume_to` DECIMAL(18,8) NULL  COMMENT '体积区间开始' , 
	`volume_from` DECIMAL(18,8) NULL  COMMENT '体积区间结束' , 
	PRIMARY KEY (`id`) 
) ENGINE=INNODB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_unicode_ci' COMMENT='件型解析';

/*Alter table in target */
ALTER TABLE `project_classify_config` 
	ADD COLUMN `upper_wh_code` varchar(32)  NULL COMMENT '上游仓库编码' after `project_classify` ;

/* Alter table in target */
ALTER TABLE `shipping_type_rule` 
	ADD COLUMN `order_type` VARCHAR(10)  NULL COMMENT '订单类型' AFTER `delivery_type` , 
	ADD COLUMN `site_code` varchar(32)  NULL COMMENT '平台编码' after `order_type` , 
	ADD COLUMN `site_name` varchar(128)  NULL COMMENT '平台名称' after `site_code` , 
	ADD COLUMN `net_delivery_type` VARCHAR(32)  NULL COMMENT '网点配送类型' AFTER `site_name` ;