package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.PieceAnalysisService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.rule.PieceAnalysisFeign;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.service.pieceAnalysis.PieceAnalysisRun;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.PieceAnalysis;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PieceAnalysisServiceImpl implements PieceAnalysisService {

    @Autowired
    private PieceAnalysisFeign pieceAnalysisFeign;

    @Autowired
    private PieceAnalysisRun pieceAnalysisRun;

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private OrderverifyHelper orderverifyHelper;

    /**
    * !@件型解析 - 中台
    * @param: [target]
    * @return: com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt
    * @author: 陈永培
    * @createtime: 2023/5/22 11:42
    */
    @Override
    @ZeebeFlow("PIECE_ANALYSIS")
    public CustomerOrderInfoExt pieceAnalysisExec(CustomerOrderInfoExt target) {
        PieceAnalysis pieceAnalysis = new PieceAnalysis();
        pieceAnalysis.setSourceSystem(target.getSourceSystem());
        pieceAnalysis.setTenantCode(target.getTenantCode());
        pieceAnalysis.setPageSize(10000);
        if(CollectionUtils.isEmpty(target.getCustomerOrderItems())){
            throw BusinessException.fail("客户订单详细数据为空");
        }
        JsonResponse<PageResponse<PieceAnalysis>> pieceAnalysisList = pieceAnalysisFeign.searchPieceAnalysis(pieceAnalysis);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(pieceAnalysisList.getCode())) {
            throw BusinessException.fail("获取件型解析配置失败," + JSONObject.toJSONString(pieceAnalysisList));
        }
        if (CollectionUtils.isEmpty(pieceAnalysisList.data.list)) {
            FlowListenerParam.success("无需解析");
            return target;
        }
        Map<Long, CustomerOrderItem> mapList = target.getCustomerOrderItems().stream().collect(Collectors.toMap(CustomerOrderItem::getId, e -> e));
        List<CustomerOrderItem> targetList = new ArrayList<>();
        if (CollectionUtils.isEmpty(targetList)||mapList.size()>0) {
            pieceAnalysisRun.execute(target, pieceAnalysisList.data.list, mapList,targetList);
        }
        if (!CollectionUtils.isEmpty(targetList)) {
            StringBuilder builder = new StringBuilder();
            Map<Long, CustomerOrderItem> mapCustomerOrderItem = targetList.stream().collect(Collectors.toMap(CustomerOrderItem::getId, e -> e));
            int count = 10;
            for (CustomerOrderItem e : target.getCustomerOrderItems()) {
                CustomerOrderItem customerOrderItem = mapCustomerOrderItem.get(e.getId());
                if (customerOrderItem != null) {
                    e.setItemSize(customerOrderItem.getItemSize());
                    builder.append(",").append(customerOrderItem.getItemCode()).append(":").append(customerOrderItem.getItemSize());
                    count--;
                    if (count == 0){
                        break;
                    }
                }

            }
            lmpOrderFlowHelper.updateCustomerOrderItem(targetList);
            String s = builder.toString();
            if (s.length()<1){
                FlowListenerParam.success("");
            }else{
                FlowListenerParam.success(s.substring(1));
            }
        }else{
            FlowListenerParam.success("未匹配到件型规则，无需解析");
        }

        return target;
    }


    /**
     * 计算件型
     * @param customerOrderInfoExt
     * @return
     */
    @Override
    public CustomerOrderInfoExt itemSizeAnalysis(CustomerOrderInfoExt customerOrderInfoExt) {

        CustomerOrderInfo customerOrderInfo = customerOrderInfoExt;
        OrderType orderType = OrderType.valueOf(customerOrderInfo.getOrderType());
        String sourceSys = customerOrderInfo.getSourceSystem();
        String businessMode = customerOrderInfo.getBusinessMode();
        //计算件型 销售出库（大宝仓）、销售出库（拼多多）、上门取件单、万融
        // 万融
        boolean wanRong = StringUtils.isNotBlank(customerOrderInfo.getCustomerCode())
            && "A0034067".equals(customerOrderInfo.getCustomerCode());
        String whCode = customerOrderInfo.getWhCode();
        boolean cnItemSize = false;
        boolean pddItemSize = false;
        if (whCode != null){
            List<String> dabao = Arrays.asList(CommonConstant.getDabaoArray());
            boolean dabaoFlag = dabao.contains(whCode);
            boolean caiNiaoFlag = customerOrderInfo.getSourceSystem().equals(SourceSystem.CAINIAO.getKey()) && dabaoFlag;
            boolean sourceSystemFlag = wanRong || caiNiaoFlag || customerOrderInfo.getSourceSystem().equals(SourceSystem.PDD.getKey());
            Boolean aoOrderTypeFlag = OrderType.AO == orderType && ("TRUNK_13476700".equals(customerOrderInfo.getCarrierCode()));
            boolean dpOrderTypeFlag = OrderType.DP == orderType || OrderType.DPRI == orderType;

            if(StringUtils.isNotBlank(customerOrderInfo.getCustomerCode())) {
                cnItemSize = redisHelper.getCnItemSize().contains(customerOrderInfo.getCustomerCode());
                pddItemSize = redisHelper.getPddItemSize().contains(customerOrderInfo.getCustomerCode());
                if(StringUtils.isNotBlank(customerOrderInfo.getSourceSystem())){
                    if(!cnItemSize){
                        cnItemSize = redisHelper.getCnItemSize().contains(customerOrderInfo.getSourceSystem() + customerOrderInfo.getCustomerCode());
                    }
                    if(!pddItemSize){
                        pddItemSize = redisHelper.getPddItemSize().contains(customerOrderInfo.getSourceSystem() + customerOrderInfo.getCustomerCode());
                    }
                }
            }

            if(BusinessMode.isB2C(businessMode) && (sourceSystemFlag || aoOrderTypeFlag || dpOrderTypeFlag || cnItemSize || pddItemSize)) {
                List<CustomerOrderItem> items = customerOrderInfoExt.getCustomerOrderItems();
                orderverifyHelper.itemSizeAnalysis(items, sourceSys, customerOrderInfo);
                customerOrderInfoExt.setCustomerOrderItems(items);
            }
        }

        return customerOrderInfoExt;
    }
}
