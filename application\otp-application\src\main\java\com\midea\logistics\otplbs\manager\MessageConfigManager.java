package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.rule.MessageConfigFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.bean.MessageConfigExcelRequest;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.rule.domain.bean.MessageConfig;
import com.midea.logistics.otplbs.fegin.MasggerFegin;
import com.midea.msgcenter.domain.bean.Message;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.auth.bean.UserInfo;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class MessageConfigManager {
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private MessageConfigFeign messageConfigFeign;
    @Autowired
    EbCustomerManager ebCustomerManager;
    @Autowired
    private EsCompanyManager esCompanyManager;
    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    MasggerFegin masggerFegin;

    private final String REPORT_OTP_SEND_MESSAGE_CONFIG = "REPORT_OTP_SEND_MESSAGE_CONFIG";


    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) {
        log.info("=============发货短信配置导入开始=============");
        JsonResponse response = new JsonResponse();
        response.setMsg("发货短信配置导入成功");
        try {
            Map<Integer,String> errorMap = Maps.newTreeMap();
            ImportExcel importExcel = new ImportExcel(file, 1, 0, applicationContext);
            int lastCellNum = importExcel.getLastCellNum();
            Class clazz = MessageConfigExcelRequest.class;
            if (lastCellNum != 7) {
                throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
            }
            List<MessageConfigExcelRequest> dataList = importExcel.getDataList(clazz, null);
            if (CollectionUtils.isEmpty(dataList)) {
                return response;
            }
            this.checkExcelDistantDataAndSave(dataList,errorMap);
            if (errorMap.size() != 0) {
                throw BusinessException.fail(JSON.toJSONString(errorMap.values()));
            }
        } catch (Exception e) {
            response.setCode(BaseCodeEnum.FAILED.getCode());
            response.setMsg(e.getMessage());
        }
        sendMessager(response);
        return response;
    }

    private void sendMessager(JsonResponse response) {
        Message message = new Message();
        message.setTitle("发货短信配置导入"+ (BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())? "成功" : "失败" ));
        message.setApplicationCode(dictHelper.getDictVaule(REPORT_OTP_SEND_MESSAGE_CONFIG,"applicationCode"));
        message.setCreateTime(new Date());
        message.setMessageType(1);
        UserInfo userInfo = (UserInfo) iSsoService.getUserInfo();
        if (userInfo != null) {
            message.addReceiver(userInfo.getUserCode());
        }
        message.setContent(response.getMsg());
        masggerFegin.sendMessage(message);
    }

    private void checkExcelDistantDataAndSave(List<MessageConfigExcelRequest> dataList, Map<Integer, String> errorMap) {
        dataList.stream().forEach(data -> {
            MessageConfig config = new MessageConfig();
            try {
                this.checkParams(data);
                this.getConfigData(data, config);
                JsonResponse jsonResponse = messageConfigFeign.addOrUpdateMessageConfig(config);
                if (null == jsonResponse || !BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
                    errorMap.put(data.getRow(),"第" + data.getRow() + "行数据异常." + jsonResponse.getMsg());
                }
            } catch (Exception e) {
                errorMap.put(data.getRow(), "第" + data.getRow() + "行数据异常." + e.getMessage());
            }
        });
    }

    private void getConfigData(MessageConfigExcelRequest data, MessageConfig config) {
        config.setBusinessMode(data.getBusinessMode());
        config.setCustomerCode(data.getCustomerCode());
        config.setCustomerName(data.getCustomerName());
        config.setCreateUserCode(iSsoService.getUserCode());
        config.setUpdateUserCode(iSsoService.getUserCode());
        config.setOrderType(OrderType.getType(data.getOrderTypeName()));
        if (StringUtils.isBlank(config.getOrderType())) {
            throw BusinessException.fail("解析不出正确的订单类型");
        }
        if (StringUtils.isBlank(config.getCustomerName())) {
            EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(data.getCustomerCode());
            if (null != ebCustomerCache) {
                config.setCustomerName(ebCustomerCache.getEbcuNameCn());
            }
        }
        config.setSiteCode(data.getSiteCode());
        config.setSiteName(data.getSiteName());
        if (StringUtils.isBlank(config.getSiteName())) {
            EsCompany esCompanySite = esCompanyManager.getEsCompanyCache(data.getSiteCode());
            if (null != esCompanySite) {
                config.setSiteName(esCompanySite.getEscoCompanyNameCn());
            }
        }
        config.setEnableFlag(1);
        if (CommonConstant.NOT_ENABLED2.equals(data.getEnableFlagName())) {
            config.setEnableFlag(0);
        }
    }


    private void checkParams(MessageConfigExcelRequest data) {
        if (StringUtils.isBlank(data.getSiteCode()) || StringUtils.isBlank(data.getBusinessMode())
            || StringUtils.isBlank(data.getOrderTypeName())) {
            throw BusinessException.fail("平台、业务模式、订单类型必填");
        }
    }

}
