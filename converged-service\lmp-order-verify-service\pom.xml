<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.midea.logistics.otp</groupId>
        <artifactId>converged-service</artifactId>
        <version>1.0.1</version>
    </parent>

    <groupId>com.midea.logistics.otp</groupId>
    <artifactId>lmp-order-verify-service</artifactId>
    <version>1.0.1</version>

    <properties>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.mideaframework</groupId>
            <artifactId>midea-mq-api</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>druid-spring-boot-starter</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.midea.logistics.zeebe</groupId>
            <artifactId>lc-zeebe-spring-boot-starter</artifactId>
            <version>2.0.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mideaframework</groupId>
                    <artifactId>midea-framework-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mideaframework</groupId>
                    <artifactId>midea-auth-sdk-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mideaframework</groupId>
                    <artifactId>lc-mybatis-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.midea.logistics.lcp</groupId>
            <artifactId>lcp-lots-spring-boot-starter</artifactId>
            <version>2.1.5.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mideaframework</groupId>
                    <artifactId>midea-framework-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.3.0</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>7.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>parent-join-client</artifactId>
            <version>7.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>aggs-matrix-stats-client</artifactId>
            <version>7.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>rank-eval-client</artifactId>
            <version>7.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>lang-mustache-client</artifactId>
            <version>7.3.0</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>7.3.0</version>
        </dependency>


        <dependency>
            <groupId>com.midea.logistics.otp</groupId>
            <artifactId>otp-order-common-service</artifactId>
            <version>1.0.1</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>
