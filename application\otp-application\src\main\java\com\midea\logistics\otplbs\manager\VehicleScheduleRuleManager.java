package com.midea.logistics.otplbs.manager;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.midea.logistics.cache.manager.EbPlaceManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.domain.mdm.domain.EbPlace;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.dispatch.BopFeign;
import com.midea.logistics.otp.common.helper.ImportExcelHelper;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.common.helper.bean.VehicleScheduleRuleExcelRequest;
import com.midea.logistics.otp.dispatch.domain.bean.DispatchData;
import com.midea.logistics.otp.rule.domain.bean.VehicleScheduleRule;
import com.midea.logistics.otp.rule.domain.response.VehicleScheduleCompanyAuthResponse;
import com.midea.logistics.otplbs.fegin.VehicleScheduleRuleFeign;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.sdk.service.MideaAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: VehicleScheduleRuleManager
 * <AUTHOR>
 * Date 2020-11-19 11:21:09
 * Description:班次配置excel导入导出
 */
@Slf4j
@Component
public class VehicleScheduleRuleManager {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    Validatorhelper validatorhelper;

    @Autowired
    VehicleScheduleRuleFeign vehicleScheduleRuleFeign;

    @Autowired
    private EbPlaceManager ebPlaceManager;

    @Autowired
    private EsCompanyManager esCompanyManager;

    @Autowired
    private ISsoService iSsoService;

    @Autowired
    private BopFeign bopFeign;

    @Autowired
    private MideaAuthService mideaAuthService;

    private static final Logger logger = LoggerFactory.getLogger( VehicleScheduleRuleManager.class );

    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) throws Exception {
        logger.info("班次配置导入开始============================");
        //可扫描excel中间带空行的导入工具类
        ImportExcelHelper ei = new ImportExcelHelper(file, 1, 0, applicationContext);
        int lastCellNum = ei.getLastCellNum();
        if (lastCellNum != 28) {
            throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
        }
        List<VehicleScheduleRuleExcelRequest> vehicleScheduleRules = ei.getDataList(VehicleScheduleRuleExcelRequest.class, null);
        if (CollectionUtils.isEmpty(vehicleScheduleRules)) {
            throw BusinessException.fail("导入的模板不能为空");
        }

        String userCode = iSsoService.getUserCode();
        logger.info("VehicleScheduleRuleExcelImport_userCode:{}", userCode);

        List<String> companyCodeList = mideaAuthService.getUserDataAuths(userCode, CommonConstant.COMPANY_DATA);
        if (CollectionUtils.isEmpty(companyCodeList)) {
            logger.error("用户没有任何分公司的权限");
            throw BusinessException.fail("用户没有任何分公司的权限");
        }

        //List<String> errorStr = Lists.newArrayList();
        List<VehicleScheduleRuleExcelRequest> requestList = Lists.newArrayList();
        Map<Integer, String> errorMap = Maps.newTreeMap();
        //校验值是否合规
        vehicleScheduleRules.forEach(data -> {
            //是否有新增或修改分公司的权限
            if (!StringUtils.isEmpty(data.getCompanyCode())) {
                //没有分公司权限
                if (!companyCodeList.contains("ALL") && !companyCodeList.contains(data.getCompanyCode())) {
                    errorMap.put(data.getRow(),MessageFormat.format("在第{0}行，没有导入分公司的权限。", data.getRow()));
                    return;
                }
            }
            Integer row = data.getRow();
            check(row, errorMap, data, requestList);
        });

        JsonResponse importResponse = new JsonResponse();
        if (!CollectionUtils.isEmpty(requestList)) {
            importResponse = vehicleScheduleRuleFeign.importBatchInsert(requestList);
            if (Objects.equals(importResponse.getCode(), BaseCodeEnum.FAILED.getCode())) {
                Map<Integer, String> repeatMap = JSON.parseObject(importResponse.getMsg(), new TypeReference<Map<Integer, String>>() {
                });
                errorMap.putAll(repeatMap);
                throw BusinessException.fail(JSON.toJSONString(errorMap.values()));
            }
        }

        if (errorMap.size() != 0) {
            errorMap.put(100000, MessageFormat.format("已成功导入{0}条数据，请修改有错误的数据。", requestList.size()));
            throw BusinessException.fail(JSON.toJSONString(errorMap.values()));
        }

        logger.info("班次配置导入结束============================");
        return JsonResponse.success(Optional.ofNullable(importResponse.getData()).orElse(null));
    }

    private void check(Integer row, Map<Integer, String> errorMap, VehicleScheduleRuleExcelRequest data, List<VehicleScheduleRuleExcelRequest> requestList) {

        StringBuilder stringBuilder = new StringBuilder();
        try {
            //班次配置合法性校验
            String senderProvinceCode = data.getSenderProvinceCode();
            EbPlace senderProvinceEbPlaceCache = ebPlaceManager.getEbPlaceCache(senderProvinceCode);
            if (StringUtils.isBlank(senderProvinceCode)) {
                stringBuilder.append("发货省编码[值为空] ,请检查。");
            } else if (null == senderProvinceEbPlaceCache){
                stringBuilder.append("发货省编码[不存在]。");
            }

            String senderProvinceName = data.getSenderProvinceName();
            if (StringUtils.isBlank(senderProvinceName)) {
                stringBuilder.append("发货省名称[值为空] ,请检查。");
            } else if (Objects.nonNull(senderProvinceEbPlaceCache) && !Objects.equals(senderProvinceEbPlaceCache.getEbplNameCn(),senderProvinceName)){
                stringBuilder.append("发货省名称与发货省编码[不匹配]。");
            }


            String senderCityCode = data.getSenderCityCode();
            EbPlace senderCityEbPlaceCache = ebPlaceManager.getEbPlaceCache(senderCityCode);
            if (StringUtils.isBlank(senderCityCode)) {
                stringBuilder.append("发货市编码[值为空] ,请检查。");
            } else if (null == senderCityEbPlaceCache){
                stringBuilder.append("发货市编码[不存在]。");
            }

            String senderCityName = data.getSenderCityName();
            if (StringUtils.isBlank(senderCityName)) {
                stringBuilder.append("发货市名称[值为空] ,请检查。");
            } else if (Objects.nonNull(senderCityEbPlaceCache) && !Objects.equals(senderCityEbPlaceCache.getEbplNameCn(),senderCityName)){
                stringBuilder.append("发货市名称与发货市编码[不匹配]。");
            }

            String senderDistrictCode = data.getSenderDistrictCode();
            EbPlace senderDistrictEbPlaceCache = ebPlaceManager.getEbPlaceCache(senderDistrictCode);
            if (StringUtils.isBlank(senderDistrictCode)) {
                stringBuilder.append("发货区县编码[值为空] ,请检查。");
            } else if (null == senderDistrictEbPlaceCache){
                stringBuilder.append("发货区县编码[不存在]。");
            }

            String senderDistrictName = data.getSenderDistrictName();
            if (StringUtils.isBlank(senderDistrictName)) {
                stringBuilder.append("发货区县名称[值为空] ,请检查。");
            } else if (Objects.nonNull(senderDistrictEbPlaceCache) && !Objects.equals(senderDistrictEbPlaceCache.getEbplNameCn(),senderDistrictName)){
                stringBuilder.append("发货区县编码与发货区县名称[不匹配]。");
            }

            String receiverProvinceCode = data.getReceiverProvinceCode();
            EbPlace receiverProvinceEbPlaceCache = ebPlaceManager.getEbPlaceCache(receiverProvinceCode);
            if (StringUtils.isBlank(receiverProvinceCode)) {
                stringBuilder.append("收货省编码[值为空] ,请检查。");
            } else if (null == receiverProvinceEbPlaceCache){
                stringBuilder.append("收货省编码[不存在]。");
            }

            String receiverProvinceName = data.getReceiverProvinceName();
            if (StringUtils.isBlank(receiverProvinceName)) {
                stringBuilder.append("收货省名称[值为空] ,请检查。");
            } else if (Objects.nonNull(receiverProvinceEbPlaceCache) && !Objects.equals(receiverProvinceEbPlaceCache.getEbplNameCn(),receiverProvinceName)){
                stringBuilder.append("收货省名称与收货省编码[不匹配]。");
            }

            String receiverCityCode = data.getReceiverCityCode();
            EbPlace receiverCityEbPlaceCache = ebPlaceManager.getEbPlaceCache(receiverCityCode);
            if (StringUtils.isBlank(receiverCityCode)) {
                stringBuilder.append("收货市编码[值为空] ,请检查。");
            } else if (null == receiverCityEbPlaceCache){
                stringBuilder.append("收货市编码[不存在]。");
            }

            String receiverCityName = data.getReceiverCityName();
            if (StringUtils.isBlank(receiverCityName)) {
                stringBuilder.append("收货市名称[值为空] ,请检查。");
            } else if (Objects.nonNull(receiverCityEbPlaceCache) && !Objects.equals(receiverCityEbPlaceCache.getEbplNameCn(),receiverCityName)){
                stringBuilder.append("收货市名称与收货市编码[不匹配]。");
            }

            String receiverDistrictCode = data.getReceiverDistrictCode();
            EbPlace receiverDistrictEbPlaceCache = ebPlaceManager.getEbPlaceCache(receiverDistrictCode);
            if (StringUtils.isBlank(receiverDistrictCode)) {
                stringBuilder.append("收货区县编码[值为空] ,请检查。");
            } else if (null == receiverDistrictEbPlaceCache){
                stringBuilder.append("收货区县编码[不存在]。");
            }

            String receiverDistrictName = data.getReceiverDistrictName();
            if (StringUtils.isBlank(receiverDistrictName)) {
                stringBuilder.append("收货区县名称[值为空] ,请检查。");
            } else if (Objects.nonNull(receiverDistrictEbPlaceCache) && !Objects.equals(receiverDistrictEbPlaceCache.getEbplNameCn(),receiverDistrictName)){
                stringBuilder.append("收货区县名称与收货区县编码[不匹配]。");
            }

            String companyCode = data.getCompanyCode();
            EsCompany esCompanyCache = esCompanyManager.getEsCompanyCache(companyCode);
            if (StringUtils.isBlank(companyCode)) {
                stringBuilder.append("分公司编码[值为空] ,请检查。");
            } else if (null == esCompanyCache){
                stringBuilder.append("分公司编码[不存在]。");
            }

            String companyName = data.getCompanyName();
            if (StringUtils.isBlank(companyName)) {
                stringBuilder.append("分公司名称[值为空] ,请检查。");
            } else if (Objects.nonNull(esCompanyCache) && !Objects.equals(esCompanyCache.getEscoCompanyNameCn(),companyName)){
                stringBuilder.append("分公司名称与分公司编码不匹配。");
            }

            String vehicleScheduleName = data.getVehicleScheduleName();
            if (StringUtils.isBlank(vehicleScheduleName)) {
                stringBuilder.append("班次名称[值为空] ,请检查。");
            } else if (vehicleScheduleName.length() > 64) {
                stringBuilder.append("班次名称[长度大于64] ,请检查。");
            }

            String requireArrivalDate = data.getRequireArrivalDate();
            if (ToolUtils.isEmpty(requireArrivalDate)) {
                stringBuilder.append("应到车日期[值为空] ,请检查。");
            } else if (!Objects.equals(requireArrivalDate, "T0") && !Objects.equals(requireArrivalDate, "T1")) {
                stringBuilder.append("应到车日期[数据异常,值只能为:'T0'或者'T1'] ,请检查。");
            }

            String requireDepartDate = data.getRequireDepartDate();
            if (ToolUtils.isEmpty(requireDepartDate)) {
                stringBuilder.append("应发车日期[值为空] ,请检查。");
            } else if (!Objects.equals(requireDepartDate, "T0") && !Objects.equals(requireDepartDate, "T1")) {
                stringBuilder.append("应发车日期[数据异常,值只能为:'T0'或者'T1'] ,请检查。");
            }

            String requireArriveDate = data.getRequireArriveDate();
            if (ToolUtils.isEmpty(requireArriveDate)) {
                stringBuilder.append("应抵达日期[值为空] ,请检查。");
            } else if (!Objects.equals(requireArriveDate, "T0") && !Objects.equals(requireArriveDate, "T1")) {
                stringBuilder.append("应抵达日期[数据异常,值只能为:'T0'或者'T1'] ,请检查。");
            }

            String requireArrivalTime = data.getRequireArrivalTime();
            if (StringUtils.isBlank(requireArrivalTime)) {
                stringBuilder.append("应到车时间[值为空] ,请检查。");
            } else {
                if (isNotTime(requireArrivalTime)) {
                    stringBuilder.append("应到车时间[值不符合规范] ,请检查。");
                }
            }

            String requireDepartTime = data.getRequireDepartTime();
            if (StringUtils.isBlank(requireDepartTime)) {
                stringBuilder.append("应发车时间[值为空] ,请检查。");
            } else {
                if (isNotTime(requireDepartTime)) {
                    stringBuilder.append("应发车时间[值不符合规范] ,请检查。");
                }
            }

            String requireArriveTime = data.getRequireArriveTime();
            if (StringUtils.isBlank(requireArriveTime)) {
                stringBuilder.append("应抵达时间[值为空] ,请检查。");
            } else {
                if (isNotTime(requireArriveTime)) {
                    stringBuilder.append("应抵达时间[值不符合规范] ,请检查。");
                }
            }

            String effectiveTime = data.getEffectiveTime();
            Boolean effectiveIsDate = isDate(effectiveTime);
            if (StringUtils.isBlank(effectiveTime)) {
                stringBuilder.append("生效时间[值为空] ,请检查。");
            } else {
                if (!effectiveIsDate) {
                    stringBuilder.append("生效时间[值不符合规范] ,请检查。");
                }
            }

            String failureTime = data.getFailureTime();
            Boolean failureIsDate = isDate(failureTime);
            if (StringUtils.isBlank(failureTime)) {
                stringBuilder.append("失效时间[值为空] ,请检查。");
            } else {
                if (!failureIsDate) {
                    stringBuilder.append("失效时间[值不符合规范] ,请检查。");
                }
            }

            //生效,失效时间格式
            if (effectiveIsDate && failureIsDate) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                effectiveTime = StringUtils.join(effectiveTime, " 00:00:00");
                failureTime = StringUtils.join(failureTime, " 23:59:59");
                Date effective = null;
                Date failure = null;
                try {
                    effective = sdf.parse(effectiveTime);
                    failure = sdf.parse(failureTime);
                    if (effective.after(failure)) {
                        stringBuilder.append("失效时间应大于生效时间");
                    } else {
                        data.setEffectiveTime(effectiveTime);
                        data.setFailureTime(failureTime);
                    }
                } catch (ParseException e) {
                    stringBuilder.append("日期格式转换异常");
                }
            }

            if (!StringUtils.isEmpty(stringBuilder.toString())) {
                stringBuilder.insert(0, "在第" + row + "行，");
                throw BusinessException.fail(stringBuilder.toString());
            }
            requestList.add(data);
        } catch (BusinessException ex){
            errorMap.put(data.getRow(),ex.getMessage());
            log.error(ex.getMessage(), ex);
        }catch (Exception ep){
            throw BusinessException.fail(ep.getMessage());
        }
    }

    public static boolean isNotTime(String time){
        Pattern p = Pattern.compile("((((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|())))?$");
        try {
            return !p.matcher(time).matches();
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isDate(String date) {
        Pattern p = Pattern.compile("^(?:(?!0000)[0-9]{4}([-]?)(?:(?:0?[1-9]|1[0-2])([-]?)(?:0?[1-9]|1[0-9]|2[0-8])|(?:0?[13-9]|1[0-2])([-]?)(?:29|30)|(?:0?[13578]|1[02])([-]?)31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)([-]?)0?2([-]?)29)$");
        try {
            return p.matcher(date).matches();
        } catch (Exception e) {
            return false;
        }
    }
}

