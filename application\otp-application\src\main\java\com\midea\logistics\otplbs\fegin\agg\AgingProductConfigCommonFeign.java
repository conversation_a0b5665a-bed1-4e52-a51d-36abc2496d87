package com.midea.logistics.otplbs.fegin.agg;

import com.midea.logistics.otp.bean.agg.AgingProductConfig;
import com.midea.logistics.otp.bean.agg.AgingProductNode;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "AgingProductConfigCommonFeign")
public interface AgingProductConfigCommonFeign {

    @GetMapping("/aging/product/config/list")
    JsonResponse<List<AgingProductConfig>> agingProductConfigList(@SpringQueryMap AgingProductConfig entity);

    @GetMapping("/aging/product/config/page")
    JsonResponse agingProductConfigPage(@SpringQueryMap AgingProductConfig entity);

    @GetMapping("/aging/product/config/count")
    JsonResponse<BaseDomain> agingProductConfigCount(@SpringQueryMap AgingProductConfig entity);

    @GetMapping("/aging/product/config/detail")
    JsonResponse<AgingProductConfig> agingProductConfigDetail(@RequestParam("id") Long id);

    @GetMapping("/aging/product/config/detail")
    JsonResponse<AgingProductConfig> agingProductConfigDetail(@SpringQueryMap AgingProductConfig entity);

    @PostMapping("/aging/product/config/add")
    JsonResponse<BaseDomain> agingProductConfigAdd(@RequestBody AgingProductConfig entity);

    @PutMapping("/aging/product/config/update")
    JsonResponse<Integer> agingProductConfigUpdate(@RequestBody AgingProductConfig entity);

    @DeleteMapping("/aging/product/config/remove")
    JsonResponse<Integer> agingProductConfigRemove(@RequestBody AgingProductConfig entity);


    @GetMapping("/agingProductConfig/pageList")
    JsonResponse pageList(@SpringQueryMap AgingProductConfig agingProductConfig);

    @PostMapping("/agingProductConfig/deleteBatchByCode")
    JsonResponse deleteBatchByCode(@RequestBody List<String> agingCodes);
    @PostMapping("/agingProductConfig/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<AgingProductNode> agingProductNodeList);
}

