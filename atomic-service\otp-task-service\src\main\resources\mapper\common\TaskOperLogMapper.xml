<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskOperLogMapper">

    <sql id="searchFieldsSql">
            `delete_flag` AS  deleteFlag,
            `update_user_code` AS  updateUserCode,
            `create_time` AS  createTime,
            `create_user_code` AS  createUserCode,
            `task_no` AS  taskNo,
            `remark` AS  remark,
            `update_time` AS  updateTime,
            `id` AS  id,
            `tenant_code` AS  tenantCode,
            `version` AS  version
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="createTime !=null">
            and `create_time` =#{createTime}
        </if>
        <if test="taskNo !=null and taskNo != ''">
            and `task_no` =#{taskNo}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="tenantCode !=null and tenantCode != ''">
            and `tenant_code` =#{tenantCode}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="taskNo !=null and taskNo != ''">
                `task_no` = #{taskNo},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="tenantCode !=null and tenantCode != ''">
                `tenant_code` = #{tenantCode},
            </if>
        </set>
    </sql>

    <sql id="idFieldsSql">
        from task_oper_log t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.TaskOperLog">
        select
        <include refid="searchFieldsSql"/>
        from task_oper_log t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.TaskOperLog">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from task_oper_log t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.TaskOperLog">
        select
        <include refid="searchFieldsSql"/>
        from task_oper_log t
        <include refid="whereFieldsSql"/>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        task_oper_log t
        <include refid="setFieldsSql"/>
        where
        `version` = #{version}
        and `id` = #{id}
    </update>

    <update id="deleteById">
        update
        task_oper_log t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.TaskOperLog" useGeneratedKeys="true"
            keyProperty="id">
        insert into task_oper_log
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="taskNo !=null and taskNo != ''">
                `task_no`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="tenantCode !=null and tenantCode != ''">
                `tenant_code`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="taskNo !=null and taskNo != ''">
                #{taskNo},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="tenantCode !=null and tenantCode != ''">
                #{tenantCode},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `update_user_code`,
            `create_user_code`,
            `task_no`,
            `remark`,
            `tenant_code`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.updateUserCode},
            #{item.createUserCode},
            #{item.taskNo},
            #{item.remark},
            #{item.tenantCode},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        task_oper_log
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.taskNo !=null and item.taskNo != ''">
                `task_no` = #{item.taskNo},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.tenantCode !=null and item.tenantCode != ''">
                `tenant_code` = #{item.tenantCode},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields">
        <trim prefix="(" suffix=")" prefixOverrides="and">
            <if test="item.deleteFlag !=null">
                and `delete_flag` =#{item.deleteFlag}
            </if>
            <if test="item.createTime !=null">
                and `create_time` =#{item.createTime}
            </if>
            <if test="item.taskNo !=null and item.taskNo != ''">
                and `task_no` =#{item.taskNo}
            </if>
            <if test="item.remark !=null">
                and `remark` =#{item.remark}
            </if>
            <if test="item.updateTime !=null">
                and `update_time` =#{item.updateTime}
            </if>
            <if test="item.tenantCode !=null and item.tenantCode != ''">
                and `tenant_code` =#{item.tenantCode}
            </if>
            <if test="item.version !=null">
                and `version` =#{item.version}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_oper_log
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>


    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_oper_log
            set `delete_flag`=1
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

</mapper>