package com.midea.logistics.otp.orderverify.start;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.constants.MessageQueueDefine;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.common.mq.producer.ExpressOrderFlowProducer;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.orderverify.service.IOrderInfoService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目名称：logistics-otp
 * 功能说明：快递流程帮助类
 * <AUTHOR>
 * @createtime 2021/10/4 15:18
 */
@Component
@Slf4j
public class ExpressSubFlowStart {

    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private ExpressOrderFlowProducer expressOrderFlowProducer;
    @Autowired
    private IOrderInfoService iOrderInfoService;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    /**
     * 启动流程
     * @param flowTemplateCode
     * @param flowTicket
     * @return
     */
    public Integer startFlow(String orderNo) {

        OrderInfoExt orderInfo = orderverifyHelper.getOrderInfoExt(orderNo);

        log.info(" mq消息 "+ MessageQueueDefine.EXPRESS_ORDER_FLOW +"   消费内容 message =  {}", JSON.toJSON(orderInfo));

        if (StringUtils.isEmpty(orderNo)) {
            throw BusinessException.fail("订单号不能为空");
        }

        if (ToolUtils.isEmpty(orderInfo)) {
            return null;
        }

        iOrderInfoService.expressSubFlowVerify(orderInfo);
        return 1;
    }


    public Integer startFlow(OrderInfo orderInfo) {

        if (ToolUtils.isEmpty(orderInfo)) {
            return null;
        }

        OrderInfoExt orderInfoExt = orderverifyHelper.getOrderInfoExt(orderInfo);

        log.info(" mq消息 "+ MessageQueueDefine.EXPRESS_ORDER_FLOW +"   消费内容 message =  {}", JSON.toJSON(orderInfoExt));

        if (StringUtils.isEmpty(orderInfo.getOrderNo())) {
            throw BusinessException.fail("订单号不能为空");
        }

        if (ToolUtils.isEmpty(orderInfoExt)) {
            return null;
        }

        iOrderInfoService.expressSubFlowVerify(orderInfoExt);
        return 1;
    }

}
