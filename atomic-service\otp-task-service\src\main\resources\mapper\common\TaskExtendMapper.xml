<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskExtendMapper">

    <sql id="searchFieldsSql">
            `conf_obj` AS  confObj,
            `delivery_amount` AS  deliveryAmount,
            `assemble_num` AS  assembleNum,
            `tenant_code` AS  tenantCode,
            `parent_order_no` AS  parentOrderNo,
            `cd_customer_code` AS  cdCustomerCode,
            `assemble_status` AS  assembleStatus,
            `relation_prepose_task_no` AS  relationPreposeTaskNo,
            `delete_flag` AS  deleteFlag,
            `order_distinction_flag` AS  orderDistinctionFlag,
            `carrier_name` AS  carrierName,
            `assemble_no` AS  assembleNo,
            `task_no` AS  taskNo,
            `driver_mobile` AS  driverMobile,
            `transport_product` AS  transportProduct,
            `vehicle_handling_seq` AS  vehicleHandlingSeq,
            `id` AS  id,
            `vehicle_car` AS  vehicleCar,
            `origin_task_no` AS  originTaskNo,
            `order_no` AS  orderNo,
            `create_user_code` AS  createUserCode,
            `update_time` AS  updateTime,
            `version` AS  version,
            `transportation_division` AS  transportationDivision,
            `update_user_code` AS  updateUserCode,
            `driver` AS  driver,
            `create_time` AS  createTime,
            `carrier_code` AS  carrierCode,
            `plan_order_no` AS  planOrderNo,
            `contract_code` AS  contractCode,
            `driver_identity` AS  driverIdentity,
            `car_model` AS  carModel
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="confObj !=null and confObj != ''">
                        and `conf_obj` =#{confObj}
                    </if>
                    <if test="deliveryAmount !=null">
                        and `delivery_amount` =#{deliveryAmount}
                    </if>
                    <if test="assembleNum !=null">
                        and `assemble_num` =#{assembleNum}
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        and `tenant_code` =#{tenantCode}
                    </if>
                    <if test="parentOrderNo !=null and parentOrderNo != ''">
                        and `parent_order_no` =#{parentOrderNo}
                    </if>
                    <if test="cdCustomerCode !=null and cdCustomerCode != ''">
                        and `cd_customer_code` =#{cdCustomerCode}
                    </if>
                    <if test="assembleStatus !=null">
                        and `assemble_status` =#{assembleStatus}
                    </if>
                    <if test="relationPreposeTaskNo !=null and relationPreposeTaskNo != ''">
                        and `relation_prepose_task_no` =#{relationPreposeTaskNo}
                    </if>
                    <if test="orderDistinctionFlag !=null and orderDistinctionFlag != ''">
                        and `order_distinction_flag` =#{orderDistinctionFlag}
                    </if>
                    <if test="carrierName !=null and carrierName != ''">
                        and `carrier_name` =#{carrierName}
                    </if>
                    <if test="assembleNo !=null and assembleNo != ''">
                        and `assemble_no` =#{assembleNo}
                    </if>
                    <if test="taskNo !=null and taskNo != ''">
                        and `task_no` =#{taskNo}
                    </if>
                    <if test="driverMobile !=null and driverMobile != ''">
                        and `driver_mobile` =#{driverMobile}
                    </if>
                    <if test="transportProduct !=null and transportProduct != ''">
                        and `transport_product` =#{transportProduct}
                    </if>
                    <if test="vehicleHandlingSeq !=null">
                        and `vehicle_handling_seq` =#{vehicleHandlingSeq}
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="vehicleCar !=null and vehicleCar != ''">
                        and `vehicle_car` =#{vehicleCar}
                    </if>
                    <if test="originTaskNo !=null and originTaskNo != ''">
                        and `origin_task_no` =#{originTaskNo}
                    </if>
                    <if test="orderNo !=null and orderNo != ''">
                        and `order_no` =#{orderNo}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="transportationDivision !=null">
                        and `transportation_division` =#{transportationDivision}
                    </if>
                    <if test="driver !=null and driver != ''">
                        and `driver` =#{driver}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="carrierCode !=null and carrierCode != ''">
                        and `carrier_code` =#{carrierCode}
                    </if>
                    <if test="planOrderNo !=null and planOrderNo != ''">
                        and `plan_order_no` =#{planOrderNo}
                    </if>
                    <if test="contractCode !=null and contractCode != ''">
                        and `contract_code` =#{contractCode}
                    </if>
                    <if test="driverIdentity !=null and driverIdentity != ''">
                        and `driver_identity` =#{driverIdentity}
                    </if>
                    <if test="carModel !=null and carModel != ''">
                        and `car_model` =#{carModel}
                    </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
                        <if test="confObj !=null and confObj != ''">
                            `conf_obj` = #{confObj},
                        </if>
                        <if test="orderNo !=null and orderNo != ''">
                            `order_no` = #{orderNo},
                        </if>
                        <if test="deliveryAmount != null">
                            `delivery_amount` = #{deliveryAmount},
                        </if>
                        <if test="assembleNum != null">
                            `assemble_num` = #{assembleNum},
                        </if>
                        <if test="tenantCode !=null and tenantCode != ''">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="parentOrderNo !=null and parentOrderNo != ''">
                            `parent_order_no` = #{parentOrderNo},
                        </if>
                        <if test="cdCustomerCode !=null and cdCustomerCode != ''">
                            `cd_customer_code` = #{cdCustomerCode},
                        </if>
                        <if test="transportationDivision != null">
                            `transportation_division` = #{transportationDivision},
                        </if>
                        <if test="assembleStatus != null">
                            `assemble_status` = #{assembleStatus},
                        </if>
                        <if test="relationPreposeTaskNo !=null and relationPreposeTaskNo != ''">
                            `relation_prepose_task_no` = #{relationPreposeTaskNo},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="driver !=null and driver != ''">
                            `driver` = #{driver},
                        </if>
                        <if test="orderDistinctionFlag !=null and orderDistinctionFlag != ''">
                            `order_distinction_flag` = #{orderDistinctionFlag},
                        </if>
                        <if test="carrierName !=null and carrierName != ''">
                            `carrier_name` = #{carrierName},
                        </if>
                        <if test="assembleNo !=null and assembleNo != ''">
                            `assemble_no` = #{assembleNo},
                        </if>
                        <if test="carrierCode !=null and carrierCode != ''">
                            `carrier_code` = #{carrierCode},
                        </if>
                        <if test="taskNo !=null and taskNo != ''">
                            `task_no` = #{taskNo},
                        </if>
                        <if test="planOrderNo !=null and planOrderNo != ''">
                            `plan_order_no` = #{planOrderNo},
                        </if>
                        <if test="driverMobile !=null and driverMobile != ''">
                            `driver_mobile` = #{driverMobile},
                        </if>
                        <if test="transportProduct !=null and transportProduct != ''">
                            `transport_product` = #{transportProduct},
                        </if>
                        <if test="vehicleHandlingSeq != null">
                            `vehicle_handling_seq` = #{vehicleHandlingSeq},
                        </if>
                        <if test="vehicleCar !=null and vehicleCar != ''">
                            `vehicle_car` = #{vehicleCar},
                        </if>
                        <if test="originTaskNo !=null and originTaskNo != ''">
                            `origin_task_no` = #{originTaskNo},
                        </if>
                        <if test="contractCode !=null and contractCode != ''">
                            `contract_code` = #{contractCode},
                        </if>
                        <if test="driverIdentity !=null and driverIdentity != ''">
                            `driver_identity` = #{driverIdentity},
                        </if>
                        <if test="carModel !=null and carModel != ''">
                            `car_model` = #{carModel},
                        </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="confObj !=null">
                            `conf_obj` = #{confObj},
                        </if>
                        <if test="orderNo !=null">
                            `order_no` = #{orderNo},
                        </if>
                        <if test="deliveryAmount != null">
                            `delivery_amount` = #{deliveryAmount},
                        </if>
                        <if test="assembleNum != null">
                            `assemble_num` = #{assembleNum},
                        </if>
                        <if test="tenantCode !=null">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="parentOrderNo !=null">
                            `parent_order_no` = #{parentOrderNo},
                        </if>
                        <if test="cdCustomerCode !=null">
                            `cd_customer_code` = #{cdCustomerCode},
                        </if>
                        <if test="transportationDivision != null">
                            `transportation_division` = #{transportationDivision},
                        </if>
                        <if test="assembleStatus != null">
                            `assemble_status` = #{assembleStatus},
                        </if>
                        <if test="relationPreposeTaskNo !=null">
                            `relation_prepose_task_no` = #{relationPreposeTaskNo},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="driver !=null">
                            `driver` = #{driver},
                        </if>
                        <if test="orderDistinctionFlag !=null">
                            `order_distinction_flag` = #{orderDistinctionFlag},
                        </if>
                        <if test="carrierName !=null">
                            `carrier_name` = #{carrierName},
                        </if>
                        <if test="assembleNo !=null">
                            `assemble_no` = #{assembleNo},
                        </if>
                        <if test="carrierCode !=null">
                            `carrier_code` = #{carrierCode},
                        </if>
                        <if test="taskNo !=null">
                            `task_no` = #{taskNo},
                        </if>
                        <if test="planOrderNo !=null">
                            `plan_order_no` = #{planOrderNo},
                        </if>
                        <if test="driverMobile !=null">
                            `driver_mobile` = #{driverMobile},
                        </if>
                        <if test="transportProduct !=null">
                            `transport_product` = #{transportProduct},
                        </if>
                        <if test="vehicleHandlingSeq != null">
                            `vehicle_handling_seq` = #{vehicleHandlingSeq},
                        </if>
                        <if test="vehicleCar !=null">
                            `vehicle_car` = #{vehicleCar},
                        </if>
                        <if test="originTaskNo !=null">
                            `origin_task_no` = #{originTaskNo},
                        </if>
                        <if test="contractCode !=null">
                            `contract_code` = #{contractCode},
                        </if>
                        <if test="driverIdentity !=null">
                            `driver_identity` = #{driverIdentity},
                        </if>
                        <if test="carModel !=null">
                            `car_model` = #{carModel},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from task_extend t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.TaskExtend">
        select
        <include refid="searchFieldsSql"/>
        from task_extend t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">${orderByType}</if>
        </if>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.TaskExtend">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from task_extend t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.TaskExtend">
        select
        <include refid="searchFieldsSql"/>
        from task_extend t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
${orderByType}            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        task_extend t
        <include refid="setFieldsSql"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        task_extend t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        task_extend t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.TaskExtend" useGeneratedKeys="true" keyProperty="id">
        insert into task_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="confObj !=null and confObj != ''">
                        `conf_obj`,
                    </if>

                    <if test="orderNo !=null and orderNo != ''">
                        `order_no`,
                    </if>

                    <if test="createUserCode != null">
                        `create_user_code`,
                    </if>

                    <if test="deliveryAmount != null">
                        `delivery_amount`,
                    </if>

                    <if test="assembleNum != null">
                        `assemble_num`,
                    </if>

                    <if test="tenantCode !=null and tenantCode != ''">
                        `tenant_code`,
                    </if>

                    <if test="parentOrderNo !=null and parentOrderNo != ''">
                        `parent_order_no`,
                    </if>

                    <if test="cdCustomerCode !=null and cdCustomerCode != ''">
                        `cd_customer_code`,
                    </if>

                    <if test="transportationDivision != null">
                        `transportation_division`,
                    </if>

                    <if test="assembleStatus != null">
                        `assemble_status`,
                    </if>

                    <if test="relationPreposeTaskNo !=null and relationPreposeTaskNo != ''">
                        `relation_prepose_task_no`,
                    </if>

                    <if test="updateUserCode != null">
                        `update_user_code`,
                    </if>

                    <if test="driver !=null and driver != ''">
                        `driver`,
                    </if>

                    <if test="orderDistinctionFlag !=null and orderDistinctionFlag != ''">
                        `order_distinction_flag`,
                    </if>

                    <if test="carrierName !=null and carrierName != ''">
                        `carrier_name`,
                    </if>

                    <if test="assembleNo !=null and assembleNo != ''">
                        `assemble_no`,
                    </if>

                    <if test="carrierCode !=null and carrierCode != ''">
                        `carrier_code`,
                    </if>

                    <if test="taskNo !=null and taskNo != ''">
                        `task_no`,
                    </if>

                    <if test="planOrderNo !=null and planOrderNo != ''">
                        `plan_order_no`,
                    </if>

                    <if test="driverMobile !=null and driverMobile != ''">
                        `driver_mobile`,
                    </if>

                    <if test="transportProduct !=null and transportProduct != ''">
                        `transport_product`,
                    </if>

                    <if test="vehicleHandlingSeq != null">
                        `vehicle_handling_seq`,
                    </if>

                    <if test="vehicleCar !=null and vehicleCar != ''">
                        `vehicle_car`,
                    </if>

                    <if test="originTaskNo !=null and originTaskNo != ''">
                        `origin_task_no`,
                    </if>

                    <if test="contractCode !=null and contractCode != ''">
                        `contract_code`,
                    </if>

                    <if test="driverIdentity !=null and driverIdentity != ''">
                        `driver_identity`,
                    </if>

                    <if test="carModel !=null and carModel != ''">
                        `car_model`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="confObj !=null and confObj != ''">
                        #{confObj},
                    </if>
                    <if test="orderNo !=null and orderNo != ''">
                        #{orderNo},
                    </if>
                    <if test="createUserCode != null">
                        #{createUserCode},
                    </if>
                    <if test="deliveryAmount != null">
                        #{deliveryAmount},
                    </if>
                    <if test="assembleNum != null">
                        #{assembleNum},
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        #{tenantCode},
                    </if>
                    <if test="parentOrderNo !=null and parentOrderNo != ''">
                        #{parentOrderNo},
                    </if>
                    <if test="cdCustomerCode !=null and cdCustomerCode != ''">
                        #{cdCustomerCode},
                    </if>
                    <if test="transportationDivision != null">
                        #{transportationDivision},
                    </if>
                    <if test="assembleStatus != null">
                        #{assembleStatus},
                    </if>
                    <if test="relationPreposeTaskNo !=null and relationPreposeTaskNo != ''">
                        #{relationPreposeTaskNo},
                    </if>
                    <if test="updateUserCode != null">
                        #{updateUserCode},
                    </if>
                    <if test="driver !=null and driver != ''">
                        #{driver},
                    </if>
                    <if test="orderDistinctionFlag !=null and orderDistinctionFlag != ''">
                        #{orderDistinctionFlag},
                    </if>
                    <if test="carrierName !=null and carrierName != ''">
                        #{carrierName},
                    </if>
                    <if test="assembleNo !=null and assembleNo != ''">
                        #{assembleNo},
                    </if>
                    <if test="carrierCode !=null and carrierCode != ''">
                        #{carrierCode},
                    </if>
                    <if test="taskNo !=null and taskNo != ''">
                        #{taskNo},
                    </if>
                    <if test="planOrderNo !=null and planOrderNo != ''">
                        #{planOrderNo},
                    </if>
                    <if test="driverMobile !=null and driverMobile != ''">
                        #{driverMobile},
                    </if>
                    <if test="transportProduct !=null and transportProduct != ''">
                        #{transportProduct},
                    </if>
                    <if test="vehicleHandlingSeq != null">
                        #{vehicleHandlingSeq},
                    </if>
                    <if test="vehicleCar !=null and vehicleCar != ''">
                        #{vehicleCar},
                    </if>
                    <if test="originTaskNo !=null and originTaskNo != ''">
                        #{originTaskNo},
                    </if>
                    <if test="contractCode !=null and contractCode != ''">
                        #{contractCode},
                    </if>
                    <if test="driverIdentity !=null and driverIdentity != ''">
                        #{driverIdentity},
                    </if>
                    <if test="carModel !=null and carModel != ''">
                        #{carModel},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                `conf_obj`,
                `order_no`,
                `create_user_code`,
                `delivery_amount`,
                `assemble_num`,
                `tenant_code`,
                `parent_order_no`,
                `cd_customer_code`,
                `transportation_division`,
                `assemble_status`,
                `relation_prepose_task_no`,
                `update_user_code`,
                `driver`,
                `order_distinction_flag`,
                `carrier_name`,
                `assemble_no`,
                `carrier_code`,
                `task_no`,
                `plan_order_no`,
                `driver_mobile`,
                `transport_product`,
                `vehicle_handling_seq`,
                `vehicle_car`,
                `origin_task_no`,
                `contract_code`,
                `driver_identity`,
                `car_model`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.confObj},
                #{item.orderNo},
                #{item.createUserCode},
                #{item.deliveryAmount},
                #{item.assembleNum},
                #{item.tenantCode},
                #{item.parentOrderNo},
                #{item.cdCustomerCode},
                #{item.transportationDivision},
                #{item.assembleStatus},
                #{item.relationPreposeTaskNo},
                #{item.updateUserCode},
                #{item.driver},
                #{item.orderDistinctionFlag},
                #{item.carrierName},
                #{item.assembleNo},
                #{item.carrierCode},
                #{item.taskNo},
                #{item.planOrderNo},
                #{item.driverMobile},
                #{item.transportProduct},
                #{item.vehicleHandlingSeq},
                #{item.vehicleCar},
                #{item.originTaskNo},
                #{item.contractCode},
                #{item.driverIdentity},
                #{item.carModel},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        task_extend
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.confObj !=null and item.confObj != ''">
                     `conf_obj`  = #{item.confObj},
                 </if>
                 <if test="item.orderNo !=null and item.orderNo != ''">
                     `order_no`  = #{item.orderNo},
                 </if>
                 <if test="item.deliveryAmount != null">
                     `delivery_amount`  = #{item.deliveryAmount},
                 </if>
                 <if test="item.assembleNum != null">
                     `assemble_num`  = #{item.assembleNum},
                 </if>
                 <if test="item.tenantCode !=null and item.tenantCode != ''">
                     `tenant_code`  = #{item.tenantCode},
                 </if>
                 <if test="item.parentOrderNo !=null and item.parentOrderNo != ''">
                     `parent_order_no`  = #{item.parentOrderNo},
                 </if>
                 <if test="item.cdCustomerCode !=null and item.cdCustomerCode != ''">
                     `cd_customer_code`  = #{item.cdCustomerCode},
                 </if>
                 <if test="item.transportationDivision != null">
                     `transportation_division`  = #{item.transportationDivision},
                 </if>
                 <if test="item.assembleStatus != null">
                     `assemble_status`  = #{item.assembleStatus},
                 </if>
                 <if test="item.relationPreposeTaskNo !=null and item.relationPreposeTaskNo != ''">
                     `relation_prepose_task_no`  = #{item.relationPreposeTaskNo},
                 </if>
                 <if test="item.updateUserCode != null">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.driver !=null and item.driver != ''">
                     `driver`  = #{item.driver},
                 </if>
                 <if test="item.orderDistinctionFlag !=null and item.orderDistinctionFlag != ''">
                     `order_distinction_flag`  = #{item.orderDistinctionFlag},
                 </if>
                 <if test="item.carrierName !=null and item.carrierName != ''">
                     `carrier_name`  = #{item.carrierName},
                 </if>
                 <if test="item.assembleNo !=null and item.assembleNo != ''">
                     `assemble_no`  = #{item.assembleNo},
                 </if>
                 <if test="item.carrierCode !=null and item.carrierCode != ''">
                     `carrier_code`  = #{item.carrierCode},
                 </if>
                 <if test="item.taskNo !=null and item.taskNo != ''">
                     `task_no`  = #{item.taskNo},
                 </if>
                 <if test="item.planOrderNo !=null and item.planOrderNo != ''">
                     `plan_order_no`  = #{item.planOrderNo},
                 </if>
                 <if test="item.driverMobile !=null and item.driverMobile != ''">
                     `driver_mobile`  = #{item.driverMobile},
                 </if>
                 <if test="item.transportProduct !=null and item.transportProduct != ''">
                     `transport_product`  = #{item.transportProduct},
                 </if>
                 <if test="item.vehicleHandlingSeq != null">
                     `vehicle_handling_seq`  = #{item.vehicleHandlingSeq},
                 </if>
                 <if test="item.vehicleCar !=null and item.vehicleCar != ''">
                     `vehicle_car`  = #{item.vehicleCar},
                 </if>
                 <if test="item.originTaskNo !=null and item.originTaskNo != ''">
                     `origin_task_no`  = #{item.originTaskNo},
                 </if>
                 <if test="item.contractCode !=null and item.contractCode != ''">
                     `contract_code`  = #{item.contractCode},
                 </if>
                 <if test="item.driverIdentity !=null and item.driverIdentity != ''">
                     `driver_identity`  = #{item.driverIdentity},
                 </if>
                 <if test="item.carModel !=null and item.carModel != ''">
                     `car_model`  = #{item.carModel},
                 </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.confObj !=null and item.confObj != ''">
                        and  `conf_obj`  =#{item.confObj}
                    </if>
                    <if test="item.deliveryAmount !=null">
                        and  `delivery_amount`  =#{item.deliveryAmount}
                    </if>
                    <if test="item.assembleNum !=null">
                        and  `assemble_num`  =#{item.assembleNum}
                    </if>
                    <if test="item.tenantCode !=null and item.tenantCode != ''">
                        and  `tenant_code`  =#{item.tenantCode}
                    </if>
                    <if test="item.parentOrderNo !=null and item.parentOrderNo != ''">
                        and  `parent_order_no`  =#{item.parentOrderNo}
                    </if>
                    <if test="item.cdCustomerCode !=null and item.cdCustomerCode != ''">
                        and  `cd_customer_code`  =#{item.cdCustomerCode}
                    </if>
                    <if test="item.assembleStatus !=null">
                        and  `assemble_status`  =#{item.assembleStatus}
                    </if>
                    <if test="item.relationPreposeTaskNo !=null and item.relationPreposeTaskNo != ''">
                        and  `relation_prepose_task_no`  =#{item.relationPreposeTaskNo}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.orderDistinctionFlag !=null and item.orderDistinctionFlag != ''">
                        and  `order_distinction_flag`  =#{item.orderDistinctionFlag}
                    </if>
                    <if test="item.carrierName !=null and item.carrierName != ''">
                        and  `carrier_name`  =#{item.carrierName}
                    </if>
                    <if test="item.assembleNo !=null and item.assembleNo != ''">
                        and  `assemble_no`  =#{item.assembleNo}
                    </if>
                    <if test="item.taskNo !=null and item.taskNo != ''">
                        and  `task_no`  =#{item.taskNo}
                    </if>
                    <if test="item.driverMobile !=null and item.driverMobile != ''">
                        and  `driver_mobile`  =#{item.driverMobile}
                    </if>
                    <if test="item.transportProduct !=null and item.transportProduct != ''">
                        and  `transport_product`  =#{item.transportProduct}
                    </if>
                    <if test="item.vehicleHandlingSeq !=null">
                        and  `vehicle_handling_seq`  =#{item.vehicleHandlingSeq}
                    </if>
                    <if test="item.vehicleCar !=null and item.vehicleCar != ''">
                        and  `vehicle_car`  =#{item.vehicleCar}
                    </if>
                    <if test="item.originTaskNo !=null and item.originTaskNo != ''">
                        and  `origin_task_no`  =#{item.originTaskNo}
                    </if>
                    <if test="item.orderNo !=null and item.orderNo != ''">
                        and  `order_no`  =#{item.orderNo}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.version !=null">
                        and  `version`  =#{item.version}
                    </if>
                    <if test="item.transportationDivision !=null">
                        and  `transportation_division`  =#{item.transportationDivision}
                    </if>
                    <if test="item.driver !=null and item.driver != ''">
                        and  `driver`  =#{item.driver}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.carrierCode !=null and item.carrierCode != ''">
                        and  `carrier_code`  =#{item.carrierCode}
                    </if>
                    <if test="item.planOrderNo !=null and item.planOrderNo != ''">
                        and  `plan_order_no`  =#{item.planOrderNo}
                    </if>
                    <if test="item.contractCode !=null and item.contractCode != ''">
                        and  `contract_code`  =#{item.contractCode}
                    </if>
                    <if test="item.driverIdentity !=null and item.driverIdentity != ''">
                        and  `driver_identity`  =#{item.driverIdentity}
                    </if>
                    <if test="item.carModel !=null and item.carModel != ''">
                        and  `car_model`  =#{item.carModel}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_extend
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
#{item.id}
        </foreach>
    </update>


    <update id="deleteBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_extend
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>

</mapper>