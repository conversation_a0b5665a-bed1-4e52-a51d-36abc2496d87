package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.mideaframework.core.web.PageResponse;

import java.util.List;
/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: ITaskAddressService
* Author: lindq2
* Date: 2019-6-28 10:56:27
* Description:ITaskAddressService服务接口
*/
public interface ITaskAddressService{

    Integer saveTaskAddress( TaskAddress taskAddress );

    Integer deleteTaskAddress( TaskAddress taskAddress );

    Integer updateTaskAddress( TaskAddress taskAddress );

    TaskAddress selectOneTaskAddress( TaskAddress taskAddress );

    PageResponse<TaskAddress> selectTaskAddressPage( TaskAddress taskAddress );

    List<TaskAddress> selectTaskAddressByIndex( TaskAddress taskAddress );

    int countTaskAddressByIndex( TaskAddress taskAddress );

    Integer insertBatch(List<TaskAddress> taskAddresss);

    Integer batchUpdate(List<TaskAddress> taskAddresss);

    Integer deleteBatch(List<TaskAddress> taskAddresss);

    Integer batchUpdateNull(List<TaskAddress> taskAddresss);

    Integer batchUpdateByTaskNo(List<TaskAddress> taskAddresss);

    Integer batchUpdateCanSetEmptyByTaskNo(List<TaskAddress> taskAddresss);

    List<TaskAddress> searchTaskAddressListByTaskNos(List<String> taskNos);

    List<TaskAddress> searchLastTaskAddressListByOrderNos(List<String> orderNos);
}