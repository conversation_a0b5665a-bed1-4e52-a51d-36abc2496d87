package com.midea.logistics.otplbs.manager;

import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Component
public class KcnEntyEcStlptJdCfgManager {
    @Autowired
    private ReportFeign reportFeign;
    @Autowired
    private UpDownloadHelper upDownloadHelper;

    public JsonResponse importKcnEntyEcStlptJdCfg(@RequestParam("file") MultipartFile file) {
        ImportFileRequest request = upDownloadHelper.uploadFileToOss(file);
        JsonResponse jsonResponse = reportFeign.importKcnEntyEcStlptJdCfg(request);
        if (!jsonResponse.judgeSuccess()) {
            throw BusinessException.fail(jsonResponse.getMsg());
        }
        return jsonResponse;
    }
}
