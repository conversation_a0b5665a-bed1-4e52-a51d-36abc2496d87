package com.midea.logistics.otplbs.manager;

import com.midea.logistics.otp.report.service.domain.bean.OrderAllProcess;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.Encodes;
import com.wkclz.util.excel.Excel;
import com.wkclz.util.excel.ExcelException;
import com.wkclz.util.excel.ExcelRow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;

/**
* @description: 订单
* @author:
* @createtime:
*/
@Component
public class ReportExportManager {


    private static final Logger logger = LoggerFactory.getLogger(ReportExportManager.class);

    protected void export(HttpServletResponse response, String fileName, Excel excel) throws IOException {
        // 保存到临时文件并输出 File
        File file = null;
        try {
            file = excel.createXlsxByFile();
        } catch (ExcelException e) {
            logger.error(e.getMessage());
        }

        if(null == file){
            throw BusinessException.fail("找不到文件");
        }

        try(
                // 2.下载
                OutputStream out = response.getOutputStream();
                InputStream is = new FileInputStream(file.getPath());
            ) {

            response.setContentType("application/octet-stream; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment; filename="+ Encodes.urlEncode(fileName));


            byte[] b = new byte[4096];
            int size = is.read(b);
            while (size > 0) {
                out.write(b, 0, size);
                size = is.read(b);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    public void export(HttpServletResponse response, List<OrderAllProcess> orderAllProcesses) throws IOException {
        String fileName = "小电全流程报表-"+System.currentTimeMillis()+".xlsx";

        Excel excel = new Excel();
        excel.setTitle("小电全流程");

        String[] header = { "服务平台", "订单来源号", "客户名称", "店铺名称", "订单类型", "安得订单号",
                            "客户订单号", "订单当前状态", "订单执行状态", "出仓库", "入库仓",
                            "入库时间", "收货人", "收货人手机", "收货人省", "收货人市",
                            "收货人区县", "收货人地址", "支付时间", "订单生成时间", "仓库接单时间(WMS接单时间）",
                            "上游指定快递", "快递公司", "发货时间", "快递单号","商品编码",
                            "商品名称", "商品状态", "商品类别", "实际发货数", "重量",
                            "体积", "单价", "订单来源","长",
                            "宽", "高", "计划数量",};

        excel.setHeader(header);


        for (OrderAllProcess object : orderAllProcesses) {

            ExcelRow row = excel.createRow();
            /**
             * 服务平台
             */
            row.addCell(object.getSiteName());
            /**
             * 订单来源号
             */
            row.addCell(object.getOriginOrderNo());
            /**
             * 客户名称
             */
            row.addCell(object.getCustomerName());

            /**
             * 店铺名称
             */
            row.addCell(object.getShopName());

            /**
             * 订单类型
             */
            row.addCell(object.getOrderType());

            /**
             * 安得订单号
             */
            row.addCell(object.getOrderNo());

            /**
             * 客户订单号
             */
            row.addCell(object.getCustomerOrderNo());

            /**
             * 订单当前状态
             */
            row.addCell(object.getOrderStatus());

            /**
             * 订单执行状态
             */
            row.addCell(object.getExcuteStatus());
            /**
             * 仓库名称 出库
             */
            row.addCell(object.getOutWhName());
            /**
             * 仓库名称 入库
             */
            row.addCell(object.getInWhName());

            /**
             * 入库时间
             */
            row.addCell(object.getInTime());

            /**
             * 收货人
             */
            row.addCell(object.getReceiverName());

            /**
             * 收货人手机
             */
            row.addCell(object.getReceiverMobile());

            /**
             * 收货人省
             */
            row.addCell(object.getReceiverProvinceName());

            /**
             * 收货人市
             */
            row.addCell(object.getReceiverCityName());

            /**
             * 收货人区县
             */
            row.addCell(object.getReceiverDistrictName ());

            /**
             * 收货人地址
             */
            row.addCell(object.getReceiverDetailAddr());

            /**
             * 支付时间
             */
            row.addCell(object.getPayDate());

            /**
             * 订单生成时间
             */
            row.addCell(object.getOrderCreateTime());

            /**
             * 仓库接单时间
             */
            row.addCell(object.getWmsCreateTime());

            /**
             * 上游指定快递
             */
            row.addCell(object.getUpperCarrierCode());

            /**
             * 快递公司
             */
            row.addCell(object.getCarrierCode());

            /**
             * 发货时间
             */
            row.addCell(object.getShipTime());

            /**
             * 快递单号
             */
            row.addCell(object.getWaybillNo());

            /**
             * 商品编码
             */
            row.addCell(object.getItemCode());

            /**
             * 商品名称
             */
            row.addCell(object.getItemName());

            /**
             * 商品状态
             */
            row.addCell(object.getItemStatus());

            /**
             * 商品类别
             */
            row.addCell(object.getItemClass());

            /**
             * 实际发货数
             */
            row.addCell(object.getActualQty());

            /**
             * 重量
             */
            row.addCell(object.getSingleWeight());

            /**
             * 体积
             */
            row.addCell(object.getVolume());

            /**
             * 单价
             */
            row.addCell(object.getPrice());

            /**
             * 订单来源
             */
            row.addCell(object.getSourceSystem());

            /**
             * 长
             */
            row.addCell(object.getLength());

            /**
             * 宽
             */
            row.addCell(object.getWidth());

            /**
             * 高
             */
            row.addCell(object.getHeight());

            /**
             * 计划数量
             */
            row.addCell(object.getTotalQty());


        }

        //导出
       this.export(response, fileName, excel);

    }



}


