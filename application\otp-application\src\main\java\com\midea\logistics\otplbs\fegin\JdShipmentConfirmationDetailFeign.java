package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.dispatch.domain.bean.JdShipmentConfirmationDetail;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: JdShipmentConfirmationDetailFeign
* Author: ex_chenty1
* Date: 2020-12-22 9:16:34
* Description:收货确认单明细 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface JdShipmentConfirmationDetailFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/jdShipmentConfirmationDetail", method = RequestMethod.POST)
    JsonResponse create(@RequestBody JdShipmentConfirmationDetail jdShipmentConfirmationDetail);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/jdShipmentConfirmationDetail/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/jdShipmentConfirmationDetail/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody JdShipmentConfirmationDetail jdShipmentConfirmationDetail);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/jdShipmentConfirmationDetail/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/jdShipmentConfirmationDetails", method = RequestMethod.POST)
    JsonResponse search(@RequestBody JdShipmentConfirmationDetail jdShipmentConfirmationDetail);
}
