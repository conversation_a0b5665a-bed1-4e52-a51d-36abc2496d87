package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.common.helper.bean.CustomerAgingConfigExcelResponse;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerAgingConfig;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CustomerAgingConfigFeign
 * Author: liwende
 * Date: 2019-5-27 11:44:56
 * Description:客户节点时效 feign
 */
@FeignClient(value = "logistics-otp-order-service",url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface CustomerAgingConfigFeign {

    @RequestMapping(value = "/customerAgingConfig", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerAgingConfig customerAgingConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerAgingConfig/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerAgingConfig/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerAgingConfig customerAgingConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerAgingConfig/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     * 根据查询条件查询列表
     *
     * @param customerAgingConfig
     * @return
     */
    @RequestMapping(value = "/customerAgingConfigsCount", method = RequestMethod.GET)
    JsonResponse searchCount(@SpringQueryMap CustomerAgingConfig customerAgingConfig);

    /**
     * 根据查询条件查询列表
     *
     * @param customerAgingConfig
     * @return
     */
    @RequestMapping(value = "/customerAgingConfigs", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap CustomerAgingConfig customerAgingConfig);

    /**
     * 客户时效及详情列表新增保存或修改保存
     */
    @RequestMapping(value = "/customerAgingConfig/createOrUpdateToSave", method = RequestMethod.POST)
    JsonResponse createOrUpdateToSave(@RequestBody /*@Valid*/ CustomerAgingConfig customerAgingConfig);

    @PostMapping(OrderRouters.CUSTOMER_AGING_CONFIG_BATCH_CREATE_OR_UPDATE_TO_SAVE)
    JsonResponse batchCreateOrUpdateToSave(@RequestBody List<CustomerAgingConfig> customerAgingConfigs);
    /**
     * 导出
     *
     * @param
     * @param
     * @return
     */
    @GetMapping(OrderRouters.CUSTOMER_AGING_CONFIG_EXPORTS)
    JsonResponse<List<CustomerAgingConfigExcelResponse>> exports(@SpringQueryMap CustomerAgingConfig customerAgingConfig);

    /**
     * 客户订单时效解析
     */
    @RequestMapping(value = "/agingParse", method = RequestMethod.POST)
    JsonResponse agingParse(@RequestBody OrderInfo orderInfo);

    /**
     * 批量停用
     */
    @RequestMapping(value = "/batchStop", method = RequestMethod.POST)
    JsonResponse batchStop(@RequestBody List<Long> ids);

    /**
     * 批量启用
     */
    @RequestMapping(value = "/batchStart", method = RequestMethod.POST)
    JsonResponse batchStart(@RequestBody List<Long> ids);
}
