<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.CustomerConfirmRuleMapper">

    <sql id="whereCustomFieldsSql">
        where delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="batchFlag !=null">
            and `batch_flag` =#{batchFlag}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and `source_system` =#{sourceSystem}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="remark !=null">
            and `remark` =#{remark}
        </if>
        <if test="confirmFlag !=null">
            and `confirm_flag` =#{confirmFlag}
        </if>
        <if test="customerName !=null and customerName != ''">
            and `customer_name` =#{customerName}
        </if>
        <if test="enabledFalg !=null">
            and `enabled_falg` =#{enabledFalg}
        </if>
        <if test="partFlag !=null">
            and `part_flag` =#{partFlag}
        </if>
        <if test="startTime != null and endTime != null">
            <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="orderTypes !=null and orderTypes.size > 0">
            and t.order_type in
            <foreach collection="orderTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerCodeList!=null and customerCodeList.size > 0">
            and t.customer_code in
            <foreach collection="customerCodeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="notIncustomerCodeList!=null and notIncustomerCodeList.size > 0">
            and t.customer_code not in
            <foreach collection="notIncustomerCodeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </sql>


    <select id="getCustomerConfirmRuleList"
            resultType="com.midea.logistics.otp.rule.domain.bean.CustomerConfirmRule">
        select
        <include refid="searchFieldsSql"/>
        from customer_confirm_rule t
        where delete_flag=0
        <if test="sourceSystems != null and sourceSystems.size > 0">
            and t.source_system in
            <foreach collection="sourceSystems" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="orderTypes != null and orderTypes.size > 0">
            and t.order_type in
            <foreach collection="orderTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCustomerConfirmRules" resultType="com.midea.logistics.otp.rule.domain.bean.CustomerConfirmRule">
        select
        <include refid="searchFieldsSql"/>
        from customer_confirm_rule t
        <include refid="whereCustomFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
                ${orderByType} </if>
        </if>
        limit ${start},${pageSize}
    </select>
</mapper>