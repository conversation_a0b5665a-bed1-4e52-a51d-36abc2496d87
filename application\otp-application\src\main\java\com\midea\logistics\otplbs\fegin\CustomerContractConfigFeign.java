package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.bean.CustomerContractConfig;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: CustomerContractConfigFeign
 * Author: liwende
 * Date: 2019-6-15 12:01:34
 * Description:客户合同配置表 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface CustomerContractConfigFeign {

    /**
     *
     */
    @RequestMapping(value = "/customerContractConfig", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CustomerContractConfig customerContractConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerContractConfig/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerContractConfig/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CustomerContractConfig customerContractConfig);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/customerContractConfig/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     *
     */
    @RequestMapping(value = "/customerContractConfigs", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap CustomerContractConfig customerContractConfig);

    /**
     * @param ids
     * @return
     */
    @RequestMapping(value = "/customerContractConfig/batchDeleteByBusinessKey", method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(List<Long> ids);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = "/customerContractConfig/batchCreateOrUpdate", method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(List<CustomerContractConfig> customerContractConfigs);
}
