package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.bean.ApartRule;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ApartRuleFeign
 * Author: jiajun
 * Date: 2019-5-27 13:59:00
 * Description:拆单规则 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface ApartRuleFeign {

    @RequestMapping(value = "/apartRule", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ApartRule apartRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/apartRule/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/apartRule/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ApartRule apartRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/apartRule/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/apartRules", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap ApartRule apartRule);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = "/apartRule/batchCreateOrUpdate", method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<ApartRule> apartRules);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = "/apartRule/batchDeleteByBusinessKey", method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
