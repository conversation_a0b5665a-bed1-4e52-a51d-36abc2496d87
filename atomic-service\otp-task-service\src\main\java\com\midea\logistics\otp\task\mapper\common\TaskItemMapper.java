package com.midea.logistics.otp.task.mapper.common;

import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.mideaframework.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskItemMapper
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:TaskItemMapper服务接口
 */
@Mapper
public interface TaskItemMapper extends BaseMapper<TaskItem> {

    Integer batchUpdateQty(List<TaskItem> taskItems);

    List<TaskItem> listByTaskNos(List<String> taskNos);

    List<TaskItem> sumActQtyByTaskNos(@Param("TaskNos") List<String> TaskNos);

    List<TaskItem> querySplitTasksByOrderNo(@Param("orderNo") String orderNo);
}