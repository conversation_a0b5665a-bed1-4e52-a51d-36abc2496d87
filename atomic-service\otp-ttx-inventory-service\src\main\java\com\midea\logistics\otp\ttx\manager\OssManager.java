package com.midea.logistics.otp.ttx.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.encript.MD5Utils;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: yaowl
 * @date: 2020-1-15 17:05
 */
@Service
public class OssManager {

    @Value("${spring.oss.appId}")
    private String appId;
    @Value("${spring.oss.bucketName}")
    private String bucketName;
    @Value("${spring.oss.timeStamp}")
    private String timeStamp;
    @Value("${spring.oss.appKey}")
    private String appKey;
    @Value("${spring.oss.certificationUrl}")
    private String certificationUrl;
    @Value("${spring.oss.downLoadRequestUrl}")
    private String downLoadRequestUrl;
    @Value("${spring.oss.upLoadUrl}")
    private String upLoadUrl;

    private Logger logger= LoggerFactory.getLogger(OssManager.class);

    private OkHttpClient client;
    private MediaType mediaType ;

    private String ossCertification=null;

    private String connectFailmsg="连接失败，获取certification失败";
    private OssManager() {
        client= new OkHttpClient();
        mediaType = MediaType.parse("application/json");
    }

    /**
     * @Description: 调用OSS上传文件接口
     * @Param: [file, url, certification]
     * @return: boolean
     * @Author: yaowl
     * @Date: 2019-9-17
     */
    public boolean upLoad(File file) {
        ossCertification=getCertification();
        String upLoadCompleteUrl = upLoadUrl + appId + "/" + bucketName + "/" + file.getName();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/octet-stream"), file);
        DateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        Request request = new Request.Builder()
            .url(upLoadCompleteUrl)
            .put(requestBody)
            .addHeader("certification", ossCertification)
            .addHeader("x-amz-date", format.format(new Date()))
            .build();
        Response response;
        try {
            response = client.newCall(request).execute();
        } catch (IOException e) {
            logger.error("连接上传接口失败，上传异常文件失败");
            return false;
        }
        if (response == null || !response.isSuccessful()) {
            logger.error("连接上传接口成功，上传异常文件失败");
            return false;
        }
        return true;
    }

    /**
     * @Description: 获取下载的文件流
     * @Param: [filename, certification]
     * @return: java.io.InputStream
     * @Author: yaowl
     * @Date: 2019-8-29
     */
    public InputStream getDownloadFile(String filename ) {
        ossCertification=getCertification();
        String downLoadUrl = downLoadRequestUrl + appId + "/" + bucketName + "/" + filename + "?certification=" + ossCertification;
        InputStream inputStream=null;
        Request request = new Request.Builder()
            .url(downLoadUrl)
            .get()
            .addHeader("Content-Type", "application/json")
            .build();
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()||response.body()==null) {
                return null;
            }
            inputStream =  response.body().byteStream() ;
        } catch (Exception e) {
            logger.error("{}:OSS文件下载失败",filename);
        }
        return inputStream;
    }

    /**
     * @Description: 发送鉴权请求，获取权限
     * @Param: []
     * @return: java.lang.String
     * @Author: yaowl
     * @Date: 2019-8-29
     */
    public String getCertification() {
        String signature = MD5Utils.md5(appId + timeStamp + appKey);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Int", 18000);
        jsonObject.put("appid", appId);
        jsonObject.put("signature",signature);
        jsonObject.put("ts","1556181405140");
        String param=jsonObject.toJSONString();
        RequestBody body = RequestBody.create(mediaType, param);
        Request request = new Request.Builder()
            .url(certificationUrl)
            .post(body)
            .build();

        Response response;
        try {
            response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                jsonObject = JSON.parseObject(response.body() != null ? response.body().string() : null);
                if (jsonObject.getInteger("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    return data.getString("certification");
                }
            }
            throw new BusinessException(connectFailmsg);
        } catch (IOException e) {
            logger.error(connectFailmsg);
            throw new BusinessException(connectFailmsg);
        }
    }

}
