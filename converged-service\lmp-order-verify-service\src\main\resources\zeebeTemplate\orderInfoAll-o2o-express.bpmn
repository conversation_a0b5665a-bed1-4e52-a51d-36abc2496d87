<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1t472w9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-o2o-express" name="子订单流程-o2o-express" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0f14mld</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0um1w36" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0f14mld</bpmn:incoming>
      <bpmn:outgoing>Flow_0gawn5t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1xtt5lw" name="计费相关">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/anyBmsInfo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0gawn5t</bpmn:incoming>
      <bpmn:outgoing>Flow_097xpec</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1d6l6xs" name="推送查单系统,bms,net,task">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bmsLotsNetTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_097xpec</bpmn:incoming>
      <bpmn:outgoing>Flow_03kxhbz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0f14mld" sourceRef="StartEvent_1" targetRef="Activity_0um1w36" />
    <bpmn:sequenceFlow id="Flow_0gawn5t" sourceRef="Activity_0um1w36" targetRef="Activity_1xtt5lw" />
    <bpmn:sequenceFlow id="Flow_097xpec" sourceRef="Activity_1xtt5lw" targetRef="Activity_1d6l6xs" />
    <bpmn:endEvent id="Event_07p1e5k" name="结束">
      <bpmn:incoming>Flow_03kxhbz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03kxhbz" sourceRef="Activity_1d6l6xs" targetRef="Event_07p1e5k" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-o2o-express">
      <bpmndi:BPMNEdge id="Flow_03kxhbz_di" bpmnElement="Flow_03kxhbz">
        <di:waypoint x="820" y="117" />
        <di:waypoint x="892" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_097xpec_di" bpmnElement="Flow_097xpec">
        <di:waypoint x="650" y="117" />
        <di:waypoint x="720" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gawn5t_di" bpmnElement="Flow_0gawn5t">
        <di:waypoint x="490" y="117" />
        <di:waypoint x="550" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f14mld_di" bpmnElement="Flow_0f14mld">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="390" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0um1w36_di" bpmnElement="Activity_0um1w36">
        <dc:Bounds x="390" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xtt5lw_di" bpmnElement="Activity_1xtt5lw">
        <dc:Bounds x="550" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1d6l6xs_di" bpmnElement="Activity_1d6l6xs">
        <dc:Bounds x="720" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_07p1e5k_di" bpmnElement="Event_07p1e5k">
        <dc:Bounds x="892" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="900" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
