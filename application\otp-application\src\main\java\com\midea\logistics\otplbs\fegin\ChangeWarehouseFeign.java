package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.converged.domain.request.ChangeWarehouseBatchRequest;
import com.midea.logistics.otp.order.converged.domain.request.ChangeWarehouseRequest;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * FileName: ChangeWarehouseFeign
 * Author: ludh
 * Date: 2023-02-08
 * Description:改仓 feign
 */
@FeignClient(value = "logistics-otp-order-converged-service")
public interface ChangeWarehouseFeign {

    @PostMapping(value = OrderRouters.CHANGE_WAREHOUSE_DI)
    JsonResponse changeWarehouseDi(@RequestBody ChangeWarehouseRequest changeWarehouseRequest);


    /**
     * 批量分拨入库改仓
     */
    @PostMapping(value = OrderRouters.CHANGE_WAREHOUSE_DI_BATCH)
    JsonResponse changeWarehouseDiByBatch(@RequestBody ChangeWarehouseBatchRequest batchRequest);

}
