package com.midea.logistics.otplbs.bean;

import lombok.Data;
import lombok.Getter;

/**
 * @Author: dumg
 * @Date: 2023-03-23-9:03
 * Description:
 */
@Getter
public class ImportResponseVo {
    private String docId;
    private String fileName;
    private Double fileSize;
    private String createTime;
    private String filePath;
    private String createUserCode;

    public ImportResponseVo() {
    }

    public ImportResponseVo docId(String docId){
        this.docId=docId;
        return this;
    }

    public ImportResponseVo fileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public ImportResponseVo fileSize(Double fileSize) {
        this.fileSize = fileSize;
        return this;
    }

    public ImportResponseVo createTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public ImportResponseVo filePath(String filePath) {
        this.filePath = filePath;
        return this;
    }

    public ImportResponseVo createUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
        return this;
    }
}
