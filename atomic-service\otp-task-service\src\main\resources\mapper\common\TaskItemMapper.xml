<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskItemMapper">

    <sql id="searchFieldsSql">
            `product_specification` AS  productSpecification,
            `sn_code` AS  snCode,
            `item_code` AS  itemCode,
            `lg_order_no` AS  lgOrderNo,
            `measure_standard` AS  measureStandard,
            `cancle_qty` AS  cancleQty,
            `delete_flag` AS  deleteFlag,
            `is_first` AS  isFirst,
            `item_name` AS  itemName,
            `customer_item_code` AS  customerItemCode,
            `specification_unit` AS  specificationUnit,
            `single_volume` AS  singleVolume,
            `price` AS  price,
            `split_qty` AS  splitQty,
            `lot_att08` AS  lotAtt08,
            `unit_code` AS  unitCode,
            `lot_att07` AS  lotAtt07,
            `lot_att06` AS  lotAtt06,
            `install_flag` AS  installFlag,
            `lot_att05` AS  lotAtt05,
            `id` AS  id,
            `item_size` AS  itemSize,
            `lot_att03` AS  lotAtt03,
            `brand` AS  brand,
            `lot_att02` AS  lotAtt02,
            `source_item_name` AS  sourceItemName,
            `produce_timestamp` AS  produceTimestamp,
            `order_no` AS  orderNo,
            `set_flag` AS  setFlag,
            `upper_item_status_to` AS  upperItemStatusTo,
            `amout` AS  amout,
            `packing_decimal_amount` AS  packingDecimalAmount,
            `bu_code` AS  buCode,
            `to_customer_item_code` AS  toCustomerItemCode,
            `hik_flag` AS  hikFlag,
            `version` AS  version,
            `item_charging_type` AS  itemChargingType,
            `volume` AS  volume,
            `total_volume` AS  totalVolume,
            `total_gross_weight` AS  totalGrossWeight,
            `material_group1` AS  materialGroup1,
            `update_user_code` AS  updateUserCode,
            `net_weight` AS  netWeight,
            `unit` AS  unit,
            `material_group3` AS  materialGroup3,
            `fictitious_flag` AS  fictitiousFlag,
            `upper_item_status` AS  upperItemStatus,
            `material_group2` AS  materialGroup2,
            `material_group5` AS  materialGroup5,
            `material_group4` AS  materialGroup4,
            `upper_line_no` AS  upperLineNo,
            `material_group6` AS  materialGroup6,
            `packing_amount` AS  packingAmount,
            `source_item_code` AS  sourceItemCode,
            `source_order_no` AS  sourceOrderNo,
            `item_spec` AS  itemSpec,
            `plan_qty` AS  planQty,
            `item_suite_qty` AS  itemSuiteQty,
            `extend_info_fields` AS  extendInfoFields,
            `item_class_name` AS  itemClassName,
            `upper_item_id` AS  upperItemId,
            `service_flag` AS  serviceFlag,
            `final_price` AS  finalPrice,
            `remark` AS  remark,
            `tenant_code` AS  tenantCode,
            `parent_line_no` AS  parentLineNo,
            `item_status_to` AS  itemStatusTo,
            `new_item_flag` AS  newItemFlag,
            `market_model` AS  marketModel,
            `to_item_code` AS  toItemCode,
            `produce_code` AS  produceCode,
            `lot_code` AS  lotCode,
            `item_suite_code` AS  itemSuiteCode,
            `item_status` AS  itemStatus,
            `task_no` AS  taskNo,
            `single_weight` AS  singleWeight,
            `source_pruduct` AS  sourcePruduct,
            `item_line_no` AS  itemLineNo,
            `create_user_code` AS  createUserCode,
            `batch_code` AS  batchCode,
            `length` AS  length,
            `update_time` AS  updateTime,
            `act_qty` AS  actQty,
            `gross_weight` AS  grossWeight,
            `expire_timestamp` AS  expireTimestamp,
            `finance_no` AS  financeNo,
            `create_time` AS  createTime,
            `item_class` AS  itemClass,
            `pkg_qty` AS  pkgQty,
            `width` AS  width,
            `height` AS  height,
            `extend1` AS  extend1,
            `notax_price` AS  notaxPrice,
            `upper_item_code` AS  upperItemCode,
            `group_indication` AS groupIndication,
            `if_install` AS ifInstall,
            `main_item_code` AS mainItemCode,
            `material_group7` AS  materialGroup7,
            `material_group8` AS  materialGroup8
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="productSpecification !=null and productSpecification != ''">
                        and `product_specification` =#{productSpecification}
                    </if>
                    <if test="snCode !=null and snCode != ''">
                        and `sn_code` =#{snCode}
                    </if>
                    <if test="itemCode !=null and itemCode != ''">
                        and `item_code` =#{itemCode}
                    </if>
                    <if test="lgOrderNo !=null and lgOrderNo != ''">
                        and `lg_order_no` =#{lgOrderNo}
                    </if>
                    <if test="measureStandard !=null and measureStandard != ''">
                        and `measure_standard` =#{measureStandard}
                    </if>
                    <if test="cancleQty !=null">
                        and `cancle_qty` =#{cancleQty}
                    </if>
                    <if test="isFirst !=null">
                        and `is_first` =#{isFirst}
                    </if>
                    <if test="itemName !=null and itemName != ''">
                        and `item_name` =#{itemName}
                    </if>
                    <if test="customerItemCode !=null and customerItemCode != ''">
                        and `customer_item_code` =#{customerItemCode}
                    </if>
                    <if test="specificationUnit !=null and specificationUnit != ''">
                        and `specification_unit` =#{specificationUnit}
                    </if>
                    <if test="singleVolume !=null">
                        and `single_volume` =#{singleVolume}
                    </if>
                    <if test="price !=null">
                        and `price` =#{price}
                    </if>
                    <if test="splitQty !=null">
                        and `split_qty` =#{splitQty}
                    </if>
                    <if test="lotAtt08 !=null and lotAtt08 != ''">
                        and `lot_att08` =#{lotAtt08}
                    </if>
                    <if test="unitCode !=null and unitCode != ''">
                        and `unit_code` =#{unitCode}
                    </if>
                    <if test="lotAtt07 !=null and lotAtt07 != ''">
                        and `lot_att07` =#{lotAtt07}
                    </if>
                    <if test="lotAtt06 !=null and lotAtt06 != ''">
                        and `lot_att06` =#{lotAtt06}
                    </if>
                    <if test="installFlag !=null">
                        and `install_flag` =#{installFlag}
                    </if>
                    <if test="lotAtt05 !=null and lotAtt05 != ''">
                        and `lot_att05` =#{lotAtt05}
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="itemSize !=null and itemSize != ''">
                        and `item_size` =#{itemSize}
                    </if>
                    <if test="lotAtt03 !=null and lotAtt03 != ''">
                        and `lot_att03` =#{lotAtt03}
                    </if>
                    <if test="brand !=null and brand != ''">
                        and `brand` =#{brand}
                    </if>
                    <if test="lotAtt02 !=null and lotAtt02 != ''">
                        and `lot_att02` =#{lotAtt02}
                    </if>
                    <if test="sourceItemName !=null and sourceItemName != ''">
                        and `source_item_name` =#{sourceItemName}
                    </if>
                    <if test="produceTimestamp !=null">
                        and `produce_timestamp` =#{produceTimestamp}
                    </if>
                    <if test="orderNo !=null and orderNo != ''">
                        and `order_no` =#{orderNo}
                    </if>
                    <if test="setFlag !=null">
                        and `set_flag` =#{setFlag}
                    </if>
                    <if test="upperItemStatusTo !=null and upperItemStatusTo != ''">
                        and `upper_item_status_to` =#{upperItemStatusTo}
                    </if>
                    <if test="amout !=null">
                        and `amout` =#{amout}
                    </if>
                    <if test="packingDecimalAmount !=null">
                        and `packing_decimal_amount` =#{packingDecimalAmount}
                    </if>
                    <if test="buCode !=null and buCode != ''">
                        and `bu_code` =#{buCode}
                    </if>
                    <if test="toCustomerItemCode !=null and toCustomerItemCode != ''">
                        and `to_customer_item_code` =#{toCustomerItemCode}
                    </if>
                    <if test="hikFlag !=null and hikFlag != ''">
                        and `hik_flag` =#{hikFlag}
                    </if>
                    <if test="itemChargingType !=null and itemChargingType != ''">
                        and `item_charging_type` =#{itemChargingType}
                    </if>
                    <if test="volume !=null">
                        and `volume` =#{volume}
                    </if>
                    <if test="totalVolume !=null">
                        and `total_volume` =#{totalVolume}
                    </if>
                    <if test="totalGrossWeight !=null">
                        and `total_gross_weight` =#{totalGrossWeight}
                    </if>
                    <if test="materialGroup1 !=null and materialGroup1 != ''">
                        and `material_group1` =#{materialGroup1}
                    </if>
                    <if test="netWeight !=null">
                        and `net_weight` =#{netWeight}
                    </if>
                    <if test="unit !=null and unit != ''">
                        and `unit` =#{unit}
                    </if>
                    <if test="materialGroup3 !=null and materialGroup3 != ''">
                        and `material_group3` =#{materialGroup3}
                    </if>
                    <if test="fictitiousFlag !=null">
                        and `fictitious_flag` =#{fictitiousFlag}
                    </if>
                    <if test="upperItemStatus !=null and upperItemStatus != ''">
                        and `upper_item_status` =#{upperItemStatus}
                    </if>
                    <if test="materialGroup2 !=null and materialGroup2 != ''">
                        and `material_group2` =#{materialGroup2}
                    </if>
                    <if test="materialGroup5 !=null and materialGroup5 != ''">
                        and `material_group5` =#{materialGroup5}
                    </if>
                    <if test="materialGroup4 !=null and materialGroup4 != ''">
                        and `material_group4` =#{materialGroup4}
                    </if>
                    <if test="upperLineNo !=null and upperLineNo != ''">
                        and `upper_line_no` =#{upperLineNo}
                    </if>
                    <if test="materialGroup6 !=null and materialGroup6 != ''">
                        and `material_group6` =#{materialGroup6}
                    </if>
                    <if test="packingAmount !=null">
                        and `packing_amount` =#{packingAmount}
                    </if>
                    <if test="sourceItemCode !=null and sourceItemCode != ''">
                        and `source_item_code` =#{sourceItemCode}
                    </if>
                    <if test="sourceOrderNo !=null and sourceOrderNo != ''">
                        and `source_order_no` =#{sourceOrderNo}
                    </if>
                    <if test="itemSpec !=null and itemSpec != ''">
                        and `item_spec` =#{itemSpec}
                    </if>
                    <if test="planQty !=null">
                        and `plan_qty` =#{planQty}
                    </if>
                    <if test="itemSuiteQty !=null">
                        and `item_suite_qty` =#{itemSuiteQty}
                    </if>
                    <if test="extendInfoFields !=null and extendInfoFields != ''">
                        and `extend_info_fields` =#{extendInfoFields}
                    </if>
                    <if test="itemClassName !=null and itemClassName != ''">
                        and `item_class_name` =#{itemClassName}
                    </if>
                    <if test="upperItemId !=null and upperItemId != ''">
                        and `upper_item_id` =#{upperItemId}
                    </if>
                    <if test="serviceFlag !=null and serviceFlag != ''">
                        and `service_flag` =#{serviceFlag}
                    </if>
                    <if test="finalPrice !=null">
                        and `final_price` =#{finalPrice}
                    </if>
                    <if test="remark !=null">
                        and `remark` =#{remark}
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        <!--            and `tenant_code` =#{tenantCode}-->
                    </if>
                    <if test="parentLineNo !=null">
                        and `parent_line_no` =#{parentLineNo}
                    </if>
                    <if test="itemStatusTo !=null and itemStatusTo != ''">
                        and `item_status_to` =#{itemStatusTo}
                    </if>
                    <if test="newItemFlag !=null and newItemFlag != ''">
                        and `new_item_flag` =#{newItemFlag}
                    </if>
                    <if test="marketModel !=null and marketModel != ''">
                        and `market_model` =#{marketModel}
                    </if>
                    <if test="toItemCode !=null and toItemCode != ''">
                        and `to_item_code` =#{toItemCode}
                    </if>
                    <if test="produceCode !=null and produceCode != ''">
                        and `produce_code` =#{produceCode}
                    </if>
                    <if test="lotCode !=null and lotCode != ''">
                        and `lot_code` =#{lotCode}
                    </if>
                    <if test="itemSuiteCode !=null and itemSuiteCode != ''">
                        and `item_suite_code` =#{itemSuiteCode}
                    </if>
                    <if test="itemStatus !=null and itemStatus != ''">
                        and `item_status` =#{itemStatus}
                    </if>
                    <if test="taskNo !=null and taskNo != ''">
                        and `task_no` =#{taskNo}
                    </if>
                    <if test="singleWeight !=null">
                        and `single_weight` =#{singleWeight}
                    </if>
                    <if test="sourcePruduct !=null and sourcePruduct != ''">
                        and `source_pruduct` =#{sourcePruduct}
                    </if>
                    <if test="itemLineNo !=null">
                        and `item_line_no` =#{itemLineNo}
                    </if>
                    <if test="batchCode !=null and batchCode != ''">
                        and `batch_code` =#{batchCode}
                    </if>
                    <if test="length !=null">
                        and `length` =#{length}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="actQty !=null">
                        and `act_qty` =#{actQty}
                    </if>
                    <if test="grossWeight !=null">
                        and `gross_weight` =#{grossWeight}
                    </if>
                    <if test="expireTimestamp !=null">
                        and `expire_timestamp` =#{expireTimestamp}
                    </if>
                    <if test="financeNo !=null and financeNo != ''">
                        and `finance_no` =#{financeNo}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="itemClass !=null and itemClass != ''">
                        and `item_class` =#{itemClass}
                    </if>
                    <if test="pkgQty !=null">
                        and `pkg_qty` =#{pkgQty}
                    </if>
                    <if test="width !=null">
                        and `width` =#{width}
                    </if>
                    <if test="extend1 !=null and extend1 != ''">
                        and `extend1` =#{extend1}
                    </if>
                    <if test="notaxPrice !=null">
                        and `notax_price` =#{notaxPrice}
                    </if>
                    <if test="upperItemCode !=null and upperItemCode != ''">
                        and `upper_item_code` =#{upperItemCode}
                    </if>
                    <if test="groupIndication !=null and groupIndication != ''">
                        and `group_indication` =#{groupIndication}
                    </if>
                    <if test="ifInstall !=null and ifInstall != ''">
                        and `if_install` =#{ifInstall}
                    </if>
                    <if test="mainItemCode !=null and mainItemCode != ''">
                        and `main_item_code` =#{mainItemCode}
                    </if>
                    <if test="materialGroup7 !=null and materialGroup7 != ''">
                        and `material_group7` =#{materialGroup7}
                    </if>
                    <if test="materialGroup8 !=null and materialGroup8 != ''">
                        and `material_group8` =#{materialGroup8}
                    </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
                        <if test="productSpecification !=null and productSpecification != ''">
                            `product_specification` = #{productSpecification},
                        </if>
                        <if test="snCode !=null and snCode != ''">
                            `sn_code` = #{snCode},
                        </if>
                        <if test="itemCode !=null and itemCode != ''">
                            `item_code` = #{itemCode},
                        </if>
                        <if test="lgOrderNo !=null and lgOrderNo != ''">
                            `lg_order_no` = #{lgOrderNo},
                        </if>
                        <if test="measureStandard !=null and measureStandard != ''">
                            `measure_standard` = #{measureStandard},
                        </if>
                        <if test="cancleQty != null">
                            `cancle_qty` = #{cancleQty},
                        </if>
                        <if test="isFirst != null">
                            `is_first` = #{isFirst},
                        </if>
                        <if test="itemName !=null and itemName != ''">
                            `item_name` = #{itemName},
                        </if>
                        <if test="customerItemCode !=null and customerItemCode != ''">
                            `customer_item_code` = #{customerItemCode},
                        </if>
                        <if test="specificationUnit !=null and specificationUnit != ''">
                            `specification_unit` = #{specificationUnit},
                        </if>
                        <if test="singleVolume != null">
                            `single_volume` = #{singleVolume},
                        </if>
                        <if test="price != null">
                            `price` = #{price},
                        </if>
                        <if test="splitQty != null">
                            `split_qty` = #{splitQty},
                        </if>
                        <if test="lotAtt08 !=null and lotAtt08 != ''">
                            `lot_att08` = #{lotAtt08},
                        </if>
                        <if test="unitCode !=null and unitCode != ''">
                            `unit_code` = #{unitCode},
                        </if>
                        <if test="lotAtt07 !=null and lotAtt07 != ''">
                            `lot_att07` = #{lotAtt07},
                        </if>
                        <if test="lotAtt06 !=null and lotAtt06 != ''">
                            `lot_att06` = #{lotAtt06},
                        </if>
                        <if test="installFlag != null">
                            `install_flag` = #{installFlag},
                        </if>
                        <if test="lotAtt05 !=null and lotAtt05 != ''">
                            `lot_att05` = #{lotAtt05},
                        </if>
                        <if test="itemSize !=null and itemSize != ''">
                            `item_size` = #{itemSize},
                        </if>
                        <if test="lotAtt03 !=null and lotAtt03 != ''">
                            `lot_att03` = #{lotAtt03},
                        </if>
                        <if test="brand !=null and brand != ''">
                            `brand` = #{brand},
                        </if>
                        <if test="lotAtt02 !=null and lotAtt02 != ''">
                            `lot_att02` = #{lotAtt02},
                        </if>
                        <if test="sourceItemName !=null and sourceItemName != ''">
                            `source_item_name` = #{sourceItemName},
                        </if>
                        <if test="produceTimestamp != null">
                            `produce_timestamp` = #{produceTimestamp},
                        </if>
                        <if test="orderNo !=null and orderNo != ''">
                            `order_no` = #{orderNo},
                        </if>
                        <if test="setFlag != null">
                            `set_flag` = #{setFlag},
                        </if>
                        <if test="upperItemStatusTo !=null and upperItemStatusTo != ''">
                            `upper_item_status_to` = #{upperItemStatusTo},
                        </if>
                        <if test="amout != null">
                            `amout` = #{amout},
                        </if>
                        <if test="packingDecimalAmount != null">
                            `packing_decimal_amount` = #{packingDecimalAmount},
                        </if>
                        <if test="buCode !=null and buCode != ''">
                            `bu_code` = #{buCode},
                        </if>
                        <if test="toCustomerItemCode !=null and toCustomerItemCode != ''">
                            `to_customer_item_code` = #{toCustomerItemCode},
                        </if>
                        <if test="hikFlag !=null and hikFlag != ''">
                            `hik_flag` = #{hikFlag},
                        </if>
                        <if test="itemChargingType !=null and itemChargingType != ''">
                            `item_charging_type` = #{itemChargingType},
                        </if>
                        <if test="volume != null">
                            `volume` = #{volume},
                        </if>
                        <if test="totalVolume != null">
                            `total_volume` = #{totalVolume},
                        </if>
                        <if test="totalGrossWeight != null">
                            `total_gross_weight` = #{totalGrossWeight},
                        </if>
                        <if test="materialGroup1 !=null and materialGroup1 != ''">
                            `material_group1` = #{materialGroup1},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="netWeight != null">
                            `net_weight` = #{netWeight},
                        </if>
                        <if test="unit !=null and unit != ''">
                            `unit` = #{unit},
                        </if>
                        <if test="materialGroup3 !=null and materialGroup3 != ''">
                            `material_group3` = #{materialGroup3},
                        </if>
                        <if test="fictitiousFlag != null">
                            `fictitious_flag` = #{fictitiousFlag},
                        </if>
                        <if test="upperItemStatus !=null and upperItemStatus != ''">
                            `upper_item_status` = #{upperItemStatus},
                        </if>
                        <if test="materialGroup2 !=null and materialGroup2 != ''">
                            `material_group2` = #{materialGroup2},
                        </if>
                        <if test="materialGroup5 !=null and materialGroup5 != ''">
                            `material_group5` = #{materialGroup5},
                        </if>
                        <if test="materialGroup4 !=null and materialGroup4 != ''">
                            `material_group4` = #{materialGroup4},
                        </if>
                        <if test="upperLineNo !=null and upperLineNo != ''">
                            `upper_line_no` = #{upperLineNo},
                        </if>
                        <if test="materialGroup6 !=null and materialGroup6 != ''">
                            `material_group6` = #{materialGroup6},
                        </if>
                        <if test="packingAmount != null">
                            `packing_amount` = #{packingAmount},
                        </if>
                        <if test="sourceItemCode !=null and sourceItemCode != ''">
                            `source_item_code` = #{sourceItemCode},
                        </if>
                        <if test="sourceOrderNo !=null and sourceOrderNo != ''">
                            `source_order_no` = #{sourceOrderNo},
                        </if>
                        <if test="itemSpec !=null and itemSpec != ''">
                            `item_spec` = #{itemSpec},
                        </if>
                        <if test="planQty != null">
                            `plan_qty` = #{planQty},
                        </if>
                        <if test="itemSuiteQty != null">
                            `item_suite_qty` = #{itemSuiteQty},
                        </if>
                        <if test="itemClassName !=null and itemClassName != ''">
                            `item_class_name` = #{itemClassName},
                        </if>
                        <if test="upperItemId !=null and upperItemId != ''">
                            `upper_item_id` = #{upperItemId},
                        </if>
                        <if test="serviceFlag !=null and serviceFlag != ''">
                            `service_flag` = #{serviceFlag},
                        </if>
                        <if test="finalPrice != null">
                            `final_price` = #{finalPrice},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="tenantCode !=null and tenantCode != ''">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="parentLineNo != null">
                            `parent_line_no` = #{parentLineNo},
                        </if>
                        <if test="itemStatusTo !=null and itemStatusTo != ''">
                            `item_status_to` = #{itemStatusTo},
                        </if>
                        <if test="newItemFlag !=null and newItemFlag != ''">
                            `new_item_flag` = #{newItemFlag},
                        </if>
                        <if test="marketModel !=null and marketModel != ''">
                            `market_model` = #{marketModel},
                        </if>
                        <if test="toItemCode !=null and toItemCode != ''">
                            `to_item_code` = #{toItemCode},
                        </if>
                        <if test="extendInfoFields !=null and extendInfoFields != ''">
                            `extend_info_fields` = #{extendInfoFields},
                        </if>
                        <if test="produceCode !=null and produceCode != ''">
                            `produce_code` = #{produceCode},
                        </if>
                        <if test="lotCode !=null and lotCode != ''">
                            `lot_code` = #{lotCode},
                        </if>
                        <if test="itemSuiteCode !=null and itemSuiteCode != ''">
                            `item_suite_code` = #{itemSuiteCode},
                        </if>
                        <if test="itemStatus !=null and itemStatus != ''">
                            `item_status` = #{itemStatus},
                        </if>
                        <if test="taskNo !=null and taskNo != ''">
                            `task_no` = #{taskNo},
                        </if>
                        <if test="singleWeight != null">
                            `single_weight` = #{singleWeight},
                        </if>
                        <if test="sourcePruduct !=null and sourcePruduct != ''">
                            `source_pruduct` = #{sourcePruduct},
                        </if>
                        <if test="itemLineNo != null">
                            `item_line_no` = #{itemLineNo},
                        </if>
                        <if test="batchCode !=null and batchCode != ''">
                            `batch_code` = #{batchCode},
                        </if>
                        <if test="length != null">
                            `length` = #{length},
                        </if>
                        <if test="actQty != null">
                            `act_qty` = #{actQty},
                        </if>
                        <if test="grossWeight != null">
                            `gross_weight` = #{grossWeight},
                        </if>
                        <if test="expireTimestamp != null">
                            `expire_timestamp` = #{expireTimestamp},
                        </if>
                        <if test="financeNo !=null and financeNo != ''">
                            `finance_no` = #{financeNo},
                        </if>
                        <if test="itemClass !=null and itemClass != ''">
                            `item_class` = #{itemClass},
                        </if>
                        <if test="pkgQty != null">
                            `pkg_qty` = #{pkgQty},
                        </if>
                        <if test="width != null">
                            `width` = #{width},
                        </if>
                        <if test="height != null">
                            `height` = #{height},
                        </if>
                        <if test="extend1 !=null and extend1 != ''">
                            `extend1` = #{extend1},
                        </if>
                        <if test="notaxPrice != null">
                            `notax_price` = #{notaxPrice},
                        </if>
                        <if test="upperItemCode !=null and upperItemCode != ''">
                            `upper_item_code` = #{upperItemCode},
                        </if>
                        <if test="groupIndication !=null and groupIndication != ''">
                            `group_indication` =#{groupIndication},
                        </if>
                        <if test="ifInstall !=null and ifInstall != ''">
                            `if_install` =#{ifInstall},
                        </if>
                        <if test="mainItemCode !=null and mainItemCode != ''">
                            `main_item_code` =#{mainItemCode},
                        </if>
                        <if test="materialGroup7 !=null and materialGroup7 != ''">
                            `material_group7` = #{materialGroup7},
                        </if>
                        <if test="materialGroup8 !=null and materialGroup8 != ''">
                            `material_group8` = #{materialGroup8},
                        </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="productSpecification !=null">
                            `product_specification` = #{productSpecification},
                        </if>
                        <if test="snCode !=null">
                            `sn_code` = #{snCode},
                        </if>
                        <if test="itemCode !=null">
                            `item_code` = #{itemCode},
                        </if>
                        <if test="lgOrderNo !=null">
                            `lg_order_no` = #{lgOrderNo},
                        </if>
                        <if test="measureStandard !=null">
                            `measure_standard` = #{measureStandard},
                        </if>
                        <if test="cancleQty != null">
                            `cancle_qty` = #{cancleQty},
                        </if>
                        <if test="isFirst != null">
                            `is_first` = #{isFirst},
                        </if>
                        <if test="itemName !=null">
                            `item_name` = #{itemName},
                        </if>
                        <if test="customerItemCode !=null">
                            `customer_item_code` = #{customerItemCode},
                        </if>
                        <if test="specificationUnit !=null">
                            `specification_unit` = #{specificationUnit},
                        </if>
                        <if test="singleVolume != null">
                            `single_volume` = #{singleVolume},
                        </if>
                        <if test="price != null">
                            `price` = #{price},
                        </if>
                        <if test="splitQty != null">
                            `split_qty` = #{splitQty},
                        </if>
                        <if test="lotAtt08 !=null">
                            `lot_att08` = #{lotAtt08},
                        </if>
                        <if test="unitCode !=null">
                            `unit_code` = #{unitCode},
                        </if>
                        <if test="lotAtt07 !=null">
                            `lot_att07` = #{lotAtt07},
                        </if>
                        <if test="lotAtt06 !=null">
                            `lot_att06` = #{lotAtt06},
                        </if>
                        <if test="installFlag != null">
                            `install_flag` = #{installFlag},
                        </if>
                        <if test="lotAtt05 !=null">
                            `lot_att05` = #{lotAtt05},
                        </if>
                        <if test="itemSize !=null">
                            `item_size` = #{itemSize},
                        </if>
                        <if test="lotAtt03 !=null">
                            `lot_att03` = #{lotAtt03},
                        </if>
                        <if test="brand !=null">
                            `brand` = #{brand},
                        </if>
                        <if test="lotAtt02 !=null">
                            `lot_att02` = #{lotAtt02},
                        </if>
                        <if test="sourceItemName !=null">
                            `source_item_name` = #{sourceItemName},
                        </if>
                        <if test="produceTimestamp != null">
                            `produce_timestamp` = #{produceTimestamp},
                        </if>
                        <if test="orderNo !=null">
                            `order_no` = #{orderNo},
                        </if>
                        <if test="setFlag != null">
                            `set_flag` = #{setFlag},
                        </if>
                        <if test="upperItemStatusTo !=null">
                            `upper_item_status_to` = #{upperItemStatusTo},
                        </if>
                        <if test="amout != null">
                            `amout` = #{amout},
                        </if>
                        <if test="packingDecimalAmount != null">
                            `packing_decimal_amount` = #{packingDecimalAmount},
                        </if>
                        <if test="buCode !=null">
                            `bu_code` = #{buCode},
                        </if>
                        <if test="toCustomerItemCode !=null">
                            `to_customer_item_code` = #{toCustomerItemCode},
                        </if>
                        <if test="hikFlag !=null">
                            `hik_flag` = #{hikFlag},
                        </if>
                        <if test="itemChargingType !=null">
                            `item_charging_type` = #{itemChargingType},
                        </if>
                        <if test="volume != null">
                            `volume` = #{volume},
                        </if>
                        <if test="totalVolume != null">
                            `total_volume` = #{totalVolume},
                        </if>
                        <if test="totalGrossWeight != null">
                            `total_gross_weight` = #{totalGrossWeight},
                        </if>
                        <if test="materialGroup1 !=null">
                            `material_group1` = #{materialGroup1},
                        </if>
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="netWeight != null">
                            `net_weight` = #{netWeight},
                        </if>
                        <if test="unit !=null">
                            `unit` = #{unit},
                        </if>
                        <if test="materialGroup3 !=null">
                            `material_group3` = #{materialGroup3},
                        </if>
                        <if test="fictitiousFlag != null">
                            `fictitious_flag` = #{fictitiousFlag},
                        </if>
                        <if test="upperItemStatus !=null">
                            `upper_item_status` = #{upperItemStatus},
                        </if>
                        <if test="materialGroup2 !=null">
                            `material_group2` = #{materialGroup2},
                        </if>
                        <if test="materialGroup5 !=null">
                            `material_group5` = #{materialGroup5},
                        </if>
                        <if test="materialGroup4 !=null">
                            `material_group4` = #{materialGroup4},
                        </if>
                        <if test="upperLineNo !=null">
                            `upper_line_no` = #{upperLineNo},
                        </if>
                        <if test="materialGroup6 !=null">
                            `material_group6` = #{materialGroup6},
                        </if>
                        <if test="packingAmount != null">
                            `packing_amount` = #{packingAmount},
                        </if>
                        <if test="sourceItemCode !=null">
                            `source_item_code` = #{sourceItemCode},
                        </if>
                        <if test="sourceOrderNo !=null">
                            `source_order_no` = #{sourceOrderNo},
                        </if>
                        <if test="itemSpec !=null">
                            `item_spec` = #{itemSpec},
                        </if>
                        <if test="planQty != null">
                            `plan_qty` = #{planQty},
                        </if>
                        <if test="itemSuiteQty != null">
                            `item_suite_qty` = #{itemSuiteQty},
                        </if>
                        <if test="itemClassName !=null">
                            `item_class_name` = #{itemClassName},
                        </if>
                        <if test="upperItemId !=null">
                            `upper_item_id` = #{upperItemId},
                        </if>
                        <if test="serviceFlag !=null">
                            `service_flag` = #{serviceFlag},
                        </if>
                        <if test="finalPrice != null">
                            `final_price` = #{finalPrice},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="tenantCode !=null">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="parentLineNo != null">
                            `parent_line_no` = #{parentLineNo},
                        </if>
                        <if test="itemStatusTo !=null">
                            `item_status_to` = #{itemStatusTo},
                        </if>
                        <if test="newItemFlag !=null">
                            `new_item_flag` = #{newItemFlag},
                        </if>
                        <if test="marketModel !=null">
                            `market_model` = #{marketModel},
                        </if>
                        <if test="toItemCode !=null">
                            `to_item_code` = #{toItemCode},
                        </if>
                        <if test="toItemCode !=null">
                            `to_item_code` = #{toItemCode},
                        </if>
                        <if test="extendInfoFields !=null">
                            `extend_info_fields` = #{extendInfoFields},
                        </if>
                        <if test="produceCode !=null">
                            `produce_code` = #{produceCode},
                        </if>
                        <if test="lotCode !=null">
                            `lot_code` = #{lotCode},
                        </if>
                        <if test="itemSuiteCode !=null">
                            `item_suite_code` = #{itemSuiteCode},
                        </if>
                        <if test="itemStatus !=null">
                            `item_status` = #{itemStatus},
                        </if>
                        <if test="taskNo !=null">
                            `task_no` = #{taskNo},
                        </if>
                        <if test="singleWeight != null">
                            `single_weight` = #{singleWeight},
                        </if>
                        <if test="sourcePruduct !=null">
                            `source_pruduct` = #{sourcePruduct},
                        </if>
                        <if test="itemLineNo != null">
                            `item_line_no` = #{itemLineNo},
                        </if>
                        <if test="batchCode !=null">
                            `batch_code` = #{batchCode},
                        </if>
                        <if test="length != null">
                            `length` = #{length},
                        </if>
                        <if test="actQty != null">
                            `act_qty` = #{actQty},
                        </if>
                        <if test="grossWeight != null">
                            `gross_weight` = #{grossWeight},
                        </if>
                        <if test="expireTimestamp != null">
                            `expire_timestamp` = #{expireTimestamp},
                        </if>
                        <if test="financeNo !=null">
                            `finance_no` = #{financeNo},
                        </if>
                        <if test="itemClass !=null">
                            `item_class` = #{itemClass},
                        </if>
                        <if test="pkgQty != null">
                            `pkg_qty` = #{pkgQty},
                        </if>
                        <if test="width != null">
                            `width` = #{width},
                        </if>
                        <if test="height != null">
                            `height` = #{height},
                        </if>
                        <if test="extend1 !=null">
                            `extend1` = #{extend1},
                        </if>
                        <if test="notaxPrice != null">
                            `notax_price` = #{notaxPrice},
                        </if>
                        <if test="upperItemCode !=null">
                            `upper_item_code` = #{upperItemCode},
                        </if>
                        <if test="groupIndication !=null">
                            `group_indication` =#{groupIndication},
                        </if>
                        <if test="ifInstall !=null">
                            `if_install` =#{ifInstall},
                        </if>
                        <if test="mainItemCode !=null">
                            `main_item_code` =#{mainItemCode},
                        </if>
                        <if test="materialGroup7 !=null">
                            `material_group7` = #{materialGroup7},
                        </if>
                        <if test="materialGroup8 !=null">
                            `material_group8` = #{materialGroup8},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from task_item t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.TaskItem">
        select
        <include refid="searchFieldsSql"/>
        from task_item t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.TaskItem">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from task_item t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.TaskItem">
        select
        <include refid="searchFieldsSql"/>
        from task_item t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
${orderByType}            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="updateById">
        update
        task_item t
        <include refid="setFieldsSql"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        task_item t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        task_item t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.TaskItem" useGeneratedKeys="true" keyProperty="id">
        insert into task_item
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="productSpecification !=null and productSpecification != ''">
                        `product_specification`,
                    </if>

                    <if test="snCode !=null and snCode != ''">
                        `sn_code`,
                    </if>

                    <if test="itemCode !=null and itemCode != ''">
                        `item_code`,
                    </if>

                    <if test="lgOrderNo !=null and lgOrderNo != ''">
                        `lg_order_no`,
                    </if>

                    <if test="measureStandard !=null and measureStandard != ''">
                        `measure_standard`,
                    </if>

                    <if test="cancleQty != null">
                        `cancle_qty`,
                    </if>

                    <if test="isFirst != null">
                        `is_first`,
                    </if>

                    <if test="itemName !=null and itemName != ''">
                        `item_name`,
                    </if>

                    <if test="customerItemCode !=null and customerItemCode != ''">
                        `customer_item_code`,
                    </if>

                    <if test="specificationUnit !=null and specificationUnit != ''">
                        `specification_unit`,
                    </if>

                    <if test="singleVolume != null">
                        `single_volume`,
                    </if>

                    <if test="price != null">
                        `price`,
                    </if>

                    <if test="splitQty != null">
                        `split_qty`,
                    </if>

                    <if test="lotAtt08 !=null and lotAtt08 != ''">
                        `lot_att08`,
                    </if>

                    <if test="unitCode !=null and unitCode != ''">
                        `unit_code`,
                    </if>

                    <if test="lotAtt07 !=null and lotAtt07 != ''">
                        `lot_att07`,
                    </if>

                    <if test="lotAtt06 !=null and lotAtt06 != ''">
                        `lot_att06`,
                    </if>

                    <if test="installFlag != null">
                        `install_flag`,
                    </if>

                    <if test="lotAtt05 !=null and lotAtt05 != ''">
                        `lot_att05`,
                    </if>

                    <if test="itemSize !=null and itemSize != ''">
                        `item_size`,
                    </if>

                    <if test="lotAtt03 !=null and lotAtt03 != ''">
                        `lot_att03`,
                    </if>

                    <if test="brand !=null and brand != ''">
                        `brand`,
                    </if>

                    <if test="lotAtt02 !=null and lotAtt02 != ''">
                        `lot_att02`,
                    </if>

                    <if test="sourceItemName !=null and sourceItemName != ''">
                        `source_item_name`,
                    </if>

                    <if test="produceTimestamp != null">
                        `produce_timestamp`,
                    </if>

                    <if test="orderNo !=null and orderNo != ''">
                        `order_no`,
                    </if>

                    <if test="setFlag != null">
                        `set_flag`,
                    </if>

                    <if test="upperItemStatusTo !=null and upperItemStatusTo != ''">
                        `upper_item_status_to`,
                    </if>

                    <if test="amout != null">
                        `amout`,
                    </if>

                    <if test="packingDecimalAmount != null">
                        `packing_decimal_amount`,
                    </if>

                    <if test="buCode !=null and buCode != ''">
                        `bu_code`,
                    </if>

                    <if test="toCustomerItemCode !=null and toCustomerItemCode != ''">
                        `to_customer_item_code`,
                    </if>

                    <if test="hikFlag !=null and hikFlag != ''">
                        `hik_flag`,
                    </if>

                    <if test="itemChargingType !=null and itemChargingType != ''">
                        `item_charging_type`,
                    </if>

                    <if test="volume != null">
                        `volume`,
                    </if>

                    <if test="totalVolume != null">
                        `total_volume`,
                    </if>

                    <if test="totalGrossWeight != null">
                        `total_gross_weight`,
                    </if>

                    <if test="materialGroup1 !=null and materialGroup1 != ''">
                        `material_group1`,
                    </if>

                    <if test="updateUserCode != null">
                        `update_user_code`,
                    </if>

                    <if test="netWeight != null">
                        `net_weight`,
                    </if>

                    <if test="unit !=null and unit != ''">
                        `unit`,
                    </if>

                    <if test="materialGroup3 !=null and materialGroup3 != ''">
                        `material_group3`,
                    </if>

                    <if test="fictitiousFlag != null">
                        `fictitious_flag`,
                    </if>

                    <if test="upperItemStatus !=null and upperItemStatus != ''">
                        `upper_item_status`,
                    </if>

                    <if test="materialGroup2 !=null and materialGroup2 != ''">
                        `material_group2`,
                    </if>

                    <if test="materialGroup5 !=null and materialGroup5 != ''">
                        `material_group5`,
                    </if>

                    <if test="materialGroup4 !=null and materialGroup4 != ''">
                        `material_group4`,
                    </if>

                    <if test="upperLineNo !=null and upperLineNo != ''">
                        `upper_line_no`,
                    </if>

                    <if test="materialGroup6 !=null and materialGroup6 != ''">
                        `material_group6`,
                    </if>

                    <if test="packingAmount != null">
                        `packing_amount`,
                    </if>

                    <if test="sourceItemCode !=null and sourceItemCode != ''">
                        `source_item_code`,
                    </if>

                    <if test="sourceOrderNo !=null and sourceOrderNo != ''">
                        `source_order_no`,
                    </if>

                    <if test="itemSpec !=null and itemSpec != ''">
                        `item_spec`,
                    </if>

                    <if test="planQty != null">
                        `plan_qty`,
                    </if>

                    <if test="itemSuiteQty != null">
                        `item_suite_qty`,
                    </if>

                    <if test="itemClassName !=null and itemClassName != ''">
                        `item_class_name`,
                    </if>

                    <if test="upperItemId !=null and upperItemId != ''">
                        `upper_item_id`,
                    </if>

                    <if test="serviceFlag !=null and serviceFlag != ''">
                        `service_flag`,
                    </if>

                    <if test="finalPrice != null">
                        `final_price`,
                    </if>

                    <if test="remark != null">
                        `remark`,
                    </if>

                    <if test="tenantCode !=null and tenantCode != ''">
                        `tenant_code`,
                    </if>

                    <if test="parentLineNo != null">
                        `parent_line_no`,
                    </if>

                    <if test="itemStatusTo !=null and itemStatusTo != ''">
                        `item_status_to`,
                    </if>

                    <if test="newItemFlag !=null and newItemFlag != ''">
                        `new_item_flag`,
                    </if>

                    <if test="marketModel !=null and marketModel != ''">
                        `market_model`,
                    </if>

                    <if test="toItemCode !=null and toItemCode != ''">
                        `to_item_code`,
                    </if>

                    <if test="extendInfoFields !=null and extendInfoFields != ''">
                        `extend_info_fields`,
                    </if>

                    <if test="produceCode !=null and produceCode != ''">
                        `produce_code`,
                    </if>

                    <if test="lotCode !=null and lotCode != ''">
                        `lot_code`,
                    </if>

                    <if test="itemSuiteCode !=null and itemSuiteCode != ''">
                        `item_suite_code`,
                    </if>

                    <if test="itemStatus !=null and itemStatus != ''">
                        `item_status`,
                    </if>

                    <if test="taskNo !=null and taskNo != ''">
                        `task_no`,
                    </if>

                    <if test="singleWeight != null">
                        `single_weight`,
                    </if>

                    <if test="sourcePruduct !=null and sourcePruduct != ''">
                        `source_pruduct`,
                    </if>

                    <if test="itemLineNo != null">
                        `item_line_no`,
                    </if>

                    <if test="createUserCode != null">
                        `create_user_code`,
                    </if>

                    <if test="batchCode !=null and batchCode != ''">
                        `batch_code`,
                    </if>

                    <if test="length != null">
                        `length`,
                    </if>

                    <if test="actQty != null">
                        `act_qty`,
                    </if>

                    <if test="grossWeight != null">
                        `gross_weight`,
                    </if>

                    <if test="expireTimestamp != null">
                        `expire_timestamp`,
                    </if>

                    <if test="financeNo !=null and financeNo != ''">
                        `finance_no`,
                    </if>

                    <if test="itemClass !=null and itemClass != ''">
                        `item_class`,
                    </if>

                    <if test="pkgQty != null">
                        `pkg_qty`,
                    </if>

                    <if test="width != null">
                        `width`,
                    </if>

                    <if test="height != null">
                        `height`,
                    </if>

                    <if test="extend1 !=null and extend1 != ''">
                        `extend1`,
                    </if>

                    <if test="notaxPrice != null">
                        `notax_price`,
                    </if>

                    <if test="upperItemCode !=null and upperItemCode != ''">
                        `upper_item_code`,
                    </if>

                    <if test="groupIndication !=null and groupIndication != ''">
                        `group_indication`,
                    </if>

                    <if test="ifInstall !=null and ifInstall != ''">
                        `if_install`,
                    </if>
                    <if test="mainItemCode !=null and mainItemCode != ''">
                        `main_item_code`,
                    </if>
                    <if test="materialGroup7 !=null and materialGroup7 != ''">
                        `material_group7`,
                    </if>
                    <if test="materialGroup8 !=null and materialGroup8 != ''">
                        `material_group8`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="productSpecification !=null and productSpecification != ''">
                        #{productSpecification},
                    </if>
                    <if test="snCode !=null and snCode != ''">
                        #{snCode},
                    </if>
                    <if test="itemCode !=null and itemCode != ''">
                        #{itemCode},
                    </if>
                    <if test="lgOrderNo !=null and lgOrderNo != ''">
                        #{lgOrderNo},
                    </if>
                    <if test="measureStandard !=null and measureStandard != ''">
                        #{measureStandard},
                    </if>
                    <if test="cancleQty != null">
                        #{cancleQty},
                    </if>
                    <if test="isFirst != null">
                        #{isFirst},
                    </if>
                    <if test="itemName !=null and itemName != ''">
                        #{itemName},
                    </if>
                    <if test="customerItemCode !=null and customerItemCode != ''">
                        #{customerItemCode},
                    </if>
                    <if test="specificationUnit !=null and specificationUnit != ''">
                        #{specificationUnit},
                    </if>
                    <if test="singleVolume != null">
                        #{singleVolume},
                    </if>
                    <if test="price != null">
                        #{price},
                    </if>
                    <if test="splitQty != null">
                        #{splitQty},
                    </if>
                    <if test="lotAtt08 !=null and lotAtt08 != ''">
                        #{lotAtt08},
                    </if>
                    <if test="unitCode !=null and unitCode != ''">
                        #{unitCode},
                    </if>
                    <if test="lotAtt07 !=null and lotAtt07 != ''">
                        #{lotAtt07},
                    </if>
                    <if test="lotAtt06 !=null and lotAtt06 != ''">
                        #{lotAtt06},
                    </if>
                    <if test="installFlag != null">
                        #{installFlag},
                    </if>
                    <if test="lotAtt05 !=null and lotAtt05 != ''">
                        #{lotAtt05},
                    </if>
                    <if test="itemSize !=null and itemSize != ''">
                        #{itemSize},
                    </if>
                    <if test="lotAtt03 !=null and lotAtt03 != ''">
                        #{lotAtt03},
                    </if>
                    <if test="brand !=null and brand != ''">
                        #{brand},
                    </if>
                    <if test="lotAtt02 !=null and lotAtt02 != ''">
                        #{lotAtt02},
                    </if>
                    <if test="sourceItemName !=null and sourceItemName != ''">
                        #{sourceItemName},
                    </if>
                    <if test="produceTimestamp != null">
                        #{produceTimestamp},
                    </if>
                    <if test="orderNo !=null and orderNo != ''">
                        #{orderNo},
                    </if>
                    <if test="setFlag != null">
                        #{setFlag},
                    </if>
                    <if test="upperItemStatusTo !=null and upperItemStatusTo != ''">
                        #{upperItemStatusTo},
                    </if>
                    <if test="amout != null">
                        #{amout},
                    </if>
                    <if test="packingDecimalAmount != null">
                        #{packingDecimalAmount},
                    </if>
                    <if test="buCode !=null and buCode != ''">
                        #{buCode},
                    </if>
                    <if test="toCustomerItemCode !=null and toCustomerItemCode != ''">
                        #{toCustomerItemCode},
                    </if>
                    <if test="hikFlag !=null and hikFlag != ''">
                        #{hikFlag},
                    </if>
                    <if test="itemChargingType !=null and itemChargingType != ''">
                        #{itemChargingType},
                    </if>
                    <if test="volume != null">
                        #{volume},
                    </if>
                    <if test="totalVolume != null">
                        #{totalVolume},
                    </if>
                    <if test="totalGrossWeight != null">
                        #{totalGrossWeight},
                    </if>
                    <if test="materialGroup1 !=null and materialGroup1 != ''">
                        #{materialGroup1},
                    </if>
                    <if test="updateUserCode != null">
                        #{updateUserCode},
                    </if>
                    <if test="netWeight != null">
                        #{netWeight},
                    </if>
                    <if test="unit !=null and unit != ''">
                        #{unit},
                    </if>
                    <if test="materialGroup3 !=null and materialGroup3 != ''">
                        #{materialGroup3},
                    </if>
                    <if test="fictitiousFlag != null">
                        #{fictitiousFlag},
                    </if>
                    <if test="upperItemStatus !=null and upperItemStatus != ''">
                        #{upperItemStatus},
                    </if>
                    <if test="materialGroup2 !=null and materialGroup2 != ''">
                        #{materialGroup2},
                    </if>
                    <if test="materialGroup5 !=null and materialGroup5 != ''">
                        #{materialGroup5},
                    </if>
                    <if test="materialGroup4 !=null and materialGroup4 != ''">
                        #{materialGroup4},
                    </if>
                    <if test="upperLineNo !=null and upperLineNo != ''">
                        #{upperLineNo},
                    </if>
                    <if test="materialGroup6 !=null and materialGroup6 != ''">
                        #{materialGroup6},
                    </if>
                    <if test="packingAmount != null">
                        #{packingAmount},
                    </if>
                    <if test="sourceItemCode !=null and sourceItemCode != ''">
                        #{sourceItemCode},
                    </if>
                    <if test="sourceOrderNo !=null and sourceOrderNo != ''">
                        #{sourceOrderNo},
                    </if>
                    <if test="itemSpec !=null and itemSpec != ''">
                        #{itemSpec},
                    </if>
                    <if test="planQty != null">
                        #{planQty},
                    </if>
                    <if test="itemSuiteQty != null">
                        #{itemSuiteQty},
                    </if>
                    <if test="itemClassName !=null and itemClassName != ''">
                        #{itemClassName},
                    </if>
                    <if test="upperItemId !=null and upperItemId != ''">
                        #{upperItemId},
                    </if>
                    <if test="serviceFlag !=null and serviceFlag != ''">
                        #{serviceFlag},
                    </if>
                    <if test="finalPrice != null">
                        #{finalPrice},
                    </if>
                    <if test="remark != null">
                        #{remark},
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        #{tenantCode},
                    </if>
                    <if test="parentLineNo != null">
                        #{parentLineNo},
                    </if>
                    <if test="itemStatusTo !=null and itemStatusTo != ''">
                        #{itemStatusTo},
                    </if>
                    <if test="newItemFlag !=null and newItemFlag != ''">
                        #{newItemFlag},
                    </if>
                    <if test="marketModel !=null and marketModel != ''">
                        #{marketModel},
                    </if>
                    <if test="toItemCode !=null and toItemCode != ''">
                        #{toItemCode},
                    </if>
                    <if test="extendInfoFields !=null and extendInfoFields != ''">
                        #{extendInfoFields},
                    </if>
                    <if test="produceCode !=null and produceCode != ''">
                        #{produceCode},
                    </if>
                    <if test="lotCode !=null and lotCode != ''">
                        #{lotCode},
                    </if>
                    <if test="itemSuiteCode !=null and itemSuiteCode != ''">
                        #{itemSuiteCode},
                    </if>
                    <if test="itemStatus !=null and itemStatus != ''">
                        #{itemStatus},
                    </if>
                    <if test="taskNo !=null and taskNo != ''">
                        #{taskNo},
                    </if>
                    <if test="singleWeight != null">
                        #{singleWeight},
                    </if>
                    <if test="sourcePruduct !=null and sourcePruduct != ''">
                        #{sourcePruduct},
                    </if>
                    <if test="itemLineNo != null">
                        #{itemLineNo},
                    </if>
                    <if test="createUserCode != null">
                        #{createUserCode},
                    </if>
                    <if test="batchCode !=null and batchCode != ''">
                        #{batchCode},
                    </if>
                    <if test="length != null">
                        #{length},
                    </if>
                    <if test="actQty != null">
                        #{actQty},
                    </if>
                    <if test="grossWeight != null">
                        #{grossWeight},
                    </if>
                    <if test="expireTimestamp != null">
                        #{expireTimestamp},
                    </if>
                    <if test="financeNo !=null and financeNo != ''">
                        #{financeNo},
                    </if>
                    <if test="itemClass !=null and itemClass != ''">
                        #{itemClass},
                    </if>
                    <if test="pkgQty != null">
                        #{pkgQty},
                    </if>
                    <if test="width != null">
                        #{width},
                    </if>
                    <if test="height != null">
                        #{height},
                    </if>
                    <if test="extend1 !=null and extend1 != ''">
                        #{extend1},
                    </if>
                    <if test="notaxPrice != null">
                        #{notaxPrice},
                    </if>
                    <if test="upperItemCode !=null and upperItemCode != ''">
                        #{upperItemCode},
                    </if>
                    <if test="groupIndication !=null and groupIndication != ''">
                        #{groupIndication},
                    </if>
                    <if test="ifInstall !=null and ifInstall != ''">
                        #{ifInstall},
                    </if>
                    <if test="mainItemCode !=null and mainItemCode != ''">
                        #{mainItemCode},
                    </if>
                    <if test="materialGroup7 !=null and materialGroup7 != ''">
                        #{materialGroup7},
                    </if>
                    <if test="materialGroup8 !=null and materialGroup8 != ''">
                        #{materialGroup8},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                `product_specification`,
                `sn_code`,
                `item_code`,
                `lg_order_no`,
                `measure_standard`,
                `cancle_qty`,
                `is_first`,
                `item_name`,
                `customer_item_code`,
                `specification_unit`,
                `single_volume`,
                `price`,
                `split_qty`,
                `lot_att08`,
                `unit_code`,
                `lot_att07`,
                `lot_att06`,
                `install_flag`,
                `lot_att05`,
                `item_size`,
                `lot_att03`,
                `brand`,
                `lot_att02`,
                `source_item_name`,
                `produce_timestamp`,
                `order_no`,
                `set_flag`,
                `upper_item_status_to`,
                `amout`,
                `packing_decimal_amount`,
                `bu_code`,
                `to_customer_item_code`,
                `hik_flag`,
                `item_charging_type`,
                `volume`,
                `total_volume`,
                `total_gross_weight`,
                `material_group1`,
                `update_user_code`,
                `net_weight`,
                `unit`,
                `material_group3`,
                `fictitious_flag`,
                `upper_item_status`,
                `material_group2`,
                `material_group5`,
                `material_group4`,
                `upper_line_no`,
                `material_group6`,
                `packing_amount`,
                `source_item_code`,
                `source_order_no`,
                `item_spec`,
                `plan_qty`,
                `item_suite_qty`,
                `item_class_name`,
                `upper_item_id`,
                `service_flag`,
                `final_price`,
                `remark`,
                `tenant_code`,
                `parent_line_no`,
                `item_status_to`,
                `new_item_flag`,
                `market_model`,
                `to_item_code`,
                `extend_info_fields`,
                `produce_code`,
                `lot_code`,
                `item_suite_code`,
                `item_status`,
                `task_no`,
                `single_weight`,
                `source_pruduct`,
                `item_line_no`,
                `create_user_code`,
                `batch_code`,
                `length`,
                `act_qty`,
                `gross_weight`,
                `expire_timestamp`,
                `finance_no`,
                `item_class`,
                `pkg_qty`,
                `width`,
                `height`,
                `extend1`,
                `notax_price`,
                `upper_item_code`,
                `group_indication`,
                `if_install`,
                `main_item_code`,
                `material_group7`,
                `material_group8`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.productSpecification},
                #{item.snCode},
                #{item.itemCode},
                #{item.lgOrderNo},
                #{item.measureStandard},
                #{item.cancleQty},
                #{item.isFirst},
                #{item.itemName},
                #{item.customerItemCode},
                #{item.specificationUnit},
                #{item.singleVolume},
                #{item.price},
                #{item.splitQty},
                #{item.lotAtt08},
                #{item.unitCode},
                #{item.lotAtt07},
                #{item.lotAtt06},
                #{item.installFlag},
                #{item.lotAtt05},
                #{item.itemSize},
                #{item.lotAtt03},
                #{item.brand},
                #{item.lotAtt02},
                #{item.sourceItemName},
                #{item.produceTimestamp},
                #{item.orderNo},
                #{item.setFlag},
                #{item.upperItemStatusTo},
                #{item.amout},
                #{item.packingDecimalAmount},
                #{item.buCode},
                #{item.toCustomerItemCode},
                #{item.hikFlag},
                #{item.itemChargingType},
                #{item.volume},
                #{item.totalVolume},
                #{item.totalGrossWeight},
                #{item.materialGroup1},
                #{item.updateUserCode},
                #{item.netWeight},
                #{item.unit},
                #{item.materialGroup3},
                #{item.fictitiousFlag},
                #{item.upperItemStatus},
                #{item.materialGroup2},
                #{item.materialGroup5},
                #{item.materialGroup4},
                #{item.upperLineNo},
                #{item.materialGroup6},
                #{item.packingAmount},
                #{item.sourceItemCode},
                #{item.sourceOrderNo},
                #{item.itemSpec},
                #{item.planQty},
                #{item.itemSuiteQty},
                #{item.itemClassName},
                #{item.upperItemId},
                #{item.serviceFlag},
                #{item.finalPrice},
                #{item.remark},
                #{item.tenantCode},
                #{item.parentLineNo},
                #{item.itemStatusTo},
                #{item.newItemFlag},
                #{item.marketModel},
                #{item.toItemCode},
                #{item.extendInfoFields},
                #{item.produceCode},
                #{item.lotCode},
                #{item.itemSuiteCode},
                #{item.itemStatus},
                #{item.taskNo},
                #{item.singleWeight},
                #{item.sourcePruduct},
                #{item.itemLineNo},
                #{item.createUserCode},
                #{item.batchCode},
                #{item.length},
                #{item.actQty},
                #{item.grossWeight},
                #{item.expireTimestamp},
                #{item.financeNo},
                #{item.itemClass},
                #{item.pkgQty},
                #{item.width},
                #{item.height},
                #{item.extend1},
                #{item.notaxPrice},
                #{item.upperItemCode},
                #{item.groupIndication},
                #{item.ifInstall},
                #{item.mainItemCode},
                #{item.materialGroup7},
                #{item.materialGroup8},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        task_item
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.productSpecification !=null and item.productSpecification != ''">
                     `product_specification`  = #{item.productSpecification},
                 </if>
                 <if test="item.snCode !=null and item.snCode != ''">
                     `sn_code`  = #{item.snCode},
                 </if>
                 <if test="item.itemCode !=null and item.itemCode != ''">
                     `item_code`  = #{item.itemCode},
                 </if>
                 <if test="item.lgOrderNo !=null and item.lgOrderNo != ''">
                     `lg_order_no`  = #{item.lgOrderNo},
                 </if>
                 <if test="item.measureStandard !=null and item.measureStandard != ''">
                     `measure_standard`  = #{item.measureStandard},
                 </if>
                 <if test="item.cancleQty != null">
                     `cancle_qty`  = #{item.cancleQty},
                 </if>
                 <if test="item.isFirst != null">
                     `is_first`  = #{item.isFirst},
                 </if>
                 <if test="item.itemName !=null and item.itemName != ''">
                     `item_name`  = #{item.itemName},
                 </if>
                 <if test="item.customerItemCode !=null and item.customerItemCode != ''">
                     `customer_item_code`  = #{item.customerItemCode},
                 </if>
                 <if test="item.specificationUnit !=null and item.specificationUnit != ''">
                     `specification_unit`  = #{item.specificationUnit},
                 </if>
                 <if test="item.singleVolume != null">
                     `single_volume`  = #{item.singleVolume},
                 </if>
                 <if test="item.price != null">
                     `price`  = #{item.price},
                 </if>
                 <if test="item.splitQty != null">
                     `split_qty`  = #{item.splitQty},
                 </if>
                 <if test="item.lotAtt08 !=null and item.lotAtt08 != ''">
                     `lot_att08`  = #{item.lotAtt08},
                 </if>
                 <if test="item.unitCode !=null and item.unitCode != ''">
                     `unit_code`  = #{item.unitCode},
                 </if>
                 <if test="item.lotAtt07 !=null and item.lotAtt07 != ''">
                     `lot_att07`  = #{item.lotAtt07},
                 </if>
                 <if test="item.lotAtt06 !=null and item.lotAtt06 != ''">
                     `lot_att06`  = #{item.lotAtt06},
                 </if>
                 <if test="item.installFlag != null">
                     `install_flag`  = #{item.installFlag},
                 </if>
                 <if test="item.lotAtt05 !=null and item.lotAtt05 != ''">
                     `lot_att05`  = #{item.lotAtt05},
                 </if>
                 <if test="item.itemSize !=null and item.itemSize != ''">
                     `item_size`  = #{item.itemSize},
                 </if>
                 <if test="item.lotAtt03 !=null and item.lotAtt03 != ''">
                     `lot_att03`  = #{item.lotAtt03},
                 </if>
                 <if test="item.brand !=null and item.brand != ''">
                     `brand`  = #{item.brand},
                 </if>
                 <if test="item.lotAtt02 !=null and item.lotAtt02 != ''">
                     `lot_att02`  = #{item.lotAtt02},
                 </if>
                 <if test="item.sourceItemName !=null and item.sourceItemName != ''">
                     `source_item_name`  = #{item.sourceItemName},
                 </if>
                 <if test="item.produceTimestamp != null">
                     `produce_timestamp`  = #{item.produceTimestamp},
                 </if>
                 <if test="item.orderNo !=null and item.orderNo != ''">
                     `order_no`  = #{item.orderNo},
                 </if>
                 <if test="item.setFlag != null">
                     `set_flag`  = #{item.setFlag},
                 </if>
                 <if test="item.upperItemStatusTo !=null and item.upperItemStatusTo != ''">
                     `upper_item_status_to`  = #{item.upperItemStatusTo},
                 </if>
                 <if test="item.amout != null">
                     `amout`  = #{item.amout},
                 </if>
                 <if test="item.packingDecimalAmount != null">
                     `packing_decimal_amount`  = #{item.packingDecimalAmount},
                 </if>
                 <if test="item.buCode !=null and item.buCode != ''">
                     `bu_code`  = #{item.buCode},
                 </if>
                 <if test="item.toCustomerItemCode !=null and item.toCustomerItemCode != ''">
                     `to_customer_item_code`  = #{item.toCustomerItemCode},
                 </if>
                 <if test="item.hikFlag !=null and item.hikFlag != ''">
                     `hik_flag`  = #{item.hikFlag},
                 </if>
                 <if test="item.itemChargingType !=null and item.itemChargingType != ''">
                     `item_charging_type`  = #{item.itemChargingType},
                 </if>
                 <if test="item.volume != null">
                     `volume`  = #{item.volume},
                 </if>
                <if test="item.totalVolume != null">
                    `total_volume`  = #{item.totalVolume},
                </if>
                <if test="item.totalGrossWeight != null">
                    `total_gross_weight`  = #{item.totalGrossWeight},
                </if>
                 <if test="item.materialGroup1 !=null and item.materialGroup1 != ''">
                     `material_group1`  = #{item.materialGroup1},
                 </if>
                 <if test="item.updateUserCode != null">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.netWeight != null">
                     `net_weight`  = #{item.netWeight},
                 </if>
                 <if test="item.unit !=null and item.unit != ''">
                     `unit`  = #{item.unit},
                 </if>
                 <if test="item.materialGroup3 !=null and item.materialGroup3 != ''">
                     `material_group3`  = #{item.materialGroup3},
                 </if>
                 <if test="item.fictitiousFlag != null">
                     `fictitious_flag`  = #{item.fictitiousFlag},
                 </if>
                 <if test="item.upperItemStatus !=null and item.upperItemStatus != ''">
                     `upper_item_status`  = #{item.upperItemStatus},
                 </if>
                 <if test="item.materialGroup2 !=null and item.materialGroup2 != ''">
                     `material_group2`  = #{item.materialGroup2},
                 </if>
                 <if test="item.materialGroup5 !=null and item.materialGroup5 != ''">
                     `material_group5`  = #{item.materialGroup5},
                 </if>
                 <if test="item.materialGroup4 !=null and item.materialGroup4 != ''">
                     `material_group4`  = #{item.materialGroup4},
                 </if>
                 <if test="item.upperLineNo !=null and item.upperLineNo != ''">
                     `upper_line_no`  = #{item.upperLineNo},
                 </if>
                 <if test="item.materialGroup6 !=null and item.materialGroup6 != ''">
                     `material_group6`  = #{item.materialGroup6},
                 </if>
                 <if test="item.packingAmount != null">
                     `packing_amount`  = #{item.packingAmount},
                 </if>
                 <if test="item.sourceItemCode !=null and item.sourceItemCode != ''">
                     `source_item_code`  = #{item.sourceItemCode},
                 </if>
                 <if test="item.sourceOrderNo !=null and item.sourceOrderNo != ''">
                     `source_order_no`  = #{item.sourceOrderNo},
                 </if>
                 <if test="item.itemSpec !=null and item.itemSpec != ''">
                     `item_spec`  = #{item.itemSpec},
                 </if>
                 <if test="item.planQty != null">
                     `plan_qty`  = #{item.planQty},
                 </if>
                 <if test="item.itemSuiteQty != null">
                     `item_suite_qty`  = #{item.itemSuiteQty},
                 </if>
                 <if test="item.itemClassName !=null and item.itemClassName != ''">
                     `item_class_name`  = #{item.itemClassName},
                 </if>
                 <if test="item.upperItemId !=null and item.upperItemId != ''">
                     `upper_item_id`  = #{item.upperItemId},
                 </if>
                 <if test="item.serviceFlag !=null and item.serviceFlag != ''">
                     `service_flag`  = #{item.serviceFlag},
                 </if>
                 <if test="item.finalPrice != null">
                     `final_price`  = #{item.finalPrice},
                 </if>
                 <if test="item.remark != null">
                     `remark`  = #{item.remark},
                 </if>
                 <if test="item.tenantCode !=null and item.tenantCode != ''">
                     `tenant_code`  = #{item.tenantCode},
                 </if>
                 <if test="item.parentLineNo != null">
                     `parent_line_no`  = #{item.parentLineNo},
                 </if>
                 <if test="item.itemStatusTo !=null and item.itemStatusTo != ''">
                     `item_status_to`  = #{item.itemStatusTo},
                 </if>
                 <if test="item.newItemFlag !=null and item.newItemFlag != ''">
                     `new_item_flag`  = #{item.newItemFlag},
                 </if>
                 <if test="item.marketModel !=null and item.marketModel != ''">
                     `market_model`  = #{item.marketModel},
                 </if>
                 <if test="item.toItemCode !=null and item.toItemCode != ''">
                     `to_item_code`  = #{item.toItemCode},
                 </if>
                 <if test="item.extendInfoFields !=null and item.extendInfoFields != ''">
                     `extend_info_fields`  = #{item.extendInfoFields},
                 </if>
                 <if test="item.produceCode !=null and item.produceCode != ''">
                     `produce_code`  = #{item.produceCode},
                 </if>
                 <if test="item.lotCode !=null and item.lotCode != ''">
                     `lot_code`  = #{item.lotCode},
                 </if>
                 <if test="item.itemSuiteCode !=null and item.itemSuiteCode != ''">
                     `item_suite_code`  = #{item.itemSuiteCode},
                 </if>
                 <if test="item.itemStatus !=null and item.itemStatus != ''">
                     `item_status`  = #{item.itemStatus},
                 </if>
                 <if test="item.taskNo !=null and item.taskNo != ''">
                     `task_no`  = #{item.taskNo},
                 </if>
                 <if test="item.singleWeight != null">
                     `single_weight`  = #{item.singleWeight},
                 </if>
                 <if test="item.sourcePruduct !=null and item.sourcePruduct != ''">
                     `source_pruduct`  = #{item.sourcePruduct},
                 </if>
                 <if test="item.itemLineNo != null">
                     `item_line_no`  = #{item.itemLineNo},
                 </if>
                 <if test="item.batchCode !=null and item.batchCode != ''">
                     `batch_code`  = #{item.batchCode},
                 </if>
                 <if test="item.length != null">
                     `length`  = #{item.length},
                 </if>
                 <if test="item.actQty != null">
                     `act_qty`  = #{item.actQty},
                 </if>
                 <if test="item.grossWeight != null">
                     `gross_weight`  = #{item.grossWeight},
                 </if>
                 <if test="item.expireTimestamp != null">
                     `expire_timestamp`  = #{item.expireTimestamp},
                 </if>
                 <if test="item.financeNo !=null and item.financeNo != ''">
                     `finance_no`  = #{item.financeNo},
                 </if>
                 <if test="item.itemClass !=null and item.itemClass != ''">
                     `item_class`  = #{item.itemClass},
                 </if>
                 <if test="item.pkgQty != null">
                     `pkg_qty`  = #{item.pkgQty},
                 </if>
                 <if test="item.width != null">
                     `width`  = #{item.width},
                 </if>
                 <if test="item.height != null">
                     `height`  = #{item.height},
                 </if>
                 <if test="item.extend1 !=null and item.extend1 != ''">
                     `extend1`  = #{item.extend1},
                 </if>
                 <if test="item.notaxPrice != null">
                     `notax_price`  = #{item.notaxPrice},
                 </if>
                 <if test="item.upperItemCode !=null and item.upperItemCode != ''">
                     `upper_item_code`  = #{item.upperItemCode},
                 </if>
                 <if test="item.groupIndication !=null and item.groupIndication != ''">
                     `group_indication`  = #{item.groupIndication},
                 </if>
                 <if test="item.ifInstall !=null and item.ifInstall != ''">
                     `if_install`  = #{item.ifInstall},
                 </if>
                 <if test="item.mainItemCode !=null and item.mainItemCode != ''">
                     `main_item_code`  = #{item.mainItemCode},
                 </if>
                 <if test="item.materialGroup7 !=null and item.materialGroup7 != ''">
                     `material_group7`  = #{item.materialGroup7},
                 </if>
                 <if test="item.materialGroup8 !=null and item.materialGroup8 != ''">
                     `material_group8`  = #{item.materialGroup8},
                 </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.productSpecification !=null and item.productSpecification != ''">
                        and  `product_specification`  =#{item.productSpecification}
                    </if>
                    <if test="item.snCode !=null and item.snCode != ''">
                        and  `sn_code`  =#{item.snCode}
                    </if>
                    <if test="item.itemCode !=null and item.itemCode != ''">
                        and  `item_code`  =#{item.itemCode}
                    </if>
                    <if test="item.lgOrderNo !=null and item.lgOrderNo != ''">
                        and  `lg_order_no`  =#{item.lgOrderNo}
                    </if>
                    <if test="item.measureStandard !=null and item.measureStandard != ''">
                        and  `measure_standard`  =#{item.measureStandard}
                    </if>
                    <if test="item.cancleQty !=null">
                        and  `cancle_qty`  =#{item.cancleQty}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.isFirst !=null">
                        and  `is_first`  =#{item.isFirst}
                    </if>
                    <if test="item.itemName !=null and item.itemName != ''">
                        and  `item_name`  =#{item.itemName}
                    </if>
                    <if test="item.customerItemCode !=null and item.customerItemCode != ''">
                        and  `customer_item_code`  =#{item.customerItemCode}
                    </if>
                    <if test="item.specificationUnit !=null and item.specificationUnit != ''">
                        and  `specification_unit`  =#{item.specificationUnit}
                    </if>
                    <if test="item.singleVolume !=null">
                        and  `single_volume`  =#{item.singleVolume}
                    </if>
                    <if test="item.price !=null">
                        and  `price`  =#{item.price}
                    </if>
                    <if test="item.splitQty !=null">
                        and  `split_qty`  =#{item.splitQty}
                    </if>
                    <if test="item.lotAtt08 !=null and item.lotAtt08 != ''">
                        and  `lot_att08`  =#{item.lotAtt08}
                    </if>
                    <if test="item.unitCode !=null and item.unitCode != ''">
                        and  `unit_code`  =#{item.unitCode}
                    </if>
                    <if test="item.lotAtt07 !=null and item.lotAtt07 != ''">
                        and  `lot_att07`  =#{item.lotAtt07}
                    </if>
                    <if test="item.lotAtt06 !=null and item.lotAtt06 != ''">
                        and  `lot_att06`  =#{item.lotAtt06}
                    </if>
                    <if test="item.installFlag !=null">
                        and  `install_flag`  =#{item.installFlag}
                    </if>
                    <if test="item.lotAtt05 !=null and item.lotAtt05 != ''">
                        and  `lot_att05`  =#{item.lotAtt05}
                    </if>
                    <if test="item.itemSize !=null and item.itemSize != ''">
                        and  `item_size`  =#{item.itemSize}
                    </if>
                    <if test="item.lotAtt03 !=null and item.lotAtt03 != ''">
                        and  `lot_att03`  =#{item.lotAtt03}
                    </if>
                    <if test="item.brand !=null and item.brand != ''">
                        and  `brand`  =#{item.brand}
                    </if>
                    <if test="item.lotAtt02 !=null and item.lotAtt02 != ''">
                        and  `lot_att02`  =#{item.lotAtt02}
                    </if>
                    <if test="item.sourceItemName !=null and item.sourceItemName != ''">
                        and  `source_item_name`  =#{item.sourceItemName}
                    </if>
                    <if test="item.produceTimestamp !=null">
                        and  `produce_timestamp`  =#{item.produceTimestamp}
                    </if>
                    <if test="item.orderNo !=null and item.orderNo != ''">
                        and  `order_no`  =#{item.orderNo}
                    </if>
                    <if test="item.setFlag !=null">
                        and  `set_flag`  =#{item.setFlag}
                    </if>
                    <if test="item.upperItemStatusTo !=null and item.upperItemStatusTo != ''">
                        and  `upper_item_status_to`  =#{item.upperItemStatusTo}
                    </if>
                    <if test="item.amout !=null">
                        and  `amout`  =#{item.amout}
                    </if>
                    <if test="item.packingDecimalAmount !=null">
                        and  `packing_decimal_amount`  =#{item.packingDecimalAmount}
                    </if>
                    <if test="item.buCode !=null and item.buCode != ''">
                        and  `bu_code`  =#{item.buCode}
                    </if>
                    <if test="item.toCustomerItemCode !=null and item.toCustomerItemCode != ''">
                        and  `to_customer_item_code`  =#{item.toCustomerItemCode}
                    </if>
                    <if test="item.hikFlag !=null and item.hikFlag != ''">
                        and  `hik_flag`  =#{item.hikFlag}
                    </if>
                    <if test="item.version !=null">
                        and  `version`  =#{item.version}
                    </if>
                    <if test="item.itemChargingType !=null and item.itemChargingType != ''">
                        and  `item_charging_type`  =#{item.itemChargingType}
                    </if>
                    <if test="item.volume !=null">
                        and  `volume`  =#{item.volume}
                    </if>
                    <if test="item.totalVolume !=null">
                        and  `total_volume`  =#{item.totalVolume}
                    </if>
                    <if test="item.totalGrossWeight !=null">
                        and  `total_gross_weight`  =#{item.totalGrossWeight}
                    </if>
                    <if test="item.materialGroup1 !=null and item.materialGroup1 != ''">
                        and  `material_group1`  =#{item.materialGroup1}
                    </if>
                    <if test="item.netWeight !=null">
                        and  `net_weight`  =#{item.netWeight}
                    </if>
                    <if test="item.unit !=null and item.unit != ''">
                        and  `unit`  =#{item.unit}
                    </if>
                    <if test="item.materialGroup3 !=null and item.materialGroup3 != ''">
                        and  `material_group3`  =#{item.materialGroup3}
                    </if>
                    <if test="item.fictitiousFlag !=null">
                        and  `fictitious_flag`  =#{item.fictitiousFlag}
                    </if>
                    <if test="item.upperItemStatus !=null and item.upperItemStatus != ''">
                        and  `upper_item_status`  =#{item.upperItemStatus}
                    </if>
                    <if test="item.materialGroup2 !=null and item.materialGroup2 != ''">
                        and  `material_group2`  =#{item.materialGroup2}
                    </if>
                    <if test="item.materialGroup5 !=null and item.materialGroup5 != ''">
                        and  `material_group5`  =#{item.materialGroup5}
                    </if>
                    <if test="item.materialGroup4 !=null and item.materialGroup4 != ''">
                        and  `material_group4`  =#{item.materialGroup4}
                    </if>
                    <if test="item.upperLineNo !=null and item.upperLineNo != ''">
                        and  `upper_line_no`  =#{item.upperLineNo}
                    </if>
                    <if test="item.materialGroup6 !=null and item.materialGroup6 != ''">
                        and  `material_group6`  =#{item.materialGroup6}
                    </if>
                    <if test="item.packingAmount !=null">
                        and  `packing_amount`  =#{item.packingAmount}
                    </if>
                    <if test="item.sourceItemCode !=null and item.sourceItemCode != ''">
                        and  `source_item_code`  =#{item.sourceItemCode}
                    </if>
                    <if test="item.sourceOrderNo !=null and item.sourceOrderNo != ''">
                        and  `source_order_no`  =#{item.sourceOrderNo}
                    </if>
                    <if test="item.itemSpec !=null and item.itemSpec != ''">
                        and  `item_spec`  =#{item.itemSpec}
                    </if>
                    <if test="item.planQty !=null">
                        and  `plan_qty`  =#{item.planQty}
                    </if>
                    <if test="item.itemSuiteQty !=null">
                        and  `item_suite_qty`  =#{item.itemSuiteQty}
                    </if>
                    <if test="item.extendInfoFields !=null and item.extendInfoFields != ''">
                        and  `extend_info_fields`  =#{item.extendInfoFields}
                    </if>
                    <if test="item.itemClassName !=null and item.itemClassName != ''">
                        and  `item_class_name`  =#{item.itemClassName}
                    </if>
                    <if test="item.upperItemId !=null and item.upperItemId != ''">
                        and  `upper_item_id`  =#{item.upperItemId}
                    </if>
                    <if test="item.serviceFlag !=null and item.serviceFlag != ''">
                        and  `service_flag`  =#{item.serviceFlag}
                    </if>
                    <if test="item.finalPrice !=null">
                        and  `final_price`  =#{item.finalPrice}
                    </if>
                    <if test="item.remark !=null">
                        and  `remark`  =#{item.remark}
                    </if>
                    <if test="item.tenantCode !=null and item.tenantCode != ''">
                        and  `tenant_code`  =#{item.tenantCode}
                    </if>
                    <if test="item.parentLineNo !=null">
                        and  `parent_line_no`  =#{item.parentLineNo}
                    </if>
                    <if test="item.itemStatusTo !=null and item.itemStatusTo != ''">
                        and  `item_status_to`  =#{item.itemStatusTo}
                    </if>
                    <if test="item.newItemFlag !=null and item.newItemFlag != ''">
                        and  `new_item_flag`  =#{item.newItemFlag}
                    </if>
                    <if test="item.marketModel !=null and item.marketModel != ''">
                        and  `market_model`  =#{item.marketModel}
                    </if>
                    <if test="item.toItemCode !=null and item.toItemCode != ''">
                        and  `to_item_code`  =#{item.toItemCode}
                    </if>
                    <if test="item.produceCode !=null and item.produceCode != ''">
                        and  `produce_code`  =#{item.produceCode}
                    </if>
                    <if test="item.lotCode !=null and item.lotCode != ''">
                        and  `lot_code`  =#{item.lotCode}
                    </if>
                    <if test="item.itemSuiteCode !=null and item.itemSuiteCode != ''">
                        and  `item_suite_code`  =#{item.itemSuiteCode}
                    </if>
                    <if test="item.itemStatus !=null and item.itemStatus != ''">
                        and  `item_status`  =#{item.itemStatus}
                    </if>
                    <if test="item.taskNo !=null and item.taskNo != ''">
                        and  `task_no`  =#{item.taskNo}
                    </if>
                    <if test="item.singleWeight !=null">
                        and  `single_weight`  =#{item.singleWeight}
                    </if>
                    <if test="item.sourcePruduct !=null and item.sourcePruduct != ''">
                        and  `source_pruduct`  =#{item.sourcePruduct}
                    </if>
                    <if test="item.itemLineNo !=null">
                        and  `item_line_no`  =#{item.itemLineNo}
                    </if>
                    <if test="item.batchCode !=null and item.batchCode != ''">
                        and  `batch_code`  =#{item.batchCode}
                    </if>
                    <if test="item.length !=null">
                        and  `length`  =#{item.length}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.actQty !=null">
                        and  `act_qty`  =#{item.actQty}
                    </if>
                    <if test="item.grossWeight !=null">
                        and  `gross_weight`  =#{item.grossWeight}
                    </if>
                    <if test="item.expireTimestamp !=null">
                        and  `expire_timestamp`  =#{item.expireTimestamp}
                    </if>
                    <if test="item.financeNo !=null and item.financeNo != ''">
                        and  `finance_no`  =#{item.financeNo}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.itemClass !=null and item.itemClass != ''">
                        and  `item_class`  =#{item.itemClass}
                    </if>
                    <if test="item.pkgQty !=null">
                        and  `pkg_qty`  =#{item.pkgQty}
                    </if>
                    <if test="item.width !=null">
                        and  `width`  =#{item.width}
                    </if>
                    <if test="item.height !=null">
                        and  `height`  =#{item.height}
                    </if>
                    <if test="item.extend1 !=null and item.extend1 != ''">
                        and  `extend1`  =#{item.extend1}
                    </if>
                    <if test="item.notaxPrice !=null">
                        and  `notax_price`  =#{item.notaxPrice}
                    </if>
                    <if test="item.upperItemCode !=null and item.upperItemCode != ''">
                        and  `upper_item_code`  =#{item.upperItemCode}
                    </if>
                    <if test="item.groupIndication !=null and item.groupIndication != ''">
                        and  `group_indication`  = #{item.groupIndication}
                    </if>
                    <if test="item.ifInstall !=null and item.ifInstall != ''">
                        and  `if_install`  = #{item.ifInstall}
                    </if>
                    <if test="item.mainItemCode !=null and item.mainItemCode != ''">
                        and  `main_item_code`  = #{item.mainItemCode}
                    </if>
                    <if test="item.materialGroup7 !=null and item.materialGroup7 != ''">
                        and  `material_group7`  =#{item.materialGroup7}
                    </if>
                    <if test="item.materialGroup8 !=null and item.materialGroup8 != ''">
                        and  `material_group8`  =#{item.materialGroup8}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_item
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
#{item.id}
        </foreach>
    </update>


    <update id="deleteBatchByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_item
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>

</mapper>