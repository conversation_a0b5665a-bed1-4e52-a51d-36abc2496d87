package com.midea.logistics.otp.order.common.fegin.fallback;


import com.midea.logistics.otp.order.domain.bean.PledgeInfo;
import com.midea.logistics.otp.order.common.fegin.PledgeInfoFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年06月27日 上午21:25
 */
@Component
@Slf4j
public class PledgeInfoFeignFallBack implements FallbackFactory<PledgeInfoFeign> {
    @Override
    public PledgeInfoFeign create(Throwable throwable) {
        log.info("bop PledgeInfoFeignFallBack 接口调度失败 {}", throwable.getMessage());
        return new PledgeInfoFeign() {
            @Override
            public JsonResponse create(@Valid PledgeInfo pledgeInfo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<PledgeInfo> queryByBusinessKey(Long id) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> deleteByBusinessKey(Long id) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> update(Long id, PledgeInfo pledgeInfo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<PageResponse<PledgeInfo>> search(PledgeInfo pledgeInfo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<PledgeInfo> findByOrderNo(String orderNo) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }
        };
    }
}
