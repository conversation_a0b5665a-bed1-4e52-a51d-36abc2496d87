package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CountrySubsidyInfo {
    //处理到第几步  10-同步es   20-回传上游
    private Integer step;

    private String waybillNo;
    private String sn;
    private String imei;
    private Date completeTime;
    private String wpServiceCode;
    private List<Photo> photoList;
    private List<String> photoUrls;
    @Data
    public static class Photo{
        private String photoUrl;
        private int photoType;
    }

}
