package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.LuggageConversionService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.MaterialLuggagehelper;
import com.midea.logistics.otp.order.converged.domain.response.MaterialLuggageRelation;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LuggageConversionServiceImpl implements LuggageConversionService {

    private static final String REPORT_LOMS_TI_TO = "REPORT_LOMS_TI_TO";

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private MaterialLuggagehelper materialLuggagehelper;
    @Autowired
    private DictHelper dictHelper;

    @Override
    @ZeebeFlow("LUGGAGE_CONVERSION")
    public CustomerOrderInfoExt luggageConversion(CustomerOrderInfoExt customerOrderInfoExt) {
        //校验
        if (StringUtils.isEmpty(customerOrderInfoExt.getOrderNo())) {
            throw BusinessException.fail("订单号不能为空");
        }

        String orderNo = customerOrderInfoExt.getOrderNo();

        CustomerOrderInfo customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfo(orderNo);
        if (null == customerOrderInfo) {
            throw BusinessException.fail("查询订单信息为空"+orderNo);
        }

        String sourceSystem = customerOrderInfo.getSourceSystem();
        String orderType = customerOrderInfo.getOrderType();
        String customerCode = customerOrderInfo.getCustomerCode();
        boolean isConver = SourceSystem.KingDee_SD.getKey().equals(sourceSystem) || SourceSystem.PG.getKey().equals(sourceSystem) || SourceSystem.ZP_WBKH.getKey().equals(sourceSystem)
            || SourceSystem.COLGATE.getKey().equals(sourceSystem) || SourceSystem.JBLSAP_WBKH.getKey().equals(sourceSystem) || SourceSystem.JBLPOP_WBKH.getKey().equals(sourceSystem);
        boolean isHand = StringUtils.isNotEmpty(dictHelper.getDictVaule(REPORT_LOMS_TI_TO,customerCode));
        if(!isConver && !isHand) {
            FlowListenerParam.success("来源系统非沱牌/宝洁/舟谱/高露洁/金佰利不需要转换");
            return customerOrderInfoExt;
        }

        //查询客户订单商品信息
        List<CustomerOrderItem> customerOrderItems = lmpOrderFlowHelper.listCustomerOrderItem(orderNo);
        if (CollectionUtils.isEmpty(customerOrderItems)) {
            throw BusinessException.fail("没有查询到商品信息,orderNo"+orderNo);
        }


        StringBuffer str = new StringBuffer();
        log.info("luggageConversion-> orderNo:{},customerCode:{},开始箱支转换---------->",orderNo,customerCode);

        for (CustomerOrderItem customerOrderItem : customerOrderItems) {
            if (SourceSystem.PG.getKey().equals(sourceSystem) || isHand || SourceSystem.ZP_WBKH.getKey().equals(sourceSystem) || SourceSystem.COLGATE.getKey().equals(sourceSystem) ||
                SourceSystem.JBLSAP_WBKH.getKey().equals(sourceSystem) || SourceSystem.JBLPOP_WBKH.getKey().equals(sourceSystem)){
                //宝洁的转换根据上游下发的unit来换算
                materialLuggagehelper.pgSpecConvert(customerOrderItem,customerCode,str);
            }else {
                BigDecimal planQty = customerOrderItem.getPlanQty();
                String upperItemCode = customerOrderItem.getUpperItemCode();
                String customerItemCode = customerOrderItem.getCustomerItemCode();
                BigDecimal result = materialLuggagehelper.packageToBranch(upperItemCode, customerCode, planQty);
                log.info("luggageConversion-> orderNo:{},customerItemCode:{},转换前的数量：{}-->转换后的数量:{}",orderNo,customerCode,planQty,result);
                customerOrderItem.setPlanQty(result);
                str.append("["+customerItemCode+"："+planQty.setScale(4)+" -> " +result+"],");
            }
        }
        //2024年5月9日08:54:57 因为流程放到商品解析后面了,所以这里订单头的总数量totalQty还要重新计算一下
        this.resetInfoTotalQty(customerOrderInfoExt,customerOrderItems);

        lmpOrderFlowHelper.updateCustomerOrderItem(customerOrderItems);

        String info = str.toString();
        if(StringUtils.isNotEmpty(info)){
            info = info.substring(0, info.length() - 1);
        }
        FlowListenerParam.success("转换成功,转换结果："+info);
        return customerOrderInfoExt;
    }

    /**
     * 箱包转换导致计划数量变化了 所以头表的总计划数量 总体积总重量这些要重新计算
     * @param customerOrderInfoExt
     * @param customerOrderItems
     */
    private void resetInfoTotalQty(CustomerOrderInfoExt customerOrderInfoExt, List<CustomerOrderItem> customerOrderItems) {
        BigDecimal totalQty = BigDecimal.ZERO;
        // order的总体积和总重量
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal totalPkgQty = BigDecimal.ZERO;
        for (int i = 0; i < customerOrderItems.size(); i++) {
            CustomerOrderItem item = customerOrderItems.get(i);
            if (Integer.valueOf(1).compareTo(item.getDeleteFlag()) == 0) {
                continue;
            }
            totalQty = totalQty.add(item.getPlanQty());

            if (null != item.getPkgQty()) {
                totalPkgQty = totalPkgQty.add(item.getPkgQty());
            }
            if (item.getVolume() != null) {
                totalVolume = totalVolume.add(item.getPlanQty().multiply(item.getVolume()));
            }else{
                totalVolume = totalVolume.add(item.getTotalVolume() == null ? BigDecimal.ZERO: item.getTotalVolume());
            }
            if (item.getGrossWeight() != null) {
                totalWeight = totalWeight.add(item.getPlanQty().multiply(item.getGrossWeight()));
            }else{
                totalWeight = totalWeight.add(item.getTotalGrossWeight()== null ? BigDecimal.ZERO:item.getTotalGrossWeight());
            }
        }
        customerOrderInfoExt.setTotalQty(totalQty);
        customerOrderInfoExt.setTotalGrossWeight(totalWeight);
        customerOrderInfoExt.setTotalVolume(totalVolume);
        customerOrderInfoExt.setTotalPkgQty(totalPkgQty);
        customerOrderInfoExt.setCustomerOrderItems(customerOrderItems);

        CustomerOrderInfo update = new CustomerOrderInfo();
        update.setId(customerOrderInfoExt.getId());
        update.setTotalQty(totalQty);
        update.setTotalGrossWeight(totalWeight);
        update.setTotalVolume(totalVolume);
        update.setTotalPkgQty(totalPkgQty);
        lmpOrderFlowHelper.updateCustomerOrderInfo(update,"箱规转换头表总数更新");
    }
}
