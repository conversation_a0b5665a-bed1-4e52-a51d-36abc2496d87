package com.midea.logistics.imp.orderverify.service;


import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.mideaframework.core.web.JsonResponse;

/**
* @description: 解析四级地址
* @author: 陈永培
* @createtime: 2020/11/16 19:46
*/
public interface IAddressResolvingService {

    /**
    * @description: 解析四级地址
    * @param: [customerOrderInfoExt]
    * @return: CustomerOrderInfoExt
    * @author: 陈永培
    * @createtime: 2020/11/16 19:46
    */
    CustomerOrderInfoExt queryForCountry(CustomerOrderInfoExt customerOrderInfoExt);
}
