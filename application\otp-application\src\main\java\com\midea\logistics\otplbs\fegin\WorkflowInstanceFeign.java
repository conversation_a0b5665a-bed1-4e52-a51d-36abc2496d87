package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.WorkflowInstance;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: WorkflowInstanceFeign
 * Author: wangkc4
 * Date: 2019-6-10 10:34:03
 * Description:流程-实例 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface WorkflowInstanceFeign {

    @RequestMapping(value = "/workflowInstance", method = RequestMethod.POST)
    JsonResponse create(@RequestBody WorkflowInstance workflowInstance);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workflowInstance/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workflowInstance/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody WorkflowInstance workflowInstance);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workflowInstance/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/workflowInstances", method = RequestMethod.POST)
    JsonResponse search(@RequestBody WorkflowInstance workflowInstance);
}
