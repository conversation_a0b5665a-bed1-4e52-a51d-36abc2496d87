package com.midea.logistics.otp.order.common.fegin.dc;

import java.util.List;

import com.midea.logistics.otp.common.request.DcWarehouseMapping;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import com.midea.logistics.lcp.domain.order.entity.CustomerMapping;
import com.midea.logistics.lcp.domain.order.entity.ItemCodeMapping;
import com.midea.logistics.lcp.domain.order.entity.ItemStatusMapping;
import com.midea.logistics.lcp.domain.order.entity.WarehouseMapping;
import com.midea.logistics.otp.bean.EcmInfoMappingBean;
import com.mideaframework.core.web.JsonResponse;

/**
 * Date:     2020-11-18 15:53
 * sit: url = "http://10.16.95.35:8054"
 * ！！！！！！！！！ 记得加 ,headers = {"tenantCode=annto"} 租户 ！！！！！！！！！！！！！！！！
 */
@FeignClient(value = "logistics-lcp-dc-order-atomic")
public interface DcAtomicOrderFeign {

    /**
     * 通过上游客户编码和来源系统查询上游客户数据
     * @param entity
     * @return
     */
    @GetMapping(value = "/mapping/customer/mapping/detail/cache",headers = {"tenantCode=annto"})
    JsonResponse<CustomerMapping> getByUpperCustomerCodeAndSourceSystem(@SpringQueryMap CustomerMapping entity);

    /**
    * @description: 商品状态
    * @param: [itemStatusMapping]
    * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.lcp.domain.order.entity.ItemStatusMapping>
    * @author: 陈永培
    * @createtime: 2022/6/24 11:27
    */
    @GetMapping(value ="/mapping/item/status/mapping/detail/cache",headers = {"tenantCode=annto"})
    JsonResponse<ItemStatusMapping> itemStatusMappingDetailCache(@SpringQueryMap ItemStatusMapping itemStatusMapping);
    
    /**
     * @description: 商品状态V2 增加来源系统+上游客户编码精准匹配
     * @param: [itemStatusMapping]
     * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.lcp.domain.order.entity.ItemStatusMapping>
     * @author: 陈永培
     * @createtime: 2022/6/24 11:27
     */
    @GetMapping(value ="/mapping/item/status/mapping/detail/cacheV2",headers = {"tenantCode=annto"})
    JsonResponse<ItemStatusMapping> itemStatusMappingDetailCacheV2(@SpringQueryMap ItemStatusMapping itemStatusMapping);
    
    /**
    * @description: 查询商品对照
    * @param: [itemCodeMapping]
    * @return: com.mideaframework.core.web.JsonResponse<ItemCodeMapping>
    * @author: 陈永培
    * @createtime: 2022/4/16 18:02
    */
    @GetMapping(value = "/item/code/mapping/cache",headers = {"tenantCode=annto"})
    JsonResponse<ItemCodeMapping> itemCodeMappingCache(@SpringQueryMap ItemCodeMapping itemCodeMapping);


    @GetMapping(value = "/customer/mapping/list")
    JsonResponse<List<CustomerMapping>> customerMappingListBySysAndCustomerCode(
        @RequestParam("sourceSystem") String sourceSystem,
        @RequestParam("customerCode") String customerCode,
        @RequestHeader(name = "tenantCode") String tenantCode);

    @GetMapping(value = "/warehouse/mapping/list")
    JsonResponse<List<WarehouseMapping>> warehouseMappingListBySysAndSiteCode(
        @RequestParam("sourceSystem") String sourceSystem,
        @RequestParam("siteCode") String siteCode,
        @RequestHeader(name = "tenantCode") String tenantCode);

    @GetMapping(value = "/item/status/mapping/list")
    JsonResponse<List<ItemStatusMapping>> itemStatusMappingListBySysAndItemStatus(
        @RequestParam("sourceSystem") String sourceSystem,
        @RequestParam("itemStatus") String itemStatus,
        @RequestHeader(name = "tenantCode") String tenantCode);

    @PostMapping(value = "/customer/mapping/getMapping",headers = {"tenantCode=annto"})
    JsonResponse<List<EcmInfoMappingBean>> ecmInfoMappingSearch(@RequestBody EcmInfoMappingBean entity);

    @PostMapping(value = "/customer/mapping/getMapping")
    JsonResponse<List<EcmInfoMappingBean>> ecmInfoMappingSearchWithSettingHeaders(
        @RequestBody EcmInfoMappingBean entity,
        @RequestHeader(name = "tenantCode") String tenantCode);

    @GetMapping(value = "/item/status/mapping/upperItemStatusCode/search",headers = {"tenantCode=annto"})
    JsonResponse<List<String>> searchOfcUpperItemStatusCode(@RequestParam("customerCode") String customerCode, @RequestParam("siteCode") String siteCode, @RequestParam("itemStatus") String itemStatus);

    @GetMapping(value = "/mapping/warehouse/mapping/detail/cache",headers = {"tenantCode=annto"})
    JsonResponse<DcWarehouseMapping> warehouseMappingCacheCqCode(@SpringQueryMap DcWarehouseMapping var1);
}
