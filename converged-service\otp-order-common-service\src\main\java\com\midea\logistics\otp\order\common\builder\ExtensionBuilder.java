package com.midea.logistics.otp.order.common.builder;

import com.mideaframework.core.utils.SpringContextHolder;
import org.springframework.stereotype.Component;


@Component
public class ExtensionBuilder<E> {



    public <T> T doWork(String beanName, E paramOne,T paramTow) {

        Extension<E, T> extension = SpringContextHolder.getBean(beanName);
        return extension.doWork(paramOne,paramTow);
    }
}
