package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;

/** 
* @description: 计费业务类型
* @author: 陈永培
* @createtime: 2020/11/18 22:37 
*/ 
public interface AnalysisBusineesFeeService {

    /**
    * @description: 计费业务类型
    * @param: [orderInfo]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2020/11/18 22:39
    */
    OrderInfoExt analysisBusineesFeeConfig(OrderInfoExt orderInfo, Boolean updateFlag);
}