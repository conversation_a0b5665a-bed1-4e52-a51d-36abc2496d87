package com.midea.logistics.otp.order.common.cache;

import com.google.common.collect.Lists;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.rule.IssueRuleFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.PieceAnalysisFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.ProjectClassifyConfigFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.ShippingTypeRuleFeign;
import com.midea.logistics.otp.rule.domain.bean.IssueRule;
import com.midea.logistics.otp.rule.domain.bean.PieceAnalysis;
import com.midea.logistics.otp.rule.domain.bean.ProjectClassifyConfig;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.mideaframework.core.bean.TenantPagingDomain;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class RuleCacheImpl implements RuleCache {


    private static final Logger logger = LoggerFactory.getLogger(RuleCache.class);

    // 类反馈缓存
    private static Map<String, List> PROPERTY_DESCRIPTORS = new HashMap<>();
    // 原始静态List
    private static final Map<String, List> CONFIGS = new HashMap<>();
    // 缓存应用名
    private static final List<String> appNames = Lists.newArrayList(
        "logistics-otp-orderverify-converged-service",
        "logistics-otp-ordertask-converged-service",
        "logistics-otp-order-converged-service","logistics-lmp-order-verify-service");

    private static final List<String> appRuleNames = Lists.newArrayList(
        "logistics-otp-ordertask-converged-service",
        "logistics-otp-order-converged-service");

    @Autowired
    private DiscoveryClient discoveryClient;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IssueRuleFeign issueRuleFeign;

    @Autowired
    private ShippingTypeRuleFeign shippingTypeRuleFeign;

    @Autowired
    private ProjectClassifyConfigFeign projectClassifyConfigFeign;

    @Autowired
    private PieceAnalysisFeign pieceAnalysisFeign;

    /**
    * @description: 显示所有的配置
    * @param: [param]
    * @return: T
    * @author: 陈永培
    * @createtime: 2020/4/28 9:52
    */
    public static Map<String, List> getAllConfig () {
        return  CONFIGS;
    }


    @Override
    public <T> T search(T param){
        if (param == null){
            return null;
        }
        String name = param.getClass().getName();
        List<T> cache = cache(name);
        if (CollectionUtils.isEmpty(cache)){
            throw BusinessException.fail("缓存内容 " + name + " 暂不支持缓存，需要进行配置");
        }

        // 值匹配，若能匹配上，就返回
        List<Method> valuedList = getValuedList(param);
        if (valuedList == null) {
            return null;
        }

        try {

            /**
             * 匹配不成功规则：
             * 1. paramValue 要求为空， configValue 不为空
             * 2. paramValue 不为空，configValue 为空
             * 3. paramValue 不为空，configValue 不为空，但值不匹配
             */

            for (T config : cache) {
                int fit = 0;
                for (Method method : valuedList) {
                    Object configValue = method.invoke(config);
                    String paramValue = method.invoke(param).toString();    // 已经确定不为空

                    // 1. paramValue 要求为空， configValue 为空, 命中一次
                    if ("null".equalsIgnoreCase(paramValue) && (configValue == null || configValue.toString().trim().length() == 0)){
                        fit ++;
                        continue;
                    }

                    // paramValue 不为空，configValue 不为空，值不匹配
                    if (configValue != null && paramValue.equals(configValue.toString())){
                        fit ++;
                        continue;
                    }
                }
                if (fit == valuedList.size()){
                    return config;
                }
            }
        } catch (IllegalAccessException e) {
            logger.error(e.getMessage(), e);
        } catch (InvocationTargetException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }


    @Override
    public <T> List<T> searchList(T param){
        if (param == null){
            return null;
        }
        String name = param.getClass().getName();
        List<T> cache = cache(name);
        if (CollectionUtils.isEmpty(cache)){
            throw BusinessException.fail("缓存内容 " + name + " 暂不支持缓存，需要进行配置");
        }

        // 值匹配，若能匹配上，就返回
        List<Method> valuedList = getValuedList(param);
        if (valuedList == null) {
            return null;
        }

        try {

            /**
             * 匹配不成功规则：
             * 1. paramValue 要求为空， configValue 不为空
             * 2. paramValue 不为空，configValue 为空
             * 3. paramValue 不为空，configValue 不为空，但值不匹配
             */

            List<T> configs = Lists.newArrayList();

            for (T config : cache) {
                int fit = 0;
                for (Method method : valuedList) {
                    Object configValue = method.invoke(config);
                    String paramValue = method.invoke(param).toString();    // 已经确定不为空

                    // 1. paramValue 要求为空， configValue 为空, 命中一次
                    if ( ("null".equalsIgnoreCase(paramValue) || "".equalsIgnoreCase(paramValue)) && (configValue == null || configValue.toString().trim().length() == 0)){
                        fit ++;
                        continue;
                    }

                    // paramValue 不为空，configValue 不为空，值不匹配
                    if (configValue != null && paramValue.equals(configValue.toString())){
                        fit ++;
                        continue;
                    }
                }
                if (fit == valuedList.size()){
                    configs.add(config);
                }
            }

            return configs;
        } catch (IllegalAccessException e) {
            logger.error(e.getMessage(), e);
        } catch (InvocationTargetException e) {
            logger.error(e.getMessage(), e);
        }

        //内存查不到查询数据库
        return searchDB(param);
//        return null;
    }

    private <T> List<T> searchDB(T param) {
        String clazz = param.getClass().getName();
        int pageSize = 1;
        // IssueRule 下发规则
        if (IssueRule.class.getName().equals(clazz)){
            IssueRule issueRule = new IssueRule();
            BeanUtils.copyProperties(param, issueRule);
            issueRule.setPageSize(pageSize);
            JsonResponse<PageResponse<IssueRule>> cache = issueRuleFeign.search(issueRule);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(cache.getCode())){
                throw BusinessException.fail("获取下发规则配置失败");
            }
            return (List<T>) cache.data.list;
        }

        if (ShippingTypeRule.class.getName().equals(clazz)){
            ShippingTypeRule shippingTypeRule = new ShippingTypeRule();
            BeanUtils.copyProperties(param, shippingTypeRule);
            shippingTypeRule.setPageSize(pageSize);
            JsonResponse<PageResponse<ShippingTypeRule>> cache = shippingTypeRuleFeign.getShippingTypeRuleList(shippingTypeRule);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(cache.getCode())){
                throw BusinessException.fail("获取配送方式配置失败");
            }
            return (List<T>) cache.data.list;
        }

        if (ProjectClassifyConfig.class.getName().equals(clazz)){
            ProjectClassifyConfig projectClassifyConfig = new ProjectClassifyConfig();
            BeanUtils.copyProperties(param, projectClassifyConfig);
            projectClassifyConfig.setPageSize(pageSize);
            JsonResponse<PageResponse<ProjectClassifyConfig>> cache = projectClassifyConfigFeign.search(projectClassifyConfig);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(cache.getCode())){
                throw BusinessException.fail("获取项目分类配置失败");
            }
            return (List<T>) cache.data.list;
        }
        return null;
    }


    /**
     * 请求清除所有缓存，只有当前实例被调用
     * @param clazz
     */
    @Override
    public void clear(Class clazz){
        String name = clazz.getName();
        logger.info("配置有更新，即将通知各服务清除缓存，配置类：{}", name);
        // 发送请求，清除所有缓存
        // 发出通知给 discoveryClient发现服务， 进行清除各注册服务的缓存
        appNames.stream().forEach(a-> {
                List<ServiceInstance> serviceInstances = discoveryClient.getInstances(a);
                for (ServiceInstance s : serviceInstances) {
                    String uri = s.getUri() + "/caCheClear?clazz=" + name;
                    restTemplate.getForEntity(uri, JsonResponse.class);
                    logger.info(uri + "清除缓存完成！");
                }
            }
        );

        if (IssueRule.class.getName().equals(name)){
            appRuleNames.stream().forEach(a-> {
                    List<ServiceInstance> serviceInstances = discoveryClient.getInstances(a);
                    for (ServiceInstance s : serviceInstances) {
                        String uri = s.getUri() + "/issueRuleCache";
                        restTemplate.getForEntity(uri, JsonResponse.class);
                        logger.info(uri + "更新下发规则缓存完成！");
                    }
                }
            );
        }
    }



    /**
     * 缓存
     */
    private synchronized <T> List<T> cache(String clazz){
        List<T> list = CONFIGS.get(clazz);
        if (list != null){
            return list;
        }

        //查询1w条
        Integer pageSize = 25000;

        // IssueRule 下发规则
        if (IssueRule.class.getName().equals(clazz)){
            IssueRule issueRule = new IssueRule();
            issueRule.setPageSize(pageSize);
            JsonResponse<PageResponse<IssueRule>> cache = issueRuleFeign.search(issueRule);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(cache.getCode())){
                throw BusinessException.fail("获取下发规则配置失败");
            }
            CONFIGS.put(clazz, cache.data.list);
        }

        if (ShippingTypeRule.class.getName().equals(clazz)){
            ShippingTypeRule param = new ShippingTypeRule();
            param.setPageSize(pageSize);
            JsonResponse<PageResponse<ShippingTypeRule>> cache = shippingTypeRuleFeign.getShippingTypeRuleList(param);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(cache.getCode())){
                throw BusinessException.fail("获取配送方式配置失败");
            }
            CONFIGS.put(clazz, cache.data.list);
        }

        if (ProjectClassifyConfig.class.getName().equals(clazz)){
            ProjectClassifyConfig param = new ProjectClassifyConfig();
            param.setPageSize(pageSize);
            JsonResponse<PageResponse<ProjectClassifyConfig>> cache = projectClassifyConfigFeign.searchList(param);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(cache.getCode())){
                throw BusinessException.fail("获取项目分类配置失败");
            }
            CONFIGS.put(clazz, cache.data.list);
        }

        if (PieceAnalysis.class.getName().equals(clazz)){
            PieceAnalysis param = new PieceAnalysis();
            param.setPageSize(pageSize);
            JsonResponse<PageResponse<PieceAnalysis>> cache = pieceAnalysisFeign.searchPieceAnalysis(param);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(cache.getCode())){
                throw BusinessException.fail("获取件型解析配置失败");
            }
            CONFIGS.put(clazz, cache.data.list);


        }


        // TODO 其他缓存查询，独立实现，继续往这里添加

        return CONFIGS.get(clazz);
    }


    /**
     * 删除本地缓存，由收到的消息执行，所有实例均会执行
     * @param clazz
     */
    public static void remove(String clazz){
        CONFIGS.remove(clazz);
    }


    /**
     * 获取有值的方法
     */
    private static <T> List<Method> getValuedList(T param){
        List<PropertyDescriptor> propertyDescriptors = getPropertyDescriptors(param.getClass());
        if (propertyDescriptors == null){
            return null;
        }
        List<Method> list = null;
        for (PropertyDescriptor property : propertyDescriptors) {
            Method getter = property.getReadMethod();
            Object value = null;
            try {
                value = getter.invoke(param);
            } catch (IllegalAccessException e) {
                logger.error(e.getMessage(), e);
            } catch (InvocationTargetException e) {
                logger.error(e.getMessage(), e);
            }
            if (value != null) {
                if (list == null){
                    list = new ArrayList<>();
                }
                list.add(getter);
            }
        }
        return list;
    }



    /**
     * 获取 bean 方法
     * @param clazz
     * @return
     */
    private static List<PropertyDescriptor> getPropertyDescriptors(Class clazz){
        String name = clazz.getName();
        List ps = PROPERTY_DESCRIPTORS.get(name);
        if (ps != null){
            return ps;
        }
        try {
            BeanInfo tenantPagingDomain = Introspector.getBeanInfo(TenantPagingDomain.class);
            PropertyDescriptor[] extPropertyDescriptors = tenantPagingDomain.getPropertyDescriptors();

            BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            List<PropertyDescriptor> list = new ArrayList<>();
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                Method readMethod = propertyDescriptor.getReadMethod();
                if ("getClass".equals(readMethod.getName())){
                    continue;
                }

                for(int i=0; i<extPropertyDescriptors.length; i++){
                    Method extReadMethod = extPropertyDescriptors[i].getReadMethod();
                    if(extReadMethod.equals(readMethod)){
                        break;
                    }
                    if(i == extPropertyDescriptors.length-1){
                        list.add(propertyDescriptor);
                    }
                }
            }
            PROPERTY_DESCRIPTORS.put(name, list);
            return list;
        } catch (IntrospectionException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

}
