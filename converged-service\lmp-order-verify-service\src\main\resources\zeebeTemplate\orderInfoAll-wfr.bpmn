<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="order-info-process-wfr" name="子订单流程-模糊订单" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1wde1q2</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_14d5skw">
      <bpmn:incoming>Flow_13j2a01</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_13j2a01" sourceRef="Activity_195th7r" targetRef="Event_14d5skw" />
    <bpmn:serviceTask id="Activity_195th7r" name="生成任务">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="task" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/sentTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1civu78</bpmn:incoming>
      <bpmn:outgoing>Flow_13j2a01</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wde1q2" sourceRef="StartEvent_1" targetRef="Activity_0674ix2" />
    <bpmn:serviceTask id="Activity_0674ix2" name="质押">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="pledge" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/pledgeCheck" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wde1q2</bpmn:incoming>
      <bpmn:outgoing>Flow_1civu78</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1civu78" sourceRef="Activity_0674ix2" targetRef="Activity_195th7r" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="order-info-process-wfr">
      <bpmndi:BPMNEdge id="Flow_13j2a01_di" bpmnElement="Flow_13j2a01">
        <di:waypoint x="590" y="117" />
        <di:waypoint x="652" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wde1q2_di" bpmnElement="Flow_1wde1q2">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1civu78_di" bpmnElement="Flow_1civu78">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="490" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0674ix2_di" bpmnElement="Activity_0674ix2">
        <dc:Bounds x="270" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1aomcsq_di" bpmnElement="Activity_195th7r">
        <dc:Bounds x="490" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="189" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="652" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1269" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
