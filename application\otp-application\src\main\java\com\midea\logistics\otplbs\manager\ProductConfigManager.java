package com.midea.logistics.otplbs.manager;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.MdmDataDictionaryDetailManager;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EbPlace;
import com.midea.logistics.domain.mdm.domain.MdmDataDictionaryDetail;
import com.midea.logistics.domain.mdm.request.EbPlaceRequest;
import com.midea.logistics.lc.file.sdk.builder.FileUploadBuilder;
import com.midea.logistics.lc.file.sdk.service.LcFileService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbCustomerFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbplaceFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.ProductConfigFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.RedisLockHelper;
import com.midea.logistics.otp.common.helper.bean.ProductConfigExcelRequest;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.BusinessMode;
import com.midea.logistics.otp.enums.CostOrderInfoStatusEnum;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.report.service.domain.request.CostOrderInfoImportFileRequest;
import com.midea.logistics.otp.rule.domain.bean.ProductConfig;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
@Component
public class ProductConfigManager {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ProductConfigFeign productConfigFeign;

    @Autowired
    private EbCustomerFeign ebCustomerFeign;

    @Autowired
    private EbCustomerManager ebCustomerManager;

    @Autowired
    private MdmDataDictionaryDetailManager mdmDataDictionaryDetailManager;

    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private RedisLockHelper redisLockHelper;
    @Autowired
    private LcFileService lcFileService;
    @Autowired
    private ReportFeign reportFeign;
    @Autowired
    private EbplaceFeign ebPlaceFeign;

    public void excelImport(@RequestParam("file") MultipartFile file,String tenantCode) throws Exception {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(3, 5, 30, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(50));
        ImportExcel ei = new ImportExcel(file, 1, 0, applicationContext);
        FutureTask<List<MdmDataDictionaryDetail> > futureTask = new FutureTask<>(new Callable<List<MdmDataDictionaryDetail>>() {
            @Override
            public List<MdmDataDictionaryDetail> call() throws Exception {
                return  mdmDataDictionaryDetailManager.getMdmDataDictionaryDetailCache("SYS_CUST_RELATION_TYPE");
            }
        });
        executor.submit(futureTask);
        List<ProductConfigExcelRequest> list = ei.getDataList(ProductConfigExcelRequest.class, null);
        if (CollectionUtils.isEmpty(list)) {
            throw BusinessException.fail("导入服务产品规则数据为空");
        }
        List<MdmDataDictionaryDetail>  mdmDictionaryDetailList=futureTask.get();
        if(CollectionUtils.isEmpty(mdmDictionaryDetailList)){
            throw BusinessException.fail("根据客户系字典编码，未查找到客户系字典数据！");
        }
        int[] row = {1};
        List<String> failList=new ArrayList<>();
        list.stream().forEach(e -> {
            try {
                row[0]++;
                check(e);
                //重新赋值
                if(!StringUtils.isEmpty(e.getDeleteFlag())&&e.getId()==null){
                    throw BusinessException.fail("添加了删除标识，未填写ID");
                }
                FutureTask<ProductConfigExcelRequest> productConfigFutureTask = new FutureTask<>(new Callable<ProductConfigExcelRequest>() {
                    @Override
                    public ProductConfigExcelRequest call() throws Exception {
                        try{
                             setCustomerRelationship(e,mdmDictionaryDetailList);
                        }catch (Exception ex){
                            e.setExceptionMessage(ex.getMessage());
                        }
                        return e;
                    }
                });
                executor.submit(productConfigFutureTask);
                ProductConfigExcelRequest entity=productConfigFutureTask.get();
                if(!StringUtils.isEmpty(entity.getExceptionMessage())){
                    failList.add("第" + row[0] + "行解析数据异常," +entity.getExceptionMessage());
                    return;
                }
                ProductConfig productConfig = new ProductConfig();
                BeanUtils.copyProperties(entity, productConfig);
              /*
                if(!StringUtils.isEmpty(tenantCode)) {
                    productConfig.setTenantCode(tenantCode);
                }*/
                JsonResponse jsonResponse = productConfigFeign.batchCreateOrUpdate(Arrays.asList(productConfig));
                if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
                    throw BusinessException.fail(JSON.toJSONString(jsonResponse));
                }
            } catch (Exception ex) {
                failList.add("第" + row[0] + "行解析数据异常," +ex.getMessage());
            }

        });
        executor.shutdown();
        if (failList.size() > 0) {
            throw BusinessException.fail(failList.toString());
        }

    }


    private void check(ProductConfigExcelRequest entity) {
        if (ToolUtils.isEmpty(entity.getProductCode())&&ToolUtils.isEmpty(entity.getProductName())) {
            throw BusinessException.fail("产品编码和产品名称不能同时为空");
        }
        if (ToolUtils.isEmpty(entity.getProductLineCode())&&ToolUtils.isEmpty(entity.getProductLineName())) {
            throw BusinessException.fail("产品线编码和产品线名称不能同时为空");
        }
        checkAddress(entity,"不能为空",false);
        if(ToolUtils.isEmpty(entity.getProductAging())){
            throw BusinessException.fail("时效产品不能为空");
        }
        if(!StringUtils.isEmpty(entity.getOrderStartTime())){
            checkOrderBeginDate( entity.getOrderStartTime());
        }else {
            throw BusinessException.fail("截单开始时间不能为空");
        }
        if(!StringUtils.isEmpty(entity.getOrderEndTime())){
            checkOrderEndDate(entity.getOrderEndTime());
        }else{
            throw BusinessException.fail("截单结束时间不能为空");
        }
        List<String> addressCode = getAddressCode(entity.getReceiverProvinceName(), entity.getReceiverCityName(), entity.getReceiverDistrictName(), entity.getReceiverTownName());
        entity.setReceiverProvinceCode(addressCode.get(0));
        entity.setReceiverCityCode(addressCode.get(1));
        entity.setReceiverDistrictCode(addressCode.get(2));
        entity.setReceiverTownCode(addressCode.get(3));
        addressCode = getAddressCode(entity.getSenderProvinceName(), entity.getSenderCityName(), entity.getSenderDistrictName(), entity.getSenderTownName());
        entity.setSenderProvinceCode(addressCode.get(0));
        entity.setSenderCityCode(addressCode.get(1));
        entity.setSenderDistrictCode(addressCode.get(2));
        entity.setSenderTownCode(addressCode.get(3));
        checkAddress(entity,"不存在",true);
    }

    private void checkAddress(ProductConfigExcelRequest entity,String errorMessage,boolean isCode){
        try{
            if (ToolUtils.isEmpty(entity.getReceiverProvinceName())||(ToolUtils.isEmpty(entity.getReceiverProvinceCode())&&isCode)) {
                throw BusinessException.fail("目的地省名称");
            }
            if (ToolUtils.isEmpty(entity.getReceiverCityName())||(ToolUtils.isEmpty(entity.getReceiverCityCode())&&isCode)) {
                throw BusinessException.fail("目的地市名称");
            }
            if (ToolUtils.isEmpty(entity.getReceiverDistrictName())||(ToolUtils.isEmpty(entity.getReceiverDistrictCode())&&isCode)) {
                throw BusinessException.fail("目的地区县名称");
            }
            if (ToolUtils.isEmpty(entity.getReceiverTownName())||(ToolUtils.isEmpty(entity.getReceiverTownCode())&&isCode)) {
                throw BusinessException.fail("目的地乡镇名称");
            }
            if (ToolUtils.isEmpty(entity.getSenderProvinceName())||(ToolUtils.isEmpty(entity.getSenderProvinceCode())&&isCode)) {
                throw BusinessException.fail("始发省名称");
            }
            if (ToolUtils.isEmpty(entity.getSenderCityName())||(ToolUtils.isEmpty(entity.getSenderCityCode())&&isCode)) {
                throw BusinessException.fail("始发市名称");
            }
            if (ToolUtils.isEmpty(entity.getSenderDistrictName())||(ToolUtils.isEmpty(entity.getSenderDistrictCode())&&isCode)) {
                throw BusinessException.fail("始发区县名称");
            }
            if (ToolUtils.isEmpty(entity.getSenderTownName())||(ToolUtils.isEmpty(entity.getSenderTownCode())&&isCode)) {
                throw BusinessException.fail("始发乡镇名称");
            }
        }catch (Exception e){
            throw BusinessException.fail(e.getMessage()+errorMessage);
        }
    }

    private void checkOrderBeginDate(String orderTime){
        try{
            if(orderTime.startsWith("-")) {
                orderTime=orderTime.replace("-","");
            }
            DateUtils.parseDate(new SimpleDateFormat(CommonConstant.DAY).format(new Date()).concat(" " + orderTime), CommonConstant.TIME);
            String[] dateSplit=orderTime.split(":");
            if(dateSplit.length<2||!dateSplit[1].equals("00")||!dateSplit[2].equals("00")){
                throw BusinessException.fail("");
            }
        }catch (Exception e){
            throw BusinessException.fail("截单开始时间格式错误,请填写正确整点开始时间、例如：08:00:00整点开始时间");
        }

    }

    private void checkOrderEndDate(String orderTime){
        try{
            DateUtils.parseDate(new SimpleDateFormat(CommonConstant.DAY).format(new Date()).concat(" " + orderTime), CommonConstant.TIME);
            String[] dateSplit=orderTime.split(":");
           if(dateSplit.length<2||!dateSplit[1].equals("59")||!dateSplit[2].equals("59")){
               throw BusinessException.fail("");
           }
        }catch (Exception e){
            throw BusinessException.fail("截单结束时间格式错误,请填写正确结束时间、例如：08:59:59结束时间");
        }

    }

    private List<String> getAddressCode(String _provinceName, String _cityName, String _districtName, String _townName) {
        String provinceName = _provinceName.replace(" ", "");
        String cityName = _cityName.replace(" ", "");
        String districtName = _districtName.replace(" ", "");
        String townName = _townName.replace(" ", "");
        List<String> list = Lists.newArrayList(provinceName, cityName, districtName, townName);
        List<String> addressCode = getAddressCode(list);
        if (CollectionUtils.isEmpty(addressCode) || addressCode.size() != 4) {
            throw BusinessException.fail("获取四级编码失败，解释四级地址：" + list.toString());
        }
        return addressCode;
    }



    private  void setCustomerRelationship(ProductConfigExcelRequest entity,List<MdmDataDictionaryDetail> mdmDictionaryDetailList) {
        EbCustomer ebCustomer=null;
        if(!StringUtils.isEmpty(entity.getCustomerCode())){
            ebCustomer = ebCustomerManager.getEbCustomerCache(entity.getCustomerCode());
            if(ebCustomer==null){
                throw BusinessException.fail("客户编码错误,客户字典里未找到该编码！");
            }
            entity.setCustomerName(ebCustomer.getEbcuNameCn());
        }else if (StringUtils.isEmpty(entity.getCustomerCode())&&!StringUtils.isEmpty(entity.getCustomerName())){
            ebCustomer= queryCustomerName(entity.getCustomerName());
            if(ebCustomer==null){
                throw BusinessException.fail("根据客户名称未找到客户编码！");
            }
            entity.setCustomerCode(ebCustomer.getPmCode());
        }

        if(!StringUtils.isEmpty(entity.getBussinessMode())&&!BusinessMode.B2C.getName().equals(entity.getBussinessMode())&&!BusinessMode.B2B.getName().equals(entity.getBussinessMode())){
            throw BusinessException.fail("业务模式填写错误，业务模式字典里未匹配到！");
        }

        if(!StringUtils.isEmpty(entity.getCustomerCode())&&!StringUtils.isEmpty(entity.getCustomerName())){
            entity.setCustomerGroupName(null);
            entity.setCustomerGroup(null);
        }

        if(!StringUtils.isEmpty(entity.getProductCode())){
            String productCodeName = dictHelper.getDictVaule("PRODUCT_CONFIG",entity.getProductCode());
            if(StringUtils.isEmpty(productCodeName)){
                throw BusinessException.fail("产品编码错误,产品字典里未找到该编码！");
            }
            entity.setProductName(productCodeName);
        }else if (StringUtils.isEmpty(entity.getProductCode())&&!StringUtils.isEmpty(entity.getProductName())){
            String productCode=dictHelper.getDictCodeByName("PRODUCT_CONFIG",entity.getProductName());
            if(StringUtils.isEmpty(productCode)){
                throw BusinessException.fail("根据产品名称未找到产品编码！");
            }
            entity.setProductCode(productCode);
        }

        if(!StringUtils.isEmpty(entity.getProductLineCode())){
            String productLineCodeName = dictHelper.getDictVaule("PRODUCT_LINE_CONFIG",entity.getProductLineCode());
            if(StringUtils.isEmpty(productLineCodeName)){
                throw BusinessException.fail("产品线编码错误,产品线字典里未找到该编码！");
            }
            entity.setProductLineName(productLineCodeName);
        }else if (StringUtils.isEmpty(entity.getProductLineCode())&&!StringUtils.isEmpty(entity.getProductLineName())){
            String productLineCode=dictHelper.getDictCodeByName("PRODUCT_LINE_CONFIG",entity.getProductLineName());
            if(StringUtils.isEmpty(productLineCode)){
                throw BusinessException.fail("根据产品线名称未找到产品线编码！");
            }
            entity.setProductLineCode(productLineCode);
        }

        MdmDataDictionaryDetail mdmDataDictionaryDetail=null;
        if(!StringUtils.isEmpty(entity.getCustomerGroup())){
            mdmDataDictionaryDetail=getMdmDataDictionaryDetail(mdmDictionaryDetailList,entity.getCustomerGroup(),null);
            if(mdmDataDictionaryDetail==null){
                throw BusinessException.fail("客户系填写错误,客户系字典里未找到该编码！");
            }
            entity.setCustomerGroupName(mdmDataDictionaryDetail.getName());
        }else if(StringUtils.isEmpty(entity.getCustomerGroup())&&!StringUtils.isEmpty(entity.getCustomerGroupName())){
            mdmDataDictionaryDetail=getMdmDataDictionaryDetail(mdmDictionaryDetailList,null,entity.getCustomerGroupName());
            if(mdmDataDictionaryDetail==null){
                throw BusinessException.fail("根据客户系名称未找到客户系！");
            }
            entity.setCustomerGroup(mdmDataDictionaryDetail.getCode());
        }
        String productAging=dictHelper.getDictCodeByName("PRES_PRODUCT_CONFIG",entity.getProductAging());
        if(StringUtils.isEmpty(productAging)){
            throw BusinessException.fail("根据时效产品名称未找到时效产品编码！");
        }
        entity.setProductAging(productAging);
        if(!StringUtils.isEmpty(entity.getOrderType())) {
            String orderType = OrderType.getType(entity.getOrderType());
            entity.setOrderType(orderType);
            if (StringUtils.isEmpty(orderType)) {
                throw BusinessException.fail("订单类型填写错误，订单类型字典未匹配到！");
            }
        }
        if(!StringUtils.isEmpty(entity.getDeliveryType())){
            String deliveryType= DeliveryType.getType(entity.getDeliveryType());
            if(StringUtils.isEmpty(deliveryType)){
                throw BusinessException.fail("配送方式填写错误，配送方式字典未匹配到！");
            }
            entity.setDeliveryType(deliveryType);
        }
    }

    private MdmDataDictionaryDetail  getMdmDataDictionaryDetail (List<MdmDataDictionaryDetail> mdmDictionaryDetailList,String codeValue,String codeName){
        for(MdmDataDictionaryDetail mdmDataDictionaryDetail : mdmDictionaryDetailList){
            if(codeValue!=null&&!StringUtils.isEmpty(mdmDataDictionaryDetail.getCode())&&mdmDataDictionaryDetail.getCode().trim().equals(codeValue.trim())&&mdmDataDictionaryDetail.getEnableFlag()==1){
                 return mdmDataDictionaryDetail;
            }
            if(codeName!=null&&!StringUtils.isEmpty(mdmDataDictionaryDetail.getName())&&mdmDataDictionaryDetail.getName().trim().equals(codeName.trim())&&mdmDataDictionaryDetail.getEnableFlag()==1){
                return mdmDataDictionaryDetail;
            }
        }
        return  null;

    }



    private EbCustomer queryCustomerName(String customerName) {
        if(ToolUtils.isEmpty(customerName)) {
            return null;
        }
        EbCustomer ebCustomer = new EbCustomer();
        ebCustomer.setEbcuNameCn(customerName);
        JsonResponse<PageResponse<EbCustomer>> jsonResponse = ebCustomerFeign.searchByEbCustomer(ebCustomer);
        log.info("ebCustomerFeign.searchByEbCustomer==={}",jsonResponse.toString());
        List<EbCustomer> list = jsonResponse.data.list;
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 上传OSS
     * @param file
     * @return
     */
    public JsonResponse importExcel(MultipartFile file) {
        String userCode = iSsoService.getUserCode();
        if (org.apache.commons.lang.StringUtils.isBlank(userCode)) {
            throw BusinessException.fail("用户未登录");
        }

        CostOrderInfoImportFileRequest request = new CostOrderInfoImportFileRequest();
        request.setFileName(file.getOriginalFilename());
        request.setUserCode(userCode);

        String value = redisLockHelper.getValue(CostOrderInfoStatusEnum.PRODUCT_CONFIG_LOCK + request.getLockKey());
        if (null != value) {
            throw BusinessException.fail("[" + file.getOriginalFilename() + "]系统正在导入，请稍等");
        }

        FileUploadBuilder build = FileUploadBuilder.newBuilder().file(file).bucket("c-loms").userCode(userCode).build();
        String upload = lcFileService.upload(build);
        if (org.apache.commons.lang.StringUtils.isBlank(upload)) {
            throw BusinessException.fail("文件上传异常");
        }
        request.setFileUrl(upload);

        return reportFeign.importProductConfig(request);
    }



    /**
     * 以下为校验用户输入的省市区镇
     *
     * @param
     * @return
     */
    public List<String> getAddressCode(List<String> addrs) {
        if (null == addrs || addrs.size() < 3){
            return null;
        }
        List<String> list = Lists.newArrayList();
        if (ToolUtils.isEmpty(addrs.get(0))) {

            log.warn(BaseCodeEnum.REQUEST_NULL.getCode(), "省不能为空");
            return null;
        }
        EbPlace place = queryPlace(addrs.get(0).trim(), "PLACE_PROVINCE", "100000");
        if (place != null) {
            list.add(place.getEbplCode());
            if (ToolUtils.isNotEmpty(addrs.get(1))) {
                place = queryPlace(addrs.get(1).trim(), "PLACE_CITY", place.getEbplCode());
            }
        }else{
            list.add(null);
        }

        if (place != null) {
            list.add(place.getEbplCode());
            if (ToolUtils.isNotEmpty(addrs.get(2))) {
                place = queryPlace(addrs.get(2).trim(), "PLACE_DISTRICT", place.getEbplCode());
            }
        }else{
            list.add(null);
        }
        if (place != null) {
            list.add(place.getEbplCode());
            if (addrs.size() != 4){
                list.add(null);
                return list;
            }
            if (ToolUtils.isNotEmpty(addrs.get(3))) {
                place = queryPlace(addrs.get(3).trim(), "PLACE_STREET", place.getEbplCode());
            } else {
                place = null;
            }
        }else{
            list.add(null);
        }
        if (place == null) {
            if (addrs.size() == 4){
                log.warn(BaseCodeEnum.PARAMETER_INVALID.getCode(), addrs.get(0) + "省" + addrs.get(1) + "市" + addrs.get(2) + "区"   + addrs.get(3) + "不存在");
            }
            log.warn(BaseCodeEnum.PARAMETER_INVALID.getCode(), addrs.get(0) + "省" + addrs.get(1) + "市" + addrs.get(2) + "区"  + "不存在");
            list.add("");
        }else{
            list.add(place.getEbplCode());
        }
        return list;
    }

    /**
     * 查询地点
     */
    private EbPlace queryPlace(String name, String type, String parentCode) {
        EbPlaceRequest ebPlaceRequest = new EbPlaceRequest();
        ebPlaceRequest.setEbplNameCn(name);
        ebPlaceRequest.setEbplType(type);
        ebPlaceRequest.setEbplParentPmCode(parentCode);
        JsonResponse<PageResponse<EbPlace>> jsonResponse = ebPlaceFeign.search(ebPlaceRequest);
        List<EbPlace> ebPlaceList = jsonResponse.data.list;
        EbPlace ebPlace = CollectionUtils.isEmpty(ebPlaceList) ? null : ebPlaceList.get(0);
        if (null !=ebPlace && ToolUtils.isNotEqual(name,ebPlace.getEbplNameCn())){
            ebPlace = null;
        }
        return ebPlace;
    }
}
