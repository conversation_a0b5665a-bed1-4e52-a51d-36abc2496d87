package com.midea.logistics.otp.order.common.aop;

import com.midea.logistics.otp.enums.VdOrderOperateType;

import java.lang.annotation.*;

/**
 * ClassName: VdOrderFlow
 * Description: 增值服务流程注解，用于增值服务订单审核流程的日志记录和异常时状态更新
 *
 * <AUTHOR>
 * @date 2024/5/17 14:34
 */
@Documented
@Inherited
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface VdOrderFlow {
    String value() default "";
    VdOrderOperateType node  ();
}
