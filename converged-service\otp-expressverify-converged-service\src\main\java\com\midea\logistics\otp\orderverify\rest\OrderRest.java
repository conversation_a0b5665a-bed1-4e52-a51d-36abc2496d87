package com.midea.logistics.otp.orderverify.rest;

import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.orderverify.service.StandardProductAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.midea.logistics.cache.manager.ControlParamManager;
import com.midea.logistics.domain.mdm.domain.ControlParam;
import com.midea.logistics.domain.mdm.domain.Task;
import com.midea.logistics.otp.bean.BusinessCateoryDto;
import com.midea.logistics.otp.common.feign.servicefeign.order.CnDispatchPlanFeign;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.enums.BusinessCategory;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderAddressFeign;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.OrderInfoFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.common.service.SeparateWarehouseService;
import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.orderverify.service.CargoRightTransferService;
import com.midea.logistics.otp.orderverify.service.IOrderInfoService;
import com.midea.logistics.otp.orderverify.service.LoadingFeeAnalyzeService;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.RestDoing;

import static com.midea.logistics.otp.order.common.helper.SmallPartsHelper.SMALL_EXPRESS_SITE_CODE;
import static com.midea.logistics.otp.order.common.helper.SmallPartsHelper.VOLUME_CONTROL;

@RestController
public class OrderRest {
    private Logger logger = LoggerFactory.getLogger(OrderRest.class);
    @Autowired
    private CargoRightTransferService cargoRightTransferService;
    @Autowired
    private IOrderInfoService iOrderInfoService;


    @Autowired
    private LoadingFeeAnalyzeService loadingFeeAnalyzeService;
    @Autowired
    private SeparateWarehouseService separateWarehouseService;
    @Autowired
    private CnDispatchPlanFeign cnDispatchPlanFeign;

    @Autowired
    private BusinessCategoryHelper businessCategoryHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;
    @Autowired
    private DeliveryTypeHelper deliveryTypeHelper;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private OrderStatusCheckHelper orderStatusCheckHelper;
    @Autowired
    private SmallPartsHelper smallPartsHelper;
    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private ControlParamManager controlParamManager;
    @Autowired
    private StandardProductAnalysisService standardProductAnalysisService;


    /**
     * 计费等商品信息审核
     * @param orderNo
     * @param request
     * @return
     */
    @GetMapping("/auditItemsByFlow")
    public JsonResponse auditItemsByFlow(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            cargoRightTransferService.auditItemsByFlow(null);
        };
        return doing.go(request, logger);
    }



    /**
     * 合同校验
     *
     * @param orderNo
     * @param request
     * @return
     */
    @GetMapping(OrderConvergedRouters.CONTRACT_VERIFICATION)
    public JsonResponse contractVerify(@RequestParam("orderNo")String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            OrderInfo search = new OrderInfo();
            search.setOrderNo(orderNo);
            OrderInfo origin = orderInfoFeign.selectOne(search).data;
            OrderInfoExt orderInfo = new OrderInfoExt();
            BeanUtils.copyProperties(origin,orderInfo);
            JsonResponse flowListenerParam = iOrderInfoService.contractVerify(orderInfo);
            jsonResponse.data = flowListenerParam;
        };
        return doing.go(request, logger);
    }



    /**
     * 装卸计费解析
     * @param orderNo
     * @param request
     * @return
     */
    @GetMapping(OrderConvergedRouters.LOADING_FEE_ANALYZE)
    public JsonResponse loadingFeeAnalyze(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        OrderInfo search = new OrderInfo();
        search.setOrderNo(orderNo);
        OrderInfo orderInfo = orderInfoFeign.selectOne(search).data;
        RestDoing doing = jsonResponse -> jsonResponse.data = loadingFeeAnalyzeService.loadingFeeAnalyzeRedis(orderInfo);
        return doing.go(request, logger);
    }


    /**
     * 同步查单
     * @param orderNo
     * @param request
     * @return
     */
    @PostMapping(OrderConvergedRouters.ORDER_INFO_PUSH_LOTS)
    public JsonResponse pushLots(@RequestParam("orderNo")String orderNo, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            OrderInfo search = new OrderInfo();
            search.setOrderNo(orderNo);
            OrderInfo orderInfo = orderInfoFeign.selectOne(search).data;
            jsonResponse.data = iOrderInfoService.pushLots(orderInfo);
        };
        return doing.go(request, logger);
    }

    /**
     * 自动分仓
     * @param orderNo
     * @param request
     * @return
     */
    @PostMapping(OrderConvergedRouters.SEPARATE_WAREHOUSE_AUTOMATIC)
    public JsonResponse<FlowListenerParam> automatic(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = separateWarehouseService.automatic(orderNo);
        };
        return doing.go(request, logger);
    }





    @PostMapping(OrderConvergedRouters.BUSINESS_CATEGORY)
    public JsonResponse<BusinessCategory> getBusinessCategory(@RequestBody BusinessCateoryDto businessCateoryDto, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            jsonResponse.data =  businessCategoryHelper.getBusinessCategory(businessCateoryDto);
        };
        return doing.go(request, logger);
    }

    /**
     * 获取配送方式测试
     * @param orderNo
     * @param request
     * @return
     */
    @GetMapping("/getDeliveryRule")
    public JsonResponse getDeliveryRule(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            CustomerOrderInfo customerOrderInfo = orderFlowHelper.getCustomerOrderInfo(orderNo);
            CustomerOrderAddress address = customerOrderAddressFeign.findByOrderNo(orderNo).data;
            String deliveryRule = deliveryTypeHelper.getDeliveryRule(customerOrderInfo,null, address);
            jsonResponse.data = deliveryRule;
        };
        return doing.go(request, logger);
    }

    /**
     * 获取配送方式测试
     * @param orderNo
     * @param request
     * @return
     */
    @GetMapping("/getDeliveryRuleByOrderInfo")
    public JsonResponse getDeliveryRuleByOrderInfo(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            OrderInfo search = new OrderInfo();
            search.setOrderNo(orderNo);
            OrderInfo orderInfo = orderInfoFeign.selectOne(search).data;
            CustomerOrderInfo customerOrderInfo = orderFlowHelper.getCustomerOrderInfo(orderNo);
            String deliveryRule = deliveryTypeHelper.getDeliveryRule(customerOrderInfo,orderInfo);
            jsonResponse.data = deliveryRule;
        };
        return doing.go(request, logger);
    }

    /**
     * 小件配送方式排查
     * @param orderInfo
     * @param request
     * @return
     */
    @PostMapping(OrderConvergedRouters.SMALL_EXPRESS_DELIVERY)
    public JsonResponse smallExpressDelivery(@RequestBody OrderInfo orderInfo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            HashMap<String,Object> map = new HashMap();
            String parentOrderNo = orderInfo.getParentOrderNo();
            String orderNo = orderInfo.getOrderNo();
            CustomerOrderInfo customerOrderInfo = null;
            OrderInfo targetOrderInfo = null;
            if (ToolUtils.isNotEmpty(orderNo)) {
                OrderInfo search = new OrderInfo();
                search.setOrderNo(orderNo);
                targetOrderInfo = orderInfoFeign.selectOne(search).data;
                customerOrderInfo = customerOrderInfoFeign.getOrderInfoByOrderNos(targetOrderInfo.getParentOrderNo()).data;

            }else{
                customerOrderInfo = customerOrderInfoFeign.getOrderInfoByOrderNos(parentOrderNo).data;
            }

            ControlParam controlParam = controlParamManager.getCache(VOLUME_CONTROL);
            map.put("体积参数m³",ToolUtils.isNotEmpty(controlParam)? controlParam.getValue()+"m³":"暂无配置");
            ControlParam siteCodecontrolParam = controlParamManager.getCache(SMALL_EXPRESS_SITE_CODE);
            map.put("小件平台参数",ToolUtils.isNotEmpty(siteCodecontrolParam)? siteCodecontrolParam.getValue():"暂无配置");

            boolean smallPiece = smallPartsHelper.isSmallExpress(customerOrderInfo);
            boolean inSmallPipeSiteCode = smallPartsHelper.isInSmallExpressSiteCode(customerOrderInfo);
            map.put("父单小件标识",smallPiece );
            map.put("是否小件平台(是-快递，否-直配)",inSmallPipeSiteCode);
            String pd = redisHelper.getB2cPoSmallExpressDeliveryType(customerOrderInfo.getOrderNo());
            map.put("父单（小件）配送方式",ToolUtils.isNotEmpty(pd)?pd:"空");

            if (null != targetOrderInfo){
                String sd = redisHelper.getB2cPoSmallExpressDeliveryType(targetOrderInfo.getOrderNo());
                map.put("子单（小件）配送方式",ToolUtils.isNotEmpty(sd)?sd:"空" );
            }
            jsonResponse.setData(map);
        };
        return doing.go(request, logger);
    }

    /**
     * 测试是否有redis取消锁
     * @param task
     * @param request
     * @return
     */
    @PostMapping("/checkMultipyOrderLock")
    public JsonResponse checkMultipyOrderLock(@RequestBody Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            boolean  orderLock = orderStatusCheckHelper.checkMultipyOrderLock(task.getCustomerOrderNo(),task.getParentOrderNo(),task.getOrderNo(),task.getTaskNo());
            jsonResponse.data = orderLock;
        };
        return doing.go(request, logger);
    }

    /**
     * 标准产品解析
     * @return
     */

    @GetMapping("/standardProductAnalysisByOrderNo")
    public JsonResponse standardProductAnalysis(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        CustomerOrderInfoExt customerOrderInfo = orderFlowHelper.getCustomerOrderInfoExt(orderNo);
        return JsonResponse.success(standardProductAnalysisService.standardProductAnalysis(customerOrderInfo));
    }

}
