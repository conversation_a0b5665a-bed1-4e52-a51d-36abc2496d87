package com.midea.logistics.otp.order.common.fegin.fallback;

import com.midea.logistics.domain.mdm.domain.MidSiteWhControl;
import com.midea.logistics.otp.order.common.fegin.MidSiteWhControlFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年07月23日 上午10:30
 */
@Slf4j
@Component
public class MidSiteWhControlFeignFallBack implements FallbackFactory<MidSiteWhControlFeign> {
    @Override
    public MidSiteWhControlFeign create(Throwable throwable) {
        return new MidSiteWhControlFeign() {
            @Override
            public JsonResponse<MidSiteWhControl> getByWhCode(String customerCode, String whCode) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }

            @Override
            public JsonResponse<PageResponse<MidSiteWhControl>> searchPage(MidSiteWhControl midSiteWhControl) {
                JsonResponse jsonResponse = new JsonResponse();
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(throwable.getMessage());
                return jsonResponse;
            }
        };
    }
}
