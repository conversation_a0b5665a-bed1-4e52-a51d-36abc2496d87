package com.midea.logistics.otplbs.config;

import com.mideaframework.core.utils.http.HttpContext;
import com.mideaframework.sdk.helper.OkHttpCallHelper;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * OTP 定制
 */
@Configuration
public class OtpFeignHeaderInterceptor implements RequestInterceptor {
    @Autowired
    private OkHttpCallHelper okHttpCallHelper;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        HttpServletRequest request = null;
        try {
            request = HttpContext.getRequest();
        } catch (NullPointerException e) {
            return;
        }
        Set<String> headerSets = new HashSet<>();
        if (request != null) {
            Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();
                    String values = request.getHeader(name);
                    if ("Content-Type".equalsIgnoreCase(name)){
                        continue;
                    }
                    if (!headerSets.contains(name)) {
                        requestTemplate.header(name, values);
                        headerSets.add(name);
                    }

                }
            }
            if (HttpMethod.GET.name().equals(requestTemplate.method())) {
                Map<String, String[]> requestMap = request.getParameterMap();
                Map<String, Collection<String>> queries = new HashMap<>();
                Map<String, Collection<String>> map = requestTemplate.queries();
                if (!CollectionUtils.isEmpty(requestMap)) {
                    for (String key : requestMap.keySet()) {
                        String[] reqStrings = requestMap.get(key);
                        if (reqStrings != null && reqStrings.length > 0 && map.get(key) == null) {
                            queries.put(key, Arrays.asList(reqStrings));
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(queries)) {
                    requestTemplate.queries(queries);
                }
            }
            Map<String, String> headers = okHttpCallHelper.populateHeaders();
            if (!CollectionUtils.isEmpty(headers)) {
                for (String key : headers.keySet()) {
                    if (!headerSets.contains(key)) {
                        requestTemplate.header(key, headers.get(key));
                        headerSets.add(key);
                    }

                }
            }
        }

    }
}
