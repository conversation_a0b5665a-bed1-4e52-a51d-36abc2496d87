<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all-qp2c" name="父订单流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0b9or4c</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_0a0g75l" name="MIP流程">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/mipExecutionAudit" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zdzxft</bpmn:incoming>
      <bpmn:outgoing>Flow_0ap55e3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_12lns6w" name="是否走mip流程" default="Flow_1ajskls">
      <bpmn:incoming>Flow_0b9or4c</bpmn:incoming>
      <bpmn:outgoing>Flow_1ajskls</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zdzxft</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ajskls" name="no" sourceRef="Gateway_12lns6w" targetRef="Gateway_1yfd9e1" />
    <bpmn:sequenceFlow id="Flow_0zdzxft" name="yes" sourceRef="Gateway_12lns6w" targetRef="Activity_0a0g75l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.mipFlag!=null and customerOrderInfo.mipFlag=1)=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0nccbee" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="receive" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1uewv0z</bpmn:incoming>
      <bpmn:outgoing>Flow_07qlm7g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ap55e3" sourceRef="Activity_0a0g75l" targetRef="Gateway_1yfd9e1" />
    <bpmn:serviceTask id="Activity_119zmlg" name="仓间调拨解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/allocationParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="allocationParse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1n6udmd</bpmn:incoming>
      <bpmn:outgoing>Flow_0v8tmtg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/addressResolving" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k9k1b7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wi0xbe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kptmf2" name="货权转移校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/cargo/right/transfer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p7943l</bpmn:incoming>
      <bpmn:outgoing>Flow_1wuusbt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0kj1gz5</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1g2qvk8" name="是否仓间调拨" default="Flow_1odmwry">
      <bpmn:incoming>Flow_07qlm7g</bpmn:incoming>
      <bpmn:incoming>Flow_1eh4qu3</bpmn:incoming>
      <bpmn:outgoing>Flow_1n6udmd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1odmwry</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07qlm7g" sourceRef="Activity_0nccbee" targetRef="Gateway_1g2qvk8" />
    <bpmn:sequenceFlow id="Flow_1n6udmd" name="yes" sourceRef="Gateway_1g2qvk8" targetRef="Activity_119zmlg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO") or (customerOrderInfo.sourceSystem="TTX" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AO")) or (customerOrderInfo.sourceSystem="OFC" and customerOrderInfo.orderType="AO"))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_04dmfyf" name="是否地址解析" default="Flow_191u9lk">
      <bpmn:incoming>Flow_1odmwry</bpmn:incoming>
      <bpmn:incoming>Flow_0v8tmtg</bpmn:incoming>
      <bpmn:outgoing>Flow_191u9lk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k9k1b7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1odmwry" sourceRef="Gateway_1g2qvk8" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_0v8tmtg" sourceRef="Activity_119zmlg" targetRef="Gateway_04dmfyf" />
    <bpmn:exclusiveGateway id="Gateway_0fo5mo8" name="是否货权转移" default="Flow_1o83mga">
      <bpmn:incoming>Flow_13i1do9</bpmn:incoming>
      <bpmn:incoming>Flow_044v17i</bpmn:incoming>
      <bpmn:outgoing>Flow_1o83mga</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p7943l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_191u9lk" name="no" sourceRef="Gateway_04dmfyf" targetRef="Activity_06h0trf" />
    <bpmn:sequenceFlow id="Flow_1wi0xbe" sourceRef="Activity_1i377su" targetRef="Activity_06h0trf" />
    <bpmn:exclusiveGateway id="Gateway_1b565p2" name="是否分仓拆单">
      <bpmn:incoming>Flow_1o83mga</bpmn:incoming>
      <bpmn:incoming>Flow_1wuusbt</bpmn:incoming>
      <bpmn:outgoing>Flow_0kj1gz5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o83mga" name="no" sourceRef="Gateway_0fo5mo8" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_1k9k1b7" name="yes" sourceRef="Gateway_04dmfyf" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and (((customerOrderInfo.deliveryType!=null)=false or (customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT")) and (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO") and (customerOrderInfo.orderSource!=null and customerOrderInfo.orderSource!="HANDLE")))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1p7943l" name="yes" sourceRef="Gateway_0fo5mo8" targetRef="Activity_0kptmf2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CAINIAOPO","CCSJDPI","CCSPO","CCSPI","CIMSPO","CIMSAI","CIMSPI","CIMSRI","PDDRO","PDDPI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wuusbt" sourceRef="Activity_0kptmf2" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_0kj1gz5" name="no" sourceRef="Gateway_1b565p2" targetRef="Activity_0uruim6" />
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Event_09zbrsb" />
    <bpmn:exclusiveGateway id="Gateway_1yfd9e1" name="是否走收货单位解析" default="Flow_1eh4qu3">
      <bpmn:incoming>Flow_1ajskls</bpmn:incoming>
      <bpmn:incoming>Flow_0ap55e3</bpmn:incoming>
      <bpmn:outgoing>Flow_1uewv0z</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eh4qu3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1uewv0z" name="yes" sourceRef="Gateway_1yfd9e1" targetRef="Activity_0nccbee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CIMSPO","CCSPO","CAINIAOAO","CAINIAOYS","CIMSRI","OFCPO","OFCAO"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1eh4qu3" name="no" sourceRef="Gateway_1yfd9e1" targetRef="Gateway_1g2qvk8" />
    <bpmn:serviceTask id="Activity_07ox4pt" name="业务大类解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/businessCategory" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1l2txqb</bpmn:incoming>
      <bpmn:outgoing>Flow_08sq54w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_08sq54w" sourceRef="Activity_07ox4pt" targetRef="Gateway_07sc21e" />
    <bpmn:sequenceFlow id="Flow_13i1do9" sourceRef="Activity_1iziya6" targetRef="Gateway_0fo5mo8" />
    <bpmn:serviceTask id="Activity_1iziya6" name="服务产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/productConfig" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ctnvkr</bpmn:incoming>
      <bpmn:outgoing>Flow_13i1do9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_07sc21e" name="禁用服务产品" default="Flow_044v17i">
      <bpmn:incoming>Flow_08sq54w</bpmn:incoming>
      <bpmn:outgoing>Flow_044v17i</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ctnvkr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ctnvkr" sourceRef="Gateway_07sc21e" targetRef="Activity_1iziya6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_044v17i" sourceRef="Gateway_07sc21e" targetRef="Gateway_0fo5mo8" />
    <bpmn:sequenceFlow id="Flow_0b9or4c" sourceRef="StartEvent_1" targetRef="Gateway_12lns6w" />
    <bpmn:serviceTask id="Activity_06h0trf" name="标准产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/standardProductAnalysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wi0xbe</bpmn:incoming>
      <bpmn:incoming>Flow_191u9lk</bpmn:incoming>
      <bpmn:outgoing>Flow_1l2txqb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1l2txqb" sourceRef="Activity_06h0trf" targetRef="Activity_07ox4pt" />
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all-qp2c">
      <bpmndi:BPMNEdge id="Flow_0b9or4c_di" bpmnElement="Flow_0b9or4c">
        <di:waypoint x="188" y="230" />
        <di:waypoint x="266" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_044v17i_di" bpmnElement="Flow_044v17i">
        <di:waypoint x="990" y="495" />
        <di:waypoint x="990" y="550" />
        <di:waypoint x="800" y="550" />
        <di:waypoint x="800" y="475" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ctnvkr_di" bpmnElement="Flow_0ctnvkr">
        <di:waypoint x="965" y="470" />
        <di:waypoint x="940" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13i1do9_di" bpmnElement="Flow_13i1do9">
        <di:waypoint x="840" y="470" />
        <di:waypoint x="805" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08sq54w_di" bpmnElement="Flow_08sq54w">
        <di:waypoint x="1050" y="470" />
        <di:waypoint x="1015" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eh4qu3_di" bpmnElement="Flow_1eh4qu3">
        <di:waypoint x="461" y="205" />
        <di:waypoint x="461" y="150" />
        <di:waypoint x="670" y="150" />
        <di:waypoint x="670" y="225" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="559" y="132" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uewv0z_di" bpmnElement="Flow_1uewv0z">
        <di:waypoint x="486" y="230" />
        <di:waypoint x="528" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="499" y="207" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="420" y="470" />
        <di:waypoint x="298" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kj1gz5_di" bpmnElement="Flow_0kj1gz5">
        <di:waypoint x="575" y="470" />
        <di:waypoint x="520" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="560" y="513" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wuusbt_di" bpmnElement="Flow_1wuusbt">
        <di:waypoint x="640" y="620" />
        <di:waypoint x="600" y="620" />
        <di:waypoint x="600" y="495" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p7943l_di" bpmnElement="Flow_1p7943l">
        <di:waypoint x="780" y="495" />
        <di:waypoint x="780" y="620" />
        <di:waypoint x="740" y="620" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="787" y="557" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9k1b7_di" bpmnElement="Flow_1k9k1b7">
        <di:waypoint x="865" y="230" />
        <di:waypoint x="1050" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="897" y="213" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o83mga_di" bpmnElement="Flow_1o83mga">
        <di:waypoint x="755" y="470" />
        <di:waypoint x="625" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="696" y="452" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wi0xbe_di" bpmnElement="Flow_1wi0xbe">
        <di:waypoint x="1100" y="270" />
        <di:waypoint x="1100" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191u9lk_di" bpmnElement="Flow_191u9lk">
        <di:waypoint x="840" y="255" />
        <di:waypoint x="840" y="360" />
        <di:waypoint x="1050" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="944" y="338" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v8tmtg_di" bpmnElement="Flow_0v8tmtg">
        <di:waypoint x="821" y="151" />
        <di:waypoint x="840" y="151" />
        <di:waypoint x="840" y="205" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1odmwry_di" bpmnElement="Flow_1odmwry">
        <di:waypoint x="715" y="230" />
        <di:waypoint x="815" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n6udmd_di" bpmnElement="Flow_1n6udmd">
        <di:waypoint x="690" y="205" />
        <di:waypoint x="690" y="151" />
        <di:waypoint x="721" y="151" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="695" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07qlm7g_di" bpmnElement="Flow_07qlm7g">
        <di:waypoint x="628" y="230" />
        <di:waypoint x="665" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ap55e3_di" bpmnElement="Flow_0ap55e3">
        <di:waypoint x="420" y="150" />
        <di:waypoint x="440" y="150" />
        <di:waypoint x="440" y="226" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zdzxft_di" bpmnElement="Flow_0zdzxft">
        <di:waypoint x="291" y="205" />
        <di:waypoint x="291" y="150" />
        <di:waypoint x="320" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="295" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ajskls_di" bpmnElement="Flow_1ajskls">
        <di:waypoint x="316" y="230" />
        <di:waypoint x="436" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="396" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l2txqb_di" bpmnElement="Flow_1l2txqb">
        <di:waypoint x="1100" y="390" />
        <di:waypoint x="1100" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="212" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="262" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="269" y="428" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0eropt8_di" bpmnElement="Activity_0a0g75l">
        <dc:Bounds x="320" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12lns6w_di" bpmnElement="Gateway_12lns6w" isMarkerVisible="true">
        <dc:Bounds x="266" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="258" y="262" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g3mnwv_di" bpmnElement="Activity_0nccbee">
        <dc:Bounds x="528" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s5niqa_di" bpmnElement="Activity_119zmlg">
        <dc:Bounds x="721" y="111" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="1050" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="420" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g2qvk8_di" bpmnElement="Gateway_1g2qvk8" isMarkerVisible="true">
        <dc:Bounds x="665" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="660" y="262" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04dmfyf_di" bpmnElement="Gateway_04dmfyf" isMarkerVisible="true">
        <dc:Bounds x="815" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="848" y="203" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yfd9e1_di" bpmnElement="Gateway_1yfd9e1" isMarkerVisible="true">
        <dc:Bounds x="436" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="424" y="262" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b565p2_di" bpmnElement="Gateway_1b565p2" isMarkerVisible="true">
        <dc:Bounds x="575" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="569" y="424" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07zfya5_di" bpmnElement="Activity_0kptmf2">
        <dc:Bounds x="640" y="580" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fo5mo8_di" bpmnElement="Gateway_0fo5mo8" isMarkerVisible="true">
        <dc:Bounds x="755" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="747" y="421" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bhkf7k_di" bpmnElement="Activity_1iziya6">
        <dc:Bounds x="840" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07sc21e_di" bpmnElement="Gateway_07sc21e" isMarkerVisible="true">
        <dc:Bounds x="965" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="957" y="421" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w1igdz_di" bpmnElement="Activity_07ox4pt">
        <dc:Bounds x="1050" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06h0trf_di" bpmnElement="Activity_06h0trf">
        <dc:Bounds x="1050" y="310" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
