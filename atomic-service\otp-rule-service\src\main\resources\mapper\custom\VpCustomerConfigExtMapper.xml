<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.VpCustomerConfigMapper">

    <select id="verifyUniqueConfig" resultType="com.midea.logistics.otp.rule.domain.bean.VpCustomerConfig">
        select
         `id` AS  id
        from vp_customer_config t
        <where>
            delete_flag=0
            <if test="customerCode !=null and customerCode != ''">
                and `customer_code` =#{customerCode}
            </if>
            <if test="siteCode !=null and siteCode != ''">
                and `site_code` =#{siteCode}
            </if>
            <if test="siteCode == null or siteCode == ''">
                and (`site_code` is null or `site_code` = '')
            </if>
            <if test="receiverName !=null and receiverName != ''">
                and `receiver_name` =#{receiverName}
            </if>
            <if test="receiverName == null or receiverName == ''">
                and (`receiver_name` is null or `receiver_name` = '')
            </if>
            <if test="upperReceiverName !=null and upperReceiverName != ''">
                and `upper_receiver_name` =#{upperReceiverName}
            </if>
            <if test="upperReceiverName == null or upperReceiverName == ''">
                and (`upper_receiver_name` is null or `upper_receiver_name` = '')
            </if>
        </where>
        limit 1
    </select>


    <select id="searchPageCount"  resultType="Integer">
        select count(*)
        from vp_customer_config t
        <include refid="whereFieldsSql"/>
        <include refid="whereSearchPage"/>
    </select>

    <select id="searchPage" resultType="com.midea.logistics.otp.rule.domain.bean.VpCustomerConfig">
        select
        <include refid="searchFieldsSql"/>
        from vp_customer_config t
        <include refid="whereFieldsSql"/>
        <include refid="whereSearchPage"/>

        order by create_time desc
        limit ${start},${pageSize}
    </select>

    <sql id="whereSearchPage">

        <if test="siteCodeList != null and siteCodeList.size > 0">
            AND site_code  IN <foreach collection="siteCodeList" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>
        <if test="companyCodeList != null and companyCodeList.size > 0">
            AND company_code  IN <foreach collection="companyCodeList" item="item" open="(" separator="," close=")"> #{item} </foreach>
        </if>

        <choose>
            <!--  客户权限 -->
            <when test="customerAuthFlag == 1 and customerCodeList !=null and customerCodeList.size()>0">
                and customer_code in
                <foreach collection="customerCodeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>

            <!--  其他 权限 -->
            <otherwise>

            </otherwise>
        </choose>

    </sql>

    <select id="getVpCustomerConfigFlag"
            resultType="com.midea.logistics.otp.rule.domain.response.VpCustomerConfigRes">
        select *
        from (
            select
                concat(
                <!--1、客户+平台+收货单位+收件人, com.midea.logistics.otp.order.common.helper.OrderverifyHelper.getVipFlag  用户画像如果匹配到第一条 可设值缓存-->
                case when #{siteCode} is not null and #{siteCode} != '' and #{siteCode} = site_code
                and #{receiverName} is not null and #{receiverName} != '' and  #{receiverName} = receiver_name
                and #{upperReceiverName} is not null and #{upperReceiverName} != '' and  #{upperReceiverName} = upper_receiver_name
                then 1 else 0 end
                ,
                <!--2、客户+平台+收货单位-->
                case when #{siteCode} is not null and #{siteCode} != '' and #{siteCode} = site_code
                and (`receiver_name` is null or `receiver_name` = '')
                and #{upperReceiverName} is not null and #{upperReceiverName} != '' and  #{upperReceiverName} = upper_receiver_name
                then 1 else 0 end
                ,
                <!--3、客户+平台+收件人-->
                case when #{siteCode} is not null and #{siteCode} != '' and #{siteCode} = site_code
                and #{receiverName} is not null and #{receiverName} != '' and  #{receiverName} = receiver_name
                and (`upper_receiver_name` is null or `upper_receiver_name` = '')
                then 1 else 0 end
                ,
                <!--4、客户+平台-->
                case when #{siteCode} is not null and #{siteCode} != '' and #{siteCode} = site_code
                and (`receiver_name` is null or `receiver_name` = '')
                and (`upper_receiver_name` is null or `upper_receiver_name` = '')
                then 1 else 0 end
                ,
                <!--4、客户-->
                case when (`site_code` is null or `site_code` = '')
                and (`receiver_name` is null or `receiver_name` = '')
                and (`upper_receiver_name` is null or `upper_receiver_name` = '')
                then 1 else 0 end
                )  as ranking,
            `id` AS  id,
            `user_image_logo_name` AS  userImageLogoName,
            `emergency_remark` AS  emergencyRemark
            from
            vp_customer_config
            <where>
                delete_flag = 0
                and `customer_code` = #{customerCode}
                and `enable_flag` = 1
            </where>
            order by ranking desc
            limit 1
        ) t
        where ranking > 0
    </select>

    <update id="batchUpdateCanSetEmpty">
        <foreach collection="list" item="item" separator=";">
            UPDATE vp_customer_config
            <include refid="setBatchFieldsSqlCanSetEmpty"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

</mapper>