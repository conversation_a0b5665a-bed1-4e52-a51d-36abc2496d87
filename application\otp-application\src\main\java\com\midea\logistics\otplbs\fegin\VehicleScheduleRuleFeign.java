package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.common.helper.bean.VehicleScheduleRuleExcelRequest;
import com.midea.logistics.otp.dispatch.domain.DispatchRouters;
import com.midea.logistics.otp.report.service.domain.bean.VehicleScheduleRuleRequest;
import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.VehicleScheduleRule;
import com.midea.logistics.otp.rule.domain.bean.custom.VehicleScheduleRuleDto;
import com.midea.logistics.otplbs.config.FeignMultipartSupportConfig;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: VehicleScheduleRuleFeign
 * <AUTHOR>
 * Date 2020-11-19 11:21:09
 * Description:班次配置Feign
 */
@FeignClient(value = "logistics-otp-rule-service",url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface VehicleScheduleRuleFeign {

    /**
     * 查询集合
     */
    @RequestMapping(value = OptRuleRouters.VEHICLE_SCHEDULE_RULE_LIST, method = RequestMethod.POST)
    public JsonResponse search(@RequestBody VehicleScheduleRule vehicleScheduleRule);

    /**
     * 批量新增或者保存
     */
    @RequestMapping(value = OptRuleRouters.VEHICLE_SCHEDULE_RULE_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    public JsonResponse batchCreateOrUpdate(@RequestBody List<VehicleScheduleRuleDto> ruleDtos);

    /**
     * 删除
     */
    @RequestMapping(value = OptRuleRouters.VEHICLE_SCHEDULE_RULE_DELETEBYIDS, method = RequestMethod.DELETE)
    public JsonResponse deleteByIds(@RequestBody List<Long> ids);

    /**
     * 导入班次配置
     */
    @PostMapping(value = OptRuleRouters.VEHICLE_SCHEDULE_RULE_IMPORT, produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    void importVehicleScheduleRule(@RequestBody MultipartFile file);

    /**
     * 查询有效时间内的集合
     */
    @RequestMapping(value = OptRuleRouters.VEHICLE_SCHEDULE_RULE_TIME_LIST, method = RequestMethod.POST)
    public JsonResponse validTimeRules(@RequestBody VehicleScheduleRule vehicleScheduleRule);

    /**
     * 新增
     */
    @PostMapping(OptRuleRouters.VEHICLE_SCHEDULE_RULE_SAVE)
    public JsonResponse create(@SpringQueryMap VehicleScheduleRuleExcelRequest request);

    /**
     * 导入批量插入
     */
    @RequestMapping(value = OptRuleRouters.VEHICLE_SCHEDULE_RULE_IMPORT_BATCH_INSERT, method = RequestMethod.POST)
    public JsonResponse importBatchInsert(@RequestBody List<VehicleScheduleRuleExcelRequest> vehicleScheduleRules);
}
