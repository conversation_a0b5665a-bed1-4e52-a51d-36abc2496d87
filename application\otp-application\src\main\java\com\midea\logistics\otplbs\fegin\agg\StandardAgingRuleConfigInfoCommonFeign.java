package com.midea.logistics.otplbs.fegin.agg;

import com.midea.logistics.otp.order.domain.bean.StandardAgingRuleConfigInfo;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "StandardAgingRuleConfigInfoCommonFeign")
public interface StandardAgingRuleConfigInfoCommonFeign {

    @GetMapping("/standard/aging/rule/config/info/list")
    JsonResponse<List<StandardAgingRuleConfigInfo>> standardAgingRuleConfigInfoList(@SpringQueryMap StandardAgingRuleConfigInfo entity);

    @GetMapping("/standard/aging/rule/config/info/page")
    JsonResponse standardAgingRuleConfigInfoPage(@SpringQueryMap StandardAgingRuleConfigInfo entity);

    @GetMapping("/standard/aging/rule/config/info/count")
    JsonResponse<BaseDomain> standardAgingRuleConfigInfoCount(@SpringQueryMap StandardAgingRuleConfigInfo entity);

    @GetMapping("/standard/aging/rule/config/info/detail")
    JsonResponse<StandardAgingRuleConfigInfo> standardAgingRuleConfigInfoDetail(@RequestParam("id") Long id);

    @GetMapping("/standard/aging/rule/config/info/detail")
    JsonResponse<StandardAgingRuleConfigInfo> standardAgingRuleConfigInfoDetail(@SpringQueryMap StandardAgingRuleConfigInfo entity);

    @PostMapping("/standard/aging/rule/config/info/add")
    JsonResponse<BaseDomain> standardAgingRuleConfigInfoAdd(@RequestBody StandardAgingRuleConfigInfo entity);

    @PutMapping("/standard/aging/rule/config/info/update")
    JsonResponse<Integer> standardAgingRuleConfigInfoUpdate(@RequestBody StandardAgingRuleConfigInfo entity);

    @DeleteMapping("/standard/aging/rule/config/info/remove")
    JsonResponse<Integer> standardAgingRuleConfigInfoRemove(@RequestBody StandardAgingRuleConfigInfo entity);

}

