package com.midea.logistics.otp.ttx.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.midea.logistics.otp.common.feign.servicefeign.dispatch.BopFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.enums.CostOrderInfoStatusEnum;
import com.midea.logistics.otp.ttx.domain.OrderCodeEnum;
import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder;
import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrderHead;
import com.midea.logistics.otp.ttx.inventory.domain.request.ReceiptDataTTXRequest;
import com.midea.logistics.otp.ttx.inventory.domain.response.ReceiptSaveTTXItem;
import com.midea.logistics.otp.ttx.service.IInboundOrderHeadService;
import com.midea.logistics.otp.ttx.service.IReceiptInventoryTTXService;
import com.midea.logistics.otp.ttx.service.InboundOrderService;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.wkclz.util.excel.Excel;
import com.wkclz.util.excel.ExcelException;
import com.wkclz.util.excel.ExcelRow;
import okhttp3.*;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-8-22 13:55
 */
@Component
public class InboundOrderManager {

    private static final Logger logger = LoggerFactory.getLogger(InboundOrderManager.class);

    @Autowired
    private InboundOrderService inboundOrderService;
    @Autowired
    private IReceiptInventoryTTXService iReceiptInventoryTTXService;
    @Autowired
    private IInboundOrderHeadService iInboundOrderHeadService;

    @Autowired
    private OssManager ossManager;

    private final BopFeign bopService;

    @Value("${spring.wms.checkIsShelfUrl}")
    private String checkIsShelfUrl;

    private String upErrorMsg="调用确认下发接口失败";

    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private UpDownloadHelper upDownloadHelper;
    @Autowired
    private ApplicationContext applicationContext;

    public InboundOrderManager(BopFeign bopService) {
        this.bopService = bopService;
    }

    /**
     * @Description: 判断cell里面的值类型，然后统一返回字符串。customer列和lpn列可空 ,日期三列也可空
     * @Param: [cell]
     * @return: java.lang.String
     * @Author: yaowl
     * @Date: 2019-9-20
     */
    private String getCellValue(Cell cell, int num) {
        String cellValue;
        if (cell == null && num >=10) {
            return "";
        } else if (cell == null) {
            throw BusinessException.fail("存在其它类型的单元格或空单元格");
        }
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getStringCellValue().trim();
                break;
            case NUMERIC:
            case FORMULA:
                NumberFormat nf = NumberFormat.getInstance();
                cellValue = nf.format(cell.getNumericCellValue()) + "";
                if (cellValue.indexOf(',') >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            default:
                cellValue = "";
        }
        //前10列必填，非空
        if (("").equals(cellValue) && num<10) {
            throw BusinessException.fail("存在其它类型的单元格或空单元格");
        }
        return cellValue;
    }

    /**
     * @Description: 将Excel一行的数据转为ReceiptDataTTXRequest对象
     * @Param: [row]
     * @return: com.midea.logistics.otp.ttx.inventory.domain.request.ReceiptDataTTXRequest
     * @Author: yaowl
     * @Date: 2019-9-20
     */
    private ReceiptDataTTXRequest convertRowData(int lineNum, Row row) {
        if (row == null) {
            return null;
        }
        ReceiptDataTTXRequest request = new ReceiptDataTTXRequest();
        request.setCode(getCellValue(row.getCell(0), 0));
        request.setWarehouseCode(getCellValue(row.getCell(1), 1));
        request.setCompanyCode(getCellValue(row.getCell(2), 2));
        request.setReceiptType(getCellValue(row.getCell(3), 3));
        request.setItemCode(getCellValue(row.getCell(4), 4));
        request.setItemName(getCellValue(row.getCell(5), 5));
        int tqy = Integer.parseInt(getCellValue(row.getCell(6), 6));
        request.setTotalQty(tqy);
        request.setInventorySts(getCellValue(row.getCell(7), 7));
        request.setToLocation(getCellValue(row.getCell(8), 8));
        request.setBatch(getCellValue(row.getCell(9), 9));
        //新增三个日期字段
        request.setManufactureDate(validDate(getCellValue(row.getCell(10), 10)));
        request.setExpirationDate(validDate(getCellValue(row.getCell(11), 11)));
        request.setAgingDate(validDate(getCellValue(row.getCell(12), 12)));

        request.setCustomerCode(getCellValue(row.getCell(13), 13));
        request.setLpn(getCellValue(row.getCell(14), 14));
        request.setErpOrderLineNum(lineNum + "");
        return request;
    }

    /**
     * 校验日期是否符合格式
     * @param str
     * @return
     */
    public String validDate(String str) {
        // 指定日期格式为四位年-两位月份-两位日期，注意yyyy-MM-dd区分大小写；
        String dateStr = "";
        if("".equals(str)){
            return dateStr;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007-02-29会被接受，并转换成2007-03-01
            simpleDateFormat.setLenient(false);
            dateStr = simpleDateFormat.format(simpleDateFormat.parse(str));
        } catch (ParseException e) {
            throw BusinessException.fail(str + ":日期不符合'yyyy-MM-dd'格式，请重新填写并校验");
        }
        return dateStr;
    }


    private Sheet getExcelSheet(String fileUrl, String fileName) {
        ImportFileRequest importFileRequest = new ImportFileRequest();
        String userCode = iSsoService.getUserCode();
        userCode = userCode == null ? "system" : userCode;
        importFileRequest.setUserCode(userCode);
        importFileRequest.setFileUrl(fileUrl);
        importFileRequest.setFileName(fileName);
        String importKey = CostOrderInfoStatusEnum.INBOUND_ORDER_LOCK + importFileRequest.getLockKey();
        // 获得工作簿
        Sheet sheet;
        File downloadFileFromOss = null;
        FileInputStream fileInputStream=null;
        try {
            downloadFileFromOss = upDownloadHelper.downloadFileFromOss(importFileRequest, importKey, 5 * 60L);
            fileInputStream = new FileInputStream(downloadFileFromOss);
            MultipartFile file = new MockMultipartFile(downloadFileFromOss.getName(), fileName, null, fileInputStream);
            ImportExcel ei = new ImportExcel(file, 1, 0, applicationContext);
            sheet = ei.getSheet();
        } catch (Exception e) {
            logger.error("获取OSS文件excel失败");
            throw BusinessException.fail(e.getMessage());
        } finally {
            upDownloadHelper.end(downloadFileFromOss, importKey, fileName);
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    logger.error("关闭文件流失败");
                }
            }
        }
        //读取excel数据
        if (sheet == null) {
            logger.error("获取OSS文件excel失败");
            throw BusinessException.fail("excel为空");
        }
        return sheet;
    }

    /**
     * @Description: 将一个入库文件内的数据保存入WMS
     * @Param:
     * @return: com.mideaframework.core.web.JsonResponse
     * @Date: 2019-9-20
     */
    private JsonResponse saveInboundOrder(InboundOrder selectedOrder) {
        logger.info("保存入库文件：{}", selectedOrder.getFileName());
        JsonResponse jsonResponse = new JsonResponse();
        jsonResponse.setCode(BaseCodeEnum.SUCCESS.getCode());
        try {
            Sheet sheet = getExcelSheet(selectedOrder.getFileUrl(), selectedOrder.getFileName());
            int startNum = 2;
            int rowNum = sheet.getLastRowNum();
            List<ReceiptDataTTXRequest> requests = new ArrayList<>();
            List<InboundOrderHead> heads = new ArrayList<>();
            for (int index = startNum; index < rowNum + 1; index++) {
                Row row = sheet.getRow(index);
                ReceiptDataTTXRequest request = convertRowData(index, row);
                if (request == null) {
                    continue;
                }
                //核对仓库编码是否全部一致
                if (index > startNum && !requests.get(0).getWarehouseCode().equals(request.getWarehouseCode())) {
                    throw BusinessException.fail(selectedOrder.getFileName() + "仓库编码不一致！");
                }
                requests.add(request);
                //创建入库文件头
                InboundOrderHead head;
                if (!("").equals(request.getCustomerCode())) {
                    head = new InboundOrderHead(request.getCode() + '_' + request.getCustomerCode(), request.getWarehouseCode());
                } else {
                    head = new InboundOrderHead(request.getCode(), request.getWarehouseCode());
                }
                if (!heads.contains(head)) {
                    head.setOnShelf(0);
                    heads.add(head);
                    head.setInbounderOrderId(selectedOrder.getId());
                }
            }

            //调用接口，保存入库单，检查库位等信息，更新入库单状态，更新入库单表头
            JsonResponse dataJsonResponse = iReceiptInventoryTTXService.importReceiptData(requests);
            if (dataJsonResponse.getCode().equals(BaseCodeEnum.SUCCESS.getCode())) {
                iInboundOrderHeadService.insertBatch(heads);
                selectedOrder.setOrderStatus(OrderCodeEnum.SAVED.getCode());

            } else {//出错
                List<ReceiptSaveTTXItem> responseItems = (List<ReceiptSaveTTXItem>) dataJsonResponse.getData();
                //生成异常文件，并上传文件并生成地址
                selectedOrder.setErrorFileUrl(createErrorFile(responseItems));
                selectedOrder.setOrderStatus(OrderCodeEnum.ERROR.getCode());
                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                jsonResponse.setMsg(selectedOrder.getFileName() + "入库单内有异常信息，保存失败，生成异常文件");
            }
        } catch (Exception be) {//异常
            ReceiptSaveTTXItem responseItem = new ReceiptSaveTTXItem();
            responseItem.setMsg(be.getMessage());
            List<ReceiptSaveTTXItem> responseItems = new ArrayList<>();
            responseItems.add(responseItem);
            //生成异常文件，并上传文件并生成地址
            selectedOrder.setErrorFileUrl(createErrorFile(responseItems));
            selectedOrder.setOrderStatus(OrderCodeEnum.ERROR.getCode());
            jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
            jsonResponse.setMsg(selectedOrder.getFileName() + be.getMessage());
            return jsonResponse;
        }
        return jsonResponse;
    }

    /**
     * 定时器：将状态为已上传的所有入库文件的数据保存入WMS
     * @Param: []
     * @return: com.mideaframework.core.web.JsonResponse
     * @Author: yaowl
     * @Date: 2019-9-20
     */
    public JsonResponse saveInboundOrderList() {
        JsonResponse totalJsonResponse = new JsonResponse();
        totalJsonResponse.setCode(BaseCodeEnum.SUCCESS.getCode());

        InboundOrder inboundOrder = new InboundOrder();
        inboundOrder.setOrderStatus(OrderCodeEnum.UPLOAD.getCode());
        inboundOrder.setPageSize(Integer.MAX_VALUE);
        List<InboundOrder> selectedOrderList = inboundOrderService.select(inboundOrder);
        if (selectedOrderList.isEmpty()) {
            totalJsonResponse.setMsg("没有状态为“已上传”的入库单文件。");
            return totalJsonResponse;
        }

        changeOrderStatus(selectedOrderList, OrderCodeEnum.SAVING.getCode());

        ExecutorService executor = new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS, new SynchronousQueue<>());
        List<Future<JsonResponse>> futureList = selectedOrderList.stream().map(selectedOrder -> executor.submit(() -> saveInboundOrder(selectedOrder))).collect(Collectors.toList());

        StringBuilder stringBuilder = new StringBuilder();
        for (Future<JsonResponse> future : futureList) {
            try {
                if (!future.isCancelled()) {
                    JsonResponse jsonResponse = future.get();
                    stringBuilder.append(jsonResponse.getMsg()).append(";");
                }
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                logger.error(e.toString());
                stringBuilder.append(e.toString());
            }
        }

        inboundOrderService.batchUpdate(selectedOrderList);

        totalJsonResponse.setMsg(stringBuilder.toString());
        return totalJsonResponse;
    }

    /**
     * @Description: 调用WMS的确认上架接口
     * @Param: [warehouseCode, code]
     * @return: boolean
     * @Author: yaowl
     * @Date: 2019-9-17
     */
    private JSONObject checkIn(String code, String warehouseCode) throws IOException {
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        HashMap<String, String> map = new HashMap<>();
        map.put("warehouseCode", warehouseCode);
        map.put("code", code);

        JsonResponse jsonResponse = bopService.otpTtxInventoryCheckIn(map);
        if (jsonResponse.getCode().equals(BaseCodeEnum.SUCCESS.getCode())) {
            return JSON.parseObject(jsonResponse.getData().toString());
        } else {
            HashMap<String, String> resultMap = new HashMap<>();
            resultMap.put("code", jsonResponse.getCode());
            resultMap.put("msg", jsonResponse.getMsg());
            return JSON.parseObject(JSON.toJSONString(resultMap));
        }
    }


    /**
     * @Description: 确认一个入库文件下发
     * @Param: []
     * @return: com.mideaframework.core.web.JsonResponse
     * @Author: yaowl
     * @Date: 2019-9-18
     */
    private JsonResponse checkIsShelf(InboundOrder inboundOrder) {
        logger.info("下发入库文件,id为:{}，文件名为:{}",inboundOrder.getId(),inboundOrder.getFileName());

        StringBuilder msg = new StringBuilder();
        msg.append(inboundOrder.getFileName()).append(":");

        InboundOrderHead inboundOrderHead = new InboundOrderHead();
        inboundOrderHead.setOnShelf(0);
        inboundOrderHead.setInbounderOrderId(inboundOrder.getId());
        List<InboundOrderHead> headList = iInboundOrderHeadService.select(inboundOrderHead);

        if (headList.isEmpty()) {
            msg.append("入库单没有入库单号和仓库编码");
            inboundOrder.setOrderStatus(OrderCodeEnum.UPERROR.getCode());
            return JsonResponse.fail(msg.toString());
        }

        List<InboundOrderHead> changeList = new ArrayList<>();
        inboundOrder.setOrderStatus(OrderCodeEnum.UPED.getCode());
        for (InboundOrderHead head : headList) {
            JSONObject jsonObject = null;
            boolean timeOutFlag = false;
            try {
                jsonObject = checkIn(head.getCode(), head.getWarehouseCode());
            } catch (SocketTimeoutException se) {
                timeOutFlag = true;
            } catch (IOException e) {
                inboundOrder.setOrderStatus(OrderCodeEnum.UPERROR.getCode());
                msg.append(e.toString()).append(upErrorMsg);
                return JsonResponse.fail(msg.toString());
            }
            //如果超时，或者返回值为成功，则下发成功；否则下发失败，返回失败
            if (timeOutFlag || (jsonObject != null && BaseCodeEnum.SUCCESS.getCode().equals(jsonObject.getString("code")))) {
                head.setOnShelf(1);
                changeList.add(head);
            }
            else {
                msg.append(head.getCode())
                        .append(":")
                        .append(jsonObject != null ? jsonObject.getString("code") : "1")
                        .append(":")
                        .append(jsonObject != null ? jsonObject.getString("msg") : "ErrorConnecting")
                        .append(";");
                logger.error("{}", msg);
                return JsonResponse.fail(msg.toString());
            }
        }
        iInboundOrderHeadService.batchUpdate(changeList);
        return new JsonResponse();
    }

    /**
     * @Description: 定时任务：确认一组入库文件上架
     * @Param: []
     * @return: com.mideaframework.core.web.JsonResponse
     * @Author: yaowl
     * @Date: 2019-9-20
     */
    public JsonResponse checkListIsShelf() {
        InboundOrder inboundOrder = new InboundOrder();
        inboundOrder.setOrderStatus(OrderCodeEnum.SAVED.getCode());
        inboundOrder.setPageSize(Integer.MAX_VALUE);
        List<InboundOrder> selectedOrderList = inboundOrderService.select(inboundOrder);

        JsonResponse totalJsonResponse = new JsonResponse();
        totalJsonResponse.setCode(BaseCodeEnum.SUCCESS.getCode());
        if (selectedOrderList.isEmpty()) {
            logger.error("没有状态为“已保存”的入库单文件");
            totalJsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
            totalJsonResponse.setMsg("没有状态为“已保存”的入库单文件");
            return totalJsonResponse;
        }

        changeOrderStatus(selectedOrderList, OrderCodeEnum.UPING.getCode());

        ThreadPoolExecutor executor = new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS, new SynchronousQueue<>());
        List<Future<JsonResponse>> futureList = new ArrayList<>();
        for (InboundOrder selectedOrder : selectedOrderList) {
            futureList.add(
                executor.submit(() -> checkIsShelf(selectedOrder))
            );
        }
        StringBuilder stringBuilder = new StringBuilder();

        for (Future<JsonResponse> future : futureList) {
            try {
                if (!future.isCancelled()) {
                    JsonResponse jsonResponse = future.get();
                    stringBuilder.append(jsonResponse.getMsg()).append(";");
                    }
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                logger.error(e.toString());
                stringBuilder.append(e.toString());
            }
        }
        inboundOrderService.batchUpdate(selectedOrderList);
        totalJsonResponse.setMsg(stringBuilder.toString());
        return totalJsonResponse;
    }

    /**
     * @Description: 手动重新下发入库单
     * @Param: [id]
     * @return: com.mideaframework.core.web.JsonResponse
     * @Author: yaowl
     * @Date: 2019-9-18
     */
    public JsonResponse reCheckIsShelf(long id) {
        logger.info("重新下发id为{}，的异常入库单",id);
        InboundOrder inboundOrder = new InboundOrder(id);
        InboundOrder selectedOrder = inboundOrderService.selectOne(inboundOrder);

        if (selectedOrder == null || !selectedOrder.getOrderStatus().equals(OrderCodeEnum.UPERROR.getCode())) {
            logger.error("没有id为{}的下发异常的入库单",id);
            return JsonResponse.fail("此下发异常的入库单为空");
        }

        StringBuilder msg = new StringBuilder();
        msg.append(selectedOrder.getFileName()).append(":");
        //获取未下发的入库单文件头数组
        InboundOrderHead inboundOrderHead = new InboundOrderHead();
        inboundOrderHead.setOnShelf(0);
        inboundOrderHead.setInbounderOrderId(id);
        List<InboundOrderHead> headList = iInboundOrderHeadService.select(inboundOrderHead);
        if (headList.isEmpty()) {
            msg.append("该下发异常的入库单没有入库单号和仓库编码");
            return JsonResponse.fail(msg.toString());
        }
        List<InboundOrderHead> changeList = new ArrayList<>();
        for (InboundOrderHead head : headList) {
            JSONObject jsonObject = null;
            boolean timeOutFlag = false;
            try {
                jsonObject = checkIn(head.getCode(), head.getWarehouseCode());
            } catch (SocketTimeoutException se) {
                timeOutFlag = true;
            } catch (IOException e) {
                logger.error("{},{}",e, upErrorMsg);
                return JsonResponse.fail(e.toString() + upErrorMsg);
            }
            //如果超时，或者下发成功，则修改；否则下发失败，返回
            if (timeOutFlag || (jsonObject != null && BaseCodeEnum.SUCCESS.getCode().equals(jsonObject.getString("code")))) {
                head.setOnShelf(1);
                changeList.add(head);
            } else {
                msg.append(head.getCode())
                        .append(":")
                        .append(jsonObject != null ? jsonObject.getString("code") : "1")
                        .append(":")
                        .append(jsonObject != null ? jsonObject.getString("msg") : "ErrorConnecting")
                        .append(";");
                logger.error("{}", msg);
                return JsonResponse.fail(msg.toString());
            }
        }

        iInboundOrderHeadService.batchUpdate(changeList);
        selectedOrder.setOrderStatus(OrderCodeEnum.UPED.getCode());
        inboundOrderService.update(selectedOrder);
        logger.info("id为：{}的异常入库单下发成功",id);
        return new JsonResponse();
    }


    /**
     * @Description: 创建异常文件，并上传
     * @Param: [responseItems]
     * @return: java.lang.String
     * @Author: yaowl
     * @Date: 2019-8-26
     */
    public String createErrorFile(List<ReceiptSaveTTXItem> responseItems) {
        if (responseItems == null || responseItems.isEmpty()) {
            logger.error("异常数据为空，请检查responseItems");
            return null;
        }
        Excel excel = new Excel();
        excel.setTitle("异常报告");
        String[] excelHeader = {"erpOrderLineNum", "itemCode", "msg"};
        excel.setHeader(excelHeader);

        for (ReceiptSaveTTXItem item : responseItems) {
            ExcelRow row = excel.createRow();
            row.addCell(item.getErpOrderLineNum());
            row.addCell(item.getItemCode());
            row.addCell(item.getMsg());
        }
        File file;
        String fileUrl = "";
        FileInputStream fileInputStream = null;
        try {
            file = excel.createXlsxByFile();
            if (file != null) {
                fileInputStream = new FileInputStream(file);
                MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), null, fileInputStream);
                ImportFileRequest importFileRequest = upDownloadHelper.uploadFileToOss(multipartFile);
                fileUrl = importFileRequest.getFileUrl();
            }
        } catch (Exception e) {
            logger.error("异常excel文件创建失败:{}", e.toString());
            e.printStackTrace();
            return null;
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    logger.error("异常excel文件创建失败:{}", e.toString());
                    e.printStackTrace();
                }
            }
        }
        return fileUrl;
    }

    private void changeOrderStatus(List<InboundOrder> list, String status) {
        for (InboundOrder inboundOrder : list) {
            inboundOrder.setOrderStatus(status);
        }
        inboundOrderService.batchUpdate(list);
    }


}
