package com.midea.logistics.otplbs.fegin.orderagg;

import com.midea.logistics.otp.common.request.AssembleRuleConfigDto;
import com.midea.logistics.otp.common.request.AutoDistributionLabelPreposeTaskDto;
import com.midea.logistics.otp.common.request.PreposeTaskExtDto;
import com.midea.logistics.otp.common.request.PreposeTasksDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.midea.logistics.otp.bean.RecommendAddressDto;
import com.mideaframework.core.web.JsonResponse;

@FeignClient(value = "logistics-order-agg-service")
public interface OrderAggCommonFeign {


    /**
    * @description: 获取推荐地址
    * @param: [orderNo]
    * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.otp.bean.RecommendAddressDto>
    * @author: 陈永培
    * @createtime: 2022/5/13 15:48
    */
    @GetMapping(value = "/recommendedAddress")
    public JsonResponse<RecommendAddressDto> recommendedAddress(@RequestParam("orderNo") String orderNo);

    /**
     * 集拼
     * @param preposeTasksDto
     * @return
     */
    @PostMapping("/preposeTask/assemble")
    public JsonResponse assemble(@RequestBody PreposeTasksDto preposeTasksDto);

    /**
     * 取消集拼
     * @param preposeTasksDto
     * @return
     */
    @PostMapping("/preposeTask/cancelAssemble")
    public JsonResponse cancelAssemble(@RequestBody PreposeTasksDto preposeTasksDto);

    /**
     * 加单
     * @param preposeTasksDto
     * @return
     */
    @PostMapping("/preposeTask/addOrder")
    public JsonResponse addOrder(@RequestBody PreposeTasksDto preposeTasksDto);

    /**
     * 移除
     * @param preposeTasksDto
     * @return
     */
    @PostMapping("/preposeTask/removeAssemble")
    public JsonResponse removeAssemble(@RequestBody PreposeTasksDto preposeTasksDto);

    @PostMapping("/preposeTask/split")
    public JsonResponse split(@RequestBody PreposeTaskExtDto preposeTasksDto);


    /**
     * 移除
     * @param preposeTasksDto
     * @return
     */
    @PostMapping("/preposeTask/confirmAssemble")
    public JsonResponse confirmAssemble(@RequestBody PreposeTasksDto preposeTasksDto);
    /**
     * 一单一拼
     * @param preposeTasksDto
     * @return
     */
    @PostMapping("/preposeTask/assembleOneToOne")
    public JsonResponse assembleOneToOne(@RequestBody PreposeTasksDto preposeTasksDto);

    @GetMapping("/capacity/mip/search")
    JsonResponse searchMipLogs(@RequestParam("mipApplyNo") String mipApplyNo);


    @PostMapping("/preposeTask/assembleByConfig")
    public JsonResponse assembleAuto(@RequestBody AssembleRuleConfigDto assembleRuleConfigDto);

    @PostMapping("/preposeTask/autoDistibutionLabel")
    public JsonResponse autoDistibutionLabel(@RequestBody AutoDistributionLabelPreposeTaskDto autoDistributionLabelPreposeTaskDto);
}
