package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.ExceptionCheck;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: ExceptionCheckFeign
* Author: luoh
* Date: 2019-8-16 14:41:43
* Description:异常登记 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface ExceptionCheckFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/exceptionCheck", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ExceptionCheck exceptionCheck);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/exceptionCheck/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/exceptionCheck/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ExceptionCheck exceptionCheck);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/exceptionCheck/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/exceptionChecks", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap ExceptionCheck exceptionCheck);
}
