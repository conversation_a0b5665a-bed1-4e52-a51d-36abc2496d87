package com.midea.logistics.otp.order.common.fegin;

import com.midea.logistics.otp.bean.BusinessLineTaskDto;
import com.midea.logistics.otp.common.constants.BaseRouter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.midea.logistics.otp.ordertask.converged.domain.TaskConvergedRouters;
import com.midea.logistics.otp.ordertask.converged.domain.request.CommerceCategoriesDto;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.mideaframework.core.web.JsonResponse;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskFeign
 * Author: ex_wuhc
 * Date: 2019年5月31日 下午3:12:06
 * Description: 任务集合feign
 */
@FeignClient(value = "logistics-otp-ordertask-converged-service" ,url = BaseRouter.LOGISTICS_OTP_ORDERTASK_CONVERGED_SERVICE)
public interface TaskConvergedFeign {
    
    /**
    * @description: 基地分拨出清空holdFlag标识并下发任务
    * @param: [task]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2024/4/3 10:52
    */
    @PostMapping(TaskConvergedRouters.TASK_CLEAR_HOLD_FLAG_AND_ISSUE_TASK)
    public JsonResponse clearHoldFlagAndIssueTask(@RequestBody Task task);
    
    /**
     * 电商分类打标控制器
     * @param commerceCategoriesDto
     * @return
     */
    @PostMapping("/commerceCategories")
    public JsonResponse commerceCategoriesRest(@RequestBody CommerceCategoriesDto commerceCategoriesDto);

    /**
     * 电商分类打标控制器
     * @param commerceCategoriesDto
     * @return
     */
    @PostMapping(TaskConvergedRouters.COMMERCE_CATEGORIES_TASK)
    public JsonResponse commerceCategoriesTaskRest(@RequestBody BusinessLineTaskDto dto);
}