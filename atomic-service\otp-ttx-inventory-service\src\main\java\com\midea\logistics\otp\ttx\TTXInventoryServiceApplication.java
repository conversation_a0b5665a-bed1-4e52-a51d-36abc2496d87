package com.midea.logistics.otp.ttx;

import com.midea.logistics.otp.common.helper.*;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

@EnableEurekaClient
@EnableEncryptableProperties
@EnableFeignClients(value = {"com.midea.logistics.cache","com.midea.logistics.otp.common.feign.servicefeign.dispatch"})
@ComponentScan(basePackages = {
    "com.mideaframework.core",
    "com.mideaframework.sdk",
    "com.midea.logistics"
},excludeFilters = {
    @ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        classes = { CheckBomHelper.class,MdmHelper.class, BusinessHelper.class, BusinessParamHelper.class, AbnormalSysHelper.class ,ContractSwitchHelper.class,BusPermissionHelper.class,RiPushAmsHelper.class,TokenHelper.class,UapAuthHelper.class,MarkHelper.class,FindOriginOrderHelper.class,OrderLabelMarkHelper.class,PreFlowEndHelper.class})
}
)
@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class
})
public class TTXInventoryServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(TTXInventoryServiceApplication.class, args);
    }
}
