package com.midea.logistics;

import com.google.common.collect.Lists;
import com.midea.logistics.otp.common.helper.bean.VehicleScheduleRuleExcelRequest;
import com.midea.logistics.otp.rule.RuleServiceApplication;
import com.midea.logistics.otp.rule.domain.bean.VehicleScheduleRule;
import com.midea.logistics.otp.rule.domain.bean.custom.VehicleScheduleRuleDto;
import com.midea.logistics.otp.rule.mapper.common.VehicleScheduleRuleMapper;
import com.midea.logistics.otp.rule.rest.VehicleScheduleRuleRest;
import com.midea.logistics.otp.rule.service.IVehicleScheduleRuleService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.PageResponse;
import com.sun.jersey.core.util.StringIgnoreCaseKeyComparator;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = RuleServiceApplication.class)
public class VehicleSchduleTest {

    @Autowired
    private IVehicleScheduleRuleService vehicleScheduleRuleService;

    @Autowired
    private VehicleScheduleRuleRest vehicleScheduleRuleRest;

    @Test
    public void deleteBatchByIds() {
        Integer integer = vehicleScheduleRuleService.batchDeleteByBusinessKey(Arrays.asList(3L));
        System.out.println(integer);
    }

    @Test
    public void selectVehicleScheduleRulePage() {
        VehicleScheduleRule vehicleScheduleRule = new VehicleScheduleRule();
        vehicleScheduleRule.setPageNo(1);
        vehicleScheduleRule.setPageSize(10);
        vehicleScheduleRule.setCompanyCode("7751614555");
        List<VehicleScheduleRule> vehicleScheduleRuleList = vehicleScheduleRuleService.selectVehicleScheduleRuleByIndex(vehicleScheduleRule);
        //List<VehicleScheduleRuleDto> list = page.getList();
        //Integer totalCount = page.totalCount;
        //System.out.println(totalCount);
        vehicleScheduleRuleList.forEach(System.out::println);
    }

    @Test
    public void save() {
        VehicleScheduleRule vehicleScheduleRule = new VehicleScheduleRule("1", "1", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "12-25", "12-25", "12-25",
            new Date(), new Date());
        vehicleScheduleRuleService.saveVehicleScheduleRule(vehicleScheduleRule);
    }

    @Test
    public void selectOneVehicleScheduleRule() {
        VehicleScheduleRule vehicleScheduleRule = new VehicleScheduleRule("1", "1", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            null, null);
        VehicleScheduleRule reRule = vehicleScheduleRuleService.selectOneVehicleScheduleRule(vehicleScheduleRule);
        System.out.println("reRule = " + reRule);
    }

    @Test
    public void batchCreateOrUpdate() {
        /*VehicleScheduleRule vehicleScheduleRule1 = new VehicleScheduleRule("23", "1", "1", "1", "1", "1", "1", "1",
            "1", "2", "342", "43", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            new Date(), new Date());
        VehicleScheduleRule vehicleScheduleRule2 = new VehicleScheduleRule("1", "1", "1", "1", "1", "1", "1", "1",
            "1", "5", "6", "7", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            new Date(), new Date());*/
        VehicleScheduleRule vehicleScheduleRule3 = new VehicleScheduleRule("4", "4", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            null, null);
        VehicleScheduleRule vehicleScheduleRule4 = new VehicleScheduleRule("4", "4", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            null, null);
        //vehicleScheduleRule3.setId(4L);
        ArrayList<VehicleScheduleRule> list = Lists.newArrayList();
        //list.add(vehicleScheduleRule1);
        //list.add(vehicleScheduleRule2);
        list.add(vehicleScheduleRule3);
        list.add(vehicleScheduleRule4);
        String integer = vehicleScheduleRuleService.batchCreateOrUpdate(list);
        System.out.println("integer = " + integer);

    }

    @Test
    public void updateBatch() {
        VehicleScheduleRule vehicleScheduleRule3 = new VehicleScheduleRule("3", "3", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            null, null);
        vehicleScheduleRule3.setId(3L);
        ArrayList<VehicleScheduleRule> list = Lists.newArrayList(vehicleScheduleRule3);
        Integer integer = vehicleScheduleRuleService.batchUpdate(list);
        System.out.println("integer = " + integer);
    }

    @Test
    public void date() throws InvocationTargetException, IllegalAccessException {
        /*SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        //String a = StringUtils.join("2020-11-12", " 23:59:59");
        String a = "23:333234324dfasf阿斯顿";
        Date date = null;
        try {
            date = sdf.parse(a);
        } catch (ParseException e) {
        }
        System.out.println("date = " + date);*/

        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");

        String a = "0881-2-28";
        if (isDate1(a)) {
            System.out.println("111");
        }

        //String a1 = "18:59";
        //if (isTime(a1)) {
        //    System.out.println("111");
        //}

        //Date date1 = null;
        //try {
        //    date1 = sdf1.parse(a);
        //} catch (ParseException e) {
        //}
        //System.out.println("date = " + date1);

        /*VehicleScheduleRuleExcelRequest ruleExcelRequest = new VehicleScheduleRuleExcelRequest();
        VehicleScheduleRule vehicleScheduleRule = new VehicleScheduleRule();
        ruleExcelRequest.setCompanyCode("1");
        ruleExcelRequest.setEffectiveTime(null);
        BeanUtils.copyProperties(vehicleScheduleRule,ruleExcelRequest);
        System.out.println(vehicleScheduleRule.getEffectiveTime());
        System.out.println(vehicleScheduleRule.getCompanyCode());*/
    }

    public static boolean isTime(String time){
        Pattern p = Pattern.compile("((((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|())))?$");
        return p.matcher(time).matches();
    }

    public static boolean isDate(String date){
        Pattern p = Pattern.compile("^((\\d{2}(([02468][048])|([13579][26]))[\\-\\s]?((((0?[13578])|(1[02]))[\\-\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\s]?((((0?[13578])|(1[02]))[\\-\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))?$");
        return p.matcher(date).matches();
    }

    public static boolean isDate1(String date) {
        Pattern p = Pattern.compile("^(?:(?!0000)[0-9]{4}([-]?)(?:(?:0?[1-9]|1[0-2])([-]?)(?:0?[1-9]|1[0-9]|2[0-8])|(?:0?[13-9]|1[0-2])([-]?)(?:29|30)|(?:0?[13578]|1[02])([-]?)31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)([-]?)0?2([-]?)29)$");
        return p.matcher(date).matches();
    }

    @Test
    public void testList() {
        /*List<String> list = Lists.newArrayList();
        list.add("1");
        list.add("2");
        list.add(null);
        list.add("3");
        list.add("3");
        list.add(null);
        list.add("4");
        list.add("5");
        List<String> collect = list.stream().distinct().collect(Collectors.toList());
        System.out.println(collect.size());
        collect.forEach(System.out::println);
        System.out.println("======");
        list.forEach(System.out::println);*/

        VehicleScheduleRule vehicleScheduleRule1 = new VehicleScheduleRule("23", "1", "1", "1", "1", "1", "1", "1",
            "1", "2", "342", "43", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            new Date(), new Date());
        VehicleScheduleRule vehicleScheduleRule2 = new VehicleScheduleRule("1", "1", "1", "1", "1", "1", "1", "1",
            "1", "5", "6", "7", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            new Date(), new Date());
        VehicleScheduleRule vehicleScheduleRule3 = new VehicleScheduleRule("4", "4", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", null, "1", "1", "1", "1", "12-25", "12-25", "12-25",
            null, null);
        VehicleScheduleRule vehicleScheduleRule4 = new VehicleScheduleRule("4", "4", "1", "1", "1", "1", "1", "1",
            "1", "1", "1", "1", "1", "1", "2222", "1", "1", "1", "1", "12-25", "12-25", "12-25",
            null, new Date());
        VehicleScheduleRule vehicleScheduleRule5 = null;

        /*if (Objects.equals(vehicleScheduleRule4, vehicleScheduleRule3)) {
            System.out.println("111111");
        }*/
        ArrayList<VehicleScheduleRule> lists = Lists.newArrayList();
        lists.add(vehicleScheduleRule3);
        lists.add(vehicleScheduleRule4);
        //同时插入不存在数据库的数据需要校验重复
        Map<VehicleScheduleRule,Integer> map = new HashMap<>();
        for (VehicleScheduleRule item : lists) {
            //定义一个计数器，记录当前元素出现的次数
            Integer count = 1;
            //如果当前元素在map容器中已存在，计数器+1
            if (map.get(item)!=null) {
                count = map.get(item) + 1;
            }
            //往map容器里存数据
            map.put(item,count);
        }
        for (VehicleScheduleRule key : map.keySet()) {
            if (map.get(key) > 1) {
                System.out.println(key.toString());
            }
        }

        /*ArrayList<VehicleScheduleRule> lists = Lists.newArrayList();
        lists.add(vehicleScheduleRule1);
        lists.add(vehicleScheduleRule2);
        lists.add(null);
        lists.add(vehicleScheduleRule3);
        lists.add(vehicleScheduleRule4);
        lists.add(vehicleScheduleRule5);
        List<VehicleScheduleRule> collect = lists.stream().distinct().collect(Collectors.toList());
        System.out.println(collect.size());
        collect.forEach(System.out::println);
        System.out.println("------------------");
        lists.forEach(System.out::println);*/
    }

    @Autowired
    private VehicleScheduleRuleMapper vehicleScheduleRuleMapper;

    @Test
    public void test() {
        VehicleScheduleRuleExcelRequest vehicleScheduleRule = new VehicleScheduleRuleExcelRequest();
        //vehicleScheduleRule.setPageNo(1);
        //vehicleScheduleRule.setPageSize(10);
        //vehicleScheduleRule.setCompanyCode("7751614555");
        //List<VehicleScheduleRule> integer = vehicleScheduleRuleMapper.selectIndexByTimeLimit(vehicleScheduleRule);
        ////System.out.println(integer);
        //integer.forEach(System.out::println);
        //vehicleScheduleRule.setCompanyCode("");

        //if ((vehicleScheduleRule)) {
        //    System.out.println(111);
        //} else {
        //    System.out.println(vehicleScheduleRule);
        //}
    }

}
