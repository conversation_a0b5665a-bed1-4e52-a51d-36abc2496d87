<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.VipCustomerConfigExtMapper">

    <sql id="whereFieldsSql">
    where delete_flag=0
    <if test="customerAddress !=null">
        and `customer_address` =#{customerAddress}
    </if>
    <if test="customerMobile !=null">
        and `customer_mobile` =#{customerMobile}
    </if>
    <if test="customerName !=null">
        and `customer_name` =#{customerName}
    </if>
</sql>

    <select id="checkIsExist" resultType="Long">
        select id
        from vip_customer_config t
        <include refid="whereFieldsSql"/>
    </select>

</mapper>