package com.midea.logistics.imp.orderverify.rest;


import com.midea.logistics.imp.orderverify.service.LuggageConversionService;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;


@RestController
public class LuggageConversionRest {

    @Autowired
    private LuggageConversionService luggageConversionService;

    @PostMapping("/lmp/validate/luggage/conversion")
    public JsonResponse LuggageConversion(@RequestBody CustomerOrderInfoExt customerOrderInfoExt){
        try {
            return JsonResponse.success(luggageConversionService.luggageConversion(customerOrderInfoExt));
        } catch (Exception e) {
            throw BusinessException.fail("箱包关系转换异常，失败原因:" + e.getMessage());
        }
    }

    @GetMapping("/lmp/validate/luggage/conversion/byOrderNo")
    public JsonResponse LuggageConversionByOrderNo(@RequestBody CustomerOrderInfoExt customerOrderInfoExt){
        try {
            luggageConversionService.luggageConversion(customerOrderInfoExt);
            return JsonResponse.success(new HashMap<>());
        } catch (Exception e) {
            throw BusinessException.fail("箱包关系转换异常，失败原因:" + e.getMessage());
        }
    }
}
