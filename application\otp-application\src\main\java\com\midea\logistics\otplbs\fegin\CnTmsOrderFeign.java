package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.CnTmsOrder;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: CnTmsOrderFeign
* Author: lindq2
* Date: 2022-3-25 19:43:46
* Description:菜鸟tms配单表 feign
*/
@FeignClient(value = "logistics-otp-order-receive-service")
public interface CnTmsOrderFeign {

    @RequestMapping(value = "/cnTmsOrder", method = RequestMethod.POST)
    JsonResponse create(@RequestBody CnTmsOrder cnTmsOrder);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/cnTmsOrder/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/cnTmsOrder/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody CnTmsOrder cnTmsOrder);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/cnTmsOrder/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/cnTmsOrders", method = RequestMethod.GET)
    JsonResponse<PageResponse<CnTmsOrder>> search(@SpringQueryMap CnTmsOrder cnTmsOrder);
}
