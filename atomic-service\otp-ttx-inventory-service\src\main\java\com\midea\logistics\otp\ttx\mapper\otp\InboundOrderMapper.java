package com.midea.logistics.otp.ttx.mapper.otp;

import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder;
import com.mideaframework.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: InboundOrderMapper
 * Author: yaowl
 * Date: 2019-8-21 19:21:37
 * Description:InboundOrderMapper服务接口
 */
@Mapper
public interface InboundOrderMapper extends BaseMapper<InboundOrder> {

}