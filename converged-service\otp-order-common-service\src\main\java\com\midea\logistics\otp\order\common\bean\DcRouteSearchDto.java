package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * DC查询路由
 */
@Data
public class DcRouteSearchDto {

    /**
     * 启停状态（启用0，停用1）
     */
    private Integer enableFlag;

    /**
     * 路由编码
     */
    private String rcCode;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 始发地编码
     */
    private String originPlaceCode;

    /**
     * 始发地
     */
    private String originPlace;

    /**
     * 目的地
     */
    private String destinationPlace;

    /**
     * 始发地-省
     */
    private String originPlaceProvince;

    /**
     * 始发地-市
     */
    private String originPlaceCity;

    /**
     * 始发地-区
     */
    private String originPlaceDistrict;

    /**
     * 始发地-镇
     */
    private String originPlaceTown;

    /**
     * 揽收仓仓库编码
     */
    private String collectWhCode;

    /**
     * 揽收仓
     */
    private String collectWarehouse;

    /**
     * 分拨仓仓库编码
     */
    private String distributionWhCode;

    /**
     * 分拨仓
     */
    private String distributionWarehouse;

    /**
     * 网点编码
     */
    private String siteCode;

    /**
     * 网点名称
     */
    private String siteName;

    /**
     * 网点联系人
     */
    private String linkName;

    /**
     * 网点联系方式
     */
    private String linkPhone;

    /**
     * 网点地址
     */
    private String siteAddress;

    /**
     * 网点所在省
     */
    private String linkProvince;

    /**
     * 网点所在省编码
     */
    private String linkProvinceCode;

    /**
     * 网点所在市
     */
    private String linkCity;

    /**
     * 网点所在市编码
     */
    private String linkCityCode;

    /**
     * 网点所在区县
     */
    private String linkDistrict;

    /**
     * 网点所在区县编码
     */
    private String linkDistrictCode;

    /**
     * 网点所在乡镇
     */
    private String linkTown;

    /**
     * 网点所在乡镇编码
     */
    private String linkTownCode;

    /**
     * 网点办公地址
     */
    private String linkOfficeAddr;

    /**
     * 覆盖区域编码
     */
    private String areaCode;

    /**
     * 覆盖区域名称
     */
    private String areaName;

    /**
     * 网点覆盖区域
     */
    private String siteCoverageArea;

    private Date updateTimeStart;
    private Date updateTimeEnd;
    private Long id;
    private Date createTime;
    private Integer pageNo;
    private Integer pageSize;
    private Integer offset;
    private String orderByType;
    private List ids;
    private Integer count;
    private Date startTime;
    private Date endTime;
    private Integer enableTenant;

    /**
     * 始发地省编码
     */
    private String originProvinceCode;

    /**
     * 始发地市编码
     */
    private String originCityCode;

    /**
     * 始发地区编码
     */
    private String originDistrictCode;

    /**
     * 始发地镇编码
     */
    private String originTownCode;

    /**
     * 始发地详细地址
     */
    private String originDetailAddress;

}
