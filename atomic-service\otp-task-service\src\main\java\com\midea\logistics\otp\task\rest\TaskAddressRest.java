package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.service.ITaskAddressService;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: TaskAddressController
* Author: lindq2
* Date: 2019-6-28 10:56:27
* Description:任务地址表控制层
*/
@RestController
public class TaskAddressRest extends BaseController<TaskAddress> {

    private Logger logger = LoggerFactory.getLogger(TaskAddressRest.class);

    @Autowired
    private ITaskAddressService iTaskAddressService;

    /**
    * 新增
    *
    * @return
    */
    @RequestMapping(value = "/taskAddress", method = RequestMethod.POST)
    public JsonResponse create(@Valid @RequestBody TaskAddress taskAddress, HttpServletRequest request) {

    RestDoing doing = jsonResponse -> {
        iTaskAddressService.saveTaskAddress(taskAddress);
    };
    return doing.go(request, logger);
    }


    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/taskAddress/{id}", method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

    RestDoing doing = jsonResponse -> {
        TaskAddress taskAddressQr = new TaskAddress();
        taskAddressQr.setId(id);
        TaskAddress taskAddress = iTaskAddressService.selectOneTaskAddress(taskAddressQr);
        jsonResponse.data = taskAddress;
    };
    return doing.go(request, logger);

    }


    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/taskAddress/{id}", method = RequestMethod.DELETE)
    public JsonResponse deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

    RestDoing doing = jsonResponse -> {
        TaskAddress taskAddressQr = new TaskAddress(id);
        jsonResponse.data = iTaskAddressService.deleteTaskAddress(taskAddressQr);
    };
    return doing.go(request, logger);

    }

    /**
    * @param taskAddress
    * @return
    */
    @RequestMapping(value = "/taskAddress/{id}", method = RequestMethod.PUT)
    public JsonResponse update(@PathVariable("id") Long id,  /**@Valid*/ @RequestBody TaskAddress taskAddress, HttpServletRequest request) {

    RestDoing doing = jsonResponse -> {
        taskAddress.setId(id);
        taskAddress.setVersion(taskAddress.getVersion());
        jsonResponse.data = iTaskAddressService.updateTaskAddress(taskAddress);
    };
    return doing.go(request, logger);
    }


    /**
     * @param taskAddresss
     * @return
     */
    @RequestMapping(value = "/batchUpdate", method = RequestMethod.PUT)
    public JsonResponse<Integer> batchUpdate(@RequestBody List<TaskAddress> taskAddresss, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskAddressService.batchUpdate(taskAddresss);
        };
        return doing.go(request, logger);
    }


    /**
     * @param taskAddresss
     * @return
     */
    @RequestMapping(value = "/batchUpdateNull", method = RequestMethod.PUT)
    public JsonResponse<Integer> batchUpdateNull(@RequestBody List<TaskAddress> taskAddresss, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskAddressService.batchUpdateNull(taskAddresss);
        };
        return doing.go(request, logger);
    }



    /**
    * @return 查询集合
    */
    @RequestMapping(value = "/taskAddresss", method = RequestMethod.GET)
    public JsonResponse<PageResponse<TaskAddress>> search(@SpringQueryMap TaskAddress taskAddress, HttpServletRequest request) {

    RestDoing doing = jsonResponse -> {
        PageResponse<TaskAddress> PageResponse = iTaskAddressService.selectTaskAddressPage(taskAddress);
        jsonResponse.data = PageResponse;
    };
    return doing.go(request, logger);

    }


    /**
     * @param task
     * @return
     */
    @RequestMapping(value = "/taskAddress", method = RequestMethod.GET)
    public JsonResponse<TaskAddress> queryByTask(@SpringQueryMap Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskAddress taskAddressQr = new TaskAddress();
            taskAddressQr.setTaskNo(task.getTaskNo());
            TaskAddress taskAddress = iTaskAddressService.selectOneTaskAddress(taskAddressQr);
            jsonResponse.data = taskAddress;
        };
        return doing.go(request, logger);

    }

    /**
     * @param taskNo
     * @return
     */
    @RequestMapping(value = "/queryTaskByTaskNo", method = RequestMethod.GET)
    public JsonResponse<TaskAddress> queryTaskByTaskNo(@RequestParam("taskNo") String taskNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            TaskAddress taskAddressQr = new TaskAddress();
            taskAddressQr.setTaskNo(taskNo);
            TaskAddress taskAddress = iTaskAddressService.selectOneTaskAddress(taskAddressQr);
            jsonResponse.data = taskAddress;
        };
        return doing.go(request, logger);

    }

    /**
     * @param taskAddresss
     * @return
     */
    @RequestMapping(value = "/taskAddress/batchUpdateByTaskNo", method = RequestMethod.PUT)
    public JsonResponse<Integer> batchUpdateByTaskNo(@RequestBody List<TaskAddress> taskAddresss, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskAddressService.batchUpdateByTaskNo(taskAddresss);
        };
        return doing.go(request, logger);
    }

    /**
     * 查询订单任务集合
     * @param
     * @return
     */
    @PostMapping(value = "/taskAddress/taskNos")
    public JsonResponse<List<TaskAddress>> searchTaskAddressListByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<TaskAddress> response = iTaskAddressService.searchTaskAddressListByTaskNos(taskNos);
            jsonResponse.data = response;
        };
        return doing.go(request, logger);
    }

    /**
     * 查询订单任务集合
     * @param
     * @return
     */
    @PostMapping(value = "/taskAddress/orderNos")
    public JsonResponse<List<TaskAddress>> searchLastTaskAddressListByOrderNos(@RequestBody List<String> orderNos, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<TaskAddress> response = iTaskAddressService.searchLastTaskAddressListByOrderNos(orderNos);
            jsonResponse.data = response;
        };
        return doing.go(request, logger);
    }

    /**
     * @param taskAddresss
     * @return
     */
    @PostMapping(value = "/taskAddress/batchUpdateCanSetEmptyByTaskNo")
    public JsonResponse<Integer> batchUpdateCanSetEmptyByTaskNo(@RequestBody List<TaskAddress> taskAddresss, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskAddressService.batchUpdateCanSetEmptyByTaskNo(taskAddresss);
        };
        return doing.go(request, logger);
    }
}
