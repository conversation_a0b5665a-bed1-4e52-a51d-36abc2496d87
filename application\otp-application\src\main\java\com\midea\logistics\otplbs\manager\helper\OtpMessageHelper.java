package com.midea.logistics.otplbs.manager.helper;

import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otplbs.fegin.MsgFegin;
import com.midea.msgcenter.domain.bean.Message;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.auth.bean.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * ©Copyright ©1968-2024 Midea Group,IT
 *
 * @Description: 
 * @FileName: MessageHelper
 * @Author: fengxw26
 * @Date: 2024/11/26 11:54
 */
@Component
@Slf4j
public class OtpMessageHelper {

    @Autowired
    private DictHelper dictHelper;
    @Autowired
    MsgFegin msgFegin;
    @Autowired
    private ISsoService iSsoService;

    private final String REPORT_OTP_SEND_MESSAGE_CONFIG = "REPORT_OTP_SEND_MESSAGE_CONFIG";

    /**
     * 
     * @param content 最大支持约为1万6千个汉字
     * @param title
     * @param messageType
     */
    public void sendMessage(String content, String title, Integer messageType) {
        Message message = new Message();
        message.setTitle(title);
        message.setApplicationCode(dictHelper.getDictVaule(REPORT_OTP_SEND_MESSAGE_CONFIG,"applicationCode"));
        message.setCreateTime(new Date());
        message.setMessageType(messageType);
        UserInfo userInfo = (UserInfo) iSsoService.getUserInfo();
        if (userInfo != null) {
            message.addReceiver(userInfo.getUserCode());
        }
        message.setContent(content);
        msgFegin.sendMessage(message);
    }
}
