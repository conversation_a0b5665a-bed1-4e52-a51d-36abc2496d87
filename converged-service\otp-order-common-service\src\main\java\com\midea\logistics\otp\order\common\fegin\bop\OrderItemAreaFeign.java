package com.midea.logistics.otp.order.common.fegin.bop;

import com.midea.logistics.logisticsbopsdk.constants.RequestUrlConstant;
import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.converged.domain.request.ArriveAreaRequest;
import com.midea.logistics.otp.order.converged.domain.request.OnwayAreaRequest;
import com.midea.logistics.otp.order.converged.domain.request.OutAreaRequest;
import com.midea.logistics.otp.order.common.fegin.fallback.OrderItemAreaFeignFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: VerifyWhOutFeign
 * @author: crystal
 * @date: 2019/7/2 10:24
 * @description: 爆仓校验  待接口对接
 */
@FeignClient(value = "logistics-bop-service", fallback = OrderItemAreaFeignFallback.class,url = BaseRouter.LOGISTICS_OTP_BOP_SERVICE)
public interface OrderItemAreaFeign {


    /**
     * 爆仓增加或核销在途面积
     */
    @PostMapping(RequestUrlConstant.CD_OVER_FLOW_ONWAY)
    String cdOverFlowOnway(@RequestBody  OnwayAreaRequest bean);

    /**
     * 爆仓校验
     */
    @PostMapping(RequestUrlConstant.CD_OVER_FLOW_CHECK_AREA)
    String cdOverFlowCheckArea(@RequestBody   OutAreaRequest request);

    /**
     * 爆仓增加或者到车面积
     */
    @PostMapping(RequestUrlConstant.CD_OVER_FLOW_ARRIVE)
    String cdOverFlowArrive(@RequestBody  ArriveAreaRequest request);

    /**
     * 爆仓校验
     */
    @PostMapping(value = "/inner/T201904230000000014/OFC/cdOverFlowCheck")
    String cdOverFlowCheckAreaLMDM(@RequestBody   OutAreaRequest request);

    /**
     * 爆仓增加或核销在途面积
     */
    @PostMapping(value = "/inner/T201904230000000014/OFC/cdOverFlowOnway")
    String cdOverFlowOnwayLMDM(@RequestBody  OnwayAreaRequest bean);

    /**
     * 爆仓增加或者到车面积
     */
    @PostMapping(value = "/inner/T201904230000000014/OFC/cdOverFlowArrive")
    String cdOverFlowArriveLMDM(@RequestBody  ArriveAreaRequest request);
}
