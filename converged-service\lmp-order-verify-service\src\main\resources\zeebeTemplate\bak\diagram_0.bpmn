<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1x7sd08" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-all-test_0" name="测试zeebe_0_new" isExecutable="true">
    <bpmn:startEvent id="Event_11ossyg" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0kq91j6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0jmkq9a" name="商品确认1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_12n068l</bpmn:incoming>
      <bpmn:incoming>Flow_0rx455b</bpmn:incoming>
      <bpmn:outgoing>Flow_0d6k2cp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0cumgpb" name="结束">
      <bpmn:incoming>Flow_1r2vc78</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_02o0ekv" name="是否箱包转换" default="Flow_0rx455b">
      <bpmn:incoming>Flow_0f8hwzx</bpmn:incoming>
      <bpmn:outgoing>Flow_0rx455b</bpmn:outgoing>
      <bpmn:outgoing>Flow_11m11ab</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_1a74hjo" name="箱包转换">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_11m11ab</bpmn:incoming>
      <bpmn:outgoing>Flow_12n068l</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0us4sa7" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hix2aj</bpmn:incoming>
      <bpmn:outgoing>Flow_1augmii</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1ewoj8n" name="MIP流程">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="http" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1bav04h</bpmn:incoming>
      <bpmn:outgoing>Flow_16iidh0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0ep527t" name="是否走mip流程" default="Flow_0i8l6j1">
      <bpmn:incoming>Flow_1js85f5</bpmn:incoming>
      <bpmn:incoming>Flow_0dvg2y4</bpmn:incoming>
      <bpmn:outgoing>Flow_1bav04h</bpmn:outgoing>
      <bpmn:outgoing>Flow_0i8l6j1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_1axpt8d" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="receive" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1x5tf4a</bpmn:incoming>
      <bpmn:outgoing>Flow_0r7yp5n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_116hi0z" name="仓间调拨解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="allocationParse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0i5qkc2</bpmn:incoming>
      <bpmn:outgoing>Flow_1k29ocj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_012e3ua" name="四级地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0um2gro</bpmn:incoming>
      <bpmn:outgoing>Flow_1e2i45y</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0c6r0no" name="货权转移校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1v9tc21</bpmn:incoming>
      <bpmn:outgoing>Flow_00r41ll</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0jl0sa4" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0jy4qoc</bpmn:incoming>
      <bpmn:outgoing>Flow_1r2vc78</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1ox57hu" name="是否仓间调拨" default="Flow_07fpdbq">
      <bpmn:incoming>Flow_0r7yp5n</bpmn:incoming>
      <bpmn:incoming>Flow_1vuw097</bpmn:incoming>
      <bpmn:outgoing>Flow_0i5qkc2</bpmn:outgoing>
      <bpmn:outgoing>Flow_07fpdbq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0g303p6" name="是否地址解析" default="Flow_0wmfen2">
      <bpmn:incoming>Flow_1k29ocj</bpmn:incoming>
      <bpmn:incoming>Flow_07fpdbq</bpmn:incoming>
      <bpmn:outgoing>Flow_0um2gro</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wmfen2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1fo6g5k" name="是否货权转移" default="Flow_1desvdz">
      <bpmn:incoming>Flow_18zwhxo</bpmn:incoming>
      <bpmn:outgoing>Flow_1v9tc21</bpmn:outgoing>
      <bpmn:outgoing>Flow_1desvdz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1ibn333" name="是否分仓拆单">
      <bpmn:incoming>Flow_00r41ll</bpmn:incoming>
      <bpmn:incoming>Flow_1desvdz</bpmn:incoming>
      <bpmn:outgoing>Flow_0jy4qoc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_0batlvx" name="订单信息校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="orderInfoConfirm" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1augmii</bpmn:incoming>
      <bpmn:incoming>Flow_0jaf0sk</bpmn:incoming>
      <bpmn:outgoing>Flow_0f8hwzx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0ua1s7d" name="B2C" default="Flow_1js85f5">
      <bpmn:incoming>Flow_0d6k2cp</bpmn:incoming>
      <bpmn:outgoing>Flow_1js85f5</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p1mb7c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_04vg5am" name="件型计算">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="pieceAnalysis" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p1mb7c</bpmn:incoming>
      <bpmn:outgoing>Flow_0dvg2y4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0xup6sw" name="是否手工单" default="Flow_1hix2aj">
      <bpmn:incoming>Flow_0kq91j6</bpmn:incoming>
      <bpmn:outgoing>Flow_1hix2aj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0jaf0sk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_060zppy" name="是否走收货单位解析" default="Flow_1vuw097">
      <bpmn:incoming>Flow_16iidh0</bpmn:incoming>
      <bpmn:incoming>Flow_0i8l6j1</bpmn:incoming>
      <bpmn:outgoing>Flow_1x5tf4a</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vuw097</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_1nkm1qg" name="业务大类解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/verify/demo" />
          <zeebe:header key="method" value="GET" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e2i45y</bpmn:incoming>
      <bpmn:incoming>Flow_0wmfen2</bpmn:incoming>
      <bpmn:outgoing>Flow_18zwhxo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0kq91j6" sourceRef="Event_11ossyg" targetRef="Gateway_0xup6sw" />
    <bpmn:sequenceFlow id="Flow_12n068l" sourceRef="Activity_1a74hjo" targetRef="Activity_0jmkq9a" />
    <bpmn:sequenceFlow id="Flow_0rx455b" name="no" sourceRef="Gateway_02o0ekv" targetRef="Activity_0jmkq9a" />
    <bpmn:sequenceFlow id="Flow_0d6k2cp" sourceRef="Activity_0jmkq9a" targetRef="Gateway_0ua1s7d" />
    <bpmn:sequenceFlow id="Flow_1r2vc78" sourceRef="Activity_0jl0sa4" targetRef="Event_0cumgpb" />
    <bpmn:sequenceFlow id="Flow_0f8hwzx" sourceRef="Activity_0batlvx" targetRef="Gateway_02o0ekv" />
    <bpmn:sequenceFlow id="Flow_11m11ab" name="yes" sourceRef="Gateway_02o0ekv" targetRef="Activity_1a74hjo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.sourceSystem!=null and customerOrderInfo.sourceSystem="KingDee-SD")=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1hix2aj" name="NO" sourceRef="Gateway_0xup6sw" targetRef="Activity_0us4sa7" />
    <bpmn:sequenceFlow id="Flow_1augmii" sourceRef="Activity_0us4sa7" targetRef="Activity_0batlvx" />
    <bpmn:sequenceFlow id="Flow_1bav04h" name="yes" sourceRef="Gateway_0ep527t" targetRef="Activity_1ewoj8n">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.mipFlag!=null and customerOrderInfo.mipFlag=1)=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_16iidh0" sourceRef="Activity_1ewoj8n" targetRef="Gateway_060zppy" />
    <bpmn:sequenceFlow id="Flow_1js85f5" name="no" sourceRef="Gateway_0ua1s7d" targetRef="Gateway_0ep527t" />
    <bpmn:sequenceFlow id="Flow_0dvg2y4" sourceRef="Activity_04vg5am" targetRef="Gateway_0ep527t" />
    <bpmn:sequenceFlow id="Flow_0i8l6j1" name="no" sourceRef="Gateway_0ep527t" targetRef="Gateway_060zppy" />
    <bpmn:sequenceFlow id="Flow_1x5tf4a" name="yes" sourceRef="Gateway_060zppy" targetRef="Activity_1axpt8d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CIMSPO","CCSPO","CAINIAOAO","CAINIAOYS","CIMSRI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0r7yp5n" sourceRef="Activity_1axpt8d" targetRef="Gateway_1ox57hu" />
    <bpmn:sequenceFlow id="Flow_0i5qkc2" name="yes" sourceRef="Gateway_1ox57hu" targetRef="Activity_116hi0z">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AI")))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1k29ocj" sourceRef="Activity_116hi0z" targetRef="Gateway_0g303p6" />
    <bpmn:sequenceFlow id="Flow_0um2gro" name="yes" sourceRef="Gateway_0g303p6" targetRef="Activity_012e3ua">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and (((customerOrderInfo.deliveryType!=null)=false or (customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT")) and (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO") and (customerOrderInfo.orderSource!=null and customerOrderInfo.orderSource!="HANDLE")))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e2i45y" sourceRef="Activity_012e3ua" targetRef="Activity_1nkm1qg" />
    <bpmn:sequenceFlow id="Flow_1v9tc21" name="yes" sourceRef="Gateway_1fo6g5k" targetRef="Activity_0c6r0no">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CAINIAOPO","CCSJDPI","CIMSPO","CIMSPI","CIMSRI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00r41ll" sourceRef="Activity_0c6r0no" targetRef="Gateway_1ibn333" />
    <bpmn:sequenceFlow id="Flow_0jy4qoc" name="no" sourceRef="Gateway_1ibn333" targetRef="Activity_0jl0sa4" />
    <bpmn:sequenceFlow id="Flow_1vuw097" name="no" sourceRef="Gateway_060zppy" targetRef="Gateway_1ox57hu" />
    <bpmn:sequenceFlow id="Flow_07fpdbq" sourceRef="Gateway_1ox57hu" targetRef="Gateway_0g303p6" />
    <bpmn:sequenceFlow id="Flow_0wmfen2" name="no" sourceRef="Gateway_0g303p6" targetRef="Activity_1nkm1qg" />
    <bpmn:sequenceFlow id="Flow_18zwhxo" sourceRef="Activity_1nkm1qg" targetRef="Gateway_1fo6g5k" />
    <bpmn:sequenceFlow id="Flow_1desvdz" name="no" sourceRef="Gateway_1fo6g5k" targetRef="Gateway_1ibn333" />
    <bpmn:sequenceFlow id="Flow_1p1mb7c" name="yes" sourceRef="Gateway_0ua1s7d" targetRef="Activity_04vg5am">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo.businessMode="B2C")=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0jaf0sk" name="yes" sourceRef="Gateway_0xup6sw" targetRef="Activity_0batlvx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and customerOrderInfo.orderSource="HANDLE") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all-test_0">
      <bpmndi:BPMNEdge id="Flow_0jaf0sk_di" bpmnElement="Flow_0jaf0sk">
        <di:waypoint x="196" y="245" />
        <di:waypoint x="196" y="300" />
        <di:waypoint x="440" y="300" />
        <di:waypoint x="440" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="310" y="282" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p1mb7c_di" bpmnElement="Flow_1p1mb7c">
        <di:waypoint x="805" y="195" />
        <di:waypoint x="805" y="144" />
        <di:waypoint x="831" y="144" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="812" y="163" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1desvdz_di" bpmnElement="Flow_1desvdz">
        <di:waypoint x="1495" y="410" />
        <di:waypoint x="1355" y="410" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1419" y="392" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18zwhxo_di" bpmnElement="Flow_18zwhxo">
        <di:waypoint x="1680" y="410" />
        <di:waypoint x="1545" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wmfen2_di" bpmnElement="Flow_0wmfen2">
        <di:waypoint x="1520" y="245" />
        <di:waypoint x="1680" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1608" y="298" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07fpdbq_di" bpmnElement="Flow_07fpdbq">
        <di:waypoint x="1395" y="220" />
        <di:waypoint x="1495" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vuw097_di" bpmnElement="Flow_1vuw097">
        <di:waypoint x="1141" y="195" />
        <di:waypoint x="1141" y="140" />
        <di:waypoint x="1350" y="140" />
        <di:waypoint x="1350" y="215" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1239" y="122" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jy4qoc_di" bpmnElement="Flow_0jy4qoc">
        <di:waypoint x="1305" y="410" />
        <di:waypoint x="1271" y="410" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1293" y="442" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00r41ll_di" bpmnElement="Flow_00r41ll">
        <di:waypoint x="1369" y="520" />
        <di:waypoint x="1330" y="520" />
        <di:waypoint x="1330" y="435" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v9tc21_di" bpmnElement="Flow_1v9tc21">
        <di:waypoint x="1520" y="435" />
        <di:waypoint x="1520" y="520" />
        <di:waypoint x="1469" y="520" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1527" y="475" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e2i45y_di" bpmnElement="Flow_1e2i45y">
        <di:waypoint x="1730" y="260" />
        <di:waypoint x="1730" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0um2gro_di" bpmnElement="Flow_0um2gro">
        <di:waypoint x="1545" y="220" />
        <di:waypoint x="1680" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1566" y="203" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k29ocj_di" bpmnElement="Flow_1k29ocj">
        <di:waypoint x="1501" y="141" />
        <di:waypoint x="1520" y="141" />
        <di:waypoint x="1520" y="195" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i5qkc2_di" bpmnElement="Flow_0i5qkc2">
        <di:waypoint x="1370" y="195" />
        <di:waypoint x="1370" y="141" />
        <di:waypoint x="1401" y="141" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1375" y="165" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r7yp5n_di" bpmnElement="Flow_0r7yp5n">
        <di:waypoint x="1308" y="220" />
        <di:waypoint x="1345" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x5tf4a_di" bpmnElement="Flow_1x5tf4a">
        <di:waypoint x="1166" y="220" />
        <di:waypoint x="1208" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1179" y="197" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i8l6j1_di" bpmnElement="Flow_0i8l6j1">
        <di:waypoint x="996" y="220" />
        <di:waypoint x="1116" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1076" y="202" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dvg2y4_di" bpmnElement="Flow_0dvg2y4">
        <di:waypoint x="931" y="144" />
        <di:waypoint x="951" y="144" />
        <di:waypoint x="951" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1js85f5_di" bpmnElement="Flow_1js85f5">
        <di:waypoint x="830" y="220" />
        <di:waypoint x="946" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="882" y="202" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16iidh0_di" bpmnElement="Flow_16iidh0">
        <di:waypoint x="1100" y="140" />
        <di:waypoint x="1120" y="140" />
        <di:waypoint x="1120" y="216" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bav04h_di" bpmnElement="Flow_1bav04h">
        <di:waypoint x="971" y="195" />
        <di:waypoint x="971" y="140" />
        <di:waypoint x="1000" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="975" y="165" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1augmii_di" bpmnElement="Flow_1augmii">
        <di:waypoint x="347" y="220" />
        <di:waypoint x="390" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hix2aj_di" bpmnElement="Flow_1hix2aj">
        <di:waypoint x="221" y="220" />
        <di:waypoint x="247" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="226" y="202" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11m11ab_di" bpmnElement="Flow_11m11ab">
        <di:waypoint x="544" y="195" />
        <di:waypoint x="544" y="126" />
        <di:waypoint x="576" y="126" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="527" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f8hwzx_di" bpmnElement="Flow_0f8hwzx">
        <di:waypoint x="490" y="220" />
        <di:waypoint x="519" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r2vc78_di" bpmnElement="Flow_1r2vc78">
        <di:waypoint x="1171" y="410" />
        <di:waypoint x="1058" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d6k2cp_di" bpmnElement="Flow_0d6k2cp">
        <di:waypoint x="746" y="220" />
        <di:waypoint x="780" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rx455b_di" bpmnElement="Flow_0rx455b">
        <di:waypoint x="569" y="220" />
        <di:waypoint x="646" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="578" y="223" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12n068l_di" bpmnElement="Flow_12n068l">
        <di:waypoint x="676" y="126" />
        <di:waypoint x="696" y="126" />
        <di:waypoint x="696" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kq91j6_di" bpmnElement="Flow_0kq91j6">
        <di:waypoint x="148" y="220" />
        <di:waypoint x="171" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_11ossyg_di" bpmnElement="Event_11ossyg">
        <dc:Bounds x="112" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="119" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0jmkq9a_di" bpmnElement="Activity_0jmkq9a">
        <dc:Bounds x="646" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cumgpb_di" bpmnElement="Event_0cumgpb">
        <dc:Bounds x="1022" y="392" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1029" y="368" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02o0ekv_di" bpmnElement="Gateway_02o0ekv" isMarkerVisible="true">
        <dc:Bounds x="519" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="512" y="253" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1a74hjo_di" bpmnElement="Activity_1a74hjo">
        <dc:Bounds x="576" y="86" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0us4sa7_di" bpmnElement="Activity_0us4sa7">
        <dc:Bounds x="247" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ewoj8n_di" bpmnElement="Activity_1ewoj8n">
        <dc:Bounds x="1000" y="100" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ep527t_di" bpmnElement="Gateway_0ep527t" isMarkerVisible="true">
        <dc:Bounds x="946" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="938" y="252" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1axpt8d_di" bpmnElement="Activity_1axpt8d">
        <dc:Bounds x="1208" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_116hi0z_di" bpmnElement="Activity_116hi0z">
        <dc:Bounds x="1401" y="101" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_012e3ua_di" bpmnElement="Activity_012e3ua">
        <dc:Bounds x="1680" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0c6r0no_di" bpmnElement="Activity_0c6r0no">
        <dc:Bounds x="1369" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0jl0sa4_di" bpmnElement="Activity_0jl0sa4">
        <dc:Bounds x="1171" y="370" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ox57hu_di" bpmnElement="Gateway_1ox57hu" isMarkerVisible="true">
        <dc:Bounds x="1345" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1340" y="252" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g303p6_di" bpmnElement="Gateway_0g303p6" isMarkerVisible="true">
        <dc:Bounds x="1495" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1528" y="193" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1fo6g5k_di" bpmnElement="Gateway_1fo6g5k" isMarkerVisible="true">
        <dc:Bounds x="1495" y="385" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1490" y="442" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ibn333_di" bpmnElement="Gateway_1ibn333" isMarkerVisible="true">
        <dc:Bounds x="1305" y="385" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1299" y="364" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0batlvx_di" bpmnElement="Activity_0batlvx">
        <dc:Bounds x="390" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ua1s7d_di" bpmnElement="Gateway_0ua1s7d" isMarkerVisible="true">
        <dc:Bounds x="780" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="794" y="252" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04vg5am_di" bpmnElement="Activity_04vg5am">
        <dc:Bounds x="831" y="104" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0xup6sw_di" bpmnElement="Gateway_0xup6sw" isMarkerVisible="true">
        <dc:Bounds x="171" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="168" y="171" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_060zppy_di" bpmnElement="Gateway_060zppy" isMarkerVisible="true">
        <dc:Bounds x="1116" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1104" y="252" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1nkm1qg_di" bpmnElement="Activity_1nkm1qg">
        <dc:Bounds x="1680" y="370" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
