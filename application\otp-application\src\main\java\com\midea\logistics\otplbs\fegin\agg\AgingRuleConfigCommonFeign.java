package com.midea.logistics.otplbs.fegin.agg;

import cn.hutool.db.PageResult;
import com.midea.logistics.otp.bean.agg.AgingRuleConfig;
import com.mideaframework.core.bean.BaseDomain;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @author: DOP Group GEN
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "AgingRuleConfigCommonFeign")
public interface AgingRuleConfigCommonFeign {

    @GetMapping("/aging/rule/config/list")
    JsonResponse<List<AgingRuleConfig>> agingRuleConfigList(@SpringQueryMap AgingRuleConfig entity);

    @GetMapping("/aging/rule/config/page")
    JsonResponse agingRuleConfigPage(@SpringQueryMap AgingRuleConfig entity);

    @GetMapping("/aging/rule/config/count")
    JsonResponse<BaseDomain> agingRuleConfigCount(@SpringQueryMap AgingRuleConfig entity);

    @GetMapping("/aging/rule/config/detail")
    JsonResponse<AgingRuleConfig> agingRuleConfigDetail(@RequestParam("id") Long id);

    @GetMapping("/aging/rule/config/detail")
    JsonResponse<AgingRuleConfig> agingRuleConfigDetail(@SpringQueryMap AgingRuleConfig entity);

    @PostMapping("/aging/rule/config/add")
    JsonResponse<BaseDomain> agingRuleConfigAdd(@RequestBody AgingRuleConfig entity);

    @PutMapping("/aging/rule/config/update")
    JsonResponse<Integer> agingRuleConfigUpdate(@RequestBody AgingRuleConfig entity);

    @DeleteMapping("/aging/rule/config/remove")
    JsonResponse<Integer> agingRuleConfigRemove(@RequestBody AgingRuleConfig entity);


    @PostMapping("/custom/agingRuleConfig/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<AgingRuleConfig> list);

}

