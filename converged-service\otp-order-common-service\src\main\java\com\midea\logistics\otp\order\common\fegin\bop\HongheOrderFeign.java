package com.midea.logistics.otp.order.common.fegin.bop;

import com.midea.logistics.logisticsbopsdk.constants.RequestUrlConstant;
import com.midea.logistics.otp.order.common.fegin.fallback.LtmsSignFeignFallback;
import com.midea.logistics.otp.order.domain.dto.OrderForSignDto;
import com.midea.logistics.otp.order.domain.request.*;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * Copyright 1968-2019 Midea Group,IT
 *
 * @fileName: HongheOrderFeign
 * @author: ex_zhangquan
 * @date: 2019/10/15 10:24
 * @description: 鸿合相关接口开发
 */
@FeignClient(value = "logistics-bop-service", fallback = LtmsSignFeignFallback.class)
public interface HongheOrderFeign {


    /**
     * 拉单接口
     */
    @PostMapping("/inner/T201904230000000014/udpApi/hhOrderQuery")
    JsonResponse<String> getHongheOrder(@RequestBody HongheOrderRequest bean);

    /**
     * 商品同步接口
     */
    @PostMapping("/inner/T201904230000000014/udpApi/hhItemSyscn")
    JsonResponse<String> sysGoods(@RequestBody HongheGoodsRequest bean);

    /**
     * 商品同步接口
     */
    @PostMapping("/inner/T201904230000000014/udpApi/itemSynch")
    JsonResponse<String> sysGoodsToEDI(@RequestBody HongHeItemProdBean bean);

    @PostMapping("/inner/T201904230000000014/udpApi/route/returnOrderUpload")
    JsonResponse<String> returnOrderUpload(@RequestBody ReturnOrderUploadRequest bean);

    @PostMapping("/inner/T201904230000000014/udpApi/SubmitDebriefByParty")
    JsonResponse<String> SubmitDebriefByParty(@RequestBody String hongheServiceRequest);


    /**
     * 商品同步接口新
     * @param hongheGoodsRequest
     * @return
     */
    @PostMapping("/inner/T201904230000000014/udpApi/hhItemSyscnNew")
    JsonResponse<String> sysGoodsNew(HongheGoodsRequest hongheGoodsRequest);

    /**
     * 鸿合拉单接口新
     * @param request
     * @return
     */
    @PostMapping("/inner/T201904230000000014/udpApi/hhOrderQueryNew")
    JsonResponse<String> getHongheOrderNew(HongheOrderRequest request);
}
