package com.midea.logistics.imp.orderverify.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.midea.logistics.otp.bean.CustomerConfigDTO;
import com.midea.logistics.otp.common.utils.Assert;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.common.service.SCCOrderStockCheckService;
import com.midea.logistics.otp.order.common.utils.StockShortageResponseUtil;
import com.midea.logistics.otp.order.domain.dto.IssuedItemExt;
import com.midea.logistics.otp.order.domain.dto.OrderExtendConfDto;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.mideaframework.core.utils.thread.ThreadLocals;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.midea.logistics.otp.common.utils.Assert;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.common.service.SCCOrderStockCheckService;
import com.mideaframework.core.utils.thread.ThreadLocals;
import com.midea.logistics.otp.order.common.utils.StockShortageResponseUtil;
import com.mideaframework.core.utils.thread.ThreadLocals;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.ControlParamManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.ControlParam;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.LmpSeparateWarehouseService;
import com.midea.logistics.logisticsbopsdk.bean.WmsSameWhCodeValidateDto;
import com.midea.logistics.logisticsbopsdk.service.OtpService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.ordertask.TaskApiFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.ControlParamFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.orderAgg.OrderAggFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.CompartRuleFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.CustomerConfigFeign;
import com.midea.logistics.otp.common.helper.*;
import com.midea.logistics.otp.common.request.CreateOrderContext;
import com.midea.logistics.otp.common.request.OrderStockCheckDto;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.bean.OrderSplitDto;
import com.midea.logistics.otp.order.common.es.EsOrderLogService;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.constant.WorkflowConstant;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.common.mq.producer.OrderAuditMessageProducer;
import com.midea.logistics.otp.order.common.mq.producer.PledgeCancelProducer;
import com.midea.logistics.otp.order.common.mq.producer.WorkflowVerifyProducer;
import com.midea.logistics.otp.order.common.mq.producer.ZeebeOrderProducer;
import com.midea.logistics.otp.order.common.service.CenterInvService;
import com.midea.logistics.otp.order.common.service.SCCOrderStockCheckService;
import com.midea.logistics.otp.order.common.utils.StockShortageResponseUtil;
import com.midea.logistics.otp.order.converged.domain.request.SeparateWarehouseConfirmRequest;
import com.midea.logistics.otp.order.converged.domain.response.SeparateWarehouseSearchItemResponse;
import com.midea.logistics.otp.order.converged.domain.response.SeparateWarehouseSearchOrderResponse;
import com.midea.logistics.otp.order.converged.domain.response.SeparateWarehouseSearchResponse;
import com.midea.logistics.otp.order.converged.domain.response.SeparateWarehouseSearchWarehouseResponse;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.request.CenterInvQueryReq;
import com.midea.logistics.otp.order.domain.response.CenterInvQueryItem;
import com.midea.logistics.otp.order.domain.response.CenterInvQueryResponse;
import com.midea.logistics.otp.order.domain.response.WhetherApartedResponse;
import com.midea.logistics.otp.rule.domain.bean.CompartRule;
import com.midea.logistics.otp.rule.domain.bean.CustomerConfig;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.auth.bean.UserInfo;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.utils.thread.ThreadLocals;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.OpenJsonResponse;
import com.mideaframework.core.web.PageResponse;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LmpSeparateWarehouseServiceImpl implements LmpSeparateWarehouseService {
    /**
     * 父单货值透传下发 字典
     */
    private static final String ORDER_VALUE_ISSUE = "LOMS_ORDER_VALUE_ISSUE";

    @Autowired
    CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private CenterInvService centerInvService;
    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    private OrderFeign orderFeign;
    @Autowired
    CustomerConfigFeign customerConfigFeign;
    @Autowired
    CompartRuleFeign compartRuleFeign;
    @Autowired
    CustomerOrderItemFeign customerOrderItemFeign;
    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private IdGenHelper idGenHelper;
    @Autowired
    private OtpService otpService;
    @Autowired
    private PledgeCancelProducer pledgeCancelProducer;

    @Autowired
    private WorkflowVerifyProducer workflowVerifyProducer;
    @Autowired
    private RedisLockHelper redisLockHelper;

    @Autowired
    private EsOrderLogService esOrderLogServiceImpl;
     @Autowired
    private PartCancelSplitRedisLockHelper partCancelSplitRedisLockHelper;
    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private ZeebeOrderProducer zeebeOrderProducer;
    @Autowired
    private ControlParamFeign controlParamFeign;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private FlowRuleHelper flowRuleHelper;
    @Autowired
    private ControlParamManager controlParamManager;
    @Autowired
    private TmallHelper tmallHelper;
    @Autowired
    private BusinessParamHelper businessParamHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;
    @Autowired
    private OrderExtendFeign orderExtendFeign;
    @Autowired
    private AutoSeparateWarehouseHelper autoSeparateWarehouseHelper;
    @Autowired
    private BusinessHelper businessHelper;

    @Override
    public SeparateWarehouseSearchResponse search(String orderNo) {
        SeparateWarehouseSearchResponse response = new SeparateWarehouseSearchResponse();

        List<SeparateWarehouseSearchItemResponse> itemList = Lists.newArrayList();
        List<SeparateWarehouseSearchOrderResponse> orderList = Lists.newArrayList();
        //查询客户订单
        CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
        customerOrderInfo.setOrderNo(orderNo);
        JsonResponse<CustomerOrderInfo> customerOrderInfoJsonResponse = customerOrderInfoFeign.searchOne(customerOrderInfo);
        if (null == customerOrderInfoJsonResponse || null == customerOrderInfoJsonResponse.data()) {
            throw BusinessException.fail("未查询到订单信息");
        }
        customerOrderInfo = customerOrderInfoJsonResponse.data();
        if (OrderType.YS.getKey().equalsIgnoreCase(customerOrderInfo.getOrderType())
            && CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag())) {
            throw BusinessException.fail("模糊订单不支持分仓");
        }
        String whCode = customerOrderInfo.getWhCode();
        //查询已分配的订单
        JsonResponse<List<OrderInfo>> orderInfoResponse = orderFeign.selectOrderInfoByOrderNo(Lists.newArrayList(orderNo));
        if (null == orderInfoResponse
            || CollectionUtils.isEmpty(orderInfoResponse.data)) {

            //throw BusinessException.fail("未查询到子订单信息");
        } else {
            List<OrderInfo> orderInfoList = orderInfoResponse.data;
            List<String> orderNos = Lists.newArrayList();
            orderInfoList.forEach(
                order -> {
                    SeparateWarehouseSearchOrderResponse separateWarehouseSearchOrderResponse = new SeparateWarehouseSearchOrderResponse();
                    BeanUtils.copyProperties(order, separateWarehouseSearchOrderResponse);
                    orderList.add(separateWarehouseSearchOrderResponse);
                    orderNos.add(order.getOrderNo());
                }
            );
            response.setOrderList(orderList);
        }
        //
        JsonResponse<WhetherApartedResponse> whetherApartedResponseJsonResponse = customerOrderInfoFeign.whetherAparted(orderNo);
        Map<String, Map<Integer, WhetherApartedResponse.WhetherApartedItem>> whetherApartedItemMap = Maps.newHashMap();
        if (whetherApartedResponseJsonResponse != null && whetherApartedResponseJsonResponse.data() != null) {
            whetherApartedItemMap = whetherApartedResponseJsonResponse.data().getItems().stream().collect(Collectors.groupingBy(WhetherApartedResponse.WhetherApartedItem::getItemCode
                , Collectors.groupingBy(WhetherApartedResponse.WhetherApartedItem::getItemLineNo, Collectors.collectingAndThen(Collectors.toList(), v -> v.get(0)))));
        }
        //查询客户订单商品信息
        JsonResponse<List<CustomerOrderItem>> customerOrderItemResponse = customerOrderInfoFeign.listCustomOrderItemsByOrderNos(Lists.newArrayList(customerOrderInfo.getOrderNo()));
        if (null == customerOrderItemResponse || null == customerOrderItemResponse.data
            || CollectionUtils.isEmpty(customerOrderItemResponse.data)) {
            throw BusinessException.fail("未查询到订单商品信息");
        }
        item:
        for (CustomerOrderItem item : customerOrderItemResponse.data) {
            SeparateWarehouseSearchItemResponse separateWarehouseSearchItemResponse = new SeparateWarehouseSearchItemResponse();
            BeanUtils.copyProperties(item, separateWarehouseSearchItemResponse);
            //计算已分配和未分配的数量
            separateWarehouseSearchItemResponse.setAllocatedNum(whetherApartedItemMap.get(item.getItemCode()).get(item.getItemLineNo()).getAllocatedNum());
            separateWarehouseSearchItemResponse.setUnallocatedNum(whetherApartedItemMap.get(item.getItemCode()).get(item.getItemLineNo()).getUnallocatedNum());
            if (ToolUtils.isNotEmpty(separateWarehouseSearchItemResponse.getItemStatus())) {
                separateWarehouseSearchItemResponse.setItemStatusName(ItemStatus.getName(separateWarehouseSearchItemResponse.getItemStatus()));
            }
            List<SeparateWarehouseSearchWarehouseResponse> warehouseList = Lists.newArrayList();
            boolean isShare = JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType());

            if (
                //分拨单，分仓的时候，根据平台查询平台下的仓库
                OrderType.DO.getKey().equals(customerOrderInfo.getOrderType())
                    //入库订单不查询库存,列出平台下的所有仓库
                    || (InOutType.IN.getName().equals(customerOrderInfo.getInOutType()) && !isShare)
            ) {
                List<CdWarehouse> cdWarehouseListCacheFromSiteCode = cdWarehouseManager.getCdWarehouseListCacheFromSiteCode(customerOrderInfo.getSiteCode());
                cdWarehouseListCacheFromSiteCode.stream().forEach(
                    wh -> {
                        //查询仓库的时候,要取有效的那个,即cdwhIsStop=0
                        if (!Double.valueOf(CommonConstant.FLAG_NO).equals(wh.getCdwhIsStop())) {
                            return;
                        }
                        SeparateWarehouseSearchWarehouseResponse separateWarehouseSearchWarehouseResponse = new SeparateWarehouseSearchWarehouseResponse();
                        separateWarehouseSearchWarehouseResponse.setCount(BigDecimal.ZERO);
                        separateWarehouseSearchWarehouseResponse.setWhCode(wh.getWhCode());
                        separateWarehouseSearchWarehouseResponse.setWhName(wh.getCdwhName());
                        if (ToolUtils.isEmpty(whCode) || wh.getWhCode().equals(whCode)) {
                            warehouseList.add(separateWarehouseSearchWarehouseResponse);
                        }
                    }
                );
                if (ToolUtils.isEmpty(warehouseList)) {
                    throw BusinessException.fail("平台：" + customerOrderInfo.getSiteCode() + "没有可用的仓库");
                }
            } else {


                if (//纯运输和直发不查库存
                    OrderType.YS == EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType())
                        || OrderType.ZF == EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType())) {
                    SeparateWarehouseSearchWarehouseResponse separateWarehouseSearchWarehouseResponse = new SeparateWarehouseSearchWarehouseResponse();
                    separateWarehouseSearchWarehouseResponse.setCount(BigDecimal.ZERO);
                    separateWarehouseSearchWarehouseResponse.setWhCode(CommonConstant.WHCODE_FLAG);
                    separateWarehouseSearchWarehouseResponse.setWhName("纯运输");
                    warehouseList.add(separateWarehouseSearchWarehouseResponse);
                    separateWarehouseSearchItemResponse.setWarehouseList(warehouseList);
                    itemList.add(separateWarehouseSearchItemResponse);
                    continue item;
                }
                //通过客户，平台，商品查询仓库
                CenterInvQueryReq centerInvQueryReq = new CenterInvQueryReq();
                centerInvQueryReq.setInvType("custInv");
                //货权转移，手工分仓时，点击分仓按钮，RI&SHARE的订单，根据targetCustomerCode查库存
                centerInvQueryReq.setOwnerCode(customerOrderInfo.getCustomerCode());
                if (OrderType.RI.getKey().equals(customerOrderInfo.getOrderType())
                    && JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType())) {
                    centerInvQueryReq.setOwnerCode(customerOrderInfo.getTargetCustomerCode());
                }
                centerInvQueryReq.setSiteCode(customerOrderInfo.getSiteCode());
                centerInvQueryReq.setCompanyCode(customerOrderInfo.getCompanyCode());
                centerInvQueryReq.setCustItemCode(item.getCustomerItemCode());
                centerInvQueryReq.setItemCode(item.getItemCode());
                centerInvQueryReq.setItemStatus(item.getItemStatus());
                if (!ToolUtils.isEmpty(whCode)) {
                    centerInvQueryReq.setWhCode(whCode);
                }
                centerInvQueryReq.setPageNo(1);
                centerInvQueryReq.setPageSize(1000);
                CenterInvQueryResponse centerInvQueryResponsePageResponse = centerInvService.queryCenterInvInfo(centerInvQueryReq);

                if (null == centerInvQueryResponsePageResponse || ToolUtils.isEmpty(centerInvQueryResponsePageResponse.getItems())) {
                    //throw BusinessException.fail("未查询到库存信息");
                    itemList.add(separateWarehouseSearchItemResponse);
                    continue item;
                }
                List<CenterInvQueryItem> custInvList = centerInvQueryResponsePageResponse.getItems();
                List<String> sameWhCodeList = null;
                if (isShare) {
                    List<String> whcodeList = custInvList.stream().map(CenterInvQueryItem::getWhCode).collect(Collectors.toList());
                    sameWhCodeList = validateSameWarehouseCode(customerOrderInfo.getCustomerCode(), customerOrderInfo.getTargetCustomerCode(), whcodeList);
                }
                wh:
                for (CenterInvQueryItem centerInvInfo : custInvList) {
                    if (isShare && ToolUtils.isEmpty(sameWhCodeList)) {
                        break wh;
                    }
                    if (isShare && !sameWhCodeList.contains(centerInvInfo.getWhCode())
                    ) {
                        log.info("separateWarehouseSearch=仓库不同仓的========={}==>{}", sameWhCodeList, centerInvInfo.getWhCode());
                        continue wh;
                    }
                    SeparateWarehouseSearchWarehouseResponse separateWarehouseSearchWarehouseResponse = new SeparateWarehouseSearchWarehouseResponse();
                    separateWarehouseSearchWarehouseResponse.setCount(centerInvInfo.getEffectQtyEa() == null ? BigDecimal.ZERO : centerInvInfo.getEffectQtyEa());
                    separateWarehouseSearchWarehouseResponse.setWhCode(centerInvInfo.getWhCode());
                    if (separateWarehouseSearchWarehouseResponse.getWhCode() != null) {
                        CdWarehouse cdWarehouseCache = cdWarehouseManager.getCdWarehouseCache(centerInvInfo.getWhCode());
                        if (cdWarehouseCache != null) {
                            separateWarehouseSearchWarehouseResponse.setWhName(cdWarehouseCache.getCdwhName());
                        }
                    }
                    if (ToolUtils.isEmpty(whCode) || separateWarehouseSearchWarehouseResponse.getWhCode().equals(whCode)) {
                        warehouseList.add(separateWarehouseSearchWarehouseResponse);
                    }
                }
            }

            separateWarehouseSearchItemResponse.setWarehouseList(warehouseList);
            itemList.add(separateWarehouseSearchItemResponse);
        }
        //判断是否有库存存在
        List<Integer> list = Lists.newArrayList();
        itemList.stream().forEach(
            i -> {
                if (ToolUtils.isNotEmpty(i.getWarehouseList())) {
                    list.add(i.getWarehouseList().size());
                }
            }
        );
        if (list.size() <= 0) {
            throw BusinessException.fail("未查询到库存信息");
        }
        response.setItemList(itemList);
        return response;
    }

    @Override
    public List<String> confirm(String orderNo, boolean compartFlag, List<SeparateWarehouseConfirmRequest> separateWarehouseConfirmRequestList) {
        // 检查 lock
        String key = "SEPARATEWAREHOUSE:CONFIRM" + orderNo;
        try {
            boolean lock = redisLockHelper.tryLock(key, 30L);
            if (!lock) {
                throw BusinessException.fail("分仓过于频繁，请稍后再试！");
            }
            OrderSplitDto confirm = confirm(orderNo, compartFlag, separateWarehouseConfirmRequestList, true);
            OrderLog orderLog = new OrderLog();
            orderLog.setOperateFlag("Y");
            orderLog.setOperateType(OrderOperateType.APART_HAND.getKey());
            orderLog.setParentOrderNo(orderNo);
            if (CollectionUtils.isEmpty(confirm.getOrderNos()) && CollectionUtils.isEmpty(confirm.getOrderInfoExts())) {
                orderLog.setOperateFlag("N");
                orderLog.setOperateContent(confirm.getErrMsg());
                esOrderLogServiceImpl.saveLog(orderLog);
                throw BusinessException.fail(confirm.getErrMsg());
            }
            orderLog.setOrderNo(confirm.getOrderNos().get(0));
            orderLog.setOperateContent("子单单号：" + confirm.getOrderNos().get(0));
            esOrderLogServiceImpl.saveLog(orderLog);
//            // 已经执行的 流程节点
//            WorkflowNodeInstance nodeInstanceParam = new WorkflowNodeInstance();
//            nodeInstanceParam.setWorkflowTicket(orderNo);
//            nodeInstanceParam.setTemplateCode(WorkflowConstant.CUSTOMER_ORDER);
//            JsonResponse<List<WorkflowNodeInstance>> nodeIns = workflowFeign.getWorkflowNodeInstances(nodeInstanceParam);
//            List<WorkflowNodeInstance> nodeInstances = nodeIns.data();
//            if (ToolUtils.isEmpty(nodeInstances)){
//                return confirm.getOrderNos();
//            }
//            // 待执行的节点
//            WorkflowNodeInstance nodeInstance = nodeInstances.get(nodeInstances.size() - 1);
//            // 如果是货权转移失败的直接结束父单流程
//            if ("customer_order_cargo_right_transfer".equals(nodeInstance.getNodeCode()) && nodeInstance.getFlowNodeStatus() != 0) {
//                // 流程获取 ，获取需要跑的流程
//                WorkflowInstance workflowInstance = new WorkflowInstance();
//                workflowInstance.setTemplateCode(WorkflowConstant.CUSTOMER_ORDER);
//                workflowInstance.setWorkflowTicket(orderNo);
//                JsonResponse<WorkflowInstance> workflowInstanceRespone = workflowFeign.getWorkflowInstance(workflowInstance);
//                if (workflowInstance.getEndFlag() != 1) {
//                    workflowInstance.setEndTime(new Date());
//                    workflowInstance.setEndFlag(1);
//                    workflowInstanceFeign.update(workflowInstance.getId(), workflowInstance);
//                }
//            }
            return confirm.getOrderNos();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw BusinessException.fail(e.getMessage());
        } finally {
            redisLockHelper.unLock(key);
        }
    }

    /**
     * 【此方法内部不允许有 任何的 throw Exception】
     *
     * @param orderNo
     * @param compartFlag                         是否占用库存
     * @param separateWarehouseConfirmRequestList
     * @param isHandWork                          是否手工分仓
     * @return
     */
    public OrderSplitDto confirm(String orderNo, boolean compartFlag, List<SeparateWarehouseConfirmRequest> separateWarehouseConfirmRequestList, boolean isHandWork) {
        if (ToolUtils.isEmpty(separateWarehouseConfirmRequestList)) {
            log.warn("分仓失败，必填数据不能为空");
            return OrderSplitDto.fail("分仓失败，必填数据不能为空");
        }
        //记录所有商品的拆分状态
        List<String> apartStatusList = Lists.newArrayList();
        String apartStatus = CommonConstant.APARTED;
        //校验订单商品数量
        JsonResponse<WhetherApartedResponse> whetherApartedResponseJsonResponse = customerOrderInfoFeign.whetherAparted(orderNo);

        if (whetherApartedResponseJsonResponse == null || whetherApartedResponseJsonResponse.data() == null) {
            return OrderSplitDto.fail("校验订单商品失败");
        }
        Map<String, Map<Integer, List<SeparateWarehouseConfirmRequest>>> listMap = separateWarehouseConfirmRequestList.stream()
            .collect(Collectors.groupingBy(SeparateWarehouseConfirmRequest::getItemCode, Collectors.groupingBy(SeparateWarehouseConfirmRequest::getItemLineNo)));
        WhetherApartedResponse whetherApartedResponse = whetherApartedResponseJsonResponse.data();
        for (WhetherApartedResponse.WhetherApartedItem item : whetherApartedResponse.getItems()) {

            if (ToolUtils.isEmpty(listMap.get(item.getItemCode())) || ToolUtils.isEmpty(listMap.get(item.getItemCode()).get(item.getItemLineNo()))) {
                if (item.getUnallocatedNum().intValue() == 0) {
                    apartStatusList.add(CommonConstant.APARTED);
                } else {
                    apartStatusList.add(CommonConstant.PART);
                }
                continue;
            }
            double sumCount = listMap.get(item.getItemCode()).get(item.getItemLineNo()).stream().mapToDouble(SeparateWarehouseConfirmRequest::getCount).sum();
            if (sumCount <= 0) {
                return OrderSplitDto.fail((String.format("商品itemCode:{%s},行号itemLineNo{%s} 分配数量不能为{%s}", item.getItemCode(), item.getItemLineNo(), sumCount)));
            }
            BigDecimal sum = item.getUnallocatedNum().subtract(BigDecimal.valueOf(sumCount));
            if (sum.intValue() < 0) {
                return OrderSplitDto.fail(String.format("校验订单商品失败,商品itemCode:{%s},行号itemLineNo{%s} 数量超过了可分配数量", item.getItemCode(), item.getItemLineNo()));
            }
            if (sum.intValue() == 0) {
                apartStatusList.add(CommonConstant.APARTED);
            } else {
                apartStatusList.add(CommonConstant.PART);
            }
        }
        //只要有一种商品没拆完，就是部分拆单
        if (apartStatusList.contains(CommonConstant.PART)) {
            apartStatus = CommonConstant.PART;
        }
        //查询客户订单
        CustomerOrderInfo customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfo(orderNo);
        if (ToolUtils.isNotEmpty(customerOrderInfo.getOrderStatus()) && customerOrderInfo.getOrderStatus().intValue() >= OrderStatus.CLOSED.getKey().intValue()) {
            return OrderSplitDto.fail("分仓失败,请重新检查订单状态;");
        }
        if (CommonConstant.APARTED.equals(customerOrderInfo.getApartStatus())) {
            throw BusinessException.fail("分仓失败，拆单已完成，请检查拆单状态！");
        }
        if (OrderType.YS.getKey().equalsIgnoreCase(customerOrderInfo.getOrderType())
            && CommonConstant.FLAG_YES.equals(customerOrderInfo.getPlanOrderFlag())) {
            return OrderSplitDto.fail("模糊订单不支持分仓");
        }
        customerOrderInfo.setUpdateUserName(null);
        customerOrderInfo.setUpdateUserCode(null);
        CustomerOrderAddress address = lmpOrderFlowHelper.getCustomerOrderAddress(orderNo);

        //查询客户订单商品信息
        JsonResponse<List<CustomerOrderItem>> customerOrderItemResponse = customerOrderInfoFeign.listCustomOrderItemsByOrderNos(Lists.newArrayList(customerOrderInfo.getOrderNo()));
        if (null == customerOrderItemResponse || CollectionUtils.isEmpty(customerOrderItemResponse.data)) {
            return OrderSplitDto.fail("未查询到订单商品信息");
        }
        List<CustomerOrderItem> customerOrderItems = Lists.newArrayList();
        Map<String, Map<Integer, List<CustomerOrderItem>>> customerOrderItemMaps = Maps.newHashMap();
        //根据商品分组
        try {
            customerOrderItemMaps = customerOrderItemResponse.data
                .stream().collect(Collectors.groupingBy(CustomerOrderItem::getItemCode, Collectors.groupingBy(CustomerOrderItem::getItemLineNo)));
        } catch (NullPointerException e) {
            log.error(e.getMessage(), e);
            return OrderSplitDto.fail("请检查商品信息！商品行号或者商品编码为空");
        }

        //一个仓库一张订单，所以根据仓库分组
        Map<String, List<SeparateWarehouseConfirmRequest>> separateWarehouseConfirmMap = separateWarehouseConfirmRequestList
            .stream().collect(Collectors.groupingBy(SeparateWarehouseConfirmRequest::getWhCode));
        boolean notAparted = !CommonConstant.APARTED.equals(apartStatus) || separateWarehouseConfirmMap.values().size() > 1;
        //订单类型是DO的，如果手动分仓的时候，要做个强校验，不允许拆单
        String apartType = null;
        if (isHandWork) {
            if (OrderType.DO.getKey().equals(customerOrderInfo.getOrderType()) && notAparted) {
                return OrderSplitDto.fail("分拨单，不允许拆单");
            }
            CustomerConfig customerConfig = getCustomerConfig(customerOrderInfo);
            if (null != customerConfig && ApartType.UNALLOWED.getKey().equals(customerConfig.getApartType())) {
                if (notAparted) {
                    return OrderSplitDto.fail("当前订单类型不支持拆单");
                }
                OrderInfo search1 = new OrderInfo();
                search1.setParentOrderNo(orderNo);
                List<OrderInfo> data = orderInfoFeign.list(search1).data();
                if (ToolUtils.isNotEmpty(data)) {
                    return OrderSplitDto.fail("当前订单类型不支持拆单");
                }
                apartType = ApartType.UNALLOWED.getKey();
            }
        }
        Integer waybillNo = orderFeign.getWaybillNoByCustomerOrderNo(customerOrderInfo.getCustomerOrderNo()).data();
        waybillNo = null == waybillNo ? 0 : waybillNo;
        List<OrderInfo> orderInfoList = Lists.newArrayList();
        List<OrderInfoItem> orderInfoItemList = Lists.newArrayList();
        List<OrderInfoExt> orderInfoExts = Lists.newArrayList();
        List<OrderInfoExt> orderInfoExtsBack = Lists.newArrayList();

        Map<String, Map<Integer, WhetherApartedResponse.WhetherApartedItem>> whetherApartedItemMap = whetherApartedResponse.getItems().stream()
            .collect(Collectors.groupingBy(WhetherApartedResponse.WhetherApartedItem::getItemCode,
                Collectors.groupingBy(WhetherApartedResponse.WhetherApartedItem::getItemLineNo, Collectors.collectingAndThen(Collectors.toList(), v -> v.get(0)))));

        order:
        for (List<SeparateWarehouseConfirmRequest> whs : separateWarehouseConfirmMap.values()) {
            OrderInfoExt orderInfoExt = new OrderInfoExt();
            waybillNo++;
            List<OrderInfoItem> orderInfoItems = Lists.newArrayList();

            OrderInfo orderInfo = generateOrder(customerOrderInfo);
            if (address != null) {
                BeanUtils.copyProperties(address, orderInfo
                    , "id", "orderNo", "remark", "planOrderFlag", "tenantCode");
            }
            //数量
            BigDecimal totalQty = BigDecimal.ZERO;
            //体积
            BigDecimal totalVolume = BigDecimal.ZERO;
            //重量
            BigDecimal totalGrossWeight = BigDecimal.ZERO;
            //净重
            BigDecimal totalNetWeight = BigDecimal.ZERO;
            //货值
            BigDecimal orderValue = BigDecimal.ZERO;
            item:
            for (SeparateWarehouseConfirmRequest wh : whs) {
                //纯运输订单没有仓库
                if (!CommonConstant.WHCODE_FLAG.equals(wh.getWhCode())) {
                    orderInfo.setWhCode(wh.getWhCode());
                    String whName = cdWarehouseManager.getCdWarehouseNameByWhCode(wh.getWhCode());
                    orderInfo.setWhName(whName);
                }
                //生成订单商品信息
                OrderInfoItem orderInfoItem = new OrderInfoItem();
                orderInfoItem.setItemCode(wh.getItemCode());
                if (ToolUtils.isEmpty(customerOrderItemMaps.get(wh.getItemCode())) ||
                    ToolUtils.isEmpty(customerOrderItemMaps.get(wh.getItemCode()).get(wh.getItemLineNo()))) {
                    continue item;
                }

                CustomerOrderItem customerOrderItem = customerOrderItemMaps.get(wh.getItemCode()).get(wh.getItemLineNo()).get(0);
                //复制客户订单商品信息到新生成的订单商品
                BeanUtils.copyProperties(customerOrderItem, orderInfoItem
                    , "id", "cancleQty", "splitQty", "orderNo", "actQty");
                orderInfoItem.setPlanQty(BigDecimal.valueOf(wh.getCount()));
                orderInfoItem.setParentOrderNo(orderInfo.getParentOrderNo());
                orderInfoItem.setOrderNo(orderInfo.getOrderNo());
                orderInfoItem.setSubOrderNo(customerOrderItem.getSubOrdercode());
                orderInfoItem.setUpdateUserName(null);
                orderInfoItem.setUpdateUserCode(null);
                orderInfoItem.setCreateUserCode(null);
                orderInfoItem.setCreateUserName(null);
                orderInfoItem.setPackingAmount(customerOrderItem.getPackingAmount());

                orderverifyHelper.setCCSInstallType(orderInfo, customerOrderItem);

                //设置子单商品表的总体积、总毛重
                BigDecimal volume = Optional.ofNullable(orderInfoItem.getVolume()).orElse(BigDecimal.ZERO); // 单台体积
                BigDecimal grossWeight = Optional.ofNullable(orderInfoItem.getGrossWeight()).orElse(BigDecimal.ZERO); // 单台毛重
                BigDecimal planQty = orderInfoItem.getPlanQty(); // 计划数量
                orderInfoItem.setTotalVolume(planQty.multiply(volume));
                orderInfoItem.setTotalGrossWeight(planQty.multiply(grossWeight));
                if (customerOrderItem.getTotalVolume() != null || customerOrderItem.getTotalGrossWeight() != null) {
                    BigDecimal unallocatedNum = Optional.ofNullable(whetherApartedItemMap.get(customerOrderItem.getItemCode()))
                        .map(m -> m.get(customerOrderItem.getItemLineNo())).map(item -> item.getUnallocatedNum()).orElse(BigDecimal.ZERO); //未分配数量
                    if (planQty.compareTo(unallocatedNum) == 0) { //拆完当前子单后当前商品行剩余未分配数量为0
                        //已分配数量
                        BigDecimal allocatedNum = Optional.ofNullable(whetherApartedItemMap.get(customerOrderItem.getItemCode()).get(customerOrderItem.getItemLineNo()).getAllocatedNum()).orElse(BigDecimal.ZERO);
                        //子单商品行的总体积=父单该商品行总体积-已分配总体积
                        Optional.ofNullable(customerOrderItem.getTotalVolume()).ifPresent(v -> orderInfoItem.setTotalVolume(v.subtract(allocatedNum.multiply(volume))));
                        //子单商品行的总毛重=父单该商品行总毛重-已分配总毛重
                        Optional.ofNullable(customerOrderItem.getTotalGrossWeight()).ifPresent(v -> orderInfoItem.setTotalGrossWeight(v.subtract(allocatedNum.multiply(grossWeight))));
                    }
                }

                orderInfoItemList.add(orderInfoItem);
                orderInfoItems.add(orderInfoItem);
                //计算订单
                totalQty = totalQty.add(orderInfoItem.getPlanQty());
                // B2C B2C的单台体积、单台重量都不能为空
                // B2B 的体积不能为空
                //菜鸟小件，需要判断数量是否符合箱规数量
                if (SourceSystem.CAINIAO.getKey().equals(customerOrderInfo.getSourceSystem()) && StringUtils.isNotEmpty(customerOrderInfo.getGrayFlag()) && ItemStatus.AH.getKey().equals(customerOrderInfo.getGrayFlag())) {
                    if (null == orderInfoItem.getPackingAmount() && null == orderInfoItem.getPackingDecimalAmount()) {
                        return OrderSplitDto.fail("菜鸟小件箱规数量为空!");
                    }
                    BigDecimal packingAmount = orderInfoItem.getPackingDecimalAmount() != null ? orderInfoItem.getPackingDecimalAmount() : new BigDecimal(orderInfoItem.getPackingAmount());
                    log.info("packingAmount:{}", packingAmount);
                    if (planQty.remainder(packingAmount).compareTo(BigDecimal.ZERO) != 0) {
                        return OrderSplitDto.fail("拆单不符合箱规,必须整箱!");
                    }
                }
                boolean hkwsB2bWbkh = false;
                //sourceSystem=HKWSB2B-WBKH 并且订单明细isFirst =0,审核新品不校验体积重量
                if (SourceSystem.HKWSB2B_WBKH.getKey().equals(customerOrderInfo.getSourceSystem())){
                    hkwsB2bWbkh = CommonConstant.FLAG_NO.equals(orderInfoItem.getIsFirst());
                }

                if (!hkwsB2bWbkh){
                    if (BusinessMode.isB2B(customerOrderInfo.getBusinessMode()) && orderInfoItem.getVolume() == null) {
                        return OrderSplitDto.fail("B2B订单 体积 不能同时为空");
                    }
                    if (BusinessMode.isB2C(customerOrderInfo.getBusinessMode()) && ((orderInfoItem.getGrossWeight() == null || orderInfoItem.getVolume() == null))) {
                        return OrderSplitDto.fail("B2C订单 毛重 和 体积 不能同时为空");
                    }
                }

                if (orderInfoItem.getTotalGrossWeight() != null) {
                    totalGrossWeight = totalGrossWeight.add(orderInfoItem.getTotalGrossWeight());
                } else if (orderInfoItem.getGrossWeight() != null) {
                    BigDecimal grossWeightTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getGrossWeight());
                    totalGrossWeight = totalGrossWeight.add(grossWeightTemp);
                }

                if (orderInfoItem.getTotalVolume() != null) {
                    totalVolume = totalVolume.add(orderInfoItem.getTotalVolume());
                } else if (orderInfoItem.getVolume() != null) {
                    BigDecimal volumeTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getVolume());
                    totalVolume = totalVolume.add(volumeTemp);
                }

                if (orderInfo.getTotalNetWeight() != null) {
                    totalNetWeight = orderInfo.getTotalNetWeight();
                } else if (orderInfoItem.getNetWeight() != null) {
                    BigDecimal netWeightTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getNetWeight());
                    totalNetWeight = totalNetWeight.add(netWeightTemp);
                }

                if (orderInfoItem.getVolume() != null) {
                    //CCS订单子单货值：sum(price*planQty)/10000；无单价，或price=0，则货值为0；
                    if (SourceSystem.CCS.getKey().equals(customerOrderInfo.getSourceSystem()) || SourceSystem.OFC.getKey().equals(customerOrderInfo.getSourceSystem())
                        //|| SourceSystem.HANDLE.getKey().equals(customerOrderInfo.getSourceSystem())
                    ) {
                        if (ObjectUtils.defaultIfNull(orderInfoItem.getPrice(), BigDecimal.ZERO).doubleValue() == 0d) {
                            orderValue = orderValue.add(BigDecimal.ZERO);
                        } else {
                            //orderValue =
                            BigDecimal orderValueTemp = ObjectUtils.defaultIfNull(orderInfoItem.getPlanQty(), BigDecimal.ZERO)
                                .multiply(ObjectUtils.defaultIfNull(orderInfoItem.getPrice(), BigDecimal.ZERO)).divide(new BigDecimal(10000), 8, BigDecimal.ROUND_HALF_UP);
                            orderValue = orderValue.add(orderValueTemp);
                        }
                    }
                }

            }
            //计算订单数量，体积，重量
            orderInfo.setTotalQty(totalQty);
            orderInfo.setTotalVolume(totalVolume);
            orderInfo.setTotalGrossWeight(totalGrossWeight);
            orderInfo.setTotalNetWeight(totalNetWeight);
            orderInfo.setApartStatus(apartStatus);
            if (BigDecimal.ZERO.compareTo(totalQty) == 0) {
                continue order;
            }
            if (CommonConstant.getApartSingle().contains(customerOrderInfo.getUpperOrderType())
                && BusinessMode.isB2B(customerOrderInfo.getBusinessMode())
            ) {
                orderInfo.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
            }

            orderInfo.setUpdateUserName(null);
            orderInfo.setUpdateUserCode(null);
            orderInfo.setCreateUserCode(null);
            orderInfo.setCreateUserName(null);
//           CIMS/手工订单子单货值：
//            (orderInfo.totalQty/customerOrderInfo.totalQty)*customerOrderInfo.orderValue
            ArrayList<String> sysList = Lists.newArrayList(SourceSystem.CIMS.getKey()
                , SourceSystem.HANDLE.getKey()
                , SourceSystem.KingDee_SD.getKey(), SourceSystem.OFC.getKey(), SourceSystem.MSS.getKey(), SourceSystem.GJPQCT_WBKH.getKey(), SourceSystem.JNC_CXP.getKey(), SourceSystem.BYT002_WBKH.getKey()
                , SourceSystem.Danone.getKey(), SourceSystem.YJC_WBKH.getKey());
            String sourceSystem = customerOrderInfo.getSourceSystem();
            //2024年4月12日09:02:35 黄泽宇 搞个字典配置 配置规则 来源系统_安得客户编码
            String dictValue = dictHelper.getDictVaule(ORDER_VALUE_ISSUE, String.format("%s_%s", sourceSystem, customerOrderInfo.getCustomerCode()));
            boolean issue = sysList.contains(sourceSystem) || StringUtils.isNotEmpty(dictValue);
            if (issue && ToolUtils.isNotEmpty(customerOrderInfo.getOrderValue())) {
                orderValue = ObjectUtils.defaultIfNull(customerOrderInfo.getOrderValue(), BigDecimal.ZERO)
                    .multiply(ObjectUtils.defaultIfNull(orderInfo.getTotalQty(), BigDecimal.ZERO)).divide(customerOrderInfo.getTotalQty(), 8, BigDecimal.ROUND_HALF_UP);
            }
            orderInfo.setOrderValue(orderValue);

            /**
             * 2025年4月11日10:21:55 黄泽宇 三养食品 采购入库单 因为是按照明细 groupIndication 分组拆单的
             * 将 groupIndication 的值设置到 头表的platformOrderNo 平台单号
             */
            if (SourceSystem.SAMYANG_WBKH.getKey().equals(sourceSystem) && OrderType.isPIOrder(customerOrderInfo.getOrderType())){
                orderInfo.setPlatformOrderNo(orderInfoItemList.get(0).getGroupIndication());
            }

            orderInfoList.add(orderInfo);

            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);
            if (compartFlag) {
                boolean isShare = JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType());
                //纯运输、直发订单不分仓
                if (Arrays.asList(OrderType.YS, OrderType.ZF).contains(EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType()))
                    //入库订单不占用库存
                    || (InOutType.IN.getName().equals(customerOrderInfo.getInOutType()) && !isShare)
                    || OrderType.DO.getKey().equals(customerOrderInfo.getOrderType())
                ) {
                    continue order;
                }
                //货权转移,校验是否同仓
                if (isShare) {
                    String customerCode = customerOrderInfo.getCustomerCode();
                    String targetCustomerCode = customerOrderInfo.getTargetCustomerCode();
                    if (ToolUtils.isEmpty(targetCustomerCode)) {
                        return OrderSplitDto.fail("目标客户为空，调剂单,货权转移校验失败！！");
                    }
                    List<String> sameWarehouseCode = validateSameWarehouseCode(customerCode, targetCustomerCode, Lists.newArrayList(orderInfoExt.getWhCode()));
                    if (org.springframework.util.CollectionUtils.isEmpty(sameWarehouseCode)) {
                        //LOGGER.warn("WMS同仓校验失败，orderNo:{}", orderNo);
                        return OrderSplitDto.fail("分仓校验，WMS同仓校验失败，请到WMS-【配置】-【出库配置】-【客户】维护协同关系[客户编码:"+ customerCode + ",目标客户编码:"+ targetCustomerCode +"]！！");
                    }
                    if (OrderType.RI.getKey().equals(customerOrderInfo.getOrderType())) {
                        orderInfoExt.setCustomerCode(customerOrderInfo.getTargetCustomerCode());
                    }
                }
                JsonResponse jsonResponse = null;
                //占用库存
                try {
                    long currentTimeMillis = System.currentTimeMillis();
                    jsonResponse = centerInvService.proHoldInv(orderInfoExt, CenterInvType.PLAN_OMS_OUT.getKey());
                    log.info("占用的-调用中央库存---{}-------------------耗时{}ms", jsonResponse, System.currentTimeMillis() - currentTimeMillis);
                    if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
                        if (CollectionUtils.isNotEmpty(orderInfoExtsBack)) {
                            orderInfoExtsBack.stream().forEach(
                                o -> {
                                    JsonResponse jsonResponse2 = centerInvService.cancelOrder(o, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
                                    log.info("取消已经成功的占用---------" + jsonResponse2);
                                }
                            );
                        }
                        JsonResponse jsonResponse2 = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
                        log.info("取消占用的---------" + jsonResponse2);
                        Object data = jsonResponse.getData();
                        return OrderSplitDto.fail(data == null ? "占用库存调用鹊桥失败" : "占用库存调用鹊桥失败：" + data.toString());
                    } else {
                        orderInfoExtsBack.add(orderInfoExt);
                    }

                } catch (Exception e) {
                    if (CollectionUtils.isNotEmpty(orderInfoExtsBack)) {
                        orderInfoExtsBack.stream().forEach(
                            o -> {
                                JsonResponse jsonResponse2 = centerInvService.cancelOrder(o, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
                                log.info("取消已经成功的占用---------" + jsonResponse2);
                            }
                        );
                    }
                    log.error(e.getMessage(), e);
                    JsonResponse jsonResponse2 = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
                    log.info("取消占用的---------" + jsonResponse2);
                    return OrderSplitDto.fail(jsonResponse == null ? "占用库存调用鹊桥失败" : "占用库存调用鹊桥失败：" + jsonResponse.data());
                }

            }
            orderInfoExts.add(orderInfoExt);
        }

        //批量新增
        JsonResponse jr2 = orderInfoItemFeign.batchCreateOrUpdate(orderInfoItemList);
        if (!"0".equals(jr2.getCode())) {
            orderInfoExts.stream().forEach(
                orderInfoExt ->//取消库存
                    centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey())
            );
            return OrderSplitDto.fail("子订单明细插入失败：" + jr2.getMsg());
        }
        JsonResponse jr1 = orderInfoFeign.batchCreateOrUpdate(orderInfoList);
        if (!"0".equals(jr1.getCode())) {
            orderInfoExts.stream().forEach(
                orderInfoExt ->//取消库存
                    centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey())
            );
            List<String> orderNos = orderInfoList.stream().map(o -> o.getOrderNo()).collect(Collectors.toList());
            JsonResponse<List<OrderInfoItem>> orderInfoItemResponse = orderInfoItemFeign.listOrderInfoItemByOrderNos(orderNos);
            if (orderInfoItemResponse.data() != null) {
                List<Long> orderItemIds = orderInfoItemResponse.data().stream().map(OrderInfoItem::getId).collect(Collectors.toList());
                orderInfoItemFeign.batchDeleteByBusinessKey(orderItemIds);
            }
            return OrderSplitDto.fail("子订单插入失败: " + jr1.getMsg());
        }

        customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfo(orderNo);
        if (isHandWork) {
            customerOrderInfo.setApartType(ApartType.HANDLE.getKey());//手工拆单
        }
        customerOrderInfo.setApartStatus(apartStatus);
        if (Lists.newArrayList(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT.getKey(), ExceptionType.INVENTORY_INSUFFICIENT.getKey()).contains(customerOrderInfo.getExceptionType())) {
            customerOrderInfo.setExceptionType("-");
            customerOrderInfo.setExceptionDesc("-");
        }

        //TODO 触发流程
        // 如果是手动操作分仓，需要触发子订单流程
        if (isHandWork && !CollectionUtils.isEmpty(orderInfoList)) {
            List<String> orderNos = Lists.newArrayList();
            //货权转移
//            if(ToolUtils.isNotEmpty(customerOrderInfo.getJoinType()) && !JoinType.WP.getKey().equals(customerOrderInfo.getJoinType())){
//                //生成任务
//                orderNos = orderInfoList.stream().map(o -> {
//                    OrderMq orderMq = new OrderMq();
//                    orderMq.setOrderNo(o.getOrderNo());
//                    orderMq.setNode(OrderStatus.AUDITED.getKey());
//                    orderAuditMessageProducer.sent(orderMq);
//                    return o.getOrderNo();
//                }).collect(Collectors.toList());
//            } else {
            orderNos = orderInfoList.stream().map(o -> o.getOrderNo()).collect(Collectors.toList());
//            }

            customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
            lmpOrderFlowHelper.updateCanSetEmptyCustomerOrderInfo(customerOrderInfo);

            if (orderNo.startsWith(IdGenHelper.PARENT_PREFIX_LMP)) {
                //触发中台流程
//                CustomerOrderInfoExt customerOrderInfoExt = new CustomerOrderInfoExt();
//                customerOrderInfoExt.setOrderNo(orderNo);
//                customerOrderVerifyEndService.customerOrderVerifyEnd(customerOrderInfoExt);
                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
            } else {
                // 这里只需要触发父流程。有下一流程处理子流程
                //workflowVerifyProducer.send(WorkflowConstant.CUSTOMER_ORDER, customerOrderInfo.getOrderNo());
                for (String workflowTicket : orderNos) {
                    workflowVerifyProducer.send(WorkflowConstant.ANNTO_ORDER, workflowTicket, customerOrderInfo.getOrderNo());
                }
            }
            return OrderSplitDto.successH(orderNos, orderInfoExts);
        } else {
            lmpOrderFlowHelper.updateCanSetEmptyCustomerOrderInfo(customerOrderInfo);
        }
        return OrderSplitDto.success(orderInfoExts);
    }

    @Override
    public boolean cancel(List<String> orderNos) {

        JsonResponse<List<OrderInfoItem>> orderInfoItemResponse = orderInfoItemFeign.listOrderInfoItemByOrderNos(orderNos);

        if (null == orderInfoItemResponse
            || CollectionUtils.isEmpty(orderInfoItemResponse.data)) {
            throw BusinessException.fail("未查询到安得订单商品信息");
        } else {
            JsonResponse<List<OrderInfo>> orderInfoResponse = orderFeign.listByOrderNos(orderNos);
            if (null == orderInfoResponse
                || CollectionUtils.isEmpty(orderInfoResponse.data)) {
                throw BusinessException.fail("未查询到安得订单信息");
            }

            //根据商品分组
            Map<String, List<OrderInfoItem>> orderItemMap = orderInfoItemResponse.data
                .stream().collect(Collectors.groupingBy(OrderInfoItem::getOrderNo));
            List<Long> orderIds = orderInfoResponse.data.stream().map(OrderInfo::getId).collect(Collectors.toList());
            List<Long> orderItemIds = orderInfoItemResponse.data.stream().map(OrderInfoItem::getId).collect(Collectors.toList());
            for (OrderInfo orderInfo : orderInfoResponse.data) {
                boolean isShare = OrderType.RI.getKey().equals(orderInfo.getOrderType()) && JoinType.SHARE.getKey().equals(orderInfo.getJoinType());
                //取消库存占用对象
                OrderInfoExt orderInfoExt = new OrderInfoExt();
                List<OrderInfoItem> orderInfoItems = orderItemMap.get(orderInfo.getOrderNo());
                BeanUtils.copyProperties(orderInfo, orderInfoExt);
                if (isShare && SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
                    orderInfoExt.setCustomerCode(orderInfoExt.getTargetCustomerCode());
                }
                orderInfoExt.setOrderInfoItems(orderInfoItems);
                //取消库存
                centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
                if (ExcuteStatus.FAILD.getKey().equals(orderInfo.getExcuteStatus())) {
                    orderInfo.setExcuteStatus(ExcuteStatus.CANCEL.getKey());
                    //取消质押
                    pledgeCancelProducer.sent(orderInfo.getOrderNo());
                    orderInfoFeign.update(orderInfo.getId(), orderInfo);
                }
            }
            //批量删除
            orderInfoFeign.batchDeleteByBusinessKey(orderIds);
            orderInfoItemFeign.batchDeleteByBusinessKey(orderItemIds);
        }
        //查询客户订单
        CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
        customerOrderInfo.setOrderNo(orderInfoItemResponse.data.get(0).getParentOrderNo());
        JsonResponse<CustomerOrderInfo> search = customerOrderInfoFeign.searchOne(customerOrderInfo);
        if (null == search || null == search.data()) {
            throw BusinessException.fail(search == null ? "no respone from OustomerOrderInfoFeign - searchOne" : "查询客户订单失败");
        }
        customerOrderInfo = search.data();
        customerOrderInfo.setApartStatus(CommonConstant.PART);//部分拆单
        customerOrderInfoFeign.update(customerOrderInfo.getId(), customerOrderInfo);
        return true;
    }

    /*
        @Override
        @ZeebeFlow("APART")
        public CustomerOrderInfoExt automatic(CustomerOrderInfoExt customerOrderInfoExt) {
            customerOrderInfoExt.setIsHandle(false);
            FlowListenerParam flowListenerParam = new FlowListenerParam();
            flowListenerParam.setFlowStatus(FlowStatus.SUCCESS);

            //查询客户订单
            if (ApartType.HANDLE.getKey().equals(customerOrderInfoExt.getApartType())){
                customerOrderInfoExt.setIsHandle(true);
                FlowListenerParam.success("手工拆单");
                return customerOrderInfoExt;
                //return FlowListenerParam.hold("手工拆单");
            }
            //B2C的分拨单（orderSource=ELS,orderType=DO,orderType=RDO），分仓拆单时，不用查规则，直接1:1生成子单，且子单号取parentOrderNo
            //模糊订单也直接生成
            if ((BusinessMode.B2C.getName().equals(customerOrderInfoExt.getBusinessMode())
                && OrderSource.ELS.getKey().equals(customerOrderInfoExt.getOrderSource())
                && (OrderType.DO.getKey().equals(customerOrderInfoExt.getOrderType())|| OrderType.RDO.getKey().equals(customerOrderInfoExt.getOrderType())))
            || CommonConstant.FLAG_YES.equals(customerOrderInfoExt.getPlanOrderFlag())
            ){
                String subOrderNo = customerOrderInfoExt.getOrderNo();
                if (CommonConstant.FLAG_YES.equals(customerOrderInfoExt.getPlanOrderFlag())){
                    if (customerOrderInfoExt.getOrderNo().startsWith(IdGenHelper.PARENT_PREFIX_LMP)){
                        subOrderNo = idGenHelper.genAnntoOrderNo(IdGenHelper.ANNTO_PREFIX_LMP);
                    }else{
                        subOrderNo = idGenHelper.genAnntoOrderNo(IdGenHelper.ANNTO_PREFIX_OTP);
                    }
                }
                customerOrderInfoExt.setApartType(ApartType.NOT_APART.getKey());
                customerOrderInfoExt.setApartStatus(CommonConstant.APARTED);
                // 子单
                OrderInfo orderInfo = new OrderInfo();
                BeanUtils.copyProperties(customerOrderInfoExt, orderInfo
                    , "id", "orderNo");
                orderInfo.setTenantCode(customerOrderInfoExt.getTenantCode());
                orderInfo.setOrderNo(subOrderNo);
                CustomerOrderAddress address = customerOrderInfoExt.getCustomerOrderAddress();//lmpOrderFlowHelper.getCustomerOrderAddress(orderNo);
                if (address != null){
                    BeanUtils.copyProperties(address, orderInfo
                        , "id", "orderNo", "remark","planOrderFlag","tenantCode");
                }
                orderInfo.setUpperAgingCode(customerOrderInfoExt.getAgingProductCode());
                orderInfo.setParentOrderNo(customerOrderInfoExt.getOrderNo());
                //查询客户订单商品信息
                List<CustomerOrderItem> customerOrderItems = customerOrderInfoExt.getCustomerOrderItems(); //lmpOrderFlowHelper.listCustomerOrderItem(orderNo);
                if (!CommonConstant.FLAG_YES.equals(customerOrderInfoExt.getPlanOrderFlag())
                    && (null == customerOrderItems || CollectionUtils.isEmpty(customerOrderItems))) {
                    throw BusinessException.fail("没有查询到商品信息");
                }
                if (ToolUtils.isNotEmpty(customerOrderItems)){
                    List<OrderInfoItem> items = customerOrderItems.parallelStream().map(
                        customerOrderItem ->{
                            OrderInfoItem item = new OrderInfoItem();
                            //复制客户订单商品信息到新生成的订单商品
                            BeanUtils.copyProperties(customerOrderItem, item
                                , "id", "orderNo");
                            item.setOrderNo(orderInfo.getOrderNo());
                            item.setParentOrderNo(customerOrderItem.getOrderNo());
                            return item;
                        }
                    ).collect(Collectors.toList());
                    JsonResponse jr2 = orderInfoItemFeign.batchCreateOrUpdate(items);
                    if (!"0".equals(jr2.getCode())){
                        throw BusinessException.fail("插入子订单明细失败");
                    }
                }
                //批量新增
                JsonResponse jr1 = orderInfoFeign.batchCreateOrUpdate(Lists.newArrayList(orderInfo));
                if (!"0".equals(jr1.getCode())){
                    throw BusinessException.fail("插入子订单失败");
                }
                //重新 更新父单的状态
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                List<String> orderNos = Lists.newArrayList(orderInfo).stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
                //return FlowListenerParam.success("生成子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                FlowListenerParam.success("生成子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));

                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
                return customerOrderInfoExt;
            }
            // 货权转移订单处理
            if(JoinType.isJoinType(customerOrderInfoExt.getJoinType())){
                // 要找到子单，继续触发
                OrderInfo orderInfo = new OrderInfo();
                orderInfo.setParentOrderNo(customerOrderInfoExt.getOrderNo());
                JsonResponse<List<OrderInfo>> list = orderInfoFeign.list(orderInfo);
                if (!"0".equals(list.getCode())){
                    throw BusinessException.fail("货权转移，查询子订单失败");
                }
                if (CollectionUtils.isEmpty(list.data)){
                    throw BusinessException.fail("货权转移，无子订单");
                }
                List<String> orderNos = list.data.stream().map(l -> l.getOrderNo()).collect(Collectors.toList());

                //return FlowListenerParam.success("货权转移订单，子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                FlowListenerParam.success("货权转移订单，子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));

                return customerOrderInfoExt;
            }


            if (ToolUtils.isNotEmpty(customerOrderInfoExt.getApartStatus()) && CommonConstant.APARTED.equals(customerOrderInfoExt.getApartStatus())){
                //return FlowListenerParam.success("");
                FlowListenerParam.success("");

                return customerOrderInfoExt;
            }
            //是否出库任务
            boolean empoyFlag = InOutType.OUT == InOutType.getEnum(customerOrderInfoExt.getInOutType()) || InOutType.ADJUST == InOutType.getEnum(customerOrderInfoExt.getInOutType());

            CustomerConfig customerConfig = getCustomerConfig(customerOrderInfoExt);
            //没有客户配置，
            if (customerConfig == null){
                customerConfig = new CustomerConfig();
                customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_NO);
                //修改为不拆单
                customerConfig.setApartType(ApartType.NOT_APART.getKey());
            }
            if (ApartType.UNALLOWED.getKey().equals(customerConfig.getApartType())){
                customerOrderInfoExt.setApartType(ApartType.UNALLOWED.getKey());
            }
            //手工分仓
            if (ApartType.HANDLE.getKey().equals(customerConfig.getApartType())){
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                //return FlowListenerParam.hold("手工拆单");
                FlowListenerParam.success("手工拆单");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }
            List<OrderInfoExt> succussOrderInfos = Lists.newArrayList();
            List<String> errorInfos = Lists.newArrayList();

            // 有仓库存在
            if (StringUtils.isNotBlank(customerOrderInfoExt.getWhCode())){
                //拆单，生成拆单对象
                List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfoExt
                    , customerConfig.getApartType());
                if (ToolUtils.isNotEmpty(orderList)) {
                    String whCode = customerOrderInfoExt.getWhCode();
                    //获取仓库，执行分仓
                    for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                        list.forEach(
                            req -> req.setWhCode(whCode)
                        );
                        //执行拆单
                        OrderSplitDto confirm = confirm(customerOrderInfoExt.getOrderNo(), empoyFlag, list, false);
                        if (CollectionUtils.isNotEmpty(confirm.getOrderInfoExts())){
                            succussOrderInfos.addAll(confirm.getOrderInfoExts());
                        } else {
                            errorInfos.add(whCode + ":" + confirm.getErrMsg());
                        }

                    }
                }

                if (!CollectionUtils.isEmpty(errorInfos)){
                    if (empoyFlag && ToolUtils.isNotEmpty(succussOrderInfos)){
                        List<String> orderNos = succussOrderInfos.stream().map(c -> c.getOrderNo()).collect(Collectors.toList());
                        cancel(orderNos);
                    }
                    // 返回错误【处理掉重复错误】
                    errorInfos = errorInfos.stream().distinct().collect(Collectors.toList());
                    //return FlowListenerParam.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT, JSONObject.toJSONString(errorInfos));
                    throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT.getValue()+ JSONObject.toJSONString(errorInfos));
                }
                List<String> orderNos = succussOrderInfos.stream().map(s -> s.getOrderNo()).collect(Collectors.toList());

                //return FlowListenerParam.success("子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                FlowListenerParam.success("子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
                return customerOrderInfoExt;
            }

            // 没有仓库存在

            //运输单，1：1生成子单
            if (OrderType.YS == EnumUtils.getEnum(OrderType.class, customerOrderInfoExt.getOrderType())) {
                List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfoExt
                    , customerConfig.getApartType());

                for (List<SeparateWarehouseConfirmRequest> list : orderList) {

                    list.forEach(
                        req -> req.setWhCode(CommonConstant.WHCODE_FLAG)
                    );
                    //执行拆单，分仓
                    OrderSplitDto confirm = confirm(customerOrderInfoExt.getOrderNo(), empoyFlag && CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()), list, false);
                    if (CollectionUtils.isNotEmpty(confirm.getOrderInfoExts())){
                        succussOrderInfos.addAll(confirm.getOrderInfoExts());
                    } else {
                        errorInfos.add(confirm.getErrMsg());
                    }
                }

                if (!CollectionUtils.isEmpty(errorInfos)){
                    if (empoyFlag && ToolUtils.isNotEmpty(succussOrderInfos)){
                        List<String> orderNos = succussOrderInfos.stream().map(c -> c.getOrderNo()).collect(Collectors.toList());
                        cancel(orderNos);
                    }
                    String errStr = JSONObject.toJSONString(errorInfos);
                    if (errStr != null && errStr.contains("获取库存等待锁超时")){
                        //return FlowListenerParam.timeout(errStr);
                        throw BusinessException.fail(errStr);
                    }

                    // 返回错误
                    throw BusinessException.fail(ExceptionType.INVENTORY_INSUFFICIENT.getValue() + errStr);
                }
                List<String> orderNos = succussOrderInfos.stream().map(s -> s.getOrderNo()).collect(Collectors.toList());

                //return FlowListenerParam.success("子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                FlowListenerParam.success("子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
                return customerOrderInfoExt;
            }
            //判断是否自动分仓

            //订单接单时，先判断订单的仓库字段是否为空，若为空，去调用配置中心-》》客户配置，此订单是否自动分仓
            if (CommonConstant.STRING_FLAG_NO.equals(customerConfig.getCompartFlag())) {
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                flowListenerParam.setFlowStatus(FlowStatus.HOLD);
                flowListenerParam.setErrorMsg("是否分仓配置为否，需要手动分仓");
                //return flowListenerParam;
                FlowListenerParam.success("是否分仓配置为否，需要手动分仓");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }

            //拆单，生成拆单对象
            List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfoExt
                , customerConfig.getApartType());
            if (ToolUtils.isNotEmpty(orderList)) {
                //自动分仓，查询分仓规则
                CompartRule compartRule = getCompartRule(customerOrderInfoExt);
                if(null == compartRule){
                    customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                    customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
    //                return FlowListenerParam.hold("未查到分仓规则,无法自动分仓");
                    FlowListenerParam.success("未查到分仓规则,无法自动分仓");
                    customerOrderInfoExt.setIsHandle(true);
                    return customerOrderInfoExt;
                }
                String whCode = compartRule.getWhCode();
                if(ToolUtils.isEmpty(whCode)){
                    customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                    customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                    lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                    //return FlowListenerParam.hold("分仓规则未配置仓库，无法分仓");
                    FlowListenerParam.success("分仓规则未配置仓库，无法分仓");
                    customerOrderInfoExt.setIsHandle(true);
                    return customerOrderInfoExt;
                }
                //获取仓库，执行分仓
                for (List<SeparateWarehouseConfirmRequest> list : orderList) {

                    list.forEach(
                        req -> req.setWhCode(whCode)
                    );
                    //执行拆单，分仓
                    OrderSplitDto confirm = confirm(customerOrderInfoExt.getOrderNo(), empoyFlag && CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()), list, false);
                    if (CollectionUtils.isNotEmpty(confirm.getOrderInfoExts())){
                        succussOrderInfos.addAll(confirm.getOrderInfoExts());
                    } else {
                        errorInfos.add(confirm.getErrMsg());
                    }
                }


                if (!CollectionUtils.isEmpty(errorInfos)){
                    if (empoyFlag && ToolUtils.isNotEmpty(succussOrderInfos)){
                        List<String> orderNos = succussOrderInfos.stream().map(c -> c.getOrderNo()).collect(Collectors.toList());
                        cancel(orderNos);
                    }

                    String errStr = JSONObject.toJSONString(errorInfos);
                    if (errStr != null && errStr.contains("获取库存等待锁超时")){
                        //return FlowListenerParam.timeout(errStr);
                        throw BusinessException.fail(errStr);
                    }
                    // 返回错误
                    //return FlowListenerParam.fail(ExceptionType.INVENTORY_INSUFFICIENT, errStr);
                    throw BusinessException.fail(ExceptionType.INVENTORY_INSUFFICIENT.getValue() + errStr);
                }
                List<String> orderNos = succussOrderInfos.stream().map(s -> s.getOrderNo()).collect(Collectors.toList());

                //return FlowListenerParam.success("子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                FlowListenerParam.success("子订单 "+orderNos.size()+" 单:"+ JSONObject.toJSONString(orderNos));
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
                return customerOrderInfoExt;
            }

            //return FlowListenerParam.fail("未定义的分仓规则");
            throw BusinessException.fail("未定义的分仓规则");
        }

    */

    @Autowired
    private DictHelper dictHelper;

    @Autowired
    private SCCOrderStockCheckService sccOrderStockCheckService;
    @Autowired
    private OrderAggFeign orderAggFeign;

    /**
    * !@分仓 - 自动分仓（中台）
    * @param: [customerOrderInfoExt]
    * @return: com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt
    * @author: 陈永培
    * @createtime: 2023/5/22 19:55
    */
    @Override
    @ZeebeFlow("APART")
    public CustomerOrderInfoExt automatic(CustomerOrderInfoExt customerOrderInfoExt) {

        // 检查 lock
        String key = "SEPARATEWAREHOUSE:AUTOMATIC" + customerOrderInfoExt.getOrderNo();
        boolean lock = redisLockHelper.tryLock(key, 30L);
        if (!lock) {
            throw BusinessException.fail("分仓过于频繁，请稍后再试！");
        }

        //!@分仓 - 自动分仓（中台） - 1、订单挂起
        CustomerOrderInfoExtend customerOrderInfoExtend = businessParamHelper.getCustomerOrderInfoExtend(customerOrderInfoExt.getOrderNo());
        customerOrderInfoExt.setCustomerOrderInfoExtend(customerOrderInfoExtend);
        //订单挂起
        if (customerOrderInfoExtend != null && CommonEnum.Y.getKey().equals(customerOrderInfoExtend.getIsPending())) {
            FlowListenerParam.hold("订单已挂起，请取消挂起后再分仓!");
            return customerOrderInfoExt;
        }
        //人工审单
        CustomerConfig customerConfig = getCustomerConfig(customerOrderInfoExt);
        if(ToolUtils.isNotEmpty(customerConfig)) {
            if (CommonConstant.FLAG_YES.equals(customerConfig.getHandCheckFlag()) && StringUtils.isEmpty(redisHelper.getHoldStatus(customerOrderInfoExt.getOrderNo()))){
                redisHelper.setHoldStatus(customerOrderInfoExt.getOrderNo());
                throw BusinessException.fail("客户配置[人工审单]，请由用户手工进行审核");
            }
        }
        //!@分仓 - 自动分仓（中台） - 2、检查一下是否正在部分取消
        partCancelSplitRedisLockHelper.checkPartCancelLockAndThrow(customerOrderInfoExt.getOrderNo());
        FlowListenerParam flowListenerParam = new FlowListenerParam();
        flowListenerParam.setFlowStatus(FlowStatus.SUCCESS);

        //2021-6-15 17:48:53 黄进广：由于存在并发的情况，接单马上部分取消，导致自动拆单里面的流程变量item还是原来的，因此需要加上所，让所有服务器拆单的时候，有这个标识的就读取数据库
        orderFlowHelper.reloadCutomerOrderItem2(customerOrderInfoExt);

        //有坑，货权转移失败打上手工标识，然后不手工分而是按审核就卡在下面手工判断，所以先判断拆单状态吧
        if (ToolUtils.isNotEmpty(customerOrderInfoExt.getApartStatus()) && CommonConstant.APARTED.equals(customerOrderInfoExt.getApartStatus())) {
            subOrderFlow(customerOrderInfoExt);
            FlowListenerParam.success("");
            return customerOrderInfoExt;
        }

        if (ApartType.HANDLE.getKey().equals(customerOrderInfoExt.getApartType())) {
            customerOrderInfoExt.setIsHandle(true);
            FlowListenerParam.success("手工拆单");
            return customerOrderInfoExt;
        }

        //2024年8月8日23:04:52 浩明：https://cf.annto.com/pages/resumedraft.action?draftId=46466128&draftShareId=11a5e11b-b739-4252-b70c-1d7a52b1395f&，往订单描述写备注
        customerOrderInfoExt.setExceptionDesc("402：订单"+customerOrderInfoExt.getCustomerOrderNo()+"，待分仓 "+orderAggFeign.getVerifyFailPersonByCustOrder(customerOrderInfoExt).data);


        //视源 PO/YS订单 超大方量手工拆单
        if (Lists.newArrayList(OrderType.PO.getKey(), OrderType.YS.getKey()).contains(customerOrderInfoExt.getOrderType()) && SourceSystem.CVTE.getKey().equals(customerOrderInfoExt.getSourceSystem())) {
            String volumeValue = redisHelper.getAutomicVolume();
            if (StringUtils.isBlank(volumeValue)) {
                String volumeKey = "VOLUME_CVTE_AUTOMATIC";
                HashMap map = getControlParamByKey(volumeKey);
                volumeValue = null == map ? "0" : (String) map.get("volume");
                redisHelper.setAutomicVolume(volumeValue);
            }

            BigDecimal totalVolume = customerOrderInfoExt.getTotalVolume();
            BigDecimal volume = StringUtils.isBlank(volumeValue) ? BigDecimal.ZERO : new BigDecimal(volumeValue);
            if (!volume.equals(BigDecimal.ZERO) && null != totalVolume && totalVolume.compareTo(volume) >= 0) {
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                FlowListenerParam.success("手工拆单");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }
        }


        //订单区分标识为前分仓（order_distinction_flag =BO-FW）的订单，
        // 来源系统为手工单（source_system=HANDLE），
        // 订单类型为状态调整单（order_type = TF），手工建单保存审核时，不做自动分仓，需人工手工分仓
        boolean isBOFW = false;
        if (customerOrderInfoExtend != null ){
            String orderDistinctionFlag = customerOrderInfoExtend.getOrderDistinctionFlag();
            if(OrderDistinctionFlag.isFWh(orderDistinctionFlag)){
                isBOFW = true;
            }
            log.info("========orderNo={}======orderDistinctionFlag={}=",customerOrderInfoExtend.getOrderNo(),orderDistinctionFlag);
            //!@分仓 - 自动分仓（中台） - 3、基地前分仓 & TF单 需人工手工分仓
            if (OrderDistinctionFlag.BO_FW.getKey().equals(orderDistinctionFlag) && OrderType.isTFOrder(customerOrderInfoExt.getOrderType())
                && SourceSystem.HANDLE.getKey().equals(customerOrderInfoExt.getSourceSystem())){
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                FlowListenerParam.success("手工拆单");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }

            //!@分仓 - 自动分仓（中台） - 4、glh:父单有仓时，基地标识：BO-GFW（）、BO-GRW（后分仓） + 订单类型：销售出库、调拨出库、出库直发 + 业务控制参数GWMS_NOT_CHECK_INVENTORY 有配置服务平台+客户时，GWMS订单分仓不查库存
            if (StringUtils.isNotBlank(customerOrderInfoExt.getWhCode()) && businessHelper.isGwmsNotCheckInv(customerOrderInfoExt, customerOrderInfoExtend)) {
                return this.generateOrdersNotCheckInv(customerOrderInfoExt, customerOrderInfoExtend, null);
            }
        }


        String orderNo = customerOrderInfoExt.getOrderNo();
        //!@分仓 - 自动分仓（中台） - 5、B2C的分拨单（orderSource=ELS,orderType=DO,orderType=RDO），分仓拆单时，不用查规则，直接1:1生成子单，且子单号取parentOrderNo
        //模糊订单也直接生成
        if ((BusinessMode.B2C.getName().equals(customerOrderInfoExt.getBusinessMode())
            && OrderSource.ELS.getKey().equals(customerOrderInfoExt.getOrderSource())
            && (OrderType.DO.getKey().equals(customerOrderInfoExt.getOrderType()) || OrderType.RDO.getKey().equals(customerOrderInfoExt.getOrderType())))
            || CommonConstant.FLAG_YES.equals(customerOrderInfoExt.getPlanOrderFlag())
        ) {
            String subOrderNo = customerOrderInfoExt.getOrderNo();
            if (CommonConstant.FLAG_YES.equals(customerOrderInfoExt.getPlanOrderFlag())) {
                 subOrderNo = flowRuleHelper.getAnOrderNo(customerOrderInfoExt);
            }
            customerOrderInfoExt.setApartType(ApartType.NOT_APART.getKey());
            customerOrderInfoExt.setApartStatus(CommonConstant.APARTED);
            customerOrderInfoExt.setExceptionDesc("-");
            // 子单
            OrderInfo orderInfo = new OrderInfo();
            BeanUtils.copyProperties(customerOrderInfoExt, orderInfo
                , "id", "orderNo");
            orderInfo.setOrderNo(subOrderNo);
            orderInfo.setTenantCode(customerOrderInfoExt.getTenantCode());
            CustomerOrderAddress address = customerOrderInfoExt.getCustomerOrderAddress();
            if (address != null) {
                BeanUtils.copyProperties(address, orderInfo
                    , "id", "orderNo", "remark", "planOrderFlag", "tenantCode");
            }
            orderInfo.setUpperAgingCode(customerOrderInfoExt.getAgingProductCode());
            orderInfo.setParentOrderNo(customerOrderInfoExt.getOrderNo());
            //查询客户订单商品信息
            List<CustomerOrderItem> customerOrderItems = customerOrderInfoExt.getCustomerOrderItems();
            if (!CommonConstant.FLAG_YES.equals(customerOrderInfoExt.getPlanOrderFlag())
                && (null == customerOrderItems || CollectionUtils.isEmpty(customerOrderItems))) {
                throw BusinessException.fail("没有查询到商品信息");
            }
            if (ToolUtils.isNotEmpty(customerOrderItems)) {
                List<OrderInfoItem> items = customerOrderItems.parallelStream().map(
                    customerOrderItem -> {
                        OrderInfoItem item = new OrderInfoItem();
                        //复制客户订单商品信息到新生成的订单商品
                        BeanUtils.copyProperties(customerOrderItem, item
                            , "id", "orderNo");
                        item.setOrderNo(orderInfo.getOrderNo());
                        item.setParentOrderNo(customerOrderItem.getOrderNo());
                        return item;
                    }
                ).collect(Collectors.toList());
                JsonResponse jr2 = orderInfoItemFeign.batchCreateOrUpdate(items);
                if (!"0".equals(jr2.getCode())) {
                    throw BusinessException.fail("插入子订单明细失败");
                }
            }

            //批量新增
            JsonResponse jr1 = orderInfoFeign.batchCreateOrUpdate(Lists.newArrayList(orderInfo));
            if (!"0".equals(jr1.getCode())) {
                throw BusinessException.fail("插入子订单失败");
            }
            //重新 更新父单的状态
            customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");

            List<String> orderNos = Lists.newArrayList(orderInfo).stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
            if (orderNo.startsWith(IdGenHelper.PARENT_PREFIX_LMP)) {
                //触发中台流程
                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
            } else {
                workflowVerifyProducer.send(WorkflowConstant.CUSTOMER_ORDER, customerOrderInfoExt.getOrderNo());
                for (String workflowTicket : orderNos) {
                    workflowVerifyProducer.send(WorkflowConstant.ANNTO_ORDER, workflowTicket, customerOrderInfoExt.getOrderNo());
                }
            }
            FlowListenerParam.success("生成子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
            return customerOrderInfoExt;
        }
        // !@分仓 - 自动分仓（中台） - 6、货权转移订单处理
        if (JoinType.isJoinType(customerOrderInfoExt.getJoinType())) {
            List<String> orderNos = subOrderFlow(customerOrderInfoExt);
            FlowListenerParam.success("货权转移订单，子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
            return customerOrderInfoExt;
        }

        //!@分仓 - 自动分仓（中台） - 6、查询是否查单
        String apartStatus = checkAparted(customerOrderInfoExt.getOrderNo());
        if (CommonConstant.APARTED.equals(apartStatus)) {
            customerOrderInfoExt.setExceptionDesc("-");
            customerOrderInfoExt.setApartStatus(CommonConstant.APARTED);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
            subOrderFlow(customerOrderInfoExt);
            FlowListenerParam.success("");
            return customerOrderInfoExt;
        }
        //是否出库任务
        boolean empoyFlag = InOutType.OUT == InOutType.getEnum(customerOrderInfoExt.getInOutType()) || InOutType.ADJUST == InOutType.getEnum(customerOrderInfoExt.getInOutType());
        boolean isCZ = ProjectClassifyEnum.isCZ(customerOrderInfoExt.getProjectClassify());
        
        //!@分仓 - 自动分仓（中台） - 7、查询客户配置
        //2025年6月11日17:23:50 因为要判断是否人工审单 所以查询配置迁移
//        CustomerConfig customerConfig = getCustomerConfig(customerOrderInfoExt);
        log.info("订单：{}，客户配置：{}",customerOrderInfoExt.getOrderNo(),customerConfig);
        //没有客户配置，
        if (customerConfig == null) {
            customerConfig = new CustomerConfig();
            customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_NO);
            //修改为不拆单
            customerConfig.setApartType(ApartType.NOT_APART.getKey());
        }

        if (ApartType.UNALLOWED.getKey().equals(customerConfig.getApartType())) {
            customerOrderInfoExt.setApartType(ApartType.UNALLOWED.getKey());
        }
        Integer deliverypayType = ObjectUtils.defaultIfNull(customerOrderInfoExt.getDeliverypayType(),0);
        if (2==deliverypayType){
            customerConfig.setApartType(ApartType.NOT_APART.getKey());
        }
        
        //!@分仓 - 自动分仓（中台） - 8、gsc:上游履约中心B2C订单拆分单品单件无匹配平台时，增加一个判断条件【商品数量超过20】则 不需要拆分单品单件
        if (SourceSystem.OFC.getKey().equals(customerOrderInfoExt.getSourceSystem()) && BusinessMode.isB2C(customerOrderInfoExt.getBusinessMode())
            && null != customerOrderInfoExt.getTotalQty() && CommonEnum.Y.getValue().equals(customerConfig.getDefaultRuleFlag())) {
            ControlParam cache = controlParamManager.getCache(CommonConstant.OFC_SPLITORDER_JUDGEQTY);
            if (null != cache && StringUtils.isNotBlank(cache.getValue()) && new BigDecimal(cache.getValue()).compareTo(customerOrderInfoExt.getTotalQty()) < 0) {
                customerConfig.setApartType(ApartType.NOT_APART.getKey());
                customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_YES);
            }
        }
        
        //!@分仓 - 自动分仓（中台） - 9、仓直订单默认自动分仓
        if(isCZ){
            customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_YES);
        }
        //!@分仓 - 自动分仓（中台） - 10、C2M订单按单品单件分仓拆单规则进行
        if (C2MType.isC2M(customerOrderInfoExt.getC2mType())) {
            customerConfig.setApartType(ApartType.SINGLE.getKey());
            customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_YES);
        }
        //!@分仓 - 自动分仓（中台） - 11、云仓破损换货 自动拆单下发
        if (ProjectClassifyEnum.isCloudWhDamage(customerOrderInfoExt.getProjectClassify())) {
                customerConfig.setApartType(ApartType.NOT_APART.getKey());
                customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_YES);
        }
        //!@分仓 - 自动分仓（中台） - 12、O2O门店仓发快递 自动整单下发
        if (SourceSystem.isMRP(customerOrderInfoExt.getSourceSystem()) && ProjectClassifyEnum.isO2oExpress(customerOrderInfoExt.getProjectClassify())) {
            customerConfig.setApartType(ApartType.NOT_APART.getKey());
            customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_YES);
        }
        //手工分仓
        if (ApartType.HANDLE.getKey().equals(customerConfig.getApartType())) {
            customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
            customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
            FlowListenerParam.success("手工拆单");
            customerOrderInfoExt.setIsHandle(true);
            return customerOrderInfoExt;
        }

        // 传送带模式的核销规则。不用分仓
        if (SourceSystem.SCC.getKey().equals(customerOrderInfoExt.getSourceSystem())
                && UpperOrderType.getWriteOffRulesType().contains(customerOrderInfoExt.getUpperOrderType())
                && WhCodeEnum.W00524.getKey().equals(customerOrderInfoExt.getWhCode())
                && StringUtils.isNotBlank(customerOrderInfoExt.getOriginOrderNo())) {
            if (hasWmsTiCustomerOrderInfo(customerOrderInfoExt)) {
                // 订单更新为：全部入库和关单
                customerOrderInfoExt.setOrderStatus(OrderStatus.INBOUND.getKey());
                customerOrderInfoExt.setExcuteStatus(ExcuteStatus.CLOSED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "下线入库 分仓拆单");
                FlowListenerParam.success("不用分仓");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }
        }
        // 如果是 WMS 的 下线入库,无需分仓， 需生成子单订单、任务 ，订单更新为：全部入库和关单
        if (SourceSystem.WMS.getKey().equals(customerOrderInfoExt.getSourceSystem()) && OrderType.TI.getKey().equals(customerOrderInfoExt.getUpperOrderType())) {
            empoyFlag = true;
        }

        // 如果是 AWMS 的 数量调整入库、数量调整出库,无需分仓， 需生成子单订单、任务 ，订单更新为：全部入库和关单
        if (SourceSystem.AWMS.getKey().equals(customerOrderInfoExt.getSourceSystem()) && Arrays.asList(OrderType.ADI.getKey(), OrderType.ADO.getKey()).contains(customerOrderInfoExt.getUpperOrderType())) {
            empoyFlag = false;
        }

        //glh:退货入库单 & 拦截分拨出库任务 & 分拨出库任务状态< 分拨出库 时,若中转出库的平台（拦截的分拨出库任务的平台）和入库单的平台一致，子单分仓的时候，子单的仓按中转出的仓保存
        if (OrderType.isRIOrder(customerOrderInfoExt.getOrderType()) && StringUtils.isNotBlank(customerOrderInfoExt.getOriginOrderNo())) {
            Task interceptTask= new Task();
            interceptTask.setExcuteStatus(ExcuteStatus.INTERCEPT.getKey());
            interceptTask.setWaybillNo(customerOrderInfoExt.getOriginOrderNo());
            List<Task> interceptTasks = businessParamHelper.getLastTaskByIndex(interceptTask, OrderType.RI.getKey());
            if (CollectionUtils.isEmpty(interceptTasks)) {
                interceptTask.setWaybillNo(null);
                interceptTask.setCustomerOrderNo(customerOrderInfoExt.getOriginOrderNo());
                if (SourceSystem.QIMEN.getKey().equals(customerOrderInfoExt.getSourceSystem())) {
                    interceptTask.setCustomerCode(customerOrderInfoExt.getCustomerCode());
                    interceptTask.setSourceSystem(customerOrderInfoExt.getSourceSystem());
                }
                interceptTasks = businessParamHelper.getLastTaskByIndex(interceptTask, OrderType.RI.getKey());
            }
            if (CollectionUtils.isNotEmpty(interceptTasks)) {
                //获取拦截的最新分拨出库任务
                interceptTask = interceptTasks.stream().max(Comparator.comparing(Task::getId)).get();
                if (TaskType.isDO(interceptTask.getTaskType()) && interceptTask.getOrderStatus() != null && interceptTask.getOrderStatus().compareTo(OrderStatus.DISTRIBUTION_OUT.getKey()) < 0
                    && Objects.equals(interceptTask.getSiteCode(), customerOrderInfoExt.getSiteCode())) {
                    customerOrderInfoExt.setWhCode(interceptTask.getWhCode());
                    customerOrderInfoExt.setWhName(interceptTask.getWhName());
                }
            }
        }

        CreateOrderContext cot = new CreateOrderContext();
        cot.setCustomerOrderInfo(customerOrderInfoExt);
        cot.setOrderAddress(customerOrderInfoExt.getCustomerOrderAddress());
        cot.setOrderInfoExtend(customerOrderInfoExtend);
        List<CustomerOrderItem> customerOrderItems = Lists.newArrayList();
        customerOrderItems.addAll(customerOrderInfoExt.getCustomerOrderItems());
        cot.setCustomerOrderItemList(customerOrderItems);
        //仓群 --- 自动分仓
        if(customerOrderInfoExtend != null && StringUtils.isNotBlank(customerOrderInfoExtend.getWhClusterCode())){
            //订单接单时，先判断订单的仓库字段是否为空，若为空，去调用配置中心-》》客户配置，此订单是否自动分仓
            if (CommonConstant.STRING_FLAG_NO.equals(customerConfig.getCompartFlag())) {
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                FlowListenerParam.success("是否分仓配置为否，需要手动分仓");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }
            try{
                List<WhetherApartedResponse> whetherApartedResponses = customerOrderInfoFeign.batchWhetherAparted(Lists.newArrayList(customerOrderInfoExt.getOrderNo())).data();
                if(whetherApartedResponses == null || org.springframework.util.CollectionUtils.isEmpty(whetherApartedResponses)){
                    FlowListenerParam.hold(ExceptionType.INVENTORY_INSUFFICIENT, "查询订单未分配数量失败");
                    throw BusinessException.fail(ExceptionType.INVENTORY_INSUFFICIENT.getValue() + "查询订单未分配数量失败");
                }
                JsonResponse<JsonNode> jsonResponse =  centerInvService.queryInvBatchWhCluster(whetherApartedResponses,CenterInvType.PLAN_OMS_OUT.getKey(),customerOrderInfoExt,customerOrderInfoExtend,cot.getOrderAddress());
                if(!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())){
                    FlowListenerParam.hold(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2, jsonResponse.getMsg());
                    throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2.getValue() + jsonResponse.getMsg());
                }
                JsonNode invDataNodes = jsonResponse.data;
                List<List<SeparateWarehouseConfirmRequest>> orderList = autoSeparateWarehouseHelper.autoSeparateForWhCluster(cot,invDataNodes,customerConfig.getApartType());
                if(CollectionUtil.isEmpty(orderList)){
                    FlowListenerParam.hold(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2, "分仓请求对象orderList为空");
                    throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2.getValue() + "分仓请求对象orderList为空");
                }
                OrderSplitDto dto = batchGenerateOrder(cot, orderList, empoyFlag);
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    FlowListenerParam.hold(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2, dto.getErrMsg());
                    throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2.getValue() + dto.getErrMsg());
                }
            }catch (Exception e){
                //自动分仓失败
                FlowListenerParam.hold(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2,e.getMessage());
                throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2.getValue() + e.getMessage());
            }
            List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
            redisHelper.setAutoSplitSubOrderCount(orderNo,orderNos.size());
            FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
            return customerOrderInfoExt;

        }
        // 有仓库存在
        if (StringUtils.isNotBlank(customerOrderInfoExt.getWhCode())) {
            //拆单，生成拆单对象
            List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfoExt
                , customerConfig.getApartType(),customerOrderInfoExtend);
            if (ToolUtils.isNotEmpty(orderList)) {
                String whCode = customerOrderInfoExt.getWhCode();
                //获取仓库，执行分仓
                for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                    list.forEach(
                        req -> req.setWhCode(whCode)
                    );
                }
                OrderSplitDto dto = batchGenerateOrder(cot, orderList, empoyFlag);
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    String orderType = customerOrderInfoExt.getOrderType();
                    if (Lists.newArrayList(OrderType.AO.getKey(),OrderType.PO.getKey()).contains(orderType)
                    && SourceSystem.OFC.getKey().equals(customerOrderInfoExt.getSourceSystem()) && BusinessMode.isB2B(customerOrderInfoExt.getBusinessMode())
                    ){
                        //2B的PO、AO单子指定仓库的单子,库存不足时支持手工分仓到平台下有库存的仓
                        FlowListenerParam.hold(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT, dto.getErrMsg());
                        throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT.getValue() + dto.getErrMsg());
                    }
                    FlowListenerParam.hold(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2, dto.getErrMsg());
                    throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2.getValue() + dto.getErrMsg());

                    //return customerOrderInfoExt;
                }
            }
            List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
            //2023年5月22日11:23:59 泓铄：电商一单多件，设置子单的拆单数量，后面配送方式解析的时候需要用到
            redisHelper.setAutoSplitSubOrderCount(orderNo,orderNos.size());
            FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
            return customerOrderInfoExt;
        }

        // 没有仓库存在

        //运输单，1：1生成子单
        if (Arrays.asList(OrderType.YS, OrderType.ZF, OrderType.SO).contains(EnumUtils.getEnum(OrderType.class, customerOrderInfoExt.getOrderType()))) {
            List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfoExt
                , customerConfig.getApartType(),customerOrderInfoExtend);
            if (ToolUtils.isNotEmpty(orderList)) {
                //获取仓库，执行分仓
                for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                    list.forEach(
                        req -> req.setWhCode(CommonConstant.WHCODE_FLAG)
                    );
                }

                OrderSplitDto dto = batchGenerateOrder(cot, orderList, empoyFlag && CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()));
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    if (dto.getErrMsg().contains("获取库存等待锁超时")) {
                        throw BusinessException.fail(dto.getErrMsg());
                    }
                    FlowListenerParam.hold(ExceptionType.INVENTORY_INSUFFICIENT, dto.getErrMsg());
                    throw BusinessException.fail(ExceptionType.INVENTORY_INSUFFICIENT.getValue() + dto.getErrMsg());
                    //return customerOrderInfoExt;
                }
            }
            List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());

            FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
            return customerOrderInfoExt;
        }
        //判断是否自动分仓

        //订单接单时，先判断订单的仓库字段是否为空，若为空，去调用配置中心-》》客户配置，此订单是否自动分仓
        if (CommonConstant.STRING_FLAG_NO.equals(customerConfig.getCompartFlag())) {
            customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
            customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
            FlowListenerParam.success("是否分仓配置为否，需要手动分仓");
            customerOrderInfoExt.setIsHandle(true);
            return customerOrderInfoExt;
        }
        //拆单，生成拆单对象
        List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfoExt
            , customerConfig.getApartType(),customerOrderInfoExtend);
        if (ToolUtils.isNotEmpty(orderList)) {
            log.info("----------自动分仓---------{}",customerOrderInfoExt.getOrderNo());
            //自动分仓，查询分仓规则
            CompartRule compartRule = getCompartRule(customerOrderInfoExt);
            if (null == compartRule && !isCZ) {
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                FlowListenerParam.success("未查到分仓规则,无法自动分仓");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }
            //仓直订单默认按基地分仓规则走
            if(null == compartRule && isCZ){
                compartRule = new CompartRule();
                compartRule.setWhCode(customerOrderInfoExt.getWhCode());
                compartRule.setCompartType(CompartTypeEnum.BOWH.getKey());
            }
            //有分仓配置是仓直，默认基地自动分仓
            if(null != compartRule && isCZ){
                compartRule.setWhCode(customerOrderInfoExt.getWhCode());
                compartRule.setCompartType(CompartTypeEnum.BOWH.getKey());
            }
            String whCode = compartRule.getWhCode();
            String compartType = compartRule.getCompartType();
             /*  1 分仓类型（COMPART_TYPE）新增枚举值：基地按仓分（BOWH），配置选择基地按仓分配时，仓库控制非必填；
                 2 order_distinction_flag 等于BO-FW订单，在分仓拆单读取分仓规则节点，若配置的分仓类型为：基地按仓分（BOWH），
                 且仓库非空，则按照配置仓库进行分仓，若仓库为空，则调用按仓库分配逻辑执行分仓，根据分仓算法反馈的仓库进行分仓；*/
            if (ToolUtils.isEmpty(whCode) && !CompartTypeEnum.isBOWH(compartType)) {
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                FlowListenerParam.success("分仓规则未配置仓库，无法分仓");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }
            //无仓库 配置了基地自动分仓，并且是基地前分仓订单


            if (ToolUtils.isNotEmpty(orderList) && ToolUtils.isNotEmpty(whCode)) {
                //获取仓库，执行分仓
                for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                    list.forEach(
                        req -> req.setWhCode(whCode)
                    );
                }
                OrderSplitDto dto = batchGenerateOrder(cot, orderList, empoyFlag && CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()));
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    if (dto.getErrMsg().contains("获取库存等待锁超时")) {
                        throw BusinessException.fail(dto.getErrMsg());
                    }
                    FlowListenerParam.hold(ExceptionType.INVENTORY_INSUFFICIENT, dto.getErrMsg());
                    throw BusinessException.fail(ExceptionType.INVENTORY_INSUFFICIENT.getValue() + dto.getErrMsg());
                    //return customerOrderInfoExt;
                }
            }else if(CompartTypeEnum.isBOWH(compartType) && isBOFW && ToolUtils.isEmpty(whCode)){

                Map<String, WhetherApartedResponse> whetherApartedMap = Optional.ofNullable( customerOrderInfoFeign.batchWhetherAparted(Lists.newArrayList(customerOrderInfoExt.getOrderNo())).data())
                    .orElseThrow(() -> BusinessException.fail("查询订单未分配数量失败"))
                    .stream().collect(Collectors.groupingBy(WhetherApartedResponse::getOrderNo,Collectors.collectingAndThen(Collectors.toList(), v -> v.get(0))));

                Map<String, CustomerOrderInfo> customerOrderInfoMap = new HashMap<>();
                customerOrderInfoMap.put(customerOrderInfoExt.getOrderNo(),customerOrderInfoExt);
                List<List<SeparateWarehouseConfirmRequest>> orderListNew = new ArrayList<>();
                try{
                    orderListNew =  autoSeparateWarehouseHelper.autoSeparateWhByLot(Lists.newArrayList(customerOrderInfoExt.getOrderNo()),whetherApartedMap,customerOrderInfoMap,ProjectClassifyEnum.isZHKT(customerOrderInfoExt.getProjectClassify()));
                }catch (Exception e){
                    FlowListenerParam.hold(ExceptionType.INVENTORY_INSUFFICIENT, JSONObject.toJSONString(e.getMessage()));
                    throw BusinessException.fail(ExceptionType.INVENTORY_INSUFFICIENT.getValue() + JSONObject.toJSONString(e.getMessage()));
                }

                OrderSplitDto dto = batchGenerateOrder(cot, orderListNew, empoyFlag && CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()));
                if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                    if (dto.getErrMsg().contains("获取库存等待锁超时")) {
                        throw BusinessException.fail(dto.getErrMsg());
                    }
                    FlowListenerParam.hold(ExceptionType.INVENTORY_INSUFFICIENT, dto.getErrMsg());
                    throw BusinessException.fail(ExceptionType.INVENTORY_INSUFFICIENT.getValue() + dto.getErrMsg());
                    //return customerOrderInfoExt;
                }
            //默认一个手工分仓
            }else {
                customerOrderInfoExt.setApartType(ApartType.HANDLE.getKey());
                customerOrderInfoExt.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfoExt, "分仓拆单");
                FlowListenerParam.success("分仓规则未配置仓库，无法分仓");
                customerOrderInfoExt.setIsHandle(true);
                return customerOrderInfoExt;
            }
            List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
            //2023年5月22日11:23:59 泓铄：电商一单多件，设置子单的拆单数量，后面配送方式解析的时候需要用到
            redisHelper.setAutoSplitSubOrderCount(orderNo,orderNos.size());
            FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
            return customerOrderInfoExt;
        }
        FlowListenerParam.fail("未定义的分仓规则");
        customerOrderInfoExt.setIsHandle(true);
        return customerOrderInfoExt;

    }


    private List<String> subOrderFlow(CustomerOrderInfoExt customerOrderInfoExt) {

        // 要找到子单，继续触发
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setParentOrderNo(customerOrderInfoExt.getOrderNo());
        JsonResponse<List<OrderInfo>> list = orderInfoFeign.list(orderInfo);
        if (!"0".equals(list.getCode())) {
            throw BusinessException.fail("货权转移，查询子订单失败");
        }
        if (CollectionUtils.isEmpty(list.data)) {
            throw BusinessException.fail("货权转移，无子订单");
        }
        List<String> orderNos = list.data.stream().map(l -> l.getOrderNo()).collect(Collectors.toList());
        if (customerOrderInfoExt.getOrderNo().startsWith(IdGenHelper.PARENT_PREFIX_LMP)) {
            //触发中台流程
            orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
        } else {
            workflowVerifyProducer.send(WorkflowConstant.CUSTOMER_ORDER, customerOrderInfoExt.getOrderNo());
            for (String workflowTicket : orderNos) {
                workflowVerifyProducer.send(WorkflowConstant.ANNTO_ORDER, workflowTicket, customerOrderInfoExt.getOrderNo());
            }
        }
        return orderNos;
    }

    private OrderSplitDto batchGenerateOrder(CreateOrderContext cot, List<List<SeparateWarehouseConfirmRequest>> separateWarehouseConfirmRequestList, boolean compartFlag) {
        if (!CollectionUtils.isEmpty(separateWarehouseConfirmRequestList) && separateWarehouseConfirmRequestList.size() > 1000) {
            return OrderSplitDto.fail("订单拆单已经超过1000个子单，请合理配置拆单规则");
        }
        // 当前子订单
        List<OrderInfo> orderInfoList = Lists.newArrayList();
        List<OrderInfoItem> orderInfoItemList = Lists.newArrayList();
        List<OrderInfoExt> orderInfoExtList = Lists.newArrayList();
        List<String> errorInfos = Lists.newArrayList();
        separateWarehouseConfirmRequestList.forEach(
            s -> {
                OrderSplitDto dto = generateOrder(cot, s);
                if (ToolUtils.isEmpty(dto.getErrMsg())) {
                    orderInfoList.addAll(cot.getOrderInfoList());
                    orderInfoItemList.addAll(cot.getOrderInfoItemList());
                    orderInfoExtList.addAll(cot.getOrderInfoExtList());
                } else {
                    errorInfos.add(dto.getErrMsg());
                }
            }
        );
        if (ToolUtils.isNotEmpty(errorInfos)) {
            List<String> errorInfo = errorInfos.stream().distinct().collect(Collectors.toList());
            return OrderSplitDto.fail(errorInfos.toString());
        }
        boolean notCheckInvFlag = CommonEnum.isAllOne(cot.getNotCheckInvFlag());

        if (compartFlag) {
            // CZF出库直发单，并且CustomerOrderInfo的事业部实体entityId在数据字典的编码有维护的话，则调用美云销的下线直发库存查询接口，返回失败抛出异常
            CustomerOrderInfo customerOrderInfo = cot.getCustomerOrderInfo();
            if (OrderType.CZF.getKey().equals(customerOrderInfo.getOrderType())
                && sccOrderStockCheckService.isMaintenance(customerOrderInfo.getEntityId(), customerOrderInfo.getSiteCode())) {
                OrderStockCheckDto orderStockCheckDto = OrderStockCheckDto.builder()
                    .orderInfoItemList(orderInfoItemList).orderInfo(orderInfoList.get(0)).build();


                try {
                    // 调用美云销的下线直发库存查询接口 SCC库存查询，返回失败则抛出异常
                    orderStockCheckDto.setCheckType(OrderStockCheckDto.CheckType.CHECK);
                    JsonResponse response = sccOrderStockCheckService.czfOrderStockCheck(orderStockCheckDto);
                    if (!response.judgeSuccess()) {
                        return OrderSplitDto.fail("直发订单调用校验上游美云销库存校验出现异常，失败原因" + response.getMsg());
                    }
                } catch (Exception ex) {
                    log.error("直发订单调用校验上游美云销库存校验出现异常，失败原因{}", ex);
                    return OrderSplitDto.fail("直发订单调用校验上游美云销库存校验出现异常，失败原因" + ex.getMessage());
                }
            }
            CustomerOrderInfoExtend customerOrderInfoExtend = cot.getOrderInfoExtend();
            if(null == customerOrderInfoExtend) {
                customerOrderInfoExtend = businessParamHelper.getCustomerOrderInfoExtend(customerOrderInfo.getOrderNo());
                cot.setOrderInfoExtend(customerOrderInfoExtend);
            }
            long currentTimeMillis = System.currentTimeMillis();
            JsonResponse jsonResponse = new JsonResponse<>();
            if (Objects.equals(customerOrderInfo.getSourceSystem(), SourceSystem.WMS.getKey()) && Objects.equals(customerOrderInfo.getUpperOrderType(), OrderType.TI.getKey())) {
                jsonResponse = centerInvService.proHoldInvBatch(orderInfoExtList, CenterInvType.ACT_IN.getKey());
            } else {
                //占用库存时需判断是否仓库集群，若为是，则调用【城配系统接口/中央库存寻仓接口】  统一调用中央库存
               jsonResponse = centerInvService.proHoldInvBatch(orderInfoExtList, CenterInvType.PLAN_OMS_OUT.getKey());
            }
            log.info("占用的-调用中央库存---{}--------proHoldInvBatch-----------耗时{}ms", jsonResponse, System.currentTimeMillis() - currentTimeMillis);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
                String whCode = orderInfoExtList.get(0).getWhCode();
                String whName = Optional.ofNullable(cdWarehouseManager.getCdWarehouseNameByWhCode(whCode)).get();

                Object data = jsonResponse.getData();

                //订单有仓库分仓失败,"占用库存调用鹊桥失败：商品：0010604951仓库可用库存数不足,操作失败,可用库存为 0"
                //***可用库存不足，商品0010604951占用失败；请联系核算主管确认！
                //2022年3月28日11:01:31 李娟： 天猫平台，需要同步天猫缺货记录
                tmallHelper.tmallItemLackNotice(cot.getCustomerOrderInfo(),cot.getCustomerOrderItemList(),jsonResponse.getMsg());
                if(customerOrderInfoExtend != null && StringUtils.isNotBlank(customerOrderInfoExtend.getWhClusterCode())){
                    return OrderSplitDto.fail(jsonResponse.getMsg()+"可用库存不足；请联系核算主管确认！");
                }


                // 获取到库存不足的itemCode，并拼接在仓库名称后面
                String itemCodesStringValue = StockShortageResponseUtil.getStockShortageItemCodes(jsonResponse);
                if (data == null) {
                    return OrderSplitDto.fail(whName + itemCodesStringValue + "可用库存不足；请联系核算主管确认！");
                }
                return OrderSplitDto.fail(whName + itemCodesStringValue + "可用库存不足；" + data.toString() + "请联系核算主管确认！");
            }
        } else if (notCheckInvFlag) {
            //不做库存校验
        } else {
            // CZF出库直发单，并且CustomerOrderInfo的事业部实体entityId在数据字典的编码有维护的话，则调用美云销的下线直发库存查询接口，返回失败抛出异常
            CustomerOrderInfo customerOrderInfo = cot.getCustomerOrderInfo();
            if (OrderType.CZF.getKey().equals(customerOrderInfo.getOrderType())
                && sccOrderStockCheckService.isMaintenance(customerOrderInfo.getEntityId(), customerOrderInfo.getSiteCode())) {
                OrderStockCheckDto orderStockCheckDto = OrderStockCheckDto.builder()
                    .orderInfoItemList(orderInfoItemList).orderInfo(orderInfoList.get(0)).build();
                try {
                    // 调用美云销的下线直发库存查询接口 SCC库存查询，返回失败则抛出异常
                    orderStockCheckDto.setCheckType(OrderStockCheckDto.CheckType.CHECK);
                    JsonResponse response = sccOrderStockCheckService.czfOrderStockCheck(orderStockCheckDto);
                    if (!response.judgeSuccess()) {
                        return OrderSplitDto.fail("直发订单调用校验上游美云销库存校验出现异常，失败原因" + response.getMsg());
                    }
                } catch (Exception ex) {
                    log.error("直发订单调用校验上游美云销库存校验出现异常，失败原因{}", ex);
                    return OrderSplitDto.fail("直发订单调用校验上游美云销库存校验出现异常，失败原因" + ex.getMessage());
                }
            }
        }

        //批量新增
        JsonResponse jr2 = orderInfoItemFeign.batchCreateOrUpdate(orderInfoItemList);
        if (!"0".equals(jr2.getCode())) {
            log.info("lmpverify batchGenerateOrder-> ：customerOrderNo：{} 生成子明细失败，回滚库存",cot.getCustomerOrderInfo().getCustomerOrderNo());
            orderInfoExtList.stream().forEach(
                orderInfoExt ->//取消库存
                    centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey())
            );
            return OrderSplitDto.fail("子订单明细插入失败：" + jr2.getMsg());
        }

        for (OrderInfo orderInfo :orderInfoList){
            String whCode = (String)ThreadLocals.get("whCode_"+orderInfo.getOrderNo());
            if (null != whCode) {
                orderInfo.setWhCode(whCode);
                orderInfo.setWhName((String) ThreadLocals.get("whName_"+orderInfo.getOrderNo()));
            }
        }

        JsonResponse jr1 = orderInfoFeign.batchCreateOrUpdate(orderInfoList);
        if (!"0".equals(jr1.getCode())) {
            List<String> orderNos = orderInfoList.stream().map(o -> o.getOrderNo()).collect(Collectors.toList());
            JsonResponse<List<OrderInfoItem>> orderInfoItemResponse = orderInfoItemFeign.listOrderInfoItemByOrderNos(orderNos);
            if (orderInfoItemResponse.data() != null) {
                List<Long> orderItemIds = orderInfoItemResponse.data().stream().map(OrderInfoItem::getId).collect(Collectors.toList());
                orderInfoItemFeign.batchDeleteByBusinessKey(orderItemIds);
            }
            log.info("lmpverify batchGenerateOrder-> ：customerOrderNo：{} 生成子失败，回滚库存",cot.getCustomerOrderInfo().getCustomerOrderNo());
            orderInfoExtList.stream().forEach(
                orderInfoExt ->//取消库存
                    centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey())
            );
            return OrderSplitDto.fail("子订单插入失败: " + jr1.getMsg());
        }
        CustomerOrderInfo customerOrderInfo = cot.getCustomerOrderInfo();
        customerOrderInfo.setApartStatus(CommonConstant.APARTED);
        if (Lists.newArrayList(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT.getKey(), ExceptionType.INVENTORY_INSUFFICIENT.getKey()).contains(customerOrderInfo.getExceptionType())) {
            customerOrderInfo.setExceptionType("");
            customerOrderInfo.setExceptionDesc("");
        }
        //插入子订单扩展表
        this.createOrderExtend(customerOrderInfo.getOrderNo(), orderInfoList, customerOrderInfo);
        //TODO 触发流程
        // 如果是手动操作分仓，需要触发子订单流程
        if (!CollectionUtils.isEmpty(orderInfoList)) {
            List<String> orderNos = Lists.newArrayList();
            orderNos = orderInfoList.stream().map(o -> o.getOrderNo()).collect(Collectors.toList());
            customerOrderInfo.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
            lmpOrderFlowHelper.updateCanSetEmptyCustomerOrderInfo(customerOrderInfo);
            if (customerOrderInfo.getOrderNo().startsWith(IdGenHelper.PARENT_PREFIX_LMP)) {
                //触发中台流程
                orderNos.stream().forEach(s -> zeebeOrderProducer.sent(s));
            } else {
                workflowVerifyProducer.send(WorkflowConstant.CUSTOMER_ORDER, customerOrderInfo.getOrderNo());
                for (String workflowTicket : orderNos) {
                    workflowVerifyProducer.send(WorkflowConstant.ANNTO_ORDER, workflowTicket, customerOrderInfo.getOrderNo());
                }
            }
        }
        cot.setOrderInfoItemList(orderInfoItemList);
        cot.setOrderInfoExtList(orderInfoExtList);
        cot.setOrderInfoList(orderInfoList);
        return OrderSplitDto.success(orderInfoExtList);
    }


    /**
     * 创建子单扩展表数据
     */
    @Override
    public void createOrderExtend(String orderNo, List<OrderInfo> orderInfoList, CustomerOrderInfo customerOrderInfo) {
        CustomerOrderInfoExtend search = new CustomerOrderInfoExtend();
        search.setOrderNo(orderNo);
        JsonResponse<CustomerOrderInfoExtend> customerOrderInfoExtendJsonResponse = customerOrderInfoExtendFeign.selectOne(search);
        CustomerOrderInfoExtend customerOrderInfoExtend = null;
        if (customerOrderInfoExtendJsonResponse.judgeSuccess()) {
            customerOrderInfoExtend = customerOrderInfoExtendJsonResponse.data;
        }
        List<OrderExtend> saveExtendList = new ArrayList<>();
        List<OrderExtend> updateExtendList = new ArrayList<>();
        Integer vipFlag = customerOrderInfo.getVipFlag();
        for (OrderInfo orderInfo : orderInfoList) {
            //如果已经存在，更新数据
            OrderExtend updateExtend = getOrderExtend(orderInfo.getOrderNo());
            if (updateExtend != null && customerOrderInfoExtend != null && (customerOrderInfoExtend.getSourceBuyerTenantType() != null
                || StringUtils.isNotBlank(customerOrderInfoExtend.getCommerceCategories())
                || vipFlag != null
                || SourceSystem.isMRP(orderInfo.getSourceSystem()) && ObjectUtil.equals(CommonConstant.FLAG_YES, orderInfo.getEmergenceFlag()))) {
                updateExtend.setSourceBuyerTenantType(customerOrderInfoExtend.getSourceBuyerTenantType());
                updateExtend.setDeliveryAmount(customerOrderInfoExtend.getDeliveryAmount()); // 自动分仓：当是MRP的紧急订单时，设置一下运费金额
                updateExtend.setCommerceCategories(customerOrderInfoExtend.getCommerceCategories());

                //子单扩展字段
                if(vipFlag != null){
                    OrderExtendConfDto oeDto = updateExtend.defaultConDto();
                    oeDto.setVipFlag(vipFlag);
                    updateExtend.setConfObj(JSON.toJSONString(oeDto));
                }
                updateExtendList.add(updateExtend);
            } else if (customerOrderInfoExtend != null && (customerOrderInfoExtend.getSourceBuyerTenantType() != null
                || StringUtils.isNotBlank(customerOrderInfoExtend.getCommerceCategories())
                || vipFlag != null
                || SourceSystem.isMRP(orderInfo.getSourceSystem()) && ObjectUtil.equals(CommonConstant.FLAG_YES, orderInfo.getEmergenceFlag()))) {
                OrderExtend<Object> extend = new OrderExtend<>();
                BeanUtils.copyProperties(orderInfo, extend, "id", "remark", "version", "tenantCode");
                extend.setDeliveryAmount(customerOrderInfoExtend.getDeliveryAmount()); // 自动分仓：当是MRP的紧急订单时，设置一下运费金额
                extend.setSourceBuyerTenantType(customerOrderInfoExtend.getSourceBuyerTenantType());
                extend.setCommerceCategories(customerOrderInfoExtend.getCommerceCategories());
                //子单扩展字段
                if(vipFlag != null){
                    OrderExtendConfDto oeDto = new OrderExtendConfDto();
                    oeDto.setVipFlag(vipFlag);
                    extend.setConfObj(JSON.toJSONString(oeDto));
                }
                saveExtendList.add(extend);
            }
        }

        if (saveExtendList.size() > 0) {
            orderExtendFeign.insertBatch(saveExtendList);
        }
        if (updateExtendList.size() > 0) {
            orderExtendFeign.updateBatch(updateExtendList);
        }
    }

    /**
     * @description: !@获取子单扩展表数据
     */
    private OrderExtend getOrderExtend(String orderNo) {
        OrderExtend orderExtend = new OrderExtend();
        orderExtend.setOrderNo(orderNo);
        JsonResponse<OrderExtend> response = orderExtendFeign.selectOne(orderExtend);
        return response.getData();
    }

    /**
     * 复制客户订单信息到新生成的订单
     *
     * @return
     */
    private OrderSplitDto generateOrder(CreateOrderContext cot, List<SeparateWarehouseConfirmRequest> separateWarehouseConfirmRequestList) {
        CustomerOrderInfo customerOrderInfo = cot.getCustomerOrderInfo();
        CustomerOrderAddress address = cot.getOrderAddress();
        List<CustomerOrderItem> customerOrderItems = cot.getCustomerOrderItemList();
        Map<String, Map<Integer, List<CustomerOrderItem>>> customerOrderItemMaps = Maps.newHashMap();
        //根据商品分组
        try {
            customerOrderItemMaps = customerOrderItems
                .stream().collect(Collectors.groupingBy(CustomerOrderItem::getItemCode, Collectors.groupingBy(CustomerOrderItem::getItemLineNo)));
        } catch (NullPointerException e) {
            log.error(e.getMessage(), e);
            return OrderSplitDto.fail("请检查商品信息！商品行号或者商品编码为空");
        }

        List<OrderInfo> orderInfoList = Lists.newArrayList();
        List<OrderInfoItem> orderInfoItemList = Lists.newArrayList();
        List<OrderInfoExt> orderInfoExts = Lists.newArrayList();

        //一个仓库一张订单，所以根据仓库分组
        Map<String, List<SeparateWarehouseConfirmRequest>> separateWarehouseConfirmMap = separateWarehouseConfirmRequestList
            .stream().collect(Collectors.groupingBy(SeparateWarehouseConfirmRequest::getWhCode));
        //家居订单不能多个子单 只能一个仓库
        boolean HLFlag = StringUtils.isNotBlank(customerOrderInfo.getProjectClassify()) && ProjectClassifyEnum.HL.getKey().equals(customerOrderInfo.getProjectClassify());
        //虚拟商品
        boolean fictitiousFlag = StringUtils.isNotBlank(customerOrderInfo.getProjectClassify()) && ProjectClassifyEnum.CDCM_FICTITIOUS.getKey().equals(customerOrderInfo.getProjectClassify());
        if (HLFlag && separateWarehouseConfirmMap.size() != 1) {
            return OrderSplitDto.fail("家居订单不支持拆成多仓库拆单");
        }
        //代收货款不允许拆单
        if (CommonConstant.STRING_FLAG_YES.equals(customerOrderInfo.getCollectionFlag()) && separateWarehouseConfirmMap.size() != 1) {
            return OrderSplitDto.fail("代收货款不允许拆单");
        }
        //菱王电梯不允许拆单
        if (ProjectClassifyEnum.isLW(customerOrderInfo.getProjectClassify()) && separateWarehouseConfirmMap.size() != 1) {
            return OrderSplitDto.fail("菱王电梯不允许拆单");
        }
        order:
        for (List<SeparateWarehouseConfirmRequest> whs : separateWarehouseConfirmMap.values()) {
            OrderInfoExt orderInfoExt = new OrderInfoExt();
            List<OrderInfoItem> orderInfoItems = Lists.newArrayList();

            OrderInfo orderInfo = generateOrder(customerOrderInfo);
            if (address != null) {
                BeanUtils.copyProperties(address, orderInfo
                    , "id", "orderNo", "remark", "planOrderFlag", "tenantCode");
            }

            //数量
            BigDecimal totalQty = BigDecimal.ZERO;
            //体积
            BigDecimal totalVolume = BigDecimal.ZERO;
            //重量
            BigDecimal totalGrossWeight = BigDecimal.ZERO;
            //净重
            BigDecimal totalNetWeight = BigDecimal.ZERO;
            //货值
            BigDecimal orderValue = BigDecimal.ZERO;
            item:
            for (SeparateWarehouseConfirmRequest wh : whs) {
                if (ProjectClassifyEnum.JHLP.getKey().equals(customerOrderInfo.getProjectClassify()) && OrderType.isYSOrder(customerOrderInfo.getOrderType())){
                    orderInfo.setWaybillNo(wh.getWaybillNo());
                }

                Double count = wh.getCount();
                if ( 0d == count){
                    continue item;
                }

                if (CommonConstant.getApartSingle().contains(customerOrderInfo.getUpperOrderType())
                    && BusinessMode.isB2B(customerOrderInfo.getBusinessMode())
                ) {
                    orderInfo.setWaybillNo(wh.getWaybillNo());
                }
                //纯运输订单没有仓库
                if (!CommonConstant.WHCODE_FLAG.equals(wh.getWhCode())) {
                    orderInfo.setWhCode(wh.getWhCode());
                    String whName = cdWarehouseManager.getCdWarehouseNameByWhCode(wh.getWhCode());
                    orderInfo.setWhName(whName);
                }
                //生成订单商品信息
                OrderInfoItem orderInfoItem = new OrderInfoItem();
                orderInfoItem.setItemCode(wh.getItemCode());
                if (ToolUtils.isEmpty(customerOrderItemMaps.get(wh.getItemCode())) ||
                    ToolUtils.isEmpty(customerOrderItemMaps.get(wh.getItemCode()).get(wh.getItemLineNo()))) {
                    continue item;
                }

                CustomerOrderItem customerOrderItem = customerOrderItemMaps.get(wh.getItemCode()).get(wh.getItemLineNo()).get(0);
                //复制客户订单商品信息到新生成的订单商品
                BeanUtils.copyProperties(customerOrderItem, orderInfoItem
                    , "id", "cancleQty", "splitQty", "orderNo", "actQty");
                orderInfoItem.setPlanQty(BigDecimal.valueOf(wh.getCount()));
                orderInfoItem.setParentOrderNo(orderInfo.getParentOrderNo());
                orderInfoItem.setOrderNo(orderInfo.getOrderNo());
                orderInfoItem.setSubOrderNo(customerOrderItem.getSubOrdercode());
                orderInfoItem.setUpdateUserName(null);
                orderInfoItem.setUpdateUserCode(null);
                orderInfoItem.setCreateUserCode(null);
                orderInfoItem.setCreateUserName(null);
                orderInfoItem.setPackingAmount(customerOrderItem.getPackingAmount());

                if (StringUtils.isNotBlank(customerOrderItem.getExtendInfoFields())) {
                    IssuedItemExt issuedItemExt = JSON.parseObject(customerOrderItem.getExtendInfoFields(), IssuedItemExt.class);
                    if (issuedItemExt != null) {
                        orderInfoItem.setExtend1(issuedItemExt.getOfflineOrderConfirmFlag() != null ? issuedItemExt.getOfflineOrderConfirmFlag().toString() : null);
                    }
                }

                orderverifyHelper.setCCSInstallType(orderInfo, customerOrderItem);

                //设置子单商品表的总体积、总毛重
                BigDecimal volume = Optional.ofNullable(orderInfoItem.getVolume()).orElse(BigDecimal.ZERO); // 单台体积
                BigDecimal grossWeight = Optional.ofNullable(orderInfoItem.getGrossWeight()).orElse(BigDecimal.ZERO); // 单台毛重
                BigDecimal planQty = orderInfoItem.getPlanQty(); // 计划数量
                orderInfoItem.setTotalVolume(planQty.multiply(volume));
                orderInfoItem.setTotalGrossWeight(planQty.multiply(grossWeight));

                try{
                    //校验计划数量是否允许录入小数
                    boolean allowDecimal = businessHelper.checkIsAllowDecimal(orderInfoItem.getItemCode(), planQty);
                    //总体积，总重量计算后超过6位，需四舍五入
                    if(allowDecimal){
                        orderInfoItem.setTotalVolume(businessHelper.resetNumberScale(orderInfoItem.getTotalVolume()));
                        orderInfoItem.setTotalGrossWeight(businessHelper.resetNumberScale(orderInfoItem.getTotalGrossWeight()));
                    }
                }catch (Exception e){
                    return OrderSplitDto.fail(e.getMessage());
                }

                //由于家居订单不允许部分拆单，不允许多仓库发货、明细的总体积、总毛重直接取父单明细的
                if (HLFlag) {
                    orderInfoItem.setTotalVolume(customerOrderItem.getTotalVolume());
                    orderInfoItem.setTotalGrossWeight(customerOrderItem.getTotalGrossWeight());
                }
                // 虚拟商品 数量相同直接去父单明细的总体积毛重
                if (fictitiousFlag && planQty.compareTo(customerOrderItem.getPlanQty()) == 0) {
                    orderInfoItem.setTotalVolume(customerOrderItem.getTotalVolume());
                    orderInfoItem.setTotalGrossWeight(customerOrderItem.getTotalGrossWeight());
                }

                orderInfoItemList.add(orderInfoItem);
                orderInfoItems.add(orderInfoItem);
                //计算订单
                totalQty = totalQty.add(orderInfoItem.getPlanQty());
                // B2C B2C的单台体积、单台重量都不能为空
                // B2B 的体积不能为空
                //菜鸟小件，需要判断数量是否符合箱规数量
                if (SourceSystem.CAINIAO.getKey().equals(customerOrderInfo.getSourceSystem()) && StringUtils.isNotEmpty(customerOrderInfo.getGrayFlag()) && ItemStatus.AH.getKey().equals(customerOrderInfo.getGrayFlag())) {
                    if (null == orderInfoItem.getPackingAmount() && null == orderInfoItem.getPackingDecimalAmount()) {
                        return OrderSplitDto.fail("菜鸟小件箱规数量为空!");
                    }
                    BigDecimal packingAmount = orderInfoItem.getPackingDecimalAmount() != null ? orderInfoItem.getPackingDecimalAmount() : new BigDecimal(orderInfoItem.getPackingAmount());
                    log.info("packingAmount:{}", packingAmount);
                    if (planQty.remainder(packingAmount).compareTo(BigDecimal.ZERO) != 0) {
                        return OrderSplitDto.fail("拆单不符合箱规,必须整箱!");
                    }
                }
                boolean hkwsB2bWbkh = false;
                //sourceSystem=HKWSB2B-WBKH 并且订单明细isFirst =0,审核新品不校验体积重量
                if (SourceSystem.HKWSB2B_WBKH.getKey().equals(customerOrderInfo.getSourceSystem())){
                        hkwsB2bWbkh = CommonConstant.FLAG_NO.equals(orderInfoItem.getIsFirst());
                }
                //gl:针对门户送装系统（COP-DIO）来源的服务单（order_type=SO）,分仓拆单跳过毛重体积为空的校验：GrossWeight、Volume,  20241121 手工服务单也不卡提交
                boolean isCopDioSoOrder = SourceSystem.isCOPDIO(customerOrderInfo.getSourceSystem()) && OrderType.isSOOrder(customerOrderInfo.getOrderType()) ;
                boolean isHandleSoOrder = SourceSystem.isHANDLE(customerOrderInfo.getSourceSystem()) && OrderType.isSOOrder(customerOrderInfo.getOrderType()) ;
                //家居订单不校验体积、毛重
                if (!HLFlag && !hkwsB2bWbkh && !isCopDioSoOrder && !isHandleSoOrder) {
                    if (BusinessMode.isB2B(customerOrderInfo.getBusinessMode()) && orderInfoItem.getVolume() == null) {
                        return OrderSplitDto.fail("B2B订单 体积 不能为空");
                    }
                    if (BusinessMode.isB2C(customerOrderInfo.getBusinessMode()) && ((orderInfoItem.getGrossWeight() == null || orderInfoItem.getVolume() == null))) {
                        return OrderSplitDto.fail("B2C订单 毛重 和 体积 不能为空");
                    }
                }

                if (orderInfoItem.getTotalGrossWeight() != null) {
                    totalGrossWeight = totalGrossWeight.add(orderInfoItem.getTotalGrossWeight());
                } else if (orderInfoItem.getGrossWeight() != null) {
                    BigDecimal grossWeightTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getGrossWeight());
                    totalGrossWeight = totalGrossWeight.add(grossWeightTemp);
                }

                if (orderInfoItem.getTotalVolume() != null) {
                    totalVolume = totalVolume.add(orderInfoItem.getTotalVolume());
                } else if (orderInfoItem.getVolume() != null) {
                    BigDecimal volumeTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getVolume());
                    totalVolume = totalVolume.add(volumeTemp);
                }

                if (orderInfo.getTotalNetWeight() != null) {
                    totalNetWeight = orderInfo.getTotalNetWeight();
                } else if (orderInfoItem.getNetWeight() != null) {
                    BigDecimal netWeightTemp = orderInfoItem.getPlanQty().multiply(orderInfoItem.getNetWeight());
                    totalNetWeight = totalNetWeight.add(netWeightTemp);
                }

                if (orderInfoItem.getVolume() != null) {
                    //CCS订单子单货值：sum(price*planQty)/10000；无单价，或price=0，则货值为0；
                    if (SourceSystem.CCS.getKey().equals(customerOrderInfo.getSourceSystem())
                    ) {
                        if (ObjectUtils.defaultIfNull(orderInfoItem.getPrice(), BigDecimal.ZERO).doubleValue() == 0d) {
                            orderValue = orderValue.add(BigDecimal.ZERO);
                        } else {
                            //orderValue =
                            BigDecimal orderValueTemp = ObjectUtils.defaultIfNull(orderInfoItem.getPlanQty(), BigDecimal.ZERO)
                                .multiply(ObjectUtils.defaultIfNull(orderInfoItem.getPrice(), BigDecimal.ZERO)).divide(new BigDecimal(10000), 8, BigDecimal.ROUND_HALF_UP);
                            orderValue = orderValue.add(orderValueTemp);
                        }
                    }
                }

            }

            //家居订单如果总毛重和总体积父单有值，则直接取;
            if (HLFlag) {
                totalVolume = customerOrderInfo.getTotalVolume();
                totalGrossWeight = customerOrderInfo.getTotalGrossWeight();
            }

            //计算订单数量，体积，重量
            orderInfo.setTotalQty(totalQty);
            orderInfo.setTotalVolume(totalVolume);
            orderInfo.setTotalGrossWeight(totalGrossWeight);
            orderInfo.setTotalNetWeight(totalNetWeight);
            orderInfo.setApartStatus(CommonConstant.APARTED);
            if (BigDecimal.ZERO.compareTo(totalQty) == 0) {
                continue order;
            }
            orderInfo.setUpdateUserName(null);
            orderInfo.setUpdateUserCode(null);
            orderInfo.setCreateUserCode(null);
            orderInfo.setCreateUserName(null);
//           CIMS/手工订单子单货值：
//            (orderInfo.totalQty/customerOrderInfo.totalQty)*customerOrderInfo.orderValue
            ArrayList<String> sysList = Lists.newArrayList(SourceSystem.CIMS.getKey()
                , SourceSystem.HANDLE.getKey()
                , SourceSystem.KingDee_SD.getKey(), SourceSystem.OFC.getKey(), SourceSystem.MSS.getKey(), SourceSystem.GJPQCT_WBKH.getKey(), SourceSystem.JNC_CXP.getKey(), SourceSystem.BYT002_WBKH.getKey()
                , SourceSystem.Danone.getKey(), SourceSystem.YJC_WBKH.getKey());
            String sourceSystem = customerOrderInfo.getSourceSystem();
            //2024年4月12日09:02:35 黄泽宇 搞个字典配置 配置规则 来源系统_安得客户编码
            String dictValue = dictHelper.getDictVaule(ORDER_VALUE_ISSUE, String.format("%s_%s", sourceSystem, customerOrderInfo.getCustomerCode()));
            boolean issue = sysList.contains(sourceSystem) || StringUtils.isNotEmpty(dictValue);
            if (issue && ToolUtils.isNotEmpty(customerOrderInfo.getOrderValue())) {
                orderValue = ObjectUtils.defaultIfNull(customerOrderInfo.getOrderValue(), BigDecimal.ZERO)
                    .multiply(ObjectUtils.defaultIfNull(orderInfo.getTotalQty(), BigDecimal.ZERO)).divide(customerOrderInfo.getTotalQty(), 8, BigDecimal.ROUND_HALF_UP);
            }
            orderInfo.setOrderValue(orderValue);


            /**
             * 2025年4月11日10:21:55 黄泽宇 三养食品 采购入库单 因为是按照明细 groupIndication 分组拆单的
             * 将 groupIndication 的值设置到 头表的platformOrderNo 平台单号
             */
            if (SourceSystem.SAMYANG_WBKH.getKey().equals(sourceSystem) && OrderType.isPIOrder(customerOrderInfo.getOrderType())){
                orderInfo.setPlatformOrderNo(orderInfoItemList.get(0).getGroupIndication());
            }

            orderInfoList.add(orderInfo);

            BeanUtils.copyProperties(orderInfo, orderInfoExt);
            orderInfoExt.setOrderInfoItems(orderInfoItems);

            // wms 的 TI 因为是 IN 类型，需求需要走中央库存，但是下面的代码 continue 了，所有在这里处理
            if (SourceSystem.WMS.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.isTIOrder(customerOrderInfo.getOrderType())){
                orderInfoExts.add(orderInfoExt);
                continue order;
            }

            // 同上
            if (SourceSystem.AWMS.getKey().equals(customerOrderInfo.getSourceSystem()) && (OrderType.isADIOrder(customerOrderInfo.getOrderType()) || OrderType.isADOOrder(customerOrderInfo.getOrderType()))) {
                orderInfoExts.add(orderInfoExt);
                continue order;
            }

            boolean isShare = JoinType.SHARE.getKey().equals(customerOrderInfo.getJoinType());
            //纯运输、直发订单不分仓
            if (Arrays.asList(OrderType.YS, OrderType.ZF).contains(EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType()))
                //入库订单不占用库存
                || (InOutType.IN.getName().equals(customerOrderInfo.getInOutType()) && !isShare)
                || OrderType.DO.getKey().equals(customerOrderInfo.getOrderType())
            ) {
                continue order;
            }
            //货权转移,校验是否同仓
            if (isShare) {
                String customerCode = customerOrderInfo.getCustomerCode();
                String targetCustomerCode = customerOrderInfo.getTargetCustomerCode();
                if (ToolUtils.isEmpty(targetCustomerCode)) {
                    return OrderSplitDto.fail("目标客户为空，调剂单,货权转移校验失败！！");
                }
                List<String> sameWarehouseCode = validateSameWarehouseCode(customerCode, targetCustomerCode, Lists.newArrayList(orderInfoExt.getWhCode()));
                if (org.springframework.util.CollectionUtils.isEmpty(sameWarehouseCode)) {
                    //LOGGER.warn("WMS同仓校验失败，orderNo:{}", orderNo);
                    return OrderSplitDto.fail("分仓校验，WMS同仓校验失败，请到WMS-【配置】-【出库配置】-【客户】维护协同关系[客户编码:"+ customerCode + ",目标客户编码:"+ targetCustomerCode +"]！！");
                }
                if (OrderType.RI.getKey().equals(customerOrderInfo.getOrderType())) {
                    orderInfoExt.setCustomerCode(customerOrderInfo.getTargetCustomerCode());
                }
            }
            orderInfoExts.add(orderInfoExt);
        }

        cot.setOrderInfoList(orderInfoList);
        cot.setOrderInfoItemList(orderInfoItemList);
        cot.setOrderInfoExtList(orderInfoExts);
        return OrderSplitDto.success(orderInfoExts);
    }

    @Autowired
    private TaskApiFeign taskApiFeign;

    @Autowired
    private OrderAuditMessageProducer orderAuditMessageProducer;

    private CompartRule getCompartRule(CustomerOrderInfo customerOrderInfo) {

        CompartRule compartRule = new CompartRule();
        compartRule.setCustomerCode(customerOrderInfo.getCustomerCode());
        compartRule.setInOutType(customerOrderInfo.getInOutType());
        compartRule.setSiteCode(customerOrderInfo.getSiteCode());
        compartRule.setOrderType(customerOrderInfo.getOrderType());
        JsonResponse<PageResponse<CompartRule>> search = compartRuleFeign.searchSeparateWarehouse(compartRule);
        if (search == null || search.data == null || CollectionUtils.isEmpty(search.data.list)) {
            return null;
        }
        //多条，返回取订单类型为空的那一条
        List<CompartRule> empOrderTypeRules = new ArrayList<CompartRule>();
        boolean allHasOrderType = true;//是否都包含订单类型
         for (CompartRule rule : search.data.list) {
             //返回的就是查询的那一条，直接返回
             if(!StringUtils.isEmpty(customerOrderInfo.getOrderType()) && customerOrderInfo.getOrderType().equals(rule.getOrderType()) ){
                 return rule;
             }
             if(ToolUtils.isEmpty(rule.getOrderType())){
                 allHasOrderType = false;
                 empOrderTypeRules.add(rule);
            }
        }
         if(CollectionUtils.isNotEmpty(empOrderTypeRules)){
            return empOrderTypeRules.get(0);
         }
         if(allHasOrderType){
             return  null;
         }

        return search.data.list.get(0);
    }


    private CustomerConfig getCustomerConfig(CustomerOrderInfo customerOrderInfo) {
        CustomerConfig customerConfig = new CustomerConfig();
        BeanUtils.copyProperties(customerOrderInfo, customerConfig, "id");
        customerConfig.setOrderSystem(customerOrderInfo.getSourceSystem());

        JsonResponse<CustomerConfig> search = customerConfigFeign.getCustomerConfig(customerConfig);
        if (search == null || !"0".equals(search.getCode())) {
            String result = null != search ? search.toString() : "";
            throw BusinessException.fail("调用客户配置查询失败，返回： " + result + " 参数：" + customerConfig.toString());
        }
        return search.data();
    }


    /**
     * 复制客户订单信息到新生成的订单
     *
     * @param customerOrderInfo
     * @return
     */
    private OrderInfo generateOrder(CustomerOrderInfo customerOrderInfo) {
        OrderInfo orderInfo = new OrderInfo();
        BeanUtils.copyProperties(customerOrderInfo, orderInfo
            , "id", "orderNo");
        orderInfo.setOrderStatus(OrderStatus.NEW.getKey());
        orderInfo.setUpperAgingCode(customerOrderInfo.getAgingProductCode());
        orderInfo.setExcuteStatus(ExcuteStatus.NEW.getKey());
        orderInfo.setParentOrderNo(customerOrderInfo.getOrderNo());
        String orderNo  = flowRuleHelper.getAnOrderNo(customerOrderInfo);
        orderInfo.setTenantCode(customerOrderInfo.getTenantCode());
        orderInfo.setOrderNo(orderNo);
        //如果拓展表有子单号，直接用拓展表的字段覆盖
        CustomerOrderInfoExtend customerOrderInfoExtend = orderFlowHelper.getCustomerOrderInfoExtend(customerOrderInfo.getOrderNo());
        Optional.ofNullable(customerOrderInfoExtend).ifPresent(it -> {
            if (ToolUtils.isNotEmpty(it.getSubOrderNo())) {
                orderInfo.setOrderNo(it.getSubOrderNo());
            }
        });
        return orderInfo;
    }

    private List<List<SeparateWarehouseConfirmRequest>> jhlpApart(CustomerOrderInfo customerOrderInfo,List<CustomerOrderItem> items){
        List<List<SeparateWarehouseConfirmRequest>> orderList = Lists.newArrayList();
        //分仓拆单按母单号拆单，子单运单号：express_no  -  母单号
//根据商品品类分组
        Map<String, List<CustomerOrderItem>> itemClassListMap = items.stream().collect(Collectors.groupingBy(CustomerOrderItem::getExpressNo));
        for (List<CustomerOrderItem> itemClassList : itemClassListMap.values()) {
            List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
            //根据itemCode分组
            Map<String, CustomerOrderItem> itemCodeMap = itemClassList.stream().collect(Collectors.groupingBy(CustomerOrderItem::getItemCode
                , Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            for (CustomerOrderItem customerOrderItem : itemCodeMap.values()) {
                SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
                separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                separateWarehouseConfirmRequest.setWaybillNo(customerOrderItem.getExpressNo());
                list.add(separateWarehouseConfirmRequest);
            }
            orderList.add(list);
        }
        return orderList;
    }

    private List<List<SeparateWarehouseConfirmRequest>> generateSeparateWarehouseConfirmRequest(CustomerOrderInfoExt customerOrderInfo, String apartType,CustomerOrderInfoExtend customerOrderInfoExtend) {
        List<List<SeparateWarehouseConfirmRequest>> orderList = Lists.newArrayList();
        //有两个上游订单类型直接单品单件拆单
        if (CommonConstant.getApartSingle().contains(customerOrderInfo.getUpperOrderType())) {
            apartType = ApartType.SINGLE.getKey();
        }
        //查询客户订单商品信息
        List<CustomerOrderItem> items = Lists.newArrayList();
        items.addAll(customerOrderInfo.getCustomerOrderItems());
        if (null == items || CollectionUtils.isEmpty(items)) {
            throw BusinessException.fail("没有查询到商品信息");
        }
        items.forEach(
            customerOrderItem -> {
                if (ToolUtils.isEmpty(customerOrderItem.getItemCode())) {
                    throw BusinessException.fail("订单商品信息缺少商品编码");
                }
                if (ToolUtils.isEmpty(customerOrderItem.getItemLineNo())) {
                    throw BusinessException.fail("订单商品信息缺少商品行号");
                }
            }
        );
        //zhs:JHLP + 订单类型=YS 才走
        if (ProjectClassifyEnum.JHLP.getKey().equals(customerOrderInfo.getProjectClassify()) && OrderType.isYSOrder(customerOrderInfo.getOrderType())) {
            return this.jhlpApart(customerOrderInfo, items);
        }
        Integer waybillNo = 0;
        if (CommonConstant.getApartSingle().contains(customerOrderInfo.getUpperOrderType())
            && BusinessMode.isB2B(customerOrderInfo.getBusinessMode())
        ) {
            waybillNo = orderFeign.getWaybillNoByCustomerOrderNo(customerOrderInfo.getCustomerOrderNo()).data();
        }
        waybillNo = null == waybillNo ? 0 : waybillNo;

        /**
         * 2025年4月11日09:48:50 三养食品 采购入库单 按照 groupIndication 分组拆单 这次先特殊处理
         * 不改以前的代码  得写在最前面,否则直接就不拆单回传了
         */
        if (SourceSystem.SAMYANG_WBKH.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.isPIOrder(customerOrderInfo.getOrderType())){
            Map<String, List<CustomerOrderItem>> groupIndicationListMap = items.stream().collect(Collectors.groupingBy(
                item -> Optional.ofNullable(item.getGroupIndication()).orElse("NULL"),
                LinkedHashMap::new,
                Collectors.toList()));
            for (List<CustomerOrderItem> itemClassList : groupIndicationListMap.values()) {
                List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                waybillNo++;
                for (CustomerOrderItem customerOrderItem : itemClassList) {
                    SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                    BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                    separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
                    separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                    separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                    separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                    list.add(separateWarehouseConfirmRequest);
                }
                orderList.add(list);
            }
            return orderList;
        }

        //如果配置为不拆单，直接生成订单
        if (ApartType.NOT_APART == EnumUtils.getEnum(ApartType.class, apartType) || ApartType.UNALLOWED == EnumUtils.getEnum(ApartType.class, apartType)) {
            List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
            waybillNo++;
            for (CustomerOrderItem customerOrderItem : items) {
                BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
                separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                list.add(separateWarehouseConfirmRequest);
            }
            orderList.add(list);
            if (OrderType.YS != EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType())
                && OrderType.ZF != EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType())) {
                if (!ApartType.UNALLOWED.getKey().equals(customerOrderInfo.getApartType())) {
                    customerOrderInfo.setApartType(ApartType.NOT_APART.getKey());
                }
                //重新更新状态
                //customerOrderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
                lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "分仓拆单");
            }
            return orderList;
        }
        //如果拆单，但是无拆单规则
        if (StringUtils.isEmpty(apartType)) {
            throw BusinessException.fail("无拆单规则");
        }

        //家居订单不允许部分拆单，不允许多仓库发货，可手工拆单（只能一仓库）或者不拆单，不拆单上面判断了、这里就只判断是否手工分仓，不是直接报错
        boolean HLFlag = StringUtils.isNotBlank(customerOrderInfo.getProjectClassify()) && ProjectClassifyEnum.HL.getKey().equals(customerOrderInfo.getProjectClassify());
        if (HLFlag && ApartType.HANDLE != EnumUtils.getEnum(ApartType.class, apartType)) {
            throw BusinessException.fail("家居订单不允许部分拆单，不允许多仓库发货");
        }
        //订单审核，是否代收=Y，则不允许拆单
        if (CommonConstant.STRING_FLAG_YES.equals(customerOrderInfo.getCollectionFlag())) {
            throw BusinessException.fail("代收货款不允许拆单");
        }
        //菱王电梯不允许拆单
        if (ProjectClassifyEnum.isLW(customerOrderInfo.getProjectClassify())) {
            throw BusinessException.fail("菱王电梯不允许拆单");
        }
        if (ProjectClassifyEnum.JHLP.getKey().equals(customerOrderInfo.getProjectClassify()) &&  OrderType.isYSOrder(customerOrderInfo.getOrderType())) {
            throw BusinessException.fail("当前订单不允许拆单");
        }
        //单品单件
        if (ApartType.SINGLE == EnumUtils.getEnum(ApartType.class, apartType)) {
            //套件
            //List<CustomerOrderItem> suiteItems = items.stream().filter(a -> CommonConstant.FLAG_YES.equals(a.getSetFlag())).collect(Collectors.toList());
            //套件不能根据套散件标识区分,根据商品编码ItemSuiteCode 分组
            List<CustomerOrderItem> suiteItems = items.stream().filter(a -> ToolUtils.isNotEmpty(a.getItemSuiteCode())).collect(Collectors.toList());

            //根据套件编码分组
            Map<String, List<CustomerOrderItem>> suiteItemListMap;
            if (SourceSystem.OFC.getKey().equals(customerOrderInfo.getSourceSystem())) {
                suiteItemListMap = suiteItems.stream()
                    .collect(Collectors.groupingBy(a -> a.getItemSuiteCode() + a.getUpperLineNo()));
            } else {
                suiteItemListMap = suiteItems.stream()
                    .collect(Collectors.groupingBy(CustomerOrderItem::getItemSuiteCode));
            }

            for (List<CustomerOrderItem> suiteItemList : suiteItemListMap.values()) {
                Integer count = 0;
                //同以个套件商品 根据商品编码分组
                Map<Integer, List<CustomerOrderItem>> suiteItemMap = suiteItemList.stream()
                    .collect(Collectors.groupingBy(CustomerOrderItem::getItemLineNo));
                List<CustomerOrderItem> itemClazz = Lists.newArrayList();
                Set<Integer> itemCountSet = Sets.newHashSet();
                //计算出生成的订单数和种类
                for (List<CustomerOrderItem> subSuiteItemList : suiteItemMap.values()) {
                    CustomerOrderItem subSuiteItem = subSuiteItemList.get(0);
                    //订单总数，等于计划数量除以商品数量计划数量
                    if (subSuiteItem.getItemSuiteQty() == null) {
                        subSuiteItem.setItemSuiteQty(1);
                    }
                    BigDecimal subtract = subSuiteItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(subSuiteItem.getCancleQty(), BigDecimal.ZERO));
                    count = subtract.divide(BigDecimal.valueOf(subSuiteItem.getItemSuiteQty())).intValue();
                    itemClazz.add(subSuiteItem);
                    itemCountSet.add(count);
                }
                //菜鸟套机不成比例不卡单
                if (itemCountSet.size() > 1) {
                    //自动分仓：当菜鸟销售出库单执行单品单件拆单时，若相同套机编码的商品计划数量不成比例时，例如1:2、2:4之类的，不卡单，则不拆单，直接拆分成一个子单
                    //https://cf.annto.com/pages/viewpage.action?pageId=81869030
                    if (SourceSystem.CAINIAO.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.isPOOrder(customerOrderInfo.getOrderType())) {
                        waybillNo ++ ;
                        List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                        for (CustomerOrderItem customerOrderItem : itemClazz) {
                            SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                            BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                            separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
                            separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                            separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                            separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                            list.add(separateWarehouseConfirmRequest);
                        }
                        orderList.add(list);
                        continue;
                    }
                    throw BusinessException.fail("套件商品数量不对");
                }
                for (int i = 0; i < count; i++) {
                    waybillNo++;
                    List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                    for (CustomerOrderItem customerOrderItem : itemClazz) {
                        SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                        separateWarehouseConfirmRequest.setCount(Double.valueOf(customerOrderItem.getItemSuiteQty()));
                        separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                        separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                        separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                        list.add(separateWarehouseConfirmRequest);
                    }
                    orderList.add(list);
                }
            }
            //非套件，一个商品一单
            items.removeAll(suiteItems);
            //小于0.1m³ 商品处理 TODO https://cf.annto.com/pages/viewpage.action?pageId=51617256
            //来源系统为美云销/美的零售，订单类型为（B2C & 非基地）&销售出库单
            boolean splitSmallItem = Arrays.asList(SourceSystem.OFC.getKey(), SourceSystem.MRP.getKey()).contains(customerOrderInfo.getSourceSystem())
                && OrderType.isPOOrder(customerOrderInfo.getOrderType())
                && BusinessMode.isB2C(customerOrderInfo.getBusinessMode())
                && customerOrderInfoExtend!=null &&  !OrderDistinctionFlag.isBase(customerOrderInfoExtend.getOrderDistinctionFlag());
            List<CustomerOrderItem> smallItems = new ArrayList<>();
            List<CustomerOrderItem> notSmallItems = new ArrayList<>();
            if(splitSmallItem && !CollectionUtil.isEmpty(items)){
                for(CustomerOrderItem customerOrderItem : items){
                    if(customerOrderItem.getVolume()!=null && customerOrderItem.getVolume().compareTo(new BigDecimal("0.1"))<0){
                        smallItems.add(customerOrderItem);
                    }else{
                        notSmallItems.add(customerOrderItem);
                    }
                }
                //非小件 ，继续按正常（单品单件）的拆
                items = notSmallItems;
            }

            for (CustomerOrderItem customerOrderItem : items) {
                BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));

                for (int i = 0; i < subtract.intValue(); i++) {
                    waybillNo++;
                    List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                    SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                    separateWarehouseConfirmRequest.setCount(Double.valueOf(1));
                    separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                    separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                    separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                    list.add(separateWarehouseConfirmRequest);
                    orderList.add(list);
                }
            }
            //如果有小件,小件单独不按单品单件组装，再加入到其他子单中
            if(!CollectionUtil.isEmpty(smallItems) && (!CollectionUtil.isEmpty(notSmallItems) || !CollectionUtil.isEmpty(suiteItems))){
                log.info("非全是小件，小件加入非小件的子单中,订单号{}",customerOrderInfo.getCustomerOrderNo());
                //不做单品单件拆单
                List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                for(CustomerOrderItem customerOrderItem : smallItems){
                    SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                    BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                    separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
                    separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                    separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                    waybillNo++;
                    separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                    list.add(separateWarehouseConfirmRequest);
                }
                //加入到其中一个子单中去
                if(!CollectionUtil.isEmpty(orderList)){
                    orderList.get(0).addAll(list);
                }else {
                    orderList.add(list);
                }

            }
            //全部小件，按单品单件拆
            if(!CollectionUtil.isEmpty(smallItems) && CollectionUtil.isEmpty(notSmallItems) && CollectionUtil.isEmpty(suiteItems)){
                log.info("全是小件，按单品单件拆,订单号{}",customerOrderInfo.getCustomerOrderNo());
                for (CustomerOrderItem customerOrderItem : smallItems) {
                    BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));

                    for (int i = 0; i < subtract.intValue(); i++) {
                        waybillNo++;
                        List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                        SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                        separateWarehouseConfirmRequest.setCount(Double.valueOf(1));
                        separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                        separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                        separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                        list.add(separateWarehouseConfirmRequest);
                        orderList.add(list);
                    }
                }
            }

        }
        if (ApartType.ITEM_CLASS == EnumUtils.getEnum(ApartType.class, apartType)) {
            //根据商品品类分组
            Map<String, List<CustomerOrderItem>> itemClassListMap = items.stream().collect(Collectors.groupingBy(CustomerOrderItem::getItemClass));
            for (List<CustomerOrderItem> itemClassList : itemClassListMap.values()) {
                waybillNo++;
                List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                //根据itemCode分组
                Map<String, CustomerOrderItem> itemCodeMap = items.stream().collect(Collectors.groupingBy(CustomerOrderItem::getItemCode
                    , Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
                for (CustomerOrderItem customerOrderItem : itemCodeMap.values()) {
                    SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                    BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                    separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
                    separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                    separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                    separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                    list.add(separateWarehouseConfirmRequest);
                }
                orderList.add(list);
            }
        }
        //按照商品行拆单
        if (ApartType.ITEM_ROW == EnumUtils.getEnum(ApartType.class, apartType)) {
            //套件根据商品编码ItemSuiteCode 分组
            List<CustomerOrderItem> suiteItems = items.stream().filter(a -> ToolUtils.isNotEmpty(a.getItemSuiteCode())).collect(Collectors.toList());
            //根据套件编码分组
            Map<String, List<CustomerOrderItem>> suiteItemListMap = suiteItems.stream()
                .collect(Collectors.groupingBy(CustomerOrderItem::getItemSuiteCode));
            for (List<CustomerOrderItem> suiteItemList : suiteItemListMap.values()) {
                //同以个套件商品 根据商品行分组
                Map<Integer, List<CustomerOrderItem>> suiteItemMap = suiteItemList.stream()
                    .collect(Collectors.groupingBy(CustomerOrderItem::getItemLineNo));
                List<CustomerOrderItem> itemClazz = Lists.newArrayList();
                //计算出生成的订单种类
                for (List<CustomerOrderItem> subSuiteItemList : suiteItemMap.values()) {
                    CustomerOrderItem subSuiteItem = subSuiteItemList.get(0);
                    //订单总数，等于计划数量除以商品数量计划数量
                    if (subSuiteItem.getItemSuiteQty() == null) {
                        subSuiteItem.setItemSuiteQty(1);
                    }
                    itemClazz.add(subSuiteItem);
                }
                List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                waybillNo++;
                for (CustomerOrderItem customerOrderItem : itemClazz) {
                    SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                    separateWarehouseConfirmRequest.setCount(customerOrderItem.getPlanQty().doubleValue());
                    separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                    separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                    separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);

                    list.add(separateWarehouseConfirmRequest);
                }
                orderList.add(list);
            }
            //非套件，一个商品一单
            items.removeAll(suiteItems);
            for (CustomerOrderItem customerOrderItem : items) {

                BigDecimal subtract = customerOrderItem.getPlanQty().subtract(ObjectUtils.defaultIfNull(customerOrderItem.getCancleQty(), BigDecimal.ZERO));
                List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
                SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
                separateWarehouseConfirmRequest.setCount(subtract.doubleValue());
                separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
                separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
                waybillNo++;
                separateWarehouseConfirmRequest.setWaybillNo(customerOrderInfo.getCustomerOrderNo() + "-" + waybillNo);
                list.add(separateWarehouseConfirmRequest);
                orderList.add(list);
            }
        }
        //customerOrderInfo.setOrderStatus(OrderStatus.AUDITED.getKey());
        //获取的拆单规则为手工
        if (ApartType.HANDLE == EnumUtils.getEnum(ApartType.class, apartType)) {
//            List<SeparateWarehouseConfirmRequest> list = Lists.newArrayList();
//            for (CustomerOrderItem customerOrderItem : items) {
//                SeparateWarehouseConfirmRequest separateWarehouseConfirmRequest = new SeparateWarehouseConfirmRequest();
//                separateWarehouseConfirmRequest.setCount(customerOrderItem.getPlanQty().doubleValue());
//                separateWarehouseConfirmRequest.setItemCode(customerOrderItem.getItemCode());
//                separateWarehouseConfirmRequest.setItemLineNo(customerOrderItem.getItemLineNo());
//                list.add(separateWarehouseConfirmRequest);
//            }
//            orderList.add(list);
            customerOrderInfo.setApartType(apartType);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "分仓拆单");
        }
        if (OrderType.YS != EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType())
            && OrderType.ZF != EnumUtils.getEnum(OrderType.class, customerOrderInfo.getOrderType())) {
            customerOrderInfo.setApartType(apartType);
            lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "分仓拆单");
        }
        if (ToolUtils.isNotEmpty(orderList)) {
            return orderList;
        }
        throw BusinessException.fail("未识别的拆单类型：" + apartType);
    }


    @Override
    public List<String> validateSameWarehouseCode(String customerCode, String targetCustomerCode, List<String> whCodes) {
        WmsSameWhCodeValidateDto whCodeValidateDto = new WmsSameWhCodeValidateDto();
        whCodeValidateDto.setCustomerCode(customerCode);
        whCodeValidateDto.setTargetCustomerCode(targetCustomerCode);
        whCodeValidateDto.setWhCode(whCodes);
        JsonResponse<String> response = otpService.validateWmsSameWhCodeValidate(whCodeValidateDto);

        if (String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()).equals(response.getCode())) {
            throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
        }

        if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            throw BusinessException.fail("同仓校验,调用鹊桥失败");
        }
        log.info("validateWmsSameWhCodeValidate==============>{}", response.toString());
        String stringResult = response.data();
        OpenJsonResponse<String> openJsonResponse = JSON.parseObject(stringResult, new TypeReference<OpenJsonResponse<String>>() {
        });

        if (!"0".equals(openJsonResponse.getCode())) {
            throw BusinessException.fail("同仓校验失败:" + stringResult);
        }

        List<String> list1 = JSON.parseObject(openJsonResponse.data(), List.class);
        return list1;

    }

    /**
     * 点击时判断：子单order_info表中，
     * （delete_flag=1 || excute_status=880 || excute_status=999 || excute_status=987）时，
     * 允许取消库存占用，否则取消库存占用失败：当前订单未冲销或关闭，
     * 不支持取消预占。在order_info表中无法查到的记录也取消库存占用失败：
     * 当前订单不是城配订单，不支持取消预占。取消库存占用后，记录log到子单日志：操作人+操作。
     *
     * @param orderNo
     * @return
     */
    @Override
    public boolean callCenterInv(String orderNo) {
        List<String> orderNos = Lists.newArrayList(orderNo);
        JsonResponse<List<OrderInfo>> orderInfoResponse = orderFeign.listByOrderNosAndDel(orderNos);
        if (null == orderInfoResponse
            || CollectionUtils.isEmpty(orderInfoResponse.data)) {
            throw BusinessException.fail("当前订单不是城配订单，不支持取消预占");
        }
        OrderInfo order = orderInfoResponse.data().get(0);
        if (!(order.getDeleteFlag() == 1 || order.getExcuteStatus() == 880
            || order.getExcuteStatus() == 999 )) {
            throw BusinessException.fail("当前订单未冲销或关闭，不支持取消预占");
        }
        JsonResponse<List<OrderInfoItem>> orderInfoItemResponse = orderInfoItemFeign.listOrderInfoItemByOrderNosAndDel(orderNos);

        if (null == orderInfoItemResponse
            || CollectionUtils.isEmpty(orderInfoItemResponse.data)) {
            throw BusinessException.fail("未查询到订单商品信息");
        }
        //取消库存占用对象
        OrderInfoExt orderInfoExt = new OrderInfoExt();
        List<OrderInfoItem> orderInfoItems = orderInfoItemResponse.data();
        BeanUtils.copyProperties(order, orderInfoExt);
        orderInfoExt.setOrderInfoItems(orderInfoItems);

        UserInfo userInfo = (UserInfo) iSsoService.getUserInfo();
        String userName = "";
        if (null != userInfo) {
            userName = userInfo.getUserName();
        }
        OrderLog orderLog = new OrderLog();
        orderLog.setOperateFlag("Y");
        orderLog.setOperateType(OrderOperateType.CANCEL_CALL_CENTER_INV.getKey());
        orderLog.setParentOrderNo(order.getParentOrderNo());
        orderLog.setCustomerOrderNo(order.getCustomerOrderNo());
        orderLog.setOrderNo(orderNo);
        //取消库存
        JsonResponse response = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
        if (!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            JsonResponse jsonResponse2 = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
            log.info("取消占用的---------" + jsonResponse2);
            Object data = response.getData();
            orderLog.setOperateFlag("N");
            String logContent = userName + ":取消占用库存调用鹊桥失败";
            orderLog.setOperateContent(logContent);
            esOrderLogServiceImpl.saveLog(orderLog);
            throw BusinessException.fail(logContent);
        }
        orderLog.setOrderNo(orderNo);
        orderLog.setOperateContent(userName + ":" + orderNo + "--取消预占库存成功");
        esOrderLogServiceImpl.saveLog(orderLog);
        return true;
    }


    public String checkAparted(String orderNo) {
        List<String> apartStatusList = Lists.newArrayList();
        String apartStatus = CommonConstant.APARTED;
        //校验订单商品数量
        JsonResponse<WhetherApartedResponse> whetherApartedResponseJsonResponse = customerOrderInfoFeign.whetherAparted(orderNo);

        if (whetherApartedResponseJsonResponse == null || whetherApartedResponseJsonResponse.data() == null) {
            throw BusinessException.fail("校验订单商品失败");
        }
        WhetherApartedResponse whetherApartedResponse = whetherApartedResponseJsonResponse.data();
        for (WhetherApartedResponse.WhetherApartedItem item : whetherApartedResponse.getItems()) {

            BigDecimal sum = item.getUnallocatedNum();
            if (sum.compareTo(BigDecimal.ZERO) <= 0) {
                apartStatusList.add(CommonConstant.APARTED);
            } else {
                apartStatusList.add(CommonConstant.PART);
            }
        }
        //只要有一种商品没拆完，就是部分拆单
        if (apartStatusList.contains(CommonConstant.PART)) {
            apartStatus = CommonConstant.PART;
        }
        return apartStatus;
    }

    /**
     * 获取系统参数
     *
     * @param key
     * @return
     */
    public HashMap getControlParamByKey(String key) {
        //查询配置系统参数
        ControlParam controlParam = new ControlParam();
        controlParam.setCode(key);
        JsonResponse<PageResponse<ControlParam>> paramPageResponse = controlParamFeign.search(controlParam);
        if (paramPageResponse == null || !BaseCodeEnum.SUCCESS.getCode().equals(paramPageResponse.getCode())) {
            return null;
        }
        PageResponse<ControlParam> controlPara = paramPageResponse.getData();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(controlPara.getList())) {
            //不存在配置
            return null;
        }
        ControlParam contrlPara = controlPara.getList().get(0);
        String cfgStr = contrlPara.getValue();
        if (StringUtils.isEmpty(cfgStr)) {
            return null;
        }
        HashMap map = JSON.parseObject(cfgStr, HashMap.class);
        if (map.isEmpty()) {
            return null;
        }
        return map;
    }

    /**
     * 检查 是否有  wms ti 的订单
     * 丽红 ：按来源单号里的值，去查customer_order_info的customer_order_no，来源系统：WMS，订单类型：TI）是否有订单状态（order_status）不是取消（999）记录，
     * 有记录则接入自动关单，否则当作正常的中转单接入到系统里。
     * @param customerOrderInfoExt
     * @return
     */
    private boolean hasWmsTiCustomerOrderInfo(CustomerOrderInfoExt customerOrderInfoExt) {
        CustomerOrderInfo query = new CustomerOrderInfo();
        query.setCustomerOrderNo(customerOrderInfoExt.getOriginOrderNo());
        query.setSourceSystem(SourceSystem.WMS.getKey());
        query.setOrderType(OrderType.TI.getKey());
        CustomerOrderInfo wmsTiCustomerOrderInfo = customerOrderInfoFeign.searchOne(query).data();
        if (wmsTiCustomerOrderInfo != null) {
            if ( ! OrderStatus.CANCEL.getKey().equals(wmsTiCustomerOrderInfo.getOrderStatus())) {
                return true;
            }
        }
        return false;
    }

    /**
     *  生成子单不校验库存
     */
    public CustomerOrderInfoExt generateOrdersNotCheckInv(CustomerOrderInfoExt customerOrderInfoExt, CustomerOrderInfoExtend customerOrderInfoExtend, CustomerConfig customerConfig) {
        CreateOrderContext cot = new CreateOrderContext();
        cot.setCustomerOrderInfo(customerOrderInfoExt);
        cot.setOrderAddress(customerOrderInfoExt.getCustomerOrderAddress());
        List<CustomerOrderItem> customerOrderItems = Lists.newArrayList();
        customerOrderItems.addAll(customerOrderInfoExt.getCustomerOrderItems());
        cot.setCustomerOrderItemList(customerOrderItems);
        cot.setNotCheckInvFlag(CommonEnum.Y.getValue());

        if (customerOrderInfoExtend == null) {
            customerOrderInfoExtend = businessParamHelper.getCustomerOrderInfoExtend(customerOrderInfoExt.getOrderNo());
        }

        if (customerConfig == null) {
            customerConfig = new CustomerConfig();
            customerConfig.setCompartFlag(CommonConstant.STRING_FLAG_NO);
            customerConfig.setApartType(ApartType.NOT_APART.getKey());
        }

        List<List<SeparateWarehouseConfirmRequest>> orderList = generateSeparateWarehouseConfirmRequest(customerOrderInfoExt, customerConfig.getApartType(),customerOrderInfoExtend);
        if (ToolUtils.isNotEmpty(orderList)) {
            String whCode = customerOrderInfoExt.getWhCode();
            //获取仓库，执行分仓
            for (List<SeparateWarehouseConfirmRequest> list : orderList) {
                list.forEach(
                    req -> req.setWhCode(whCode)
                );
            }
            OrderSplitDto dto = batchGenerateOrder(cot, orderList, CommonConstant.STRING_FLAG_YES.equals(customerConfig.getCompartFlag()));
            if (ToolUtils.isNotEmpty(dto.getErrMsg())) {
                FlowListenerParam.hold(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2, dto.getErrMsg());
                throw BusinessException.fail(ExceptionType.NOT_APART_INVENTORY_INSUFFICIENT2.getValue() + dto.getErrMsg());
            }
        }
        List<String> orderNos = cot.getOrderInfoList().stream().map(s -> s.getOrderNo()).collect(Collectors.toList());
        //2023年5月22日11:23:59 泓铄：电商一单多件，设置子单的拆单数量，后面配送方式解析的时候需要用到
        redisHelper.setAutoSplitSubOrderCount(customerOrderInfoExt.getOrderNo(),orderNos.size());
        FlowListenerParam.success("子订单 " + orderNos.size() + " 单:" + JSONObject.toJSONString(orderNos));
        return customerOrderInfoExt;
    }

//        //判断是否人工审单
//    private boolean checkHandHold(CustomerOrderInfoExt customerOrderInfoExt) {
//        CustomerConfig customerConfigQo = new CustomerConfig();
//        customerConfigQo.setSiteCode(customerOrderInfoExt.getSiteCode());
//        customerConfigQo.setWhCode(customerOrderInfoExt.getWhCode());
//        customerConfigQo.setBusinessMode(customerOrderInfoExt.getBusinessMode());
//        customerConfigQo.setOrderSystem(customerOrderInfoExt.getSourceSystem());
//        customerConfigQo.setOrderType(customerOrderInfoExt.getOrderType());
//        customerConfigQo.setCustomerCode(customerOrderInfoExt.getCustomerCode());
//        JsonResponse<CustomerConfig> customerConfigResponse = customerConfigFeign.getCustomerConfig(customerConfigQo);
//        if (null != customerConfigResponse && BaseCodeEnum.SUCCESS.getCode().equals(customerConfigResponse.getCode()) && null != customerConfigResponse.getData()){
//            if (CommonConstant.FLAG_YES.equals(customerConfigResponse.getData().getHandCheckFlag()) && StringUtils.isEmpty(redisHelper.getHoldStatus(customerOrderInfoExt.getOrderNo()))){
//                redisHelper.setHoldStatus(customerOrderInfoExt.getOrderNo());
//                return true;
//            }
//        }
//        return false;
//    }
}
