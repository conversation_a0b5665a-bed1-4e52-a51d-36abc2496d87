<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.IssueRuleMapper">


    <select id="selectCount" resultType="Integer">
        select count(*)
        from issue_rule t
        <include refid="whereFieldsSql"/>
        <if test="dataAuthFlag == 1">
        and (@{"columnCode":"site_code","tableName":"es_company","custom":"site_code","dataCode":"D20190621009"}@)
        </if>

    </select>

    <select id="selectIndex" resultType="com.midea.logistics.otp.rule.domain.bean.IssueRule">
        select
        <include refid="searchFieldsSql"/>
        from issue_rule t
        <include refid="whereFieldsSql"/>
        <if test="dataAuthFlag == 1">
        and (@{"columnCode":"site_code","tableName":"es_company","custom":"site_code","dataCode":"D20190621009"}@)
        </if>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
              ${orderByType}
            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="batchUpdateNew">
        <foreach collection="list" item="item" separator=";">
            UPDATE issue_rule
            <include refid="setBatchFieldsSqlNew"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

    <sql id="setBatchFieldsSqlNew">
        <set>
            `version` = `version` + 1 ,
            <if test="item.orderType !=null and item.orderType != ''">
                `order_type`  = #{item.orderType},
            </if>
            <if test="item.issueSystem !=null and item.issueSystem != ''">
                `issue_system`  = #{item.issueSystem},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code`  = #{item.updateUserCode},
            </if>
            <if test="item.taskType !=null">
                `task_type`  = #{item.taskType},
            </if>
            <if test="item.logisticMode !=null and item.logisticMode != ''">
                `logistic_mode`  = #{item.logisticMode},
            </if>
            <if test="item.whCode !=null">
                `wh_code`  = #{item.whCode},
            </if>
            <if test="item.remark != null">
                `remark`  = #{item.remark},
            </if>
            <if test="item.systemCategory !=null and item.systemCategory != ''">
                `system_category`  = #{item.systemCategory},
            </if>
            <if test="item.pickFlag != null">
                `pick_flag`  = #{item.pickFlag},
            </if>
            <if test="item.taskStatus != null">
                `task_status`  = #{item.taskStatus},
            </if>
            <!--2025年5月30日14:09:49 进广 ： https://devsecops.midea.com/agile/workbench/work/pending?teamId=ID0000000410&systemId=ITS000000873&issueId=AAEC168468&itemType=4&zone=yellow -->
           <!--修改任务下发规则，删除仓库点击保存后，服务中心依旧显示 为了不影响原来的，所以直接新拷贝一套了-->
            <if test="item.siteCode != null ">
                `site_code`  = #{item.siteCode},
            </if>
            <if test="item.siteName != null">
                `site_name`  = #{item.siteName},
            </if>
        </set>
    </sql>

</mapper>