package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.cache.manager.MidCustomerControlManager;
import com.midea.logistics.cache.manager.MidSiteWhControlManager;
import com.midea.logistics.domain.mdm.domain.MidSiteWhControl;
import com.midea.logistics.domain.mdm.request.WhSiteCodeRequest;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.imp.orderverify.service.WarehouseAllocationService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.common.fegin.MidSiteWhControlFeign;
import com.midea.logistics.otp.order.common.fegin.SiteGoodsTypeFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.service.CenterInvService;
import com.midea.logistics.otp.order.converged.domain.response.WhAllocationCcsResponse;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* @description: 仓间调拨标识解析
* @author: 陈永培
* @createtime: 2020/11/14 9:51
*/
@Slf4j
@Service
public class WarehouseAllocationServiceImpl implements WarehouseAllocationService {

    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    @Autowired
    private CenterInvService centerInvServiceImpl;

    @Autowired
    private SiteGoodsTypeFeign siteGoodsTypeFeign;

    @Autowired
    private MidSiteWhControlFeign midSiteWhControlFeign;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private MidSiteWhControlManager midSiteWhControlManager;
    @Autowired
    private MidCustomerControlManager midCustomerControlManager;

    /**
    * @description: 仓间调拨
    * @param: [customerOrderInfo]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2020/11/14 9:58
    */
    @Override
    @ZeebeFlow("ALLOCATION_PARSE")
    public CustomerOrderInfoExt  allocationParse(CustomerOrderInfoExt customerOrderInfo) {

        CommonConstant.checkOrderInfo(customerOrderInfo);

        boolean cimsFlag = SourceSystem.CIMS.getKey().equals(customerOrderInfo.getSourceSystem())&& OrderType.isPOOrder(customerOrderInfo.getOrderType());

        if (cimsFlag) {

            String specimenType = customerOrderInfo.getSpecimenType();
            //2020年4月7日16:03:14 阳阳：CIMS仓间调拨解析的时候，如果是上样订单，不解析仓间调拨
            if(CommonConstant.STRING_FLAG_YES.equals(specimenType)){
                FlowListenerParam.success("CIMS销售出库单,上样订单,不解析仓间调拨");
                return customerOrderInfo;

            }

            if (customerOrderInfo.getUpperTargeWhCode() == null) {
                FlowListenerParam.success("目的财务仓为空，无需调拨");
                return customerOrderInfo;
            }
            if (customerOrderInfo.getUpperTargeWhCode().equals(customerOrderInfo.getUpperWhCode())) {
                FlowListenerParam.success("CIMS销售出库单，目标财务仓编码=上游仓库编码,不做仓间调拨标识解析!");
                return customerOrderInfo;
            }
            WhSiteCodeRequest request = new WhSiteCodeRequest();
            request.setUpperTargetCode(customerOrderInfo.getUpperTargeWhCode());
            request.setUpperCustomerCode(customerOrderInfo.getUpperCustomerCode());
            request.setSiteCode(customerOrderInfo.getSiteCode());
            JsonResponse<MidSiteWhControl> result = siteGoodsTypeFeign.getSiteCode(request);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(result.getCode())) {
                throw BusinessException.fail("调用基础数据 查询 siteCode 失败！" + result.getMsg());
            }
            MidSiteWhControl data = result.getData();
            if (null == data || StringUtils.isBlank(data.getSiteCode())) {
                FlowListenerParam.success("CIMS销售出库单，查询收货平台编码空，不做仓间调拨标识解析！");
                return customerOrderInfo;
            }

            customerOrderInfo.setTargetSiteCode(data.getSiteCode());
            customerOrderInfo.setTargetCustomerCode(customerOrderInfo.getCustomerCode());
            customerOrderInfo.setTargetWhCode(data.getWhCode());
            updateUpstreamDocType(customerOrderInfo, CommonEnum.Y.getKey());

        } else if (SourceSystem.CCS.getKey().equals(customerOrderInfo.getSourceSystem())) {
            String orderType = customerOrderInfo.getOrderType();
            if (OrderType.isAOOrder(orderType) || OrderType.isAIOrder(orderType)) {

                if (StringUtils.isBlank(customerOrderInfo.getRelationOrderNo())){
                    FlowListenerParam.success("CCS调拨入库，无关联单号，不做仓间调拨标识解析！");
                    return customerOrderInfo;
                }

                JsonResponse<WhAllocationCcsResponse> result = centerInvServiceImpl.intefCcsAsnOrdersInbillno(customerOrderInfo.getRelationOrderNo(), customerOrderInfo.getInOutType());

                WhAllocationCcsResponse whAllocationCcsResponse = result.getData();
                if (!BaseCodeEnum.SUCCESS.getCode().equals(result.getCode())) {
                    throw BusinessException.fail(result.getMsg() + ":" + result.getErrMsg());
                }
                if (null == whAllocationCcsResponse) {
                    FlowListenerParam.success("CCS调拨入库，调拨入库单查询为空，不做仓间调拨标识解析！");
                    return customerOrderInfo;
                }

                //如果根据系统来源查询不到 ，再根据上游客户编码查询
                JsonResponse<MidSiteWhControl> whResult = midSiteWhControlFeign.getByWhCode(customerOrderInfo.getSourceSystem(), whAllocationCcsResponse.getWarehouseCode());

                if (!BaseCodeEnum.SUCCESS.getCode().equals(whResult.getCode())) {
                    throw BusinessException.fail("调用基础数据 查询 siteCode whCode 失败！ " + whResult.getMsg());
                }

                MidSiteWhControl midSiteWhControl = whResult.getData();

                if (null == midSiteWhControl) {
                    whResult = midSiteWhControlFeign.getByWhCode(customerOrderInfo.getUpperCustomerCode(), whAllocationCcsResponse.getWarehouseCode());
                    if (!BaseCodeEnum.SUCCESS.getCode().equals(whResult.getCode())) {
                        throw BusinessException.fail("调用基础数据 查询 siteCode,whCode 失败！" + whResult.getMsg());
                    }

                    if (null == whResult.getData()) {
                        FlowListenerParam.success("CCS调拨出库,调拨入库单,未查询出收货平台编码，目标财务仓编码，不做仓间调拨标识解析！");
                        return customerOrderInfo;
                    }
                    midSiteWhControl = whResult.getData();

                }
                customerOrderInfo.setTargetSiteCode(midSiteWhControl.getSiteCode());
                customerOrderInfo.setUpperTargeWhCode(midSiteWhControl.getWhCode());
                customerOrderInfo.setUpperTargeWhName(midSiteWhControl.getWhName());
                customerOrderInfo.setTargetCustomerCode(customerOrderInfo.getCustomerCode());
                customerOrderInfo.setTargetWhCode(midSiteWhControl.getWhCode());
                if (OrderType.isAIOrder(orderType)) {
                    customerOrderInfo.setDeliveryType(DeliveryType.ZT.getKey());
                }
                customerOrderInfo.setUpstreamDocType(CommonEnum.Y.getKey());

                // 2019-9-26 15:37:47 阳阳： CCS调拨出入库，解析为仓间调拨之后，若targetSiteCode=siteCode，则判断仓库编码是否相同（同为空，也认为是相同），则修改订单类型为：AO时，改为TFO；AI时，改为TFI
                String targetSiteCode = customerOrderInfo.getTargetSiteCode();
                String siteCode = customerOrderInfo.getSiteCode();

                // 避免 '' 和 null出现的情况
                if (null == targetSiteCode) {
                    targetSiteCode = "";
                }
                if (null == siteCode) {
                    siteCode = "";
                }

                if (siteCode.equals(targetSiteCode)) {
                    if (OrderType.AO.getKey().equals(orderType)) {
                        customerOrderInfo.setOrderType(OrderType.TFO.getKey());
                        //2020-12-11 17:20:54 李娟：CCS的状态调整单给的调拨单，要我们转换，转换为状态调整单时，配送方式设为ZT
                        customerOrderInfo.setDeliveryType(DeliveryType.ZT.getKey());
                    }else if (OrderType.AI.getKey().equals(orderType)) {
                        customerOrderInfo.setOrderType(OrderType.TFI.getKey());
                        //2020-12-11 17:20:54 李娟：CCS的状态调整单给的调拨单，要我们转换，转换为状态调整单时，配送方式设为ZT
                        customerOrderInfo.setDeliveryType(DeliveryType.ZT.getKey());
                    }
                }

                orderHelper.updateCustomerOrderInfo(customerOrderInfo, "仓间调拨标识");
            }
        } else if ((SourceSystem.OFC.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.isAOPOROOrder(customerOrderInfo.getOrderType()))
            || (SourceSystem.MRP.getKey().equals(customerOrderInfo.getSourceSystem()) && OrderType.isAOPOROOrder(customerOrderInfo.getOrderType()))) {
            //超哥：若UpperTargeWhCode和UpperWhCode查出来得平台对照中得sitecode均有值才设Y
            //gsc:OFC的订单 如果上游给的specimen_type 上样撤样 的值不为空，且为Y 上样或者N 撤样的是时候， 那么在解析仓间调拨的时候就把仓间调拨的标识赋值= N；
            if (CommonEnum.Y.getKey().equals(customerOrderInfo.getSpecimenType()) || CommonEnum.N.getKey().equals(customerOrderInfo.getSpecimenType())) {
                updateUpstreamDocType(customerOrderInfo, CommonEnum.N.getKey());
                FlowListenerParam.success("仓间调拨标识: 上样或撤样默认为N");
                return customerOrderInfo;
            }
            String isUpstreamDocType = CommonEnum.N.getKey();
            if (StringUtils.isNotBlank(customerOrderInfo.getUpperWhCode()) && StringUtils.isNotBlank(customerOrderInfo.getUpperTargeWhCode())) {
                MidSiteWhControl midSiteWhControlCache = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.OFC.getKey(), customerOrderInfo.getUpperWhCode());
                if (null != midSiteWhControlCache && StringUtils.isNotBlank(midSiteWhControlCache.getSiteCode())) {
                    midSiteWhControlCache = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.OFC.getKey(), customerOrderInfo.getUpperTargeWhCode());
                    if (null != midSiteWhControlCache && StringUtils.isNotBlank(midSiteWhControlCache.getSiteCode())) {
                        isUpstreamDocType = CommonEnum.Y.getKey();
                    }
                }
            }
            updateUpstreamDocType(customerOrderInfo, isUpstreamDocType);
//            if (null != customerOrderInfo.getUpperTargeWhCode()) {
//                MidSiteWhControl midSiteWhControlCache = midSiteWhControlManager.getMidSiteWhControlCache(SourceSystem.OFC.getKey(), customerOrderInfo.getUpperTargeWhCode());
//                if (null != midSiteWhControlCache) {
//                    customerOrderInfo.setTargetSiteCode(midSiteWhControlCache.getSiteCode());
//                    customerOrderInfo.setTargetWhCode(midSiteWhControlCache.getWhCode());
//                    updateUpstreamDocType(customerOrderInfo, CommonEnum.Y.getKey());
//                }
//                MidCustomerControl midCustomerControlCache = midCustomerControlManager.getMidCustControlCache(customerOrderInfo.getUpperTargeWhCode(),SourceSystem.OFC.getKey());
//                if (null != midCustomerControlCache) {
//                    customerOrderInfo.setTargetCustomerCode(midCustomerControlCache.getAnnCustomerCode());
//                }
//            }
        } else if (SourceSystem.isMSS(customerOrderInfo.getSourceSystem())) {
            // 来源系统：MSS，订单类型：销售出库、调拨出库，若有收货服务平台（target_site_code），则仓间调拨标识设置为Y（customer_order_info，upstreamDocType = Y） https://cf.annto.com/x/fPSsAg
            String orderType = customerOrderInfo.getOrderType();
            if (OrderType.isPOOrder(orderType) || OrderType.isAOOrder(orderType)) {
                if (StringUtils.isNotBlank(customerOrderInfo.getTargetSiteCode())) {
                    updateUpstreamDocType(customerOrderInfo, CommonEnum.Y.getKey());
                    FlowListenerParam.success("仓间调拨标识: " + customerOrderInfo.getUpstreamDocType());
                    return customerOrderInfo;
                }
                FlowListenerParam.success("收货平台编码为空，无需调拨");
                return customerOrderInfo;
            }
        }
        if (customerOrderInfo.getUpstreamDocType() == null) {
            FlowListenerParam.success("订单非CIMS销售出库单，CCS调拨出库,调拨入库单，OFC调拨出库单，MSS销售出库单,调拨出库单，不做仓间调拨标识解析!");
            return customerOrderInfo;
        }

        FlowListenerParam.success("仓间调拨标识: " + customerOrderInfo.getUpstreamDocType());
        return customerOrderInfo;
    }


    /**
     * 更新客户订单表 是否仓间调拨 字段
     * @param customerOrderInfo 客户订单表
     * @param key 仓间调拨标识 Y/N
     */
    private void updateUpstreamDocType(CustomerOrderInfo customerOrderInfo, String key) {
        customerOrderInfo.setUpstreamDocType(key);
        orderHelper.updateCustomerOrderInfo(customerOrderInfo, "仓间调拨标识");
    }
}
