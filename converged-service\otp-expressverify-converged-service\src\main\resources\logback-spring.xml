<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--<include resource="org/springframework/boot/logging/logback/base.xml" />-->
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>
    <springProperty scope="context" name="spring.application.owner" source="spring.application.owner"/>
    <springProperty scope="context" name="spring.application.organization" source="spring.application.organization"/>
    <springProperty scope="context" name="spring.cloud.config.profile" source="spring.cloud.config.profile"/>
    <springProperty scope="context" name="spring.logistics-log.path" source="spring.logistics-log.path"/>
    <conversionRule conversionWord="ip" converterClass="com.mideaframework.core.config.IPLogConfig" />
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS}][%p][%thread][GTID:%X{midea-apm-gtraceid}][TID:%X{midea-apm-traceid}][SID:%X{spanId}][%logger{39}#%M %L]LINECONTENT:%m%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${spring.logistics-log.path}/${spring.application.name}.log</File>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS}][%p][%thread][GTID:%X{midea-apm-gtraceid}][TID:%X{midea-apm-traceid}][SID:%X{spanId}][%logger{39}#%M %L]LINECONTENT:%m%n
            </pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${spring.logistics-log.path}/bak/${spring.application.name}.%d{yyyy-MM-dd}.%i.log.gz
            </FileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>60</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- or whenever the file size reaches 500MB -->
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>

    </appender>

    <appender name="logistics_mq" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${spring.logistics-log.path}/${spring.application.name}-mq.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${spring.logistics-log.path}/bak/${spring.application.name}-mq.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>60</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- or whenever the file size reaches 500MB -->
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS}][%p][%thread][GTID:%X{midea-apm-gtraceid}][TID:%X{midea-apm-traceid}][SID:%X{spanId}][%logger{39}#%M %L]LINECONTENT:%m%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!--异步输出-->
    <appender name="async_log" class="ch.qos.logback.classic.AsyncAppender">
       <!-- <discardingThreshold>0</discardingThreshold>-->
        <queueSize>3000</queueSize>
        <appender-ref ref="file"/>
    </appender>

    <logger name="logistics_mq" level="INFO">
        <appender-ref ref="logistics_mq"/>
    </logger>

    <appender name="logistics_rest" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${spring.logistics-log.path}/${spring.application.name}-rest.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${spring.logistics-log.path}/bak/${spring.application.name}-rest.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>60</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- or whenever the file size reaches 500MB -->
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS}][%p][%thread][GTID:%X{midea-apm-gtraceid}][TID:%X{midea-apm-traceid}][SID:%X{spanId}][%logger{39}#%M %L]LINECONTENT:%m%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="logistics_rest" level="INFO">
        <appender-ref ref="logistics_rest"/>
    </logger>
    <logger name="com.mideaframework.sdk.helper.MideaAuthUserHelper" level="OFF">
    </logger>

    <logger name="com.mideaframework.sdk.helper.MideaAuthTenantHelper" level="OFF">
    </logger>

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="async_log"/>
        </root>
    </springProfile>
    <springProfile name="sit">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="async_log"/>
        </root>
    </springProfile>
    <springProfile name="uat">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="async_log"/>
        </root>
    </springProfile>
    <springProfile name="ver">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="async_log"/>
        </root>
    </springProfile>
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="async_log"/>
        </root>
    </springProfile>
</configuration>