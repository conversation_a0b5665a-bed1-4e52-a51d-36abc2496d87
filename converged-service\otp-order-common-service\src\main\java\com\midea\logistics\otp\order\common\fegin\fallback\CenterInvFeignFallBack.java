package com.midea.logistics.otp.order.common.fegin.fallback;

import com.midea.logistics.dtc.domain.CdWhPledgeDepart;
import com.midea.logistics.dtc.dto.PledgeConfigByNoDto;
import com.midea.logistics.otp.order.common.bean.OrderSynchronizeBmsRequest;
import com.midea.logistics.logisticsbopsdk.bean.OrderSynchronizationRequest;
import com.midea.logistics.otp.order.common.bean.ConfirmOrderDto;
import com.midea.logistics.otp.order.common.bean.GwmsInventory;
import com.midea.logistics.otp.order.common.fegin.CenterInvFeign;
import com.midea.logistics.otp.order.converged.domain.request.*;
import com.midea.logistics.otp.order.domain.bean.CenterInvInfo;
import com.midea.logistics.otp.order.domain.bean.CenterStockQueryRequest;
import com.midea.logistics.otp.order.domain.bean.ConfirmOrderListRequest;
import com.midea.logistics.otp.order.domain.request.BatchCenterInvQueryReq;
import com.midea.logistics.otp.order.domain.request.CenterInvQueryReq;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年06月25日 上午22:20
 */
@Component
@Slf4j
public class CenterInvFeignFallBack implements FallbackFactory<CenterInvFeign> {
    @Override
    public CenterInvFeign create(Throwable throwable) {
        log.info("bop CenterInvFeign 接口调度失败");
        return new CenterInvFeign() {
            @Override
            public JsonResponse callCenterInvService(String centerInv) {

                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());

//                return JsonResponse.fail(throwable.getMessage());
            }

            @Override
            public JsonResponse<String> queryCenterInvInfo(CenterInvQueryReq centerInvQueryReq) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse<String> batchQueryCenterInvInfo(BatchCenterInvQueryReq centerInvQueryReq) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse<CenterInvInfo> getReceivingPlatformInfo(String orderNo) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse searchUpperCustomerCode(Integer entityId, String upperSourceCustomerCode) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                return JsonResponse.fail(throwable.getMessage());
            }

            @Override
            public JsonResponse getPledgeConfig(PledgeConfigRequest pledgeConfigRequest) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse<List<CdWhPledgeDepart>> searchDepart(String eoorOrderNo, String pledgeOrderNo) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse getPledgeOrderByOrderNo(PledgeOrderRequest request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse getConsigneeSiteByOrderNo(ConsigneeSiteRequest consigneeSiteRequest) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
////                return null;
//                return jsonResponse;
            }

            @Override
            public JsonResponse getCustBalance(CustBalanceRequest request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                return JsonResponse.fail(throwable.getMessage());
            }

            @Override
            public JsonResponse getPledgePrice(List<PledgePriceRequest> list) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse getStockTotalAmout(PledgeAmountRequst requst) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse pledgeHisAdd(List<PledgeHisAddRequest> request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse getPledgeAmountByOrderNo(PledgeAmountRequest request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse<Integer> pledgeCancel(List<PledgeCancelRequest> request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse pledgeRang(PledgeRangRequest request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse getToBOrderNo(String custOrderNo) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse intefCcsAsnOrdersInbillno(WhAllocationCcsRequest request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse searchConfigByNo(PledgeConfigByNoDto pledgeConfigByNoDto) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse pledgeBaseUpdate(List<PledgeBaseUpdateRequest> pledgeBaseUpdateRequests) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse orderSynchronizationToBmSWithBop(OrderSynchronizeBmsRequest request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
            }

            @Override
            public JsonResponse getPledgeDepartByFinanceNo(String financeNo) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse closurePledgeConfig(List<PledgeInputRequest> inputRequestList) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse lmsCenterStockQuery(CenterStockQueryRequest request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse confirmOrderList(ConfirmOrderListRequest confirmOrderListRequest) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse<String> invQuery(Map<String, Object> request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }
            @Override
            public JsonResponse<String> locQuery(List<Map<String,Object>> request) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse<String> queryGwmsInv(Map<String, Object> param) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse<String> queryOtpGwmsInv(Map<String, Object> param) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse callCenterInvServiceAddr(String centerInv) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }

            @Override
            public JsonResponse callCenterInvSeekWhQuery(String centerInv) {
                throw new BusinessException(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()), HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase());
            }
        };
    }
}
