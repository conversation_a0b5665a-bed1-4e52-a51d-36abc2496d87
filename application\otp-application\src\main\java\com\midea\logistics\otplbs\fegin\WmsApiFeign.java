package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.dispatch.domain.request.WmsPreAllocationRequest;
import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.converged.domain.request.LogisticsRouteRequest;
import com.midea.logistics.otp.order.converged.domain.request.OutInWmsConfirmRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: WmsApiFeign
 * Author: lindq
 * Date: 2019-6-23
 * Description:出入库确认
 */
@FeignClient(value = "logistics-otp-order-converged-service")
public interface WmsApiFeign {

    @PostMapping(OrderConvergedRouters.OUTIN_WNS_CONFIRM)
    JsonResponse outInWmsConfirm(@RequestBody OutInWmsConfirmRequest info);

    @PostMapping(OrderConvergedRouters.LOGISTICS_ROUTE)
    public JsonResponse logisticsRoute(@RequestBody LogisticsRouteRequest logisticsRouteRequest);

    @PostMapping(OrderConvergedRouters.WMS_PREALLOCATION)
    public JsonResponse preAllocation(@RequestBody WmsPreAllocationRequest wmsPreAllocationRequest);
}