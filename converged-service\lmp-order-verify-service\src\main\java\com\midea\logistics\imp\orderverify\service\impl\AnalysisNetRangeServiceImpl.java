package com.midea.logistics.imp.orderverify.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.AnalysisNetRangeService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.bop.BopNetFeign;
import com.midea.logistics.otp.common.feign.servicefeign.dispatch.BopFeign;
import com.midea.logistics.otp.common.helper.BusinessParamHelper;
import com.midea.logistics.otp.common.helper.JsonResponseUtils;
import com.midea.logistics.otp.common.utils.TransferUtil;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderDistinctionFlag;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.lastmile.domain.NetTaskAddress;
import com.midea.logistics.otp.lastmile.domain.NetTaskItem;
import com.midea.logistics.otp.lastmile.domain.NetworkReceipt;
import com.midea.logistics.otp.lastmile.domain.bean.NetRange;
import com.midea.logistics.otp.order.common.bean.NetInfo;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderAddressFeign;
import com.midea.logistics.otp.order.common.fegin.lastmile.NetInfoFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.OrderExtend;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.dto.OrderExtendConfDto;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class AnalysisNetRangeServiceImpl implements AnalysisNetRangeService {

    @Autowired
    private BopNetFeign bopNetFeign;
    @Autowired
    private NetInfoFeign netInfoFeign;
    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private BopFeign bopFeign;
    @Autowired
    private BusinessParamHelper businessParamHelper;


    /**
     * b.	当配送方式解析为网点配送时，则执行网点解析节点；若非网点配送订单，则不执行；
     * c.	网点解析即获取网点信息，通过解析出的四级地址编码，
     * 在【网点覆盖范围】中匹配网点编码，获取网点信息如网点名称、网点地址、联系人等，写入order_info表中
     *
     * @param orderInfo
     * @return
     */
    @Override
    @ZeebeFlow("NET_RANGE_ANALYSIS")
    public OrderInfoExt analysisNetRange(OrderInfoExt orderInfo) {
        String deliveryType = orderInfo.getDeliveryType();

        //非网点配送订单，则不执行；
        boolean handleSo = SourceSystem.isHandleSo(orderInfo.getSourceSystem(), orderInfo.getOrderType());
        if (!Lists.newArrayList(DeliveryType.NET.getKey(),DeliveryType.NET_MATCHING.getKey()).contains(deliveryType) && !SourceSystem.isCOPDIO(orderInfo.getSourceSystem()) && !handleSo) {
            FlowListenerParam.success("非网点配送订单，不解析网点");
            return orderInfo;
        }
        //网点解析的时候，销售出库单用收货地址，
        // 退货入库单用发货地址，跟网点覆盖范围里面的四级地址进行校验，匹配网点编码
        CustomerOrderAddress address = new CustomerOrderAddress();
        NetInfo orderNetInfo = null;
        try {
            address.setOrderNo(orderInfo.getParentOrderNo());
            JsonResponse<List<CustomerOrderAddress>> list = customerOrderAddressFeign.list(address);
            if (list == null || !BaseCodeEnum.SUCCESS.getCode().equals(list.getCode()) || list.data == null) {
                throw BusinessException.fail("获取地址失败");
            }
            address = list.getData().get(0);


            //zhs:网点解析节点改为调用CSP接口查询，根据CSP返回的网点信息直接保存即可，废弃原来的先查询NET再查询MDM的逻辑
            NetworkReceipt networkReceipt = new NetworkReceipt();
            NetTaskAddress netTaskAddress = new NetTaskAddress();
            List<NetTaskItem> netTaskItems = new ArrayList();
            BeanUtils.copyProperties(orderInfo, networkReceipt);
            //zhs:退货入库单 入参的收货地址信息和发货地址信息需要对调写入，即发货地址信息写在receiver相关字段上；
            if (OrderType.isRIOrder(orderInfo.getOrderType())) {
                netTaskAddress.setReceiverCityCode(address.getSenderCityCode());
                netTaskAddress.setReceiverCityName(address.getSenderCityName());
                netTaskAddress.setReceiverDetailAddr(address.getSenderDetailAddr());
                netTaskAddress.setReceiverDistrictCode(address.getSenderDistrictCode());
                netTaskAddress.setReceiverDistrictName(address.getSenderDistrictName());
                netTaskAddress.setReceiverProvinceCode(address.getSenderProvinceCode());
                netTaskAddress.setReceiverProvinceName(address.getSenderProvinceName());
                netTaskAddress.setReceiverTownCode(address.getSenderTownCode());
                netTaskAddress.setReceiverTownName(address.getSenderTownName());
                netTaskAddress.setReceiverName(address.getSenderName());
                netTaskAddress.setReceiverTel(address.getSenderTel());
                netTaskAddress.setReceiverMobile(address.getSenderMobile());
                netTaskAddress.setSenderCityCode(address.getReceiverCityCode());
                netTaskAddress.setSenderCityName(address.getReceiverCityName());
                netTaskAddress.setSenderDetailAddr(address.getReceiverDetailAddr());
                netTaskAddress.setSenderDistrictCode(address.getReceiverDistrictCode());
                netTaskAddress.setSenderDistrictName(address.getReceiverDistrictName());
                netTaskAddress.setSenderProvinceCode(address.getReceiverProvinceCode());
                netTaskAddress.setSenderProvinceName(address.getReceiverProvinceName());
                netTaskAddress.setSenderTownCode(address.getReceiverTownCode());
                netTaskAddress.setSenderTownName(address.getReceiverTownName());
                netTaskAddress.setSenderName(address.getReceiverName());
                netTaskAddress.setSenderTel(address.getReceiverTel());
                netTaskAddress.setSenderMobile(address.getReceiverMobile());
            } else {
                netTaskAddress.setReceiverCityCode(address.getReceiverCityCode());
                netTaskAddress.setReceiverCityName(address.getReceiverCityName());
                netTaskAddress.setReceiverDetailAddr(address.getReceiverDetailAddr());
                netTaskAddress.setReceiverDistrictCode(address.getReceiverDistrictCode());
                netTaskAddress.setReceiverDistrictName(address.getReceiverDistrictName());
                netTaskAddress.setReceiverProvinceCode(address.getReceiverProvinceCode());
                netTaskAddress.setReceiverProvinceName(address.getReceiverProvinceName());
                netTaskAddress.setReceiverTownCode(address.getReceiverTownCode());
                netTaskAddress.setReceiverTownName(address.getReceiverTownName());
                netTaskAddress.setReceiverName(address.getReceiverName());
                netTaskAddress.setReceiverTel(address.getReceiverTel());
                netTaskAddress.setReceiverMobile(address.getReceiverMobile());
                netTaskAddress.setSenderCityCode(address.getSenderCityCode());
                netTaskAddress.setSenderCityName(address.getSenderCityName());
                netTaskAddress.setSenderDetailAddr(address.getSenderDetailAddr());
                netTaskAddress.setSenderDistrictCode(address.getSenderDistrictCode());
                netTaskAddress.setSenderDistrictName(address.getSenderDistrictName());
                netTaskAddress.setSenderProvinceCode(address.getSenderProvinceCode());
                netTaskAddress.setSenderProvinceName(address.getSenderProvinceName());
                netTaskAddress.setSenderTownCode(address.getSenderTownCode());
                netTaskAddress.setSenderTownName(address.getSenderTownName());
                netTaskAddress.setSenderName(address.getSenderName());
                netTaskAddress.setSenderTel(address.getSenderTel());
                netTaskAddress.setSenderMobile(address.getSenderMobile());
            }

            List<OrderInfoItem> orderInfoItems = orderInfo.getOrderInfoItems();
            if (!CollectionUtils.isEmpty(orderInfoItems)) {
                for (OrderInfoItem i : orderInfoItems) {
                    NetTaskItem netTaskItem = new NetTaskItem();
                    BeanUtils.copyProperties(i, netTaskItem);
                    netTaskItems.add(netTaskItem);
                }
            }
            networkReceipt.setNetTaskAddress(netTaskAddress);
            networkReceipt.setNetTaskItems(netTaskItems);

            JsonResponse jsonResponse = bopFeign.queryUnitByCsp(networkReceipt);
            if (jsonResponse == null || !jsonResponse.judgeSuccess()) {
                throw BusinessException.fail("CSP站点解析失败" + JsonResponseUtils.getMsg(jsonResponse));
            }
            jsonResponse  = TransferUtil.getJsonResponse(jsonResponse);
            if (jsonResponse == null || !jsonResponse.judgeSuccess()) {
                throw BusinessException.fail("CSP站点解析失败,请联系总部送装人员维护:" + JsonResponseUtils.getMsg(jsonResponse));
            }
            orderNetInfo = JSONObject.parseObject(JSON.toJSONString(jsonResponse.getData()), NetInfo.class);
            if (orderNetInfo == null || StringUtils.isBlank(orderNetInfo.getNetCode())) {
                throw BusinessException.fail("CSP站点解析数据为空,请联系总部送装人员维护");
            }
        } catch (BusinessException e) {
            //gl:来源系统为COP-DIO的家电（order_distinction_flag=JD）服务单（order_type=SO）,跳过站点解析
            if (SourceSystem.isCOPDIO(orderInfo.getSourceSystem()) && OrderDistinctionFlag.isJD(orderInfo.getOrderDistinctionFlag()) && OrderType.isSOOrder(orderInfo.getOrderType())) {
                log.info(e.getMessage());
                FlowListenerParam.success("家电服务单，不解析网点");
                return orderInfo;
            } else if (handleSo && OrderDistinctionFlag.isJD(orderInfo.getOrderDistinctionFlag())) {
                log.info(e.getMessage());
                FlowListenerParam.success("手工家电服务单，不解析网点");
                return orderInfo;
            } else {
                throw BusinessException.fail(e.getMessage());
            }
        }
        //获取网点信息如网点名称、网点地址、联系人等，写入order_info表中
        orderInfo.setNetworkName(orderNetInfo.getNetName());
        orderInfo.setNetworkContact(orderNetInfo.getNetContact());
        orderInfo.setNetworkCode(orderNetInfo.getNetCode());
        orderInfo.setNetworkAddr(orderNetInfo.getNetAddr());
        orderInfo.setNetworkPhone(orderNetInfo.getNetMobile());
        orderInfo.setNetworkTel(orderNetInfo.getNetTel());
        orderInfo.setNetworkProvinceCode(orderNetInfo.getNetProvinceCode());
        orderInfo.setNetworkProvinceName(orderNetInfo.getNetProvinceName());
        orderInfo.setNetworkCityCode(orderNetInfo.getNetCityCode());
        orderInfo.setNetworkCityName(orderNetInfo.getNetCityName());
        orderInfo.setNetworkDistrictCode(orderNetInfo.getNetDistrictCode());
        orderInfo.setNetworkDistrictName(orderNetInfo.getNetDistrictName());
        orderInfo.setNetworkTownCode(orderNetInfo.getNetTownCode());
        orderInfo.setNetworkTownName(orderNetInfo.getNetTownName());
        orderInfo.setNetLng(orderNetInfo.getNetLng());
        orderInfo.setNetLat(orderNetInfo.getNetLat());

        OrderInfo newOrderInfo = new OrderInfo();
        newOrderInfo.setId(orderInfo.getId());
        newOrderInfo.setVersion(orderInfo.getVersion());
        newOrderInfo.setNetworkName(orderNetInfo.getNetName());
        newOrderInfo.setNetworkContact(orderNetInfo.getNetContact());
        newOrderInfo.setNetworkCode(orderNetInfo.getNetCode());
        newOrderInfo.setNetworkAddr(orderNetInfo.getNetAddr());
        newOrderInfo.setNetworkPhone(orderNetInfo.getNetMobile());
        newOrderInfo.setNetworkTel(orderNetInfo.getNetTel());
        newOrderInfo.setNetworkProvinceCode(orderNetInfo.getNetProvinceCode());
        newOrderInfo.setNetworkProvinceName(orderNetInfo.getNetProvinceName());
        newOrderInfo.setNetworkCityCode(orderNetInfo.getNetCityCode());
        newOrderInfo.setNetworkCityName(orderNetInfo.getNetCityName());
        newOrderInfo.setNetworkDistrictCode(orderNetInfo.getNetDistrictCode());
        newOrderInfo.setNetworkDistrictName(orderNetInfo.getNetDistrictName());
        newOrderInfo.setNetworkTownCode(orderNetInfo.getNetTownCode());
        newOrderInfo.setNetworkTownName(orderNetInfo.getNetTownName());
        newOrderInfo.setNetLng(orderNetInfo.getNetLng());
        newOrderInfo.setNetLat(orderNetInfo.getNetLat());
        newOrderInfo.setOrderNo(orderInfo.getOrderNo());

        if (StringUtils.isNotBlank(orderNetInfo.getServerCode()) || StringUtils.isNotBlank(orderNetInfo.getServerName())
                || StringUtils.isNotBlank(orderNetInfo.getOfficerName()) || StringUtils.isNotBlank(orderNetInfo.getOfficerPhone())) {
            OrderExtend orderExtend = new OrderExtend();
            orderExtend.setOrderNo(orderInfo.getOrderNo());
            orderExtend = businessParamHelper.getOrderExtend(orderExtend);
            if (orderExtend != null) {
                OrderExtend updateExtend = new OrderExtend();
                updateExtend.setId(orderExtend.getId());
                updateExtend.setServerCode(orderNetInfo.getServerCode());
                updateExtend.setServerName(orderNetInfo.getServerName());

                // 设置扩展字段
                extendConfDto(updateExtend, orderExtend, orderNetInfo);

                businessParamHelper.updateOrderExtend(updateExtend);
            } else {
                orderExtend = new OrderExtend();
                BeanUtils.copyProperties(orderInfo, orderExtend, "id", "version");
                orderExtend.setServerCode(orderNetInfo.getServerCode());
                orderExtend.setServerName(orderNetInfo.getServerName());

                // 设置扩展字段
                extendConfDto(orderExtend, orderExtend, orderNetInfo);

                businessParamHelper.insertOrderExtend(orderExtend);
            }
        }

        lmpOrderFlowHelper.updateOrderInfo(newOrderInfo, "更新网点信息");
        FlowListenerParam.success("网点解析成功");
        return orderInfo;
    }

    // 设置扩展字段
    public void extendConfDto(OrderExtend updateExtend, OrderExtend oldOrder, NetInfo orderNetInfo) {
        OrderExtendConfDto orderExtendConfDto = oldOrder.defaultConDto();
        if (StringUtils.isNotBlank(orderNetInfo.getOfficerName())) {
            orderExtendConfDto.setOfficerName(orderNetInfo.getOfficerName());
        }
        if (StringUtils.isNotBlank(orderNetInfo.getOfficerPhone())) {
            orderExtendConfDto.setOfficerPhone(orderNetInfo.getOfficerPhone());
        }
        updateExtend.setConfObj(JSON.toJSONString(orderExtendConfDto));
    }
}
