package com.midea.logistics.otp.task.service.impl;

import com.midea.logistics.otp.bean.JobFlowControlRequest;
import com.midea.logistics.otp.enums.OrderStatus;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskExtend;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDeliveryType;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.otp.task.mapper.common.TaskExtendMapper;
import com.midea.logistics.otp.task.mapper.common.TaskItemMapper;
import com.midea.logistics.otp.task.mapper.common.TaskMapper;
import com.midea.logistics.otp.task.mapper.custom.TaskCustomMapper;
import com.midea.logistics.otp.task.service.ITaskCenterService;
import com.mideaframework.core.utils.ToolUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskServiceImpl
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:TaskServiceImpl类
 */
@Service
public class TaskCenterServiceImpl implements ITaskCenterService {


    private static final Logger logger = LoggerFactory.getLogger(TaskCenterServiceImpl.class);

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private TaskCustomMapper taskCustomMapper;

    @Autowired
    private TaskItemMapper taskItemMapper;

    @Autowired
    private TaskExtendMapper taskExtendMapper;

    @Override
    public TaskExt selectOneTaskDetail(Task task) {
        if (task == null) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }
        task = taskMapper.selectOne(task);
        if (task == null) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }

        TaskItem taskItem = new TaskItem();
//        taskItem.setOrderNo(task.getOrderNo());
        taskItem.setTaskNo(task.getTaskNo());
        taskItem.setPageSize(10000);

        List<TaskItem> list = taskItemMapper.selectByIndex(taskItem);

        TaskExt taskDetail = new TaskExt();
        BeanUtils.copyProperties(task, taskDetail);
        taskDetail.setTaskItems(list);
        TaskExtend taskExtend = new TaskExtend();
        taskExtend.setTaskNo(task.getTaskNo());
        taskExtend = taskExtendMapper.selectOne(taskExtend);
        taskDetail.setTaskExtend(taskExtend);
        return taskDetail;
    }
    
    @Override
    public List<TaskExt> taskExtByParentOrderNo(Task task) {
        List<Task> tasks = taskMapper.selectByIndex(task);
        if (tasks == null) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }
    
        List<TaskExt> result = new ArrayList<>();
        for (Task t : tasks) {
            TaskExt taskDetail = searchItemByTask(t);
            result.add(taskDetail);
        }
        return result;
    }
    
    private TaskExt searchItemByTask(Task task) {
        TaskItem taskItem = new TaskItem();
        //        taskItem.setOrderNo(task.getOrderNo());
        taskItem.setTaskNo(task.getTaskNo());
        taskItem.setPageSize(10000);
        
        List<TaskItem> list = taskItemMapper.selectByIndex(taskItem);
        
        TaskExt taskDetail = new TaskExt();
        BeanUtils.copyProperties(task, taskDetail);
        taskDetail.setTaskItems(list);
        TaskExtend taskExtend = new TaskExtend();
        taskExtend.setTaskNo(task.getTaskNo());
        taskExtend = taskExtendMapper.selectOne(taskExtend);
        taskDetail.setTaskExtend(taskExtend);
        return taskDetail;
    }
    
    @Override
    public List<String> taskNewList(Task task) {
        return taskCustomMapper.taskNewList(task);
    }

    @Override
    public List<String> taskHoldList(Task task) {
        return taskCustomMapper.taskHoldList(task);
    }

    @Override
    public List<String> taskFlowControlList(JobFlowControlRequest jobFlowControlRequest) {
        return taskCustomMapper.taskFlowControlList(jobFlowControlRequest);
    }

    @Override
    public List<Task> taskApptList(String taskNo) {
        //2B的批量，收货客户相同（upperReceiverCode相同），且未抵达的、同仓的任务
        Task task = new Task();
        task.setTaskNo(taskNo);
        task = taskMapper.selectOne(task);
        if (null == task){
           return Collections.emptyList();
        }
        String upperReceiverCode = task.getUpperReceiverCode();
        String whCode = task.getWhCode();
        task=new Task();
        task.setWhCode(whCode);
        task.setUpperReceiverCode(upperReceiverCode);
        return taskCustomMapper.taskApptList(task);
    }

    @Override
    public TaskExt selectOneTaskExt(Task task) {
        if (task == null) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }

        TaskExt taskDetail = taskMapper.selectOneDetail(task);
        if (taskDetail == null) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }

        TaskItem taskItem = new TaskItem();
        taskItem.setTaskNo(taskDetail.getTaskNo());
        taskItem.setPageSize(10000);

        List<TaskItem> list = taskItemMapper.selectByIndex(taskItem);

        taskDetail.setTaskItems(list);
        //设置任务扩展表
        TaskExtend taskExtend = new TaskExtend();
        taskExtend.setTaskNo(taskDetail.getTaskNo());
        taskExtend = taskExtendMapper.selectOne(taskExtend);

        taskDetail.setTaskExtend(taskExtend);

        return taskDetail;
    }

    @Override
    public List<Task> tasksByTaskNos(List<String> taskNos) {
        return taskCustomMapper.tasksByTaskNos(taskNos);
    }

    @Override
    public List<TaskDeliveryType> tasksDeliveryTypeByTaskNos(List<String> taskNos) {
        return taskCustomMapper.tasksDeliveryTypeByTaskNos(taskNos);
    }

    @Override
    public TaskExt selectLastTaskExt(TaskExt task) {
        if (task == null) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }

        List<TaskExt> taskDetails = taskMapper.selectTaskDetail(task);
        if (CollectionUtils.isEmpty(taskDetails)) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }
        // 多条记录时取最新任务
        TaskExt taskDetail = taskDetails.get(0);
        if(taskDetails.size() > 1 && taskDetails.size() < 100){
            for(TaskExt t : taskDetails){
                if(t.getId() > taskDetail.getId()){
                    taskDetail = t;
                }
            }
        }

        TaskItem taskItem = new TaskItem();
        taskItem.setTaskNo(taskDetail.getTaskNo());
        taskItem.setPageSize(10000);
        List<TaskItem> list = taskItemMapper.selectByIndex(taskItem);
        taskDetail.setTaskItems(list);

        return taskDetail;
    }

    /**
     * 查询拦截任务和最新的一个任务
     */
    @Override
    public List<TaskExt> selectInterceptAndLastTaskExt(TaskExt task) {
        if (task == null) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }

        List<TaskExt> taskDetails = taskMapper.selectTaskDetail(task);
        if (CollectionUtils.isEmpty(taskDetails)) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }
        List<TaskExt> responseList = new ArrayList();
        // 多条记录时取最新任务和拦截任务
        TaskExt taskDetail = taskDetails.get(0);
        if(taskDetails.size() > 1 && taskDetails.size() < 100){
            for(TaskExt t : taskDetails){
                if(t.getId() > taskDetail.getId()){
                    taskDetail = t;
                }
                if (OrderStatus.INTERCEPT.getKey().equals(t.getOrderStatus())) {
                    responseList.add(t);
                }
            }
        }
        responseList.add(taskDetail);
        if (!CollectionUtils.isEmpty(responseList)) {
            for (TaskExt taskExt : responseList) {
                TaskItem taskItem = new TaskItem();
                taskItem.setTaskNo(taskExt.getTaskNo());
                taskItem.setPageSize(10000);
                List<TaskItem> list = taskItemMapper.selectByIndex(taskItem);
                taskExt.setTaskItems(list);
            }
        }
        return responseList;
    }

    @Override
    public TaskExt selectDistributionNumOrder(TaskExt task) {
        TaskExt taskDetails = taskMapper.selectOneDetail(task);
        if (null==taskDetails) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }
        TaskExtend taskExtend = new TaskExtend();
        taskExtend.setTaskNo(taskDetails.getTaskNo());
        taskExtend=taskExtendMapper.selectOne(taskExtend);
        if(null!=taskExtend) {
            taskDetails.setOrderDistinctionFlag(taskExtend.getOrderDistinctionFlag());
        }
        return taskDetails;
    }


    @Override
    public TaskExt selectDistributionNumOrderWithOrderBy(TaskExt task) {
        task.setOrderBy("t.create_time");
        TaskExt taskDetails = taskMapper.selectOneDetailWithOrderBy(task);
        if (null==taskDetails) {
            logger.warn("select task one and items, but task is null ...");
            return null;
        }
        TaskExtend taskExtend = new TaskExtend();
        taskExtend.setTaskNo(taskDetails.getTaskNo());

        taskExtend=taskExtendMapper.selectOne(taskExtend);
        if(null!=taskExtend) {
            taskDetails.setOrderDistinctionFlag(taskExtend.getOrderDistinctionFlag());
        }
        return taskDetails;
    }

    @Override
    public List<TaskExt> taskExtByIndex(TaskExt condition) {
        if (null == condition
            || ToolUtils.isAllEmpty(condition.getId(), condition.getCustomerOrderNo(), condition.getParentOrderNo(),
            condition.getWaybillNo(), condition.getOrderNo(), condition.getTaskNo())) {
            return null;
        }
        List<Task> tasks = taskMapper.selectByIndex(condition);
        if (CollectionUtils.isEmpty(tasks)) {
            logger.warn("select taskExtByIndex, but task is null ...");
            return null;
        }

        List<TaskExt> result = new ArrayList<>();
        for (Task t : tasks) {
            TaskExt taskDetail = searchItemByTask(t);
            result.add(taskDetail);
        }
        return result;
    }


}