package com.midea.logistics.imp.orderverify.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.service.MdmClientService;
import com.midea.logistics.domain.mdm.domain.ControlParam;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.dtc.domain.EoWorkPledgeSplit;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.PledgeFlowService;
import com.midea.logistics.otp.common.feign.convergedfeign.order.SeparateWarehouseSearchFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderExceptionFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.PledgeGoodsPriceFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.PledgeSplitHisFeign;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.bean.ConfirmOrderDto;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.flow.dto.FlowStatus;
import com.midea.logistics.otp.order.common.service.CenterInvService;
import com.midea.logistics.otp.order.common.service.SeparateWarehouseService;
import com.midea.logistics.otp.order.converged.domain.bean.CustomerOrder;
import com.midea.logistics.otp.order.converged.domain.enums.OrderConvergedReturnCode;
import com.midea.logistics.otp.order.converged.domain.enums.PledgeFailEnum;
import com.midea.logistics.otp.order.converged.domain.enums.PledgeStatusEnum;
import com.midea.logistics.otp.order.converged.domain.request.*;
import com.midea.logistics.otp.order.converged.domain.response.*;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年07月19日 上午20:41
 */
@Slf4j
@Service
public class PledgeServiceImpl implements PledgeFlowService {


    @Autowired
    CenterInvService centerInvServiceImpl;
    @Autowired
    OrderFeign orderFeign;
    @Autowired
    CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    OrderInfoExtFeign orderInfoExtFeign;
    @Autowired
    PledgeInfoExtFeign pledgeInfoExtFeign;
    @Autowired
    EbCustomerManager ebCustomerManager;
    @Autowired
    PledgeInfoFeign pledgeInfoFeign;
    @Autowired
    PledgeSplitHisFeign pledgeSplitHisFeign;
    @Autowired
    OrderInfoExtendFeign orderInfoExtendFeign;
    @Autowired
    OrderInfoFeign orderInfoFeign;
    @Autowired
    OrderExceptionFeign orderExceptionFeign;
    @Autowired
    LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    PledgeGoodsPriceFeign pledgeGoodsPriceFeign;
    @Autowired
    SeparateWarehouseSearchFeign separateWarehouseSearchFeign;
    @Autowired
    SeparateWarehouseService separateWarehouseService;
    @Autowired
    MdmClientService mdmClientService;
    @Autowired
    private BusinessHelper businessHelper;

    static String PLEDGE_B2C_CONTROLL_SYSTEM = "PLEDGE_B2C_CONTROLL_SYSTEM";


    @Autowired
    private CenterInvService centerInvService;

    @Override
    @ZeebeFlow("PLEDGE_VERIFY")
    public OrderInfoExt pledgeCheck(OrderInfoExt orderInfo) {
        String orderNo = orderInfo.getOrderNo();
        MDC.put("traceId", UUID.randomUUID().toString());
        log.info("质押检查 orderNo = {}", orderInfo.getOrderNo());


        //如果 融资客户 启动了调账开始 ，直接放行，不需要变更任何金额
        JsonResponse<PageResponse<ControlParam>> controlResult = mdmClientService.getControlParam(PLEDGE_B2C_CONTROLL_SYSTEM, null);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(controlResult.getCode())) {
            throw new BusinessException("获取系统参数接口失败");
        }
        List<ControlParam> controlParamList = controlResult.getData().list;
        boolean controlFalg = controlParamList.stream().anyMatch(u -> u.getValue().indexOf(orderInfo.getSourceSystem()) >= 0);

        if (BusinessMode.isB2C(orderInfo.getBusinessMode()) && !controlFalg) {
            FlowListenerParam.success("");
//            FlowListenerParam.success("B2C 订单不进行质押校验");
            return orderInfo;
        }

        JsonResponse<List<PledgeInfo>> pledgeInfoResult = pledgeInfoExtFeign.getPledgeByOrderNo(orderNo, PledgeStatusEnum.CHECK.getKey(), PledgeFailEnum.SUCCESS.getKey());
        if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeInfoResult.getCode())) {
            log.info("质押信息 查询失败 pledgeInfoResult = {}", JSON.toJSONString(pledgeInfoResult));
            throw BusinessException.fail(ExceptionType.PLEDGE_INFO_ERROR.getValue());
        }
        //如果已经有存在的，直接返回 相当于做幂等 ，同时 在保存的也是 也做处理
        if (null != pledgeInfoResult.getData() && !pledgeInfoResult.getData().isEmpty()) {
            log.info("该订单已经质押检查 orderNo= {}", orderNo);
            FlowListenerParam.success("质押检查已经操作");
            return orderInfo;
        }

        boolean isShare = OrderType.RI.getKey().equals(orderInfo.getOrderType()) && JoinType.SHARE.getKey().equals(orderInfo.getJoinType());

        //如果不是出库单， 直接放行
        if (!InOutType.isOut(orderInfo.getInOutType()) && !isShare && !OrderType.TF.getKey().equals(orderInfo.getOrderType())) {
            log.info("不是出库单 和调拨单，放行 出库类型 {} ,isShare = {}", orderInfo.getInOutType(), isShare);
            FlowListenerParam.success("");
            return orderInfo;
        }


        //事业部发货 ，发货到安得仓  ，或者直接发给 代理商
        //上游客户发货的场景   =  CIMS（事业部） 发给CCS（代理商）
        //CIMS  的退货入库单 的  调剂单 也要添加校验

        if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem()) && OrderType.PO.getKey().equals(orderInfo.getOrderType())) {
            //cims 上游发融资客户 质押校验 取target_custome

            log.info("CIMS  事业部的单 ，进行校验");
            //如果是事业部自己出货，但是没有指定收货客户 ，直接放行
            if (StringUtils.isBlank(orderInfo.getTargetCustomerCode())) {
                log.info("事业部自己出库，目标客户为null orderNo = {}", orderInfo.getOrderNo());
                FlowListenerParam.success("事业部出货 ，放行");
                return orderInfo;
            }

            // 开始 步骤二 融资客户出货  判断 白名单，第3个步骤之类的操作
            log.info("开始CIMS  质押检查");
            OrderInfoExt result = cimsToAgentAoutCheck(orderInfo);

            JsonResponse<List<PledgeInfo>> pledgeInfoList = pledgeInfoExtFeign.getPledgeByParentOrderNo(orderInfo.getParentOrderNo(), PledgeStatusEnum.CHECK.getKey());
            //不敢对下面的结果进行判断了
            boolean flag = false;
            if (null != pledgeInfoList.getData() && !pledgeInfoList.getData().isEmpty()) {

                //判断是不是最后的一条是质押失败的
                List<PledgeInfo> pledgeInfoListData = pledgeInfoList.getData();
                for (int i = 0; i < pledgeInfoListData.size(); i++) {

                    PledgeInfo pledgeInfo = pledgeInfoListData.get(i);
                    if (PledgeFailEnum.SUCCESS.getKey() != pledgeInfo.getFailFlag() && i == pledgeInfoListData.size() - 1) {
                        flag = true;
                    }
                }
            }
            //判断下有没有质押失败的记录
            if (flag) {

                CustomerOrder orderInfoTemp = new CustomerOrder();
                orderInfoTemp.setOrderNo(orderInfo.getParentOrderNo());
                JsonResponse<CustomerOrderInfo> temp = customerOrderInfoFeign.getOrderInfoByOrderNos(orderInfo.getParentOrderNo());
                orderInfoTemp.setId(temp.getData().getId());
                orderInfoTemp.setOrderTime(new Date());
                orderInfoTemp.setOrderNo(null);
                customerOrderInfoFeign.update(orderInfoTemp.getId(), orderInfoTemp);

                OrderInfo orderInfo1 = new OrderInfo();
                orderInfo1.setOrderTime(new Date());
                orderInfo1.setId(orderInfo.getId());
                orderInfoFeign.update(orderInfo1.getId(), orderInfo1);


            }
            return result;
        } else {

            log.info("开始代理商  质押检查");
            //代理商发货 =融资客户发货


            // 其他类型客户出库
            PledgeConfigRequest pledgeConfigRequest = new PledgeConfigRequest();
            if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem()) && isShare) {
                log.info("CIMS 的调整单 ，targetCustomerCode = {}", orderInfo.getTargetCustomerCode());
                pledgeConfigRequest.setCustomersCode(orderInfo.getTargetCustomerCode());
            } else {
                //如果发货客户为空 ，错误
                if (null == orderInfo.getCustomerCode()) {
                    log.info("CCS 单 ，客户号为null ");
                    throw BusinessException.fail(ExceptionType.ORDER_INFO_ITEM_QUERY_ERROR.getValue() + "客户号为 null");
                }
                pledgeConfigRequest.setCustomersCode(orderInfo.getCustomerCode());
            }
            //一个订单 可能有多个 质押配置
            JsonResponse<List<PledgeConfigResponse>> proxyConfigResp = centerInvServiceImpl.getPledgeConfig(pledgeConfigRequest);
            //如果和BOP通讯不成功 ，返回错误

            if (!BaseCodeEnum.SUCCESS.getCode().equals(proxyConfigResp.getCode())) {
                log.info("查询质押配置失败 proxyConfigResp = {}", JSON.toJSONString(proxyConfigResp));
                throw BusinessException.fail(ExceptionType.PLEDGE_CONFIG_QUERY_ERROR.getValue());
            }
            List<PledgeConfigResponse> pledgeConfigList = proxyConfigResp.getData();


            //获取所有的订单商品
            List<OrderInfoItem> orderInfoItemList = orderInfo.getOrderInfoItems();//getOrderInfoItems(orderInfo);
            //对订单类型的预先校验，判断是不是白名单或者开启调账功能 ，如果返回fail 的继续质押校验，如果返回其他的类型 ，直接返回结果
            FlowListenerParam resultPre = beforeCheck(orderInfo, pledgeConfigList, orderInfoItemList);
            if (!FlowStatus.FAILD.equals(resultPre.getFlowStatus())) {
                if (FlowStatus.SUCCESS.equals(resultPre.getFlowStatus())) {
                    return orderInfo;
                } else {
                    throw BusinessException.fail(resultPre.getErrorMsg());
                }
            }

            //遍历质押单号 ，保存质押历史记录
            List<PledgeHisAddRequest> pledgeHisAddList = new ArrayList();

            List<PledgeInfo> pledgeInfoList = new ArrayList<>();


            //分仓记录
            List<PledgeSplitHis> pledgeSplitHisList = new ArrayList<>();

            //遍历所有的质押单
            for (int i = 0; i < pledgeConfigList.size(); i++) {


                log.info("开始 执行步骤9 ");
                FlowListenerParam result = nineStep(orderInfo, pledgeConfigList.get(i), pledgeHisAddList, pledgeInfoList, orderInfoItemList, pledgeSplitHisList);
                log.info("质押校验失败 ， result = {}", JSON.toJSONString(result));
                if (!FlowStatus.SUCCESS.equals(result.getFlowStatus())) {
                    throw BusinessException.fail(resultPre.getErrorMsg());
                }
            }
            log.info("pledgeHisAddList = {}", JSON.toJSONString(pledgeHisAddList));

            if (!pledgeSplitHisList.isEmpty()) {
                pledgeSplitHisList.forEach(pledgeSplitHis -> {
                    pledgeSplitHisFeign.create(pledgeSplitHis);
                });

            }

            if (!pledgeHisAddList.isEmpty()) {
                log.info("保存CCS质押记录");
                JsonResponse<Integer> pledgeHisResult = centerInvServiceImpl.pledgeHisAdd(pledgeHisAddList);
                if (BaseCodeEnum.SUCCESS.getCode().equals(pledgeHisResult.getCode()) && pledgeHisResult.getData() > 0) {
                    //TODO  需要保证业务上的一致性 采用其他方式
                    JsonResponse<Integer> pledgeInfoBatchResult = pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoList);
                    log.info("批量保存 质押信息 结果 {}", JSON.toJSONString(pledgeInfoBatchResult));
                    FlowListenerParam.success("");

                    JsonResponse<List<PledgeInfo>> pledgeInfoListTemp = pledgeInfoExtFeign.getPledgeByParentOrderNo(orderInfo.getParentOrderNo(), PledgeStatusEnum.CHECK.getKey());
                    ;
                    //不敢对下面的结果进行判断了
                    //不敢对下面的结果进行判断了
                    boolean flag = false;
                    if (null != pledgeInfoListTemp.getData() && !pledgeInfoListTemp.getData().isEmpty()) {

                        //判断是不是最后的一条是质押失败的
                        List<PledgeInfo> pledgeInfoListData = pledgeInfoListTemp.getData();
                        for (int i = 0; i < pledgeInfoListData.size(); i++) {

                            PledgeInfo pledgeInfo = pledgeInfoListData.get(i);
                            if (PledgeFailEnum.SUCCESS.getKey() != pledgeInfo.getFailFlag() && i == pledgeInfoListData.size() - 1) {
                                flag = true;
                            }
                        }
                    }
                    //判断下有没有质押失败的记录
                    if (flag) {

                        CustomerOrder orderInfoTemp = new CustomerOrder();
                        orderInfoTemp.setOrderNo(orderInfo.getParentOrderNo());
                        JsonResponse<CustomerOrderInfo> temp = customerOrderInfoFeign.getOrderInfoByOrderNos(orderInfo.getParentOrderNo());
                        orderInfoTemp.setId(temp.getData().getId());
                        orderInfoTemp.setOrderTime(new Date());
                        orderInfoTemp.setOrderNo(null);
                        customerOrderInfoFeign.update(orderInfoTemp.getId(), orderInfoTemp);

                        OrderInfo orderInfo1 = new OrderInfo();
                        orderInfo1.setOrderTime(new Date());
                        orderInfo1.setId(orderInfo.getId());
                        orderInfoFeign.update(orderInfo1.getId(), orderInfo1);


                    }
                    return orderInfo;
                } else {
                    throw BusinessException.fail("保存质押记录失败 。");
                }
            } else {
                //TODO .同时计算分仓记录
                log.info("保存 本地质押信息记录 pledgeInfoList = {}", JSON.toJSONString(pledgeInfoList));
                JsonResponse<Integer> pledgeInfoBatchResult = pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoList);
                log.info("批量保存 质押信息 结果 {}", JSON.toJSONString(pledgeInfoBatchResult));

                FlowListenerParam.success("");
                JsonResponse<List<PledgeInfo>> pledgeInfoListTemp = pledgeInfoExtFeign.getPledgeByParentOrderNo(orderInfo.getParentOrderNo(), PledgeStatusEnum.CHECK.getKey());
                //不敢对下面的结果进行判断了
                boolean flag = false;
                if (null != pledgeInfoListTemp.getData() && !pledgeInfoListTemp.getData().isEmpty()) {

                    //判断是不是最后的一条是质押失败的
                    List<PledgeInfo> pledgeInfoListData = pledgeInfoListTemp.getData();
                    for (int i = 0; i < pledgeInfoListData.size(); i++) {

                        PledgeInfo pledgeInfo = pledgeInfoListData.get(i);
                        if (PledgeFailEnum.SUCCESS.getKey() != pledgeInfo.getFailFlag() && i == pledgeInfoListData.size() - 1) {
                            flag = true;
                        }
                    }
                }
                //判断下有没有质押失败的记录
                if (flag) {

                    CustomerOrder orderInfoTemp = new CustomerOrder();
                    orderInfoTemp.setOrderNo(orderInfo.getParentOrderNo());
                    JsonResponse<CustomerOrderInfo> temp = customerOrderInfoFeign.getOrderInfoByOrderNos(orderInfo.getParentOrderNo());
                    orderInfoTemp.setId(temp.getData().getId());
                    orderInfoTemp.setOrderTime(new Date());
                    orderInfoTemp.setOrderNo(null);
                    customerOrderInfoFeign.update(orderInfoTemp.getId(), orderInfoTemp);

                    OrderInfo orderInfo1 = new OrderInfo();
                    orderInfo1.setOrderTime(new Date());
                    orderInfo1.setId(orderInfo.getId());
                    orderInfoFeign.update(orderInfo1.getId(), orderInfo1);
                }
                return orderInfo;
            }

        }

    }


    /**
     * fail 流程继续往下跑
     * success 直接返回
     * hold 中断
     *
     * @param orderInfo
     * @param pledgeConfigList
     * @return
     */
    private FlowListenerParam beforeCheck(OrderInfoExt orderInfo, List<PledgeConfigResponse> pledgeConfigList, List<OrderInfoItem> orderInfoItemList) {

        AtomicBoolean whiteOrderFlag = new AtomicBoolean(true);
        if (OrderType.ADO.getKey().equals(orderInfo.getOrderType()) || OrderType.TFO.getKey().equals(orderInfo.getOrderType()) || OrderType.TF.getKey().equals(orderInfo.getOrderType())) {

            List<String> itemCodeList = orderInfoItemList.stream().map(OrderInfoItem::getItemCode).collect(Collectors.toList());
            List<String> containCodeList = new ArrayList<>();
            for (int i = 0; i < pledgeConfigList.size(); i++) {

                PledgeConfigResponse pledgeConfig = pledgeConfigList.get(i);
                //查询质押 范围
                PledgeRangRequest pledgeRangRequest = new PledgeRangRequest();
                pledgeRangRequest.setOrderNo(pledgeConfig.getOrderNo());
                if ("1".equals(pledgeConfig.getRange())) {
                    pledgeRangRequest.setWhCode(orderInfo.getWhCode());
                } else {
                    pledgeRangRequest.setSiteCode(orderInfo.getSiteCode());
                }
                if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
                    pledgeRangRequest.setCustomerCode(orderInfo.getTargetCustomerCode());
                } else {
                    pledgeRangRequest.setCustomerCode(orderInfo.getCustomerCode());
                }
                JsonResponse<List<PledgeRangResponse>> pledgeRangResp = centerInvServiceImpl.pledgeRang(pledgeRangRequest);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeRangResp.getCode()) || null == pledgeRangResp.getData()) {
                    log.info("质押范围 查询接口失败");
                    return FlowListenerParam.hold(ExceptionType.PLEDGE_RANG_QUERY_ERROR);
                }
                List<PledgeRangResponse> pledgeRangList = pledgeRangResp.getData();

                if (null == pledgeRangList || pledgeRangList.isEmpty()) {
                    log.info("质押范围 为空 pledgeRangRequest = {}", JSON.toJSONString(pledgeRangRequest));
                    whiteOrderFlag.set(false);
                    break;
                }
                //商品编码


                for (int j = 0; j < pledgeRangList.size(); j++) {
//
//                    if (pledgeRangList.size() > 1) {
//                        log.info("质押范围 查询接口失败，存在多个质押范围");
//                        return FlowListenerParam.fail(ExceptionType.PLEDGE_RANG_QUERY_ERROR);
//                    }
                    PledgeRangResponse pledgeRangResponse = pledgeRangList.get(j);
                    Integer adOnOff = pledgeRangResponse.getAdOnOff();

                    //判断有没有调账开启
                    if (CommonEnum.Y.getValue().equals(adOnOff)) {
                        ConfirmOrderListRequest confirmOrderListRequest = new ConfirmOrderListRequest();
                        confirmOrderListRequest.setItemCodeList(itemCodeList);
                        confirmOrderListRequest.setPledgeNo(pledgeConfig.getOrderNo());
                        confirmOrderListRequest.setCustomersCode(orderInfo.getCustomerCode());
                        JsonResponse<List<ConfirmOrderDto>> containResult = centerInvServiceImpl.confirmOrderList(confirmOrderListRequest);
                        if (BaseCodeEnum.SUCCESS.getCode().equals(containResult.getCode())) {
                            if (null != containResult.getData()) {
                                //如果返回值 不为 null ，同时 是通过的
//                               if(true == whiteOrderFlag.get()) {
//                                   whiteOrderFlag.set(true);
//                               }
                                containCodeList.addAll(containResult.getData().stream().map(ConfirmOrderDto::getItemCode).collect(Collectors.toList()));
//                               if(isEquals(itemCodeList,containCodeList)){
//                                   return FlowListenerParam.success("白名单配置 ，放行");
//                               }
                            }
                        } else {
                            log.warn("获取商品列表失败。");
                            return FlowListenerParam.fail("获取商品列表失败。");
                        }
                        //如果返回值 不为 null ，同时 是通过的
//                        whiteOrderFlag.set(true);
                    } else {
                        whiteOrderFlag.set(false);
//                        break;
                    }
                }
//                if(false == whiteOrderFlag.get()){
//                    break;
//                }
            }


            if (whiteOrderFlag.get() == true) {
                log.info("白名单配置 ， 放行");
                return FlowListenerParam.success("白名单配置 ，放行");
            } else {
                if (isEquals(itemCodeList, containCodeList)) {
                    return FlowListenerParam.success("白名单配置 ，放行");
                } else {
                    return FlowListenerParam.fail("白名单配置失败");
                }
            }
            //判断查询出来的订单商品，是否都在质押配置里面


        } else if (OrderType.AO.getKey().equals(orderInfo.getOrderType())) {
            //查询是不是白名单 配置
            JsonResponse<Boolean> whiteOrder =
                isWhiteOrder(orderInfo.getCustomerOrderNo(), pledgeConfigList);
            if (null != whiteOrder && true == whiteOrder.data) {

                return FlowListenerParam.success("白名单配置 ，放行");
            }
            return FlowListenerParam.fail("白名单配置 ，放行");
        }
        return FlowListenerParam.fail("继续质押检查");
    }

    /**
     * 判断两个数组是否相等
     *
     * @param list1
     * @param list2
     * @return
     */
    protected static boolean isEquals(List<String> list1, List<String> list2) {
        if (null != list1 && null != list2) {
            if (list1.containsAll(list2) && list2.containsAll(list1)) {
                return true;
            }
            return false;
        }
        return true;
    }


    /**
     * 代理商出库分仓审核
     * A：在途总值； B：库存总值； C：到款余额； D：订单预控金额； E：库存质押金额； F：本次出库订单总额； G：已放行金额
     * A+B-E > F,可放行，放行增加已放行金额（增加质押操作记录，质押订单监控表增加记录）；< F 时，继续；
     *
     * @param orderInfo
     * @param pledgeConfig
     * @return
     */
    private FlowListenerParam nineStep(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, List<PledgeHisAddRequest> pledgeHisAddList, List<PledgeInfo> pledgeInfoList, List<OrderInfoItem> orderInfoItemList, List<PledgeSplitHis> pledgeSplitHisList) {


        PledgeAmountRequst pledgeAmountRequst = new PledgeAmountRequst();
        pledgeAmountRequst.setOrderNo(pledgeConfig.getOrderNo());

        //查询质押 范围
        PledgeRangRequest pledgeRangRequest = new PledgeRangRequest();
        pledgeRangRequest.setOrderNo(pledgeConfig.getOrderNo());
        if ("1".equals(pledgeConfig.getRange())) {
            pledgeRangRequest.setWhCode(orderInfo.getWhCode());
        } else {
            pledgeRangRequest.setSiteCode(orderInfo.getSiteCode());
        }
        boolean isShare = OrderType.RI.getKey().equals(orderInfo.getOrderType()) && JoinType.SHARE.getKey().equals(orderInfo.getJoinType());
        boolean cimsFlag = SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem());
        if (cimsFlag) {
            pledgeRangRequest.setCustomerCode(orderInfo.getTargetCustomerCode());
        } else {
            pledgeRangRequest.setCustomerCode(orderInfo.getCustomerCode());
        }
        JsonResponse<List<PledgeRangResponse>> pledgeRangResp = centerInvServiceImpl.pledgeRang(pledgeRangRequest);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeRangResp.getCode()) || null == pledgeRangResp.getData()) {
            log.info("质押范围 查询接口失败");
            return FlowListenerParam.hold(ExceptionType.PLEDGE_RANG_QUERY_ERROR);
        }
        List<PledgeRangResponse> pledgeRangList = pledgeRangResp.getData();
        //如果质押范围没有数据，直接通过
        if (pledgeRangList.isEmpty()) {
            return FlowListenerParam.success("该订单不在质押范围内");
        }
        if (pledgeRangList.size() > 1) {
            log.info("质押范围 查询接口失败，存在多个质押范围");
            return FlowListenerParam.fail(ExceptionType.PLEDGE_RANG_QUERY_ERROR);
        }
        PledgeRangResponse pledgeRangResponse = pledgeRangList.get(0);
        Integer adOnOff = pledgeRangResponse.getAdOnOff();


        boolean isAnnto = isAnntoWareHouse(orderInfo, pledgeConfig);
        // 代理商出库调拨到安得仓，同时判断是否启动允许调拨 ，是否是安得仓的，放行
        if (CommonEnum.Y.getKey().equals(orderInfo.getUpstreamDocType()) && CommonEnum.Y.getValue().equals(adOnOff) && isAnnto) {
            //  TODO 需要想下如果是入安得仓的需要怎么处理
            return FlowListenerParam.success("出库调拨到安得仓，放行");
        }


        JsonResponse<BigDecimal> stockTotalAmoutResp = centerInvServiceImpl.getStockTotalAmout(pledgeAmountRequst);
        log.info(" 获取库存总值 请求结果 {}", JSON.toJSONString(stockTotalAmoutResp));
        if (!BaseCodeEnum.SUCCESS.getCode().equals(stockTotalAmoutResp.getCode()) || null == stockTotalAmoutResp.getData()) {
            log.info("库存总值 查询接口失败");
            return FlowListenerParam.hold(ExceptionType.PLEDGE_STOCK_TOTAL_AMOUT_QUERY_ERROR);
        }


        /**
         * B 库存总值
         */
        BigDecimal stockTotalAmout = stockTotalAmoutResp.getData();
        /**
         * E 库存质押金额
         */
        BigDecimal totalAmount = validateBigDecimal(pledgeConfig.getTotalAmount());


        /**
         * A 在途金额
         */
        BigDecimal onwayAmount = validateBigDecimal(pledgeConfig.getOnwayAmount());


        //本次订单总额
        BigDecimal orderAmount = getAgentPledgeOrderAmount(orderInfo, pledgeConfig, true, orderInfoItemList);
        //如果，这个质押单 娶不到介个 ，不用操作该记录
        if (orderAmount.compareTo(BigDecimal.ZERO) == 0) {
            return FlowListenerParam.success("订单金额 为0 ，放行");

        }
        BigDecimal temp = onwayAmount.add(stockTotalAmout).subtract(totalAmount);

        //查询余额失败
        BigDecimal balncAmount = getCustBalance(pledgeConfig);


//        //如果是入安得仓的，增加本地的质押
//        if (isAnnto){
//
//            //添加质押记录表
//            PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
//            pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));
//            pledgeConfigModReq.setGoneAmount(validateBigDecimal(pledgeConfig.getGoneAmount()));
//            pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));
//            pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
//            pledgeConfigModReq.setBillcontrolAmount(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
//            pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
//            pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));
//            pledgeConfigModReq.setTotalAmount(validateBigDecimal(pledgeConfig.getTotalAmount()));
//            pledgeConfigModReq.setJobId(pledgeConfig.getJobId());
//            pledgeConfigModReq.setOrderNo(orderInfo.getOrderNo());
//            PledgeInfo pledgeInfo = addLocalPledgeInfo(orderInfo, pledgeConfig, isAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE);
//            pledgeInfoList.add(pledgeInfo);
//            return FlowListenerParam.success("质押成功");
//        }

        // 使用了可用库存出库（增加分仓记录 ，保存在城配数据库）
        if (temp.compareTo(BigDecimal.ZERO) >= 0) {
            //保存分仓历史记录
            PledgeSplitHis pledgeSplitHis = getPledgeSplitHis(orderInfo, pledgeConfig, stockTotalAmout, orderAmount, balncAmount);
            pledgeSplitHisList.add(pledgeSplitHis);

            //添加质押记录表
            PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
            pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));
            pledgeConfigModReq.setGoneAmount(validateBigDecimal(pledgeConfig.getGoneAmount()));
            pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));
            pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
            pledgeConfigModReq.setBillcontrolAmount(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
            pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
            pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));
            pledgeConfigModReq.setTotalAmount(validateBigDecimal(pledgeConfig.getTotalAmount()));
            pledgeConfigModReq.setJobId(pledgeConfig.getJobId());
            pledgeConfigModReq.setOrderNo(orderInfo.getOrderNo());

            PledgeHisAddRequest hisAddReq = getPledgeHisAddReq(pledgeConfig, pledgeConfigModReq);

            EoWorkPledgeSplit eoWorkPledgeSplit = new EoWorkPledgeSplit();
            BeanUtils.copyProperties(pledgeSplitHis, eoWorkPledgeSplit);
            eoWorkPledgeSplit.setCreator("140");
            eoWorkPledgeSplit.setCreateTime(new Date());
            eoWorkPledgeSplit.setModifier("140");
            eoWorkPledgeSplit.setModifyTime(new Date());


            hisAddReq.setSplitHis(eoWorkPledgeSplit);

            //添加历史记录
            pledgeHisAddList.add(hisAddReq);

            PledgeInfo pledgeInfo = addLocalPledgeInfo(orderInfo, pledgeConfig, isAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE);
            pledgeInfoList.add(pledgeInfo);
//            JsonResponse<Boolean> jsonResponse = new JsonResponse();
//            jsonResponse.setMsg(BaseCodeEnum.SUCCESS.getMsg());
//            jsonResponse.setCode(BaseCodeEnum.SUCCESS.getCode());
//            jsonResponse.setData(true);
//            return jsonResponse;
            return FlowListenerParam.success("质押成功");

        }

        //判断是否可以转回 判断是否余额使用 是否订单转换
        boolean flag = null != pledgeConfig.getBalncAmountUsed() && pledgeConfig.getBalncAmountUsed().intValue() == 1 && null != pledgeConfig.getOrderAmountChg() && pledgeConfig.getOrderAmountChg() == 1;
//步骤10
        if (flag) {
//A+B+C-D-E-G > F ，可放行（增加质押操作记录，质押订单监控表增加记录）

            // 改为 A+B+C-D-E-G > 0 ，分仓的时候已经预占库存，可放行（增加质押操作记录，质押订单监控表增加记录）


            BigDecimal transAmout = stockTotalAmout.add(validateBigDecimal(pledgeConfig.getOnwayAmount())).add(balncAmount)
                .subtract(validateBigDecimal(pledgeConfig.getBillcontrolAmount())).subtract(validateBigDecimal(pledgeConfig.getTotalAmount())).subtract(validateBigDecimal(pledgeConfig.getGoneAmount()));


            if (transAmout.compareTo(BigDecimal.ZERO) >= 0) {

                //可以质权转移 返回true

                PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
                BigDecimal goneAmount = validateBigDecimal(pledgeConfig.getGoneAmount());
                pledgeConfigModReq.setGoneAmount(goneAmount);
                pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));

                pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
                pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));


                BigDecimal trancAmount = getCcsTrancAmount(pledgeConfig, stockTotalAmout, orderAmount);
                BigDecimal billcontrolAmount = validateBigDecimal(pledgeConfig.getBillcontrolAmount()).add(trancAmount).setScale(0, BigDecimal.ROUND_UP);
                pledgeConfigModReq.setBillcontrolAmount(billcontrolAmount);
                pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));


                BigDecimal totalAmountTemp = validateBigDecimal(pledgeConfig.getTotalAmount()).subtract(trancAmount).setScale(0, BigDecimal.ROUND_DOWN);
                if (totalAmountTemp.compareTo(BigDecimal.ZERO) < 0) {
                    totalAmountTemp = BigDecimal.ZERO;
//                    pledgeConfigModReq.setBillcontrolAmount(BigDecimal.ZERO);
//                    pledgeConfigModReq.setBillcontrolAmount(validateBigDecimal(pledgeConfig.getBillcontrolAmount()).add(validateBigDecimal(pledgeConfig.getTotalAmount())).setScale(0, BigDecimal.ROUND_UP));
                }
                pledgeConfigModReq.setTotalAmount(totalAmountTemp);
                pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));
                pledgeConfigModReq.setJobId(pledgeConfig.getJobId());
                pledgeConfigModReq.setOrderNo(orderInfo.getOrderNo());


                PledgeHisAddRequest hisAddReq = getPledgeHisAddReq(pledgeConfig, pledgeConfigModReq);
                //增加已放行金额（质押操作记录，质押订单监控表各增加一条记录
                CdWhPledgeAmount pledgeAmount = getPledgeAmoutHistory(pledgeConfig, pledgeConfigModReq, orderInfo, orderAmount, balncAmount);
                pledgeAmount.setActionType(PledgeActionTypeEnum.PLEDGE_CUST_OUT.getKey());
                hisAddReq.setPledgeAmount(pledgeAmount);


                PledgeSplitHis pledgeSplitHis = getPledgeSplitHis(orderInfo, pledgeConfig, stockTotalAmout, orderAmount, balncAmount);
                pledgeSplitHisList.add(pledgeSplitHis);


                EoWorkPledgeSplit eoWorkPledgeSplit = new EoWorkPledgeSplit();
                pledgeSplitHis.setRealtobillcontrolAmount(trancAmount);
                BeanUtils.copyProperties(pledgeSplitHis, eoWorkPledgeSplit);
                eoWorkPledgeSplit.setCreator("140");
                eoWorkPledgeSplit.setCreateTime(new Date());
                eoWorkPledgeSplit.setModifier("140");
                eoWorkPledgeSplit.setModifyTime(new Date());

                hisAddReq.setSplitHis(eoWorkPledgeSplit);

                //添加历史记录
                pledgeHisAddList.add(hisAddReq);

                PledgeInfo pledgeInfo = addLocalPledgeInfo(orderInfo, pledgeConfig, isAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE);
                pledgeInfoList.add(pledgeInfo);

                return FlowListenerParam.success("库存置换成功");
            }
        }

//        JsonResponse<Boolean> jsonResponse = new JsonResponse();
//        jsonResponse.setMsg(OrderConvergedReturnCode.PLEDGE_VALIDATE_ERROR.getMsg());
//        jsonResponse.setCode(OrderConvergedReturnCode.PLEDGE_VALIDATE_ERROR.getCode());
//        jsonResponse.setData(false);
//        return jsonResponse;
//

        boolean cancel = false;


        //添加一条  pledgeinfo的失败记录

        List<PledgeInfo> pledgeInfoListTemp = new ArrayList<>();

        PledgeInfo pledgeInfo = new PledgeInfo();

        pledgeInfo.setOrderNo(orderInfo.getOrderNo());
        pledgeInfo.setPledgeCustCode(orderInfo.getCustomerCode());


        pledgeInfo.setPledgeFlag(CommonEnum.Y.getValue());
        pledgeInfo.setPledgeStatus(PledgeStatusEnum.CHECK.getKey());

        pledgeInfo.setFailFlag(PledgeFailEnum.BUSINESS_FAIL.getKey());
        pledgeInfo.setParentOrderNo(orderInfo.getParentOrderNo());
        pledgeInfoListTemp.add(pledgeInfo);
        //增加一条记录
        pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoListTemp);
        
        // 2024年11月1日18:06:13 高露：https://devsecops.midea.com/agile/workbench/work/pending/?systemId=ITS000000873&teamId=ID0000000410&demandId=AAEC057511&itemType=3&zone=yellow
        //无忧只有借款和其他SHT审核失败都会取消订单，所以无需调用下面的取消分仓方法，只需要报错即可
        if (businessHelper.isWFR_PBO_Order(orderInfo) || ProjectClassifyEnum.isSHT(orderInfo.getProjectClassify())) {
            return FlowListenerParam.fail(ExceptionType.PLEDGE_VALIDATE_ERROR);
        }
        
        //取消库存
//        try {
//            cancel = separateWarehouseService.cancel(Arrays.asList(orderInfo.getOrderNo()));
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            log.error("分仓取消失败 {}", orderInfo.getOrderNo());
//            return FlowListenerParam.hold(ExceptionType.PLEDGE_SEPARATE_CANCLE_ERROR);
//        }
//        if (!cancel) {
//            log.error("分仓取消失败 {}", orderInfo.getOrderNo());
//            return FlowListenerParam.hold(ExceptionType.PLEDGE_SEPARATE_CANCLE_ERROR);
//        }

        //修改父单，
        updateCustomerOrderStatus(orderInfo, OrderStatus.AUDIT_FAILE.getKey());
        return FlowListenerParam.fail(ExceptionType.PLEDGE_VALIDATE_ERROR);

    }


    private JsonResponse separateWarehouseCancel(OrderInfo orderInfo, List<OrderInfoItem> orderInfoItemList) {
        OrderInfoExt orderInfoExt = new OrderInfoExt();
        BeanUtils.copyProperties(orderInfo, orderInfoExt);
        orderInfoExt.setOrderInfoItems(orderInfoItemList);

        //取消库存
        JsonResponse reuslt = centerInvService.cancelOrder(orderInfoExt, CenterInvType.CANCEL_PLAN_OMS_OUT.getKey());
        return reuslt;
    }

    private PledgeSplitHis getPledgeSplitHis(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, BigDecimal stockTotalAmout, BigDecimal orderAmount, BigDecimal balncAmount) {
        //增加分仓记录

        PledgeSplitHis pledgeSplitHis = new PledgeSplitHis();
        pledgeSplitHis.setBalncAmount(balncAmount);
        pledgeSplitHis.setBank(pledgeConfig.getBank());
        pledgeSplitHis.setBillcontrolAmount(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
        pledgeSplitHis.setCustomerCode(orderInfo.getCustomerCode());
        EbCustomer ebCustomer = ebCustomerManager.getEbCustomerCache(orderInfo.getCustomerCode());

        if (null != ebCustomer) {
            pledgeSplitHis.setCustomerName(ebCustomer.getEbcuNameCn());
        }
        pledgeSplitHis.setGoneAmount(validateBigDecimal(pledgeConfig.getGoneAmount()));
        pledgeSplitHis.setLogisticNos(orderInfo.getCustomerOrderNo());
        pledgeSplitHis.setPledgeMode(pledgeConfig.getPledgeMode());
        pledgeSplitHis.setPledgeNo(pledgeConfig.getOrderNo());
        pledgeSplitHis.setRealcontrolAmount(validateBigDecimal(pledgeConfig.getTotalAmount()));
        if (null != pledgeConfig.getPledgeType()) {
            pledgeSplitHis.setPledgeType(pledgeConfig.getPledgeType().intValue());
        }
        pledgeSplitHis.setRealtobillcontrolAmount(BigDecimal.ZERO);
        pledgeSplitHis.setRecStatus(CommonEnum.NO.getValue());
        pledgeSplitHis.setRecVer(BigDecimal.ZERO);
        pledgeSplitHis.setSplitAmount(orderAmount);
        pledgeSplitHis.setSplitStatus(CommonEnum.ENABLE.getValue());
        pledgeSplitHis.setStockAmount(stockTotalAmout);
        BigDecimal usableAmount = stockTotalAmout.subtract(validateBigDecimal(pledgeConfig.getTotalAmount())).add(validateBigDecimal(pledgeConfig.getOnwayAmount()));
        pledgeSplitHis.setUsableAmount(usableAmount);
        pledgeSplitHis.setWorkOrderNos(orderInfo.getOrderNo());

        return pledgeSplitHis;


//        JsonResponse pledgeSplitHisResult = pledgeSplitHisFeign.create(pledgeSplitHis);
//
//        if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeSplitHisResult.getCode())) {
//            JsonResponse jsonResponse = new JsonResponse();
//            jsonResponse.setData(false);
//            jsonResponse.setMsg(OrderConvergedReturnCode.PLEDGE_SPLIT_HIS_SAVE_ERROR.getMsg());
//            jsonResponse.setCode(OrderConvergedReturnCode.PLEDGE_SPLIT_HIS_SAVE_ERROR.getCode());
//            return jsonResponse;
//        }
//        return null;
    }


    private BigDecimal validateBigDecimal(Double value) {
        if (null == value) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(value);
    }

    /**
     * CCS  的库存转移
     *
     * @param pledgeConfig
     * @param stockAmount
     * @return
     */
    private BigDecimal getCcsTrancAmount(PledgeConfigResponse pledgeConfig, BigDecimal stockAmount, BigDecimal orderAmount) {


        BigDecimal temp = stockAmount.add(validateBigDecimal(pledgeConfig.getOnwayAmount())).subtract(validateBigDecimal(pledgeConfig.getTotalAmount())).add(orderAmount);
        if (temp.compareTo(BigDecimal.ZERO) < 0) {
            temp = BigDecimal.ZERO;
        }
        BigDecimal temp1;
        if (orderAmount.compareTo(validateBigDecimal(pledgeConfig.getTotalAmount())) < 0) {
            temp1 = orderAmount;
        } else {
            temp1 = validateBigDecimal(pledgeConfig.getTotalAmount());
        }
        temp = temp.subtract(temp1).negate();
        if (temp.compareTo(BigDecimal.ZERO) < 0) {
            temp = BigDecimal.ZERO;
        }

        return temp;

    }


    /**
     * cims 发 代理商的客户场景  按道理 ，只有一个 质押单  步骤2 到步骤 7
     */
    private OrderInfoExt cimsToAgentAoutCheck(OrderInfoExt orderInfo) {

        //查询事业部下的所有质押配置信息
        PledgeConfigRequest pledgeConfigRequest = new PledgeConfigRequest();
        pledgeConfigRequest.setCustomersCode(orderInfo.getTargetCustomerCode());
        pledgeConfigRequest.setBuCode(orderInfo.getCustomerCode());
        JsonResponse<List<PledgeConfigResponse>> proxyConfigListResp = centerInvServiceImpl.getPledgeConfig(pledgeConfigRequest);

        log.info("查询质押配置结果  proxyConfigListResp = {}", JSON.toJSONString(proxyConfigListResp));
        //如果和BOP通讯不成功 ，返回错误
        if (!BaseCodeEnum.SUCCESS.getCode().equals(proxyConfigListResp.getCode())) {
            //return FlowListenerParam.fail(ExceptionType.PLEDGE_CONFIG_QUERY_ERROR);
            throw BusinessException.fail(ExceptionType.PLEDGE_CONFIG_QUERY_ERROR.getValue());
        }
        //该融资客户不在质押配置表里面，放行
        List<PledgeConfigResponse> pledgeConfigList = proxyConfigListResp.getData();

        log.info("获取订单商品明细");
        //获取所有的订单商品
        List<OrderInfoItem> orderInfoItemList = orderInfo.getOrderInfoItems();//getOrderInfoItems(orderInfo);
        //对订单类型的预先校验，判断是不是白名单或者开启调账功能 ，如果返回fail 的继续质押校验，如果返回其他的类型 ，直接返回结果
        FlowListenerParam resultPre = beforeCheck(orderInfo, pledgeConfigList, orderInfoItemList);
        if (!FlowStatus.FAILD.equals(resultPre.getFlowStatus())) {
            //return resultPre;
            if (FlowStatus.SUCCESS.equals(resultPre.getFlowStatus())) {
                return orderInfo;
            } else {
                throw BusinessException.fail(resultPre.getErrorMsg());
            }
        }
        //如果没有找到质押配置表信息 ，返回成功 直接放行
        if (null == pledgeConfigList || pledgeConfigList.isEmpty()) {
            log.info("不在质押配置中，质押检查放行");

            // return FlowListenerParam.success("不在质押配置中，质押检查放行");
            return orderInfo;
        }


        AtomicBoolean whiteOrderFlag = new AtomicBoolean(false);

        //检查 质押订单是不是  白名单，如果都为白名单 ，放行

        log.info("白名单校验");
        pledgeConfigList.forEach(pledgeConfig -> {
            JsonResponse<Boolean> whiteOrder = isWhiteOrder(orderInfo.getCustomerOrderNo(), pledgeConfigList);
            //如果返回值 不为 null ，同时 是通过的
            if (whiteOrder != null && BaseCodeEnum.SUCCESS.getCode().equals(whiteOrder.getCode())) {
                whiteOrderFlag.set(true);
            } else {
                whiteOrderFlag.set(false);
                return;
            }
        });


        //遍历质押单号 ，保存质押历史记录
        List<PledgeHisAddRequest> pledgeHisAddList = new ArrayList();

        List<PledgeInfo> pledgeInfoList = new ArrayList<>();

        log.info("白名单结果 whiteOrderFlag = ", whiteOrderFlag.get());
        //白名单校验 如果白名单 校验通过 ，增加已放行金额
        if (whiteOrderFlag.get() == true) {
            //添加 pledgeInfo  的记录 为后面出库 和财务入库 做准备

            JsonResponse<Boolean> result = whitePledgeConfig(orderInfo, pledgeConfigList, orderInfoItemList);

            if (!BaseCodeEnum.SUCCESS.getCode().equals(result.getCode()) && false == result.getData()) {
                //return FlowListenerParam.fail(result.getMsg());
                throw BusinessException.fail(result.getMsg());
            } else {
                //添加已放行金额

                pledgeConfigList.forEach(pledgeConfig -> {
                    boolean isAnnto = isAnntoWareHouse(orderInfo, pledgeConfig);
                    BigDecimal orderAmount = getOrderAmount(orderInfo, pledgeConfig, isAnnto, orderInfoItemList);
                    BigDecimal blcAmount = getCustBalance(pledgeConfig);
                    addGoneAmountPledgeConfig(orderInfo, pledgeConfig, pledgeHisAddList, pledgeInfoList, isAnnto, orderAmount, blcAmount);
                });


                JsonResponse<Integer> pledgeHisResult = centerInvServiceImpl.pledgeHisAdd(pledgeHisAddList);
                log.info("白名单保存 结果 pledgeHisResult = {}", JSON.toJSONString(pledgeHisResult));
                if (BaseCodeEnum.SUCCESS.getCode().equals(pledgeHisResult.getCode()) && pledgeHisResult.getData() > 0) {
                    JsonResponse<Integer> pledgeInfoBatchResult = pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoList);
                    log.info("批量保存 质押信息 结果 {}", JSON.toJSONString(pledgeInfoBatchResult));
                    // return FlowListenerParam.success("白名单 通过质押校验");
                    return orderInfo;
                } else {
                    //return FlowListenerParam.hold("保存质押失败 ，不放行");
                    throw BusinessException.fail("保存质押失败 ，不放行");
                }

            }
        }


        //一个订单号  ，可能 多个质押配置 ，例如长期 ，一个订单  ，可能有冰箱的质押 ，又有可能空调的质押
        for (int i = 0; i < pledgeConfigList.size(); i++) {
            PledgeConfigResponse pledgeConfig = pledgeConfigList.get(i);
            //遍历所有的融资客户发货
            FlowListenerParam result = threeStep(orderInfo, pledgeConfig, pledgeHisAddList, orderInfoItemList, pledgeInfoList);
            //如果不成功的话，直接返回结果

            if (!FlowStatus.SUCCESS.equals(result.getFlowStatus())) {
                log.info(" 步骤3 FlowListenerParam = ", JSON.toJSONString(result));
                //return result;
                throw BusinessException.fail(result.getErrorMsg());
            }

        }

        if (pledgeHisAddList.isEmpty()) {
            //TODO  需要保证业务上的一致性 采用其他方式
            JsonResponse<Integer> pledgeInfoBatchResult = pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoList);
            log.info("批量保存 质押信息 结果 {}", JSON.toJSONString(pledgeInfoBatchResult));

            // return FlowListenerParam.success("");
            return orderInfo;
        }
        log.info("质押保存 请求为 pledgeHisAddList = {}", JSON.toJSONString(pledgeHisAddList));
        JsonResponse<Integer> pledgeHisResult = centerInvServiceImpl.pledgeHisAdd(pledgeHisAddList);
        if (BaseCodeEnum.SUCCESS.getCode().equals(pledgeHisResult.getCode()) && pledgeHisResult.getData() > 0) {
            //TODO  需要保证业务上的一致性 采用其他方式
            JsonResponse<Integer> pledgeInfoBatchResult = pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoList);
            log.info("批量保存 质押信息 结果 {}", JSON.toJSONString(pledgeInfoBatchResult));

            //return FlowListenerParam.success("");
            return orderInfo;
        } else {
            log.info("质押记录 保存失败 pledgeHisResult = {}", JSON.toJSONString(pledgeHisResult));
            //return FlowListenerParam.hold("保存质押失败 ，不放行");
            throw BusinessException.fail("保存质押失败 ，不放行");
        }
    }

    private JsonResponse<Boolean> whitePledgeConfig(OrderInfo orderInfo, List<PledgeConfigResponse> pledgeConfigList, List<OrderInfoItem> orderInfoItemList) {
        List<PledgeInfo> pledgeInfoList = new ArrayList<>();

        for (int i = 0; i < pledgeConfigList.size(); i++) {
            PledgeConfigResponse pledgeConfig = pledgeConfigList.get(i);
            boolean iaAnnto = isAnntoWareHouse(orderInfo, pledgeConfig);
            BigDecimal orderAmount = getPledgeOrderAmount(orderInfo, pledgeConfig, false, iaAnnto, orderInfoItemList);

            PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
            pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));
            pledgeConfigModReq.setGoneAmount(validateBigDecimal(pledgeConfig.getGoneAmount()));

            pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
            pledgeConfigModReq.setBillcontrolAmount(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));


            pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));
            pledgeConfigModReq.setTotalAmount(validateBigDecimal(pledgeConfig.getTotalAmount()));

            pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));
            pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
            pledgeConfigModReq.setOrderNo(pledgeConfig.getOrderNo());
            pledgeConfigModReq.setJobId(pledgeConfig.getJobId());

            PledgeInfo pledgeInfo = addLocalPledgeInfo(orderInfo, pledgeConfig, iaAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE);
            pledgeInfo.setPledgeType(pledgeConfig.getPledgeType().intValue());
            pledgeInfo.setPledgeFlag(CommonEnum.Y.getValue());

            pledgeInfoList.add(pledgeInfo);

        }


        //保存相应的记录
        JsonResponse<Integer> resutl = pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoList);

        if (!BaseCodeEnum.SUCCESS.getCode().equals(resutl.getCode()) || resutl.getData() < 0) {
            JsonResponse jsonResponse = new JsonResponse();
            jsonResponse.setMsg("保存质押信息失败");
            jsonResponse.setData(false);
            return jsonResponse;
        }
        JsonResponse jsonResponse = new JsonResponse();
        jsonResponse.setMsg("白名单放行通过");
        jsonResponse.setData(true);
        return jsonResponse;
    }


    /**
     * 获取该订单下的所有的 订单明细
     *
     * @param orderInfo
     * @return
     */
    private List<OrderInfoItem> getOrderInfoItems(OrderInfo orderInfo) {
        JsonResponse<List<OrderInfoItem>> orderItemResp = orderInfoItemFeign.getOrderItem(orderInfo.getOrderNo());
        if (!BaseCodeEnum.SUCCESS.getCode().equals(orderItemResp.getCode())) {
            //获取订单商品明细
            throw new BusinessException(OrderConvergedReturnCode.ORDER_INFO_ITEM_QUERY_ERROR.getCode(), OrderConvergedReturnCode.ORDER_INFO_ITEM_QUERY_ERROR.getMsg());
        }
        return orderItemResp.getData();

    }

    /**
     * 保存质押价格
     *
     * @param pledgeGoodsPriceList
     * @return
     */
    private boolean savePledgeGoodsPrice(List<PledgeGoodsPrice> pledgeGoodsPriceList) {
        JsonResponse<Integer> result = pledgeGoodsPriceFeign.batchSavePrice(pledgeGoodsPriceList);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return true;
    }

    /**
     * 第三个步骤
     *
     * @param orderInfo
     * @param pledgeConfig
     * @return
     */
    private FlowListenerParam threeStep(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, List<PledgeHisAddRequest> pledgeHisAddRequestList, List<OrderInfoItem> orderInfoItemList, List<PledgeInfo> pledgeInfoList) {

        log.info("开始步骤 3 ");
        //是否入安得仓判断  如果不是安得仓，校验订单价格继续往下走
        //步骤四
        boolean iaAnnto = isAnntoWareHouse(orderInfo, pledgeConfig);

        //查询质押 范围
        PledgeRangRequest pledgeRangRequest = new PledgeRangRequest();
        pledgeRangRequest.setOrderNo(pledgeConfig.getOrderNo());
        pledgeRangRequest.setSiteCode(orderInfo.getTargetSiteCode());
        if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
            pledgeRangRequest.setCustomerCode(orderInfo.getTargetCustomerCode());
        } else {
            pledgeRangRequest.setCustomerCode(orderInfo.getCustomerCode());
        }

        JsonResponse<List<PledgeRangResponse>> pledgeRangResp = centerInvServiceImpl.pledgeRang(pledgeRangRequest);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeRangResp.getCode())) {
            log.info("质押范围 查询接口失败");
            return FlowListenerParam.hold(ExceptionType.PLEDGE_RANG_QUERY_ERROR);
        }


        //CIMS  的单，发给安得仓  ，放行  ，在出库 的时候 ，做库存转移
        //如果是进去安得仓 ，同时 也在 质押范围内
        //添加 判断 ，收发货平台编码 是否一致  ，是 入安得仓，然后同时收发货平台编码一致的时候 ，才是安得仓  ,优先判断 自提类型的订单，判断收发货平台是否一致


        log.info("是否是入安得仓判断 {}", iaAnnto);

        if (iaAnnto) {

            BigDecimal orderAmount = getPledgeOrderAmount(orderInfo, pledgeConfig, false, iaAnnto, orderInfoItemList);
            log.info("订单为  orderAmount ={}", orderAmount);
            PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
            pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));
            pledgeConfigModReq.setGoneAmount(validateBigDecimal(pledgeConfig.getGoneAmount()));

            pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
            pledgeConfigModReq.setBillcontrolAmount(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));


            pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));
            pledgeConfigModReq.setTotalAmount(validateBigDecimal(pledgeConfig.getTotalAmount()));

            pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));
            pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
            pledgeConfigModReq.setOrderNo(pledgeConfig.getOrderNo());
            pledgeConfigModReq.setJobId(pledgeConfig.getJobId());

            //判断是否货权转移  ,取最少的金额 ,入安得仓的时候，直接放行了
            if (JoinType.TRANS_INV.getKey().equals(orderInfo.getJoinType())) {
                if (orderAmount.compareTo(validateBigDecimal(pledgeConfig.getBillcontrolAmount())) > 0) {
                    orderAmount = validateBigDecimal(pledgeConfig.getBillcontrolAmount());
                }
            }

            PledgeInfo pledgeInfo = addLocalPledgeInfo(orderInfo, pledgeConfig, iaAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE);
            pledgeInfo.setPledgeType(pledgeConfig.getPledgeType().intValue());
            pledgeInfoList.add(pledgeInfo);


            //如果是安得仓继续往下走

            return FlowListenerParam.success("安得仓  订单  ，质押检查放行");

        }


        //不入安得仓 ，继续往下走 获取该质押单下的订单金额
        BigDecimal orderAmount = getPledgeOrderAmount(orderInfo, pledgeConfig, false, false, orderInfoItemList);
        //查看该质押单下的  客户余额
        BigDecimal balanceAmount = getCustBalance(pledgeConfig);
        log.info("客户余额为 balanceAmount = {} ,订单预控金额为 {}", balanceAmount, pledgeConfig.getBillcontrolAmount());
        //步骤五 判断预控金额 订单预控金额是否大于0，等于0时，放行 , 在该仓库没有质押
        if (pledgeConfig.getBillcontrolAmount() == 0) {
            //增加已放行金额（质押操作记录，质押订单监控表各增加一条记录）
            addGoneAmountPledgeConfig(orderInfo, pledgeConfig, pledgeHisAddRequestList, pledgeInfoList, iaAnnto, orderAmount, balanceAmount);

            JsonResponse result = new JsonResponse();
            result.setData(true);

            return FlowListenerParam.success("预控金额 为0  ，放行");
        }


        //判断是否使用到款余额（balnc_amount_used:0-否，1-是）

        boolean balancAmountUsed = (null != pledgeConfig.getBalncAmountUsed() && pledgeConfig.getBalncAmountUsed().intValue() == 1);
        log.info("判断是否使用到款余额 balancAmountUsed = {}", balancAmountUsed);

        if (balancAmountUsed) {
            //使用到款余额(balnc_amount_used=1)
            //到款余额(查询CIMS)-订单预控金额(billcontrol_amount)-库存质押金额(total_amount)-已放行金额(gone_amount)大于本次出库订单总额，则放行，增加已放行金额（已放行金额=已放行金额+本次出库订单总额）（质押操作记录，质押订单监控表各增加一条记录）；同时，增加质押记录。
            //小于本次出库订单总额或者不使用到款余额时，继续；


            BigDecimal value = balanceAmount.subtract(validateBigDecimal(pledgeConfig.getBillcontrolAmount())).subtract(validateBigDecimal(pledgeConfig.getGoneAmount()));
            //减去本次出库订单总额
            log.info("转换价格 为 {}", value);
            if (value.compareTo(orderAmount) >= 0) {
                //增加已放行金额（已放行金额=已放行金额+本次出库订单总额）（质押操作记录，质押订单监控表各增加一条记录）；同时，增加质押记录

                addGoneAmountPledgeConfig(orderInfo, pledgeConfig, pledgeHisAddRequestList, pledgeInfoList, iaAnnto, orderAmount, balanceAmount);
                return FlowListenerParam.success("使用到款余额成功，");
            }
        }
        //步骤七
        /**
         * 使用库存转换（def2=1）:在途总值（onway_amount）+库存总值(实时计算)+到款余额（查询CIMS）-订单预控金额(billcontrol_amount)-库存质押金额(total_amount)-已放行金额(gone_amount)大于本次出库订单总额，已放行金额增加，订单预控金额减少、库存质押金额增加,G = G + F,D = D-(最小去0(相反数(最小取0(C-G-D)-取小(F,G)))),
         * E = E+(最小去0(相反数(最小取0(C-G-D)-取小(F,G))))，同时，增加质押记录。；
         * 若小于本次出库订单总额或不使用库存转换：在途总值+库存总值-库存质押金额  大于本次出库订单总额，则放行，增加已放行金额（质押操作记录，质押订单监控表各增加一条记录），否则，禁止出库；
         */


        //获取库存总值
        PledgeAmountRequst stockTotalAmoutRequst = new PledgeAmountRequst();

        stockTotalAmoutRequst.setOrderNo(pledgeConfig.getOrderNo());
        JsonResponse<BigDecimal> stockTotalAmoutRespone = centerInvServiceImpl.getStockTotalAmout(stockTotalAmoutRequst);

        if (!BaseCodeEnum.SUCCESS.getCode().equals(stockTotalAmoutRespone.getCode())) {

            return FlowListenerParam.hold(ExceptionType.PLEDGE_STOCK_TOTAL_AMOUT_QUERY_ERROR);
        }

        BigDecimal stockTotalAmout = stockTotalAmoutRespone.getData();

        log.info("是否开始 库存转换 , {}", CommonEnum.Y.getValue().toString().equals(pledgeConfig.getDef2()));
        if (CommonEnum.Y.getValue().toString().equals(pledgeConfig.getDef2())) {


            //如果使用库存转换 了，有使用到款余额
            log.info("库存转换 是否使用到款余额  balancAmountUsed= {}", balancAmountUsed);
            if (balancAmountUsed) {
                BigDecimal transAmout = stockTotalAmout.add(validateBigDecimal(pledgeConfig.getOnwayAmount())).add(balanceAmount)
                    .subtract(validateBigDecimal(pledgeConfig.getBillcontrolAmount())).subtract(validateBigDecimal(pledgeConfig.getTotalAmount())).subtract(validateBigDecimal(pledgeConfig.getGoneAmount()));

                log.info("pledgeConfig.getOnwayAmount() = {} , balanceAmount = {} ,pledgeConfig.getBillcontrolAmount() = {} ,pledgeConfig.getTotalAmount() = {} ,pledgeConfig.getGoneAmount() = {} ,transAmout = {}", pledgeConfig.getOnwayAmount(), balanceAmount, pledgeConfig.getBillcontrolAmount(), pledgeConfig.getTotalAmount(), pledgeConfig.getGoneAmount(), transAmout);
                //步骤7
                log.info("步骤7  转换 价额 {}", transAmout);
                if (transAmout.compareTo(orderAmount) >= 0) {

                    log.info("使用库存 置换金额 ");
                    //可以质权转移 返回true

                    PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
                    BigDecimal goneAmount = validateBigDecimal(pledgeConfig.getGoneAmount()).add(orderAmount);
                    pledgeConfigModReq.setGoneAmount(goneAmount);

                    pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));
                    pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
                    pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));

                    //需要转换的 金额
                    BigDecimal trancAmount = getTrancAmount(pledgeConfig, balanceAmount, orderAmount);
                    BigDecimal billcontrolAmount = validateBigDecimal(pledgeConfig.getBillcontrolAmount()).subtract(trancAmount).setScale(0, BigDecimal.ROUND_UP);
                    if (billcontrolAmount.compareTo(BigDecimal.ZERO) < 0) {
                        billcontrolAmount = BigDecimal.ZERO;
                    }
                    pledgeConfigModReq.setBillcontrolAmount(billcontrolAmount);
                    pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));

                    BigDecimal totalAmount = validateBigDecimal(pledgeConfig.getTotalAmount()).add(trancAmount).setScale(0, BigDecimal.ROUND_DOWN);
                    pledgeConfigModReq.setTotalAmount(totalAmount);
                    pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));

                    pledgeConfigModReq.setJobId(pledgeConfig.getJobId());
                    pledgeConfigModReq.setOrderNo(orderInfo.getOrderNo());


                    PledgeHisAddRequest hisAddReq = getPledgeHisAddReq(pledgeConfig, pledgeConfigModReq);
                    //增加已放行金额（质押操作记录，质押订单监控表各增加一条记录
                    CdWhPledgeAmount pledgeAmount = getPledgeAmoutHistory(pledgeConfig, pledgeConfigModReq, orderInfo, orderAmount, balanceAmount);
                    pledgeAmount.setActionType(PledgeActionTypeEnum.OUT_CHECK.getKey());
                    hisAddReq.setPledgeAmount(pledgeAmount);
                    //质押订单监控表
                    OrderPriceDto orderPriceDto = getOrderGoodAmount(orderInfo, false, pledgeConfig.getOrderNo(), iaAnnto);
                    PledgeMoni pledgemoni = getPledgeMoni(pledgeConfig, orderInfo, CommonEnum.NO.getValue().toString(), orderAmount.doubleValue(), orderPriceDto.getDefectGoodsAmount().doubleValue(), orderPriceDto.getQuanlityGoodAmount().doubleValue());

                    hisAddReq.setPledgeMoni(pledgemoni);

                    //增加已放行金额（质押操作记录，质押订单监控表各增加一条记录 )
                    pledgeInfoList.add(addLocalPledgeInfo(orderInfo, pledgeConfig, iaAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE));

                    pledgeHisAddRequestList.add(hisAddReq);


                    return FlowListenerParam.success("使用库存转换 成功");
                }
            } else {
                BigDecimal amount = validateBigDecimal(pledgeConfig.getOnwayAmount()).add(stockTotalAmout).subtract(validateBigDecimal(pledgeConfig.getTotalAmount()));

                if (amount.compareTo(orderAmount) > -1) {
                    PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
                    BigDecimal goneAmount;
                    if (DeliveryType.isZT(orderInfo.getDeliveryType())) {
                        goneAmount = validateBigDecimal(pledgeConfig.getGoneAmount());
                    } else {
                        goneAmount = validateBigDecimal(pledgeConfig.getGoneAmount()).add(orderAmount);
                    }
                    pledgeConfigModReq.setGoneAmount(goneAmount);

                    pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));
                    pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
                    pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));

                    //需要转换的 金额
                    BigDecimal trancAmount = getTrancAmount(pledgeConfig, balanceAmount, orderAmount);
                    BigDecimal billcontrolAmount = validateBigDecimal(pledgeConfig.getBillcontrolAmount()).subtract(trancAmount).setScale(0, BigDecimal.ROUND_UP);
                    if (billcontrolAmount.compareTo(BigDecimal.ZERO) < 0) {
                        billcontrolAmount = BigDecimal.ZERO;
                    }
                    pledgeConfigModReq.setBillcontrolAmount(billcontrolAmount);
                    pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));

                    BigDecimal totalAmount = validateBigDecimal(pledgeConfig.getTotalAmount()).add(trancAmount).setScale(0, BigDecimal.ROUND_DOWN);
                    pledgeConfigModReq.setTotalAmount(totalAmount);
                    pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));

                    pledgeConfigModReq.setJobId(pledgeConfig.getJobId());
                    pledgeConfigModReq.setOrderNo(orderInfo.getOrderNo());


                    PledgeHisAddRequest hisAddReq = getPledgeHisAddReq(pledgeConfig, pledgeConfigModReq);
                    //增加已放行金额（质押操作记录，质押订单监控表各增加一条记录
                    CdWhPledgeAmount pledgeAmount = getPledgeAmoutHistory(pledgeConfig, pledgeConfigModReq, orderInfo, orderAmount, balanceAmount);
                    pledgeAmount.setActionType(PledgeActionTypeEnum.OUT_CHECK.getKey());
                    hisAddReq.setPledgeAmount(pledgeAmount);
                    //质押订单监控表
                    OrderPriceDto orderPriceDto = getOrderGoodAmount(orderInfo, false, pledgeConfig.getOrderNo(), iaAnnto);
                    PledgeMoni pledgemoni = getPledgeMoni(pledgeConfig, orderInfo, CommonEnum.NO.getValue().toString(), orderAmount.doubleValue(), orderPriceDto.getDefectGoodsAmount().doubleValue(), orderPriceDto.getQuanlityGoodAmount().doubleValue());

                    hisAddReq.setPledgeMoni(pledgemoni);

                    //增加已放行金额（质押操作记录，质押订单监控表各增加一条记录 )
                    pledgeInfoList.add(addLocalPledgeInfo(orderInfo, pledgeConfig, iaAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE));

                    pledgeHisAddRequestList.add(hisAddReq);

                    return FlowListenerParam.success("使用库存转换 成功");

                }


                BigDecimal amountTemp = validateBigDecimal(pledgeConfig.getOnwayAmount()).add(stockTotalAmout).subtract(validateBigDecimal(pledgeConfig.getTotalAmount()));

                if (amountTemp.compareTo(orderAmount) > -1) {
                    addGoneAmountPledgeConfig(orderInfo, pledgeConfig, pledgeHisAddRequestList, pledgeInfoList, iaAnnto, orderAmount, balanceAmount);
                    return FlowListenerParam.success("使用到款余额成功，");
                }
            }


        }


//        List cancleList =  new ArrayList();
//        cancleList.add(orderInfo.getOrderNo());
//        JsonResponse result = separateWarehouseSearchFeign.separateWarehouseCancel(cancleList);

        boolean cancel = false;


        //添加一条  pledgeinfo的失败记录

        List<PledgeInfo> pledgeInfoListTemp = new ArrayList<>();

        PledgeInfo pledgeInfo = new PledgeInfo();

        pledgeInfo.setOrderNo(orderInfo.getOrderNo());
        pledgeInfo.setPledgeCustCode(orderInfo.getCustomerCode());


        pledgeInfo.setPledgeFlag(CommonEnum.Y.getValue());
        pledgeInfo.setPledgeStatus(PledgeStatusEnum.CHECK.getKey());

        pledgeInfo.setFailFlag(PledgeFailEnum.BUSINESS_FAIL.getKey());
        pledgeInfo.setParentOrderNo(orderInfo.getParentOrderNo());
        pledgeInfoListTemp.add(pledgeInfo);
        //增加一条记录
        pledgeInfoExtFeign.savePledgeInfoBatch(pledgeInfoListTemp);
        //取消库存
//        try {
//
//            cancel = separateWarehouseService.cancel(Arrays.asList(orderInfo.getOrderNo()));
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            log.error("分仓取消失败 {}", orderInfo.getOrderNo());
//            return FlowListenerParam.hold(ExceptionType.PLEDGE_SEPARATE_CANCLE_ERROR);
//        }
//        if (!cancel) {
//            log.error("分仓取消失败 {}", orderInfo.getOrderNo());
//            return FlowListenerParam.hold(ExceptionType.PLEDGE_SEPARATE_CANCLE_ERROR);
//        }

        updateCustomerOrderStatus(orderInfo, OrderStatus.AUDIT_FAILE.getKey());

        return FlowListenerParam.fail(ExceptionType.PLEDGE_VALIDATE_ERROR);
    }

    private void updateCustomerOrderStatus(OrderInfo orderInfo, Integer orderStatus) {
        log.info("修改父订单状态 ，子单={}，订单状态为{}", orderInfo.getOrderNo(), orderStatus);
        //修改父单，
        CustomerOrderInfo customerOrderInfo = lmpOrderFlowHelper.getCustomerOrderInfo(orderInfo.getParentOrderNo());
        customerOrderInfo.setOrderStatus(orderStatus);
        lmpOrderFlowHelper.updateCustomerOrderInfo(customerOrderInfo, "质押");
    }


    /**
     * 获取订单总额
     *
     * @param orderInfo
     * @return
     */
    private BigDecimal getAgentPledgeOrderAmount(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, Boolean isAgent, List<OrderInfoItem> orderInfoItems) {

        List requestList = new ArrayList();
        int pageSize = 100;

        List<PledgePriceResponse> priceList = new ArrayList();
        //获取该订单下的所有商品价格
        for (int i = 1; i <= orderInfoItems.size(); i++) {
            OrderInfoItem orderInfoItem = orderInfoItems.get(i - 1);
            PledgePriceRequest temp = new PledgePriceRequest();
            temp.setGoodsCode(orderInfoItem.getItemCode());
            temp.setOrderNo(pledgeConfig.getOrderNo());
            temp.setSiteCode(orderInfo.getSiteCode());
            temp.setIsAgent(isAgent);
            if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
                temp.setCustomerCode(orderInfo.getTargetCustomerCode());
            } else {
                temp.setCustomerCode(orderInfo.getCustomerCode());
            }
            requestList.add(temp);
            if (i % pageSize == 0 || i == orderInfoItems.size()) {
                JsonResponse<List<PledgePriceResponse>> result = centerInvServiceImpl.getPledgePrice(requestList);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(result.getCode())) {
                    log.warn("获取质押价格失败, requestList = {}", JSON.toJSONString(requestList));
                    throw new BusinessException(OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getCode(), result.getMsg());
                }
                if (null != result.getData()) {
                    //获取所有的价格列表
                    priceList.addAll(result.getData());
                }
            }
        }


        //计算订单价格
        Map<String, PledgePriceResponse> map = new HashMap();
        priceList.forEach(value -> {
//            if (isAnno) {
//                map.put(value.getGoodsCode() + value.getSiteCode() + value.getParentOrderCode(), value);
//            } else {
            map.put(value.getGoodsCode() + value.getParentOrderCode(), value);
//            }
        });

        BigDecimal orderAmount = new BigDecimal(0);

        List<PledgeGoodsPrice> pledgeGoodsPriceList = new ArrayList<>();

        for (OrderInfoItem value : orderInfoItems) {
            PledgeGoodsPrice pledgeGoodsPrice = new PledgeGoodsPrice();
            PledgePriceResponse price;
//            if (isAnno) {
//                price = map.get(value.getItemCode() + orderInfo.getTargetSiteCode() + pledgeConfig.getOrderNo());
//            } else {
            price = map.get(value.getItemCode() + pledgeConfig.getOrderNo());
//            }
            if (null == price) {
                // 因为 一个订单  ，可能在不同的事业部 和不同的质押单 下，如果找不到 ，继续往下走
                continue;
            }

            pledgeGoodsPrice.setGoodsCode(value.getItemCode());
            pledgeGoodsPrice.setOrderNo(orderInfo.getOrderNo());
            pledgeGoodsPrice.setPledgeNo(pledgeConfig.getOrderNo());
            pledgeGoodsPrice.setPledgePrice(price.getPledgePrice());
            pledgeGoodsPriceList.add(pledgeGoodsPrice);
            orderAmount = orderAmount.add(value.getPlanQty().multiply(price.getPledgePrice()));
        }
        savePledgeGoodsPrice(pledgeGoodsPriceList);
        return orderAmount;
    }


    /**
     * 获取订单总额
     *
     * @param orderInfo
     * @return
     */
    private BigDecimal getPledgeOrderAmount(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, Boolean isAgent, Boolean isAnno, List<OrderInfoItem> orderInfoItems) {

        List requestList = new ArrayList();
        int pageSize = 100;

        List<PledgePriceResponse> priceList = new ArrayList();
        //获取该订单下的所有商品价格
        for (int i = 1; i <= orderInfoItems.size(); i++) {
            OrderInfoItem orderInfoItem = orderInfoItems.get(i - 1);
            PledgePriceRequest temp = new PledgePriceRequest();
            temp.setGoodsCode(orderInfoItem.getItemCode());
            temp.setOrderNo(pledgeConfig.getOrderNo());
            if (isAnno) {
                temp.setSiteCode(orderInfo.getTargetSiteCode());
            }
            temp.setIsAgent(isAgent);
            if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
                temp.setCustomerCode(orderInfo.getTargetCustomerCode());
            } else {
                temp.setCustomerCode(orderInfo.getCustomerCode());
            }
            requestList.add(temp);
            if (i % pageSize == 0 || i == orderInfoItems.size()) {
                JsonResponse<List<PledgePriceResponse>> result = centerInvServiceImpl.getPledgePrice(requestList);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(result.getCode())) {
                    log.warn("获取质押价格失败, requestList = {}", JSON.toJSONString(requestList));
                    throw new BusinessException(OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getCode(), result.getMsg());
                }
                if (null != result.getData()) {
                    //获取所有的价格列表
                    priceList.addAll(result.getData());
                }
            }
        }


        //计算订单价格
        Map<String, PledgePriceResponse> map = new HashMap();
        priceList.forEach(value -> {
            if (isAnno) {
                map.put(value.getGoodsCode() + value.getSiteCode() + value.getParentOrderCode(), value);
            } else {
                map.put(value.getGoodsCode() + value.getParentOrderCode(), value);
            }
        });

        BigDecimal orderAmount = new BigDecimal(0);

        List<PledgeGoodsPrice> pledgeGoodsPriceList = new ArrayList<>();

        for (OrderInfoItem value : orderInfoItems) {
            PledgeGoodsPrice pledgeGoodsPrice = new PledgeGoodsPrice();
            PledgePriceResponse price;
            if (isAnno) {
                price = map.get(value.getItemCode() + orderInfo.getTargetSiteCode() + pledgeConfig.getOrderNo());
            } else {
                price = map.get(value.getItemCode() + pledgeConfig.getOrderNo());
            }
            if (null == price) {
                // 因为 一个订单  ，可能在不同的事业部 和不同的质押单 下，如果找不到 ，继续往下走
                continue;
            }

            pledgeGoodsPrice.setGoodsCode(value.getItemCode());
            pledgeGoodsPrice.setOrderNo(orderInfo.getOrderNo());
            pledgeGoodsPrice.setPledgeNo(pledgeConfig.getOrderNo());
            pledgeGoodsPrice.setPledgePrice(price.getPledgePrice());
            pledgeGoodsPriceList.add(pledgeGoodsPrice);
            orderAmount = orderAmount.add(value.getPlanQty().multiply(price.getPledgePrice()));
        }
        savePledgeGoodsPrice(pledgeGoodsPriceList);
        return orderAmount;
    }

    /**
     * 增加放行金额  配置
     *
     * @param orderInfo
     * @param pledgeConfig
     * @param pledgeHisAddRequestList
     * @param pledgeInfoList
     * @param iaAnnto
     * @param orderAmount
     * @param balanceAmount
     */
    private void addGoneAmountPledgeConfig(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, List<PledgeHisAddRequest> pledgeHisAddRequestList, List<PledgeInfo> pledgeInfoList, boolean iaAnnto, BigDecimal orderAmount, BigDecimal balanceAmount) {
        PledgeConfigModReq pledgeConfigModReq = new PledgeConfigModReq();
        BigDecimal goneAmount = orderAmount.add(validateBigDecimal(pledgeConfig.getGoneAmount()));
        pledgeConfigModReq.setGoneAmount(goneAmount);

        pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));
        pledgeConfigModReq.setOnwayAmount(validateBigDecimal(pledgeConfig.getOnwayAmount()));
        pledgeConfigModReq.setOnwayAmountOld(validateBigDecimal(pledgeConfig.getOnwayAmount()));
        pledgeConfigModReq.setBillcontrolAmount(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
        pledgeConfigModReq.setBillcontrolAmountOld(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
        pledgeConfigModReq.setTotalAmount(validateBigDecimal(pledgeConfig.getTotalAmount()));
        pledgeConfigModReq.setTotalAmountOld(validateBigDecimal(pledgeConfig.getTotalAmount()));
        pledgeConfigModReq.setGoneAmountOld(validateBigDecimal(pledgeConfig.getGoneAmount()));
        pledgeConfigModReq.setJobId(pledgeConfig.getJobId());

        PledgeHisAddRequest hisAddReq = getPledgeHisAddReq(pledgeConfig, pledgeConfigModReq);

        //增加已放行金额（质押操作记录，质押订单监控表各增加一条记录
        CdWhPledgeAmount pledgeAmount = getPledgeAmoutHistory(pledgeConfig, pledgeConfigModReq, orderInfo, orderAmount, balanceAmount);
        pledgeAmount.setActionType(PledgeActionTypeEnum.OUT_CHECK.getKey());
        hisAddReq.setPledgeAmount(pledgeAmount);
        //质押订单监控表

        //质押订单监控表
        OrderPriceDto orderPriceDto = getOrderGoodAmount(orderInfo, false, pledgeConfig.getOrderNo(), iaAnnto);
//            Boolean isAnntoWareHouse = isAnntoWareHouse(orderInfo);
//            String isAnntoWareHouseStr = CommonEnum.YES.getValue().toString();
//            if (!isAnntoWareHouse) {
        String isAnntoWareHouseStr = CommonEnum.NO.getValue().toString();
//            }
        PledgeMoni pledgemoni = getPledgeMoni(pledgeConfig, orderInfo, isAnntoWareHouseStr, orderAmount.doubleValue(), orderPriceDto.getDefectGoodsAmount().doubleValue(), orderPriceDto.getQuanlityGoodAmount().doubleValue());

        hisAddReq.setPledgeMoni(pledgemoni);

        pledgeHisAddRequestList.add(hisAddReq);

        PledgeInfo pledgeInfo = addLocalPledgeInfo(orderInfo, pledgeConfig, iaAnnto, pledgeConfigModReq, orderAmount, PledgeType.FINANCE);
        pledgeInfoList.add(pledgeInfo);
    }


    /**
     * 获取质押历史记录添加bean
     *
     * @return
     */
    private PledgeHisAddRequest getPledgeHisAddReq(PledgeConfigResponse pledgeCofig, PledgeConfigModReq pledgeConfigModReq) {
        PledgeHisAddRequest request = new PledgeHisAddRequest();

        request.setConfig(pledgeConfigModReq);


        return request;
    }

    /**
     * 有些customer_code  是查不到客户编码 的，所有，如果找不到的话，就继续执行  ，或者失败的话，就当做  客户约 为 0
     * 获取该事业部下 ，质押的客户余额
     * 如果一个质押单里面，多个客户
     *
     * @param pledgeConfig
     * @return
     */
    private BigDecimal getCustBalance(PledgeConfigResponse pledgeConfig) {

        if (null == pledgeConfig.getBalncAmountUsed()) {
            return BigDecimal.ZERO;
        }
        if (CommonEnum.NO.getValue().compareTo(pledgeConfig.getBalncAmountUsed().intValue()) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal balncAmount = BigDecimal.ZERO;
        CustBalanceRequest custBalanceRequest = new CustBalanceRequest();
        custBalanceRequest.setEntityId(pledgeConfig.getBuBodyId());
        custBalanceRequest.setOrderNo(pledgeConfig.getOrderNo());
        custBalanceRequest.setSaleCenterCode(pledgeConfig.getMarketingCenterCims());
        String customersCode = pledgeConfig.getCustomersCode();

        //获取总余额
        for (String customerCode : customersCode.split(",")) {

            EbCustomer ebCustomer = ebCustomerManager.getEbCustomerCache(customerCode);
            //有些customer_code  是查不到客户编码 的，所有，如果找不到的话，就继续执行  ，或者失败的话，就当做  客户约 为 0
            if (null == ebCustomer) {
                continue;
            }
            String agentCode;
            if (null == ebCustomer.getOriginalGroupCode()) {
                throw BusinessException.fail("请求到款余额失败....");
            } else {
                agentCode = ebCustomer.getOriginalGroupCode();
            }
            custBalanceRequest.setAgentCode(agentCode);
            CustBalanceResponse balanceRespData = centerInvServiceImpl.getCustBalance(custBalanceRequest);
            if (balanceRespData == null) {
                log.warn("请求到款余额失败....");
                throw BusinessException.fail("请求到款余额失败....");
            }


            if (!balanceRespData.getIsSuccess()) {
                log.info("查询到款余额失败。");
                throw BusinessException.fail("请求到款余额失败....");
            }


            balncAmount = balncAmount.add(balanceRespData.getBalanceVal());

        }

        return balncAmount;
    }


    /**
     * 获取质权转换的金额
     *
     * @param pledgeConfig
     * @param balanceVal
     * @return
     */
    private BigDecimal getTrancAmount(PledgeConfigResponse pledgeConfig, BigDecimal balanceVal, BigDecimal orderAmount) {


        BigDecimal temp = balanceVal.subtract(validateBigDecimal(pledgeConfig.getGoneAmount())).subtract(validateBigDecimal(pledgeConfig.getBillcontrolAmount()));
        if (temp.compareTo(BigDecimal.ZERO) < 0) {
            temp = BigDecimal.ZERO;
        }
        BigDecimal temp1;
        if (orderAmount.compareTo(validateBigDecimal(pledgeConfig.getBillcontrolAmount())) < 0) {
            temp1 = orderAmount;
        } else {
            temp1 = validateBigDecimal(pledgeConfig.getBillcontrolAmount());
        }
        temp = temp.subtract(temp1).negate();
        if (temp.compareTo(BigDecimal.ZERO) < 0) {
            temp = BigDecimal.ZERO;
        }

        return temp;

    }


    /**
     * @param pledgeConfig        质押配置信息
     * @param orderInfo           订单信息
     * @param isAnntoWh           是否安得仓
     * @param defectGoodsAmount   非正品价格
     * @param quanlityGoodsAmount 正品价格
     * @return
     */
    private PledgeMoni getPledgeMoni(PledgeConfigResponse pledgeConfig, OrderInfo orderInfo, String isAnntoWh, Double orderAmount, double defectGoodsAmount, double quanlityGoodsAmount) {


//        double orderAmount = 0;

        PledgeMoni pledgeMoni = new PledgeMoni();

//        pledgeMoni.setChargeAgainstAmount();

        String pattern = "yyyy-MM-dd HH:mm:ss";

        if (null == orderInfo.getUpperReceiverCode()) {
            log.warn(OrderConvergedReturnCode.ORDER_UPPER_RECEIVER_CODE_ERROR.getMsg());
            orderInfo.setUpperReceiverCode("null");
            pledgeMoni.setConsigneeCode("null");
//            throw new BusinessException(OrderConvergedReturnCode.ORDER_UPPER_RECEIVER_CODE_ERROR.getCode(), OrderConvergedReturnCode.ORDER_UPPER_RECEIVER_CODE_ERROR.getMsg());
        } else {
            pledgeMoni.setConsigneeCode(orderInfo.getUpperReceiverCode());
        }


        String ebcuNameCn = "null";
        EbCustomer ebCustomer = ebCustomerManager.getEbCustomerCache(orderInfo.getUpperReceiverCode());
        if (null != ebCustomer) {
            ebcuNameCn = ebCustomer.getEbcuNameCn();
        }

//        String pledgeCustName = mdmCacheManager.getName(orderInfo.getUpperReceiverCode(), EbCustomer.class);
        pledgeMoni.setConsigneeName(ebcuNameCn);
        pledgeMoni.setCustomerCode(orderInfo.getCustomerCode());
        pledgeMoni.setCustomerName(orderInfo.getCustomerName());
        pledgeMoni.setCustomerNo(orderInfo.getParentOrderNo());
        //非正品入库货值
        pledgeMoni.setDefectGoodsAmount(defectGoodsAmount);
        pledgeMoni.setEoorCreateTime(DateFormatUtils.format(orderInfo.getCreateTime(), pattern));
        if (null != orderInfo.getUpperOrderTime()) {

            String date = DateFormatUtils.format(orderInfo.getUpperOrderTime(), pattern);
            pledgeMoni.setEoorDoDate(date);
            pledgeMoni.setEoorDoDate(date);
        }
        //是否自提
        if (DeliveryType.isZT(orderInfo.getDeliveryType())) {
            pledgeMoni.setEoorIsSalePackup(CommonEnum.YES.getValue().toString());
        } else {
            pledgeMoni.setEoorIsSalePackup(CommonEnum.NO.getValue().toString());
        }
        pledgeMoni.setEoorOrderNo(orderInfo.getOrderNo());
        pledgeMoni.setEoorOrderTypeCode(orderInfo.getOrderType());
        pledgeMoni.setFinanceNo(orderInfo.getRelationOrderNo());
        pledgeMoni.setGoodsAmount(orderAmount);
        pledgeMoni.setIsAnntoWh(isAnntoWh);
        pledgeMoni.setIsClosed(0);
//     手工货值不清楚   pledgeMoni.setManualDoAmount();
        pledgeMoni.setParentOrderCode(pledgeConfig.getOrderNo());
        //正品入库金额
        pledgeMoni.setQuanlityGoodsAmount(quanlityGoodsAmount);

        pledgeMoni.setIsClosed(0);
        pledgeMoni.setCreator("140");
        pledgeMoni.setCreateTime(new Date());
        pledgeMoni.setModifier("140");
        pledgeMoni.setModifyTime(new Date());
        pledgeMoni.setRecStatus(0);
        pledgeMoni.setIsPledgePriceUpdate(0);
//        pledgeMoni.setDef5("");
        pledgeMoni.setEoorDoDate(DateFormatUtils.format(orderInfo.getCreateTime(), pattern));
        return pledgeMoni;

    }


    /**
     * 获取需要保存本地的质押历史记录 ，同时保存在pledgeInfoList  里面
     *
     * @param orderInfo
     * @param pledgeConfig
     * @param isAnntoWareHouse
     * @param pledgeConfigModReq
     * @param orderAmount
     */
    private PledgeInfo addLocalPledgeInfo(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, Boolean isAnntoWareHouse, PledgeConfigModReq pledgeConfigModReq, BigDecimal orderAmount, PledgeType pledgeType) {

        PledgeInfo pledgeInfo = new PledgeInfo();
        BeanUtils.copyProperties(pledgeConfigModReq, pledgeInfo);

        pledgeInfo.setOrderNo(orderInfo.getOrderNo());
        pledgeInfo.setPledgeCode(pledgeConfig.getOrderNo());
        pledgeInfo.setPledgeCustCode(orderInfo.getCustomerCode());
        //质押是否如安得仓
        if (isAnntoWareHouse) {
            pledgeInfo.setPledgeInFlag(CommonEnum.Y.getKey());
        } else {
            pledgeInfo.setPledgeInFlag(CommonEnum.N.getKey());
        }

        pledgeInfo.setPledgeFlag(CommonEnum.Y.getValue());
        pledgeInfo.setOrderAmount(orderAmount);
        //融资类型
        pledgeInfo.setPledgeType(pledgeType.getKey());
        pledgeInfo.setPledgeStatus(PledgeStatusEnum.CHECK.getKey());
        return pledgeInfo;

    }


    /**
     * 是安得仓 返回 true 不是 ，或者查询接口失败，返回false
     *
     * @param orderInfo
     * @return
     */
    private Boolean isAnntoWareHouse(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig) {

        //是否入安得仓判断


        if (null == orderInfo.getTargetSiteCode()) {
            if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
                ConsigneeSiteRequest consigneeSiteRequest = new ConsigneeSiteRequest();
                consigneeSiteRequest.setOrderNo(orderInfo.getCustomerOrderNo());
                ConsigneeSiteResponse consigneeSiteResponse = centerInvServiceImpl.getConsigneeSiteByOrderNo(consigneeSiteRequest);
                log.info("consigneeSiteResp is {}", JSON.toJSONString(consigneeSiteResponse));
                if (null == consigneeSiteResponse || "F".equals(consigneeSiteResponse.getIsSuccess())) {
                    log.warn("can not  find consigneeSiteResponse");

                    return false;
//                    throw BusinessException.fail("查询是否安得仓接口失败");
                }

                if (!CommonEnum.Y.getKey().equals(consigneeSiteResponse.getIsAnntoWarehouse())) {
                    return false;
                }
            } else {
                return false;
            }
        }

        //是否入安得仓判断

        String siteCode = orderInfo.getTargetSiteCode();

        if (DeliveryType.isZT(orderInfo.getDeliveryType())) {
            boolean isAnntoflag = false;
            if ((null == orderInfo.getTargetSiteCode() && null == orderInfo.getUpperSenderCode())) {
                isAnntoflag = true;
            } else if (null != orderInfo.getTargetSiteCode()) {
                isAnntoflag = orderInfo.getTargetSiteCode().equals(orderInfo.getUpperSenderCode());
            }

            //判断获取转移 ，siteCode 重新设值
            if (JoinType.TRANS_INV.getKey().equals(orderInfo.getJoinType())) {
                siteCode = orderInfo.getSiteCode();
                isAnntoflag = true;
            }
            if (isAnntoflag == false) {
                return false;
            }
        }


        PledgeRangRequest pledgeRangRequest = new PledgeRangRequest();
        pledgeRangRequest.setOrderNo(pledgeConfig.getOrderNo());
        if ("1".equals(pledgeConfig.getRange())) {
            pledgeRangRequest.setWhCode(orderInfo.getWhCode());
        } else {
            pledgeRangRequest.setSiteCode(siteCode);
        }
        if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
            pledgeRangRequest.setCustomerCode(orderInfo.getTargetCustomerCode());
        } else {
            pledgeRangRequest.setCustomerCode(orderInfo.getCustomerCode());
        }
        JsonResponse<List<PledgeRangResponse>> pledgeRangResp = centerInvServiceImpl.pledgeRang(pledgeRangRequest);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeRangResp.getCode())) {
            log.info("质押范围 查询接口失败");
            throw BusinessException.fail("通过鹊桥 查询lms质押范围失败");
        }
        if (pledgeRangResp.getData().isEmpty()) {
            return false;
        }
        return true;
    }


    /**
     * 获取正品的 商品价格 ，和非正品的商品价格
     *
     * @param orderInfo
     * @return
     */
    private OrderPriceDto getOrderGoodAmount(OrderInfo orderInfo, boolean isAgent, String pledgeOrderNo, boolean isAnno) {

        JsonResponse<List<OrderInfoItem>> orderItemResp = orderInfoItemFeign.getOrderItem(orderInfo.getOrderNo());

        List<OrderInfoItem> orderInfoItems = new ArrayList<>();

        if (null != orderItemResp.getData()) {
            orderInfoItems = orderItemResp.getData();
        }
        List requestList = new ArrayList();
        int pageSize = 30;

        List<PledgePriceResponse> priceList = new ArrayList();
        //获取该订单下的所有商品价格
        for (int i = 1; i <= orderInfoItems.size(); i++) {
            OrderInfoItem orderInfoItem = orderInfoItems.get(i - 1);
            PledgePriceRequest temp = new PledgePriceRequest();
            temp.setGoodsCode(orderInfoItem.getItemCode());
            temp.setOrderNo(pledgeOrderNo);
            if (isAnno) {
                temp.setSiteCode(orderInfo.getSiteCode());
            }
            temp.setIsAgent(isAgent);
            if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
                temp.setCustomerCode(orderInfo.getTargetCustomerCode());
            } else {
                temp.setCustomerCode(orderInfo.getCustomerCode());
            }
            requestList.add(temp);
            if (i % pageSize == 0 || i == orderInfoItems.size()) {
                JsonResponse<List<PledgePriceResponse>> result = centerInvServiceImpl.getPledgePrice(requestList);
                if (null != result.getData()) {
                    priceList.addAll(result.getData());
                }
            }
        }


        //计算订单价格
        Map<String, PledgePriceResponse> map = new HashMap();
        priceList.forEach(value -> {
            map.put(value.getGoodsCode(), value);
        });

        BigDecimal quanlityGoodAmount = BigDecimal.ZERO;
        BigDecimal defectGoodsAmount = BigDecimal.ZERO;
        for (OrderInfoItem goodsItem : orderInfoItems) {
            PledgePriceResponse price = map.get(goodsItem.getItemCode());
            if (null == price) {
                continue;
//                throw new BusinessException(OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getCode(), OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getMsg());
            }
            if (CommonEnum.Y.getKey().equals(goodsItem.getItemStatus())) {
                quanlityGoodAmount = quanlityGoodAmount.add(goodsItem.getPlanQty().multiply(price.getPledgePrice()));
            } else {
                defectGoodsAmount = defectGoodsAmount.add(goodsItem.getPlanQty().multiply(price.getPledgePrice()));
            }
        }

        OrderPriceDto orderPriceDto = new OrderPriceDto();
        orderPriceDto.setDefectGoodsAmount(defectGoodsAmount);
        orderPriceDto.setQuanlityGoodAmount(quanlityGoodAmount);

        return orderPriceDto;

    }


    /**
     * 质押操作记录
     *
     * @param pledgeConfig
     * @param pledgeConfigModReq
     * @return
     */
    private CdWhPledgeAmount getPledgeAmoutHistory(PledgeConfigResponse pledgeConfig, PledgeConfigModReq pledgeConfigModReq, OrderInfo orderInfo, BigDecimal orderAmount, BigDecimal balncAmount) {

        if (null == pledgeConfig) {
            throw new BusinessException(BaseCodeEnum.REQUEST_NULL.getCode(), BaseCodeEnum.REQUEST_NULL.getMsg());
        }

        CdWhPledgeAmount cdWhPledgeAmount = new CdWhPledgeAmount();
        cdWhPledgeAmount.setTotalAmount(validateBigDecimal(pledgeConfig.getTotalAmount()));
        cdWhPledgeAmount.setBalncAmount(balncAmount);
        BigDecimal totalAmountOld = pledgeConfigModReq.getTotalAmountOld();
        if (totalAmountOld == null || totalAmountOld.compareTo(BigDecimal.ZERO) < 0) {
            totalAmountOld = BigDecimal.ZERO;
        }
        cdWhPledgeAmount.setFmTotalAmount(totalAmountOld);
        BigDecimal totalAmount = pledgeConfigModReq.getTotalAmount();
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) < 0) {
            totalAmount = BigDecimal.ZERO;
        }
        cdWhPledgeAmount.setToTotalAmount(totalAmount);
        BigDecimal goneAmountOld = pledgeConfigModReq.getGoneAmountOld();
        if (goneAmountOld == null || goneAmountOld.compareTo(BigDecimal.ZERO) < 0) {
            goneAmountOld = BigDecimal.ZERO;
        }
        cdWhPledgeAmount.setFmGoneAmount(goneAmountOld);
        BigDecimal goneAmount = pledgeConfigModReq.getGoneAmount();
        if (goneAmount == null || goneAmount.compareTo(BigDecimal.ZERO) < 0) {
            goneAmount = BigDecimal.ZERO;
        }
        cdWhPledgeAmount.setToGoneAmount(goneAmount);
        BigDecimal onwayAmount = pledgeConfigModReq.getOnwayAmount();
        if (onwayAmount == null || onwayAmount.compareTo(BigDecimal.ZERO) < 0) {
            onwayAmount = BigDecimal.ZERO;
        }
        cdWhPledgeAmount.setToOnwayAmount(onwayAmount);
        BigDecimal onwayAmountOld = pledgeConfigModReq.getOnwayAmountOld();
        if (onwayAmountOld == null || onwayAmountOld.compareTo(BigDecimal.ZERO) < 0) {
            onwayAmountOld = BigDecimal.ZERO;
        }
        cdWhPledgeAmount.setFmOnwayAmount(onwayAmountOld);

        BigDecimal billcontrolAmountOld = pledgeConfigModReq.getBillcontrolAmountOld();
        if (billcontrolAmountOld == null) {
            billcontrolAmountOld = BigDecimal.ZERO;
        }

        cdWhPledgeAmount.setFmBillcontrolAmount(billcontrolAmountOld);
        BigDecimal billcontrolAmount = pledgeConfigModReq.getBillcontrolAmount();
        if (billcontrolAmount == null) {
            billcontrolAmount = BigDecimal.ZERO;
        }
        //使用库存置换
        cdWhPledgeAmount.setToBillcontrolAmount(billcontrolAmount);

        cdWhPledgeAmount.setParentOrderCode(pledgeConfig.getOrderNo());


        cdWhPledgeAmount.setFmDef2(pledgeConfig.getDef2());
        cdWhPledgeAmount.setToDef2(pledgeConfig.getDef2());
        cdWhPledgeAmount.setFmOrderAmountChg(pledgeConfig.getOrderAmountChg());
        cdWhPledgeAmount.setToOrderAmountChg(pledgeConfig.getOrderAmountChg());
        cdWhPledgeAmount.setFmBalncAmountUsed(pledgeConfig.getBalncAmountUsed());
        cdWhPledgeAmount.setToBalncAmountUsed(pledgeConfig.getBalncAmountUsed());
        cdWhPledgeAmount.setFmMarketingCenterCims(pledgeConfig.getMarketingCenterCims());
        cdWhPledgeAmount.setToMarketingCenterCims(pledgeConfig.getMarketingCenterCims());
        cdWhPledgeAmount.setFmBuBodyId(pledgeConfig.getBuBodyId());
        cdWhPledgeAmount.setToBuBodyId(pledgeConfig.getBuBodyId());
        cdWhPledgeAmount.setFmBeginDate(pledgeConfig.getBeginDate());
        cdWhPledgeAmount.setFmEndDate(pledgeConfig.getEndDate());
        cdWhPledgeAmount.setBeginDate(pledgeConfig.getBeginDate());
        cdWhPledgeAmount.setEndDate(pledgeConfig.getEndDate());
        cdWhPledgeAmount.setFmActitive(pledgeConfig.getActitive());
        cdWhPledgeAmount.setToActitive(pledgeConfig.getActitive());
        cdWhPledgeAmount.setFmPledgeType(pledgeConfig.getPledgeType());
        cdWhPledgeAmount.setToPledgeType(pledgeConfig.getPledgeType());
        cdWhPledgeAmount.setOrderNo(orderInfo.getOrderNo());
        cdWhPledgeAmount.setCreateTime(new Date());
        cdWhPledgeAmount.setCreator("140");
        cdWhPledgeAmount.setModifier("140");
        cdWhPledgeAmount.setModifyTime(new Date());
        cdWhPledgeAmount.setRecStatus(0L);
        cdWhPledgeAmount.setCustomerCode(orderInfo.getCustomerCode());
        cdWhPledgeAmount.setCustomerName(orderInfo.getCustomerName());
        cdWhPledgeAmount.setOrderTypeCode(orderInfo.getOrderType());
        cdWhPledgeAmount.setOrderAmount(orderAmount);

        cdWhPledgeAmount.setDef1(pledgeConfig.getProperty());
        cdWhPledgeAmount.setDef2(pledgeConfig.getProperty());

        return cdWhPledgeAmount;


    }


    /**
     * 查询融资订单配置  步骤二 白名单放行判断
     */
    private JsonResponse<Boolean> isWhiteOrder(String customerOrderNo, List<PledgeConfigResponse> pledgeConfigList) {

        PledgeOrderRequest pledgeOrderRequest = new PledgeOrderRequest();
        pledgeOrderRequest.setOrderNo(customerOrderNo);
        JsonResponse<List<PledgeOrderResponse>> pledgeOrderResp = centerInvServiceImpl.getPledgeOrderByOrderNo(pledgeOrderRequest);
        log.info("pledgeOrder = {}", JSON.toJSONString(pledgeOrderResp));

        if (!BaseCodeEnum.SUCCESS.getCode().equals(pledgeOrderResp.getCode())) {
            JsonResponse<Boolean> jsonResponse = new JsonResponse();
            jsonResponse.setMsg(OrderConvergedReturnCode.PLEDGE_ORDER_QUERY_ERROR.getMsg());
            jsonResponse.setCode(OrderConvergedReturnCode.PLEDGE_ORDER_QUERY_ERROR.getCode());
            return jsonResponse;
        }

        //白名单放行判断，查询融资订单配置，若查到结果，则白名单放行；无须后续步骤；
        List<PledgeOrderResponse> pledgeOrder = pledgeOrderResp.getData();
        //判断是否白名单，如果白名单立即放行
        if (!pledgeOrder.isEmpty() && pledgeOrder.size() == pledgeConfigList.size()) {
//        if (null != pledgeOrder && CommonEnum.Y.getValue().equals(pledgeOrder.getControlType())) {
            JsonResponse<Boolean> jsonResponse = new JsonResponse();
            jsonResponse.setMsg(BaseCodeEnum.SUCCESS.getMsg());
            jsonResponse.setCode(BaseCodeEnum.SUCCESS.getCode());
            jsonResponse.setData(true);
            return jsonResponse;
        }
        return null;
    }


    /**
     * 获取订单总额
     *
     * @param orderInfo
     * @return
     */
    private BigDecimal getOrderAmount(OrderInfo orderInfo, PledgeConfigResponse pledgeConfig, Boolean isAgent, List<OrderInfoItem> orderInfoItems) {

        List requestList = new ArrayList();
        int pageSize = 30;

        List<PledgePriceResponse> priceList = new ArrayList();
        //获取该订单下的所有商品价格
        for (int i = 1; i <= orderInfoItems.size(); i++) {
            OrderInfoItem orderInfoItem = orderInfoItems.get(i - 1);
            PledgePriceRequest temp = new PledgePriceRequest();
            temp.setGoodsCode(orderInfoItem.getItemCode());
            temp.setOrderNo(pledgeConfig.getOrderNo());
//            temp.setSiteCode(orderInfo.getSiteCode());
            temp.setIsAgent(isAgent);

            if (SourceSystem.CIMS.getKey().equals(orderInfo.getSourceSystem())) {
                temp.setCustomerCode(orderInfo.getTargetCustomerCode());
            } else {
                temp.setCustomerCode(orderInfo.getCustomerCode());
            }
            temp.setIsAgent(isAgent);
            requestList.add(temp);
            if (i % pageSize == 0 || i == orderInfoItems.size()) {
                JsonResponse<List<PledgePriceResponse>> result = centerInvServiceImpl.getPledgePrice(requestList);
                if (!BaseCodeEnum.SUCCESS.getCode().equals(result.getCode()) || result.getData().isEmpty()) {
                    log.warn("获取质押价格失败, requestList = {}", JSON.toJSONString(requestList));
                    throw new BusinessException(OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getCode(), OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getMsg());
                }
                if (null != result.getData()) {
                    priceList.addAll(result.getData());
                }
            }
        }


        //计算订单价格
        Map<String, PledgePriceResponse> map = new HashMap();
        priceList.forEach(value -> {
            map.put(value.getGoodsCode(), value);
        });

        BigDecimal orderAmount = new BigDecimal(0);
        List<PledgeGoodsPrice> pledgeGoodsPriceList = new ArrayList<>();
        for (OrderInfoItem value : orderInfoItems) {
            PledgePriceResponse price = map.get(value.getItemCode());
            if (null == price) {
                log.warn("获取质押价格查询不到 ，订单商品存在");
                throw new BusinessException(OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getCode(), OrderConvergedReturnCode.PLEDGE_PRICE_QUERY_ERROR.getMsg());
            }
            PledgeGoodsPrice pledgeGoodsPrice = new PledgeGoodsPrice();
            pledgeGoodsPrice.setPledgePrice(price.getPledgePrice());
            pledgeGoodsPrice.setPledgeNo(pledgeConfig.getOrderNo());
            pledgeGoodsPrice.setOrderNo(orderInfo.getOrderNo());
            pledgeGoodsPrice.setGoodsCode(value.getItemCode());
            pledgeGoodsPriceList.add(pledgeGoodsPrice);
            orderAmount = orderAmount.add(value.getPlanQty().multiply(price.getPledgePrice()));
        }
        savePledgeGoodsPrice(pledgeGoodsPriceList);
        return orderAmount;
    }


}
