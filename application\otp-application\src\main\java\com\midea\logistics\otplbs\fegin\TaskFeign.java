package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskFeign
 * Author: fangwb
 * Date: 2019年5月14日 下午6:12:06
 * Description: feign
 */
@FeignClient(value = "logistics-otp-task-service",url = BaseRouter.LOGISTICS_OTP_TASK_SERVICE)
public interface TaskFeign {

    /**
     * @description: 根据【whCode + siteCode+ orderStatus = ‘发货’ + executeOrderStatus = ‘未关闭 ’ + taskType=OUT】 条件查询，返回前端分页列表
     * @param: [task]
     * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.otp.task.domain.bean.Task>
     * @author: 陈永培
     * @createtime: 2019/8/12 14:11
     */
    @RequestMapping(value = TaskRouters.TASK_SEARCH_TASK_OUT_LIST, method = RequestMethod.GET)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskOutList(@SpringQueryMap SearchTaskRequest task);

    @RequestMapping(value = "/task", method = RequestMethod.POST)
    JsonResponse<Integer> create(@RequestBody Task task);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.GET)
    JsonResponse<Task> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody Task task);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/tasks", method = RequestMethod.GET)
    JsonResponse<PageResponse<Task>> search(@RequestParam("task") Task task);

    @GetMapping("/car")
    JsonResponse queryCarCodeList(@RequestParam("code") String code);

    @GetMapping("/carArrivedInfo")
    JsonResponse getCarArrivedInfo(@RequestParam("code") String code);

    @PostMapping("/carArrivedInfo")
    JsonResponse saveCarArrivedInfo(@RequestBody String info);

    @GetMapping("/taskInfos")
    JsonResponse tasks(@RequestParam("task") Task task);

    @GetMapping("/taskInfo/{id}")
    JsonResponse task(@PathVariable("id") Long id);

    @PutMapping("/taskInfo/{id}")
    JsonResponse updateTask(@PathVariable("id") Long id, @RequestBody String reqBody);

    /**
     * @return 查询集合
     */
    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskList(@SpringQueryMap SearchTaskRequest searchTaskRequest);

    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskListPost(@RequestBody SearchTaskRequest searchTaskRequest);

    /**
     * @return 查询数量
     */
    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_COUNT)
    public JsonResponse<Integer> searchTaskCount(@SpringQueryMap SearchTaskRequest searchTaskRequest);

    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_COUNT)
    public JsonResponse<Integer> searchTaskCountPost(@RequestBody SearchTaskRequest searchTaskRequest);

}