package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;

/**
* @description: 装卸费规则
* @author: 陈永培
* @createtime: 2020/11/14 13:50
*/
public interface LoadingFeeAnalyzeService {
    /**
    * @description: 装卸费规则
    * @param: [customerOrderInfoExt]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2020/11/14 13:50
    */
    OrderInfoExt loadingFeeAnalyze(OrderInfoExt orderInfo, Boolean updateFlag);
}
