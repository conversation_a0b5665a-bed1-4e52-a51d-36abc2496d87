/* Alter table in target */
ALTER TABLE `appointment` 
	ADD COLUMN `arrive_no` VARCHAR(64)  NULL COMMENT '送货预约单号' AFTER `install_time_slot` , 
	ADD COLUMN `pick_start_time` TIMESTAMP   NULL COMMENT '预约提货开始时间，出库、自提出库、纯运输' AFTER `arrive_no` , 
	ADD COLUMN `pick_end_time` TIMESTAMP   NULL COMMENT '预约提货结束时间' AFTER `pick_start_time` , 
	ADD COLUMN `pick_time_slot` VARCHAR(30)  NULL COMMENT '预约提货时间段' AFTER `pick_end_time` , 
	ADD COLUMN `pick_no` VARCHAR(64)  NULL COMMENT '提货预约单号' AFTER `pick_time_slot` , 
	ADD COLUMN `unload_start_time` TIMESTAMP   NULL COMMENT '预约卸货开始时间，入库预约，调拨、分拨预约抵达时间' AFTER `pick_no` , 
	ADD COLUMN `unload_end_time` TIMESTAMP   NULL COMMENT '预约卸货结束时间' AFTER `unload_start_time` , 
	ADD COLUMN `unload_time_slot` VARCHAR(30)  NULL COMMENT '预约卸货时间段' AFTER `unload_end_time` , 
	ADD COLUMN `unload_no` VARCHAR(64)  NULL COMMENT '仓库卸货预约单号' AFTER `unload_time_slot` , 
	ADD COLUMN `appointment_operate_type` VARCHAR(32)  NULL COMMENT '预约操作类型：提货预约、送货预约、卸货预约、安装预约、送装预约' AFTER `unload_no` , 
	ADD COLUMN `appointment_system` VARCHAR(12)  NULL COMMENT '预约来源类型:O、T、NET、customer、直通宝、美的通' AFTER `appointment_operate_type` ;


/* Alter table in target */
ALTER TABLE `customer_order_address`  
	ADD COLUMN `tenant_code` VARCHAR(36)  NULL COMMENT '租户编码',
	ADD COLUMN `customer_order_no` VARCHAR(64)  NULL COMMENT '单号用于主从表关联' AFTER `tenant_code`;

/* Alter table in target */
ALTER TABLE logistics_otp_order.customer_order_waybill MODIFY COLUMN tenant_code varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'annto' NOT NULL COMMENT '租户编码';


/* Alter table in target */
ALTER TABLE `customer_order_info` 
	ADD COLUMN `expect_out_start_time` DATETIME   NULL COMMENT '要求出库开始时间' AFTER `consolidation_num` , 
	ADD COLUMN `expect_out_end_time` DATETIME   NULL COMMENT '要求出库结束时间' AFTER `expect_out_start_time` , 
	ADD COLUMN `expect_pick_start_time` DATETIME   NULL COMMENT '要求提货开始时间' AFTER `expect_out_end_time` , 
	ADD COLUMN `expect_pick_end_time` DATETIME   NULL COMMENT '要求提货结束时间' AFTER `expect_pick_start_time` ,
	ADD COLUMN `business_category` varchar(16) DEFAULT NULL COMMENT '业务大类',
	MODIFY `tenant_code` VARCHAR(36) NOT NULL DEFAULT 'annto' COMMENT '租户编码';

/* Create table in target */
CREATE TABLE `customer_order_info_mp`(
	`id` BIGINT(20) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT '主键' , 
	`remark` VARCHAR(255) NULL  COMMENT '备注' , 
	`delete_flag` SMALLINT(2) NOT NULL  DEFAULT 0 COMMENT '状态,0正常,1已删除' , 
	`create_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' , 
	`create_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '创建人员' , 
	`update_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' , 
	`update_user_code` VARCHAR(64) NULL  DEFAULT 'system' COMMENT '修改人员' , 
	`version` INT(11) NOT NULL  DEFAULT 0 COMMENT '数据版本' , 
	`tenant_code` VARCHAR(36) NULL  COMMENT '租户编码' , 
	`order_no` VARCHAR(32) NULL  COMMENT '订单号，系统生成' , 
	`customer_order_no` VARCHAR(64) NOT NULL  DEFAULT '0' COMMENT '客户订单号销售平台物流单号' , 
	`shop_id` VARCHAR(32) NULL  COMMENT '店铺编码' , 
	`shop_name` VARCHAR(128) NULL  COMMENT '店铺名称' , 
	`buyer_remark` VARCHAR(2048) NULL  COMMENT '买家备注' , 
	`logistic_mode` VARCHAR(8) NULL  COMMENT '运作模式，仓配一体、仓配分离' , 
	`aging_product_code` VARCHAR(16) NULL  COMMENT '时效产品CIMS才给' , 
	`freight_basis` VARCHAR(16) NULL  COMMENT '测量标准重货/轻货' , 
	`total_gross_weight` DECIMAL(18,8) NULL  COMMENT '总毛量' , 
	`total_volume` DECIMAL(18,8) NULL  COMMENT '总体积' , 
	`total_net_weight` DECIMAL(18,8) NULL  COMMENT '总净重' , 
	`total_qty` DECIMAL(18,8) NULL  COMMENT '总数量' , 
	`upper_receiver_code` VARCHAR(32) NULL  COMMENT '上游收货单位编码基于美的系' , 
	`upper_sender_code` VARCHAR(32) NULL  COMMENT '上游发货单位编码基于美的系' , 
	`upper_wh_code` VARCHAR(32) NULL  COMMENT '上游仓库编码' , 
	`upper_wh_name` VARCHAR(128) NULL  COMMENT '上游仓库名称' , 
	`upper_customer_code` VARCHAR(64) NULL  COMMENT '上游客户编码' , 
	`upper_customer_name` VARCHAR(128) NULL  COMMENT '上游客户名称' , 
	`upper_order_type` VARCHAR(16) NULL  COMMENT '上游订单类型' , 
	`upper_order_time` DATETIME NULL  DEFAULT CURRENT_TIMESTAMP COMMENT 'ERP审核时间(接单时间)默认当前时间' , 
	`upper_receiver_name` VARCHAR(255) NULL  COMMENT '上游收货名称' , 
	`upper_sender_name` VARCHAR(255) NULL  COMMENT '上游发货名称' , 
	`upper_service_type` TINYINT(1) NULL  COMMENT '服务类型使用关联配置表' , 
	`service_type` TINYINT(1) NULL  COMMENT '服务类型使用关联配置表' , 
	`order_value` DECIMAL(18,8) NULL  COMMENT '货值小B,接单、上游给，异步' , 
	`order_source` VARCHAR(8) NULL  COMMENT '订单来源：API：接口接入，VIEW：视图引单，FINANCE：财务单引入，HANDLE：手工' , 
	`handle_split_flag` TINYINT(1) NULL  COMMENT '手动拆单标识(9:自动拆单子单自提)' , 
	`pay_date` DATETIME NULL  COMMENT '支付时间暂未使用' , 
	`express_no` VARCHAR(1024) NULL  COMMENT '上游快递单号暂未使用，退货使用' , 
	`apart_type` VARCHAR(16) NULL  COMMENT '拆单类型：手动、单品单件' , 
	`oppintment_status` TINYINT(1) NULL  COMMENT '预约状态0未预约，1已预约暂未使用' , 
	`appointment_time` DATETIME NULL  COMMENT '预约时间暂未使用' , 
	`customer_group` VARCHAR(10) NULL  COMMENT '客户系美的系1' , 
	`identify_status` VARCHAR(32) NULL  COMMENT '鉴定类型上门取件' , 
	`pay_type` VARCHAR(32) NULL  COMMENT '付款方式' , 
	`load_type` VARCHAR(8) NULL  COMMENT '整车零担标识,取值对照为数据字典:LOAD_TYPE' , 
	`plan_order_flag` TINYINT(1) NULL  DEFAULT 0 COMMENT '模糊订单标志0：否，1：是模糊订单模糊订单' , 
	`equipment_type` VARCHAR(64) NULL  COMMENT '车型属性模糊订单' , 
	`equipment_name` VARCHAR(64) NULL  COMMENT '车型属名称模糊订单' , 
	`appoint_order_no` VARCHAR(32) NULL  COMMENT '商超预约单号' , 
	`receiver_type` VARCHAR(8) NULL  COMMENT '收货方类型' , 
	`sender_type` VARCHAR(8) NULL  COMMENT '发货方类型' , 
	`receiver_code` VARCHAR(32) NULL  COMMENT '收货单位编码' , 
	`receiver_name` VARCHAR(255) NULL  COMMENT '收货单位名称' , 
	`sender_code` VARCHAR(32) NULL  COMMENT '发货单位编码' , 
	`sender_name` VARCHAR(255) NULL  COMMENT '发货单位名称' , 
	PRIMARY KEY (`id`) 
) ENGINE=INNODB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_unicode_ci' COMMENT='客户订单表中台';


/* Create table in target */
CREATE TABLE `customer_order_info_otp`(
	`id` BIGINT(20) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT '主键' , 
	`remark` VARCHAR(255) NULL  COMMENT '备注' , 
	`delete_flag` SMALLINT(2) NOT NULL  DEFAULT 0 COMMENT '状态,0正常,1已删除' , 
	`create_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' , 
	`create_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '创建人员' , 
	`update_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' , 
	`update_user_code` VARCHAR(64) NULL  DEFAULT 'system' COMMENT '修改人员' , 
	`version` INT(11) NOT NULL  DEFAULT 0 COMMENT '数据版本' , 
	`tenant_code` VARCHAR(36) NULL  COMMENT '租户编码' , 
	`order_no` VARCHAR(32) NULL  COMMENT '订单号，系统生成' , 
	`customer_order_no` VARCHAR(64) NOT NULL  DEFAULT '0' COMMENT '客户订单号销售平台物流单号' , 
	`fact_in_out_time` DATETIME NULL  COMMENT '实际出入库时间回传上游使用' , 
	`spec_carrier_flag` TINYINT(1) NULL  COMMENT '是否指定承运商,0：不指定1：指定' , 
	`project_classify` VARCHAR(32) NULL  COMMENT '项目分类' , 
	`sc_pos_flag` TINYINT(1) NULL  DEFAULT 0 COMMENT '商超标志0：否，1：是' , 
	`join_type` VARCHAR(16) NULL  COMMENT '协同类型SHARE:调剂标识;transInv:货物转移标志' , 
	`entity_id` INT(11) NULL  COMMENT '实体基于美的系' , 
	`contract_no` VARCHAR(32) NULL  COMMENT '合同号' , 
	`dispatch_no` VARCHAR(32) NULL  COMMENT '发车单号' , 
	`upper_source_customer_code` VARCHAR(32) NULL  COMMENT '上游源客户编码基于美的系' , 
	`target_customer_code` VARCHAR(32) NULL  COMMENT '目标客户货权转移' , 
	`upper_relation_order_no` VARCHAR(64) NULL  COMMENT '上游关联单号' , 
	`upper_reference_id` VARCHAR(16) NULL  COMMENT '上游订单关联ID' , 
	`upper_targe_wh_code` VARCHAR(32) NULL  COMMENT '目标财务仓编码' , 
	`upper_targe_wh_name` VARCHAR(255) NULL  COMMENT '目标财务仓名称' , 
	`third_flag` TINYINT(1) NULL  COMMENT '是否第三方,0：否，1：是售后查询配置，不存表' , 
	`emergence_flag` TINYINT(1) NULL  COMMENT '是否紧急订单,0：否，1：是是否紧急发货，暂时没用' , 
	`outsource_flag` TINYINT(1) NULL  COMMENT '是否小电,0：否，1：是基于美的系' , 
	`exception_type` VARCHAR(64) NULL  COMMENT '订单审核异常类型' , 
	`exception_desc` VARCHAR(4095) NULL  COMMENT '订单审核异常描述' , 
	`order_rp_flag` VARCHAR(8) NULL  COMMENT '收付标志(是否产生装卸费标识)计费使用（记录中间表，BMS自己查）' , 
	`target_site_code` VARCHAR(32) NULL  COMMENT '收货平台编码解析配置，货权转移' , 
	`target_wh_code` VARCHAR(32) NULL  COMMENT '收货仓库编码' , 
	`join_reason` VARCHAR(32) NULL  COMMENT '调剂失败原因' , 
	`specimen_type` VARCHAR(8) NULL  COMMENT '上撤样美的系，索超确认' , 
	`upstream_doc_type` VARCHAR(8) NULL  COMMENT '是否仓间调拨美的系' , 
	`shop_guide_name` VARCHAR(64) NULL  COMMENT '导购姓名' , 
	`shop_guide_tel` VARCHAR(32) NULL  COMMENT '导购电话' , 
	`print_price_flag` TINYINT(4) NULL  COMMENT '是否打印单价美的系' , 
	`print_notax_price_flag` TINYINT(4) NULL  COMMENT '是否打印不含税单价美的系' , 
	`print_barcode` TINYINT(4) NULL  COMMENT '是否打印条码美的系' , 
	`work_order_no` VARCHAR(64) NULL  COMMENT 'C2M工单号' , 
	`c2m_type` VARCHAR(64) NULL  COMMENT 'C2M标识' , 
	`mip_flag` TINYINT(1) NULL  COMMENT '是否要走MIP,1是,0否查配置即可，不用存字段' , 
	`gray_flag` VARCHAR(64) NULL  COMMENT '菜鸟小件标示' , 
	`cn_dispatch` TINYINT(1) NULL  DEFAULT 0 COMMENT '预配载标志0：否，1：是' , 
	`delivered_verify_flag` TINYINT(1) NULL  DEFAULT 0 COMMENT '是否需要妥投码验证:1是,0否' , 
	`expect_install_type` VARCHAR(30) NULL  COMMENT '期望送装类型跟service_type整合' , 
	`upper_source_order_no` VARCHAR(100) NULL  COMMENT '上游源单号(国美单号等)' , 
	`upper_supplier_code` VARCHAR(64) NULL  COMMENT '上游供应商编码(国美供编码等)' , 
	PRIMARY KEY (`id`) 
) ENGINE=INNODB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_unicode_ci' COMMENT='客户订单表城配';


/* Alter table in target */
ALTER TABLE `customer_order_item` 
	ADD COLUMN `is_upper_stair` VARCHAR(16)  NULL COMMENT '是否上楼' AFTER `sub_order_no`,
	ADD COLUMN `customer_order_no` VARCHAR(64)  NULL COMMENT '客户订单';

/* Create table in target */
CREATE TABLE `mid_customer_order_address` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `remark` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `delete_flag` smallint(2) NOT NULL DEFAULT 0 COMMENT '状态,0可用,1不可用',
  `create_time` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `create_user_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'system' COMMENT '创建人员',
  `update_time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  `update_user_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '修改人员',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '数据版本',
  `order_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单号，系统生成',
  `receiver_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人名称',
  `receiver_tel` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人固定电话',
  `receiver_mobile` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人手机号码',
  `receiver_virtual_mobile` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人虚拟手机号',
  `receiver_country_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人国家编码',
  `receiver_country_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人国家名称',
  `receiver_province_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人省编码',
  `receiver_province_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人省名称',
  `receiver_city_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人市编码',
  `receiver_city_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人市名称',
  `receiver_district_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人区县编码',
  `receiver_district_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人区县名称',
  `receiver_town_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人乡镇编码',
  `receiver_town_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人乡镇名称',
  `receiver_detail_addr` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货人详细地址',
  `sender_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人名称',
  `sender_tel` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人固定电话',
  `sender_mobile` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人手机号码',
  `sender_country_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人国家编码',
  `sender_country_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人国家名称',
  `sender_province_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人省编码',
  `sender_province_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人省名称',
  `sender_city_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人市编码',
  `sender_city_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人市名称',
  `sender_district_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人区县编码',
  `sender_district_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人区县名称',
  `sender_town_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人乡镇编码',
  `sender_town_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人乡镇名称',
  `sender_detail_addr` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发货人详细地址',
  `network_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点编码',
  `network_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点名称',
  `network_contact` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点联系人',
  `network_tel` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点联系电话',
  `network_phone` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点联系手机',
  `network_province_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点省',
  `network_city_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点市',
  `network_district_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点区县',
  `network_town_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点乡镇/街道',
  `network_addr` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点详细地址',
  `start_lng` decimal(18,8) DEFAULT NULL COMMENT '发货地经度',
  `start_lat` decimal(18,8) DEFAULT NULL COMMENT '发货地维度',
  `end_lng` decimal(18,8) DEFAULT NULL COMMENT '收货地经度',
  `end_lat` decimal(18,8) DEFAULT NULL COMMENT '收货地维度',
  `network_province_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点省名称',
  `network_city_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点市名称',
  `network_district_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点区县名称',
  `network_town_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网点乡镇/街道名称',
  `origin_addr` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '源地址',
  `net_lng` decimal(18,8) DEFAULT NULL COMMENT '网点经度',
  `net_lat` decimal(18,8) DEFAULT NULL COMMENT '网点纬度',
  `tenant_code` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'annto' COMMENT '租户编码',
  PRIMARY KEY (`id`),
  KEY `order_no_index` (`order_no`),
  KEY `receiver_mobile_index` (`receiver_mobile`),
  KEY `idx_order_adress_sendmobile` (`sender_mobile`),
  KEY `idx_receiver_detail_addr` (`receiver_detail_addr`(60))
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户订单地址表';

/* Create table in target */
CREATE TABLE `mid_customer_order_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态,0可用,1不可用',
  `create_time` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `create_user_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '创建人员',
  `update_time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  `update_user_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '修改人员',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '数据版本',
  `remark` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_code` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'annto' COMMENT '租户编码',
  `order_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '订单号，系统生成',
  `origin_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始订单号',
  `customer_order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户订单号',
  `platform_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台单号',
  `relation_order_no` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联单号',
  `order_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单类型',
  `excute_status` smallint(6) DEFAULT NULL COMMENT '订单执行状态字段',
  `order_status` int(11) NOT NULL DEFAULT 100 COMMENT '订单状态 （100代表订单新建状态）',
  `company_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司编码',
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司名称',
  `customer_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户编码',
  `customer_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户名称',
  `site_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台编码',
  `site_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台名称',
  `wh_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '仓库编码',
  `wh_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '仓库名称',
  `order_source_platform` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单来源平台,例如：京东 天猫等',
  `source_system` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '来源系统,例如：菜鸟 ECM 奇门等',
  `shop_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺编码',
  `shop_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺名称',
  `upper_order_time` datetime DEFAULT current_timestamp() COMMENT 'ERP审核时间(接单时间) 默认当前时间',
  `require_out_time` datetime DEFAULT NULL COMMENT '应出库时间',
  `expect_out_time` datetime DEFAULT NULL COMMENT '要求出库时间',
  `expect_arrive_start_time` datetime DEFAULT NULL COMMENT '预期到货时间',
  `expect_arrive_end_time` datetime DEFAULT NULL COMMENT '预期到货时间',
  `expect_pick_time` datetime DEFAULT NULL COMMENT '预期到货时间',
  `carrier_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '承运商编码,自提:MJZT',
  `delivery_carrier_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '实际承运商编码,自提:MJZT',
  `spec_carrier_flag` tinyint(1) DEFAULT NULL COMMENT '是否指定承运商,0：不指定 1：指定',
  `business_mode` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'B2B，B2C ',
  `business_type` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务类型',
  `buyer_remark` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '买家备注',
  `delivery_type` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配送类型：快递、配送、运输、宅配、自提',
  `transport_type` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT '00' COMMENT '运输类型,例如：公路 铁路等',
  `logistic_mode` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '运作模式，仓配一体、仓配分离',
  `aging_product_code` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '时效产品',
  `in_out_type` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IN 入库单 OUT 出库单 YS 运输单 OTHER 其他',
  `project_classify` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目分类',
  `sc_pos_flag` tinyint(1) DEFAULT 0 COMMENT '商超标志 0：否，1：是',
  `freight_basis` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '测量标准 重货/轻货',
  `join_type` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '协同类型 SHARE:调剂标识,transInv:货物转移标志',
  `total_gross_weight` decimal(18,8) DEFAULT NULL COMMENT '总毛量',
  `total_volume` decimal(18,8) DEFAULT NULL COMMENT '总体积',
  `total_net_weight` decimal(18,8) DEFAULT NULL COMMENT '总净重',
  `total_qty` decimal(18,8) DEFAULT NULL COMMENT '总数量',
  `entity_id` int(11) DEFAULT NULL COMMENT '实体',
  `contract_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '合同号',
  `dispatch_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发车单号',
  `upper_receiver_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游收货单位编码',
  `upper_sender_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游发货单位编码',
  `upper_source_customer_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游源客户编码',
  `upper_wh_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游仓库编码',
  `upper_wh_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游仓库名称',
  `upper_customer_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游客户编码',
  `upper_customer_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游客户名称',
  `upper_order_type` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游订单类型',
  `target_customer_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标客户',
  `third_flag` tinyint(1) DEFAULT NULL COMMENT '是否第三方,0：否，1：是',
  `service_type` tinyint(1) DEFAULT NULL COMMENT '是否上门取件,0：否，1：是',
  `emergence_flag` tinyint(1) DEFAULT NULL COMMENT '是否紧急订单,0：否，1：是',
  `order_value` decimal(18,8) DEFAULT NULL COMMENT '货值',
  `outsource_flag` tinyint(1) DEFAULT NULL COMMENT '是否小电,0：否，1：是',
  `order_source` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单来源：API：接口接入，VIEW：视图引单，FINANCE：财务单引入，HANDLE：手工',
  `exception_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单审核异常类型',
  `exception_desc` varchar(4095) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单审核异常描述',
  `handle_split_flag` tinyint(1) DEFAULT NULL COMMENT '手动拆单标识(9:自动拆单子单自提)',
  `suborder_cnt` decimal(18,8) DEFAULT NULL COMMENT '包裹数量',
  `upper_relation_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游关联单号',
  `buyer_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '买家昵称',
  `pay_date` datetime DEFAULT NULL COMMENT '支付时间',
  `upper_comfirm_time` datetime DEFAULT NULL COMMENT '上游审核时间',
  `express_no` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游快递单号',
  `order_rp_flag` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收付标志(是否产生装卸费标识)',
  `consignee_time_from` datetime DEFAULT NULL COMMENT '客户指定发货时间',
  `consignee_time_to` datetime DEFAULT NULL COMMENT '客户指定发货时间',
  `target_site_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货平台编码',
  `apart_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拆单状态：已拆单、部分拆单',
  `apart_type` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拆单类型：手动、单品单件',
  `upper_reference_id` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游订单关联ID',
  `intercept_flag` smallint(6) DEFAULT NULL COMMENT '拦截取消标识999-取消，998-拦截',
  `join_reason` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '调剂失败原因',
  `oppintment_status` tinyint(1) DEFAULT NULL COMMENT '预约状态 0未预约，1已预约',
  `customer_group` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户系',
  `bu_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事业部编码',
  `specimen_type` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上撤样',
  `upstream_doc_type` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '是否仓间调拨',
  `shop_guide_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '导购姓名',
  `shop_guide_tel` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '导购电话',
  `upper_targe_wh_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标财务仓编码',
  `upper_targe_wh_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标财务仓名称',
  `upper_receiver_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游收货名称',
  `upper_sender_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游发货名称',
  `waybill_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '运单号',
  `identify_status` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鉴定类型',
  `print_price_flag` tinyint(4) DEFAULT NULL COMMENT '是否打印单价',
  `print_notax_price_flag` tinyint(4) DEFAULT NULL COMMENT '是否打印不含税单价',
  `print_barcode` tinyint(4) DEFAULT NULL COMMENT '是否打印条码',
  `if_up_stairs` varchar(4) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '是否上楼费（0：不上楼，1：上楼）',
  `pay_type` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '付款方式',
  `unit_area_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所选大区字段',
  `is_back` tinyint(1) DEFAULT NULL COMMENT '是否退货（0表示不退，1表示退货）',
  `fact_in_out_time` datetime DEFAULT NULL COMMENT '实际出入库时间',
  `order_time` timestamp NULL DEFAULT current_timestamp() COMMENT '订单时间',
  `appointment_time` datetime DEFAULT NULL COMMENT '预约时间',
  `target_wh_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收货仓库编码',
  `work_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'C2M工单号',
  `c2m_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'C2M标识',
  `mip_flag` tinyint(1) DEFAULT NULL COMMENT '是否要走MIP,1是,0否',
  `load_type` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '整车零担标识,取值对照为数据字典:LOAD_TYPE',
  `gray_flag` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜鸟小件标示',
  `cn_dispatch` tinyint(1) DEFAULT 0 COMMENT '预配载标志 0：否，1：是',
  `plan_order_flag` tinyint(1) DEFAULT 0 COMMENT '模糊订单标志 0：否，1：是',
  `equipment_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车型属性',
  `equipment_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车型属名称',
  `expect_install_start_time` datetime DEFAULT NULL COMMENT '期望开始安装时间',
  `expect_install_end_time` datetime DEFAULT NULL COMMENT '期望结束安装时间',
  `delivered_verify_flag` tinyint(1) DEFAULT 0 COMMENT '是否需要妥投码验证:1是,0否',
  `appoint_order_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商超预约单号',
  `expect_install_type` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '期望送装类型',
  `upper_source_order_no` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游源单号(国美单号等)',
  `upper_supplier_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游供应商编码(国美供编码等)',
  `accept_status` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '10' COMMENT '接单状态:10新增 20成功，30失败',
  `consolidation_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游集拼单号',
  `consolidation_num` int(11) DEFAULT NULL COMMENT '上游集拼单量',
  `business_category` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务大类',
  `install_flag` tinyint(1) DEFAULT 0 COMMENT '是否送装一体(安装):0否；1是',
  `total_pkg_qty` decimal(18,8) DEFAULT NULL COMMENT '总包件数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_no` (`order_no`),
  KEY `idx_customer_order_no` (`customer_order_no`),
  KEY `idx_customer_code` (`customer_code`),
  KEY `idx_contract_no` (`contract_no`),
  KEY `idx_source_system` (`source_system`),
  KEY `idx_create_time` (`create_time`)
)ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户订单中间表';

/* Create table in target */
CREATE TABLE `mid_customer_order_item` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_user_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '创建人,接口订单默认system,手工单默认客户id',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_user_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '修改人,接口订单默认system,手工单默认客户id',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
  `delete_flag` smallint(2) NOT NULL DEFAULT 0 COMMENT '状态,0可用,1不可用',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '数据行版本号',
  `remark` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_code` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '租户编码',
  `item_line_no` bigint(20) DEFAULT NULL COMMENT '商品行号',
  `item_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品编码',
  `customer_item_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户商品编码',
  `item_suite_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '套件编码',
  `item_class` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品类别',
  `item_class_name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品品类名称',
  `item_size` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品件型',
  `item_charging_type` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品计费件型',
  `item_status` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品状态',
  `item_status_to` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标商品状态',
  `upper_item_status` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游商品状态',
  `plan_qty` decimal(18,8) NOT NULL COMMENT '计划数量',
  `act_qty` decimal(18,8) DEFAULT NULL COMMENT '实际数量',
  `volume` decimal(18,8) DEFAULT NULL COMMENT '单台体积',
  `gross_weight` decimal(18,8) DEFAULT NULL COMMENT '单台毛重',
  `net_weight` decimal(18,8) DEFAULT NULL COMMENT '单台净重',
  `price` decimal(18,8) DEFAULT NULL COMMENT '商品单价',
  `amout` decimal(18,8) DEFAULT NULL COMMENT '商品金额',
  `unit` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
  `upper_item_id` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游商品id',
  `new_item_flag` tinyint(1) DEFAULT NULL COMMENT '是否新品,0：否，1：是',
  `sn_code` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'sn码',
  `batch_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批次号',
  `produce_code` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产批号',
  `produce_timestamp` datetime DEFAULT NULL COMMENT '生产日期',
  `expire_timestamp` datetime DEFAULT NULL COMMENT '过期日期',
  `cancle_qty` decimal(18,8) DEFAULT NULL COMMENT '取消数量',
  `customer_order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单号,用于主从表关联',
  `upper_item_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游客户商品编码',
  `item_name` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品名称',
  `source_pruduct` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IN：内部 OUT：外部',
  `specification_unit` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格单位 升PKGL',
  `product_specification` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格单位值',
  `item_spec` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品型号',
  `measure_standard` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '重泡货标识',
  `material_group1` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计费组1',
  `material_group2` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计费组2',
  `material_group3` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计费组3',
  `material_group4` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计费组4',
  `material_group5` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计费组5',
  `material_group6` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计费组6 计安装费',
  `notax_price` decimal(18,8) DEFAULT NULL COMMENT '未含税价',
  `market_model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '市场型号',
  `item_suite_qty` int(2) DEFAULT 1 COMMENT '套件数量关系',
  `lg_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'CCS代理商原始单号？',
  `split_qty` decimal(18,8) DEFAULT NULL COMMENT '已拆分数量',
  `finance_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '财务单号,内销订单发货确认由CIMS生成',
  `upper_item_status_to` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游目标商品状态',
  `set_flag` tinyint(1) DEFAULT NULL COMMENT '是否套件 0：否，1：是',
  `gift_flag` tinyint(1) DEFAULT NULL COMMENT '是否赠品 0：否，1：是',
  `express_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '快递单号',
  `barcode69` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国际条形码',
  `upper_line_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上游商品行号',
  `stockout_count` decimal(18,8) DEFAULT NULL COMMENT '短少',
  `reject_qty` decimal(18,8) DEFAULT NULL COMMENT '拒收',
  `bu_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事业部编码',
  `item_version` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品版本',
  `sub_ordercode` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '子交易单号',
  `order_source_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父交易单号',
  `order_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单号，系统生成',
  `single_volume` decimal(18,8) DEFAULT NULL COMMENT '单台体积',
  `single_weight` decimal(18,8) DEFAULT NULL COMMENT '单台重量',
  `sub_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台子单号',
  `packing_amount` int(11) DEFAULT NULL COMMENT '箱规数量',
  `to_customer_item_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标客户商品编码',
  `to_item_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标商品编码',
  `if_install` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '是否送装一体',
  `expect_install_start_time` datetime DEFAULT NULL COMMENT '期望开始安装时间',
  `expect_install_end_time` datetime DEFAULT NULL COMMENT '期望结束安装时间',
  `total_volume` decimal(18,8) DEFAULT NULL COMMENT '单行总体积',
  `total_gross_weight` decimal(18,8) DEFAULT NULL COMMENT '单行总毛重',
  `service_flag` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜鸟通电验机服务信息',
  `install_flag` tinyint(1) DEFAULT 0 COMMENT '是否送装一体(安装):0否；1是',
  `source_item_name` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '源平台商品名称',
  `source_item_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '源平台商品编码',
  `pkg_qty` decimal(18,8) DEFAULT NULL COMMENT '包件数',
  PRIMARY KEY (`id`),
  KEY `customer_order_no` (`customer_order_no`),
  KEY `item_code` (`item_code`),
  KEY `order_no` (`order_no`)
)ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单明细表中间表';

/* Create table in target */
CREATE TABLE `mid_customer_order_waybill` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态,0可用,1不可用',
  `create_time` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `create_user_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '创建人员',
  `update_time` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  `update_user_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '修改人员',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '数据版本',
  `remark` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tenant_code` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'annto' COMMENT '租户编码',
  `customer_order_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '客户订单号',
  `order_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '订单号，系统生成',
  `waybill_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '运单号',
  `vendor_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '供应商类型',
  `is_jitx` tinyint(32) DEFAULT NULL COMMENT '三段码',
  `vendor_Id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '供应商ID',
  `vip_meta` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报文体',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_no` (`order_no`),
  KEY `index_wn` (`waybill_no`),
  KEY `index_con` (`customer_order_no`)
)ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户订单面单中间表';

/* Create table in target */
CREATE TABLE `order_appointment_info`(
	`id` BIGINT(20) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT '主键' , 
	`create_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '创建用户' , 
	`create_user_name` VARCHAR(255) NOT NULL  DEFAULT 'system' COMMENT '创建用户名称' , 
	`update_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '修改用户' , 
	`update_user_name` VARCHAR(255) NOT NULL  DEFAULT 'system' COMMENT '修改用户名称' , 
	`remark` VARCHAR(500) NULL  DEFAULT '' COMMENT '备注' , 
	`delete_flag` SMALLINT(2) NOT NULL  DEFAULT 0 COMMENT '数据状态,0正常,1删除' , 
	`create_time` TIMESTAMP NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' , 
	`update_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' , 
	`version` INT(11) NOT NULL  DEFAULT 0 COMMENT '数据版本' , 
	`tenant_code` VARCHAR(36) NULL  DEFAULT 'annto' COMMENT '租户编码' , 
	`parent_order_no` VARCHAR(32) NULL  COMMENT '父单号' , 
	`order_no` VARCHAR(64) NULL  COMMENT '子单号' , 
	`customer_order_no` VARCHAR(32) NULL  COMMENT '客户订单号' , 
	`pick_appointment_require_flag` TINYINT(1) NULL  COMMENT '提货方是否要求预约' , 
	`arrive_appointment_require_flag` SMALLINT(1) NULL  COMMENT '送达是否要求预约' , 
	`pick_appointment_flag` SMALLINT(1) NULL  COMMENT '提货是否已预约' , 
	`arrive_appointment_flag` SMALLINT(1) NULL  COMMENT '送货是否已预约' , 
	`arrive_reschedule_times` INT(11) NULL  COMMENT '送货改约次数' , 
	`arrive_start_time` TIMESTAMP NULL  COMMENT '预约送达用户开始时间' , 
	`arrive_end_time` TIMESTAMP NULL  COMMENT '预约送达用户结束时间' , 
	`arrive_time_slot` VARCHAR(30) NULL  COMMENT '预约送货用户时间段' , 
	`arrive_no` VARCHAR(64) NULL  COMMENT '送货预约单号' , 
	`pick_start_time` TIMESTAMP NULL  COMMENT '预约提货开始时间，出库、自提出库、纯运输' , 
	`pick_end_time` TIMESTAMP NULL  COMMENT '预约提货结束时间' , 
	`pick_time_slot` VARCHAR(30) NULL  COMMENT '预约提货时间段' , 
	`pick_no` VARCHAR(64) NULL  COMMENT '提货预约单号' , 
	`install_type` VARCHAR(16) NULL  COMMENT '送装类型' , 
	`install_start_time` TIMESTAMP NULL  COMMENT '预约安装开始时间' , 
	`install_end_time` TIMESTAMP NULL  COMMENT '预约安装结束时间' , 
	`install_time_slot` VARCHAR(30) NULL  COMMENT '预约安装时间段' , 
	`unload_start_time` TIMESTAMP NULL  COMMENT '预约卸货开始时间，入库预约，调拨、分拨预约抵达时间' , 
	`unload_end_time` TIMESTAMP NULL  COMMENT '预约卸货结束时间' , 
	`unload_time_slot` VARCHAR(30) NULL  COMMENT '预约卸货时间段' , 
	`unload_no` VARCHAR(64) NULL  COMMENT '仓库卸货预约单号' , 
	`appoint_reason` VARCHAR(128) NULL  COMMENT '一级预约原因' , 
	`appoint_reason_name` VARCHAR(32) NULL  COMMENT '一级预约原因名称' , 
	`secend_appoint_reason` VARCHAR(128) NULL  COMMENT '二级预约原因' , 
	`secend_appoint_name` VARCHAR(128) NULL  COMMENT '二级预约原因名称' , 
	`exception_type` VARCHAR(32) NULL  COMMENT '预约异常类型' , 
	`exception_name` VARCHAR(128) NULL  COMMENT '预约异常名称' , 
	`delievery_exception` VARCHAR(128) NULL  COMMENT '售后配送异常原因' , 
	`delievery_exception_time` DATE NULL  COMMENT '配送异常操作时间' , 
	`revisiter` VARCHAR(10) NULL  COMMENT '回访人' , 
	`revisiter_mobile` VARCHAR(20) NULL  COMMENT '回访电话' , 
	PRIMARY KEY (`id`) , 
	UNIQUE KEY `index_order_no`(`order_no`) 
) ENGINE=INNODB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_unicode_ci' COMMENT='预约表';


/* Alter table in target */
ALTER TABLE `order_confirm` 
	ADD COLUMN `upstream_doc_type` varchar(8)  NULL COMMENT '是否仓间调拨' after `relation_order_no` , 
	ADD COLUMN `invoice_unit_code` varchar(32)  NULL COMMENT '全赔主体编码' after `upstream_doc_type`;
	

/* Alter table in target */
ALTER TABLE `order_confirm_package` 
	ADD COLUMN `package_code` varchar(64)  NULL COMMENT '箱号' after `order_no` ;


/* Alter table in target */
ALTER TABLE `order_extend` 
	ADD COLUMN `re_distribute` tinyint(1)   NULL COMMENT '1 二次派送 2 二次派送核销' after `sign_flag` ;

/* Alter table in target */
ALTER TABLE `order_info` 
	ADD COLUMN `expect_out_start_time` DATETIME   NULL COMMENT '要求出库开始时间' AFTER `consolidation_num` , 
	ADD COLUMN `expect_out_end_time` DATETIME   NULL COMMENT '要求出库结束时间' AFTER `expect_out_start_time` , 
	ADD COLUMN `expect_pick_start_time` DATETIME   NULL COMMENT '要求提货开始时间' AFTER `expect_out_end_time` , 
	ADD COLUMN `expect_pick_end_time` DATETIME   NULL COMMENT '要求提货结束时间' AFTER `expect_pick_start_time` ,
	ADD COLUMN `net_delivery_type` varchar(32)  NULL COMMENT '网点配送方式类型' after `expect_pick_end_time`,
	ADD COLUMN `business_category` varchar(16) DEFAULT NULL COMMENT '业务大类',
	ADD COLUMN `deliverypay_type` tinyint(4) DEFAULT 1 COMMENT '运费类型字段,寄付1/到付2，所有订单默认为寄付',
    ADD COLUMN `collection_flag` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT 'N' COMMENT '是否代收字段,是Y/否N，所有订单默认为',
    ADD COLUMN `collection_amount` decimal(18,8) DEFAULT NULL COMMENT '代收金额,不可为0或负数';



/* Alter table in target */
ALTER TABLE `task`
	ADD COLUMN `business_category` varchar(16) DEFAULT NULL COMMENT '业务大类';

/* Create table in target */
CREATE TABLE `order_info_mp`(
	`id` BIGINT(20) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT '主键' , 
	`remark` VARCHAR(255) NULL  COMMENT '备注' , 
	`delete_flag` SMALLINT(2) NOT NULL  DEFAULT 0 COMMENT '状态,0正常,1已删除' , 
	`create_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' , 
	`create_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '创建人员' , 
	`update_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' , 
	`update_user_code` VARCHAR(64) NULL  DEFAULT 'system' COMMENT '修改人员' , 
	`version` INT(11) NOT NULL  DEFAULT 0 COMMENT '数据版本' , 
	`tenant_code` VARCHAR(36) NULL  COMMENT '租户编码' , 
	`order_no` VARCHAR(32) NULL  COMMENT '订单号，系统生成' , 
	`customer_order_no` VARCHAR(64) NOT NULL  DEFAULT '0' COMMENT '客户订单号销售平台物流单号' , 
	`parent_order_no` VARCHAR(32) NOT NULL  DEFAULT '0' COMMENT '父订单号' , 
	`invoice_flag` TINYINT(1) NULL  COMMENT '是否需要发票,0：不需要1：需要' , 
	`total_gross_weight` DECIMAL(18,8) NULL  COMMENT '总毛量' , 
	`total_volume` DECIMAL(18,8) NULL  COMMENT '总体积' , 
	`total_net_weight` DECIMAL(18,8) NULL  COMMENT '总净重' , 
	`total_qty` DECIMAL(18,8) NULL  COMMENT '总数量' , 
	`distribution_flag` TINYINT(1) NULL  COMMENT '是否分拨标识，0：否，1：是' , 
	`distribution_site_code` VARCHAR(64) NULL  COMMENT '一级分拨平台编码' , 
	`distribution_wh_code` VARCHAR(64) NULL  COMMENT '一级分拨仓编码' , 
	`next_distribution_site_code` VARCHAR(64) NULL  COMMENT '二级分拨平台编码' , 
	`next_distribution_wh_code` VARCHAR(64) NULL  COMMENT '二级分拨仓编码' , 
	`freight_basis` VARCHAR(16) NULL  COMMENT '测量标准重货/轻货' , 
	`contract_customer_code` VARCHAR(32) NULL  COMMENT '合同客户编码' , 
	`contract_customer_name` VARCHAR(32) NULL  COMMENT '合同客户名称' , 
	`dispatch_no` VARCHAR(32) NULL  COMMENT '发车单号' , 
	`shop_id` VARCHAR(32) NULL  COMMENT '店铺编码' , 
	`shop_name` VARCHAR(128) NULL  COMMENT '店铺名称' , 
	`upper_order_time` DATETIME NULL  COMMENT 'ERP审核时间(接单时间)' , 
	`delivery_carrier_code` VARCHAR(32) NULL  COMMENT '实际承运商编码,自提:MJZT' , 
	`buyer_remark` VARCHAR(2048) NULL  COMMENT '买家备注' , 
	`logistic_mode` VARCHAR(8) NULL  COMMENT '运作模式，仓配一体、仓配分离' , 
	`upper_receiver_code` VARCHAR(32) NULL  COMMENT '上游收货单位编码' , 
	`upper_sender_code` VARCHAR(32) NULL  COMMENT '上游发货单位编码' , 
	`upper_wh_code` VARCHAR(32) NULL  COMMENT '上游仓库编码' , 
	`upper_wh_name` VARCHAR(128) NULL  COMMENT '上游仓库名称' , 
	`upper_customer_code` VARCHAR(64) NULL  COMMENT '上游客户编码' , 
	`upper_customer_name` VARCHAR(128) NULL  COMMENT '上游客户名称' , 
	`upper_order_type` VARCHAR(16) NULL  COMMENT '上游订单类型' , 
	`order_source` VARCHAR(8) NULL  COMMENT '订单来源：API：接口接入，VIEW：视图引单，FINANCE：财务单引入，' , 
	`invoice_unit_code` VARCHAR(32) NULL  COMMENT '会计主体' , 
	`invoice_unit_name` VARCHAR(128) NULL  COMMENT '会计主体名称' , 
	`appointment_type` VARCHAR(32) NULL  COMMENT '预约类型' , 
	`appointment_time` DATETIME NULL  COMMENT '预约时间' , 
	`appointment_reason` VARCHAR(128) NULL  COMMENT '预约异常原因' , 
	`exception_type` VARCHAR(64) NULL  COMMENT '订单审核异常类型' , 
	`exception_desc` VARCHAR(4095) NULL  COMMENT '订单审核异常描述' , 
	`handle_split_flag` TINYINT(1) NULL  COMMENT '手动拆单标识' , 
	`network_code` VARCHAR(32) NULL  COMMENT '网点编码' , 
	`network_name` VARCHAR(64) NULL  COMMENT '网点名称' , 
	`network_contact` VARCHAR(32) NULL  COMMENT '网点联系人' , 
	`network_tel` VARCHAR(64) NULL  COMMENT '网点联系电话' , 
	`network_phone` VARCHAR(64) NULL  COMMENT '网点联系手机' , 
	`network_province_code` VARCHAR(32) NULL  COMMENT '网点省' , 
	`network_city_code` VARCHAR(32) NULL  COMMENT '网点市' , 
	`network_district_code` VARCHAR(32) NULL  COMMENT '网点区县' , 
	`network_town_code` VARCHAR(32) NULL  COMMENT '网点乡镇/街道' , 
	`network_addr` VARCHAR(512) NULL  COMMENT '网点详细地址' , 
	`network_city_name` VARCHAR(64) NULL  COMMENT '网点-市-名称' , 
	`network_district_name` VARCHAR(64) NULL  COMMENT '网点-县-名称' , 
	`network_town_name` VARCHAR(64) NULL  COMMENT '网点-镇-名称' , 
	`network_province_name` VARCHAR(64) NULL  COMMENT '网点-省-名称' , 
	`net_lng` DECIMAL(18,8) NULL  COMMENT '网点经度' , 
	`net_lat` DECIMAL(18,8) NULL  COMMENT '网点维度' , 
	`reassignment_flag` TINYINT(1) NULL  COMMENT '是否改派，0：否，1：是' , 
	`total_amount` DECIMAL(18,8) NULL  COMMENT '总价' , 
	`apart_status` VARCHAR(8) NULL  COMMENT '拆单状态：已拆单、部分拆单' , 
	`apart_type` VARCHAR(16) NULL  COMMENT '拆单类型：手动、单品单件' , 
	`oppintment_status` TINYINT(1) NULL  COMMENT '预约状态0未预约，1已预约' , 
	`customer_group` VARCHAR(10) NULL  COMMENT '客户系' , 
	`upper_receiver_name` VARCHAR(255) NULL  COMMENT '上游收货名称' , 
	`upper_sender_name` VARCHAR(255) NULL  COMMENT '上游发货名称' , 
	`upper_aging_code` VARCHAR(64) NULL  COMMENT '上游客户时效编码' , 
	`identify_status` VARCHAR(32) NULL  COMMENT '鉴定类型' , 
	`pay_type` VARCHAR(32) NULL  COMMENT '付款方式' , 
	`load_type` VARCHAR(8) NULL  COMMENT '整车零担标识,取值对照为数据字典:LOAD_TYPE' , 
	`plan_order_flag` TINYINT(1) NULL  DEFAULT 0 COMMENT '模糊订单标志0：否，1：是' , 
	`equipment_type` VARCHAR(64) NULL  COMMENT '车型属性' , 
	`equipment_name` VARCHAR(64) NULL  COMMENT '车型属名称' , 
	`distribution_Limit` INT(10) NULL  COMMENT '分拨次数' , 
	`distribution_num` TINYINT(1) NULL  DEFAULT 1 COMMENT '当前段号' , 
	`delivered_verify_flag` TINYINT(1) NULL  DEFAULT 0 COMMENT '是否需要妥投码验证:1是,0否' , 
	`appoint_order_no` VARCHAR(32) NULL  COMMENT '商超预约单号' , 
	`overdue_reason1` VARCHAR(64) NULL  COMMENT '出库/入库逾期一级原因' , 
	`overdue_reason2` VARCHAR(64) NULL  COMMENT '出库/入库逾期二级原因' , 
	`overdue_time` DATETIME NULL  COMMENT '逾期登记时间' , 
	`contact_name` VARCHAR(128) NULL  COMMENT '出库/入库联系人' , 
	`contact_mobile` VARCHAR(64) NULL  COMMENT '出库/入库联系手机' , 
	`overdue_remark` TEXT NULL  COMMENT '逾期备注' , 
	`arrive_overdue_reason1` VARCHAR(64) NULL  COMMENT '到货逾期一级原因' , 
	`arrive_overdue_reason2` VARCHAR(64) NULL  COMMENT '到货逾期二级原因' , 
	`arrive_overdue_contact_name` VARCHAR(128) NULL  COMMENT '到货逾期联系人' , 
	`arrive_overdue_contact_mobile` VARCHAR(64) NULL  COMMENT '到货逾期联系手机' , 
	`arrive_overdue_remark` TEXT NULL  COMMENT '到货逾期备注' , 
	`arrive_overdue_time` DATETIME NULL  COMMENT '到货逾期登记时间' , 
	`overdue_exclude_flag` SMALLINT(2) NULL  COMMENT '是否剔除逾期考核标识0:不剔除,1:剔除' , 
	`aging_product` VARCHAR(64) NULL  COMMENT '时效产品' , 
	`aging_start_time` VARCHAR(32) NULL  COMMENT '时效起点' , 
	`order_start_time` DATETIME NULL  COMMENT '截单时间开始' , 
	`order_end_time` DATETIME NULL  COMMENT '截单时间结束' , 
	`hold_flag` TINYINT(10) NULL  DEFAULT 0 COMMENT 'hold标识(1:是,0:否)' , 
	`sign_code` VARCHAR(50) NULL  COMMENT '签收码' , 
	`act_sign_code` VARCHAR(50) NULL  COMMENT '回收签收码' , 
	`sign_code_cnt` TINYINT(1) NULL  DEFAULT 0 COMMENT '签收码发送次数' , 
	`sign_flag` TINYINT(1) NULL  COMMENT '签收码验证结果0失败1成功' , 
	PRIMARY KEY (`id`) 
) ENGINE=INNODB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_unicode_ci' COMMENT='订单子单表中台';


/* Create table in target */
CREATE TABLE `order_info_otp`(
	`id` BIGINT(20) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT '主键' , 
	`remark` VARCHAR(255) NULL  COMMENT '备注' , 
	`delete_flag` SMALLINT(2) NOT NULL  DEFAULT 0 COMMENT '状态,0正常,1已删除' , 
	`create_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' , 
	`create_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '创建人员' , 
	`update_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' , 
	`update_user_code` VARCHAR(64) NULL  DEFAULT 'system' COMMENT '修改人员' , 
	`version` INT(11) NOT NULL  DEFAULT 0 COMMENT '数据版本' , 
	`tenant_code` VARCHAR(36) NULL  COMMENT '租户编码' , 
	`customer_order_no` VARCHAR(64) NULL  COMMENT '单号,用于主从表关联' , 
	`order_no` VARCHAR(32) NULL  COMMENT '订单号，系统生成' , 
	`parent_order_no` VARCHAR(32) NOT NULL  DEFAULT '0' COMMENT '父订单号' , 
	`target_site_code` VARCHAR(32) NULL  COMMENT '收货平台编码' , 
	`service_order_no` VARCHAR(32) NULL  COMMENT 'CCSS工单号' , 
	`specimen_type` VARCHAR(8) NULL  COMMENT '上撤样' , 
	`sc_pos_flag` TINYINT(1) NULL  COMMENT '商超标志0：否，1：是' , 
	`join_type` VARCHAR(16) NULL  COMMENT '协同类型SHARE:调剂标识;transInv:货物转移标志' , 
	`entity_id` INT(11) NULL  COMMENT '实体' , 
	`contract_no` VARCHAR(32) NULL  COMMENT '合同号' , 
	`spec_carrier_flag` TINYINT(1) NULL  COMMENT '是否指定承运商,N：不指定Y：指定' , 
	`upper_source_customer_code` VARCHAR(32) NULL  COMMENT '上游源客户编码' , 
	`target_customer_code` VARCHAR(32) NULL  COMMENT '目标客户' , 
	`project_classify` VARCHAR(32) NULL  COMMENT '项目分类' , 
	`third_flag` TINYINT(1) NULL  COMMENT '是否第三方,0：否，1：是' , 
	`service_type` TINYINT(6) NULL  COMMENT '是否上门取件,0：否，1：是' , 
	`emergence_flag` TINYINT(1) NULL  COMMENT '是否紧急订单,0：否，1：是' , 
	`outsource_flag` TINYINT(1) NULL  COMMENT '是否小电,0：否，1：是' , 
	`order_rp_flag` VARCHAR(8) NULL  COMMENT '收付标志(是否产生装卸费标识)' , 
	`upper_reference_id` VARCHAR(16) NULL  COMMENT '上游订单关联ID' , 
	`join_reason` VARCHAR(32) NULL  COMMENT '调剂失败原因' , 
	`upstream_doc_type` VARCHAR(8) NULL  COMMENT '是否仓间调拨' , 
	`shop_guide_name` VARCHAR(64) NULL  COMMENT '导购姓名' , 
	`shop_guide_tel` VARCHAR(32) NULL  COMMENT '导购电话' , 
	`print_price_flag` TINYINT(4) NULL  COMMENT '是否打印单价' , 
	`print_notax_price_flag` TINYINT(4) NULL  COMMENT '是否打印不含税单价' , 
	`print_barcode` TINYINT(4) NULL  COMMENT '是否打印条码' , 
	`if_up_stairs` VARCHAR(4) NULL  COMMENT '是否上楼费（0：不上楼，1：上楼）' , 
	`target_wh_code` VARCHAR(32) NULL  COMMENT '收货仓库编码' , 
	`work_order_no` VARCHAR(64) NULL  COMMENT 'C2M工单号' , 
	`c2m_type` VARCHAR(64) NULL  COMMENT 'C2M标识' , 
	`gray_flag` VARCHAR(64) NULL  COMMENT '菜鸟小件标示' , 
	`cn_dispatch` TINYINT(1) NULL  DEFAULT 0 COMMENT '预配载标志0：否，1：是' , 
	PRIMARY KEY (`id`) 
) ENGINE=INNODB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_unicode_ci' COMMENT='订单子单表城配';


/* Create table in target */
CREATE TABLE `order_zeebe_relation`(
	`id` BIGINT(20) UNSIGNED NOT NULL  AUTO_INCREMENT COMMENT '主键' , 
	`delete_flag` SMALLINT(2) NOT NULL  DEFAULT 0 COMMENT '状态,0可用,1不可用' , 
	`create_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' , 
	`create_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '创建人员' , 
	`update_time` TIMESTAMP NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' , 
	`update_user_code` VARCHAR(64) NOT NULL  DEFAULT 'system' COMMENT '修改人员' , 
	`remark` VARCHAR(255) NULL  COMMENT '备注' , 
	`version` INT(11) NOT NULL  DEFAULT 0 COMMENT '数据版本' , 
	`order_no` VARCHAR(32) NULL  DEFAULT '1' COMMENT '订单号' , 
	`workflow_instance_key` BIGINT(20) UNSIGNED NOT NULL  COMMENT 'zeebe流程主键' , 
	PRIMARY KEY (`id`) , 
	KEY `idx_order_no`(`order_no`) 
) ENGINE=INNODB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_unicode_ci' COMMENT='订单流程关联表';

-- 增加索引
ALTER TABLE logistics_otp_order.`customer_aging_config`
ADD INDEX `idx_site_code` (`site_code`) USING BTREE ;

