package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.bean.JobRequest;
import com.midea.logistics.otp.bean.OrderRequest;
import com.midea.logistics.otp.bean.OrderResponse;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.helper.ListUtil;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.common.utils.BeanUtil;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.converged.domain.dto.AllOrderInfoDto;
import com.midea.logistics.otp.order.converged.domain.dto.TaskStatusUpdateBatch;
import com.midea.logistics.otp.order.converged.domain.request.ChangeWarehouseTaskRequest;
import com.midea.logistics.otp.order.converged.domain.request.OrderChangedReceiveDateRequest;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.custom.OrderBatchSearchRequest;
import com.midea.logistics.otp.order.domain.dto.AiVoiceAppointDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.WmsInChangeWhRequest;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskAddress;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.domain.bean.TaskZsjx;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDetailResponse;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.midea.logistics.otp.task.domain.bean.custom.TaskInfoAndDetail;
import com.midea.logistics.otp.task.domain.bean.custom.TaskSearchResponse;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskDetailsResponse;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskResponse;
import com.midea.logistics.otp.task.domain.bean.response.TaskDetailsResponse;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.domain.request.SearchTaskRequest;
import com.midea.logistics.otp.task.mapper.custom.TaskCustomMapper;
import com.midea.logistics.otp.task.service.ITaskAddressService;
import com.midea.logistics.otp.task.service.ITaskItemService;
import com.midea.logistics.otp.task.service.ITaskService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.beans.PropertyEditorSupport;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskController
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:任务明细表控制层
 */

@RestController
public class TaskRest extends BaseController<Task> {
    private final Logger logger = LoggerFactory.getLogger(TaskRest.class);

    @Autowired
    private ITaskService iTaskService;
    @Autowired
    private ITaskItemService iTaskItemService;
    @Autowired
    private ITaskAddressService iTaskAddressService;
    @Autowired
    private TaskCustomMapper taskCustomMapper;
    @Autowired
    private Validatorhelper validatorhelper;
    
    /**
     * !@无忧3.0 - 7、批量保存所有任务信息
     * @param request
     * @return
     */
    @PostMapping(TaskRouters.WFR_V3_SAVE_ALL_TASK)
    public JsonResponse<List<AllOrderInfoDto>> wfrV3SaveAllTask(@RequestBody List<AllOrderInfoDto> obj, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            List<AllOrderInfoDto> result= iTaskService.wfrV3SaveAllTask(obj);
            jsonResponse.data = result;
        };
        return doing.go(request, logger);
    }
    /**
     * 新增
     *
     * @return
     */
    @RequestMapping(value = "/task", method = RequestMethod.POST)
    public JsonResponse<Integer> create(@Valid @RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.saveTask(task);
        };
        return doing.go(request, logger);
    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task();
            taskQr.setId(id);
            Task task = iTaskService.selectOneTask(taskQr);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);

    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.DELETE)
    public JsonResponse<Integer> deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task(id);
            jsonResponse.data = iTaskService.deleteTask(taskQr);
        };
        return doing.go(request, logger);

    }
    
    /**
    * @description: 删除任务且递增orderno
    * @param: [id, request]
    * @return: com.mideaframework.core.web.JsonResponse<java.lang.Integer>
    * @author: 陈永培
    * @createtime: 2025/4/11 15:01
    */
    @RequestMapping(value = "/deleteByIdAndIncrementOrderNo/{id}", method = RequestMethod.DELETE)
    public JsonResponse<Integer> deleteByIdAndIncrementOrderNo(@PathVariable("id") Long id, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task(id);
            jsonResponse.data = iTaskService.deleteByIdAndIncrementOrderNo(taskQr);
        };
        return doing.go(request, logger);

    }

    /**
     * @param task
     * @return
     */
    @RequestMapping(value = "/task/{id}", method = RequestMethod.PUT)
    public JsonResponse<Integer> update(@PathVariable("id") Long id,  /**@Valid*/@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            task.setId(id);
            task.setVersion(task.getVersion());
            jsonResponse.data = iTaskService.updateTask(task);
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = "/tasks", method = RequestMethod.PUT)
    public JsonResponse batchUpdate(@RequestBody List<Task> tasks, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.batchUpdate(tasks);
        };
        return doing.go(request, logger);
    }

    /**
     * @param tasks
     * @return
     */
    @RequestMapping(value = "/tasks/batchUpdateByTask", method = RequestMethod.PUT)
    public JsonResponse<Integer> batchUpdateByTask(@RequestBody List<Task> tasks, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.batchUpdateByTask(tasks);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }


    /**
     * @param tasks
     * @return
     */
    @RequestMapping(value = "/tasks/batchUpdateTaskForNodeStatus", method = RequestMethod.PUT)
    public JsonResponse<Integer> batchUpdateTaskForNodeStatus(@RequestBody List<Task> tasks, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.batchUpdateTaskForNodeStatus(tasks);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }


    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/tasks", method = RequestMethod.GET)
    public JsonResponse<Task> search(@SpringQueryMap Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<Task> PageResponse = iTaskService.selectTaskPage(task);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }

    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/tasks", method = RequestMethod.POST)
    public JsonResponse<Task> searchByPost(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<Task> PageResponse = iTaskService.selectTaskPage(task);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }

    /**
     * @return 查询单个
     */
    @RequestMapping(value = "/tasks/selectOne", method = RequestMethod.GET)
    public JsonResponse<Task> selectOne(@SpringQueryMap Task search, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task task = iTaskService.selectOneTask(search);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);
    }

    /**
     * @return 查询单个
     */
    @RequestMapping(value = TaskRouters.TASK_SEARCH_ONE_BYRDIORRDO, method = RequestMethod.GET)
    public JsonResponse<Task> selectOneByRDIorRDO(@SpringQueryMap Task search, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task task = iTaskService.selectOneByRDIorRDO(search);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);
    }

    /**
     * @return 查询单个
     */
    @RequestMapping(value = TaskRouters.TASK_SEARCH_ONE_BYRDIORRDOYS, method = RequestMethod.GET)
    public JsonResponse<Task> selectOneByRDIorRDOorYS(@SpringQueryMap Task search, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task task = iTaskService.selectOneByRDIorRDOorYS(search);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);
    }

    /**
     * @return 查询单个
     */
    @RequestMapping(value = TaskRouters.TASK_SEARCH_ONE_BYRDIORRDOIT, method = RequestMethod.GET)
    public JsonResponse<Task> selectOneByRDIorRDOorIT(@SpringQueryMap Task search, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task task = iTaskService.selectOneByRDIorRDOorIT(search);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);
    }

    /**
     * @description: 根据分拨段数去查询最新的任务
     * @param: [search]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/6/28 14:08
     */
    @PostMapping(value = TaskRouters.GET_LASTEST_BY_DISTRIBUTION_NUM)
    public JsonResponse<Task> getLastestByDistributionNum(@RequestBody Task search , HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            search.setPageSize(CommonConstant.PAGE_SIZE);
            List<Task> list = iTaskService.selectTaskByIndex(search);
            if (null != list) {
                Task task = list.stream().sorted(Comparator.comparing(Task::getDistributionNum).reversed()).sorted(Comparator.comparing(Task::getCreateTime).reversed()).findFirst().orElse(null);
                jsonResponse.data = task;
            }
        };
        return doing.go(request, logger);
    }
    
    /**
     * @description: 根据分拨段数去查询最新的任务（排除关闭/取消）
     * @param: [search]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/6/28 14:08
     */
    @PostMapping(value = TaskRouters.GET_LASTEST_BY_DISTRIBUTION_NUM_NOT_CCI)
    public JsonResponse<Task> getLastestByDistributionNumNotCCI(@RequestBody Task search , HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            search.setPageSize(CommonConstant.PAGE_SIZE);
            List<Task> list = iTaskService.selectTaskByIndex(search);
            if (null != list) {
                Task task = list.stream().filter(a-> OrderStatus.isNotCCI(a.getOrderStatus(),a.getExcuteStatus())).sorted(Comparator.comparing(Task::getDistributionNum).reversed()).sorted(Comparator.comparing(Task::getCreateTime).reversed()).findFirst().orElse(null);
                jsonResponse.data = task;
            }
        };
        return doing.go(request, logger);
    }

    /**
    * @description: 按照创建时间最新的找出其中任务最新的一条
    * @param: [search, request]
    * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.otp.task.domain.bean.Task>
    * @author: 陈永培
    * @createtime: 2022/10/12 9:26
    */
    @PostMapping(value = TaskRouters.SELECT_ONE_BY_CREATETIME_DESC)
    public JsonResponse<Task> selectOneByCreateTimeDesc(@RequestBody Task search, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            search.setPageSize(CommonConstant.PAGE_SIZE);
            List<Task> list = iTaskService.selectTaskByIndex(search);
            if (null != list) {
                Task task = list.stream().sorted(Comparator.comparing(Task::getCreateTime).reversed()).findFirst().orElse(null);
                jsonResponse.data = task;
            }
        };
        return doing.go(request, logger);
    }
    
    /**
    * @description: 按照创建时间最新的找出其中任务最新的一条 （排除关闭/取消）
    * @param: [search, request]
    * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.otp.task.domain.bean.Task>
    * @author: 陈永培
    * @createtime: 2022/10/12 9:26
    */
    @PostMapping(value = TaskRouters.SELECT_ONE_BY_CREATETIME_DESC_NOT_CCI)
    public JsonResponse<Task> selectOneByCreateTimeDescNotCCI(@RequestBody Task search, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            search.setPageSize(CommonConstant.PAGE_SIZE);
            List<Task> list = iTaskService.selectTaskByIndex(search);
            if (null != list) {
                Task task = list.stream().filter(a-> OrderStatus.isNotCCI(a.getOrderStatus(),a.getExcuteStatus())).sorted(Comparator.comparing(Task::getCreateTime).reversed()).findFirst().orElse(null);
                jsonResponse.data = task;
            }
        };
        return doing.go(request, logger);
    }
    
    
    //根据子单号查询子订单-任务状态
    @RequestMapping(value = "/selectTask", method = RequestMethod.POST)
    public JsonResponse<List<Task>> selectTask(@RequestBody List<String> parentOrderNoes, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            List<Task> Response = iTaskService.selectTask(parentOrderNoes);
            jsonResponse.data = Response;
        };
        return doing.go(request, logger);

    }
    /**
     * 查询多个
     * @param task
     * @return
     */
    @RequestMapping(value = TaskRouters.TASKS_LIST, method = RequestMethod.GET)
    public JsonResponse<List<Task>> list(@SpringQueryMap Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            task.setPageSize(CommonConstant.LIST_PAGE_SIZE);
            List<Task> list = iTaskService.selectTaskByIndex(task);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);
    }

    /**
     * 查询多个-POST
     * @param task
     * @return
     */
    @RequestMapping(value = TaskRouters.TASKS_LIST_POST, method = RequestMethod.POST)
    public JsonResponse<List<Task>> listWithPost(@RequestBody Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            task.setPageSize(CommonConstant.LIST_PAGE_SIZE);
            List<Task> list = iTaskService.selectTaskByIndex(task);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);
    }


    /**
     * @param task
     * @return
     */
    @RequestMapping(value = "/task", method = RequestMethod.PUT)
    public JsonResponse<Integer> update(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.update(task);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }

    /**
     * 批量修改任务关闭状态
     * @param statusUpdateBatch
     * @return
     */
    @RequestMapping(value = "/task/updateStatusByTaskNos", method = RequestMethod.PUT)
    JsonResponse<Integer> updateStatusByTaskNos(@RequestBody @Validated TaskStatusUpdateBatch statusUpdateBatch, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.updateStatusByTaskNos(statusUpdateBatch);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = "/taskDeleteOfTI", method = RequestMethod.PUT)
    public JsonResponse deleteTaskOfTI(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.deleteTaskOfTI(task);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }

    /**
     * 清理分拨信息
     *
     * @param task
     * @return
     */
    @RequestMapping(value = "/clearDistribution", method = RequestMethod.PUT)
    public JsonResponse<Integer> clearDistribution(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.clearDistribution(task);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }

    /**
     * 更新分拨信息
     *
     * @param task
     * @return
     */
    @RequestMapping(value = "/updateDistribution", method = RequestMethod.PUT)
    public JsonResponse<Integer> updateDistribution(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.updateDistribution(task);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }


    /**
     * 更新发车单号
     *
     * @param tasks
     * @return
     */
    @RequestMapping(value = "/updateDispatchNo", method = RequestMethod.PUT)
    public JsonResponse<Integer> updateDispatchNo(@RequestBody List<Task> tasks, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.updateDispatchNo(tasks);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = "/updateLine", method = RequestMethod.PUT)
    JsonResponse<Integer> updateLine(@RequestBody List<Task> tasks, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            Integer count = iTaskService.updateLine(tasks);
            jsonResponse.data = count;
        };
        return doing.go(request, logger);
    }

    /**
     * 根据订单号找到task
     *
     * @param orderNo
     * @return
     */
    @RequestMapping(value = TaskRouters.SELECT_TASK_BY_ORDER_NO, method = RequestMethod.GET)
    public JsonResponse<Task> selectTaskByOrderNo(String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task();
            taskQr.setOrderNo(orderNo);
            Task task = iTaskService.selectOneTask(taskQr);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);
    }

    /**
     * 根据订单号找到task列表
     *
     * @param orderNo
     * @return
     */
    @RequestMapping(value = TaskRouters.SELECT_TASK_LIST_BY_ORDER_NO, method = RequestMethod.GET)
    public JsonResponse<List<Task>> selectTaskListByOrderNo(String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task();
            taskQr.setOrderNo(orderNo);
            jsonResponse.data = iTaskService.selectTaskByIndex(taskQr);
        };
        return doing.go(request, logger);
    }

    /**
     * 根据parentOrderNo找到task
     *
     * @param parentOrderNo
     * @return
     */
    @RequestMapping(value = TaskRouters.SELECT_TASK_BY_PARENTORDER_NO, method = RequestMethod.GET)
    public JsonResponse<Task> selectTaskByParentOrderNo(String parentOrderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task();
            taskQr.setParentOrderNo(parentOrderNo);
            Task task = iTaskService.selectOneTask(taskQr);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);
    }

    /**
     * 根据TaskNo找到task
     *
     * @param taskNo
     * @return
     */
    @RequestMapping(value = TaskRouters.SELECT_TASK_BY_TASK_NO, method = RequestMethod.GET)
    public JsonResponse<Task> selectTaskByTaskNo(String taskNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task taskQr = new Task();
            taskQr.setTaskNo(taskNo);
            Task task = iTaskService.selectOneTask(taskQr);
            jsonResponse.data = task;
        };
        return doing.go(request, logger);
    }

    @PostMapping(TaskRouters.TASK_GET_TASKS_BY_TASKNOS)
    public JsonResponse<List<Task>> selectTaskListByTaskNoList(@RequestBody List<String> taskNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.getTasksByTaskNos(taskNos);
        };
        return doing.go(request, logger);
    }


    /**
     * 统计方量
     *
     * @param task
     * @return
     */
    @GetMapping(value = TaskRouters.TASK_DELIVERY_COUNT)
    public JsonResponse<Task> deliveryCount(@SpringQueryMap Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Task taskR = iTaskService.deliveryCount(task);
            taskR.setOrderType(task.getOrderType());
            taskR.setSiteCode(task.getSiteCode());
            taskR.setCustomerCode(task.getCustomerCode());
            jsonResponse.data = taskR;
        };
        return doing.go(request, logger);
    }

    @PostMapping(TaskRouters.TASK_GET_TASKS_BY_ORDERNOS)
    public JsonResponse<List<Task>> getTasksByOrderNos(@RequestBody List<String> orderNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.getTasksByOrderNos(orderNos);
        };
        return doing.go(request, logger);
    }

    @PostMapping(TaskRouters.TASK_GET_TASKS_AND_ADDR_BY_ORDERNOS)
    public JsonResponse<List<TaskDetailsResponse>> getTasksAndAddrByOrderNos(@RequestBody List<String> orderNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.getTasksAndAddrByOrderNos(orderNos);
        };
        return doing.go(request, logger);
    }


    /**
     * @return 查询集合
     */
    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskList(SearchTaskRequest searchTaskRequest, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<SearchTaskResponse> PageResponse = iTaskService.searchTaskList(searchTaskRequest);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);
    }

    /**
     * @return 查询集合 支持 post
     */
    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST)
    public JsonResponse<PageResponse<SearchTaskResponse>> searchTaskListPost(@RequestBody SearchTaskRequest searchTaskRequest, HttpServletRequest request) {
        return searchTaskList(searchTaskRequest, request);
    }

    /**
     * @return 查询数量
     */
    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_COUNT)
    public JsonResponse<Integer> searchTaskCount(SearchTaskRequest searchTaskRequest, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            //是否精准配送
            if (null != searchTaskRequest.getIsPreciseDelivery()) {
                searchTaskRequest.setUpperAgingCode("JZP");
            }
            // 七天内不放弃时间索引 去掉时间索引控制 1是 0否
            if(BeanUtil.diffDay(searchTaskRequest.getCreateStartTime(), searchTaskRequest.getCreateEndTime(), 3)){
                searchTaskRequest.setIgnoreIndex(1);
            }

            jsonResponse.data = iTaskService.searchTaskCount(searchTaskRequest);
        };
        return doing.go(request, logger);
    }

    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_COUNT)
    public JsonResponse<Integer> searchTaskCountPost(@RequestBody SearchTaskRequest searchTaskRequest, HttpServletRequest request) {
        return searchTaskCount(searchTaskRequest, request);
    }

    /**
     * @return 查询集合
     */
    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_OUT_LIST)
    public JsonResponse searchTaskOutList(SearchTaskRequest searchTaskRequest, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task search = new Task();
            PageResponse<SearchTaskResponse>  pagingData = new PageResponse<>();
            search.setTaskNo(searchTaskRequest.getTaskNo());
            Task task = iTaskService.selectOneTask(search);
            if (null != task){
                //查询 whCode + siteCode+ orderStatus = ‘发货’ + executeOrderStatus < ‘未关闭 ’ + taskType=OUT && joinType = TRANS_INV
                searchTaskRequest.setWhCode(task.getWhCode());
                searchTaskRequest.setSiteCode(task.getSiteCode());
                searchTaskRequest.setOrderStatus(OrderStatus.DELIVERED.getKey());
                searchTaskRequest.setExcuteStatus(ExcuteStatus.CLOSED.getKey());
                searchTaskRequest.setTaskTypeList(Arrays.asList(TaskType.OT.getKey(),TaskType.ADO.getKey()));
                searchTaskRequest.setJoinType(JoinType.TRANS_INV.getKey());
                pagingData = iTaskService.searchTaskOutList(searchTaskRequest);
            }
            jsonResponse.data = pagingData;
        };
        return doing.go(request, logger);
    }



    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_DETAILS)
    public JsonResponse<SearchTaskDetailsResponse> searchTaskDetails(@RequestParam("taskNo") String taskNo, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.searchTaskDetails(taskNo);
        };
        return doing.go(request, logger);
    }

    @PostMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST_BY_TASKNOS)
    public JsonResponse<List<Task>> searchTaskListByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<Task> response = iTaskService.searchTaskListByTaskNos(taskNos);
            jsonResponse.data = response;
        };
        return doing.go(request, logger);
    }

    @GetMapping(value = TaskRouters.QUERY_TASK_LAST)
    public Task queryByTaskLast(Task taskQr, HttpServletRequest request) {
        Task task = null;
        if (StringUtils.isNotBlank(taskQr.getOrderNo())) {
            task = iTaskService.queryByTaskLast(taskQr);
        }
        return task;
    }

    /**
     * 当前最新任务
     *
     * @param taskQr
     * @param request
     * @return
     */
    @GetMapping(value = TaskRouters.QUERY_TASK_LAST_NOW)
    public Task queryByTaskLastNow(Task taskQr, HttpServletRequest request) {
        Task task = null;
        if (StringUtils.isNotBlank(taskQr.getOrderNo())) {
            task = iTaskService.queryByTaskLastNow(taskQr);
        }
        return task;
    }

    /**
     * @return 查询集合
     */
    @GetMapping(value = TaskRouters.TASK_SEARCH_TASK_LIST_DO_RDO)
    public JsonResponse<List<Task>> searchTaskListDoRdo(SearchTaskRequest searchTaskRequest, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<Task> PageResponse = iTaskService.searchTaskListDoRdo(searchTaskRequest);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);
    }

    /**
     * @return 查询集合
     */
    @PostMapping(value = "/task/search/task/list/byDate")
    public JsonResponse<List<TaskZsjx>> searchTaskListByDate(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            List<TaskZsjx> PageResponse = iTaskService.searchTaskListByDate(task);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);
    }

    /**
     * @return 查询集合
     */
    @GetMapping(value = TaskRouters.LIST_TASK_BY_PHONE)
    public JsonResponse<PageResponse<OrderResponse>> listTask(OrderRequest orderRequest, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            //遍历校验参数
            List<String> validate = validatorhelper.validate(orderRequest);
            if(StringUtils.isBlank(orderRequest.getPhoneNo()) && StringUtils.isBlank(orderRequest.getSenderMobile())){
                validate.add("电话不能为空！");
            }
            if (!CollectionUtils.isEmpty(validate)) {
                throw new BusinessException(BaseCodeEnum.REQUEST_NULL.getCode(), validate.get(0).toString());
            }
            PageResponse<OrderResponse> response = iTaskService.listTask(orderRequest);
            jsonResponse.data = response;
        };
        return doing.go(request, logger);
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(Date.class, new MyDateEditor());
    }
    private class MyDateEditor extends PropertyEditorSupport {
        @Override
        public void setAsText(String text) throws IllegalArgumentException {

            //通过两次异常的处理可以，绑定两次日期的格式
            SimpleDateFormat format = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
            Date date = null;
            try {
                date = format.parse(text);
            } catch (ParseException e) {
                try {
                    format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    date = format.parse(text);
                } catch (ParseException e1) {
                    logger.error("系统异常:"+e1);
                }
            }
            setValue(date);
        }
    }

    @RequestMapping(value = TaskRouters.UPDATE_TASK_CREATE_TIME,method = RequestMethod.POST)
    public JsonResponse updateTaskCreateTime(@RequestBody List<OrderChangedReceiveDateRequest> orderChangedReceiveDateRequests, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.updateTaskCreateTime(orderChangedReceiveDateRequests);
        };
        return doing.go(request, logger);
    }

    @PostMapping(value = "/tasks/batchUpdateByParentOrderNo")
    public JsonResponse batchUpdateByParentOrderNo(@RequestBody List<Task> tasks, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.batchUpdateByParentOrderNo(tasks);
        };
        return doing.go(request, logger);
    }

    @PostMapping(value = TaskRouters.SEARCH_CANCEL_TASK_BY_UPDATE_TIME)
    public JsonResponse searchCancelTaskToday(HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Task task = new Task();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(new Date().getTime()), ZoneId.systemDefault());
            LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
            task.setUpdateTime(Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant()));
            jsonResponse.data = iTaskService.selectCancelTaskByUpdateTime(task);
        };
        return doing.go(request, logger);
    }

    /**
     * 查询到车登记任务
     */
    @PostMapping(value = TaskRouters.SEARCH_CAR_ARRIVED_TASK)
    public JsonResponse searchCarArrivedTask(@RequestBody TaskExt task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.searchCarArrivedTask(task);
        };
        return doing.go(request, logger);
    }

    /**
     * @param task
     * @return
     */
    @RequestMapping(value = "/updateTaskByIndex", method = RequestMethod.POST)
    public JsonResponse<Integer> updateTaskByIndex(@RequestBody Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
             jsonResponse.data = iTaskService.updateTaskByIndex(task);
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = TaskRouters.UPDATE_TASK_CREATE_TIME_AND_FLAG,method = RequestMethod.POST)
    public JsonResponse updateTaskCreateTimeAndFlag(@RequestBody List<OrderChangedReceiveDateRequest> orderChangedReceiveDateRequests, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.updateTaskCreateTimeAndFlag(orderChangedReceiveDateRequests);
        };
        return doing.go(request, logger);
    }

    @PostMapping(value = "/tasks/listItemBytaskNo")
    public JsonResponse<List<TaskItem>> listItemBytaskNo(@RequestBody List<String> taskNoList, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.listItemBytaskNo(taskNoList);
        };
        return doing.go(request, logger);
    }

    @PostMapping(value = "/tasks/listAddrBytaskNo")
    public JsonResponse<List<TaskAddress>> listAddrBytaskNo(@RequestBody List<String> taskNoList, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.listAddrBytaskNo(taskNoList);
        };
        return doing.go(request, logger);
    }


    /**
     * 根据任务号查询任务
     * @param
     * @return
     */
    @PostMapping(value = TaskRouters.TASK_QUERY_BY_TASKNOS)
    public JsonResponse<List<Task>> queryByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.queryByTaskNos(taskNos);
        };
        return doing.go(request, logger);
    }

    /**
     * 批量删除
     *
     * @param
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_DELETE_BATCH_BY_ID, method = RequestMethod.DELETE)
    public JsonResponse deleteBatchById(@RequestBody List<Long> ids, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.deleteBatchById(ids);
        };
        return doing.go(request, logger);
    }

    /**
     * 查询多个,包含删除的
     * @param task
     * @return
     */
    @RequestMapping(value = TaskRouters.TASKS_LIST_INCLUDE_DEL, method = RequestMethod.GET)
    public JsonResponse<List<Task>> listIncludeDel(@SpringQueryMap Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            List<Task> list = iTaskService.selectTaskByIndexIncludeDel(task);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);
    }
    
    /**
    * @description: 根据TaskNo查询任务，包含删除的
    * @param: [task, request]
    * @return: com.mideaframework.core.web.JsonResponse<com.midea.logistics.otp.task.domain.bean.Task>
    * @author: 陈永培
    * @createtime: 2023/7/26 13:49
    */
    @PostMapping(value = TaskRouters.SELECT_TASK_BY_TASKNO_INCLUDEDEL)
    JsonResponse<Task> selectTaskByTaskNoIncludeDel(@RequestBody Task task, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            Task obj = iTaskService.selectTaskByTaskNoIncludeDel(task);
            jsonResponse.data = obj;
        };
        return doing.go(request, logger);
    }

    /**
     * 更新改仓任务
     * @param changeWarehouseTaskRequest
     * @return
     */
    @RequestMapping(value = TaskRouters.UPDATE_CHANGE_WAREHOUSE_TASK, method = RequestMethod.POST)
    public JsonResponse updateChangeWarehouseTask(@RequestBody ChangeWarehouseTaskRequest changeWarehouseTaskRequest, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.updateChangeWarehouseTask(changeWarehouseTaskRequest);
        };
        return doing.go(request, logger);
    }


    @PostMapping("listDeliveredTaskNo")
    public JsonResponse listDeliveredTaskNo(@RequestBody JobRequest jobRequest, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.listDeliveredTaskNo(jobRequest);
        };
        return doing.go(request, logger);
    }

    /**
     * !@订单变更 - 根据单号批量查询task
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.BATCH_SEARCH_TASK_INFO, method = RequestMethod.POST)
    public JsonResponse<TaskSearchResponse> batchSearchTaskInfo(@RequestBody OrderBatchSearchRequest searchRequest, HttpServletRequest request) {
        RestDoing<TaskSearchResponse> doing = jsonResponse -> {
            if (Objects.isNull(searchRequest)) {
                throw BusinessException.fail("batchSearchTaskInfo request is null");
            }
            if (CollectionUtils.isEmpty(searchRequest.getBatchQueryValue()) || searchRequest.getBatchQueryValue().size() == 0) {
                throw BusinessException.fail("task单号不能为空");
            }
            if (searchRequest.getBatchQueryValue().size() > 50) {
                throw BusinessException.fail("超过50个单号无法查询");
            }
            if (StringUtils.isBlank(searchRequest.getChangeDimension())) {
                throw BusinessException.fail("更改维度不能为空");
            }
            String dimension = ChangeDimension.getName(searchRequest.getChangeDimension());
            if (StringUtils.isBlank(dimension) || !dimension.equals(ChangeDimension.TASK.getValue())) {
                throw BusinessException.fail("不匹配的更改维度");
            }
            searchRequest.setDataAuthFlag(1);
            jsonResponse.data = this.fetchTaskResponse(searchRequest);
        };
        return doing.go(request, logger);
    }

    /**
     * 查询task数据
     */
    private TaskSearchResponse fetchTaskResponse(OrderBatchSearchRequest searchRequest) {
        //根据单号条件批量查询
        Integer queryCount = iTaskService.searchTaskListByTaskNosCount(searchRequest);
        if (null != queryCount && queryCount <= 0) {
            throw BusinessException.fail(String.format("任务维度下，未查询到以下单号：%s", StringUtils.join(searchRequest.getBatchQueryValue(), ",")));
        }
        List<TaskDetailResponse> taskList = iTaskService.searchTaskListByTaskNosIndex(searchRequest);
        if (CollectionUtils.isEmpty(taskList) && taskList.size() == 0) {
            throw BusinessException.fail(String.format("任务维度下，未查询到以下单号：%s", StringUtils.join(searchRequest.getBatchQueryValue(), ",")));
        }
        PageResponse<TaskDetailResponse> pageResponse = new PageResponse<>();
        pageResponse.init(searchRequest.getPageNo(), searchRequest.getPageSize(), queryCount, taskList);
        List<Object> taskAndDetails=new ArrayList<>();
        for (TaskDetailResponse task : taskList) {

            //获取task item
            String taskNo = task.getTaskNo();
            TaskItem taskItem = new TaskItem();
            taskItem.setPageSize(CommonConstant.LIST_PAGE_SIZE);
            taskItem.setTaskNo(taskNo);
            List<TaskItem> taskItems = iTaskItemService.selectTaskItemByIndex(taskItem);

            //获取task 地址
            TaskAddress searchTaskAddress = new TaskAddress();
            searchTaskAddress.setTaskNo(taskNo);
            TaskAddress taskAddress = iTaskAddressService.selectOneTaskAddress(searchTaskAddress);

            //组装明细
            task.setRemark(Objects.isNull(task.getRemark())?"":task.getRemark());
            task.buildEquipment();
            task.setChangeItems(taskItems);
            task.setAddress(taskAddress);
            taskAndDetails.add(task);
        }
        //组装返回data
        TaskSearchResponse response = new TaskSearchResponse();
        response.setPageNo(pageResponse.pageNo);
        response.setPageSize(pageResponse.pageSize);
        response.setTotalCount(pageResponse.totalCount);
        response.setOffset(pageResponse.offset);
        response.setTotalPage(pageResponse.totalPage);
        response.setList(taskAndDetails);
        List<String> taskNos = taskList.stream().map(TaskDetailResponse::getTaskNo).collect(Collectors.toList());
        List<String> diff = ListUtil.getDiff(taskNos, searchRequest.getBatchQueryValue());
        if (diff.size() > 0) {
            response.setErrMsg(String.format("任务维度下，未查询到以下单号：%s", StringUtils.join(diff, ",")));
        }
        return response;
    }

    /**
     * 通过任务查询订单详情
     */
    @PostMapping(OrderRouters.TASK_LIST_EXT_BY_TASKNOS)
    public JsonResponse<List<TaskExt>> listExtByTaskNos(@RequestBody SearchTaskRequest searchTaskRequest, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            List<TaskExt> taskExts = iTaskService.listExtByTaskNos(searchTaskRequest);
            jsonResponse.data = taskExts;
        };
        return doing.go(request, logger);
    }
    
    /**
     * @description: 更新任务的关联单号
     * @param: [task, request]
     * @return: com.mideaframework.core.web.JsonResponse<java.lang.Integer>
     * @author: 陈永培
     * @createtime: 2023年10月27日13:56:47
     */
    @RequestMapping(value = OrderRouters.UPDATE_TASK_RELATION_NO, method = RequestMethod.POST)
    public JsonResponse<Integer> updateTaskRelationNo(@RequestBody Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.updateTaskRelationNo(task);
        };
        return doing.go(request, logger);
    }
    
    /**
    * @description: 清空HoldFlag
    * @param: [task, request]
    * @return: com.mideaframework.core.web.JsonResponse<java.lang.Integer>
    * @author: 陈永培
    * @createtime: 2023/7/25 21:44
    */
    @RequestMapping(value = OrderRouters.CLEAR_HOLD_FLAG, method = RequestMethod.POST)
    public JsonResponse<Integer> clearHoldFlag(@RequestBody Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.clearHoldFlag(task);
        };
        return doing.go(request, logger);
    }
    
    /**
    * @description: 清空holdFlag同时设置合同信息
    * @param: [task, request]
    * @return: com.mideaframework.core.web.JsonResponse<java.lang.Integer>
    * @author: 陈永培
    * @createtime: 2023/7/25 21:44
    */
    @RequestMapping(value = OrderRouters.CLEAR_HOLD_FLAG_AND_SETINVOICE, method = RequestMethod.POST)
    public JsonResponse<Integer> clearHoldFlagAndSetInvoice(@RequestBody Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.clearHoldFlagAndSetInvoice(task);
        };
        return doing.go(request, logger);
    }

    /**
    * @description:设置合同信息
    * @param: [task, request]
    * @return: com.mideaframework.core.web.JsonResponse<java.lang.Integer>
    * @author: 陈永培
    * @createtime: 2023/7/25 21:44
    */
    @RequestMapping(value = OrderRouters.SETINVOICE, method = RequestMethod.POST)
    public JsonResponse<Integer> setInvoiceInfo(@RequestBody Task task, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.setInvoiceInfo(task);
        };
        return doing.go(request, logger);
    }

    @PostMapping(TaskRouters.TASK_GET_TASKS_BY_CUSTOMERORDERNOS)
    public JsonResponse<List<Task>> getTasksByCustomerOrderNos(@RequestBody List<String> customerOrderNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.getTasksByCustomerOrderNos(customerOrderNos);
        };
        return doing.go(request, logger);
    }

    /**
     * 更新任务取消单号
     */
    @PostMapping(TaskRouters.TASK_BATCH_UPDATE_TASK_CANCEL_ORDER_NO)
    public JsonResponse<Integer> batchUpdateTaskCancelOrderNo(@RequestBody List<Task> tasks, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.batchUpdateTaskCancelOrderNo(tasks);
        };
        return doing.go(request, logger);
    }

    @PutMapping(TaskRouters.TASK_BATCH_UPDATE_BY_TASK_NO)
    public JsonResponse batchUpdateByTaskNo(@RequestBody List<Task> tasks, HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.batchUpdateByTaskNo(tasks);
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = TaskRouters.TASK_UPDATE_CANSETEMPTY, method = RequestMethod.PUT)
    public JsonResponse updateByIdCanSetEmpty(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data  = iTaskService.updateByIdCanSetEmpty(task);
        };
        return doing.go(request, logger);
    }

    /**
     * 根据订单号查询任务
     * @param
     * @return
     */
    @PostMapping(value = TaskRouters.TASK_QUERY_BY_ORDERNOS)
    public JsonResponse<List<Task>> queryByOrderNos(@RequestBody List<String> orderNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.queryByOrderNos(orderNos);
        };
        return doing.go(request, logger);
    }

    /**
     * @description: 批量清空HoldFlag
     */
    @RequestMapping(value = OrderRouters.BATCH_CLEAR_HOLD_FLAG_BY_TASK_NO, method = RequestMethod.POST)
    public JsonResponse<Integer> batchClearHoldFlagByTaskNo(@RequestBody List<String> taskNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.batchClearHoldFlagByTaskNo(taskNos);
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = OrderRouters.BATCH_CLEAR_HOLD_FLAG_BY_COMPLETE_SET_NOS, method = RequestMethod.POST)
    JsonResponse<Integer> batchClearHoldFlagByCompleteSetNos(@RequestBody List<String> completeSetNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.batchClearHoldFlagByCompleteSetNos(completeSetNos);
        };
        return doing.go(request, logger);
    }
    /**
     * 专司解析刷数据
     * @param
     * @return
     */
    @PostMapping(value = "/task/zsjx")
    public JsonResponse taskJsjx(@RequestBody String param, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.taskZsjx(param);
        };
        return doing.go(request, logger);
    }

    /**
     * 专司解析刷数据
     * @param
     * @return
     */
    @PostMapping(value = "/task/zsjxyc")
    public JsonResponse taskJsjxyc(@RequestBody List<String> param) {
        JsonResponse jsonResponse = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        Integer s = iTaskService.taskJsjxyc(param);
        jsonResponse.setData(s);
        jsonResponse.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS")));
        return jsonResponse;
    }

    @PostMapping(TaskRouters.HAND_OVER_TIME_SEARCH_TASKS)
    public JsonResponse<List<Task>> handOverTimeSearchTasks(@RequestBody AiVoiceAppointDto aiVoiceAppointDto, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.handOverTimeSearchTasks(aiVoiceAppointDto);
        };
        return doing.go(request, logger);
    }

    @PostMapping(TaskRouters.HOLD_APPOINT_SEARCH_TASKS)
    public JsonResponse<List<Task>> holdAppointSearchTasks(@RequestBody AiVoiceAppointDto aiVoiceAppointDto,HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.holdAppointSearchTasks(aiVoiceAppointDto);
        };
        return doing.go(request, logger);
    }

    /**
     * 保存任务信息和详情
     */
    @PostMapping(TaskRouters.SAVE_TASK_INFO_AND_DETAIL)
    public JsonResponse<Integer> saveTaskInfoAndDetail(@RequestBody TaskInfoAndDetail taskInfoAndDetail, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.saveTaskInfoAndDetail(taskInfoAndDetail);
        };
        return doing.go(request, logger);
    }

    @PostMapping(TaskRouters.WMS_IN_CHANGE_WH_BY_TASK)
    public JsonResponse<Integer> wmsInChangeWhByTask(@RequestBody WmsInChangeWhRequest whRequest, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskService.wmsInChangeWhByTask(whRequest);
        };
        return doing.go(request, logger);
    }

    @PostMapping(TaskRouters.LIST_BY_PARENT_ORDER_NOS)
    public JsonResponse<List<Task>> listByParentOrderNos(@RequestBody List<String> parentOrderNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            List<Task> Response = iTaskService.listByParentOrderNos(parentOrderNos);
            jsonResponse.data = Response;
        };
        return doing.go(request, logger);

    }
}
