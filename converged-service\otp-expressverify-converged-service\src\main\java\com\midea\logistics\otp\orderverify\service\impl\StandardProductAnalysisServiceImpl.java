package com.midea.logistics.otp.orderverify.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.otp.common.feign.convergedfeign.order.PmsFeign;
import com.midea.logistics.otp.common.helper.IdGenHelper;
import com.midea.logistics.otp.common.utils.Assert;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderItemFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.service.CoverageAreaService;
import com.midea.logistics.otp.order.common.service.NewProductService;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.SyncOrderInfoToPmsDto;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.StandardProductAnalysisService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 标准产品解析
 */
@Service
@Slf4j
public class StandardProductAnalysisServiceImpl implements StandardProductAnalysisService {
    @Autowired
    private PmsFeign pmsFeign;
    @Autowired
    private NewProductService newProductService;
    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;
    @Autowired
    private CoverageAreaService coverageAreaService;

    /**
    !@标准产品解析 - 入口（快递）
    */
    @Override
    @ExpressFlow(node = OrderOperateType.STANDARD_PRODUCT_ANALYSIS)
    public FlowListenerParam standardProductAnalysis(CustomerOrderInfoExt customerOrderInfo) {
        if(customerOrderInfo == null || StringUtils.isEmpty(customerOrderInfo.getOrderNo())) {
            throw BusinessException.fail("订单号为空");
        }
        this.issuePms(customerOrderInfo);

        if (!SourceSystem.isCAINIAO(customerOrderInfo.getSourceSystem())
            && CollectionUtil.isNotEmpty(customerOrderInfo.getCustomerOrderItems())) {
            Boolean marking = newProductService.marking(customerOrderInfo, customerOrderInfo.getCustomerOrderItems());
            if (BooleanUtil.isTrue(marking)) {
                JsonResponse response = customerOrderItemFeign.batchCreateOrUpdate(customerOrderInfo.getCustomerOrderItems());
                Assert.isTrue(response.judgeSuccess(), "新品打标失败，请重新审核");
            }
        }

        try{
            //电商覆盖范围解析
            coverageAreaService.coverageAreaAnalysis(customerOrderInfo);
        }catch (Exception e){
            log.info("{}电商覆盖范围解析异常：{}",customerOrderInfo.getOrderNo(),e.getMessage());
        }

        return FlowListenerParam.success("标准产品解析成功");
    }

    /**
     * 下发pms
     * @param customerOrderInfo
     * @return
     */
    private CustomerOrderInfoExt issuePms(CustomerOrderInfoExt customerOrderInfo) {
        //cyy:所有非自提的订单都调【!（deliveryType=ZT  or（orderType=RI and deliveryType=EXPRESS））】
//        if (DeliveryType.isZT(customerOrderInfo.getDeliveryType()) || (OrderType.isRIOrder(customerOrderInfo.getOrderType()) && DeliveryType.isEXPRESS(customerOrderInfo.getDeliveryType()))) {
//            return customerOrderInfo;
//        }
        SyncOrderInfoToPmsDto syncOrderInfoToPmsDto = new SyncOrderInfoToPmsDto();
        syncOrderInfoToPmsDto.setOrderNo(customerOrderInfo.getOrderNo());
        syncOrderInfoToPmsDto.setParentOrderNo(customerOrderInfo.getOrderNo());
        syncOrderInfoToPmsDto.setCustomerOrderInfoExt(customerOrderInfo);
        syncOrderInfoToPmsDto.setPmsMsgType(PmsMsgType.RECEIVE.getKey());
        syncOrderInfoToPmsDto.setFlowType(IdGenHelper.PARENT_PREFIX_EXPRESS);
        JsonResponse jsonResponse = pmsFeign.syncOrderInfoToPms(syncOrderInfoToPmsDto);
        if (jsonResponse != null && jsonResponse.judgeSuccess() && jsonResponse.getData() != null) {
            syncOrderInfoToPmsDto = JSONObject.parseObject(JSON.toJSONString(jsonResponse.getData()), SyncOrderInfoToPmsDto.class);
            customerOrderInfo.setProductCode(syncOrderInfoToPmsDto.getProductCode());
        }
        return customerOrderInfo;
    }

}
