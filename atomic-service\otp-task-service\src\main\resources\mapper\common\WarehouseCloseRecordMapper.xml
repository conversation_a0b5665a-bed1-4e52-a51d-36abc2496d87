<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.WarehouseCloseRecordMapper">

    <sql id="searchFieldsSql">
            `site_code` AS  siteCode,
            `create_user_code` AS  createUserCode,
            `update_user_name` AS  updateUserName,
            `site_name` AS  siteName,
            `customer_code` AS  customerCode,
            `create_user_name` AS  createUserName,
            `update_time` AS  updateTime,
            `remark` AS  remark,
            `version` AS  version,
            `customer_name` AS  customerName,
            `delete_flag` AS  deleteFlag,
            `update_user_code` AS  updateUserCode,
            `wh_name` AS  whName,
            `wh_code` AS  whCode,
            `create_time` AS  createTime,
            `id` AS  id,
            `enable_flag` AS  enableFlag,
            `tenant_code` AS  tenantCode
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
                    <if test="siteCode !=null and siteCode != ''">
                        and `site_code` =#{siteCode}
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        and `site_name` =#{siteName}
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        and `customer_code` =#{customerCode}
                    </if>
                    <if test="updateTime !=null">
                        and `update_time` =#{updateTime}
                    </if>
                    <if test="remark !=null">
                        and `remark` =#{remark}
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        and `customer_name` =#{customerName}
                    </if>
                    <if test="whName !=null and whName != ''">
                        and `wh_name` =#{whName}
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        and `wh_code` =#{whCode}
                    </if>
                    <if test="startTime != null and endTime != null">
                        <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
                    </if>
                    <if test="id !=null">
                        and `id` =#{id}
                    </if>
                    <if test="enableFlag !=null and enableFlag != ''">
                        and `enable_flag` =#{enableFlag}
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        and `tenant_code` =#{tenantCode}
                    </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `version` = `version` + 1 ,
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="siteCode !=null and siteCode != ''">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="whName !=null and whName != ''">
                            `wh_name` = #{whName},
                        </if>
                        <if test="whCode !=null and whCode != ''">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="updateUserName !=null and updateUserName != ''">
                            `update_user_name` = #{updateUserName},
                        </if>
                        <if test="siteName !=null and siteName != ''">
                            `site_name` = #{siteName},
                        </if>
                        <if test="customerCode !=null and customerCode != ''">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="createUserName !=null and createUserName != ''">
                            `create_user_name` = #{createUserName},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="tenantCode !=null and tenantCode != ''">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="enableFlag !=null and enableFlag != ''">
                            `enable_flag` = #{enableFlag},
                        </if>
                        <if test="customerName !=null and customerName != ''">
                            `customer_name` = #{customerName},
                        </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
                        <if test="updateUserCode != null">
                            `update_user_code` = #{updateUserCode},
                        </if>
                        <if test="siteCode !=null">
                            `site_code` = #{siteCode},
                        </if>
                        <if test="whName !=null">
                            `wh_name` = #{whName},
                        </if>
                        <if test="whCode !=null">
                            `wh_code` = #{whCode},
                        </if>
                        <if test="updateUserName !=null">
                            `update_user_name` = #{updateUserName},
                        </if>
                        <if test="siteName !=null">
                            `site_name` = #{siteName},
                        </if>
                        <if test="customerCode !=null">
                            `customer_code` = #{customerCode},
                        </if>
                        <if test="createUserName !=null">
                            `create_user_name` = #{createUserName},
                        </if>
                        <if test="remark != null">
                            `remark` = #{remark},
                        </if>
                        <if test="tenantCode !=null">
                            `tenant_code` = #{tenantCode},
                        </if>
                        <if test="enableFlag !=null">
                            `enable_flag` = #{enableFlag},
                        </if>
                        <if test="customerName !=null">
                            `customer_name` = #{customerName},
                        </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from warehouse_close_record t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord">
        select
        <include refid="searchFieldsSql"/>
        from warehouse_close_record t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">${orderByType}</if>
        </if>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from warehouse_close_record t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord">
        select
        <include refid="searchFieldsSql"/>
        from warehouse_close_record t
        <include refid="whereFieldsSql"/>
        order by create_time desc
        <if test="pageNo != null and pageNo > 0 and pageSize != null and pageSize > 0">
            <bind name="start" value="(pageNo - 1) * pageSize"/>
            limit #{start}, #{pageSize}
        </if>
    </select>

    <update id="updateById">
        update
        warehouse_close_record t
        <include refid="setFieldsSql"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        warehouse_close_record t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
                `version` = #{version}
            and `id` = #{id}
    </update>



    <update id="deleteById">
        update
        warehouse_close_record t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord" useGeneratedKeys="true" keyProperty="id">
        insert into warehouse_close_record
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="updateUserCode != null">
                        `update_user_code`,
                    </if>

                    <if test="siteCode !=null and siteCode != ''">
                        `site_code`,
                    </if>

                    <if test="whName !=null and whName != ''">
                        `wh_name`,
                    </if>

                    <if test="whCode !=null and whCode != ''">
                        `wh_code`,
                    </if>

                    <if test="createUserCode != null">
                        `create_user_code`,
                    </if>

                    <if test="updateUserName !=null and updateUserName != ''">
                        `update_user_name`,
                    </if>

                    <if test="siteName !=null and siteName != ''">
                        `site_name`,
                    </if>

                    <if test="customerCode !=null and customerCode != ''">
                        `customer_code`,
                    </if>

                    <if test="createUserName !=null and createUserName != ''">
                        `create_user_name`,
                    </if>

                    <if test="remark != null">
                        `remark`,
                    </if>

                    <if test="tenantCode !=null and tenantCode != ''">
                        `tenant_code`,
                    </if>

                    <if test="enableFlag !=null and enableFlag != ''">
                        `enable_flag`,
                    </if>

                    <if test="customerName !=null and customerName != ''">
                        `customer_name`,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="updateUserCode != null">
                        #{updateUserCode},
                    </if>
                    <if test="siteCode !=null and siteCode != ''">
                        #{siteCode},
                    </if>
                    <if test="whName !=null and whName != ''">
                        #{whName},
                    </if>
                    <if test="whCode !=null and whCode != ''">
                        #{whCode},
                    </if>
                    <if test="createUserCode != null">
                        #{createUserCode},
                    </if>
                    <if test="updateUserName !=null and updateUserName != ''">
                        #{updateUserName},
                    </if>
                    <if test="siteName !=null and siteName != ''">
                        #{siteName},
                    </if>
                    <if test="customerCode !=null and customerCode != ''">
                        #{customerCode},
                    </if>
                    <if test="createUserName !=null and createUserName != ''">
                        #{createUserName},
                    </if>
                    <if test="remark != null">
                        #{remark},
                    </if>
                    <if test="tenantCode !=null and tenantCode != ''">
                        #{tenantCode},
                    </if>
                    <if test="enableFlag !=null and enableFlag != ''">
                        #{enableFlag},
                    </if>
                    <if test="customerName !=null and customerName != ''">
                        #{customerName},
                    </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                `update_user_code`,
                `site_code`,
                `wh_name`,
                `wh_code`,
                `create_user_code`,
                `update_user_name`,
                `site_name`,
                `customer_code`,
                `create_user_name`,
                `remark`,
                `tenant_code`,
                `enable_flag`,
                `customer_name`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.updateUserCode},
                #{item.siteCode},
                #{item.whName},
                #{item.whCode},
                #{item.createUserCode},
                #{item.updateUserName},
                #{item.siteName},
                #{item.customerCode},
                #{item.createUserName},
                #{item.remark},
                #{item.tenantCode},
                #{item.enableFlag},
                #{item.customerName},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        warehouse_close_record
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            `version` = `version` + 1 ,
                 <if test="item.updateUserCode != null">
                     `update_user_code`  = #{item.updateUserCode},
                 </if>
                 <if test="item.siteCode !=null and item.siteCode != ''">
                     `site_code`  = #{item.siteCode},
                 </if>
                 <if test="item.whName !=null and item.whName != ''">
                     `wh_name`  = #{item.whName},
                 </if>
                 <if test="item.whCode !=null and item.whCode != ''">
                     `wh_code`  = #{item.whCode},
                 </if>
                 <if test="item.updateUserName !=null and item.updateUserName != ''">
                     `update_user_name`  = #{item.updateUserName},
                 </if>
                 <if test="item.siteName !=null and item.siteName != ''">
                     `site_name`  = #{item.siteName},
                 </if>
                 <if test="item.customerCode !=null and item.customerCode != ''">
                     `customer_code`  = #{item.customerCode},
                 </if>
                 <if test="item.createUserName !=null and item.createUserName != ''">
                     `create_user_name`  = #{item.createUserName},
                 </if>
                 <if test="item.remark != null">
                     `remark`  = #{item.remark},
                 </if>
                 <if test="item.tenantCode !=null and item.tenantCode != ''">
                     `tenant_code`  = #{item.tenantCode},
                 </if>
                 <if test="item.enableFlag !=null and item.enableFlag != ''">
                    `enable_flag`  = #{item.enableFlag},
                 </if>
                 <if test="item.customerName !=null and item.customerName != ''">
                     `customer_name`  = #{item.customerName},
                 </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
                    <if test="item.siteCode !=null and item.siteCode != ''">
                        and  `site_code`  =#{item.siteCode}
                    </if>
                    <if test="item.updateUserName !=null and item.updateUserName != ''">
                        and  `update_user_name`  =#{item.updateUserName}
                    </if>
                    <if test="item.siteName !=null and item.siteName != ''">
                        and  `site_name`  =#{item.siteName}
                    </if>
                    <if test="item.customerCode !=null and item.customerCode != ''">
                        and  `customer_code`  =#{item.customerCode}
                    </if>
                    <if test="item.createUserName !=null and item.createUserName != ''">
                        and  `create_user_name`  =#{item.createUserName}
                    </if>
                    <if test="item.updateTime !=null">
                        and  `update_time`  =#{item.updateTime}
                    </if>
                    <if test="item.remark !=null">
                        and  `remark`  =#{item.remark}
                    </if>
                    <if test="item.version !=null">
                        and  `version`  =#{item.version}
                    </if>
                    <if test="item.customerName !=null and item.customerName != ''">
                        and  `customer_name`  =#{item.customerName}
                    </if>
                    <if test="item.deleteFlag !=null">
                        and  `delete_flag`  =#{item.deleteFlag}
                    </if>
                    <if test="item.whName !=null and item.whName != ''">
                        and  `wh_name`  =#{item.whName}
                    </if>
                    <if test="item.whCode !=null and item.whCode != ''">
                        and  `wh_code`  =#{item.whCode}
                    </if>
                    <if test="item.createTime !=null">
                        and  `create_time`  =#{item.createTime}
                    </if>
                    <if test="item.enableFlag !=null">
                        and `enable_flag`  = #{item.enableFlag},
                    </if>
                    <if test="item.tenantCode !=null and item.tenantCode != ''">
                        and  `tenant_code`  =#{item.tenantCode}
                    </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE warehouse_close_record
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
#{item.id}
        </foreach>
    </update>


    <update id="deleteBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE warehouse_close_record
            set `delete_flag`=1
            where
            `id` =
#{item.id}
        </foreach>
    </update>

    <select id="queryListByUpdate" resultType="com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord">
        select
        <include refid="searchFieldsSql"/>
        from warehouse_close_record t
        where delete_flag=0
            and `site_code` =#{siteCode}
            and `customer_code` =#{customerCode}
            <if test="whCode !=null and whCode != '' ">
                and `wh_code` =#{whCode}
            </if>
            <if test="enableFlag !=null and enableFlag != '' ">
                and `enable_flag` =#{enableFlag}
            </if>
            <![CDATA[
                and `update_time` >= #{startTime} and `update_time` <= #{endTime}
            ]]>

    </select>

    <select id="queryList" resultType="com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord">
        select
        <include refid="searchFieldsSql"/>
        from warehouse_close_record t
        where delete_flag=0
        and `site_code` =#{siteCode}
        and `customer_code` =#{customerCode}
        <if test="whCode !=null and whCode != '' ">
            and `wh_code` =#{whCode}
        </if>
        <if test="enableFlag !=null and enableFlag != '' ">
            and `enable_flag` =#{enableFlag}
        </if>
    </select>

</mapper>