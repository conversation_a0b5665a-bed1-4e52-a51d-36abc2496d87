package com.midea.logistics.otp.order.common.es;

import com.midea.logistics.otp.order.converged.domain.bean.InformationSecurityOperationVo;
import com.midea.logistics.otp.order.domain.bean.OrderLog;
import com.midea.logistics.otp.order.domain.bean.custom.OrderLogExt;

import java.util.List;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: EsOrderLogService
 * @description:
 * @author: kongly1
 * @date: 2021-8-3 8:59
 */
public interface EsOrderLogService {


    /**
     * 保存日记
     * @return
     */
    void saveLog(OrderLog orderLog);

    /**
     * 保存信息安全操作日记
     */
    void saveInformationSecurityOperation(InformationSecurityOperationVo request);

    /**
     * 查询日记
     */
    List<OrderLogExt> searchLogByParentOrderNo(String orderNo);


    /**
     * 根据任务号查询日记
     */
    List<OrderLogExt> searchLogByTaskNo(String taskNo);
}
