package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoInMapping;
import com.midea.logistics.otp.order.domain.response.OrderItemResponse;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: OrderInfoFeign
 * Author: lindq2
 * Date: 2019-5-23 9:52:11
 * Description:子订单表 feign
 */
@FeignClient(value = "logistics-otp-order-service" /*,url = "http://localhost:10132"*/)
public interface OrderInfoFeign {

    @RequestMapping(value = "/orderInfo", method = RequestMethod.POST)
    JsonResponse<Integer> create(@RequestBody OrderInfo orderInfo);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderInfo/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderInfo/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderInfo orderInfo);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderInfo/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @GetMapping(value = "/orderInfos")
    JsonResponse<PageResponse<OrderInfo>> search(@SpringQueryMap OrderInfo orderInfo);


    @RequestMapping(value = "/orderInfo/list", method = RequestMethod.GET)
    JsonResponse list(@SpringQueryMap OrderInfo orderInfo);

    @RequestMapping(value = OrderRouters.ORDERINFO_SELECT_ONE, method = RequestMethod.POST)
    public JsonResponse<OrderInfo> selectOne(@RequestBody OrderInfo orderInfo);

    @RequestMapping(value = OrderRouters.ORDER_ITEM_SEARCH, method = RequestMethod.GET)
    JsonResponse<PageResponse<OrderItemResponse>> orderItemSearch(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);

    @RequestMapping(value = OrderRouters.ORDER_ITEM_SEARCH_COUNT, method = RequestMethod.GET)
    JsonResponse<Integer> orderItemSearchCount(@SpringQueryMap OrderInfoInMapping orderInfoInMapping);

}
