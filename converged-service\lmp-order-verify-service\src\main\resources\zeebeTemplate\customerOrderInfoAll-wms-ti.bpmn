<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="customer-order-process-wms-ti" name="父订单流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1vr9m96</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0d15yei" name="商品确认">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ezzrfq</bpmn:incoming>
      <bpmn:outgoing>Flow_1ta8m95</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1vr9m96" sourceRef="StartEvent_1" targetRef="Activity_1k6ho42" />
    <bpmn:sequenceFlow id="Flow_1oyv8yd" sourceRef="Activity_1k6ho42" targetRef="Activity_0ur58zi" />
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1vr9m96</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyv8yd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ta8m95</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0ur58zi" name="订单信息校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/orderInfoConfirm" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="orderInfoConfirm" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1oyv8yd</bpmn:incoming>
      <bpmn:outgoing>Flow_1ezzrfq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Event_09zbrsb" />
    <bpmn:sequenceFlow id="Flow_1ezzrfq" sourceRef="Activity_0ur58zi" targetRef="Activity_0d15yei" />
    <bpmn:sequenceFlow id="Flow_1ta8m95" sourceRef="Activity_0d15yei" targetRef="Activity_0uruim6" />
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-wms-ti">
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="1020" y="120" />
        <di:waypoint x="1202" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyv8yd_di" bpmnElement="Flow_1oyv8yd">
        <di:waypoint x="388" y="120" />
        <di:waypoint x="500" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vr9m96_di" bpmnElement="Flow_1vr9m96">
        <di:waypoint x="189" y="120" />
        <di:waypoint x="288" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ezzrfq_di" bpmnElement="Flow_1ezzrfq">
        <di:waypoint x="600" y="120" />
        <di:waypoint x="710" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ta8m95_di" bpmnElement="Flow_1ta8m95">
        <di:waypoint x="810" y="120" />
        <di:waypoint x="920" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="153" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="143" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="288" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="1202" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1209" y="78" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="920" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0wx9b29_di" bpmnElement="Activity_0ur58zi">
        <dc:Bounds x="500" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xzxgxd_di" bpmnElement="Activity_0d15yei">
        <dc:Bounds x="710" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
