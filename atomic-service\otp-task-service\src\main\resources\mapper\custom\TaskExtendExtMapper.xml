<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskExtendMapper">

    <select id="queryByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.TaskExtend">
        select
        <include refid="searchFieldsSql"/>
        from task_extend t
        where `delete_flag`=0 and t.task_no in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <update id="updateBatchCanSetEmpty">
        <foreach collection="list" item="item" separator=";">
            UPDATE task_extend
            <include refid="setFieldsSqlCanSetEmptyBatch"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

    <sql id="setFieldsSqlCanSetEmptyBatch">
        <set>
            `version` = `version` + 1 ,
            <if test="item.orderNo !=null">
                `order_no` = #{item.orderNo},
            </if>
            <if test="item.assembleNum != null">
                `assemble_num` = #{item.assembleNum},
            </if>
            <if test="item.tenantCode !=null">
                `tenant_code` = #{item.tenantCode},
            </if>
            <if test="item.parentOrderNo !=null">
                `parent_order_no` = #{item.parentOrderNo},
            </if>
            <if test="item.cdCustomerCode !=null">
                `cd_customer_code` = #{item.cdCustomerCode},
            </if>
            <if test="item.assembleStatus != null">
                `assemble_status` = #{item.assembleStatus},
            </if>
            <if test="item.relationPreposeTaskNo !=null">
                `relation_prepose_task_no` = #{item.relationPreposeTaskNo},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.driver !=null">
                `driver` = #{item.driver},
            </if>
            <if test="item.orderDistinctionFlag !=null">
                `order_distinction_flag` = #{item.orderDistinctionFlag},
            </if>
            <if test="item.carrierName !=null">
                `carrier_name` = #{item.carrierName},
            </if>
            <if test="item.assembleNo !=null">
                `assemble_no` = #{item.assembleNo},
            </if>
            <if test="item.carrierCode !=null">
                `carrier_code` = #{item.carrierCode},
            </if>
            <if test="item.taskNo !=null">
                `task_no` = #{item.taskNo},
            </if>
            <if test="item.planOrderNo !=null">
                `plan_order_no` = #{item.planOrderNo},
            </if>
            <if test="item.driverMobile !=null">
                `driver_mobile` = #{item.driverMobile},
            </if>
            <if test="item.transportProduct !=null">
                `transport_product` = #{item.transportProduct},
            </if>
            <if test="item.vehicleHandlingSeq != null">
                `vehicle_handling_seq` = #{item.vehicleHandlingSeq},
            </if>
            <if test="item.vehicleCar !=null">
                `vehicle_car` = #{item.vehicleCar},
            </if>
            <if test="item.driverIdentity !=null">
                `driver_identity` = #{item.driverIdentity},
            </if>
            <if test="item.carModel !=null">
                `car_model` = #{item.carModel},
            </if>
        </set>
    </sql>

    <update id="deleteByTaskNos">
        UPDATE task_extend
        set `delete_flag`=1
        where
        `task_no` in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

</mapper>