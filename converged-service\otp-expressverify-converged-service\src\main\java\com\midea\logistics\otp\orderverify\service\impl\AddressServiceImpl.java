package com.midea.logistics.otp.orderverify.service.impl;

import com.midea.logistics.otp.bean.AddressDto;
import com.midea.logistics.otp.bean.AddressResult;
import com.midea.logistics.otp.common.helper.JsonResponseUtils;
import com.midea.logistics.otp.common.service.EncyptAddressService;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.AddressHelper;
import com.midea.logistics.otp.order.common.helper.GisHelper;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.ExpressAddressService;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.midea.logistics.otp.common.constants.CommonConstant.SUCCESS;

@Slf4j
@Service
public class AddressServiceImpl implements ExpressAddressService {
    @Autowired
    private AddressHelper addressHelper;
    @Autowired
    private GisHelper gisHelper;
    @Autowired
    private EncyptAddressService encyptAddressService;
    @Autowired
    private OrderverifyHelper orderverifyHelper;



    @Override
    @ExpressFlow(node = OrderOperateType.ADDRESS_ANALYSIS)
    public FlowListenerParam addressMapping(CustomerOrderInfoExt dto) {

        String orderNo = dto.getOrderNo();
        String deliveryType = dto.getDeliveryType();
        String orderType = dto.getOrderType();
        String inOutType = dto.getInOutType();
        String businessMode = dto.getBusinessMode();

        CustomerOrderAddress address = dto.getCustomerOrderAddress();

       
        /**
         * 1.检查地址
         */
        FlowListenerParam check = checkAddress(dto, address);
        if (null != check) {
            return check;
        }

        //收货
        boolean isR = (InOutType.IN.getName().equals(inOutType) && OrderType.DP.getKey().equals(orderType) ) || InOutType.OUT.getName().equals(inOutType) || InOutType.YS.getName().equals(inOutType) || (InOutType.INOUT.getName().equals(inOutType) && OrderType.DO.getKey().equals(orderType) );
        //发货
        boolean isS  = (InOutType.IN.getName().equals(inOutType) && !OrderType.DP.getKey().equals(orderType)) || InOutType.YS.getName().equals(inOutType);


        /**
         * 1.解析收货地址 ： OUT || YS || (IN && DP) ||(INOUT && DO)
         */
        AddressResult receiverResult = null;
        if (isR) {
            AddressDto addressDto = setReceiver(address);
            addressDto.setOrderNo(orderNo);
            addressDto.setCustomerCode(dto.getCustomerCode());
            addressDto.setSourceFlag(AddressDto.R);
            try {
                receiverResult = encyptAddressService.doWork(addressDto);
            } catch (Exception e) {
                return FlowListenerParam.fail("地址解析失败:"+e.getMessage());
            }
            //不成功，直接返回
            if (null != receiverResult && !receiverResult.getExplainStatus().equals(SUCCESS)) {
                return FlowListenerParam.fail(receiverResult.getMsg());
            }
        }

        /**
         * 3.解析发货地址： (IN  && 非 DP )|| 纯运输
         */
        AddressResult senderResult = null;
        if (isS) {
            AddressDto addressDto = setSender(address);
            addressDto.setOrderNo(orderNo);
            addressDto.setCustomerCode(dto.getCustomerCode());
            addressDto.setSourceFlag(AddressDto.S);
            try {
                senderResult = encyptAddressService.doWork(addressDto);
            } catch (Exception e) {
                return FlowListenerParam.fail("地址解析失败");
            }
            //不成功，直接返回
            if (null != senderResult && !senderResult.getExplainStatus().equals(SUCCESS)) {
                return FlowListenerParam.fail(senderResult.getMsg());
            }
        }

        if (null == receiverResult && null == senderResult ) {
            return FlowListenerParam.success("无需解析地址" );
        }

        /**
         * 4.设置地址
         */
        setAddress(address,receiverResult, senderResult);

        dto.setCustomerOrderAddress(address);

        //更新地址
        orderverifyHelper.updateCustomerOrderAddress(address);

        return FlowListenerParam.success(AddressHelper.getAddressInfo(dto,isR,isS,address));
    }


    /**
    * @description: 更新地址
    * @param: [oldAddress, receiverResult, senderResult]
    * @return: boolean
    * @author: 陈永培
    * @createtime: 2020/10/29 17:37
    */
    private void setAddress(CustomerOrderAddress address, AddressResult receiverResult,AddressResult senderResult) {


        if (null != receiverResult) {
            AddressDto receiver = receiverResult.getAddressDto();
            if (null != receiver) {
                address.setReceiverProvinceCode(receiver.getProvinceCode());
                address.setReceiverProvinceName(receiver.getProvinceName());
                address.setReceiverCityCode(receiver.getCityCode());
                address.setReceiverCityName(receiver.getCityName());
                address.setReceiverDistrictCode(receiver.getDistrictCode());
                address.setReceiverDistrictName(receiver.getDistrictName());
                address.setReceiverTownCode(receiver.getTownCode());
                address.setReceiverTownName(receiver.getTownName());
                address.setReceiverDetailAddr(receiver.getDetailAddr());
                address.setEndLng(receiver.getLng());
                address.setEndLat(receiver.getLat());
            }
        }

        if (null != senderResult) {
            AddressDto sender = senderResult.getAddressDto();
            if (null != sender) {
                address.setSenderProvinceCode(sender.getProvinceCode());
                address.setSenderProvinceName(sender.getProvinceName());
                address.setSenderCityCode(sender.getCityCode());
                address.setSenderCityName(sender.getCityName());
                address.setSenderDistrictCode(sender.getDistrictCode());
                address.setSenderDistrictName(sender.getDistrictName());
                address.setSenderTownCode(sender.getTownCode());
                address.setSenderTownName(sender.getTownName());
                address.setSenderDetailAddr(sender.getDetailAddr());
                address.setStartLng(sender.getLng());
                address.setStartLat(sender.getLat());
            }
        }

    }


    /**
     * @description: 网点地址
     * @param: [oldAddress]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/10/29 17:09
     */
    private AddressDto setNet(CustomerOrderAddress oldAddress) {
        AddressDto addressDto = new AddressDto();
        addressDto.setProvinceCode(oldAddress.getNetworkProvinceCode());
        addressDto.setProvinceName(oldAddress.getNetworkProvinceName());
        addressDto.setCityCode(oldAddress.getNetworkCityCode());
        addressDto.setCityName(oldAddress.getNetworkCityName());
        addressDto.setDistrictCode(oldAddress.getNetworkDistrictCode());
        addressDto.setDistrictName(oldAddress.getNetworkDistrictName());
        addressDto.setTownCode(oldAddress.getNetworkTownCode());
        addressDto.setTownName(oldAddress.getNetworkTownName());
        addressDto.setDetailAddr(oldAddress.getNetworkAddr());
        return addressDto;
    }

    /**
    * @description: 收货地址
    * @param: [oldAddress]
    * @return: void
    * @author: 陈永培
    * @createtime: 2020/10/29 17:09
    */
    private AddressDto setReceiver(CustomerOrderAddress oldAddress) {
        AddressDto addressDto = new AddressDto();
        addressDto.setProvinceCode(oldAddress.getReceiverProvinceCode());
        addressDto.setProvinceName(oldAddress.getReceiverProvinceName());
        addressDto.setCityCode(oldAddress.getReceiverCityCode());
        addressDto.setCityName(oldAddress.getReceiverCityName());
        addressDto.setDistrictCode(oldAddress.getReceiverDistrictCode());
        addressDto.setDistrictName(oldAddress.getReceiverDistrictName());
        addressDto.setTownCode(oldAddress.getReceiverTownCode());
        addressDto.setTownName(oldAddress.getReceiverTownName());
        addressDto.setDetailAddr(oldAddress.getReceiverDetailAddr());
        return addressDto;
    }

    /**
     * @description: 收货地址
     * @param: [oldAddress]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/10/29 17:09
     */
    private AddressDto setSender(CustomerOrderAddress oldAddress) {
        AddressDto addressDto = new AddressDto();
        addressDto.setProvinceCode(oldAddress.getSenderProvinceCode());
        addressDto.setProvinceName(oldAddress.getSenderProvinceName());
        addressDto.setCityCode(oldAddress.getSenderCityCode());
        addressDto.setCityName(oldAddress.getSenderCityName());
        addressDto.setDistrictCode(oldAddress.getSenderDistrictCode());
        addressDto.setDistrictName(oldAddress.getSenderDistrictName());
        addressDto.setTownCode(oldAddress.getSenderTownCode());
        addressDto.setTownName(oldAddress.getSenderTownName());
        addressDto.setDetailAddr(oldAddress.getSenderDetailAddr());
        return addressDto;
    }

    /**
     * @description: 检查地址
     * @param: [customerOrderInfo]
     * @return: com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam
     * @author: 陈永培
     * @createtime: 2020/10/23 16:32
     */
    public FlowListenerParam checkAddress(CustomerOrderInfoExt customerOrderInfoExt,CustomerOrderAddress address){

        String sourceSystem = customerOrderInfoExt.getSourceSystem();
        Integer serviceType = customerOrderInfoExt.getServiceType();
        String deliveryType = customerOrderInfoExt.getDeliveryType();
        String orderType = customerOrderInfoExt.getOrderType();
        String inOutType = customerOrderInfoExt.getInOutType();

        //自提单无需解析
        if (StringUtils.isNotBlank(deliveryType) && DeliveryType.ZT.getKey().equals(deliveryType)) {
            return FlowListenerParam.success("自提单无需解析");
        }

        //收货经纬度
        boolean receiverLatLng = StringUtils.isNotBlank(address.getReceiverTownCode()) && StringUtils.isNotBlank(address.getReceiverTownName()) && !Objects.isNull(address.getEndLat()) && !Objects.isNull(address.getEndLng());
        //发货经纬度
        boolean senderLatLng = StringUtils.isNotBlank(address.getSenderTownCode()) && StringUtils.isNotBlank(address.getSenderTownName()) && !Objects.isNull(address.getStartLat())&& !Objects.isNull(address.getStartLng());

        /**
         * (orderType=RI||DP)&sourceSystem=CAINIAO&&(serviceType=4||5||6) 原单地址，不需要四级地址解析
         * orderType=RI&sourceSystem=ECM&&(serviceType=3||4||5||6)原单地址，不需要四级地址解析
         * orderType=RI&sourceSystem=CIMS&&(serviceType=3||4)原ECM上门取件单地址，不需要四级地址解析
         * (orderType=DPRI||RI)&&sourceSystem=PDD&&(serviceType=4||5||6) 原单地址，不需要四级地址解析
         */

        boolean c1 = (OrderType.RI.getKey().equals(orderType) || OrderType.DP.getKey().equals(orderType) )  && SourceSystem.CAINIAO.getKey().equals(sourceSystem) && ToolUtils.isNotEmpty(serviceType) && "4,5,6".contains(serviceType.toString());
        boolean c2 =  OrderType.RI.getKey().equals(orderType)  && SourceSystem.ECM.getKey().equals(sourceSystem) && ToolUtils.isNotEmpty(serviceType) && "3,4,5,6".contains(serviceType.toString());
        boolean c3 =  OrderType.RI.getKey().equals(orderType)  && SourceSystem.CIMS.getKey().equals(sourceSystem) && ToolUtils.isNotEmpty(serviceType) && "3,4".contains(serviceType.toString());
        boolean c4 = (OrderType.DPRI.getKey().equals(orderType) || OrderType.RI.getKey().equals(orderType) )  && SourceSystem.PDD.getKey().equals(sourceSystem) && ToolUtils.isNotEmpty(serviceType) && "4,5,6".contains(serviceType.toString());

        if (c1 || c2 || c3 || c4){
            return FlowListenerParam.success("原单地址，不需要四级地址解析");
        }

        //收货地址已存在,无需解析(排除国苏的)
        if (InOutType.OUT.getName().equals(inOutType) && receiverLatLng) {
            return FlowListenerParam.success("收货地址已存在,无需解析");
        }

        //发货地址已存在,无需解析
        if (InOutType.IN.getName().equals(inOutType) && senderLatLng) {
            return FlowListenerParam.success("发货地址已存在,无需解析");
        }

        //发货和收货地址均存在,无需解析
        boolean flag = (InOutType.YS.getName().equals(inOutType) ||  InOutType.INOUT.getName().equals(inOutType)) && receiverLatLng && senderLatLng ;

        if (flag) {
            return FlowListenerParam.success("发货和收货地址均存在,无需解析");
        }

        return null;
    }


    /**
    * @description: 加密地址校验
    * @param: [customerOrderInfoExt, address]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2021/9/1 17:41
    */
    public JsonResponse checkEncryAddress(CustomerOrderInfoExt customerOrderInfoExt,CustomerOrderAddress address){

        String receiverProvinceCode = address.getReceiverProvinceCode();
        String receiverCityCode = address.getReceiverCityCode();
        String receiverDistrictCode = address.getReceiverDistrictCode();

        String senderProvinceCode = address.getSenderProvinceCode();
        String senderCityCode = address.getSenderCityCode();
        String senderDistrictCode = address.getSenderDistrictCode();

        String inOutType = customerOrderInfoExt.getInOutType();
        InOutType ioTypeEnum = EnumUtils.getEnum(InOutType.class, inOutType);


        //收货经纬度
        boolean receiverLatLngExist = StringUtils.isNotBlank(receiverProvinceCode) && StringUtils .isNotBlank(receiverCityCode) && StringUtils.isNotBlank(receiverDistrictCode);
        //发货经纬度
        boolean senderLatLngExist = StringUtils.isNotBlank(receiverProvinceCode) && StringUtils.isNotBlank(receiverCityCode) && StringUtils.isNotBlank(receiverDistrictCode);

        //入库
        if (InOutType.OUT == ioTypeEnum && receiverLatLngExist) {
            return JsonResponseUtils.success(customerOrderInfoExt,"收货地址（密文）已存在,无需解析");
        }

        //出库
        if (InOutType.IN == ioTypeEnum && senderLatLngExist) {
            return JsonResponseUtils.success(customerOrderInfoExt,"发货地址（密文）已存在,无需解析");
        }

        if ((InOutType.YS == ioTypeEnum ||  InOutType.INOUT == ioTypeEnum) && receiverLatLngExist && senderLatLngExist) {
            return JsonResponseUtils.success(customerOrderInfoExt,"发货和收货地址（密文）均存在,无需解析");
        }

        return null;
    }








}
