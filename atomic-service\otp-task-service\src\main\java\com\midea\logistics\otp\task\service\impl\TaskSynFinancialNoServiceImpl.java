package com.midea.logistics.otp.task.service.impl;

import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialNoAndConstractNoSyncDto;
import com.midea.logistics.otp.ordertask.converged.domain.request.FinancialOrderSyncItemDto;
import com.midea.logistics.otp.task.mapper.custom.TaskCustomMapper;
import com.midea.logistics.otp.task.service.ITaskSynFinancialNoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * Author:   zhangjh
 * Date:     2019-07-01 17:21
 * Description:
 */
@Component
public class TaskSynFinancialNoServiceImpl implements ITaskSynFinancialNoService {


    @Autowired
    private TaskCustomMapper taskCustomMapper;

    @Override
    public void synFinancialNo(List<FinancialOrderSyncItemDto> itemDtoList) {
        taskCustomMapper.synFinancialNo(itemDtoList);
    }

    @Override
    public void synContractNo(FinancialNoAndConstractNoSyncDto financialNoAndConstractNoSyncDto) {
        taskCustomMapper.synContractNo(financialNoAndConstractNoSyncDto);
    }
}
