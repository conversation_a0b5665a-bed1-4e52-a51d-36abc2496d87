<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.AccurateAppointMapper">

    <sql id="searchFieldsSql">
        `site_code` AS  siteCode,
            `receiver_town_code` AS  receiverTownCode,
            `create_user_code` AS  createUserCode,
            `update_user_name` AS  updateUserName,
            `receiver_district_code` AS  receiverDistrictCode,
            `site_name` AS  siteName,
            `receiver_city_code` AS  receiverCityCode,
            `create_user_name` AS  createUserName,
            `update_time` AS  updateTime,
            `delete_flag` AS  deleteFlag,
            `update_user_code` AS  updateUserCode,
            `create_time` AS  createTime,
            `receiver_city_name` AS  receiverCityName,
            `receiver_district_name` AS  receiverDistrictName,
            `id` AS  id,
            `receiver_province_code` AS  receiverProvinceCode,
            `receiver_town_name` AS  receiverTownName,
            `enable_flag` AS  enableFlag,
            `receiver_province_name` AS  receiverProvinceName
    </sql>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="receiverTownCode !=null and receiverTownCode != ''">
            and `receiver_town_code` =#{receiverTownCode}
        </if>
        <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
            and `receiver_district_code` =#{receiverDistrictCode}
        </if>
        <if test="siteName !=null and siteName != ''">
            and `site_name` =#{siteName}
        </if>
        <if test="receiverCityCode !=null and receiverCityCode != ''">
            and `receiver_city_code` =#{receiverCityCode}
        </if>
        <if test="updateTime !=null">
            and `update_time` =#{updateTime}
        </if>
        <if test="receiverCityName !=null and receiverCityName != ''">
            and `receiver_city_name` =#{receiverCityName}
        </if>
        <if test="receiverDistrictName !=null and receiverDistrictName != ''">
            and `receiver_district_name` =#{receiverDistrictName}
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
        <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
            and `receiver_province_code` =#{receiverProvinceCode}
        </if>
        <if test="receiverTownName !=null and receiverTownName != ''">
            and `receiver_town_name` =#{receiverTownName}
        </if>
        <if test="enableFlag !=null">
            and `enable_flag` =#{enableFlag}
        </if>
        <if test="receiverProvinceName !=null and receiverProvinceName != ''">
            and `receiver_province_name` =#{receiverProvinceName}
        </if>
    </sql>

    <sql id="setFieldsSql">
        <set>
            `site_code` = #{siteCode},
            `site_name` = #{siteName},
            `receiver_province_code` = #{receiverProvinceCode},
            `receiver_province_name` = #{receiverProvinceName},
            `receiver_city_code` = #{receiverCityCode},
            `receiver_city_name` = #{receiverCityName},
            `receiver_district_code` = #{receiverDistrictCode},
            `receiver_district_name` = #{receiverDistrictName},
            `receiver_town_code` = #{receiverTownCode},
            `receiver_town_name` = #{receiverTownName},
            `enable_flag` = #{enableFlag},
            <if test="updateUserName !=null and updateUserName != ''">
                `update_user_name` = #{updateUserName},
            </if>
            <if test="createUserName !=null and createUserName != ''">
                `create_user_name` = #{createUserName},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
        </set>
    </sql>

    <sql id="setFieldsSqlCanSetEmpty">
        <set>
            <if test="siteCode !=null">
                `site_code` = #{siteCode},
            </if>
            <if test="receiverTownCode !=null">
                `receiver_town_code` = #{receiverTownCode},
            </if>
            <if test="updateUserName !=null">
                `update_user_name` = #{updateUserName},
            </if>
            <if test="receiverDistrictCode !=null">
                `receiver_district_code` = #{receiverDistrictCode},
            </if>
            <if test="siteName !=null">
                `site_name` = #{siteName},
            </if>
            <if test="receiverCityCode !=null">
                `receiver_city_code` = #{receiverCityCode},
            </if>
            <if test="createUserName !=null">
                `create_user_name` = #{createUserName},
            </if>
            <if test="updateUserCode != null">
                `update_user_code` = #{updateUserCode},
            </if>
            <if test="receiverCityName !=null">
                `receiver_city_name` = #{receiverCityName},
            </if>
            <if test="receiverDistrictName !=null">
                `receiver_district_name` = #{receiverDistrictName},
            </if>
            <if test="receiverProvinceCode !=null">
                `receiver_province_code` = #{receiverProvinceCode},
            </if>
            <if test="receiverTownName !=null">
                `receiver_town_name` = #{receiverTownName},
            </if>
            <if test="enableFlag != null">
                `enable_flag` = #{enableFlag},
            </if>
            <if test="receiverProvinceName !=null">
                `receiver_province_name` = #{receiverProvinceName},
            </if>
        </set>
    </sql>


    <sql id="idFieldsSql">
        from accurate_appoint t
        where
            `id` = #{id}
    </sql>

    <select id="selectOne" resultType="com.midea.logistics.otp.rule.domain.bean.AccurateAppoint">
        select
        <include refid="searchFieldsSql"/>
        from accurate_appoint t
        <include refid="whereFieldsSql"/>
        limit 1
    </select>

    <select id="selectById" resultType="com.midea.logistics.otp.rule.domain.bean.AccurateAppoint">
        select
        <include refid="searchFieldsSql"/>
        <include refid="idFieldsSql"/>
    </select>

    <select id="selectByIndexCount" resultType="Integer">
        select count(*)
        from accurate_appoint t
        <include refid="whereFieldsSql"/>
    </select>

    <select id="selectByIndex" resultType="com.midea.logistics.otp.rule.domain.bean.AccurateAppoint" timeout="60">
        select
        <include refid="searchFieldsSql"/>
        from accurate_appoint
        where delete_flag=0
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
            and `receiver_province_code` =#{receiverProvinceCode}
        </if>
        <if test="receiverCityCode !=null and receiverCityCode != ''">
            and `receiver_city_code` =#{receiverCityCode}
        </if>
        <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
            and `receiver_district_code` =#{receiverDistrictCode}
        </if>
        <if test="receiverTownCode !=null and receiverTownCode != ''">
            and `receiver_town_code` =#{receiverTownCode}
        </if>
        order by create_time desc
        <if test="pageNo != null and pageNo > 0 and pageSize != null and pageSize > 0">
            <bind name="start" value="(pageNo - 1) * pageSize"/>
            limit #{start}, #{pageSize}
        </if>
    </select>

    <select id="queryByProvinceCodeAndCityCode" resultType="com.midea.logistics.otp.rule.domain.bean.AccurateAppoint"
            parameterType="java.util.List" timeout="60">
        select
            a.site_code,
            a.receiver_province_code,
            a.receiver_city_code,
            a.receiver_district_code,
            a.receiver_town_code
        from accurate_appoint a
        <where>
            a.delete_flag = 0
            <if test=" provinceCodes!= null and provinceCodes.size() > 0">
                and a.receiver_province_code in
                <foreach item="provinceCodes" collection="provinceCodes" index="index" open="(" separator="," close=")">
                    #{provinceCodes}
                </foreach>
            </if>
            <if test=" cityCodes!= null and cityCodes.size() > 0">
                and a.receiver_city_code in
                <foreach item="cityCodes" collection="cityCodes" index="index" open="(" separator="," close=")">
                    #{cityCodes}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectByIds" resultType="com.midea.logistics.otp.rule.domain.bean.AccurateAppoint"
            parameterType="java.util.List" timeout="60">
        select
        <include refid="searchFieldsSql"/>
        from accurate_appoint a
        <where>
        a.delete_flag = 0
        <if test=" listIds!= null and listIds.size() > 0">
            and a.id in
            <foreach item="listIds" collection="listIds" index="index" open="(" separator="," close=")">
                #{listIds}
            </foreach>
        </if>
        </where>
    </select>

    <update id="updateById">
        update
        accurate_appoint t
        <include refid="setFieldsSql"/>
        where
         `id` = #{id}
    </update>


    <update id="updateByIdCanSetEmpty">
        update
        accurate_appoint t
        <include refid="setFieldsSqlCanSetEmpty"/>
        where
       `id` = #{id}
    </update>



    <update id="deleteById">
        update
            accurate_appoint t
        set `delete_flag`=1
        where
            `id` = #{id}
    </update>


    <insert id="save" parameterType="com.midea.logistics.otp.rule.domain.bean.AccurateAppoint" useGeneratedKeys="true" keyProperty="id">
        insert into accurate_appoint
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="siteCode !=null and siteCode != ''">
                `site_code`,
            </if>

            <if test="receiverTownCode !=null and receiverTownCode != ''">
                `receiver_town_code`,
            </if>

            <if test="createUserCode != null">
                `create_user_code`,
            </if>

            <if test="updateUserName !=null and updateUserName != ''">
                `update_user_name`,
            </if>

            <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
                `receiver_district_code`,
            </if>

            <if test="siteName !=null and siteName != ''">
                `site_name`,
            </if>

            <if test="receiverCityCode !=null and receiverCityCode != ''">
                `receiver_city_code`,
            </if>

            <if test="createUserName !=null and createUserName != ''">
                `create_user_name`,
            </if>

            <if test="updateUserCode != null">
                `update_user_code`,
            </if>
            <if test="receiverCityName !=null and receiverCityName != ''">
                `receiver_city_name`,
            </if>
            <if test="receiverDistrictName !=null and receiverDistrictName != ''">
                `receiver_district_name`,
            </if>
            <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
                `receiver_province_code`,
            </if>

            <if test="receiverTownName !=null and receiverTownName != ''">
                `receiver_town_name`,
            </if>

            <if test="enableFlag != null">
                `enable_flag`,
            </if>

            <if test="receiverProvinceName !=null and receiverProvinceName != ''">
                `receiver_province_name`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="siteCode !=null and siteCode != ''">
                #{siteCode},
            </if>
            <if test="receiverTownCode !=null and receiverTownCode != ''">
                #{receiverTownCode},
            </if>
            <if test="createUserCode != null">
                #{createUserCode},
            </if>
            <if test="updateUserName !=null and updateUserName != ''">
                #{updateUserName},
            </if>
            <if test="receiverDistrictCode !=null and receiverDistrictCode != ''">
                #{receiverDistrictCode},
            </if>
            <if test="siteName !=null and siteName != ''">
                #{siteName},
            </if>
            <if test="receiverCityCode !=null and receiverCityCode != ''">
                #{receiverCityCode},
            </if>
            <if test="createUserName !=null and createUserName != ''">
                #{createUserName},
            </if>
            <if test="updateUserCode != null">
                #{updateUserCode},
            </if>
            <if test="receiverCityName !=null and receiverCityName != ''">
                #{receiverCityName},
            </if>
            <if test="receiverDistrictName !=null and receiverDistrictName != ''">
                #{receiverDistrictName},
            </if>
            <if test="receiverProvinceCode !=null and receiverProvinceCode != ''">
                #{receiverProvinceCode},
            </if>
            <if test="receiverTownName !=null and receiverTownName != ''">
                #{receiverTownName},
            </if>
            <if test="enableFlag != null">
                #{enableFlag},
            </if>
            <if test="receiverProvinceName !=null and receiverProvinceName != ''">
                #{receiverProvinceName},
            </if>
        </trim>
    </insert>

    <sql id="batchInsertColumn">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `site_code`,
            `receiver_town_code`,
            `create_user_code`,
            `update_user_name`,
            `receiver_district_code`,
            `site_name`,
            `receiver_city_code`,
            `create_user_name`,
            `update_user_code`,
            `receiver_city_name`,
            `receiver_district_name`,
            `receiver_province_code`,
            `receiver_town_name`,
            `enable_flag`,
            `receiver_province_name`,
        </trim>
    </sql>

    <sql id="batchInsertValue">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.siteCode},
            #{item.receiverTownCode},
            #{item.createUserCode},
            #{item.updateUserName},
            #{item.receiverDistrictCode},
            #{item.siteName},
            #{item.receiverCityCode},
            #{item.createUserName},
            #{item.updateUserCode},
            #{item.receiverCityName},
            #{item.receiverDistrictName},
            #{item.receiverProvinceCode},
            #{item.receiverTownName},
            #{item.enableFlag},
            #{item.receiverProvinceName},
        </trim>
    </sql>


    <insert id="insertBatch">
        insert into
        accurate_appoint
        <include refid="batchInsertColumn"/>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <include refid="batchInsertValue"/>
        </foreach>
    </insert>

    <sql id="setBatchFieldsSql">
        <set>
            <if test="item.siteCode !=null and item.siteCode != ''">
                `site_code`  = #{item.siteCode},
            </if>
            <if test="item.receiverTownCode !=null and item.receiverTownCode != ''">
                `receiver_town_code`  = #{item.receiverTownCode},
            </if>
            <if test="item.updateUserName !=null and item.updateUserName != ''">
                `update_user_name`  = #{item.updateUserName},
            </if>
            <if test="item.receiverDistrictCode !=null and item.receiverDistrictCode != ''">
                `receiver_district_code`  = #{item.receiverDistrictCode},
            </if>
            <if test="item.siteName !=null and item.siteName != ''">
                `site_name`  = #{item.siteName},
            </if>
            <if test="item.receiverCityCode !=null and item.receiverCityCode != ''">
                `receiver_city_code`  = #{item.receiverCityCode},
            </if>
            <if test="item.createUserName !=null and item.createUserName != ''">
                `create_user_name`  = #{item.createUserName},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code`  = #{item.updateUserCode},
            </if>
            <if test="item.receiverCityName !=null and item.receiverCityName != ''">
                `receiver_city_name`  = #{item.receiverCityName},
            </if>
            <if test="item.receiverDistrictName !=null and item.receiverDistrictName != ''">
                `receiver_district_name`  = #{item.receiverDistrictName},
            </if>
            <if test="item.receiverProvinceCode !=null and item.receiverProvinceCode != ''">
                `receiver_province_code`  = #{item.receiverProvinceCode},
            </if>
            <if test="item.receiverTownName !=null and item.receiverTownName != ''">
                `receiver_town_name`  = #{item.receiverTownName},
            </if>
            <if test="item.enableFlag != null">
                `enable_flag`  = #{item.enableFlag},
            </if>
            <if test="item.receiverProvinceName !=null and item.receiverProvinceName != ''">
                `receiver_province_name`  = #{item.receiverProvinceName},
            </if>
        </set>
    </sql>

    <sql id="setBatchWhereFields" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
            <if test="item.siteCode !=null and item.siteCode != ''">
                and  `site_code`  =#{item.siteCode}
            </if>
            <if test="item.receiverTownCode !=null and item.receiverTownCode != ''">
                and  `receiver_town_code`  =#{item.receiverTownCode}
            </if>
            <if test="item.updateUserName !=null and item.updateUserName != ''">
                and  `update_user_name`  =#{item.updateUserName}
            </if>
            <if test="item.receiverDistrictCode !=null and item.receiverDistrictCode != ''">
                and  `receiver_district_code`  =#{item.receiverDistrictCode}
            </if>
            <if test="item.siteName !=null and item.siteName != ''">
                and  `site_name`  =#{item.siteName}
            </if>
            <if test="item.receiverCityCode !=null and item.receiverCityCode != ''">
                and  `receiver_city_code`  =#{item.receiverCityCode}
            </if>
            <if test="item.createUserName !=null and item.createUserName != ''">
                and  `create_user_name`  =#{item.createUserName}
            </if>
            <if test="item.updateTime !=null">
                and  `update_time`  =#{item.updateTime}
            </if>
            <if test="item.deleteFlag !=null">
                and  `delete_flag`  =#{item.deleteFlag}
            </if>
            <if test="item.createTime !=null">
                and  `create_time`  =#{item.createTime}
            </if>
            <if test="item.receiverCityName !=null and item.receiverCityName != ''">
                and  `receiver_city_name`  =#{item.receiverCityName}
            </if>
            <if test="item.receiverDistrictName !=null and item.receiverDistrictName != ''">
                and  `receiver_district_name`  =#{item.receiverDistrictName}
            </if>
            <if test="item.receiverProvinceCode !=null and item.receiverProvinceCode != ''">
                and  `receiver_province_code`  =#{item.receiverProvinceCode}
            </if>
            <if test="item.receiverTownName !=null and item.receiverTownName != ''">
                and  `receiver_town_name`  =#{item.receiverTownName}
            </if>
            <if test="item.enableFlag !=null">
                and  `enable_flag`  =#{item.enableFlag}
            </if>
            <if test="item.receiverProvinceName !=null and item.receiverProvinceName != ''">
                and  `receiver_province_name`  =#{item.receiverProvinceName}
            </if>
        </trim>

    </sql>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE accurate_appoint
            <include refid="setBatchFieldsSql"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>


    <update id="deleteBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE accurate_appoint
            set `delete_flag`=1
            where
            `id` =
            #{item.id}
        </foreach>
    </update>


    <update id="updatePlace">
        update accurate_appoint
        <set>
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `receiver_province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `receiver_city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `receiver_district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `receiver_town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `receiver_province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `receiver_city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `receiver_city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `receiver_district_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `receiver_city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `receiver_town_code` = #{busCode}
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>

    <update id="dcReplacePlace">
        update accurate_appoint
        <set>
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `receiver_province_code` = #{newBusCode},
                    `receiver_province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `receiver_city_code` = #{newBusCode},
                    `receiver_city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `receiver_district_code` = #{newBusCode},
                    `receiver_district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `receiver_town_code` = #{newBusCode},
                    `receiver_town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `receiver_province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `receiver_city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `receiver_city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `receiver_district_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `receiver_city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `receiver_town_code` = #{busCode}
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>
    
    <select id="selectOneBySiteAndAdress" resultType="com.midea.logistics.otp.rule.domain.bean.AccurateAppoint">
        SELECT * FROM (
            SELECT 
                t.*,
                CASE
                    WHEN #{townCode} != '' 
                         AND t.receiver_town_code = #{townCode}
                         AND t.receiver_district_code = #{districtCode}THEN 1
                    WHEN #{districtCode} != '' 
                         AND t.receiver_district_code = #{districtCode}
                         AND (t.receiver_town_code IS NULL OR t.receiver_town_code = '') THEN 2
                    ELSE 999
                END AS match_priority,
                ROW_NUMBER() OVER (ORDER BY match_priority) AS row_num
            FROM accurate_appoint t
            WHERE t.site_code = #{siteCode}
            AND t.receiver_district_code = #{districtCode}
            AND t.enable_flag = 0
            AND t.delete_flag = 0
        ) ranked
        WHERE row_num = 1
        AND match_priority != 999
    </select>
</mapper>