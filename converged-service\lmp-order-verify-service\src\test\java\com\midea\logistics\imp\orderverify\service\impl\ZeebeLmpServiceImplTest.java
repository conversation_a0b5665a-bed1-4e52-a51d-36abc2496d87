package com.midea.logistics.imp.orderverify.service.impl;

import com.annto.framework.easyjunit.disable.apollo.EasyJunitDisableApollo;
import com.annto.framework.easyjunit.mariadb.EasyJunitMariadb;
import com.annto.framework.easyjunit.redis.EasyJunitRedis;
import com.annto.framework.easyjunit.rocketmq.EasyJunitRocketMQ;
import com.midea.logistics.imp.orderverify.LmpOrderVerifyService;
import com.midea.logistics.imp.orderverify.service.OrderZeebeService;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderInfoExtendFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderInfoItemFeign;
import com.midea.logistics.otp.enums.ExcuteStatus;
import com.midea.logistics.otp.enums.OrderDistinctionFlag;
import com.midea.logistics.otp.enums.SourceSystem;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderInfoFeign;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeResponeDto;
import com.midea.logistics.zeebe.sdk.service.ZeebeService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;


@EasyJunitDisableApollo
@EasyJunitRocketMQ
@EasyJunitMariadb
@EasyJunitRedis
@ActiveProfiles("local")
@TestPropertySource(locations = {"classpath:local.properties"})
@SpringBootTest(classes = LmpOrderVerifyService.class)
@Slf4j
class ZeebeLmpServiceImplTest {

    @Autowired
    private ZeebeLmpServiceImpl zeebeLmpService;

    @MockBean
    private CustomerOrderInfoFeign customerOrderInfoFeign;

    @MockBean
    private ZeebeService zeebeService;

    @MockBean
    private OrderZeebeService orderZeebeService;

    @MockBean
    private OrderInfoFeign orderInfoFeign;

    @MockBean
    private OrderInfoItemFeign orderInfoItemFeign;

    @MockBean
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;

    /**
     * 测试 MSS STC 的 zeebe 模板
     */
    @Test
    void testCustomerOrderVerifyZeebe_MssStc_ofcZeebe() {
        String orderNo = "IN2709394";
        // Mockito.when(redisLockHelper.tryLock(Mockito.any(), Mockito.any())).thenReturn(true);
        CustomerOrderInfoExt info = new CustomerOrderInfoExt();
        info.setSourceSystem(SourceSystem.MSS_STC.getKey());
        info.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
        JsonResponse<CustomerOrderInfoExt> ext = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        ext.setData(info);
        Mockito.when(customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(Mockito.any())).thenReturn(ext);

        JsonResponse<ZeebeResponeDto> instance = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        Mockito.when(zeebeService.createInstance(Mockito.argThat(argument -> "customer-order-process-all-ofc".equals(argument.getBpmnProcessId())))).thenReturn(instance);

        Mockito.when(orderZeebeService.saveWorkflowInstanceKey(Mockito.eq(orderNo), Mockito.any())).thenReturn(1L);
        JsonResponse response = zeebeLmpService.customerOrderVerifyZeebe(orderNo);
        Assertions.assertTrue(response.judgeSuccess());
        log.info("finish");
    }

    /**
     * 测试 MSS STC 的 zeebe 模板
     */
    @Test
    void testCustomerOrderVerifyZeebe_Mss_ofcZeebe() {
        String orderNo = "IN2709394";
        // Mockito.when(redisLockHelper.tryLock(Mockito.any(), Mockito.any())).thenReturn(true);
        CustomerOrderInfoExt info = new CustomerOrderInfoExt();
        info.setSourceSystem(SourceSystem.MSS.getKey());
        info.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
        JsonResponse<CustomerOrderInfoExt> ext = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        ext.setData(info);
        Mockito.when(customerOrderInfoFeign.selectCustomerOrderInfoByOrderNoWithDetail(Mockito.any())).thenReturn(ext);

        JsonResponse<ZeebeResponeDto> instance = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        Mockito.when(zeebeService.createInstance(Mockito.argThat(argument -> "customer-order-process-all-ofc".equals(argument.getBpmnProcessId())))).thenReturn(instance);

        Mockito.when(orderZeebeService.saveWorkflowInstanceKey(Mockito.eq(orderNo), Mockito.any())).thenReturn(1L);
        JsonResponse response = zeebeLmpService.customerOrderVerifyZeebe(orderNo);
        Assertions.assertTrue(response.judgeSuccess());
        log.info("finish");
    }

    /**
     * 测试 MSS STC 的 zeebe 模板
     */
    @Test
    void testOrderVerifyZeebe_MssStc_ofcZeebe() {
        String orderNo = "AN2709394";
        OrderInfo info = new OrderInfo();
        info.setSourceSystem(SourceSystem.MSS_STC.getKey());
        info.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
        JsonResponse<OrderInfo> orderInfo = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        orderInfo.setData(info);
        Mockito.when(orderInfoFeign.getOrderInfoByOrderNo(Mockito.any())).thenReturn(orderInfo);

        JsonResponse orderInfoItem = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        Mockito.when(orderInfoItemFeign.getOrderItem(Mockito.any())).thenReturn(orderInfoItem);

        JsonResponse<CustomerOrderInfoExtend> extendRep = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        CustomerOrderInfoExtend extend = new CustomerOrderInfoExtend();
        extend.setOrderDistinctionFlag(OrderDistinctionFlag.BO_FW.getKey());
        extendRep.setData(extend);
        Mockito.when(customerOrderInfoExtendFeign.selectOne(Mockito.any())).thenReturn(extendRep);

        JsonResponse<ZeebeResponeDto> instance = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        Mockito.when(zeebeService.createInstance(Mockito.argThat(argument -> "Process_0ul4aqd-ofc".equals(argument.getBpmnProcessId())))).thenReturn(instance);

        Mockito.when(orderZeebeService.saveWorkflowInstanceKey(Mockito.eq(orderNo), Mockito.any())).thenReturn(1L);
        JsonResponse response = zeebeLmpService.orderVerifyZeebe(orderNo);
        Assertions.assertTrue(response.judgeSuccess());
        log.info("finish");
    }

    /**
     * 测试 MSS 的 zeebe 模板
     */
    @Test
    void testOrderVerifyZeebe_Mss_ofcZeebe() {
        String orderNo = "AN2709394";
        OrderInfo info = new OrderInfo();
        info.setSourceSystem(SourceSystem.MSS.getKey());
        info.setExcuteStatus(ExcuteStatus.AUDITING.getKey());
        JsonResponse<OrderInfo> orderInfo = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        orderInfo.setData(info);
        Mockito.when(orderInfoFeign.getOrderInfoByOrderNo(Mockito.any())).thenReturn(orderInfo);

        JsonResponse orderInfoItem = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        Mockito.when(orderInfoItemFeign.getOrderItem(Mockito.any())).thenReturn(orderInfoItem);

        JsonResponse<CustomerOrderInfoExtend> extendRep = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        CustomerOrderInfoExtend extend = new CustomerOrderInfoExtend();
        extend.setOrderDistinctionFlag(OrderDistinctionFlag.BO_FW.getKey());
        extendRep.setData(extend);
        Mockito.when(customerOrderInfoExtendFeign.selectOne(Mockito.any())).thenReturn(extendRep);

        JsonResponse<ZeebeResponeDto> instance = new JsonResponse<>(BaseCodeEnum.SUCCESS);
        Mockito.when(zeebeService.createInstance(Mockito.argThat(argument -> "Process_0ul4aqd-ofc".equals(argument.getBpmnProcessId())))).thenReturn(instance);

        Mockito.when(orderZeebeService.saveWorkflowInstanceKey(Mockito.eq(orderNo), Mockito.any())).thenReturn(1L);
        JsonResponse response = zeebeLmpService.orderVerifyZeebe(orderNo);
        Assertions.assertTrue(response.judgeSuccess());
        log.info("finish");
    }
}