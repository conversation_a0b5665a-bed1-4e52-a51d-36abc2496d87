package com.midea.logistics.otplbs.fegin;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.midea.logistics.otp.common.bean.TaskSplitInfo;
import com.midea.logistics.otp.bean.BusinessLineTaskDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.converged.domain.request.SearchTaskOrderInfoDetailsResponse;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderBatchSearchRequest;
import com.midea.logistics.otp.order.domain.request.TaskContractVerifyRequest;
import com.midea.logistics.otp.ordertask.converged.domain.TaskConvergedRouters;
import com.midea.logistics.otp.ordertask.converged.domain.request.RowCarRequest;
import com.midea.logistics.otp.ordertask.converged.domain.request.TaskEditorRequest;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.custom.TaskSearchResponse;
import com.midea.logistics.otp.task.domain.bean.response.SearchTaskDetailsResponse;
import com.mideaframework.core.web.JsonResponse;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskApiFeign
 * Author: lindq
 * Date: 2019年6月14日 下午6:12:06
 * Description: feign
 */
@FeignClient(value = "logistics-otp-ordertask-converged-service",url = BaseRouter.LOGISTICS_OTP_ORDERTASK_CONVERGED_SERVICE)
public interface TaskApiFeign {
    
    /**
     * 任务中心 - 批量基地分拨出网点解析
     * @param taskRequest
     * @param request
     * @return
     */
    @PostMapping(TaskConvergedRouters.BATCH_HAND_BASE_DO_TASK_CSP_EXPLAIN)
    JsonResponse handBatchBaseDoTaskCspExplain(@RequestBody TaskContractVerifyRequest taskRequest);
    
    /**
     * @description: 任务中心 - 批量合同校验 按钮
     * @param: [distributionRequest, request]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2023年7月24日11:58:56
     */
    @PostMapping(TaskConvergedRouters.BATCH_HAND_TASK_CONTRACT_VERIFY)
    JsonResponse batchTaskContractVerify(@RequestBody TaskContractVerifyRequest taskRequest);
    
    
    /**
     * 任务下发
     */
    @PostMapping(value = TaskConvergedRouters.TASK_ISSUE)
    JsonResponse taskIssue(@RequestBody List<String> taskNos);

    /**
     * 任务编辑
     * @param taskEditorRequest
     * @return
     */
    @PostMapping(TaskConvergedRouters.TASK_EDIT)
    JsonResponse taskEdit(TaskEditorRequest taskEditorRequest);

    @GetMapping(TaskConvergedRouters.TASK_SEARCH_TASK_DETAILS)
    JsonResponse<SearchTaskDetailsResponse> searchTaskDetails(@RequestParam("taskNo") String taskNo);

    @GetMapping(TaskConvergedRouters.TASK_SEARCH_TASK_ORDER_DETAILS)
    JsonResponse<SearchTaskOrderInfoDetailsResponse> searchTaskOrderDetails(@RequestParam("taskNo") String taskNo);

    /**
     * 排车状态反馈接口
     * 排车接口->调用O端
     *
     * @param rowCarRequest request
     */
    @PostMapping(value = TaskConvergedRouters.DISPATCH_ROW_CAR)
    JsonResponse rowCar(@RequestBody RowCarRequest rowCarRequest);


    @PostMapping(TaskConvergedRouters.TASK_HANDLE_CREATE)
    JsonResponse taskHandleCreate(@RequestBody Task task);

    /**
     * 查询task数据
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.BATCH_SEARCH_TASK_INFO, method = RequestMethod.POST)
    JsonResponse<TaskSearchResponse> batchSearchTaskInfo(@RequestBody OrderBatchSearchRequest searchRequest);


    /**
     * 运输任务下发任务 给T
     * @param task
     * @return
     */
    @PostMapping(TaskConvergedRouters.TASK_YS_CREATE)
    JsonResponse taskYsCreate(@RequestBody CustomerOrderInfo customerOrderInfoParma);

    @PostMapping(TaskConvergedRouters.TASK_BATCH_UPDATE_LOAD_TYPE)
    JsonResponse batchUpdateLoadType(List<TaskEditorRequest> taskEditorRequests);

    /**
     * 任务拆单信息查询
     */
    @PostMapping(TaskConvergedRouters.TASK_SPLIT_INFO_SEARCH)
    JsonResponse taskSplitInfoSearch(@RequestBody TaskSplitInfo taskSplitInfo);

    /**
     * 任务拆单
     */
    @PostMapping(TaskConvergedRouters.TASK_SPLIT)
    JsonResponse taskSplit(@RequestBody TaskSplitInfo taskSplitInfo);


    /**
     * 业务线打标 - 任务维度
     * @param commerceCategoriesDto
     * @return
     */
    @PostMapping(TaskConvergedRouters.TASK_COMMERCE_CATEGORIES)
    JsonResponse businessLineRefresh(@RequestBody List<BusinessLineTaskDto> dto);
    /**
     *  业务线刷数 - 子单维度
     * @param commerceCategoriesDto
     * @return
     */
    @PostMapping(TaskConvergedRouters.TASK_COMMERCE_ORDER_CATEGORIES)
    JsonResponse serviceLineOrderRefresh(@RequestBody List<BusinessLineTaskDto> dto);
}