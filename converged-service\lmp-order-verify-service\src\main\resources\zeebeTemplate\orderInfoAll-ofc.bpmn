<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-ofc" name="子订单流程-ofc" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0opo74t</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0opo74t" sourceRef="StartEvent_1" targetRef="Activity_0smupof" />
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_02zzxtp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1foo88v" sourceRef="Activity_1es0apr" targetRef="Activity_1xd43z3" />
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_0c8zm3c</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:sequenceFlow id="Flow_0p8hiid" sourceRef="Activity_087r4hi" targetRef="Gateway_0aeq2y6" />
    <bpmn:exclusiveGateway id="Gateway_0smudh3" name="是否获取运单号" default="Flow_1e8tnvs">
      <bpmn:incoming>Flow_0znq0o1</bpmn:incoming>
      <bpmn:incoming>Flow_1hrbgzp</bpmn:incoming>
      <bpmn:outgoing>Flow_1e8tnvs</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ss94pu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Gateway_0or76mz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e8tnvs" name="yes" sourceRef="Gateway_0smudh3" targetRef="Activity_1fv890g" />
    <bpmn:serviceTask id="Activity_1es0apr" name="质押">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="pledge" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/pledgeCheck" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fnxy33</bpmn:incoming>
      <bpmn:outgoing>Flow_1foo88v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0tlqh7q" name="时效产品">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="aging" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/agingParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_161nz2d</bpmn:incoming>
      <bpmn:outgoing>Flow_039eniu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0smupof" name="大小电解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="orderOutCollabWh" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/setOrderOutCollabWh" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0opo74t</bpmn:incoming>
      <bpmn:outgoing>Flow_0fnxy33</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1xd43z3" name="爆仓">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="isOutArea" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/isOutArea" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1foo88v</bpmn:incoming>
      <bpmn:outgoing>Flow_0c8zm3c</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
          <zeebe:output source="=responseBody.deliveryType" target="orderInfo.deliveryType" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_0p8hiid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e8tnvs</bpmn:incoming>
      <bpmn:outgoing>Flow_1g901df</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0fnxy33" sourceRef="Activity_0smupof" targetRef="Activity_1es0apr" />
    <bpmn:exclusiveGateway id="Gateway_195jet3" name="是否自提" default="Flow_161nz2d">
      <bpmn:incoming>Flow_0ltk8av</bpmn:incoming>
      <bpmn:outgoing>Flow_161nz2d</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mwtbnu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161nz2d" name="no" sourceRef="Gateway_195jet3" targetRef="Activity_0tlqh7q" />
    <bpmn:sequenceFlow id="Flow_1mwtbnu" name="yes" sourceRef="Gateway_195jet3" targetRef="Activity_1kkl3ix">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and (orderInfo.orderType="DO" or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="PI") or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="AI"))) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_09m8o30" name="网点解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="nethttp" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisNetRange" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_19skgsw</bpmn:incoming>
      <bpmn:outgoing>Flow_0uur51v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0aeq2y6" name="是否网点配送" default="Flow_06arwgx">
      <bpmn:incoming>Flow_0p8hiid</bpmn:incoming>
      <bpmn:outgoing>Flow_06arwgx</bpmn:outgoing>
      <bpmn:outgoing>Flow_19skgsw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_06arwgx" name="no" sourceRef="Gateway_0aeq2y6" targetRef="Gateway_0or76mz" />
    <bpmn:sequenceFlow id="Flow_19skgsw" name="yes" sourceRef="Gateway_0aeq2y6" targetRef="Activity_09m8o30">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="NET") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0c8zm3c" sourceRef="Activity_1xd43z3" targetRef="Gateway_0mzkgmp" />
    <bpmn:serviceTask id="Activity_0hvdqty" name="计费相关">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/anyBmsInfo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ss94pu</bpmn:incoming>
      <bpmn:incoming>Flow_0qzotto</bpmn:incoming>
      <bpmn:outgoing>Flow_0ltk8av</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ltk8av" sourceRef="Activity_0hvdqty" targetRef="Gateway_195jet3" />
    <bpmn:serviceTask id="Activity_1kkl3ix" name="推送查单系统,bms,net,task">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bmsLotsNetTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1mwtbnu</bpmn:incoming>
      <bpmn:incoming>Flow_039eniu</bpmn:incoming>
      <bpmn:outgoing>Flow_02zzxtp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02zzxtp" sourceRef="Activity_1kkl3ix" targetRef="Event_14d5skw" />
    <bpmn:sequenceFlow id="Flow_1g901df" sourceRef="Activity_1fv890g" targetRef="Activity_0cgd1vf" />
    <bpmn:sequenceFlow id="Flow_0ss94pu" sourceRef="Gateway_0smudh3" targetRef="Activity_0hvdqty">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=((orderInfo.businessMode="B2B" and orderInfo.deliveryType="ZT") or (orderInfo.businessMode="B2C" and orderInfo.orderType="PO" and orderInfo.deliveryType="ZT") or (orderInfo.orderType="PO" and orderInfo.deliveryType="EXPRESS"))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_039eniu" sourceRef="Activity_0tlqh7q" targetRef="Activity_1kkl3ix" />
    <bpmn:serviceTask id="Activity_0cgd1vf" name="绑定虚拟号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bindingVirtualPhone" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1g901df</bpmn:incoming>
      <bpmn:outgoing>Flow_0qzotto</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0qzotto" sourceRef="Activity_0cgd1vf" targetRef="Activity_0hvdqty" />
    <bpmn:serviceTask id="Activity_1qblqqx" name="电商分类解析1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/ecommerceCategoriesAnalysisTag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_11085eo</bpmn:incoming>
      <bpmn:outgoing>Flow_0znq0o1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0znq0o1" sourceRef="Activity_1qblqqx" targetRef="Gateway_0smudh3" />
    <bpmn:exclusiveGateway id="Gateway_0or76mz" default="Flow_1hrbgzp">
      <bpmn:incoming>Flow_0uur51v</bpmn:incoming>
      <bpmn:incoming>Flow_06arwgx</bpmn:incoming>
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:outgoing>Flow_11085eo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hrbgzp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0uur51v" sourceRef="Activity_09m8o30" targetRef="Gateway_0or76mz" />
    <bpmn:sequenceFlow id="Flow_11085eo" name="yes" sourceRef="Gateway_0or76mz" targetRef="Activity_1qblqqx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!=null and (orderInfo.orderType="DP" or orderInfo.orderType="SO")) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1hrbgzp" sourceRef="Gateway_0or76mz" targetRef="Gateway_0smudh3" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-ofc">
      <bpmndi:BPMNEdge id="Flow_0znq0o1_di" bpmnElement="Flow_0znq0o1">
        <di:waypoint x="1280" y="177" />
        <di:waypoint x="1305" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qzotto_di" bpmnElement="Flow_0qzotto">
        <di:waypoint x="1640" y="260" />
        <di:waypoint x="1700" y="260" />
        <di:waypoint x="1700" y="432" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_039eniu_di" bpmnElement="Flow_039eniu">
        <di:waypoint x="1204" y="472" />
        <di:waypoint x="1126" y="472" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ss94pu_di" bpmnElement="Flow_0ss94pu">
        <di:waypoint x="1355" y="177" />
        <di:waypoint x="1700" y="177" />
        <di:waypoint x="1700" y="432" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1845" y="298" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g901df_di" bpmnElement="Flow_1g901df">
        <di:waypoint x="1470" y="260" />
        <di:waypoint x="1540" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02zzxtp_di" bpmnElement="Flow_02zzxtp">
        <di:waypoint x="1026" y="472" />
        <di:waypoint x="944" y="472" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ltk8av_di" bpmnElement="Flow_0ltk8av">
        <di:waypoint x="1650" y="472" />
        <di:waypoint x="1404" y="472" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c8zm3c_di" bpmnElement="Flow_0c8zm3c">
        <di:waypoint x="566" y="177" />
        <di:waypoint x="635" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06arwgx_di" bpmnElement="Flow_06arwgx">
        <di:waypoint x="985" y="177" />
        <di:waypoint x="1095" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1054" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mwtbnu_di" bpmnElement="Flow_1mwtbnu">
        <di:waypoint x="1379" y="447" />
        <di:waypoint x="1379" y="390" />
        <di:waypoint x="1076" y="390" />
        <di:waypoint x="1076" y="432" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1219" y="372" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161nz2d_di" bpmnElement="Flow_161nz2d">
        <di:waypoint x="1354" y="472" />
        <di:waypoint x="1304" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1323" y="454" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fnxy33_di" bpmnElement="Flow_0fnxy33">
        <di:waypoint x="297" y="177" />
        <di:waypoint x="327" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e8tnvs_di" bpmnElement="Flow_1e8tnvs">
        <di:waypoint x="1330" y="202" />
        <di:waypoint x="1330" y="260" />
        <di:waypoint x="1370" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1331" y="227" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="660" y="152" />
        <di:waypoint x="660" y="110" />
        <di:waypoint x="1100" y="110" />
        <di:waypoint x="1100" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="661" y="122" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p8hiid_di" bpmnElement="Flow_0p8hiid">
        <di:waypoint x="831" y="177" />
        <di:waypoint x="935" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="685" y="177" />
        <di:waypoint x="731" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="688" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1foo88v_di" bpmnElement="Flow_1foo88v">
        <di:waypoint x="427" y="177" />
        <di:waypoint x="466" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0opo74t_di" bpmnElement="Flow_0opo74t">
        <di:waypoint x="172" y="177" />
        <di:waypoint x="197" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uur51v_di" bpmnElement="Flow_0uur51v">
        <di:waypoint x="1090" y="270" />
        <di:waypoint x="1100" y="270" />
        <di:waypoint x="1100" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11085eo_di" bpmnElement="Flow_11085eo">
        <di:waypoint x="1145" y="177" />
        <di:waypoint x="1180" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1151" y="193" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19skgsw_di" bpmnElement="Flow_19skgsw">
        <di:waypoint x="960" y="202" />
        <di:waypoint x="960" y="270" />
        <di:waypoint x="990" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="967" y="234" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hrbgzp_di" bpmnElement="Flow_1hrbgzp">
        <di:waypoint x="1120" y="202" />
        <di:waypoint x="1120" y="270" />
        <di:waypoint x="1310" y="270" />
        <di:waypoint x="1310" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="136" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="143" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="908" y="454" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="915" y="497" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="635" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="647" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0smudh3_di" bpmnElement="Gateway_0smudh3" isMarkerVisible="true">
        <dc:Bounds x="1305" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1316" y="127" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01lp2qq_di" bpmnElement="Activity_1es0apr">
        <dc:Bounds x="327" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tz6c6h_di" bpmnElement="Activity_0tlqh7q">
        <dc:Bounds x="1204" y="432" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_059zzl3_di" bpmnElement="Activity_0smupof">
        <dc:Bounds x="197" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1q12own_di" bpmnElement="Activity_1xd43z3">
        <dc:Bounds x="466" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="731" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="1370" y="220" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_195jet3_di" bpmnElement="Gateway_195jet3" isMarkerVisible="true">
        <dc:Bounds x="1354" y="447" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1358" y="504" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0hvdqty_di" bpmnElement="Activity_0hvdqty">
        <dc:Bounds x="1650" y="432" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kkl3ix_di" bpmnElement="Activity_1kkl3ix">
        <dc:Bounds x="1026" y="432" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cgd1vf_di" bpmnElement="Activity_0cgd1vf">
        <dc:Bounds x="1540" y="220" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0or76mz_di" bpmnElement="Gateway_0or76mz" isMarkerVisible="true">
        <dc:Bounds x="1095" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0aeq2y6_di" bpmnElement="Gateway_0aeq2y6" isMarkerVisible="true">
        <dc:Bounds x="935" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="930" y="122" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09m8o30_di" bpmnElement="Activity_09m8o30">
        <dc:Bounds x="990" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qblqqx_di" bpmnElement="Activity_1qblqqx">
        <dc:Bounds x="1180" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
