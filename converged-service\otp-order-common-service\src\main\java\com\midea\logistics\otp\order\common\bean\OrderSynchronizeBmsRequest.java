package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderSynchronizeBmsRequest {
    private String customerOrderNo;
    private String orderType;
    private String orderNo;
    private String customerCode;
    private String companyCode;
    private String siteCode;
    private String transportType;
    private Date upperOrderTime;
    private String businessMode;
    private BigDecimal totalVolume;
    private BigDecimal totalNetWeight;
    private BigDecimal totalGrossWeight;
    private BigDecimal totalQty;
    private String whCode;
    private String distributionWhCode;
    private String invoiceUnitCode;
    private String deliveryType;
    private String upperWhCode;
    private String orderStatus;
    private String originOrderNo;
    private String specimenType;
    private Integer scPosFlag;
    private String upstreamDocType;
    private String receiverProvinceCode;
    private String receiverCityCode;
    private String receiverDistrictCode;
    private String receiverTownCode;
    private String senderProvinceCode;
    private String senderCityCode;
    private String senderDistrictCode;
    private String senderTownCode;
    private String sourceSystem;
    private String projectClassify;
    private String freightBasis;
    private String businessCategory;
    private String upperOrderType;

    private Integer taskType;

    private BigDecimal collectionAmount;

    //回传AN单号
    private String originalAnNo;

    //车型属性
    private String equipmentType;

    //车型属性名称
    private String equipmentName;

    //集拼单号
    private String consolidationOrderNo;

    //产品编码
    private String productCode;

    //产品名称
    private String productName;

    //是否标准产品
    private Integer isStandardProduct;

    //计费系统
    private String billSystem;

    /**
     * 计费字符1
     */
    private String calculateVar1;

    /**
     * 计费字符2
     */
    private String calculateVar2;

    /**
     * 计费字符3
     */
    private String calculateVar3;

    /**
     * 计费字符4
     */
    private String calculateVar4;

    /**
     * 计费数值
     */
    private BigDecimal calculateNum;

    /**
     * 是否上门取件,0：否，1：是
     */
    private Integer serviceType;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 支付金额
     */
    private BigDecimal payTotalAmount;

    /**
     * 上游收货单位编码
     */
    private String upperReceiverCode;
    /**
     * 关联单号
     */
    private String relationOrderNo;

    private String orderLabel;

    private List<OrderSynchronizationBmsItemRequest> orderDetail;
}
