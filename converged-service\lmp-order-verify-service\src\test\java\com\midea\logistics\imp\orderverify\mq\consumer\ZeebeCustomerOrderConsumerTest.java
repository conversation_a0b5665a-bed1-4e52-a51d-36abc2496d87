package com.midea.logistics.imp.orderverify.mq.consumer;

import com.midea.logistics.imp.orderverify.rest.BaseRestTest;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 测试 TMS 消费列表
 * @date 2019年07月09日 上午22:52
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class ZeebeCustomerOrderConsumerTest extends BaseRestTest {

    @Autowired
    ZeebeCustomerOrderConsumer zeebeCustomerOrderConsumer;

    @Test
    public void handlerMessage() throws Exception {

        Message message = new Message("xxs", "IN22306021150165019".getBytes());
        MessageExt messageExt = new MessageExt();
        BeanUtils.copyProperties(message, messageExt);
        zeebeCustomerOrderConsumer.handlerMessage(messageExt);
    }
}