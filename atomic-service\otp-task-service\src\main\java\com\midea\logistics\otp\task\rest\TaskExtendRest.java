package com.midea.logistics.otp.task.rest;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.service.ITaskExtendService;
import com.mideaframework.core.controller.BaseController;
import java.util.List;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.midea.logistics.otp.task.domain.bean.TaskExtend;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import com.midea.logistics.otp.common.constants.CommonConstant;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: TaskExtendController
* Author: 刘院民
* Date: 2023-3-21 11:34:11
* Description:任务拓展表控制层
*/
@RestController
public class TaskExtendRest extends BaseController<TaskExtend> {

    private Logger logger = LoggerFactory.getLogger(TaskExtendRest.class);

    @Autowired
    private ITaskExtendService iTaskExtendService;

    @Autowired
    Validatorhelper validatorhelper;

    /**
    * 新增
    *
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_CREATE, method = RequestMethod.POST)
    public JsonResponse<Integer> create(@Valid @RequestBody TaskExtend taskExtend, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer success = iTaskExtendService.saveTaskExtend(taskExtend);
            if (1 == success){
                jsonResponse.data = success;
             }
        };
        return doing.go(request, logger);
    }


    /**
    * @param id
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_DETAIL, method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskExtend taskExtendQr = new TaskExtend();
            taskExtendQr.setId(id);
            TaskExtend taskExtend = iTaskExtendService.selectOneTaskExtend(taskExtendQr);
            jsonResponse.data = taskExtend;
        };
        return doing.go(request, logger);

    }

    /**
    * 更新
    * @param taskExtend
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_UPDATE, method = RequestMethod.PUT)
    public JsonResponse update(@PathVariable("id") Long id,  /**@Valid*/ @RequestBody TaskExtend taskExtend, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            taskExtend.setId(id);
            taskExtend.setVersion(taskExtend.getVersion());
            Integer i = iTaskExtendService.updateTaskExtend(taskExtend);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
     * 更新 设置空值（blank）
     * @param taskExtend
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_UPDATE_CAN_SET_EMPTY, method = RequestMethod.PUT)
    JsonResponse updateByIdCanSetEmpty(@RequestBody TaskExtend taskExtend, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            Integer i = iTaskExtendService.updateByIdCanSetEmpty(taskExtend);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_DELETE, method = RequestMethod.DELETE)
    public JsonResponse deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskExtend taskExtendQr = new TaskExtend(id);
            Integer i = iTaskExtendService.deleteTaskExtend(taskExtendQr);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);

    }



    /**
    * 查询集合（分页）
    * @param taskExtend
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_SEARCH, method = RequestMethod.GET)
    public JsonResponse search(@SpringQueryMap TaskExtend taskExtend, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<TaskExtend> pageResponse = iTaskExtendService.selectTaskExtendPage(taskExtend);
            jsonResponse.data = pageResponse;
        };
        return doing.go(request, logger);

    }


    /**
    * 查询集合
    * @param taskExtend
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_LIST, method = RequestMethod.GET)
    public JsonResponse list(@SpringQueryMap TaskExtend taskExtend, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            taskExtend.setPageSize(CommonConstant.LIST_PAGE_SIZE);
            List<TaskExtend> list = iTaskExtendService.selectTaskExtendByIndex(taskExtend);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);

    }


    /**
    * 查询集合
    * @param taskExtend
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_SELECT_ONE, method = RequestMethod.GET)
    public JsonResponse selectOne(@SpringQueryMap TaskExtend taskExtend, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskExtend obj = iTaskExtendService.selectOneTaskExtend(taskExtend);
            jsonResponse.data = obj;
        };
        return doing.go(request, logger);

    }


    /**
    * 批量新增
    *
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_INSERT_BATCH, method = RequestMethod.POST)
    public JsonResponse insertBatch(@RequestBody List<TaskExtend> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iTaskExtendService.insertBatch(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }


    /**
    * 批量修改
    *
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_UPDATE_BATCH, method = RequestMethod.POST)
    public JsonResponse updateBatch(@RequestBody List<TaskExtend> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iTaskExtendService.batchUpdate(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
     * 批量修改
     *
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_UPDATE_BATCH_CAN_SET_EMPTY, method = RequestMethod.POST)
    public JsonResponse updateBatchCanSetEmpty(@RequestBody List<TaskExtend> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iTaskExtendService.updateBatchCanSetEmpty(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }


    /**
    * 批量删除
    *
    * @return
    */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_DELETE_BATCH, method = RequestMethod.POST)
    public JsonResponse deleteBatch(@RequestBody List<TaskExtend> list, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            Integer i = iTaskExtendService.deleteBatch(list);
            jsonResponse.data = i;
        };
        return doing.go(request, logger);
    }

    /**
     * 根据taskNos查询集合
     * @param taskNos
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_LIST_TASKNOS, method = RequestMethod.POST)
    public JsonResponse<List<TaskExtend>> listByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskExtendService.listByTaskNos(taskNos);
        };
        return doing.go(request, logger);
    }

    /**
     * 根据taskNos逻辑删除
     * @param taskNos
     * @return
     */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_DELETE_BYTASKNOS, method = RequestMethod.PUT)
    public JsonResponse<Integer> deleteByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskExtendService.deleteByTaskNos(taskNos);
        };
        return doing.go(request, logger);
    }

    /**
     * @return 查询集合
     */
    @RequestMapping(value = TaskRouters.TASK_EXTEND_SELECT_BY_INDEX, method = RequestMethod.POST)
    public JsonResponse<List<TaskExtend>> selectTaskExtendByIndex(@RequestBody TaskExtend taskExtend, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            List<TaskExtend> taskExtends = iTaskExtendService.selectTaskExtendByIndex(taskExtend);
            jsonResponse.data = taskExtends;
        };
        return doing.go(request, logger);

    }
}
