package com.midea.logistics.otp.task.rest;

import com.midea.logistics.otp.task.domain.bean.TaskOperLog;
import com.midea.logistics.otp.task.service.ITaskOperLogService;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskOperLogController
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:任务操作日志控制层
 */
@RestController
public class TaskOperLogRest extends BaseController<TaskOperLog> {

    private Logger logger = LoggerFactory.getLogger(TaskOperLogRest.class);

    @Autowired
    private ITaskOperLogService iTaskOperLogService;

    /**
     * 新增
     *
     * @return
     */
    @RequestMapping(value = "/taskOperLog", method = RequestMethod.POST)
    public JsonResponse create(@Valid @RequestBody TaskOperLog taskOperLog, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            iTaskOperLogService.saveTaskOperLog(taskOperLog);
        };
        return doing.go(request, logger);
    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskOperLog/{id}", method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskOperLog taskOperLogQr = new TaskOperLog();
            taskOperLogQr.setId(id);
            TaskOperLog taskOperLog = iTaskOperLogService.selectOneTaskOperLog(taskOperLogQr);
            jsonResponse.data = taskOperLog;
        };
        return doing.go(request, logger);

    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskOperLog/{id}", method = RequestMethod.DELETE)
    public JsonResponse deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskOperLog taskOperLogQr = new TaskOperLog(id);
            iTaskOperLogService.deleteTaskOperLog(taskOperLogQr);
        };
        return doing.go(request, logger);

    }

    /**
     * @param taskOperLog
     * @return
     */
    @RequestMapping(value = "/taskOperLog/{id}", method = RequestMethod.PUT)
    public JsonResponse update(@PathVariable("id") Long id,  /**@Valid*/@RequestBody TaskOperLog taskOperLog, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            taskOperLog.setId(id);
            taskOperLog.setVersion(taskOperLog.getVersion());
            iTaskOperLogService.updateTaskOperLog(taskOperLog);
        };
        return doing.go(request, logger);
    }


    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/taskOperLogs", method = RequestMethod.GET)
    public JsonResponse search(@SpringQueryMap TaskOperLog taskOperLog, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<TaskOperLog> PageResponse = iTaskOperLogService.selectTaskOperLogPage(taskOperLog);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }

}
