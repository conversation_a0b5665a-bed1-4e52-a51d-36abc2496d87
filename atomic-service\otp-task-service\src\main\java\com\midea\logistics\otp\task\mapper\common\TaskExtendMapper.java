package com.midea.logistics.otp.task.mapper.common;

import com.mideaframework.core.mapper.BaseMapper;
import com.midea.logistics.otp.task.domain.bean.TaskExtend;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskExtendMapper
 * Author: 刘院民
 * Date: 2023-3-21 11:34:11
 * Description:TaskExtendMapper服务接口
 */
@Mapper
public interface TaskExtendMapper extends BaseMapper<TaskExtend> {


    List<TaskExtend> queryByTaskNos(List<String> list);

    Integer updateBatchCanSetEmpty(List<TaskExtend> list);

    Integer deleteByTaskNos(List<String> taskNos);
}