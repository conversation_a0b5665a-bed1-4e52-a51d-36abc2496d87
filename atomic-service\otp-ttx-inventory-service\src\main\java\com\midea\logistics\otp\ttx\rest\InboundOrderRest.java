package com.midea.logistics.otp.ttx.rest;

import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrder;
import com.midea.logistics.otp.ttx.domain.OrderCodeEnum;
import com.midea.logistics.otp.ttx.inventory.domain.TTXRouters;
import com.midea.logistics.otp.ttx.inventory.domain.response.ReceiptSaveTTXItem;
import com.midea.logistics.otp.ttx.manager.InboundOrderManager;
import com.midea.logistics.otp.ttx.manager.OssManager;
import com.midea.logistics.otp.ttx.service.InboundOrderService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: InboundOrderController
 * Author: yaowl
 * Date: 2019-8-21 19:21:37
 * Description:表注释控制层
 */
@RestController
public class InboundOrderRest extends BaseController<InboundOrder> {

    private Logger logger = LoggerFactory.getLogger(InboundOrderRest.class);

    @Autowired
    private InboundOrderService inboundOrderService;

    @Autowired
    private  InboundOrderManager inboundOrderManager;

    @Autowired
    private OssManager ossManager;

    /**
     * Update json response.
     *
     * @param inboundOrder the inbound order
     * @param request      the request
     * @return the json response
     */
    @PutMapping(value = "/inboundOrder")
    public JsonResponse<InboundOrder> update(@RequestBody InboundOrder inboundOrder, HttpServletRequest request) {
        RestDoing<InboundOrder> doing = jsonResponse ->
            jsonResponse.data = inboundOrderService.update(inboundOrder);

        return doing.go(request, logger);
    }

    /**
     * Search json response.
     *
     * @param inboundOrder the inbound order
     * @param request      the request
     * @return the json response
     */
    @PostMapping(value = TTXRouters.TTX_SEARCH_INBOUNDORDER)
    public JsonResponse<PageResponse<InboundOrder>> search(@RequestBody InboundOrder inboundOrder, HttpServletRequest request) {
        RestDoing<PageResponse<InboundOrder>> doing = jsonResponse ->
            jsonResponse.data =  inboundOrderService.selectPage(inboundOrder);
        return doing.go(request, logger);
    }

    /**
     * Inbound order table upload json response.
     *
     * @param file 导入
     * @param request      the request
     * @return the json response
     * @Description: 入库单信息上传
     * @return: com.mideaframework.core.web.JsonResponse
     * @Author: yaowl
     * @Date: 2019 -8-22
     */
    @PostMapping(value = TTXRouters.TTX_UPLOAD_INBOUNDORDER, consumes = "multipart/form-data")
    public JsonResponse inboundOrderTableUpload(@RequestBody MultipartFile file, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            ImportFileRequest importFileRequest = inboundOrderService.inboundOrderUpload(file);

            InboundOrder inboundOrder = new InboundOrder();
            inboundOrder.setFileName(importFileRequest.getFileName());
            inboundOrder.setFileUrl(importFileRequest.getFileUrl());
            inboundOrder.setImportTime(new Date());
            inboundOrder.setOrderStatus(OrderCodeEnum.UPLOAD.getCode());
            int x = inboundOrderService.save(inboundOrder);
            if (x > 0) {
                jsonResponse.build(BaseCodeEnum.SUCCESS.getCode(), "上传成功");
            } else {
                jsonResponse.fail2("上传失败");
            }
        };
        return doing.go(request, logger);
    }


    /**
     * Inbound order template download.
     *
     * @param request  the request
     * @param response the response
     * @Description: 入库单模板下载
     * @return: void
     * @Author: yaowl
     * @Date: 2019 -8-22
     */
    @RequestMapping(value = TTXRouters.TTX_DOWNLOAD_INBOUNDORDERTEMPLATE, method = RequestMethod.GET)
    public void inboundOrderTemplateDownload(HttpServletRequest request,HttpServletResponse response) {
        inboundOrderService.inboundOrderTemplateDownload(request,response);
    }


    /** 
    * @Description: 再确认上架
    * @Param: [id] 
    * @return: com.mideaframework.core.web.JsonResponse 
    * @Author: yaowl
    * @Date: 2019-9-18 
    */
    @RequestMapping(value =TTXRouters.TTX_DOWNLOAD_RECHECKISSHELF , method = RequestMethod.GET)
    public JsonResponse reCheckIsShelf(@PathVariable("id") Long id) {
        return inboundOrderManager.reCheckIsShelf(id);
    }


    /**
     * 入库单内容保存至数据库，任务接口
     *
     * @return the json response
     */
    @GetMapping(value = "/ttx/inboundOrder/save")
    public JsonResponse inboundOrderTableSave(HttpServletRequest request) {
        return inboundOrderManager.saveInboundOrderList();
    }


    /**
     * 确认上架接口，任务接口
     *
     * @param request the request
     * @return the json response
     */
    @GetMapping(value = "/ttx/inboundOrder/checkIsShelf")
    public JsonResponse checkIsShelf(HttpServletRequest request) {
        return inboundOrderManager.checkListIsShelf();
    }


    /**
     * Create error file.测试上传异常文件接口。
     */
    @RequestMapping(value = "/ttx/inboundOrder/createError", method = RequestMethod.GET)
    public void createErrorFile(){
        List<ReceiptSaveTTXItem> itemList =new ArrayList<>();
        ReceiptSaveTTXItem item=new ReceiptSaveTTXItem();
        item.setErpOrderLineNum("1");
        item.setItemCode("1");
        item.setMsg("1");
        itemList.add(item);
        inboundOrderManager.createErrorFile(itemList);
    }

    /**
     * 获取certification
     * @return
     */
    @PostMapping(value = "/ttx/inboundOrder/getCertification")
    public JsonResponse getCertification() {
        return JsonResponse.success(ossManager.getCertification());
    }
}
