package com.midea.logistics.otp.order.common.fegin.bop;

import com.midea.logistics.otp.order.converged.domain.request.HikVisionOrderRequest;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import feign.hystrix.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @author: zhouhl
 * @date: 2021/6/17 13:55
 * @Description:
 */

@FeignClient(value = "logistics-bop-service",fallback = HikVisionBopFeign.HikVisionBop.class)
public interface HikVisionBopFeign {

    @PostMapping("/inner/T201904230000000014/udpApi/hikVisionPullOrders")
    JsonResponse<String> hikVisionPullOrders(@RequestBody HikVisionOrderRequest request, @RequestHeader("token") String token);



    @Component
    class HikVisionBop implements FallbackFactory<HikVisionBopFeign>{
        @Override
        public HikVisionBopFeign create(Throwable throwable) {
            return new HikVisionBopFeign() {
                @Override
                public JsonResponse<String> hikVisionPullOrders(HikVisionOrderRequest request,String token) {
                    JsonResponse jsonResponse = new JsonResponse();
                    jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
                    jsonResponse.setData(throwable.getMessage());
                    return jsonResponse;
                }
            };
        }
    }
}
