package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.RatWarehouse;
import com.midea.logistics.otp.rule.domain.request.IssueRuleRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: RatWarehouseFeign
* Author: luoh
* Date: 2019-12-12 16:38:41
* Description:老鼠仓 feign
*/
@FeignClient(value = "logistics-otp-rule-service")
public interface RatWarehouseFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/ratWarehouse", method = RequestMethod.POST)
    JsonResponse create(@RequestBody RatWarehouse ratWarehouse);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/ratWarehouse/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/ratWarehouse/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody RatWarehouse ratWarehouse);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/ratWarehouse/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/ratWarehouses", method = RequestMethod.POST)
    JsonResponse<PageResponse<RatWarehouse>> search(@RequestBody RatWarehouse ratWarehouse);;

    /**
     * 批量停用
     */
    @RequestMapping(value = "/ratWarehouse/batchStop", method = RequestMethod.POST)
    JsonResponse batchStop(@RequestBody List<Long> ids);

    /**
     * 批量启用
     */
    @RequestMapping(value = "/ratWarehouse/batchStart", method = RequestMethod.POST)
    JsonResponse batchStart(@RequestBody List<Long> ids);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = "/ratWarehouse/batchCreateOrUpdate", method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<RatWarehouse> ratWarehouses);

}
