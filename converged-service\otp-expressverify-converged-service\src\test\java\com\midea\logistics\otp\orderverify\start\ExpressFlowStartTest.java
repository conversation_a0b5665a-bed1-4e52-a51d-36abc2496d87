package com.midea.logistics.otp.orderverify.start;

import com.annto.framework.easyjunit.disable.apollo.EasyJunitDisableApollo;
import com.annto.framework.easyjunit.mariadb.EasyJunitMariadb;
import com.annto.framework.easyjunit.redis.EasyJunitRedis;
import com.annto.framework.easyjunit.rocketmq.EasyJunitRocketMQ;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.orderverify.OrderExpressConvergedService;
import com.midea.logistics.otp.orderverify.service.AssignWarehouseService;
import com.midea.logistics.otp.orderverify.service.CargoRightTransferService;
import com.midea.logistics.otp.orderverify.service.CustomerOrderInfoMapping;
import com.midea.logistics.otp.orderverify.service.ExpressAddressService;
import com.midea.logistics.otp.orderverify.service.StandardProductAnalysisService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@EasyJunitDisableApollo
@EasyJunitRocketMQ
@EasyJunitMariadb
@EasyJunitRedis
@ActiveProfiles("local")
@TestPropertySource(locations = "classpath:local.properties")
@SpringBootTest(classes = OrderExpressConvergedService.class)
class ExpressFlowStartTest {

    private static final Logger log = LoggerFactory.getLogger(ExpressFlowStartTest.class);

    @Autowired
    private ExpressFlowStart expressFlowStart;

    @MockBean
    private CustomerOrderInfoMapping customerOrderInfoMapping;
    @MockBean
    private CargoRightTransferService cargoRightTransferService;
    @MockBean
    private ExpressAddressService addressService;
    @MockBean
    private StandardProductAnalysisService standardProductAnalysisService;
    @MockBean
    private AssignWarehouseService separateWarehouseService;
    @MockBean
    private RedisHelper redisHelper;

    /**
     * 测试redis是否执行过期时间方法
     */
    @Test
    void tetAction_expireRedis() {
        CustomerOrderInfoExt customerOrderInfoExt = new CustomerOrderInfoExt();
        customerOrderInfoExt.setOrderNo("IN32407151335338264");

        Mockito.when(customerOrderInfoMapping.mappingCustomerOrderInfo(customerOrderInfoExt)).thenReturn(FlowListenerParam.success("成功"));
        Mockito.when(cargoRightTransferService.auditItemsByFlow(customerOrderInfoExt)).thenReturn(FlowListenerParam.success("成功"));
        Mockito.when(addressService.addressMapping(customerOrderInfoExt)).thenReturn(FlowListenerParam.success("成功"));
        Mockito.when(standardProductAnalysisService.standardProductAnalysis(customerOrderInfoExt)).thenReturn(FlowListenerParam.success("成功"));

        Mockito.when(separateWarehouseService.automatic(customerOrderInfoExt)).thenReturn(FlowListenerParam.success("成功"));

        expressFlowStart.action(customerOrderInfoExt);

        Mockito.verify(redisHelper, Mockito.times(1)).expireExpressFlowNode(customerOrderInfoExt.getOrderNo());

        log.info("finish");
    }
}