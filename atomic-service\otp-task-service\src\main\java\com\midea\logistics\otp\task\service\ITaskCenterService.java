package com.midea.logistics.otp.task.service;

import com.midea.logistics.otp.bean.JobFlowControlRequest;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.custom.TaskDeliveryType;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: ITaskCenterService
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:ITaskCenterService服务接口
 */
public interface ITaskCenterService {

    TaskExt selectOneTaskDetail(Task task);

    List<String> taskNewList(Task task);

    List<String> taskHoldList(Task task);

    List<String> taskFlowControlList(JobFlowControlRequest jobFlowControlRequest);

    List<Task> taskApptList(String taskNo);

    TaskExt selectOneTaskExt(Task task);

    List<Task> tasksByTaskNos(List<String> taskNos);

    List<TaskDeliveryType> tasksDeliveryTypeByTaskNos(List<String> taskNos);

    TaskExt selectLastTaskExt(TaskExt task);

    List<TaskExt> selectInterceptAndLastTaskExt(TaskExt task);

    TaskExt selectDistributionNumOrder(TaskExt task);

    TaskExt selectDistributionNumOrderWithOrderBy(TaskExt task);
    
    List<TaskExt> taskExtByParentOrderNo(Task taskQr);

    List<TaskExt> taskExtByIndex(TaskExt condition);
}