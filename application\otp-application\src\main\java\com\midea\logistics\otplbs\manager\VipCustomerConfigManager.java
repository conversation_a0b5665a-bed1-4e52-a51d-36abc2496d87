package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.rule.VipCustomerConfigFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.helper.bean.VipCustomerConfigExcelRequest;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.rule.domain.bean.VipCustomerConfig;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.midea.msgcenter.domain.bean.Message;
import com.mideaframework.msgcenter.starter.service.ISendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Author:  ex_dengzj4
 * Date:     2021-2-23 14:45
 * Description: VIP客户配置
 */
@Slf4j
@Component
public class VipCustomerConfigManager {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private VipCustomerConfigFeign vipCustomerConfigFeign;
    @Autowired
    private ISendMessageService sendMessageService;
    @Autowired
    private ISsoService iSsoService;
    @Autowired
    private UpDownloadHelper upDownloadHelper;
    @Autowired
    private ReportFeign reportFeign;

    private final String REPORT_OTP_VIP_FLAG_CONFIG = "REPORT_OTP_VIP_FLAG_CONFIG";

    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) {
        JsonResponse response = new JsonResponse();
        try {
            ImportExcel importExcel = new ImportExcel(file, 1, 0, applicationContext);
            int lastCellNum = importExcel.getLastCellNum();
            Class clazz = VipCustomerConfigExcelRequest.class;
            if (lastCellNum != 6) {
                throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
            }
            List<VipCustomerConfigExcelRequest> dataList = importExcel.getDataList(clazz, null);

            new ConfigExcelImportThread(dataList).run();

        } catch (Exception e) {
            response.setCode(BaseCodeEnum.FAILED.getCode());
            response.setMsg(e.getMessage());
        }
        return response;
    }

    /**
     * 异步调用
     */
    public class ConfigExcelImportThread implements Runnable {

        private List<VipCustomerConfigExcelRequest> dataList;

        ConfigExcelImportThread(List<VipCustomerConfigExcelRequest> dataList) {
            this.dataList = dataList;
        }

        @Override
        public void run() {
            try {
                Map<Integer,String> errorMap = Maps.newTreeMap();
                if (CollectionUtils.isEmpty(dataList)) {
                    sendMessage(BaseCodeEnum.SUCCESS.getCode(),"VIP客户配置导入成功");
                    return;
                }
                List<VipCustomerConfig> list = checkExcelDistantData(errorMap, dataList);
                if (errorMap.size() != 0) {
                    throw BusinessException.fail(JSON.toJSONString(errorMap.values()));
                }
                if (!CollectionUtils.isEmpty(list)) {
                    JsonResponse response1 = vipCustomerConfigFeign.addOrUpdateOrDeleteList(list);
                    if (BaseCodeEnum.FAILED.getCode().equals(response1.getCode())) {
                        throw BusinessException.fail(response1.getMsg());
                    }
                }
            } catch (Exception e) {
                sendMessage(BaseCodeEnum.FAILED.getCode(), e.getMessage());
                return;
            }
            sendMessage(BaseCodeEnum.SUCCESS.getCode(), "VIP客户配置导入成功");
        }
    }

    /**
     * excel 重复校验
     */
    private List<VipCustomerConfig> checkExcelDistantData(Map<Integer,String> errorMap, List<VipCustomerConfigExcelRequest> dataList) {
        ArrayList<String> checkList = new ArrayList<>();
        List<VipCustomerConfig> collect = dataList.stream().map(data -> {
            VipCustomerConfig config = new VipCustomerConfig();
            try {
                if (null == data.getId() && CommonConstant.YES.equals(data.getDeleteFlagName())) {
                    throw BusinessException.fail("无法进行删除,ID为空");
                }
                checkParams(data);
                if (StringUtils.isBlank(data.getDeleteFlagName())) {
                    data.setDeleteFlagName(CommonConstant.NO);
                }
                checkIsExistsParams(data);
                initConfig(data, config);
                //新增
                if (null == config.getId() && 0 == config.getDeleteFlag()) {
                    checkDistant(config);
                    if (checkDistantList(checkList, config)) {
                        return config;
                    }
                }
                //更新
                if (null != config.getId() && 0 == config.getDeleteFlag()) {
                    checkIsExistsData(config);
                    checkDistant(config);
                    if (checkDistantList(checkList, config)) {
                        return config;
                    }
                }
                //删除
                if (null != config.getId() && 1 == config.getDeleteFlag()) {
                    checkIsExistsData(config);
                    return config;
                }
            } catch (Exception e) {
                errorMap.put(data.getRow(), "第" + data.getRow() + "行数据异常." + e.getMessage());
            }
            return null;
        }).collect(Collectors.toList());
        collect.removeAll(Collections.singleton(null));
        return collect;
    }

    /**
     * 必填校验
     */
    private void checkParams(VipCustomerConfigExcelRequest request) {
        if (StringUtils.isBlank(request.getCustomerName()) && StringUtils.isBlank(request.getCustomerMobile()) && StringUtils.isBlank(request.getCustomerAddress())) {
            throw BusinessException.fail("客户名称、手机号码、地址不可同时为空");
        }
    }

    /**
     * 校验数据是否存在
     */
    private void checkIsExistsParams(VipCustomerConfigExcelRequest request) {
        String regex = "^1(3|4|5|6|7|8|9)\\d{9}$";
        if (StringUtils.isNotBlank(request.getCustomerMobile()) && !match(regex, request.getCustomerMobile())) {
            throw BusinessException.fail("手机号码格式错误，请重新输入 !");
        }
    }

    /**
     * 初始化config
     */
    private void initConfig(VipCustomerConfigExcelRequest request, VipCustomerConfig config) {
        String customerName = StringUtils.isBlank(request.getCustomerName()) ? "" : request.getCustomerName().replace(" ", "");
        String customerMobile = StringUtils.isBlank(request.getCustomerMobile()) ? "" : request.getCustomerMobile().replace(" ", "");
        String customerAddress = StringUtils.isBlank(request.getCustomerAddress()) ? "" : request.getCustomerAddress().replace(" ", "");
        String jobTitle = StringUtils.isBlank(request.getJobTitle()) ? "" : request.getJobTitle();

        config.setId(request.getId());
        config.setDeleteFlag(CommonConstant.YES.equals(request.getDeleteFlagName()) ? 1 : 0);
        config.setCustomerName(customerName);
        config.setCustomerMobile(customerMobile);
        config.setCustomerAddress(customerAddress);
        config.setJobTitle(jobTitle);
        config.setCreateUserCode(iSsoService.getUserCode());
        config.setUpdateUserCode(iSsoService.getUserCode());
    }

    /**
     * 数据库校验重复
     */
    private void checkDistant(VipCustomerConfig config) {
        JsonResponse<List<Long>> response = vipCustomerConfigFeign.checkIsExist(config);
        if (BaseCodeEnum.FAILED.getCode().equals(response.getCode())) {
            throw BusinessException.fail("调用rule服务失败." + response.getMsg() +  ";");
        }
        List<Long> ids = response.getData();
        if ((null == config.getId() && !CollectionUtils.isEmpty(ids)) || (!CollectionUtils.isEmpty(ids) && null != config.getId() && !ids.contains(config.getId()))) {
            throw BusinessException.fail("记录已存在ID为" + response.getData().get(0) + "，完成新增;");
        }
    }

    /**
     * 数据库是否存在
     */
    private void checkIsExistsData(VipCustomerConfig config) {
        JsonResponse<VipCustomerConfig> response = vipCustomerConfigFeign.queryByBusinessKey(config.getId());
        if (BaseCodeEnum.FAILED.getCode().equals(response.getCode())) {
            throw BusinessException.fail("调用rule服务失败." + response.getMsg() + ";");
        }
        if (null == response.getData()) {
            throw BusinessException.fail("记录不存在或已经删除，无法进行更新、删除");
        }
        config.setVersion(response.getData().getVersion());
    }

    /**
     * 过滤新增更新重复数据
     */
    private boolean checkDistantList(ArrayList<String> checkList, VipCustomerConfig config) {
        String key = config.getCustomerName() + "_"
            + config.getCustomerMobile() + "_"
            + config.getCustomerAddress()
            ;
        if (!checkList.contains(key)) {
            checkList.add(key);
            return true;
        }
        return false;
    }

    /**
     * 正则表达式校验
     */
    private static boolean match(String regex, String str) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    private void sendMessage(String code, String msg) {
        //发送消息
        Message commonMessage = new Message();
        commonMessage.setTitle("VIP客户配置导入" + (BaseCodeEnum.SUCCESS.getCode().equals(code) ? "成功" : "失败"));
        commonMessage.setMessageType(1);
        commonMessage.setContent(msg);
        ArrayList<String> list = new ArrayList<>();
        list.add(iSsoService.getUserCode());
        commonMessage.setReceiverList(list);
        commonMessage.setApplicationCode(dictHelper.getDictVaule(REPORT_OTP_VIP_FLAG_CONFIG,"applicationCode"));
        commonMessage.setCreateTime(new Date());
        sendMessageService.sendMessage("3", commonMessage);
    }

    public JsonResponse importVipCustomerConfig(@RequestParam("file") MultipartFile file) {
        ImportFileRequest request = upDownloadHelper.uploadFileToOss(file);
        return reportFeign.importVipCustomerConfig(request);
    }


    public JsonResponse importVpCustomerConfig(@RequestParam("file") MultipartFile file) {
        ImportFileRequest request = upDownloadHelper.uploadFileToOss(file);
        return reportFeign.importVpCustomerConfig(request);
    }
}
