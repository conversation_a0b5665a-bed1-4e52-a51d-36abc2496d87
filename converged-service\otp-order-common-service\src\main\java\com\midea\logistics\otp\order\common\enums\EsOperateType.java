package com.midea.logistics.otp.order.common.enums;

public enum EsOperateType {
    
    //海康威视采购入库
    HKWS_PI_ORDER("hkws_pi_order","orderNo"),
    
    //海康威视采购入库_SN码
    HKWS_PI_ORDER_SN("hkws_pi_order_sn","orderNo"),

    //海康威视调拨入库
    HKWS_AI_ORDER("hkws_ai_order","orderNo"),

    //海康威视调拨入库_SN码
    HKWS_AI_ORDER_SN("hkws_ai_order_sn","orderNo"),

    //海康威视区域仓采购入库
    HIKVISION_PI_ORDER("hikvision_pi_order","orderNo"),

    //海康威视区域仓采购入库_SN码
    HIKVISION_PI_ORDER_SN("hikvision_pi_order_sn","orderNo"),
    
    //变更申请明细数据
    CHANGE_APPLY_DETAIL_DATA("change_apply_detail_data","applyNo"),

    //信息安全操作记录
    INFORMATION_SECURITY_OPERATION("information_security_operation","uniqueKey"),

    //夜郎古酒sn码订单
    YLGJ_ORDER("ylgj_sn_order","orderNo"),

    //夜郎古酒SN码
    YLGJ_ORDER_SN("ylgj_order_sn","orderNo"),

    // 订单SN码数据
    ORDER_SN_CODE_INDEX("order_sn_code_index","orderNo"),

    //增值服务订单日志
    VD_SERVICE_ORDER_DATA("vd_service_order_data","vdOrderNo"),

    // 顺丰国补推送数据
    SF_COUNTRY_SUBSIDY_INFO("sf_country_subsidy_info","waybillNo"),

    ;
    EsOperateType(String index, String router) {
        this.index = index;
        this.router = router;
    }

    private String index;
    private  String router;

    public String getIndex() {
        return index;
    }

    public String getRouter() {
        return router;
    }

    public static String getName(String key){
        String name = "";
        if (null == key){
            return  name;
        }
        EsOperateType[] values = EsOperateType.values();
        for (EsOperateType value : values) {
            if (value.getIndex().equals(key)){
                name = value.getRouter();
                break;
            }
        }
        return  name;
    }

}
