package com.midea.logistics.otp.orderverify.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.helper.IdGenHelper;
import com.midea.logistics.otp.constants.MessageQueueDefine;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.orderverify.start.ExpressFlowStart;
import com.midea.logistics.otp.orderverify.start.ExpressSubFlowStart;
import com.mideaframework.transactionservice.mq.MqConsumer;
import com.mideaframework.transactionservice.mq.consumer.MQMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* @description: 手动触发流程
* @author: 陈永培
* @createtime: 2021/10/4 14:39
*/
@Component
@Slf4j
@MqConsumer(topic = MessageQueueDefine.EXPRESS_CUSTOMER_ORDER_TOPIC,tag = MessageQueueDefine.EXPRESS_CUSTOMER_ORDER_TAG)
public class ExpressCustomerOrderFlowConsumer implements MQMessageHandler {


    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private ExpressFlowStart expressFlowStart;
    @Autowired
    private ExpressSubFlowStart expressSubFlowStart;


    @Override
    public void handlerMessage(MessageExt messageExt) {

        MDC.put("traceId", UUID.randomUUID().toString());
        String orderNo = new String(messageExt.getBody());

        log.info(" mq消息 "+ MessageQueueDefine.EXPRESS_CUSTOMER_ORDER_TOPIC +"   消费内容 message =  {}", orderNo);

        if (StringUtils.isEmpty(orderNo)) {
            log.warn("订单号不能为空");
            return;
        }

        startFlow(orderNo);
    }



    public void startFlow(String orderNo) {

        List<String> tmpParentOrderNos = new ArrayList<>();
        List<CustomerOrderInfo> parentOrders = new ArrayList<>();
        List<OrderInfo> annntoOrders = new ArrayList<>();

        // 旧流程
        // AN 开头的单号，不检查父单
        if (!orderNo.startsWith(IdGenHelper.ANNTO_PREFIX) && !orderNo.startsWith(IdGenHelper.BN_PREFIX)){
            CustomerOrderInfo customerOrderInfo = orderverifyHelper.getCustomerOrderInfo(orderNo);
            if (customerOrderInfo != null) {
                Integer orderStatus = customerOrderInfo.getOrderStatus();
                tmpParentOrderNos.add(orderNo);
                // 不需要走审核的订单
                if (orderStatus >= 200 ) {
                    log.info("====> 订单 {} 状态为 {}, 不进行审核！", orderNo, orderStatus);
                } else if (CommonConstant.APARTED.equals(customerOrderInfo.getApartStatus())) {
                    log.info("====> 订单 {} 状态为 {}, 不进行审核！", orderNo, customerOrderInfo.getApartStatus());
                }/* else if ((System.currentTimeMillis() - customerOrderInfo.getUpdateTime().getTime() < 60 * 1000 ) && orderStatus > 100) {
                    log.info("====> 订单 {}, {} 最后一次更新才不到一分钟, 不进行审核！", orderNo,  orderStatus);
                }*/ else {
                    parentOrders.add(customerOrderInfo);
                }
            }
        }


        // IN 开头的单号，不检查子单
        if (!orderNo.startsWith("IN")){
            OrderInfo orderInfo = orderverifyHelper.getOrderInfo(orderNo);
            if (orderInfo != null) {
                Integer orderStatus = orderInfo.getOrderStatus();
                // 不需要走审核的订单
                if (orderStatus >= 200 ) {
                    log.info("====> 订单 {} 状态为 {}, 不进行审核！", orderNo,  orderStatus);
                } else if ((System.currentTimeMillis() - orderInfo.getUpdateTime().getTime() < 60 * 1000 ) && orderStatus > 100) {
                    log.info("====> 订单 {}, {} 最后一次更新才不到一分钟, 不进行审核！", orderNo,  orderStatus);
                } else {
                    annntoOrders.add(orderInfo);
                }
            }
        }

        // 父单号查找子单号
        tmpParentOrderNos.forEach(parentOrderNo -> {
            List<OrderInfo> list = orderverifyHelper.getOrderInfoByParentNo(parentOrderNo);
            List<OrderInfo> orders = list.stream().filter(order -> order.getOrderStatus() < 200).collect(Collectors.toList());
            annntoOrders.addAll(orders);
        });

        // 有子单时，该IN单号父单流程不进行审核
        if (!annntoOrders.isEmpty()){
            parentOrders.clear();
        }


        // 触发审核
        Integer success = 0;
        for (CustomerOrderInfo parentOrder : parentOrders) {
            success += expressFlowStart.startFlow(parentOrder);
        }

        for (OrderInfo anntoOrder : annntoOrders) {
            success += expressSubFlowStart.startFlow(anntoOrder);
        }

        if (success == 0){
            log.error("无可触发审核的订单，触发条件： {}",orderNo);
            return;
        }

        String c = CollectionUtils.isEmpty(parentOrders)? "": "客户单号 "+orderNo+" 触发审核成功";
        List<String> annntoOrderNos = annntoOrders.stream().map(OrderInfo::getOrderNo).collect(Collectors.toList());
        String a = CollectionUtils.isEmpty(annntoOrders)? "": "子单号 "+JSONObject.toJSONString(annntoOrderNos)+" 触发审核成功";
        log.info("======> {}  {}",c,a);
    }
}


