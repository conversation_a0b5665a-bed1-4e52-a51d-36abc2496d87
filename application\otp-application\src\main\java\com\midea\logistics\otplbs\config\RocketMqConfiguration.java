package com.midea.logistics.otplbs.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.log.ClientLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 抵押功能
 * @date 2019年08月30日 上午09:31
 */
@Data
@Configuration
@Slf4j
public class RocketMqConfiguration {

    @Value("${rocketmq.client.logLevel:INFO}")
    private String clientLogLevel ;

    @Value("${rocketmq.client.logRoot:/logs/rocketmqlogs}")
    private String clientLogRoot ;

    @Value("${rocketmq.client.logFileMaxIndex:10}")
    private String clientLogMaxindex ;

    @Value("${rocketmq.client.logFileName:rocketmq_client.log}")
    private String clientLogFilename ;

    @Value("${rocketmq.client.logFileMaxSize:1073741824}")
    private String clientLogFilesize ;

    @Value("${rocketmq.client.logAsyncQueueSize:1024}")
    private String clientLogAsyncQueuesize ;



    @Bean
    public RocketMqConfiguration RocketMqConfiguration(){
        log.info("rocketmq 日记配置: clientLogLevel = {},clientLogRoot = {} ,clientLogMaxindex = {} ,clientLogFilename = {} ,clientLogFilesize = {} ,clientLogAsyncQueuesize = {}",clientLogLevel ,clientLogRoot,clientLogMaxindex ,clientLogFilename,clientLogFilesize ,clientLogAsyncQueuesize);
        System.setProperty(ClientLogger.CLIENT_LOG_LEVEL, clientLogLevel); //日记级别
        System.setProperty(ClientLogger.CLIENT_LOG_ROOT, System.getProperty("user.home") + clientLogRoot); //日记文件路径
        System.setProperty(ClientLogger.CLIENT_LOG_MAXINDEX, clientLogMaxindex); //日记最大文件数量大小
        System.setProperty(ClientLogger.CLIENT_LOG_FILENAME, clientLogFilename); //日记文件名
        System.setProperty(ClientLogger.CLIENT_LOG_FILESIZE, clientLogFilesize); //日记文件大小
        System.setProperty(ClientLogger.CLIENT_LOG_ASYNC_QUEUESIZE, clientLogAsyncQueuesize); //日记异步队列大小
        return this;
    }
}
