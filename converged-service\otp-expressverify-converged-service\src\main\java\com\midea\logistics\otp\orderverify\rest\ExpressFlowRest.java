package com.midea.logistics.otp.orderverify.rest;

import com.midea.logistics.otp.orderverify.mq.consumer.ExpressCustomerOrderFlowConsumer;
import com.midea.logistics.otp.orderverify.start.ExpressFlowStart;
import com.midea.logistics.otp.orderverify.start.ExpressSubFlowStart;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.RestDoing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
public class ExpressFlowRest {

    @Autowired
    private ExpressFlowStart expressFlowStart;
    @Autowired
    private ExpressSubFlowStart expressSubFlowStart;
    @Autowired
    private ExpressCustomerOrderFlowConsumer expressCustomerOrderFlowConsumer;

    /**
    * @description: 启动主流程
    * @param: [orderNo, request]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2021/10/12 9:18
    */
    @GetMapping("/expressFlowStart")
    public JsonResponse expressFlowStart(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            expressFlowStart.startFlow(orderNo);
        };
        return doing.go(request, log);
    }

    /**
     * @description: 启动子流程
     * @param: [orderNo, request]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2021/10/12 9:18
     */
    @GetMapping("/expressSubFlowStart")
    public JsonResponse expressSubFlowStart(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            expressSubFlowStart.startFlow(orderNo);
        };
        return doing.go(request, log);
    }



    @PostMapping("/expressMqRepeatFlowStart")
    public JsonResponse expressMqRepeatFlowStart(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            expressCustomerOrderFlowConsumer.startFlow(orderNo);
        };
        return doing.go(request, log);
    }

}
