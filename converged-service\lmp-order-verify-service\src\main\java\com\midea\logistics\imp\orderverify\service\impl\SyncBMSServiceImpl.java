package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.imp.orderverify.service.SyncBmsService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.CustomerContractAgingHelper;
import com.midea.logistics.otp.order.common.mq.producer.CustomerContractAgingProducer;
import com.midea.logistics.otp.order.common.mq.producer.LotsSaveAnntoOrderProducer;
import com.midea.logistics.otp.order.common.mq.producer.OrderVerifyBmsProducer;
import com.midea.logistics.otp.order.common.mq.producer.SyncOrderInfoToPmsProducer;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.SyncOrderInfoToPmsDto;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SyncBMSServiceImpl implements SyncBmsService {

    @Autowired
    private OrderVerifyBmsProducer orderVerifyBmsProducer;
    @Autowired
    private LotsSaveAnntoOrderProducer lotsSaveAnntoOrderProducer;
    @Autowired
    private CustomerContractAgingProducer customerContractAgingProducer;
    @Autowired
    private CustomerContractAgingHelper customerContractAgingHelper;
    @Autowired
    private SyncOrderInfoToPmsProducer syncOrderInfoToPmsProducer;

    @Override
    @ZeebeFlow("SYNC_BMS")
    public OrderInfoExt syncBms(OrderInfoExt orderInfoExt) {
        OrderInfo orderInfo = orderInfoExt;
        if (CommonConstant.FLAG_YES.equals(orderInfo.getPlanOrderFlag())){
            return orderInfoExt;
        }
        if (StringUtils.isEmpty(orderInfo.getInvoiceUnitCode())) {
            FlowListenerParam.success("不同步BMS");
            return orderInfoExt;
        }
        if (OrderType.isSOOrder(orderInfo.getOrderType())) {
            FlowListenerParam.success("服务单不同步BMS");
            return orderInfoExt;
        }
        // mq异步
        orderVerifyBmsProducer.sent(orderInfo);
        FlowListenerParam.success("BMS订单同步成功");
        return orderInfoExt;
    }

    @Override
    @ZeebeFlow("PUSH_LOTS")
    public OrderInfoExt synclotp(OrderInfoExt orderInfoExt) {
        OrderInfo orderInfo = orderInfoExt;

        try{
            if(customerContractAgingHelper.checkDoAgingParse(orderInfo)){
                log.info("客户合同时效解析-推送 orderNo={}", orderInfo.getOrderNo());
                customerContractAgingProducer.agingParse(orderInfo.getOrderNo());
            }
        }catch (Exception e){
            log.error("客户合同时效解析-推送失败 orderNo={} exception={}", orderInfo.getOrderNo(),e.getMessage());
        }

        //cyy:异步同步PMS子单信息
        syncOrderInfoToPmsProducer.sent(new SyncOrderInfoToPmsDto(orderInfo.getOrderNo(), orderInfo.getParentOrderNo(), PmsMsgType.SUB_RECEIVE.getKey()));

        //zbs:【是否模糊发车单补单=是】订单审核时，跳过【推送查单系统】节点
        if (CustomFlag.isPlanDispatchOrder(orderInfo.getPlanOrderFlag())) {
            FlowListenerParam.success("模糊发车单跳过推查单");
            return orderInfoExt;
        }
        boolean sent = lotsSaveAnntoOrderProducer.sent(orderInfo);
        if (!sent) {
            throw BusinessException.fail("订单信息推送查单系统失败"+ JSONObject.toJSONString(orderInfoExt.getOrderNo()));
        }

        FlowListenerParam.success("LOTP订单同步成功");
        return orderInfoExt;
    }
}
