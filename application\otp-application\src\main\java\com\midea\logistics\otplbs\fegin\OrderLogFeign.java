package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.OrderLog;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: OrderLogFeign
 * Author: lomo.deng
 * Date: 2019-5-17 14:57:37
 * Description:订单日志表 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface OrderLogFeign {

    @RequestMapping(value = "/orderLog", method = RequestMethod.POST)
    JsonResponse create(@RequestBody OrderLog orderLog);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderLog/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderLog/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderLog orderLog);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/orderLog/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
     * 根据安得单号(或任务号或运单号或客户订单号)查询该订单详情操作日志集合
     *
     * @param orderLog
     * @return
     */
    @RequestMapping(value = "/orderLogs", method = RequestMethod.POST)
    JsonResponse search(@SpringQueryMap OrderLog orderLog);
}
