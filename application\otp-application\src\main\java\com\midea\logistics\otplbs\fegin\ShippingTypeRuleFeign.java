package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.midea.logistics.otp.rule.domain.request.ShippingTypeRuleRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ShippingTypeRuleFeign
 * Author: luoh
 * Date: 2019-6-3 16:30:57
 * Description:配送方式规则 feign
 */
@FeignClient(value = "logistics-otp-rule-service")
public interface ShippingTypeRuleFeign {

    @RequestMapping(value = OptRuleRouters.SHIPPING_TYPE_RULE_SAVE, method = RequestMethod.POST)
    JsonResponse create(@RequestBody ShippingTypeRuleRequest shippingTypeRuleRequest);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.SHIPPING_TYPE_RULE_DETAIL, method = RequestMethod.GET)
    JsonResponse<ShippingTypeRule> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.SHIPPING_TYPE_RULE_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ShippingTypeRule shippingTypeRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.SHIPPING_TYPE_RULE_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @PostMapping(OptRuleRouters.SHIPPING_TYPE_RULE_LIST)
    JsonResponse<PageResponse<ShippingTypeRule>> search(@RequestBody ShippingTypeRuleRequest shippingTypeRuleRequest);


    @PostMapping(OptRuleRouters.SHIPPING_TYPE_RULE_PAGE)
    JsonResponse<PageResponse<ShippingTypeRule>> searchPage(@RequestBody ShippingTypeRuleRequest shippingTypeRuleRequest);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.SHIPPING_TYPE_RULE_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<ShippingTypeRuleRequest> shippingTypeRuleRequests);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.SHIPPING_TYPE_RULE_BATCH_DELETE_BY_BUSINESSKEY, method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
