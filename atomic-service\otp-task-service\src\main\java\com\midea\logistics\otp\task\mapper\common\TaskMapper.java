package com.midea.logistics.otp.task.mapper.common;

import com.midea.logistics.otp.order.converged.domain.request.OrderChangedReceiveDateRequest;
import com.midea.logistics.otp.order.domain.dto.AiVoiceAppointDto;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskZsjx;
import com.midea.logistics.otp.task.domain.bean.custom.TaskExt;
import com.mideaframework.core.bean.PagingDomain;
import com.mideaframework.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: TaskMapper
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:TaskMapper服务接口
 */
@Mapper
public interface TaskMapper extends BaseMapper<Task> {

    Integer updateTaskCreateTime(List<OrderChangedReceiveDateRequest> var);

    Integer batchUpdateByParentOrderNo(List<Task> tasks);

    List<String> selectCancelTaskByUpdateTime(Task task);

    Integer selectCarArrivedTaskByIndexCount(Task task);

    List<Task> selectCarArrivedTaskByIndex(Task task);

    Integer updateByIndex(Task task);

    Integer updateTaskCreateTimeAndFlag(List<OrderChangedReceiveDateRequest> var);

    TaskExt selectOneDetail(Task task);

    List<Task> queryByTaskNos(List<String> taskNos);

    Integer deleteBatchById(List<Long> ids);

    List<TaskExt> selectTaskDetail(Task task);

    List<Task> selectAllTaskIncludeDel(Task task);

    Integer deleteChangeWarehouseTask(Task task);

    Integer deleteTaskOfTI(Task task);
	
	Integer clearHoldFlag(Task task);
	
	Integer clearHoldFlagAndSetInvoice(Task task);

	Integer setInvoiceInfo(Task task);

    Task selectTaskByTaskNoIncludeDel(Task task);


    TaskExt selectOneDetailWithOrderBy(Task task);

	Integer updateTaskRelationNo(Task task);


    Integer getCancelOrderNo(@Param("orderNo") String orderNo);

    Integer batchUpdateByTaskNo(List<Task> tasks);

    List<Task> queryByOrderNos(List<String> orderNos);

    Integer batchClearHoldFlagByTaskNo(List<String> taskNos);

    List<TaskZsjx> searchTaskListByDate(Task task);

    List<TaskZsjx> queryByTaskNosZs(List<String> taskNos);

    List<TaskZsjx> queryByTaskIds(@Param("startId") Long startId,@Param("endId") Long endId);

    List<Task> handOverTimeSearchTasks(AiVoiceAppointDto aiVoiceAppointDto);

    List<Task> holdAppointSearchTasks(AiVoiceAppointDto aiVoiceAppointDto);
    
    Integer deleteByIdAndIncrementOrderNo(Task task);
    
    Integer getDeleteOrderNo(@Param("orderNo") String orderNo);

    Integer batchClearHoldFlagByCompleteSetNos(List<String> completeSetNos);
}