<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.task.mapper.common.TaskMapper">


    <select id="getDeleteOrderNo" resultType = "java.lang.Integer">
        select
        IFNULL(SUBSTRING_INDEX(`order_no`, '#', -1)+0, 0)
        from
        task
        where
        order_no like CONCAT(replace(#{orderNo},'_','\_'),'#%')
        order by SUBSTRING_INDEX(order_no, '#', -1)+0 desc limit 1
    </select>

    <update id="deleteByIdAndIncrementOrderNo">
        update
        task t
        set
        `delete_flag`=1 ,
        `order_no` = #{orderNo}
        where
        `id` = #{id}
    </update>

    <update id="updateTaskCreateTime">
        <foreach collection="list" item="item" separator=";">
            update
                task t
            set
                t.create_time = now(),
                t.order_time = now(),
                t.expect_arrive_start_time = #{item.receiveDate}
            where
                t.parent_order_no = #{item.parentOrderNo}
                and t.delete_flag = 0
        </foreach>
    </update>

    <update id="batchUpdateByParentOrderNo">
        <foreach collection="list" item="item" separator=";">
            update
            task t
            <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.setBatchFieldsSql"/>
            where
            t.parent_order_no = #{item.parentOrderNo}
            and t.delete_flag = 0
        </foreach>
    </update>


    <select id="selectCancelTaskByUpdateTime" resultType="java.lang.String">
        select
        `task_no` AS  taskNo
        from logistics_otp_order.task t
        where
          t.delete_flag = '0'
          and t.order_status = '999'
          and t.`update_time` >= #{updateTime}
          order by t.`update_time` desc
          limit 1000
    </select>

    <select id="selectCarArrivedTaskByIndexCount" resultType="java.lang.Integer">
        select count(*)
        from task t
        <include refid="whereFieldsSql"/>
        <if test="isSearchAllFlag == null or isSearchAllFlag == 0">
            and  <![CDATA[ order_status < 310 ]]>
            and `in_out_type` = 'IN'
            and (join_type is null or join_type != 'SHARE_2O')
        </if>
    </select>

    <select id="selectCarArrivedTaskByIndex" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        from task t
        <include refid="whereFieldsSql"/>
        <if test="isSearchAllFlag == null or isSearchAllFlag == 0">
            and  <![CDATA[ order_status < 310 ]]>
            and `in_out_type` = 'IN'
            and (join_type is null or join_type != 'SHARE_2O')
        </if>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
                ${orderByType}
            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <update id="updateByIndex">
        update
        task t
        <include refid="setFieldsSql"/>
        where
        `task_no` =#{taskNo}
        <if test="version !=null and version != 0">
            and `version` = #{version}
        </if>
    </update>

    <sql id="whereFieldsSqlPre">
        where t.delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and t.`order_type` =#{orderType}
        </if>
        <if test="requireOutTime !=null">
            and t.`require_out_time` =#{requireOutTime}
        </if>
        <if test="consigneeTimeFrom !=null">
            and t.`consignee_time_from` =#{consigneeTimeFrom}
        </if>
        <if test="companyName !=null and companyName != ''">
            and t.`company_name` =#{companyName}
        </if>
        <if test="orderWhCode !=null and orderWhCode != ''">
            and t.`order_wh_code` =#{orderWhCode}
        </if>
        <if test="mainWaybillNo !=null and mainWaybillNo != ''">
            and t.`main_waybill_no` =#{mainWaybillNo}
        </if>
        <if test="totalGrossWeight !=null">
            and t.`total_gross_weight` =#{totalGrossWeight}
        </if>
        <if test="deliveredVerifyFlag !=null">
            and t.`delivered_verify_flag` =#{deliveredVerifyFlag}
        </if>
        <if test="whName !=null and whName != ''">
            and t.`wh_name` =#{whName}
        </if>
        <if test="payType !=null and payType != ''">
            and t.`pay_type` =#{payType}
        </if>
        <if test="upperCustomerCode !=null and upperCustomerCode != ''">
            and t.`upper_customer_code` =#{upperCustomerCode}
        </if>
        <if test="targeSiteCode !=null and targeSiteCode != ''">
            and t.`targe_site_code` =#{targeSiteCode}
        </if>
        <if test="ifUpStairs !=null and ifUpStairs != ''">
            and t.`if_up_stairs` =#{ifUpStairs}
        </if>
        <if test="installFlag !=null">
            and t.`install_flag` =#{installFlag}
        </if>
        <if test="id !=null">
            and t.`id` =#{id}
        </if>
        <if test="targetCustomerCode !=null and targetCustomerCode != ''">
            and t.`target_customer_code` =#{targetCustomerCode}
        </if>
        <if test="taskStatus !=null">
            and t.`task_status` =#{taskStatus}
        </if>
        <if test="qzWarehAreaFlag !=null and qzWarehAreaFlag != ''">
            and t.`qz_wareh_area_flag` =#{qzWarehAreaFlag}
        </if>
        <if test="grayFlag !=null and grayFlag != ''">
            and t.`gray_flag` =#{grayFlag}
        </if>
        <if test="agingProductCode !=null and agingProductCode != ''">
            and t.`aging_product_code` =#{agingProductCode}
        </if>
        <if test="receiverSaleStype !=null and receiverSaleStype != ''">
            and t.`receiver_sale_stype` =#{receiverSaleStype}
        </if>
        <if test="nextDistributionWhCode !=null and nextDistributionWhCode != ''">
            and t.`next_distribution_wh_code` =#{nextDistributionWhCode}
        </if>
        <if test="orderConfirmTime !=null">
            and t.`order_confirm_time` =#{orderConfirmTime}
        </if>
        <if test="orderSiteName !=null and orderSiteName != ''">
            and t.`order_site_name` =#{orderSiteName}
        </if>
        <if test="totalPkgQty !=null">
            and t.`total_pkg_qty` =#{totalPkgQty}
        </if>
        <if test="relationOrderNo !=null and relationOrderNo != ''">
            and t.`relation_order_no` =#{relationOrderNo}
        </if>
        <if test="invoiceUnitName !=null and invoiceUnitName != ''">
            and t.`invoice_unit_name` =#{invoiceUnitName}
        </if>
        <if test="expectArriveStartTime !=null">
            and t.`expect_arrive_start_time` =#{expectArriveStartTime}
        </if>
        <if test="targeWhCode !=null and targeWhCode != ''">
            and t.`targe_wh_code` =#{targeWhCode}
        </if>
        <if test="lineAging !=null">
            and t.`line_aging` =#{lineAging}
        </if>
        <if test="customerName !=null and customerName != ''">
            and t.`customer_name` =#{customerName}
        </if>
        <if test="excuteStatus !=null">
            and t.`excute_status` =#{excuteStatus}
        </if>
        <if test="upperReceiverCode !=null and upperReceiverCode != ''">
            and t.`upper_receiver_code` =#{upperReceiverCode}
        </if>
        <if test="lineCost !=null">
            and t.`line_cost` =#{lineCost}
        </if>
        <if test="cloudWhFlag !=null and cloudWhFlag != ''">
            and t.`cloud_wh_flag` =#{cloudWhFlag}
        </if>
        <if test="upperReferenceId !=null and upperReferenceId != ''">
            and t.`upper_reference_id` =#{upperReferenceId}
        </if>
        <if test="freightBasis !=null and freightBasis != ''">
            and t.`freight_basis` =#{freightBasis}
        </if>
        <if test="carrierCode !=null and carrierCode != ''">
            and t.`carrier_code` =#{carrierCode}
        </if>
        <if test="deliverypayType !=null">
            and t.`deliverypay_type` =#{deliverypayType}
        </if>
        <if test="totalNetWeight !=null">
            and t.`total_net_weight` =#{totalNetWeight}
        </if>
        <if test="zoneCode !=null and zoneCode != ''">
            and t.`zone_code` =#{zoneCode}
        </if>
        <if test="planOrderFlag !=null">
            and t.`plan_order_flag` =#{planOrderFlag}
        </if>
        <if test="distributionWhName !=null and distributionWhName != ''">
            and t.`distribution_wh_name` =#{distributionWhName}
        </if>
        <if test="upstreamDocType !=null and upstreamDocType != ''">
            and t.`upstream_doc_type` =#{upstreamDocType}
        </if>
        <if test="loadType !=null and loadType != ''">
            and t.`load_type` =#{loadType}
        </if>
        <if test="unitAreaName !=null and unitAreaName != ''">
            and t.`unit_area_name` =#{unitAreaName}
        </if>
        <if test="sourceSystem !=null and sourceSystem != ''">
            and t.`source_system` =#{sourceSystem}
        </if>
        <if test="distributionFlag !=null">
            and t.`distribution_flag` =#{distributionFlag}
        </if>
        <if test="contractNo !=null and contractNo != ''">
            and t.`contract_no` =#{contractNo}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and t.`customer_code` =#{customerCode}
        </if>
        <if test="siteName !=null and siteName != ''">
            and t.`site_name` =#{siteName}
        </if>
        <if test="lineName !=null and lineName != ''">
            and t.`line_name` =#{lineName}
        </if>
        <if test="supplierCode !=null and supplierCode != ''">
            and t.`supplier_code` =#{supplierCode}
        </if>
        <if test="orderValue !=null">
            and t.`order_value` =#{orderValue}
        </if>
        <if test="equipmentType !=null and equipmentType != ''">
            and t.`equipment_type` =#{equipmentType}
        </if>
        <if test="lineMileage !=null">
            and t.`line_mileage` =#{lineMileage}
        </if>
        <if test="specCarrierFlag !=null and specCarrierFlag != ''">
            and t.`spec_carrier_flag` =#{specCarrierFlag}
        </if>
        <if test="upperOrderType !=null and upperOrderType != ''">
            and t.`upper_order_type` =#{upperOrderType}
        </if>
        <if test="returnReason !=null and returnReason != ''">
            and t.`return_reason` =#{returnReason}
        </if>
        <if test="taskNo !=null and taskNo != ''">
            and t.`task_no` =#{taskNo}
        </if>
        <if test="projectClassify !=null and projectClassify != ''">
            and t.`project_classify` =#{projectClassify}
        </if>
        <if test="orderEndTime !=null">
            and t.`order_end_time` =#{orderEndTime}
        </if>
        <if test="cancelReason !=null and cancelReason != ''">
            and t.`cancel_reason` =#{cancelReason}
        </if>
        <if test="specimenType !=null and specimenType != ''">
            and t.`specimen_type` =#{specimenType}
        </if>
        <if test="inWhTime !=null">
            and t.`in_wh_time` =#{inWhTime}
        </if>
        <if test="workOrderNo !=null and workOrderNo != ''">
            and t.`work_order_no` =#{workOrderNo}
        </if>
        <if test="oaid !=null and oaid != ''">
            and t.`oaid` =#{oaid}
        </if>
        <if test="businessMode !=null and businessMode != ''">
            and t.`business_mode` =#{businessMode}
        </if>
        <if test="dispatchNo !=null and dispatchNo != ''">
            and t.`dispatch_no` =#{dispatchNo}
        </if>
        <if test="expectInstallStartTime !=null">
            and t.`expect_install_start_time` =#{expectInstallStartTime}
        </if>
        <if test="collectionAmount !=null">
            and t.`collection_amount` =#{collectionAmount}
        </if>
        <if test="thirdFlag !=null and thirdFlag != ''">
            and t.`third_flag` =#{thirdFlag}
        </if>
        <if test="printBarcode !=null">
            and t.`print_barcode` =#{printBarcode}
        </if>
        <if test="expectInstallType !=null and expectInstallType != ''">
            and t.`expect_install_type` =#{expectInstallType}
        </if>
        <if test="arriveAppointmentRequireFlag !=null">
            and t.`arrive_appointment_require_flag` =#{arriveAppointmentRequireFlag}
        </if>
        <if test="updateTime !=null">
            and t.`update_time` =#{updateTime}
        </if>
        <if test="customerTotalQty !=null">
            and t.`customer_total_qty` =#{customerTotalQty}
        </if>
        <if test="expectArriveEndTime !=null">
            and t.`expect_arrive_end_time` =#{expectArriveEndTime}
        </if>
        <if test="operationsCenterCode !=null and operationsCenterCode != ''">
            and t.`operations_center_code` =#{operationsCenterCode}
        </if>
        <if test="startTime != null and endTime != null">
            <![CDATA[
                        and t.`create_time` >= #{startTime} and t.`create_time` <= #{endTime}
                        ]]>
        </if>
        <if test="upperWhName !=null and upperWhName != ''">
            and t.`upper_wh_name` =#{upperWhName}
        </if>
        <if test="upperSenderName !=null and upperSenderName != ''">
            and t.`upper_sender_name` =#{upperSenderName}
        </if>
        <if test="deliveryCycle !=null and deliveryCycle != ''">
            and t.`delivery_cycle` =#{deliveryCycle}
        </if>
        <if test="businessCategory !=null and businessCategory != ''">
            and t.`business_category` =#{businessCategory}
        </if>
        <if test="distributionLastFlag !=null">
            and t.`distribution_last_flag` =#{distributionLastFlag}
        </if>
        <if test="customerOrderNo !=null and customerOrderNo != ''">
            and t.`customer_order_no` =#{customerOrderNo}
        </if>
        <if test="customerAgingCode !=null and customerAgingCode != ''">
            and t.`customer_aging_code` =#{customerAgingCode}
        </if>
        <if test="payDate !=null">
            and t.`pay_date` =#{payDate}
        </if>
        <if test="totalVolume !=null">
            and t.`total_volume` =#{totalVolume}
        </if>
        <if test="serviceType !=null and serviceType != ''">
            and t.`service_type` =#{serviceType}
        </if>
        <if test="totalQty !=null">
            and t.`total_qty` =#{totalQty}
        </if>
        <if test="payTime !=null">
            and t.`pay_time` =#{payTime}
        </if>
        <if test="stationType !=null and stationType != ''">
            and t.`station_type` =#{stationType}
        </if>
        <if test="orderSiteCode !=null and orderSiteCode != ''">
            and t.`order_site_code` =#{orderSiteCode}
        </if>
        <if test="distributionNum !=null">
            and t.`distribution_num` =#{distributionNum}
        </if>
        <if test="consolidationNum !=null">
            and t.`consolidation_num` =#{consolidationNum}
        </if>
        <if test="parentOrderNo !=null and parentOrderNo != ''">
            and t.`parent_order_no` =#{parentOrderNo}
        </if>
        <if test="distributionSiteCode !=null and distributionSiteCode != ''">
            and t.`distribution_site_code` =#{distributionSiteCode}
        </if>
        <if test="buyerRemark !=null and buyerRemark != ''">
            and t.`buyer_remark` =#{buyerRemark}
        </if>
        <if test="isBack !=null">
            and t.`is_back` =#{isBack}
        </if>
        <if test="driverQueueCode !=null and driverQueueCode != ''">
            and t.`driver_queue_code` =#{driverQueueCode}
        </if>
        <if test="orderTime !=null">
            and t.`order_time` =#{orderTime}
        </if>
        <if test="invoiceUnitCode !=null and invoiceUnitCode != ''">
            and t.`invoice_unit_code` =#{invoiceUnitCode}
        </if>
        <if test="jpOrderNo !=null and jpOrderNo != ''">
            and t.`jp_order_no` =#{jpOrderNo}
        </if>
        <if test="orderRpFlag !=null and orderRpFlag != ''">
            and t.`order_rp_flag` =#{orderRpFlag}
        </if>
        <if test="equipmentName !=null and equipmentName != ''">
            and t.`equipment_name` =#{equipmentName}
        </if>
        <if test="shopId !=null and shopId != ''">
            and t.`shop_id` =#{shopId}
        </if>
        <if test="shopGuideTel !=null and shopGuideTel != ''">
            and t.`shop_guide_tel` =#{shopGuideTel}
        </if>
        <if test="expectInstallEndTime !=null">
            and t.`expect_install_end_time` =#{expectInstallEndTime}
        </if>
        <if test="mileage !=null">
            and t.`mileage` =#{mileage}
        </if>
        <if test="companyCode !=null and companyCode != ''">
            and t.`company_code` =#{companyCode}
        </if>
        <if test="orderWhName !=null and orderWhName != ''">
            and t.`order_wh_name` =#{orderWhName}
        </if>
        <if test="invoiceFlag !=null and invoiceFlag != ''">
            and t.`invoice_flag` =#{invoiceFlag}
        </if>
        <if test="appointmentType !=null and appointmentType != ''">
            and t.`appointment_type` =#{appointmentType}
        </if>
        <if test="orderNo !=null and orderNo != ''">
            and t.`order_no` =#{orderNo}
        </if>
        <if test="customerGroup !=null and customerGroup != ''">
            and t.`customer_group` =#{customerGroup}
        </if>
        <if test="cnDispatch !=null">
            and t.`cn_dispatch` =#{cnDispatch}
        </if>
        <if test="collectionFlag !=null and collectionFlag != ''">
            and t.`collection_flag` =#{collectionFlag}
        </if>
        <if test="printPriceFlag !=null">
            and t.`print_price_flag` =#{printPriceFlag}
        </if>
        <if test="shopGuideName !=null and shopGuideName != ''">
            and t.`shop_guide_name` =#{shopGuideName}
        </if>
        <if test="orderSourcePlatform !=null and orderSourcePlatform != ''">
            and t.`order_source_platform` =#{orderSourcePlatform}
        </if>
        <if test="outsourceFlag !=null and outsourceFlag != ''">
            and t.`outsource_flag` =#{outsourceFlag}
        </if>
        <if test="serviceOrderNo !=null and serviceOrderNo != ''">
            and t.`service_order_no` =#{serviceOrderNo}
        </if>
        <if test="whCode !=null and whCode != ''">
            and t.`wh_code` =#{whCode}
        </if>
        <if test="setupType !=null and setupType != ''">
            and t.`setup_type` =#{setupType}
        </if>
        <if test="emergenceFlag !=null and emergenceFlag != ''">
            and t.`emergence_flag` =#{emergenceFlag}
        </if>
        <if test="upperAgingCode !=null and upperAgingCode != ''">
            and t.`upper_aging_code` =#{upperAgingCode}
        </if>
        <if test="appointOrderNo !=null and appointOrderNo != ''">
            and t.`appoint_order_no` =#{appointOrderNo}
        </if>
        <if test="transferFlag !=null">
            and t.`transfer_flag` =#{transferFlag}
        </if>
        <if test="orderStatus !=null">
            and t.`order_status` =#{orderStatus}
        </if>
        <if test="shopName !=null and shopName != ''">
            and t.`shop_name` =#{shopName}
        </if>
        <if test="remark !=null">
            and t.`remark` =#{remark}
        </if>
        <if test="tenantCode !=null and tenantCode != ''">
            <!--            and t.`tenant_code` =#{tenantCode}-->
        </if>
        <if test="pickFlag !=null and pickFlag != ''">
            and t.`pick_flag` =#{pickFlag}
        </if>
        <if test="distributionStationType !=null and distributionStationType != ''">
            and t.`distribution_station_type` =#{distributionStationType}
        </if>
        <if test="appointmentReason !=null and appointmentReason != ''">
            and t.`appointment_reason` =#{appointmentReason}
        </if>
        <if test="printNotaxPriceFlag !=null">
            and t.`print_notax_price_flag` =#{printNotaxPriceFlag}
        </if>
        <if test="consolidationOrderNo !=null and consolidationOrderNo != ''">
            and t.`consolidation_order_no` =#{consolidationOrderNo}
        </if>
        <if test="upperReceiverName !=null and upperReceiverName != ''">
            and t.`upper_receiver_name` =#{upperReceiverName}
        </if>
        <if test="lineCode !=null and lineCode != ''">
            and t.`line_code` =#{lineCode}
        </if>
        <if test="taskType !=null and taskType != ''">
            and t.`task_type` =#{taskType}
        </if>
        <if test="inOutType !=null and inOutType != ''">
            and t.`in_out_type` =#{inOutType}
        </if>
        <if test="pledgeType !=null and pledgeType != ''">
            and t.`pledge_type` =#{pledgeType}
        </if>
        <if test="logisticMode !=null and logisticMode != ''">
            and t.`logistic_mode` =#{logisticMode}
        </if>
        <if test="scPosFlag !=null">
            and t.`sc_pos_flag` =#{scPosFlag}
        </if>
        <if test="upperSenderCode !=null and upperSenderCode != ''">
            and t.`upper_sender_code` =#{upperSenderCode}
        </if>
        <if test="appointmentTime !=null">
            and t.`appointment_time` =#{appointmentTime}
        </if>
        <if test="whSystem !=null and whSystem != ''">
            and t.`wh_system` =#{whSystem}
        </if>
        <if test="distributionWhCode !=null and distributionWhCode != ''">
            and t.`distribution_wh_code` =#{distributionWhCode}
        </if>
        <if test="transportType !=null and transportType != ''">
            and t.`transport_type` =#{transportType}
        </if>
        <if test="withdrawalReason !=null and withdrawalReason != ''">
            and t.`withdrawal_reason` =#{withdrawalReason}
        </if>
        <if test="zoneName !=null and zoneName != ''">
            and t.`zone_name` =#{zoneName}
        </if>
        <if test="upperSourceCustomerCode !=null and upperSourceCustomerCode != ''">
            and t.`upper_source_customer_code` =#{upperSourceCustomerCode}
        </if>
        <if test="professionalCompany !=null and professionalCompany != ''">
            and t.`professional_company` =#{professionalCompany}
        </if>
        <if test="orderSource !=null and orderSource != ''">
            and t.`order_source` =#{orderSource}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and t.`site_code` =#{siteCode}
        </if>
        <if test="upperWhCode !=null and upperWhCode != ''">
            and t.`upper_wh_code` =#{upperWhCode}
        </if>
        <if test="deliveryType !=null and deliveryType != ''">
            and t.`delivery_type` =#{deliveryType}
        </if>
        <if test="entityId !=null">
            and t.`entity_id` =#{entityId}
        </if>
        <if test="distributionSiteName !=null and distributionSiteName != ''">
            and t.`distribution_site_name` =#{distributionSiteName}
        </if>
        <if test="holdFlag !=null">
            and t.`hold_flag` =#{holdFlag}
        </if>
        <if test="originOrderNo !=null and originOrderNo != ''">
            and t.`origin_order_no` =#{originOrderNo}
        </if>
        <if test="upperCustomerName !=null and upperCustomerName != ''">
            and t.`upper_customer_name` =#{upperCustomerName}
        </if>
        <if test="platformOrderNo !=null and platformOrderNo != ''">
            and t.`platform_order_no` =#{platformOrderNo}
        </if>
        <if test="joinType !=null and joinType != ''">
            and t.`join_type` =#{joinType}
        </if>
        <if test="consigneeTimeTo !=null">
            and t.`consignee_time_to` =#{consigneeTimeTo}
        </if>
        <if test="c2mType !=null and c2mType != ''">
            and t.`c2m_type` =#{c2mType}
        </if>
        <if test="businessType !=null and businessType != ''">
            and t.`business_type` =#{businessType}
        </if>
        <if test="tcFlag !=null and tcFlag != ''">
            and t.`tc_flag` =#{tcFlag}
        </if>
        <if test="nextDistributionSiteCode !=null and nextDistributionSiteCode != ''">
            and t.`next_distribution_site_code` =#{nextDistributionSiteCode}
        </if>
        <if test="waybillNo !=null and waybillNo != ''">
            and t.`waybill_no` =#{waybillNo}
        </if>
        <if test="orderStartTime !=null">
            and t.`order_start_time` =#{orderStartTime}
        </if>
        <if test="transportSystem !=null">
            and t.`transport_system` =#{transportSystem}
        </if>
        <if test="transportOrderNo !=null">
            and t.`transport_order_no` =#{transportOrderNo}
        </if>
        <if test="trainNumber !=null and trainNumber != ''">
            and t.`train_number` =#{trainNumber}
        </if>
    </sql>

    <select id="selectOneDetail" resultType="com.midea.logistics.otp.task.domain.bean.custom.TaskExt">
        select
        t.*,
        ta.`sender_country_code` AS senderCountryCode,
        ta.`sender_country_name` AS senderCountryName,
        ta.`sender_province_code` AS senderProvinceCode,
        ta.`sender_province_name` AS senderProvinceName,
        ta.`sender_city_code` AS senderCityCode,
        ta.`sender_city_name` AS senderCityName,
        ta.`sender_district_code` AS senderDistrictCode,
        ta.`sender_district_name` AS senderDistrictName,
        ta.`sender_town_code` AS senderTownCode,
        ta.`sender_town_name` AS senderTownName,
        ta.`sender_detail_addr` AS senderDetailAddr,
        ta.`sender_name` AS senderName,
        ta.`sender_tel` AS senderTel,
        ta.`sender_mobile` AS  senderMobile,
        ta.`receiver_country_code` AS receiverCountryCode,
        ta.`receiver_country_name` AS receiverCountryName,
        ta.`receiver_province_code` AS receiverProvinceCode,
        ta.`receiver_province_name` AS receiverProvinceName,
        ta.`receiver_city_code` AS receiverCityCode,
        ta.`receiver_city_name` AS receiverCityName,
        ta.`receiver_district_code` AS receiverDistrictCode,
        ta.`receiver_district_name` AS receiverDistrictName,
        ta.`receiver_town_code` AS receiverTownCode,
        ta.`receiver_town_name` AS receiverTownName,
        ta.`receiver_detail_addr` AS receiverDetailAddr,
        ta.`receiver_name` AS receiverName,
        ta.`receiver_tel` AS  receiverTel,
        ta.`receiver_mobile` AS receiverMobile,
        ta.`final_country_code` AS  finalCountryCode,
        ta.`final_country_name` AS  finalCountryName,
        ta.`final_province_code` AS  finalProvinceCode,
        ta.`final_province_name` AS  finalProvinceName,
        ta.`final_city_code` AS  finalCityCode,
        ta.`final_city_name` AS  finalCityName,
        ta.`final_district_code` AS  finalDistrictCode,
        ta.`final_district_name` AS  finalDistrictName,
        ta.`final_town_code` AS  finalTownCode,
        ta.`final_town_name` AS  finalTownName,
        ta.`final_detail_addr` AS  finalDetailAddr,
        ta.`final_name` AS  finalName,
        ta.`final_tel` AS  finalTel,
        ta.`final_mobile` AS  finalMobile
        from task t
        left join task_address ta on t.task_no = ta.task_no  and ta.delete_flag = 0
        <include refid="whereFieldsSqlPre"/>
        limit 1
    </select>

    <select id="queryByTaskNos" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        from logistics_otp_order.task t
        where
          t.delete_flag = '0'
          and t.task_no in
        <foreach collection="list" item="taskNo" index="index" open="(" close=")" separator=",">
            #{taskNo}
        </foreach>
    </select>

    <update id="deleteBatchById" parameterType="list">
        update
        task t
        set `delete_flag`=1
        where
        `id` in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectTaskDetail" resultType="com.midea.logistics.otp.task.domain.bean.custom.TaskExt">
        select
        t.*,
        ta.`sender_country_code` AS senderCountryCode,
        ta.`sender_country_name` AS senderCountryName,
        ta.`sender_province_code` AS senderProvinceCode,
        ta.`sender_province_name` AS senderProvinceName,
        ta.`sender_city_code` AS senderCityCode,
        ta.`sender_city_name` AS senderCityName,
        ta.`sender_district_code` AS senderDistrictCode,
        ta.`sender_district_name` AS senderDistrictName,
        ta.`sender_town_code` AS senderTownCode,
        ta.`sender_town_name` AS senderTownName,
        ta.`sender_detail_addr` AS senderDetailAddr,
        ta.`sender_name` AS senderName,
        ta.`sender_tel` AS senderTel,
        ta.`sender_mobile` AS  senderMobile,
        ta.`receiver_country_code` AS receiverCountryCode,
        ta.`receiver_country_name` AS receiverCountryName,
        ta.`receiver_province_code` AS receiverProvinceCode,
        ta.`receiver_province_name` AS receiverProvinceName,
        ta.`receiver_city_code` AS receiverCityCode,
        ta.`receiver_city_name` AS receiverCityName,
        ta.`receiver_district_code` AS receiverDistrictCode,
        ta.`receiver_district_name` AS receiverDistrictName,
        ta.`receiver_town_code` AS receiverTownCode,
        ta.`receiver_town_name` AS receiverTownName,
        ta.`receiver_detail_addr` AS receiverDetailAddr,
        ta.`receiver_name` AS receiverName,
        ta.`receiver_tel` AS  receiverTel,
        ta.`receiver_mobile` AS receiverMobile,
        ta.`final_country_code` AS  finalCountryCode,
        ta.`final_country_name` AS  finalCountryName,
        ta.`final_province_code` AS  finalProvinceCode,
        ta.`final_province_name` AS  finalProvinceName,
        ta.`final_city_code` AS  finalCityCode,
        ta.`final_city_name` AS  finalCityName,
        ta.`final_district_code` AS  finalDistrictCode,
        ta.`final_district_name` AS  finalDistrictName,
        ta.`final_town_code` AS  finalTownCode,
        ta.`final_town_name` AS  finalTownName,
        ta.`final_detail_addr` AS  finalDetailAddr,
        ta.`final_name` AS  finalName,
        ta.`final_tel` AS  finalTel,
        ta.`final_mobile` AS  finalMobile
        from task t
        left join task_address ta on t.task_no = ta.task_no  and ta.delete_flag = 0
        <include refid="whereFieldsSqlPre"/>
        limit ${start},${pageSize}
    </select>

    <select id="selectAllTaskIncludeDel" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        from task t
        where t.`order_no` =#{orderNo} and t.`distribution_num` =#{distributionNum}
        limit 1000
    </select>

    <select id="selectTaskByTaskNoIncludeDel" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        from task t
        where t.`task_no` =#{taskNo}
        limit 1
    </select>


    <update id="deleteChangeWarehouseTask">
        update
        task t
        set delete_flag = 1 ,version = version + 1 ,task_type = #{taskType}
        where
        id = #{id}
    </update>

    <update id="deleteTaskOfTI">
        update
        task t
        set delete_flag = 1 ,version = version + 1
        where parent_order_no = #{parentOrderNo} and order_no = #{orderNo} and task_type = 'IN'

    </update>

    <update id="clearHoldFlag">
        update
        task t
        set hold_flag = null
        where
            id = #{id}
    </update>

    <update id="updateTaskRelationNo">
        update
        task t
        set relation_order_no = #{relationOrderNo}
        where
            task_no = #{taskNo}
    </update>

    <update id="clearHoldFlagAndSetInvoice">
        update
        task t
        <set>
            t.`hold_flag` = null,
            t.`invoice_unit_code` =#{invoiceUnitCode},
            t.`invoice_unit_name` =#{invoiceUnitName},
            <if test="contractNo !=null and contractNo != ''">
                t.`contract_no` = #{contractNo},
            </if>
        </set>
        where
            id = #{id}
    </update>

    <update id="setInvoiceInfo">
        update
        task t
        <set>
            t.`invoice_unit_code` =#{invoiceUnitCode},
            t.`invoice_unit_name` =#{invoiceUnitName},
            <if test="contractNo !=null and contractNo != ''">
                t.`contract_no` = #{contractNo},
            </if>
            <if test="holdFlag !=null and holdFlag != ''">
                t.`hold_flag` = #{holdFlag},
            </if>
        </set>
        where
            id = #{id}
    </update>


    <select id="selectOneDetailWithOrderBy" resultType="com.midea.logistics.otp.task.domain.bean.custom.TaskExt">
        select
        t.*,
        ta.`sender_country_code` AS senderCountryCode,
        ta.`sender_country_name` AS senderCountryName,
        ta.`sender_province_code` AS senderProvinceCode,
        ta.`sender_province_name` AS senderProvinceName,
        ta.`sender_city_code` AS senderCityCode,
        ta.`sender_city_name` AS senderCityName,
        ta.`sender_district_code` AS senderDistrictCode,
        ta.`sender_district_name` AS senderDistrictName,
        ta.`sender_town_code` AS senderTownCode,
        ta.`sender_town_name` AS senderTownName,
        ta.`sender_detail_addr` AS senderDetailAddr,
        ta.`sender_name` AS senderName,
        ta.`sender_tel` AS senderTel,
        ta.`sender_mobile` AS  senderMobile,
        ta.`receiver_country_code` AS receiverCountryCode,
        ta.`receiver_country_name` AS receiverCountryName,
        ta.`receiver_province_code` AS receiverProvinceCode,
        ta.`receiver_province_name` AS receiverProvinceName,
        ta.`receiver_city_code` AS receiverCityCode,
        ta.`receiver_city_name` AS receiverCityName,
        ta.`receiver_district_code` AS receiverDistrictCode,
        ta.`receiver_district_name` AS receiverDistrictName,
        ta.`receiver_town_code` AS receiverTownCode,
        ta.`receiver_town_name` AS receiverTownName,
        ta.`receiver_detail_addr` AS receiverDetailAddr,
        ta.`receiver_name` AS receiverName,
        ta.`receiver_tel` AS  receiverTel,
        ta.`receiver_mobile` AS receiverMobile,
        ta.`final_country_code` AS  finalCountryCode,
        ta.`final_country_name` AS  finalCountryName,
        ta.`final_province_code` AS  finalProvinceCode,
        ta.`final_province_name` AS  finalProvinceName,
        ta.`final_city_code` AS  finalCityCode,
        ta.`final_city_name` AS  finalCityName,
        ta.`final_district_code` AS  finalDistrictCode,
        ta.`final_district_name` AS  finalDistrictName,
        ta.`final_town_code` AS  finalTownCode,
        ta.`final_town_name` AS  finalTownName,
        ta.`final_detail_addr` AS  finalDetailAddr,
        ta.`final_name` AS  finalName,
        ta.`final_tel` AS  finalTel,
        ta.`final_mobile` AS  finalMobile
        from task t
        left join task_address ta on t.task_no = ta.task_no  and ta.delete_flag = 0
        <include refid="whereFieldsSqlPre"/>
        <if test="orderBy !=null">
            order by  ${orderBy}
            <if test="orderByType !=null">
                ${orderByType}
            </if>
        </if>
        limit 1
    </select>

    <select id="getCancelOrderNo" resultType = "java.lang.Integer">
        select
          IFNULL(SUBSTRING_INDEX(`order_no`, '#', -1)+0, 0)
        from
          task
        where
          order_no like CONCAT(#{orderNo},'#%')
        order by SUBSTRING_INDEX(order_no, '#', -1)+0 desc limit 1
    </select>

    <update id="batchUpdateByTaskNo">
        <foreach collection="list" item="item" separator=";">
            update
            task t
            <include refid="com.midea.logistics.otp.task.mapper.common.TaskMapper.setBatchFieldsSql"/>
            where
            `task_no` =#{item.taskNo}
            <if test="item.version !=null and item.version != 0">
                and `version` = #{item.version}
            </if>
        </foreach>
    </update>

    <select id="queryByOrderNos" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSql"/>
        from logistics_otp_order.task t
        where
        t.delete_flag = 0
        and t.order_no in
        <foreach collection="list" item="orderNo" index="index" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>

    <update id="batchClearHoldFlagByTaskNo">
        update
        task t
        set hold_flag = null
        where
        `task_no` in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </update>

    <update id="batchClearHoldFlagByCompleteSetNos">
        UPDATE task t
        JOIN set_delivery_order_info s ON t.parent_order_no = s.order_no
        SET t.hold_flag = NULL
        WHERE t.delete_flag = 0
        AND s.delete_flag = 0
        AND s.hold_flag = 18
        AND s.complete_set_no IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="searchTaskListByDate" resultType="com.midea.logistics.otp.task.domain.bean.TaskZsjx">
        select
            t.id,
            t.task_no ,
            t.order_no,
            t.delivery_type,
            t.professional_company,
            t.parent_order_no,
            t.distribution_num,
            t.order_type,
            t.customer_code,
            t.wh_code,
            t.site_code,
            oe.professional_company as professionalCompanyOrderExtend,
            coe.order_distinction_flag
        from task t
        left join order_extend oe  on oe.order_no = t.order_no
        left join customer_order_info_extend coe on t.parent_order_no = coe.order_no
        where t.delete_flag=0
          and t.delivery_type != 'ZT'
          and t.professional_company != '2'
          and t.professional_company != '7'
            <![CDATA[
                and t.create_time >= #{startTime} and t.create_time <= #{endTime}
             ]]>

    </select>

    <select id="queryByTaskNosZs" resultType="com.midea.logistics.otp.task.domain.bean.TaskZsjx">
        select
        t.id,
        t.task_no ,
        t.order_no,
        t.delivery_type,
        t.professional_company,
        t.parent_order_no,
        t.distribution_num,
        t.order_type,
        t.customer_code,
        t.wh_code,
        t.site_code,
        oe.professional_company as professionalCompanyOrderExtend,
        coe.order_distinction_flag
        from task t
        left join order_extend oe  on oe.order_no = t.order_no
        left join customer_order_info_extend coe on t.parent_order_no = coe.order_no
        where t.delete_flag=0
        and t.delivery_type != 'ZT'
        and t.professional_company != '2'
        and t.professional_company != '7'
        and t.task_no in
        <foreach collection="list" item="taskNos" index="index" open="(" close=")" separator=",">
            #{taskNos}
        </foreach>
    </select>

    <select id="queryByTaskIds" resultType="com.midea.logistics.otp.task.domain.bean.TaskZsjx">
        select
        t.id,
        t.task_no ,
        t.order_no,
        t.delivery_type,
        t.professional_company,
        t.parent_order_no,
        t.distribution_num,
        t.order_type,
        t.customer_code,
        t.wh_code,
        t.site_code,
        oe.professional_company as professionalCompanyOrderExtend,
        coe.order_distinction_flag
        from task t
        left join order_extend oe  on oe.order_no = t.order_no
        left join customer_order_info_extend coe on t.parent_order_no = coe.order_no
        where t.delete_flag=0
            <![CDATA[
            and t.id >= #{startId} and t.id <= #{endId}
            ]]>
        and t.delivery_type != 'ZT'
        and t.professional_company != '2'
        and t.professional_company != '7'
    </select>

    <select id="handOverTimeSearchTasks" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        <include refid="searchFieldsSqlExt"/>
        from
        order_appointment_info oai
        left join task t on
        oai.order_no = t.order_no
        where
        t.delete_flag = 0
        and t.distribution_num = 1
        and (t.wh_system is null or t.wh_system = '')
        and t.hold_flag = 17
        and t.order_status not in (998,999)
        and <![CDATA[ oai.update_time >= date_sub(current_date(),interval 1 day) ]]>
        and oai.pre_appoint_way = 'HAND'
        and oai.pre_appoint_status = '6'
        and <![CDATA[ TIMESTAMPDIFF(hour,oai.update_time,now())>=1 ]]>
        limit #{rowNum}
    </select>

    <select id="holdAppointSearchTasks" resultType="com.midea.logistics.otp.task.domain.bean.Task">
        select
        t.*
        from task t left join order_extend oe on t.order_no = oe.order_no and oe.delete_flag = 0
        where
        t.delete_flag = 0
        and t.distribution_num = 1
        and (t.wh_system is null or t.wh_system = '')
        and t.hold_flag = 17
        and <![CDATA[ oe.order_end_time >= current_date() ]]>
        and <![CDATA[ oe.order_end_time < date_add(current_date(),interval 1 day) ]]>
        limit #{rowNum}
    </select>

    <sql id="searchFieldsSqlExt">
        t.`id` AS  id,
        t.`order_type` AS  orderType,
        t.`require_out_time` AS  requireOutTime,
        t.`consignee_time_from` AS  consigneeTimeFrom,
        t.`company_name` AS  companyName,
        t.`order_wh_code` AS  orderWhCode,
        t.`main_waybill_no` AS  mainWaybillNo,
        t.`total_gross_weight` AS  totalGrossWeight,
        t.`delivered_verify_flag` AS  deliveredVerifyFlag,
        t.`wh_name` AS  whName,
        t.`pay_type` AS  payType,
        t.`upper_customer_code` AS  upperCustomerCode,
        t.`targe_site_code` AS  targeSiteCode,
        t.`if_up_stairs` AS  ifUpStairs,
        t.`install_flag` AS  installFlag,
        t.`target_customer_code` AS  targetCustomerCode,
        t.`task_status` AS  taskStatus,
        t.`qz_wareh_area_flag` AS  qzWarehAreaFlag,
        t.`gray_flag` AS  grayFlag,
        t.`aging_product_code` AS  agingProductCode,
        t.`receiver_sale_stype` AS  receiverSaleStype,
        t.`next_distribution_wh_code` AS  nextDistributionWhCode,
        t.`order_confirm_time` AS  orderConfirmTime,
        t.`order_site_name` AS  orderSiteName,
        t.`total_pkg_qty` AS  totalPkgQty,
        t.`relation_order_no` AS  relationOrderNo,
        t.`invoice_unit_name` AS  invoiceUnitName,
        t.`version` AS  version,
        t.`expect_arrive_start_time` AS  expectArriveStartTime,
        t.`targe_wh_code` AS  targeWhCode,
        t.`line_aging` AS  lineAging,
        t.`customer_name` AS  customerName,
        t.`excute_status` AS  excuteStatus,
        t.`upper_receiver_code` AS  upperReceiverCode,
        t.`line_cost` AS  lineCost,
        t.`cloud_wh_flag` AS  cloudWhFlag,
        t.`upper_reference_id` AS  upperReferenceId,
        t.`freight_basis` AS  freightBasis,
        t.`carrier_code` AS  carrierCode,
        t.`deliverypay_type` AS  deliverypayType,
        t.`total_net_weight` AS  totalNetWeight,
        t.`zone_code` AS  zoneCode,
        t.`plan_order_flag` AS  planOrderFlag,
        t.`distribution_wh_name` AS  distributionWhName,
        t.`upstream_doc_type` AS  upstreamDocType,
        t.`load_type` AS  loadType,
        t.`unit_area_name` AS  unitAreaName,
        t.`source_system` AS  sourceSystem,
        t.`distribution_flag` AS  distributionFlag,
        t.`contract_no` AS  contractNo,
        t.`customer_code` AS  customerCode,
        t.`site_name` AS  siteName,
        t.`line_name` AS  lineName,
        t.`supplier_code` AS  supplierCode,
        t.`order_value` AS  orderValue,
        t.`equipment_type` AS  equipmentType,
        t.`line_mileage` AS  lineMileage,
        t.`spec_carrier_flag` AS  specCarrierFlag,
        t.`upper_order_type` AS  upperOrderType,
        t.`return_reason` AS  returnReason,
        t.`task_no` AS  taskNo,
        t.`project_classify` AS  projectClassify,
        t.`order_end_time` AS  orderEndTime,
        t.`cancel_reason` AS  cancelReason,
        t.`specimen_type` AS  specimenType,
        t.`in_wh_time` AS  inWhTime,
        t.`work_order_no` AS  workOrderNo,
        t.`oaid` AS  oaid,
        t.`business_mode` AS  businessMode,
        t.`dispatch_no` AS  dispatchNo,
        t.`expect_install_start_time` AS  expectInstallStartTime,
        t.`collection_amount` AS  collectionAmount,
        t.`third_flag` AS  thirdFlag,
        t.`print_barcode` AS  printBarcode,
        t.`create_user_code` AS  createUserCode,
        t.`expect_install_type` AS  expectInstallType,
        t.`arrive_appointment_require_flag` AS  arriveAppointmentRequireFlag,
        t.`update_time` AS  updateTime,
        t.`customer_total_qty` AS  customerTotalQty,
        t.`expect_arrive_end_time` AS  expectArriveEndTime,
        t.`operations_center_code` AS  operationsCenterCode,
        t.`create_time` AS  createTime,
        t.`upper_wh_name` AS  upperWhName,
        t.`upper_sender_name` AS  upperSenderName,
        t.`delivery_cycle` AS  deliveryCycle,
        t.`business_category` AS  businessCategory,
        t.`distribution_last_flag` AS  distributionLastFlag,
        t.`customer_order_no` AS  customerOrderNo,
        t.`customer_aging_code` AS  customerAgingCode,
        t.`pay_date` AS  payDate,
        t.`total_volume` AS  totalVolume,
        t.`service_type` AS  serviceType,
        t.`total_qty` AS  totalQty,
        t.`pay_time` AS  payTime,
        t.`station_type` AS  stationType,
        t.`order_site_code` AS  orderSiteCode,
        t.`distribution_num` AS  distributionNum,
        t.`consolidation_num` AS  consolidationNum,
        t.`parent_order_no` AS  parentOrderNo,
        t.`distribution_site_code` AS  distributionSiteCode,
        t.`delete_flag` AS  deleteFlag,
        t.`buyer_remark` AS  buyerRemark,
        t.`is_back` AS  isBack,
        t.`driver_queue_code` AS  driverQueueCode,
        t.`order_time` AS  orderTime,
        t.`invoice_unit_code` AS  invoiceUnitCode,
        t.`jp_order_no` AS  jpOrderNo,
        t.`order_rp_flag` AS  orderRpFlag,
        t.`equipment_name` AS  equipmentName,
        t.`shop_id` AS  shopId,
        t.`shop_guide_tel` AS  shopGuideTel,
        t.`expect_install_end_time` AS  expectInstallEndTime,
        t.`mileage` AS  mileage,
        t.`company_code` AS  companyCode,
        t.`order_wh_name` AS  orderWhName,
        t.`invoice_flag` AS  invoiceFlag,
        t.`appointment_type` AS  appointmentType,
        t.`order_no` AS  orderNo,
        t.`customer_group` AS  customerGroup,
        t.`cn_dispatch` AS  cnDispatch,
        t.`collection_flag` AS  collectionFlag,
        t.`print_price_flag` AS  printPriceFlag,
        t.`shop_guide_name` AS  shopGuideName,
        t.`order_source_platform` AS  orderSourcePlatform,
        t.`outsource_flag` AS  outsourceFlag,
        t.`service_order_no` AS  serviceOrderNo,
        t.`update_user_code` AS  updateUserCode,
        t.`wh_code` AS  whCode,
        t.`setup_type` AS  setupType,
        t.`emergence_flag` AS  emergenceFlag,
        t.`upper_aging_code` AS  upperAgingCode,
        t.`appoint_order_no` AS  appointOrderNo,
        t.`transfer_flag` AS  transferFlag,
        t.`order_status` AS  orderStatus,
        t.`shop_name` AS  shopName,
        t.`remark` AS  remark,
        t.`tenant_code` AS  tenantCode,
        t.`pick_flag` AS  pickFlag,
        t.`distribution_station_type` AS  distributionStationType,
        t.`appointment_reason` AS  appointmentReason,
        t.`print_notax_price_flag` AS  printNotaxPriceFlag,
        t.`consolidation_order_no` AS  consolidationOrderNo,
        t.`upper_receiver_name` AS  upperReceiverName,
        t.`line_code` AS  lineCode,
        t.`task_type` AS  taskType,
        t.`in_out_type` AS  inOutType,
        t.`pledge_type` AS  pledgeType,
        t.`logistic_mode` AS  logisticMode,
        t.`sc_pos_flag` AS  scPosFlag,
        t.`upper_sender_code` AS  upperSenderCode,
        t.`appointment_time` AS  appointmentTime,
        t.`wh_system` AS  whSystem,
        t.`distribution_wh_code` AS  distributionWhCode,
        t.`transport_type` AS  transportType,
        t.`withdrawal_reason` AS  withdrawalReason,
        t.`zone_name` AS  zoneName,
        t.`upper_source_customer_code` AS  upperSourceCustomerCode,
        t.`professional_company` AS  professionalCompany,
        t.`order_source` AS  orderSource,
        t.`site_code` AS  siteCode,
        t.`upper_wh_code` AS  upperWhCode,
        t.`delivery_type` AS  deliveryType,
        t.`entity_id` AS  entityId,
        t.`distribution_site_name` AS  distributionSiteName,
        t.`hold_flag` AS  holdFlag,
        t.`origin_order_no` AS  originOrderNo,
        t.`upper_customer_name` AS  upperCustomerName,
        t.`platform_order_no` AS  platformOrderNo,
        t.`join_type` AS  joinType,
        t.`consignee_time_to` AS  consigneeTimeTo,
        t.`c2m_type` AS  c2mType,
        t.`business_type` AS  businessType,
        t.`tc_flag` AS  tcFlag,
        t.`next_distribution_site_code` AS  nextDistributionSiteCode,
        t.`waybill_no` AS  waybillNo,
        t.`order_start_time` AS  orderStartTime,
        t.`transport_system` AS  transportSystem,
        t.`transport_order_no` AS  transportOrderNo,
        t.`train_number` AS  trainNumber
    </sql>
</mapper>