package com.midea.logistics.otplbs.manager;

import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * ClassName: ValueAddedManager
 * Description: 增值服务订单导入
 *
 * <AUTHOR>
 * @date 2024/5/21 9:35
 */
@Slf4j
@Component
public class ValueAddedManager {
    @Autowired
    private ReportFeign reportFeign;
    @Autowired
    private UpDownloadHelper upDownloadHelper;
    @Autowired
    private ISsoService iSsoService;

    public JsonResponse importVdOrder(@RequestParam("file") MultipartFile file, String importType) {
        String userCode = iSsoService.getUserCode();
        if (org.apache.commons.lang.StringUtils.isBlank(userCode)) {
            throw BusinessException.fail("用户未登录");
        }
        ImportFileRequest request = upDownloadHelper.uploadFileToOss(file);
        request.setImportType(importType);
        JsonResponse jsonResponse = reportFeign.importVdOrder(request);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            throw BusinessException.fail(jsonResponse.getMsg());
        }
        return jsonResponse;
    }
}
