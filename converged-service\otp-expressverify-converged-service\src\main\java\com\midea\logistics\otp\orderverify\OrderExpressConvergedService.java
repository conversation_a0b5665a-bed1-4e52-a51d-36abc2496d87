package com.midea.logistics.otp.orderverify;

import com.mideaframework.core.config.properties.AppProperties;
import com.mideaframework.transactionservice.mq.MqCilentsScan;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

/**
 * 运配聚合微服务
 */
@EnableEurekaClient
@EnableEncryptableProperties
// @EnableAsync
@MqCilentsScan(basePackages = "com.midea.logistics.otp.orderverify.mq")
@EnableFeignClients(value = {
        "com.midea.logistics.otp.order.common.fegin",
        "com.midea.logistics.otp.common.feign",
        "com.midea.logistics.logisticsbopsdk",
        "com.midea.logistics.cache",
})
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@ComponentScan(basePackages = {"com.midea.logistics.*", "com.mideaframework.*"})
@EnableScheduling
public class OrderExpressConvergedService {
    public static void main(String[] args) {

        ConfigurableApplicationContext run = SpringApplication.run(OrderExpressConvergedService.class, args);

        AppProperties.setEnv(run);
        System.setProperty("SEATA_CONFIG_ENV", AppProperties.CURRENT_ENV.name().toLowerCase());

    }

    @Bean
    public RestTemplate restTemplate(){
        return new RestTemplate();
    }
}
