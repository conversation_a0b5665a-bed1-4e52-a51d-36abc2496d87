package com.midea.logistics.otp.order.common.enums;

/** 
* @description: 变更申请日志 
* @author: 陈永培
* @createtime: 2023/3/4 10:27 
*/ 
public enum ChangeApplyLogType {
    
    CHANGE_APPLY_HANDLE("CHANGE_APPLY_HANDLE","单据处理"),
    MIP_APPROVE("MIP_APPROVE","MIP审批"),
    DATA_CHANGE("DATA_CHANGE","数据变更"),
    
    ;
    ChangeApplyLogType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private String type;
    private  String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getName(String key){
        String name = "";
        if (null == key){
            return  name;
        }
        ChangeApplyLogType[] values = ChangeApplyLogType.values();
        for (ChangeApplyLogType value : values) {
            if (value.getType().equals(key)){
                name = value.getDesc();
                break;
            }
        }
        return  name;
    }

}
