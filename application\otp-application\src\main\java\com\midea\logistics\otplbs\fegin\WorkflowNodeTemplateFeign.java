package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.WorkflowNodeTemplate;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: WorkflowNodeTemplateFeign
 * Author: wangkc4
 * Date: 2019-6-10 10:34:04
 * Description:流程-节点模板 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface WorkflowNodeTemplateFeign {

    @RequestMapping(value = "/workflowNodeTemplate", method = RequestMethod.POST)
    JsonResponse create(@RequestBody WorkflowNodeTemplate workflowNodeTemplate);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workflowNodeTemplate/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workflowNodeTemplate/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody WorkflowNodeTemplate workflowNodeTemplate);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/workflowNodeTemplate/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/workflowNodeTemplates", method = RequestMethod.POST)
    JsonResponse search(@RequestBody WorkflowNodeTemplate workflowNodeTemplate);
}
