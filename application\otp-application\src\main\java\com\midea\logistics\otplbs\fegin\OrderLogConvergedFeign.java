package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.converged.domain.bean.InformationSecurityOperationVo;
import com.midea.logistics.otp.order.domain.bean.OrderLog;
import com.midea.logistics.otp.order.domain.bean.custom.OrderLogExt;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: OrderLogFeign
 * Author: lomo.deng
 * Date: 2019-5-17 14:57:37
 * Description:订单日志表 feign
 */
@FeignClient(value = "logistics-otp-order-converged-service")
public interface OrderLogConvergedFeign {

    /**
     * 根据安得单号(或任务号或运单号或客户订单号)查询该订单详情操作日志集合
     *
     * @param orderLog
     * @return
     */
    @RequestMapping(value = "/orderLogs", method = RequestMethod.POST)
    JsonResponse<List<OrderLogExt>> search(@RequestBody OrderLog orderLog);

    /**
     * 根据安得单号(或任务号或运单号或客户订单号)查询该订单详情操作日志集合
     *
     * @param request
     * @return JsonResponse<Object>

     */
    @GetMapping(value = "/saveInformationSecurityLog")
    JsonResponse<Object> saveInformationSecurityLog(@SpringQueryMap InformationSecurityOperationVo request);
}
