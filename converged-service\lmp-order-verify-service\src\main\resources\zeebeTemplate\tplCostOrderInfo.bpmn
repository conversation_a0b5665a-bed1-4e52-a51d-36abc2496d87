<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="cost-order-process-all" name="3pl纯计费订单流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= costOrderInfo" target="costOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1dw38pt</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0l105b2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-cost-agg-service/cost/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=costOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="costOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1dw38pt</bpmn:incoming>
      <bpmn:outgoing>Flow_1nzgxo4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=costOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="costOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-cost-agg-service/cost/address/mapping" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_16hqqhs</bpmn:incoming>
      <bpmn:outgoing>Flow_1wi0xbe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kptmf2" name="专司解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=costOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="costOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-cost-agg-service/cost/professional/company" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="projectType" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1s8vkj8</bpmn:incoming>
      <bpmn:outgoing>Flow_0l105b2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wi0xbe" sourceRef="Activity_1i377su" targetRef="Gateway_1cifedk" />
    <bpmn:serviceTask id="Activity_1iziya6" name="合同解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=costOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="costOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-cost-agg-service/cost/contract/verify" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0juhe6n</bpmn:incoming>
      <bpmn:incoming>Flow_09hw1z3</bpmn:incoming>
      <bpmn:outgoing>Flow_1s8vkj8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1dw38pt" sourceRef="StartEvent_1" targetRef="Activity_1k6ho42" />
    <bpmn:sequenceFlow id="Flow_1nzgxo4" sourceRef="Activity_1k6ho42" targetRef="Activity_1wa0f6l" />
    <bpmn:sequenceFlow id="Flow_0l105b2" sourceRef="Activity_0kptmf2" targetRef="Event_09zbrsb" />
    <bpmn:sequenceFlow id="Flow_1s8vkj8" sourceRef="Activity_1iziya6" targetRef="Activity_0kptmf2" />
    <bpmn:serviceTask id="Activity_1b9fto1" name="计费类型解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="busineesFee" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=costOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="costOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-cost-agg-service/cost/business/type" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1w6zy88</bpmn:incoming>
      <bpmn:outgoing>Flow_0juhe6n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0juhe6n" sourceRef="Activity_1b9fto1" targetRef="Activity_1iziya6" />
    <bpmn:exclusiveGateway id="Gateway_1cifedk" name="非自提" default="Flow_09hw1z3">
      <bpmn:incoming>Flow_0quikrt</bpmn:incoming>
      <bpmn:incoming>Flow_1wi0xbe</bpmn:incoming>
      <bpmn:outgoing>Flow_1w6zy88</bpmn:outgoing>
      <bpmn:outgoing>Flow_09hw1z3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1w6zy88" name="yes" sourceRef="Gateway_1cifedk" targetRef="Activity_1b9fto1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(costOrderInfo!=null and costOrderInfo.deliveryType!="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09hw1z3" name="no" sourceRef="Gateway_1cifedk" targetRef="Activity_1iziya6" />
    <bpmn:exclusiveGateway id="Gateway_1ap0ymd" name="是否地址解析" default="Flow_0quikrt">
      <bpmn:incoming>Flow_14lfo63</bpmn:incoming>
      <bpmn:outgoing>Flow_16hqqhs</bpmn:outgoing>
      <bpmn:outgoing>Flow_0quikrt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_16hqqhs" name="yes" sourceRef="Gateway_1ap0ymd" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(costOrderInfo!=null and costOrderInfo.deliveryType!="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0quikrt" name="no" sourceRef="Gateway_1ap0ymd" targetRef="Gateway_1cifedk" />
    <bpmn:sequenceFlow id="Flow_14lfo63" sourceRef="Activity_1wa0f6l" targetRef="Gateway_1ap0ymd" />
    <bpmn:serviceTask id="Activity_1wa0f6l" name="装卸计费解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="bmsFee" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-cost-agg-service/cost/loadingFeeAnalyze" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=costOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="costOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1nzgxo4</bpmn:incoming>
      <bpmn:outgoing>Flow_14lfo63</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="cost-order-process-all">
      <bpmndi:BPMNEdge id="Flow_14lfo63_di" bpmnElement="Flow_14lfo63">
        <di:waypoint x="520" y="120" />
        <di:waypoint x="575" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0quikrt_di" bpmnElement="Flow_0quikrt">
        <di:waypoint x="600" y="145" />
        <di:waypoint x="600" y="220" />
        <di:waypoint x="920" y="220" />
        <di:waypoint x="920" y="120" />
        <di:waypoint x="995" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="754" y="202" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16hqqhs_di" bpmnElement="Flow_16hqqhs">
        <di:waypoint x="625" y="120" />
        <di:waypoint x="690" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="649" y="102" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09hw1z3_di" bpmnElement="Flow_09hw1z3">
        <di:waypoint x="1045" y="120" />
        <di:waypoint x="1180" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1106" y="102" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w6zy88_di" bpmnElement="Flow_1w6zy88">
        <di:waypoint x="1020" y="145" />
        <di:waypoint x="1020" y="360" />
        <di:waypoint x="1180" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1027" y="254" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0juhe6n_di" bpmnElement="Flow_0juhe6n">
        <di:waypoint x="1230" y="320" />
        <di:waypoint x="1230" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s8vkj8_di" bpmnElement="Flow_1s8vkj8">
        <di:waypoint x="1280" y="120" />
        <di:waypoint x="1360" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l105b2_di" bpmnElement="Flow_0l105b2">
        <di:waypoint x="1460" y="120" />
        <di:waypoint x="1532" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nzgxo4_di" bpmnElement="Flow_1nzgxo4">
        <di:waypoint x="360" y="120" />
        <di:waypoint x="420" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dw38pt_di" bpmnElement="Flow_1dw38pt">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="260" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wi0xbe_di" bpmnElement="Flow_1wi0xbe">
        <di:waypoint x="790" y="120" />
        <di:waypoint x="995" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="143" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="1532" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1541" y="78" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="260" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="690" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07zfya5_di" bpmnElement="Activity_0kptmf2">
        <dc:Bounds x="1360" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bhkf7k_di" bpmnElement="Activity_1iziya6">
        <dc:Bounds x="1180" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1b9fto1_di" bpmnElement="Activity_1b9fto1">
        <dc:Bounds x="1180" y="320" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cifedk_di" bpmnElement="Gateway_1cifedk" isMarkerVisible="true">
        <dc:Bounds x="995" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1003.5" y="71" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ap0ymd_di" bpmnElement="Gateway_1ap0ymd" isMarkerVisible="true">
        <dc:Bounds x="575" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="609" y="93" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0gdzvav_di" bpmnElement="Activity_1wa0f6l">
        <dc:Bounds x="420" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
