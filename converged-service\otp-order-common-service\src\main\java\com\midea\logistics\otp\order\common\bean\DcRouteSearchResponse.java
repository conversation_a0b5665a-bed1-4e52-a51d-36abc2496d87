package com.midea.logistics.otp.order.common.bean;

import lombok.Data;

import java.util.Date;

/**
 * DC路由返回
 */
@Data
public class DcRouteSearchResponse {

    /**
     * 网点联系方式
     */
    private String linkPhone;

    /**
     * 网点所在乡镇
     */
    private String linkTown;

    /**
     * 始发地
     */
    private String originPlace;

    /**
     * 始发地镇编码
     */
    private String originTownCode;

    /**
     * 网点名称
     */
    private String siteName;

    /**
     * 网点所在区县编码
     */
    private String linkDistrictCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 网点办公地址
     */
    private String linkOfficeAddr;

    /**
     * 始发地区编码
     */
    private String originDistrictCode;


    /**
     * 始发地-镇
     */
    private String originPlaceTown;

    /**
     * 网点联系人
     */
    private String linkName;

    /**
     * 状态（0可用，1不可用）
     */
    private Integer deleteFlag;

    /**
     * 网点覆盖区域
     */
    private String siteCoverageArea;

    /**
     * 网点所在市编码
     */
    private String linkCityCode;

    /**
     * 网点所在区县
     */
    private String linkDistrict;

    /**
     * 覆盖区域名称
     */
    private String areaName;

    /**
     * 目的地
     */
    private String destinationPlace;

    /**
     * 分拨仓仓库编码
     */
    private String distributionWhCode;

    /**
     * 主键
     */
    private Long id;

    /**
     * 启停状态（启用0，停用1）
     */
    private Integer enableFlag;

    /**
     * 网点编码
     */
    private String siteCode;

    /**
     * 网点所在市
     */
    private String linkCity;

    /**
     * 网点所在乡镇编码
     */
    private String linkTownCode;

    /**
     * 路由编码
     */
    private String rcCode;

    /**
     * 创建人
     */
    private String createUserCode;

    /**
     * 网点所在省编码
     */
    private String linkProvinceCode;

    /**
     * 网点地址
     */
    private String siteAddress;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 揽收仓
     */
    private String collectWarehouse;

    /**
     * 始发地-区
     */
    private String originPlaceDistrict;

    /**
     * 网点所在省
     */
    private String linkProvince;

    /**
     * 数据版本
     */
    private Integer version;

    /**
     * 始发地省编码
     */
    private String originProvinceCode;

    /**
     * 分拨仓
     */
    private String distributionWarehouse;

    /**
     * 修改人
     */
    private String updateUserCode;

    /**
     * 覆盖区域编码
     */
    private String areaCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 揽收仓仓库编码
     */
    private String collectWhCode;

    /**
     * 始发地编码
     */
    private String originPlaceCode;

    /**
     * 始发地-市
     */
    private String originPlaceCity;

    /**
     * 始发地市编码
     */
    private String originCityCode;

    /**
     * 始发地-省
     */
    private String originPlaceProvince;

    /**
     * 分拨仓所在城市
     */
    private String distributionCityName;


}
