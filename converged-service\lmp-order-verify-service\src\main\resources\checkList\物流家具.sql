ALTER TABLE `logistics_otp_lastmile`.`net_task`   
  ADD COLUMN `total_pkg_qty` DECIMAL(18,8) NULL   COMMENT '包件总数';
  
  ALTER TABLE `logistics_otp_lastmile`.`net_task_item`   
  ADD COLUMN `pkg_qty` DECIMAL(18,8) NULL   COMMENT '包件数',
  ADD COLUMN `network_pkg_qty` DECIMAL(18,8) NULL   COMMENT '网点签收包件数',
  ADD COLUMN `install_flag` TINYINT NULL   COMMENT '是否安装标识',
  CHANGE `pkg_qty` `pkg_qty` DECIMAL(18,8) NULL   COMMENT '包件数',
  CHANGE `network_pkg_qty` `network_pkg_qty` DECIMAL(18,8) NULL   COMMENT '网点签收包件数';


-- 中间表
ALTER TABLE logistics_otp_order.mid_customer_order_item
ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是',
ADD COLUMN pkg_qty decimal(18,8) NULL COMMENT '包件数';

ALTER TABLE logistics_otp_order.mid_customer_order_info
 ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是',
 ADD COLUMN total_pkg_qty decimal(18,8) NULL COMMENT '总包件数';

-- 客户订单表
ALTER TABLE logistics_otp_order.customer_order_info
ADD COLUMN total_pkg_qty decimal(18,8) NULL COMMENT '总包件数',
ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是';

ALTER TABLE logistics_otp_order.customer_order_item
ADD COLUMN pkg_qty decimal(18,8) NULL COMMENT '包件数',
ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是';


-- 子单表
ALTER TABLE logistics_otp_order.order_info
ADD COLUMN total_pkg_qty decimal(18,8) NULL COMMENT '总包件数',
ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是';


ALTER TABLE logistics_otp_order.order_info_item
ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是',
ADD COLUMN pkg_qty decimal(18,8) NULL COMMENT '包件数';

-- 任务表
ALTER TABLE logistics_otp_order.task
ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是',
ADD COLUMN total_pkg_qty decimal(18,8) NULL COMMENT '总包件数';


ALTER TABLE logistics_otp_order.task_item
ADD COLUMN install_flag TINYINT(1) DEFAULT 0 NULL COMMENT '是否送装一体(安装):0否；1是',
ADD COLUMN pkg_qty decimal(18,8) NULL COMMENT '包件数';


-- T脚本
ALTER TABLE logistics_otp_dispatch.transport ADD total_pkg_qty decimal(18,8) DEFAULT 0 NOT NULL COMMENT '总包件数';

ALTER TABLE logistics_otp_dispatch.transport_detail ADD pkg_qty decimal(18,8) DEFAULT 0 NOT NULL COMMENT '包件数';

ALTER TABLE logistics_otp_dispatch.receipt ADD total_pkg_qty decimal(18,8) DEFAULT 0 NOT NULL COMMENT '总包件数';

ALTER TABLE logistics_otp_dispatch.receipt_detail ADD pkg_qty decimal(18,8) DEFAULT 0 NOT NULL COMMENT '包件数';



-- 城配扩展表
ALTER TABLE logistics_otp_order.customer_order_info_otp ADD total_pkg_qty decimal(18,8) NULL COMMENT '总包件数';
