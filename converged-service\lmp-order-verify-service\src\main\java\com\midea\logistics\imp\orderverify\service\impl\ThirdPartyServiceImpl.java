package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.ThirdPartyService;
import com.midea.logistics.otp.enums.CommonEnum;
import com.midea.logistics.otp.order.common.fegin.ThirdPartRuleFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.ThirdPartRule;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ThirdPartyServiceImpl implements ThirdPartyService {


    @Autowired
    private ThirdPartRuleFeign thirdPartRuleFeign;

    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    @Autowired
    private OrderFlowHelper orderFlowHelper;

    @Override
    @ZeebeFlow("THIRD_ANALYSIS")
    public OrderInfoExt setThirdParty(OrderInfoExt orderInfo) {

        String customerCode = orderInfo.getCustomerCode();
        String whCode = orderInfo.getWhCode();

        ThirdPartRule thirdPartRule = new ThirdPartRule();
        thirdPartRule.setCustomerCode(customerCode);
        thirdPartRule.setWhCode(whCode);
        //    根据条件查询第三方规则
        JsonResponse<ThirdPartRule> response = thirdPartRuleFeign.getThirdPartRule(thirdPartRule);
        if (response == null || !BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            throw BusinessException.fail("三方配置查询失败");
        }

        if (response.getData() != null) {
            ThirdPartRule data = response.getData();
            orderInfo.setThirdFlag(data.getThirdPart());
        } else {
            //  未查出规则默认N
            orderInfo.setThirdFlag(CommonEnum.NO.getValue());
        }
        lmpOrderFlowHelper.updateOrderInfo(orderInfo, "更新第三方解析");

        FlowListenerParam.success("第三方: " + CommonEnum.commonDesc(orderInfo.getThirdFlag()));
        return orderInfo;
    }
}
