package com.midea.logistics.imp.orderverify.service.impl;

import java.util.List;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.ObjectUtil;
import com.midea.logistics.cache.manager.ControlParamManager;
import com.midea.logistics.domain.mdm.domain.ControlParam;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.utils.ControlParamHelper;
import com.midea.logistics.cache.manager.ControlParamManager;
import com.midea.logistics.domain.mdm.domain.ControlParam;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.helper.OrderverifyHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.imp.orderverify.service.AnalysisBusineesFeeService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderTraceFeign;
import com.midea.logistics.otp.common.feign.servicefeign.rule.CustomerContractConfigFeign;
import com.midea.logistics.otp.order.common.fegin.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.rule.domain.bean.BusineesFeeConfig;
import com.midea.logistics.otp.rule.domain.request.BusineesFeeConfigRequest;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

import lombok.extern.slf4j.Slf4j;

/** 
* @description: 计费业务类型解析
* @author: 陈永培
* @createtime: 2020/11/18 22:56 
*/ 
@Service
@Slf4j
public class AnalysisBusineesFeeServiceImpl implements AnalysisBusineesFeeService {

    @Autowired
    TaskFeign taskFeign;
    @Autowired
    BusineesFeeConfigFeign busineesFeeConfigFeign;
    @Autowired
    OmsSuContractFegin omsSuContractFegin;
    @Autowired
    BopServiceFeign bopServiceFeign;
    @Autowired
    OrderFeign orderFeign;
    @Autowired
    OrderInfoItemFeign orderInfoItemFeign;
    @Autowired
    CustomerContractConfigFeign customerContractConfigFeign;
    @Autowired
    OrderTraceFeign orderTraceFeign;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private ControlParamManager controlParamManager;
    @Autowired
    private ControlParamHelper controlParamHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private BusinessHelper businessHelper;

    private final Cache<String, Boolean> controlCache = Caffeine.newBuilder().initialCapacity(1).expireAfterWrite(5, TimeUnit.MINUTES).build();

    @Override
    @ZeebeFlow("BUSNESS_TYPE")
    public OrderInfoExt analysisBusineesFeeConfig(OrderInfoExt orderInfo, Boolean updateFlag) {

        CommonConstant.checkOrderInfo(orderInfo);

        // （-1）即买即送判断逻辑
        if (orderInfo.getIsMrpJspsOrder() != null && orderInfo.getIsMrpJspsOrder() ) {
            return updateBusinessType(orderInfo, BusinessMode.isB2C(orderInfo.getBusinessMode()) ?  BusinessType.HOME_DELIVERY : BusinessType.DELIVERY , updateFlag);
        }

        // (-2) 送新取旧发快递的上门取件单
        if (businessHelper.isSxqjExpress(orderInfo)) {
            FlowListenerParam.success("不解析");
            return orderInfo;
        }

        String orderType = orderInfo.getOrderType();
        DeliveryType deliveryType = EnumUtils.getEnum(DeliveryType.class, orderInfo.getDeliveryType());

        //2021年9月7日10:26:31 泓铄：父单有，继承父单的
        CustomerOrderInfo customerOrderInfo = orderHelper.getCustomerOrderInfo(orderInfo.getParentOrderNo());
        String customerBusinessType = customerOrderInfo.getBusinessType();
        if (ToolUtils.isNotEmpty(customerBusinessType)) {
            log.info("analysisBusineesFeeConfig-> orderNo:{},继承父单业务模式：customerBusinessType:{}",orderInfo.getOrderNo(),customerBusinessType);
            return updateBusinessType(orderInfo, BusinessType.getBusinessType(customerBusinessType), updateFlag);
        }

        // （0）OFC父单快递配送方式默认快递
        if (SourceSystem.isOFC(customerOrderInfo.getSourceSystem()) && DeliveryType.isEXPRESS(customerOrderInfo.getDeliveryType()) && OrderType.isPOOrder(orderType)) {
            return updateBusinessType(orderInfo, BusinessType.EXPRESS, updateFlag);
        }

        //202504 泓铄 超区快递发货计费类型为快递
        if(SourceSystem.isOFC(orderInfo.getSourceSystem()) || SourceSystem.isMRP(orderInfo.getSourceSystem())) {
            CustomerOrderInfoExtend customerOrderInfoExtend = orderHelper.getCustomerOrderInfoExtend(orderInfo.getParentOrderNo());
            if (customerOrderInfoExtend != null && LoanScenarioCode.EXPRESS_DELIVERY_OVER_REGION.getKey().equals(customerOrderInfoExtend.getLoanScenarioCode())) {
                log.info("订单号[{}]属于超区快递发货，计费业务类型解析解析为快递", orderInfo.getOrderNo());
                return updateBusinessType(orderInfo, BusinessType.EXPRESS, updateFlag);
            }
        }

        //调拨返厂标识接入和按运输计费 https://cf.annto.com/pages/viewpage.action?pageId=78545085
        // 2025年8月4日 去除 remark， https://cf.annto.com/pages/viewpage.action?pageId=87584843
        if (SourceSystem.isOFC(customerOrderInfo.getSourceSystem())  && OrderType.isAOOrder(orderType)
            && ProjectClassifyEnum.isDBFC(customerOrderInfo.getProjectClassify())
            && CommonConstant.isDBFCEntityId(customerOrderInfo.getEntityId())
            && BusinessMode.isB2B(customerOrderInfo.getBusinessMode())
            && deliveryType != null
            && ! (DeliveryType.EXPRESS == deliveryType || DeliveryType.ZT == deliveryType || DeliveryType.EAD == deliveryType)) {
            //加个开关，防止..
            ControlParam controlParam = controlParamManager.getCache(CommonConstant.OFC_AO_BUSINESS_TYPE);
            if (controlParam != null && controlParam.getValue().equals(CommonConstant.STRING_FLAG_YES)) {
                log.info("analysisBusineesFeeConfig-> orderNo:{},调拨返厂订单业务类型,来源系统OFC+订单类型AO+符合条件转换业务模式解析为运输",orderInfo.getOrderNo());
                return updateBusinessType(orderInfo, BusinessType.TRANSPORT, updateFlag);
            }
        }
        // （1）针对来源 系统为MRP，紧急订单标识为是（emergence_flag=1）的订单，计费业务类型解析直接默认：B2C的模式，计费业务类型（businessType）默认宅配，B2B的模式，计费业务类型(businessType)默认配送
        if (SourceSystem.isMRP(orderInfo.getSourceSystem()) && ObjectUtil.equals(CommonConstant.FLAG_YES, orderInfo.getEmergenceFlag())) {
            BusinessType businessType = null;
            if (BusinessMode.isB2B(orderInfo.getBusinessMode())) {
                businessType = BusinessType.DELIVERY;
            } else {
                businessType = BusinessType.HOME_DELIVERY;
            }
            return updateBusinessType(orderInfo, businessType, true);
        }

        // （1）自提校验装卸合同
        if (DeliveryType.ZT == deliveryType) {
            return updateBusinessType(orderInfo, BusinessType.HANDLING, updateFlag);
        }

        // （2）业务模式为2B时，需根据客户、平台、订单类型解析计费业务类型：
        BusineesFeeConfigRequest busineesFeeConfigRequest = new BusineesFeeConfigRequest();
        busineesFeeConfigRequest.setBusinessMode(orderInfo.getBusinessMode());
        busineesFeeConfigRequest.setOrderType(orderInfo.getOrderType());
        busineesFeeConfigRequest.setSiteCode(orderInfo.getSiteCode());
        busineesFeeConfigRequest.setCustomerCode(orderInfo.getCustomerCode());

        // true 调用新模式， 否则切换旧逻辑
        if (orderverifyHelper.isControl(controlCache)) {
            /*
            替换旧的 匹配规则
            优先级1 五个维度都有值：业务模式、服务平台、客户、仓库、订单类型
            优先级2 四个维度有值：业务模式、服务平台、客户、仓库
            优先级3 四个维度有值：业务模式、服务平台、仓库、订单类型
            优先级4 四个维度有值：业务模式、服务平台、客户、订单类型
            优先级5 三个维度有值：业务模式、服务平台、仓库
            优先级6 三个维度有值：业务模式、服务平台、客户
            优先级6 三个维度有值：业务模式、服务平台、订单类型
            优先级7 两个维度有值：业务模式、服务平台
           */
            busineesFeeConfigRequest.setWhCode(orderInfo.getWhCode());
            BusineesFeeConfig config = orderverifyHelper.analysisConfigRule(busineesFeeConfigRequest);
            if (config != null) {
                return updateBusinessType(orderInfo, BusinessType.getBusinessType(config.getBusinessType()), updateFlag);
            }
        } else {
            JsonResponse<PageResponse<BusineesFeeConfig>> busineesFeeConfigPage = busineesFeeConfigFeign.search(busineesFeeConfigRequest);
            List<BusineesFeeConfig> busineesFeeConfigList = busineesFeeConfigPage.getData().getList();
            if (!CollectionUtils.isEmpty(busineesFeeConfigList)) {
                BusineesFeeConfig busineesFeeConfig = busineesFeeConfigList.get(0);
                String businessType = busineesFeeConfig.getBusinessType();
                return updateBusinessType(orderInfo, BusinessType.getBusinessType(businessType), updateFlag);
            }
            // （3）若根据客户、平台、订单类型查不到数据，则根据客户、平台查询配置，
            busineesFeeConfigRequest.setOrderType(null);
            JsonResponse<BusineesFeeConfig> busineesFeeConfigRes = busineesFeeConfigFeign.getByCustomerCodeAndSiteCode(busineesFeeConfigRequest);
            if (null != busineesFeeConfigRes.getData()) {
                BusineesFeeConfig busineesFeeConfig = busineesFeeConfigRes.getData();
                String businessType = busineesFeeConfig.getBusinessType();
                return updateBusinessType(orderInfo, BusinessType.getBusinessType(businessType), updateFlag);
            }
            //超哥:若根据客户、平台查不到数据，则根据平台查询配置
            busineesFeeConfigRequest.setCustomerCode(null);
            JsonResponse<BusineesFeeConfig> busineesFeeConfigR = busineesFeeConfigFeign.getByCustomerCodeAndSiteCode(busineesFeeConfigRequest);
            if (null != busineesFeeConfigR.getData()) {
                BusineesFeeConfig busineesFeeConfig = busineesFeeConfigR.getData();
                String businessType = busineesFeeConfig.getBusinessType();
                return updateBusinessType(orderInfo, BusinessType.getBusinessType(businessType), updateFlag);
            }
        }
        // 依然查不到，记录订单异常、订单操作日志、设置订单异常类型及其描述，卡单；反之根据结果设置订单计费业务类型。


        //根据业务模式、订单类型、配送方式、平台、客户解析计费业务类型（business_type）
        //（4）、配送方式为快递时，计费业务类型为快递；end.
        if (DeliveryType.EXPRESS == deliveryType) {
            return updateBusinessType(orderInfo, BusinessType.EXPRESS, updateFlag);
        }

        //（5）、配送方式是配送或网点配送则默认是配送
        if (DeliveryType.DELIVERY == deliveryType || DeliveryType.NET == deliveryType) {
            // 2020-7-30 09:54:27 ：黄进广 配送方式是配送或网点配送则默认是配送
            return updateBusinessType(orderInfo, BusinessType.DELIVERY, updateFlag);
        }

        //（6）、业务模式为2C时，直配、宅配、纯运输单的计费业务类型均为宅配费；end.
        if (BusinessMode.isB2C(orderInfo.getBusinessMode())) {

            if (DeliveryType.WAREHOUSEMATCHING == deliveryType || DeliveryType.DOT == deliveryType) {
                return updateBusinessType(orderInfo, BusinessType.HOME_DELIVERY, updateFlag);
            }

            //2020-7-30 09:47:31 黄进广 ：纯运输订单且是B2C，默认是宅配；（新增逻辑）
            if ( OrderType.YS.getKey().equals(orderType)) {
                return updateBusinessType(orderInfo, BusinessType.HOME_DELIVERY, updateFlag);
            }
        }

        // （7）、业务模式为2B时，纯运输单为配送
        if (BusinessMode.isB2B(orderInfo.getBusinessMode())) {

            //2020-7-30 09:47:59 黄进广 ：纯运输订单且是B2B，默认是配送；（新增逻辑）
            if ( OrderType.YS.getKey().equals(orderType)) {
                return updateBusinessType(orderInfo, BusinessType.DELIVERY, updateFlag);
            }
        }

        //（8）
        throw BusinessException.fail("请维护业务计费类型");
    }



    private OrderInfoExt updateBusinessType(OrderInfoExt orderInfo, BusinessType businessType, Boolean updateFlag) {
        if(null == businessType || StringUtils.isBlank(businessType.getKey())){
            throw BusinessException.fail("更新业务类型为空！请检查数据！");
        }
        OrderInfo newOrderInfo = new OrderInfo();
        newOrderInfo.setBusinessType(businessType.getKey());
        newOrderInfo.setId(orderInfo.getId());
        newOrderInfo.setVersion(0L);
        newOrderInfo.setOrderNo(orderInfo.getOrderNo());
        if(updateFlag) {
            orderHelper.updateOrderInfo(newOrderInfo, "更新业务类型");
        }
        orderInfo.setBusinessType(businessType.getKey());
        FlowListenerParam.success(businessType.getValue());

        return orderInfo;
    }

    private OrderInfoExt updateBusinessTypeByParent(OrderInfoExt orderInfo, BusinessType businessType) {
        if(null == businessType || StringUtils.isBlank(businessType.getKey())){
            throw BusinessException.fail("更新业务类型为空！请检查数据！");
        }
        OrderInfo newOrderInfo = new OrderInfo();
        newOrderInfo.setBusinessType(businessType.getKey());
        newOrderInfo.setId(orderInfo.getId());
        newOrderInfo.setVersion(0L);
        newOrderInfo.setOrderNo(orderInfo.getOrderNo());
        orderHelper.updateOrderInfo(newOrderInfo, "更新业务类型");
        orderInfo.setBusinessType(businessType.getKey());
        FlowListenerParam.success(businessType.getValue()+"（继承父单）");

        return orderInfo;
    }

}