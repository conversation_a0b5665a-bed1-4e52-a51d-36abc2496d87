package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.bean.custom.OrderConfirmExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderConfirmName;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


@FeignClient(value = "logistics-search-agg-service")
public interface OrderConfirmSearchFeign {
    @RequestMapping(value = "/orderConfirms", method = RequestMethod.GET)
    JsonResponse<PageResponse<OrderConfirmName>> search(@SpringQueryMap OrderConfirmExt orderConfirm);

    @RequestMapping(value = "/orderConfirmsCount", method = RequestMethod.GET)
    JsonResponse<Integer> orderConfirmsCount(@SpringQueryMap OrderConfirmExt orderConfirm);

}
