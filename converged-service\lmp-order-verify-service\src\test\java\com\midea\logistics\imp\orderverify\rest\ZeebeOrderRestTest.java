package com.midea.logistics.imp.orderverify.rest;

import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.junit.Assert.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class ZeebeOrderRestTest extends BaseRestTest {

    @Test
    public void automatic() throws Exception {




        String contend = "{\n" +
            "    \"id\" : 4828127790,\n" +
            "    \"createTime\" : \"2020-11-30 09:33:56\",\n" +
            "    \"updateTime\" : \"2020-11-30 09:33:56\",\n" +
            "    \"deleteFlag\" : 0,\n" +
            "    \"version\" : 0,\n" +
            "    \"createUserCode\" : \"system\",\n" +
            "    \"updateUserCode\" : \"system\",\n" +
            "    \"remark\" : \"苏宁经销采购订单号1020441677、1020441648、1020441649\",\n" +
            "    \"start\" : 0,\n" +
            "    \"pageSize\" : 30,\n" +
            "    \"orderBy\" : \"1\",\n" +
            "    \"orderByType\" : \"asc\",\n" +
            "    \"asc\" : \" asc\",\n" +
            "    \"dataMap\" : { },\n" +
            "    \"tenantCode\" : \"annto\",\n" +
            "    \"orderNo\" : \"IN22020113009331000035215\",\n" +
            "    \"customerOrderNo\" : \"Z202011271-5\",\n" +
            "    \"orderType\" : \"PO\",\n" +
            "    \"excuteStatus\" : 100,\n" +
            "    \"orderStatus\" : 100,\n" +
            "    \"companyCode\" : \"5021907252\",\n" +
            "    \"companyName\" : \"济南分公司\",\n" +
            "    \"customerCode\" : \"A0009158\",\n" +
            "    \"customerName\" : \"志高空调官方旗舰店\",\n" +
            "    \"siteCode\" : \"***********\",\n" +
            "    \"siteName\" : \"济南电商中心\",\n" +
            "    \"whCode\" : \"W201288\",\n" +
            "    \"whName\" : \"济南电商大宝仓\",\n" +
            "    \"sourceSystem\" : \"CIMS\",\n" +
            "    \"upperOrderTime\" : \"2020-11-14 18:35:00\",\n" +
            "    \"expectArriveStartTime\" : \"2020-11-27 23:59:59\",\n" +
            "    \"businessMode\" : \"B2B\",\n" +
            "    \"transportType\" : \"00\",\n" +
            "    \"agingProductCode\" : \"BZ01\",\n" +
            "    \"inOutType\" : \"OUT\",\n" +
            "    \"totalGrossWeight\" : 0,\n" +
            "    \"totalVolume\" : 0,\n" +
            "    \"entityId\" : 17,\n" +
            "    \"upperReceiverCode\" : \"C0016132\",\n" +
            "    \"upperSourceCustomerCode\" : \"C0016132\",\n" +
            "    \"upperWhCode\" : \"MD7C44F200\",\n" +
            "    \"upperWhName\" : \"烟机广州中心佛山正品-成品\",\n" +
            "    \"upperCustomerCode\" : \"43411\",\n" +
            "    \"upperOrderType\" : \"PO\",\n" +
            "    \"orderValue\" : 0.0,\n" +
            "    \"orderSource\" : \"API\",\n" +
            "    \"exceptionDesc\" : \"收发货平台不一致，不做货权转移\",\n" +
            "    \"upperReferenceId\" : \"20070363\",\n" +
            "    \"upperReceiverName\" : \"苏宁易购集团股份有限公司苏宁采购中心*广州\",\n" +
            "    \"upperSenderName\" : \"苏宁易购集团股份有限公司苏宁采购中心*广州\",\n" +
            "    \"workOrderNo\" : \"_\",\n" +
            "    \"orderTime\" : \"2020-11-30 09:33:56\",\n" +
            "    \"cnDispatch\" : 0,\n" +
            "    \"deliveredVerifyFlag\" : 0,\n" +
            "    \"customerOrderAddress\" : {\n" +
            "      \"id\" : 4663467372,\n" +
            "      \"createTime\" : \"2020-11-30 09:33:56\",\n" +
            "      \"updateTime\" : \"2020-11-30 09:33:56\",\n" +
            "      \"deleteFlag\" : 0,\n" +
            "      \"version\" : 2,\n" +
            "      \"createUserCode\" : \"system\",\n" +
            "      \"updateUserCode\" : \"system\",\n" +
            "      \"remark\" : \"苏宁经销采购订单号1020441677、1020441648、1020441649\",\n" +
            "      \"start\" : 0,\n" +
            "      \"pageSize\" : 30,\n" +
            "      \"orderBy\" : \"1\",\n" +
            "      \"orderByType\" : \"asc\",\n" +
            "      \"asc\" : \" asc\",\n" +
            "      \"dataMap\" : { },\n" +
            "      \"orderNo\" : \"IN22020113009331000035215\",\n" +
            "      \"receiverName\" : \"龙琪\",\n" +
            "      \"receiverMobile\" : \"13535513809\",\n" +
            "      \"receiverProvinceCode\" : \"144\",\n" +
            "      \"receiverProvinceName\" : \"广东省\",\n" +
            "      \"receiverCityCode\" : \"14406\",\n" +
            "      \"receiverCityName\" : \"佛山市\",\n" +
            "      \"receiverDistrictCode\" : \"1440607\",\n" +
            "      \"receiverDistrictName\" : \"三水区\",\n" +
            "      \"receiverTownCode\" : \"1440607103\",\n" +
            "      \"receiverTownName\" : \"乐平镇\",\n" +
            "      \"receiverDetailAddr\" : \"佛山市三水区乐平镇苏宁物流三水基地\",\n" +
            "      \"senderDistrictName\" : \"狮山镇\",\n" +
            "      \"senderTownCode\" : \"1440605124\",\n" +
            "      \"endLng\" : 113.02834639181492,\n" +
            "      \"endLat\" : 23.27249520783694,\n" +
            "      \"originAddr\" : \"佛山市三水区乐平镇苏宁物流三水基地\"\n" +
            "    },\n" +
            "    \"customerOrderItems\" : [ {\n" +
            "      \"id\" : 55682944642,\n" +
            "      \"createTime\" : \"2020-11-30 09:33:56\",\n" +
            "      \"updateTime\" : \"2020-11-30 09:33:56\",\n" +
            "      \"deleteFlag\" : 0,\n" +
            "      \"version\" : 0,\n" +
            "      \"createUserCode\" : \"system\",\n" +
            "      \"updateUserCode\" : \"system\",\n" +
            "      \"start\" : 0,\n" +
            "      \"pageSize\" : 30,\n" +
            "      \"orderBy\" : \"1\",\n" +
            "      \"orderByType\" : \"asc\",\n" +
            "      \"asc\" : \" asc\",\n" +
            "      \"dataMap\" : { },\n" +
            "      \"itemLineNo\" : 1,\n" +
            "      \"itemCode\" : \"0006445240\",\n" +
            "      \"customerItemCode\" : \"21073010004319\",\n" +
            "      \"itemSuiteCode\" : \"\",\n" +
            "      \"itemClass\" : \"M111.CRYJ.CRYJ0001\",\n" +
            "      \"itemStatus\" : \"Y\",\n" +
            "      \"upperItemStatus\" : \"MD7C44F200\",\n" +
            "      \"planQty\" : 2.0,\n" +
            "      \"volume\" : 0.003281,\n" +
            "      \"grossWeight\" : 28.0,\n" +
            "      \"netWeight\" : 24.5,\n" +
            "      \"price\" : 0.0,\n" +
            "      \"unit\" : \"\",\n" +
            "      \"customerOrderNo\" : \"Z202011271-5\",\n" +
            "      \"upperItemCode\" : \"21073010004319\",\n" +
            "      \"itemName\" : \"CXW-280-C62P 侧吸型烟机 （NFC+智能WiFi）触控 玻璃 中国\",\n" +
            "      \"materialGroup1\" : \"G000331\",\n" +
            "      \"materialGroup5\" : \"N000877\",\n" +
            "      \"upperItemStatusTo\" : \"\",\n" +
            "      \"setFlag\" : 0,\n" +
            "      \"upperLineNo\" : \"27882206\",\n" +
            "      \"orderNo\" : \"IN22020113009331000035215\",\n" +
            "      \"singleVolume\" : 0.331245,\n" +
            "      \"singleWeight\" : 28.0,\n" +
            "      \"totalQty\" : 2.0" +
            "    } ]\n" +
            "  }";
        mockMvc.perform(MockMvcRequestBuilders.post("/separateWarehouse/automatic").content(contend).contentType(MediaType.APPLICATION_JSON).header("tenantCode","annto"))
            .andExpect(status().isOk()).andDo(print()).andExpect(jsonPath("$.code").isString())
            .andExpect(jsonPath("$.code").value("0"));
    }
}