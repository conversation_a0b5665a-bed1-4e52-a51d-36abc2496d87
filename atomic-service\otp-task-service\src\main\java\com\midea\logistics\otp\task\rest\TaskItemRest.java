package com.midea.logistics.otp.task.rest;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.utils.SUtils;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.task.domain.bean.Task;
import com.midea.logistics.otp.task.domain.bean.TaskItem;
import com.midea.logistics.otp.task.domain.constant.TaskRouters;
import com.midea.logistics.otp.task.service.ITaskItemService;
import com.midea.logistics.otp.task.service.ITaskService;
import com.mideaframework.core.controller.BaseController;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import com.mideaframework.core.web.RestDoing;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: TaskItemController
 * Author: lindq2
 * Date: 2019-6-3 10:31:29
 * Description:任务表控制层
 */
@RestController
public class TaskItemRest extends BaseController<TaskItem> {

    private Logger logger = LoggerFactory.getLogger(TaskItemRest.class);

    @Autowired
    private ITaskItemService iTaskItemService;
    @Autowired
    private ITaskService iTaskService;

    /**
     * 新增
     *
     * @return
     */
    @RequestMapping(value = "/taskItem", method = RequestMethod.POST)
    public JsonResponse<Integer> create(@Valid @RequestBody TaskItem taskItem, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.saveTaskItem(taskItem);
        };
        return doing.go(request, logger);
    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskItem/{id}", method = RequestMethod.GET)
    public JsonResponse queryByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskItem taskItemQr = new TaskItem();
            taskItemQr.setId(id);
            TaskItem taskItem = iTaskItemService.selectOneTaskItem(taskItemQr);
            jsonResponse.data = taskItem;
        };
        return doing.go(request, logger);

    }


    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/taskItem/{id}", method = RequestMethod.DELETE)
    public JsonResponse deleteByBusinessKey(@PathVariable("id") Long id, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            TaskItem taskItemQr = new TaskItem(id);
            iTaskItemService.deleteTaskItem(taskItemQr);
        };
        return doing.go(request, logger);

    }

    /**
     * @param taskItem
     * @return
     */
    @RequestMapping(value = "/taskItem/{id}", method = RequestMethod.PUT)
    public JsonResponse update(@PathVariable("id") Long id,  /**@Valid*/@RequestBody TaskItem taskItem, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            taskItem.setId(id);
            taskItem.setVersion(taskItem.getVersion());
            iTaskItemService.updateTaskItem(taskItem);
        };
        return doing.go(request, logger);
    }

    /**
     * @param taskItems
     * @return
     */
    @RequestMapping(value = "/taskItems", method = RequestMethod.PUT)
    public JsonResponse<Integer> update(@RequestBody List<TaskItem> taskItems, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.batchUpdate(taskItems);
        };
        return doing.go(request, logger);
    }


    /**
     * @param taskItems
     * @return
     */
    @RequestMapping(value = "/taskItems/batchUpdateQty", method = RequestMethod.PUT)
    public JsonResponse<Integer> batchUpdateQty(@RequestBody List<TaskItem> taskItems, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.batchUpdateQty(taskItems);
        };
        return doing.go(request, logger);
    }


    /**
     * @param taskItems
     * @return
     */
    @RequestMapping(value = "/taskItems/insertBatch", method = RequestMethod.POST)
    public JsonResponse<Integer> insertBatch(@RequestBody List<TaskItem> taskItems, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.insertBatch(taskItems);
        };
        return doing.go(request, logger);
    }

    /**
     * @param taskItems
     * @return
     */
    @RequestMapping(value = "/taskItems/deleteBatch", method = RequestMethod.POST)
    public JsonResponse<Integer> deleteBatch(@RequestBody List<TaskItem> taskItems, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.deleteBatch(taskItems);
        };
        return doing.go(request, logger);
    }
    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/taskItems", method = RequestMethod.GET)
    public JsonResponse search(TaskItem taskItem, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<TaskItem> PageResponse = iTaskItemService.selectTaskItemPage(taskItem);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }


    /**
     * @return 查询集合
     */
    @RequestMapping(value = "/taskItemsPost", method = RequestMethod.POST)
    public JsonResponse searchPost(@RequestBody TaskItem taskItem, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            PageResponse<TaskItem> PageResponse = iTaskItemService.selectTaskItemPage(taskItem);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }


    /**
     * @return 查询集合
     */
    @RequestMapping(value = TaskRouters.SELECT_TASK_DETAIL_BY_TASK, method = RequestMethod.POST)
    public JsonResponse searchPostByTask(@RequestBody Task task, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            if(StringUtils.isBlank(task.getOrderNo())){
                return;
            }
            Task taskR = iTaskService.selectOneTask(task);
            TaskItem taskItem = new TaskItem();
            taskItem.setTaskNo(taskR.getTaskNo());
            PageResponse<TaskItem> PageResponse = iTaskItemService.selectTaskItemPage(taskItem);
            jsonResponse.data = PageResponse;
        };
        return doing.go(request, logger);

    }


    /**
     * 查询集合
     * @param adjustApply
     * @return
     */
    @RequestMapping(value = OrderRouters.TASKITEMS_LIST, method = RequestMethod.GET)
    public JsonResponse list(@SpringQueryMap TaskItem taskItem, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            taskItem.setPageSize(CommonConstant.LIST_PAGE_SIZE);
            List<TaskItem> list = iTaskItemService.selectTaskItemByIndex(taskItem);
            jsonResponse.data = list;
        };
        return doing.go(request, logger);
    }


    /**
     * 查询集合
     * @param adjustApply
     * @return
     */
    @RequestMapping(value = OrderRouters.TASKITEMS_SELECT_ONE, method = RequestMethod.GET)
    public JsonResponse selectOne(@SpringQueryMap TaskItem taskItem, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            TaskItem obj = iTaskItemService.selectOneTaskItem(taskItem);
            jsonResponse.data = obj;
        };
        return doing.go(request, logger);
    }

    /**
     * 根据任务号查询任务详情
     * @param taskNos
     * @return
     */
    @RequestMapping(value = "/taskItems/taskNos", method = RequestMethod.POST)
    JsonResponse<List<TaskItem>> listByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.listByTaskNos(taskNos);
        };
        return doing.go(request, logger);
    }
    
    /**
     * 根据父单号查询任务详情
     * @param taskNos
     * @return
     */
    @RequestMapping(value = "/taskItems/byParentOrderNo", method = RequestMethod.GET)
    JsonResponse<List<TaskItem>> listByParentOrderNo(@RequestParam("parentOrderNo") String parentOrderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.listByParentOrderNo(parentOrderNo);
        };
        return doing.go(request, logger);
    }

    /**
     * 根据任务号查询ackQty 求和
     * @param taskNos
     * @return
     */
    @RequestMapping(value = OrderRouters.TASKITEMS_SUM_ACT_QTY_BY_TASK_NOS, method = RequestMethod.POST)
    JsonResponse<List<TaskItem>> sumActQtyByTaskNos(@RequestBody List<String> taskNos, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.sumActQtyByTaskNos(taskNos);
        };
        return doing.go(request, logger);
    }

    /**
     * 批量更新任务明细
     */
    @RequestMapping(value = "/taskItem/batchUpdate", method = RequestMethod.PUT)
    public JsonResponse<Integer> batchUpdate(@RequestBody List<TaskItem> taskItems, HttpServletRequest request) {

        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.batchUpdate(taskItems);
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = "/taskItems/byParentOrderNoAndDistributionNum", method = RequestMethod.GET)
    JsonResponse<List<TaskItem>> listByParentOrderNoAndDistributeNum(@RequestParam("parentOrderNo") String parentOrderNo,@RequestParam("distributionNum") Integer distributionNum,HttpServletRequest request){
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.listByParentOrderNoAndDistributionNum(parentOrderNo,distributionNum);
        };
        return doing.go(request, logger);
    }

    @RequestMapping(value = OrderRouters.TASKITEMS_QUERY_SPLIT_TASKS_BY_ORDER_NO, method = RequestMethod.GET)
    JsonResponse<List<TaskItem>> querySplitTasksByOrderNo(@RequestParam("orderNo") String orderNo, HttpServletRequest request) {
        RestDoing doing = jsonResponse -> {
            jsonResponse.data = iTaskItemService.querySplitTasksByOrderNo(orderNo);
        };
        return doing.go(request, logger);
    }

}
