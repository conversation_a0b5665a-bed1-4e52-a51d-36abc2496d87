package com.midea.logistics.otplbs.bean;

import lombok.Data;

@Data
public class ItsmLoginResponse {

    /**
     * {
     *     "statusCode": "00",
     *     "status": true,
     *     "tokenId": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2OTU5NTUzNDM3MjEsInBheWxvYWQiOiJsaW5kcTIifQ.E-eWw2jgm9w-FcYXHvaSHhed1__-Z65pv1-85aRV18g",
     *     "content": "登录成功"
     * }
     */

    private String statusCode;
    private Boolean status;
    private String tokenId;
    private String content;
}
