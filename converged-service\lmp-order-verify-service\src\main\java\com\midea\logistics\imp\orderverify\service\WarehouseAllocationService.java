package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.domain.mdm.domain.CustomerOrderInfo;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.mideaframework.core.web.JsonResponse;

/**
* @description: 仓间调拨
* @author: 陈永培
* @createtime: 2020/11/14 9:41
*/
public interface WarehouseAllocationService {

   /**
   * @description: 仓间调拨解析
   * @param: [customerOrderInfoExt]
   * @return: CustomerOrderInfoExt
   * @author: 陈永培
   * @createtime: 2020/11/14 9:41
   */
   CustomerOrderInfoExt allocationParse(CustomerOrderInfoExt customerOrderInfoExt);
}
