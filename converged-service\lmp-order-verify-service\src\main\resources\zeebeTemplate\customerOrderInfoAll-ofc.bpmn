<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_05p2bh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:message id="Message_0vzd89y" name="Message_0hcjpga">
    <bpmn:extensionElements>
      <zeebe:subscription correlationKey="=customerOrderInfo.orderNo" />
    </bpmn:extensionElements>
  </bpmn:message>
  <bpmn:process id="customer-order-process-all-ofc" name="父订单流程-ofc" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="= customerOrderInfo" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_08mw2j3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_0d15yei" name="商品确认">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="itemMapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/item/mappingOfc" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="= customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1oyv8yd</bpmn:incoming>
      <bpmn:outgoing>Flow_0lyaql5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1k6ho42" name="订单映射">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mapping" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/order/mapping" />
          <zeebe:header key="method" value="POST" />
          <zeebe:header key="requestHeaders" value="{&#34;tenantCode&#34;:&#34;annto&#34;}" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08mw2j3</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyv8yd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0nccbee" name="收货单位解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/explain/receive/platform/customer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="receive" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1uewv0z</bpmn:incoming>
      <bpmn:outgoing>Flow_07qlm7g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_119zmlg" name="仓间调拨解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/allocationParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="allocationParse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1n6udmd</bpmn:incoming>
      <bpmn:outgoing>Flow_0v8tmtg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1i377su" name="地址解析">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-order-agg-service/addressResolving" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="address" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k9k1b7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wi0xbe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0kptmf2" name="货权转移校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/validate/cargo/right/transfer" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p7943l</bpmn:incoming>
      <bpmn:outgoing>Flow_1wuusbt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1g2qvk8" name="是否仓间调拨" default="Flow_1odmwry">
      <bpmn:incoming>Flow_07qlm7g</bpmn:incoming>
      <bpmn:incoming>Flow_1eh4qu3</bpmn:incoming>
      <bpmn:outgoing>Flow_1n6udmd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1odmwry</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_04dmfyf" name="是否地址解析" default="Flow_191u9lk">
      <bpmn:incoming>Flow_1odmwry</bpmn:incoming>
      <bpmn:incoming>Flow_0v8tmtg</bpmn:incoming>
      <bpmn:outgoing>Flow_191u9lk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k9k1b7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0fo5mo8" name="是否货权转移" default="Flow_1o83mga">
      <bpmn:incoming>Flow_05szt6n</bpmn:incoming>
      <bpmn:outgoing>Flow_1o83mga</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p7943l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1b565p2" name="是否分仓拆单">
      <bpmn:incoming>Flow_1o83mga</bpmn:incoming>
      <bpmn:incoming>Flow_1wuusbt</bpmn:incoming>
      <bpmn:outgoing>Flow_0kj1gz5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1yfd9e1" name="是否走收货单位解析" default="Flow_1eh4qu3">
      <bpmn:incoming>Flow_0lyaql5</bpmn:incoming>
      <bpmn:outgoing>Flow_1uewv0z</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eh4qu3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_07ox4pt" name="统配计费解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/lmp/businessCategoryAndTcFlag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xkk2iv</bpmn:incoming>
      <bpmn:outgoing>Flow_05szt6n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_09zbrsb" name="结束">
      <bpmn:incoming>Flow_0jufsid</bpmn:incoming>
      <bpmn:incoming>Flow_0uiqdgn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_0uruim6" name="分仓拆单">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/separateWarehouse/automatic" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="separateWarehouse" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_06j9hoe</bpmn:incoming>
      <bpmn:outgoing>Flow_0jufsid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_05szt6n" sourceRef="Activity_07ox4pt" targetRef="Gateway_0fo5mo8" />
    <bpmn:sequenceFlow id="Flow_0lyaql5" sourceRef="Activity_0d15yei" targetRef="Gateway_1yfd9e1" />
    <bpmn:sequenceFlow id="Flow_08mw2j3" sourceRef="StartEvent_1" targetRef="Activity_1k6ho42" />
    <bpmn:sequenceFlow id="Flow_1eh4qu3" name="no" sourceRef="Gateway_1yfd9e1" targetRef="Gateway_1g2qvk8" />
    <bpmn:sequenceFlow id="Flow_1uewv0z" name="yes" sourceRef="Gateway_1yfd9e1" targetRef="Activity_0nccbee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CIMSPO","CCSPO","CAINIAOAO","CAINIAOYS","CIMSRI","OFCPO","OFCAO","OFCRO","OFCCZF","OFCZF","MSSPO","MSSAO","MSSRO","MSSCZF"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0jufsid" sourceRef="Activity_0uruim6" targetRef="Event_09zbrsb" />
    <bpmn:sequenceFlow id="Flow_0kj1gz5" name="no" sourceRef="Gateway_1b565p2" targetRef="Gateway_1nna105" />
    <bpmn:sequenceFlow id="Flow_1wuusbt" sourceRef="Activity_0kptmf2" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_1p7943l" name="yes" sourceRef="Gateway_0fo5mo8" targetRef="Activity_0kptmf2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=some x in ["CAINIAOPO","CCSJDPI","CCSPO","CCSPI","CIMSPO","CIMSAI","CIMSPI","CIMSRI","OFCPI","OFCAI","OFCPO","MSSAO","MSSPO","MSSRO","MSSAI","MSSPI","MSSRI","MSS-STCPO","MSS-STCTO","MSS-STCADO","MSS-STCPI","MSS-STCTI","MSS-STCADI"] satisfies x = customerOrderInfo.sourceSystem + customerOrderInfo.orderType</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1k9k1b7" name="yes" sourceRef="Gateway_04dmfyf" targetRef="Activity_1i377su">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo!=null and (((customerOrderInfo.deliveryType!=null)=false or (customerOrderInfo.deliveryType!=null and customerOrderInfo.deliveryType!="ZT")) and (customerOrderInfo.orderType!=null and customerOrderInfo.orderType!="RDO") and (customerOrderInfo.orderSource!=null and customerOrderInfo.orderSource!="HANDLE")))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1o83mga" name="no" sourceRef="Gateway_0fo5mo8" targetRef="Gateway_1b565p2" />
    <bpmn:sequenceFlow id="Flow_1wi0xbe" sourceRef="Activity_1i377su" targetRef="Activity_0dnj7py" />
    <bpmn:sequenceFlow id="Flow_191u9lk" name="no" sourceRef="Gateway_04dmfyf" targetRef="Activity_0dnj7py" />
    <bpmn:sequenceFlow id="Flow_0v8tmtg" sourceRef="Activity_119zmlg" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_1odmwry" sourceRef="Gateway_1g2qvk8" targetRef="Gateway_04dmfyf" />
    <bpmn:sequenceFlow id="Flow_1n6udmd" name="yes" sourceRef="Gateway_1g2qvk8" targetRef="Activity_119zmlg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=customerOrderInfo!=null and customerOrderInfo.orderType!=null and customerOrderInfo.sourceSystem!=null and ((customerOrderInfo.sourceSystem="CIMS" and customerOrderInfo.orderType="PO") or (customerOrderInfo.sourceSystem="TTX" and customerOrderInfo.orderType="PO")  or (customerOrderInfo.sourceSystem="CCS" and (customerOrderInfo.orderType="AI" or customerOrderInfo.orderType="AO")) or (customerOrderInfo.sourceSystem="OFC" and (customerOrderInfo.orderType="AO" or customerOrderInfo.orderType="PO" or customerOrderInfo.orderType="RO")) or (customerOrderInfo.sourceSystem="MSS" and (customerOrderInfo.orderType="PO" or customerOrderInfo.orderType="AO")) or (customerOrderInfo.sourceSystem="MRP" and (customerOrderInfo.orderType="AO" or customerOrderInfo.orderType="PO" or customerOrderInfo.orderType="RO")))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07qlm7g" sourceRef="Activity_0nccbee" targetRef="Gateway_1g2qvk8" />
    <bpmn:sequenceFlow id="Flow_1oyv8yd" sourceRef="Activity_1k6ho42" targetRef="Activity_0d15yei" />
    <bpmn:serviceTask id="Activity_1wn8uta" name="前置任务生成">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/preposeTask/generate" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1b8os14</bpmn:incoming>
      <bpmn:outgoing>Flow_0uiqdgn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1nna105" name="是否后分仓" default="Flow_06j9hoe">
      <bpmn:incoming>Flow_0kj1gz5</bpmn:incoming>
      <bpmn:outgoing>Flow_11he6v7</bpmn:outgoing>
      <bpmn:outgoing>Flow_06j9hoe</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11he6v7" name="yes" sourceRef="Gateway_1nna105" targetRef="Activity_0tzxjdv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(customerOrderInfo.deliveryType=null or (customerOrderInfo.deliveryType!="ZT" and customerOrderInfo.deliveryType!="EXPRESS" )) and (customerOrderInfo.inOutType!=null and (customerOrderInfo.inOutType in ("OUT","YS"))) and (customerOrderInfo.orderDistinctionFlag!=null and (customerOrderInfo.orderDistinctionFlag in ("BO-RW","BO-GRW")))</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_06j9hoe" name="no" sourceRef="Gateway_1nna105" targetRef="Activity_0uruim6" />
    <bpmn:sequenceFlow id="Flow_0uiqdgn" sourceRef="Activity_1wn8uta" targetRef="Event_09zbrsb" />
    <bpmn:serviceTask id="Activity_0tzxjdv" name="爆仓校验">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/isOutArea/byCustomerOrderInfoExt" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:taskDefinition type="transfer" retries="1" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_11he6v7</bpmn:incoming>
      <bpmn:outgoing>Flow_1b8os14</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1b8os14" sourceRef="Activity_0tzxjdv" targetRef="Activity_1wn8uta" />
    <bpmn:serviceTask id="Activity_0dnj7py" name="标准产品解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="businessCategory" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=customerOrderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="customerOrderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/standardProductAnalysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wi0xbe</bpmn:incoming>
      <bpmn:incoming>Flow_191u9lk</bpmn:incoming>
      <bpmn:outgoing>Flow_1xkk2iv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1xkk2iv" sourceRef="Activity_0dnj7py" targetRef="Activity_07ox4pt" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customer-order-process-all-ofc">
      <bpmndi:BPMNEdge id="Flow_1xkk2iv_di" bpmnElement="Flow_1xkk2iv">
        <di:waypoint x="1370" y="380" />
        <di:waypoint x="1370" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b8os14_di" bpmnElement="Flow_1b8os14">
        <di:waypoint x="720" y="640" />
        <di:waypoint x="640" y="640" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uiqdgn_di" bpmnElement="Flow_0uiqdgn">
        <di:waypoint x="540" y="640" />
        <di:waypoint x="400" y="640" />
        <di:waypoint x="400" y="488" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06j9hoe_di" bpmnElement="Flow_06j9hoe">
        <di:waypoint x="745" y="470" />
        <di:waypoint x="640" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="686" y="452" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11he6v7_di" bpmnElement="Flow_11he6v7">
        <di:waypoint x="770" y="495" />
        <di:waypoint x="770" y="600" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="777" y="570" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyv8yd_di" bpmnElement="Flow_1oyv8yd">
        <di:waypoint x="370" y="230" />
        <di:waypoint x="480" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07qlm7g_di" bpmnElement="Flow_07qlm7g">
        <di:waypoint x="898" y="230" />
        <di:waypoint x="935" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n6udmd_di" bpmnElement="Flow_1n6udmd">
        <di:waypoint x="960" y="205" />
        <di:waypoint x="960" y="151" />
        <di:waypoint x="991" y="151" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="965" y="175" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1odmwry_di" bpmnElement="Flow_1odmwry">
        <di:waypoint x="985" y="230" />
        <di:waypoint x="1085" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v8tmtg_di" bpmnElement="Flow_0v8tmtg">
        <di:waypoint x="1091" y="151" />
        <di:waypoint x="1110" y="151" />
        <di:waypoint x="1110" y="205" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191u9lk_di" bpmnElement="Flow_191u9lk">
        <di:waypoint x="1110" y="255" />
        <di:waypoint x="1110" y="340" />
        <di:waypoint x="1320" y="340" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1214" y="318" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wi0xbe_di" bpmnElement="Flow_1wi0xbe">
        <di:waypoint x="1370" y="270" />
        <di:waypoint x="1370" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o83mga_di" bpmnElement="Flow_1o83mga">
        <di:waypoint x="1078" y="470" />
        <di:waypoint x="945" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1008" y="452" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9k1b7_di" bpmnElement="Flow_1k9k1b7">
        <di:waypoint x="1135" y="230" />
        <di:waypoint x="1320" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1167" y="213" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p7943l_di" bpmnElement="Flow_1p7943l">
        <di:waypoint x="1103" y="495" />
        <di:waypoint x="1103" y="584" />
        <di:waypoint x="1059" y="584" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1081" y="523" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wuusbt_di" bpmnElement="Flow_1wuusbt">
        <di:waypoint x="959" y="584" />
        <di:waypoint x="920" y="584" />
        <di:waypoint x="920" y="495" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kj1gz5_di" bpmnElement="Flow_0kj1gz5">
        <di:waypoint x="895" y="470" />
        <di:waypoint x="795" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="873" y="513" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jufsid_di" bpmnElement="Flow_0jufsid">
        <di:waypoint x="540" y="470" />
        <di:waypoint x="418" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uewv0z_di" bpmnElement="Flow_1uewv0z">
        <di:waypoint x="756" y="230" />
        <di:waypoint x="798" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="207" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eh4qu3_di" bpmnElement="Flow_1eh4qu3">
        <di:waypoint x="731" y="205" />
        <di:waypoint x="731" y="150" />
        <di:waypoint x="940" y="150" />
        <di:waypoint x="940" y="225" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="829" y="132" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08mw2j3_di" bpmnElement="Flow_08mw2j3">
        <di:waypoint x="188" y="230" />
        <di:waypoint x="270" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lyaql5_di" bpmnElement="Flow_0lyaql5">
        <di:waypoint x="580" y="230" />
        <di:waypoint x="706" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05szt6n_di" bpmnElement="Flow_05szt6n">
        <di:waypoint x="1320" y="470" />
        <di:waypoint x="1128" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="212" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xzxgxd_di" bpmnElement="Activity_0d15yei">
        <dc:Bounds x="480" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0k7fj18_di" bpmnElement="Activity_1k6ho42">
        <dc:Bounds x="270" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g3mnwv_di" bpmnElement="Activity_0nccbee">
        <dc:Bounds x="798" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s5niqa_di" bpmnElement="Activity_119zmlg">
        <dc:Bounds x="991" y="111" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0hyno_di" bpmnElement="Activity_1i377su">
        <dc:Bounds x="1320" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07zfya5_di" bpmnElement="Activity_0kptmf2">
        <dc:Bounds x="959" y="544" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g2qvk8_di" bpmnElement="Gateway_1g2qvk8" isMarkerVisible="true">
        <dc:Bounds x="935" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="930" y="262" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04dmfyf_di" bpmnElement="Gateway_04dmfyf" isMarkerVisible="true">
        <dc:Bounds x="1085" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1118" y="203" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fo5mo8_di" bpmnElement="Gateway_0fo5mo8" isMarkerVisible="true">
        <dc:Bounds x="1078" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1071" y="421" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b565p2_di" bpmnElement="Gateway_1b565p2" isMarkerVisible="true">
        <dc:Bounds x="895" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="889" y="424" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yfd9e1_di" bpmnElement="Gateway_1yfd9e1" isMarkerVisible="true">
        <dc:Bounds x="706" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="694" y="262" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w1igdz_di" bpmnElement="Activity_07ox4pt">
        <dc:Bounds x="1320" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09zbrsb_di" bpmnElement="Event_09zbrsb">
        <dc:Bounds x="382" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="389" y="428" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mfhri_di" bpmnElement="Activity_0uruim6">
        <dc:Bounds x="540" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1wn8uta_di" bpmnElement="Activity_1wn8uta">
        <dc:Bounds x="540" y="600" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nna105_di" bpmnElement="Gateway_1nna105" isMarkerVisible="true">
        <dc:Bounds x="745" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="742" y="421" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0tzxjdv_di" bpmnElement="Activity_0tzxjdv">
        <dc:Bounds x="720" y="600" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0dnj7py_di" bpmnElement="Activity_0dnj7py">
        <dc:Bounds x="1320" y="300" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
