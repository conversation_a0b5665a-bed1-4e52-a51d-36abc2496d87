package com.midea.logistics.otp.ttx.datasource;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2019-8-28 19:18
 */
@Configuration
public class DataSourceConfig {
    /**地址为application.properteis中对应属性的前缀*/
    @Bean(name="otpDataSourceProperties")
    @ConfigurationProperties(prefix = "spring.datasource.otp")
    public DataSourceProperties otpDataSourceProperties(){
        return new DataSourceProperties();
    }


    @Bean(name = "otpDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.otp")
    public DataSource otpDataSource() {
        return otpDataSourceProperties().initializeDataSourceBuilder().build();
    }


    @Bean(name="wmsDataSourceProperties")
    @ConfigurationProperties(prefix = "spring.datasource.wms")
    public DataSourceProperties wmsDataSourceProperties(){
        return new DataSourceProperties();
    }

    @Bean(name="wmsDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.wms")
    public DataSource wmsDataSource() {
        return wmsDataSourceProperties().initializeDataSourceBuilder().build();
    }


}