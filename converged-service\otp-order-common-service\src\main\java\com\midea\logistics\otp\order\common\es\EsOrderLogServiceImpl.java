package com.midea.logistics.otp.order.common.es;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.order.CustomerOrderItemFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.OrderLogFeign;
import com.midea.logistics.otp.common.utils.DateUtils;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.OrderOperateType;
import com.midea.logistics.otp.order.common.enums.EsOperateType;
import com.midea.logistics.otp.order.converged.domain.bean.InformationSecurityOperationVo;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderLog;
import com.midea.logistics.otp.order.domain.bean.custom.OrderLogExt;
import com.mideaframework.core.auth.ISsoService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.bulk.BackoffPolicy;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.function.BiConsumer;

import static cn.hutool.core.date.DatePattern.SIMPLE_MONTH_PATTERN;
import static com.midea.logistics.otp.enums.OrderOperateType.CLOSE_ORDER;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: EsOrderLogService
 * @description:
 * @author: kongly1
 * @date: 2021-8-3 8:59
 */
@Slf4j
@Service
public class EsOrderLogServiceImpl implements EsOrderLogService {

    @Value("${lc.es.saveType:es}")
    String saveLogType;

    @Autowired
    OrderLogFeign orderLogFeign;

    @Autowired
    RestHighLevelClient client;

    @Autowired
    ISsoService iSsoService;

    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;

    @Autowired
    private DictHelper dictHelper;

    private static BulkProcessor bulkProcessor;


    final static List<String> emptyList = Lists.newArrayList(
        //OrderOperateType.ADDRESS_ANALYSIS.getKey(),
        OrderOperateType.APART.getKey(),
        OrderOperateType.AUDIT_ITEMS.getKey(),
        OrderOperateType.CARGO_RIGHT_TRANSFER.getKey(),
        OrderOperateType.ISSUED_TASK.getKey(),
        //OrderOperateType.ISSUED_TASK_LASTMILE.getKey(),
//        OrderOperateType.NET_BACK.getKey(),
        OrderOperateType.ORDER_MAPPING.getKey(),
        OrderOperateType.PLEDGE_VERIFY.getKey(),
        OrderOperateType.PUSH_LOTS.getKey(),
        OrderOperateType.TRAN_TMS.getKey(),
        OrderOperateType.UPDATE_RPFLAG.getKey(),
        OrderOperateType.TRAN_WMS.getKey(),
        OrderOperateType.CANCEL_CSS_NET.getKey(),
        OrderOperateType.CANCEL_LAST_NET.getKey(),
        OrderOperateType.CANCEL_TMS.getKey(),
        OrderOperateType.CANCEL_WMS.getKey(),
        OrderOperateType.CANCEL_TSS.getKey(),
        OrderOperateType.CANCEL_CHECK_ORDER.getKey(),
        //OrderOperateType.CANCEL_POST.getKey(),
        OrderOperateType.CANCEL_ARRIVEAREA.getKey(),
        OrderOperateType.CANCEL_ONWAY.getKey(),
        OrderOperateType.CANCEL_NET.getKey(),
        OrderOperateType.INVENTORY_ROLLBACK.getKey(),
        OrderOperateType.PLEDGE_CANCEL.getKey(),
        OrderOperateType.CANCEL_UPDATE_CUSTOMER.getKey(),
        OrderOperateType.CANCEL_UPDATE_ORDER.getKey(),
        OrderOperateType.CANCEL_UPDATE_BMS.getKey(),
        OrderOperateType.CANCEL_UPDATE_TRACE.getKey(),
        OrderOperateType.CANCEL_UPDATE_TASK.getKey(),
        OrderOperateType.CANCEL_UPDATE_ITEM.getKey(),
        OrderOperateType.RETURN_UPSTREAM.getKey(),
        OrderOperateType.FINANCIAL_SYN.getKey(),
        OrderOperateType.CANCEL_TRANSFER_FLOW.getKey(),
        OrderOperateType.REVEIVE_UNIT.getKey(),
        OrderOperateType.CANCEL_TRANSFER_FLOW_RESULT.getKey(),
        OrderOperateType.PART_CANCEL.getKey(),
        OrderOperateType.WHOLE_CANCEL.getKey(),
        "PART_CANCEL_SUCCESS",
        "WHOLE_CANCEL_SUCCESS",
        OrderOperateType.WITHDRAW_TO_TMS.getKey(),
        OrderOperateType.WITHDRAW_TO_WMS.getKey(),
        OrderOperateType.WITHDRAW_TO_TTX.getKey(),
        OrderOperateType.WITHDRAW_TO_DPS.getKey(),
        OrderOperateType.WITHDRAW_ARRAVE.getKey(),
        OrderOperateType.WITHDRAW_ONWAY.getKey(),
        OrderOperateType.INTERCEPT.getKey(),
        OrderOperateType.THIRD_ANALYSIS.getKey(),
        OrderOperateType.INTERCEPT_FAILED.getKey(),
        OrderOperateType.WITHDRAW_TMS.getKey(),
        OrderOperateType.WITHDRAW_IN_WMS.getKey(),
        OrderOperateType.WITHDRAW_OUT_WMS.getKey(),
        OrderOperateType.WITHDRAW_CSS.getKey(),
        OrderOperateType.WITHDRAW_NET.getKey(),
        OrderOperateType.INTERCEPT_NET.getKey(),
        OrderOperateType.INTERCEPT_CSS.getKey(),
        OrderOperateType.INTERCEPT_FLAG.getKey(),
        OrderOperateType.INTERCEPT_SMALL_ELECTRICITY.getKey(),
        OrderOperateType.WITHDRAW_DISPATCHED_WORKER.getKey(),
        OrderOperateType.WITHDRAW_DISPATCHED_WORKER.getKey(),
        OrderOperateType.INTERCEPT_NO_FIT.getKey(),
        OrderOperateType.NO_CREATE_REJECT_ORDER.getKey(),
        OrderOperateType.CREATE_REJECT_ORDER.getKey(),
        OrderOperateType.CANCEL_IN_DISTRIBUTION.getKey(),
        OrderOperateType.INVENTORYFLOW.getKey(), OrderOperateType.BMS_INFO.getKey(), OrderOperateType.WITHDRAW.getKey(),
        OrderOperateType.PARENT_OVERFLOW_VERIFY.getKey()
    );


    public BulkProcessor bulkProcessor() {
        if (null == bulkProcessor) {
            synchronized (this) {
                BiConsumer<BulkRequest, ActionListener<BulkResponse>> bulkConsumer =
                    (request, bulkListener) -> client.bulkAsync(request, RequestOptions.DEFAULT, bulkListener);

                bulkProcessor = BulkProcessor.builder(bulkConsumer, new BulkProcessor.Listener() {
                    @Override
                    public void beforeBulk(long executionId, BulkRequest request) {
                        //在这儿你可以自定义执行同步之前执行什么
                        return;
                    }

                    @SneakyThrows
                    @Override
                    public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
                        //在这儿你可以自定义执行完同步之后执行什么
                        return;
                    }

                    @Override
                    public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
                        //写入失败后
                        failure.printStackTrace();
                        log.error("ES写入失败", failure);
                        log.error("ES 写入失败 request : ", JSON.toJSONString(request.requests()));
                    }
                }).setBulkActions(3000)
                    .setBulkSize(new ByteSizeValue(5, ByteSizeUnit.MB))
                    .setFlushInterval(TimeValue.timeValueSeconds(2))
                    .setConcurrentRequests(20)
                    .setBackoffPolicy(BackoffPolicy.exponentialBackoff(TimeValue.timeValueMillis(100), 3))
                    .build();
            }

        }
        return bulkProcessor;
    }


    /**
     * 保存日记
     *
     * @return
     */
    @Override
    public void saveLog(OrderLog orderLog) {

//        orderLogFeign.create(orderLog);
        if ("mysql".equals(saveLogType)){
            orderLogFeign.create(orderLog);
            return;
        }
        orderLog.setCreateTime(new Date());
        String index = "oms_order_log_" + DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
//        String index = "oms_order_log";
        IndexRequest indexRequest = new IndexRequest(index);
        if (StringUtils.isBlank(orderLog.getCreateUserCode())) {
            String userCode = iSsoService.getUserCode();
            if (StringUtils.isBlank(userCode)) {
                userCode = "system";
            }
            orderLog.setCreateUserCode(userCode);
        }
        if (StringUtils.isBlank(orderLog.getUpdateUserCode())) {
            String userCode = iSsoService.getUserCode();
            if (StringUtils.isBlank(userCode)) {
                userCode = "system";
            }
            orderLog.setUpdateUserCode(userCode);
        }
        orderLog.setUpdateTime(orderLog.getCreateTime());
        indexRequest.source(JSON.toJSONString(orderLog), XContentType.JSON);
        indexRequest.routing(orderLog.getParentOrderNo());
        bulkProcessor().add(indexRequest);
        return;
    }

    @Override
    public List<OrderLogExt> searchLogByTaskNo(String taskNo) {
        //        String index = "oms_order_log_" + DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
        String index = "oms_order_log";
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchQuery("taskNo", taskNo));
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        List<OrderLogExt> orderLogList = getOrderLogExts(searchRequest);

        boolean isAllLog = false;
        if (!orderLogList.isEmpty()) {
            // 判断下有没有运作模式解析的
            isAllLog = orderLogList.stream().filter(m -> m.getOperateType().equals("LOGISTIC_MODE")).findAny().isPresent();
        }
        //如果没有订单映射的日记， 日记列表也是空的，去数据那边查一下
        if (!isAllLog) {
            OrderLog orderLog = new OrderLog();
            orderLog.setTaskNo(taskNo);
            JsonResponse<List<OrderLogExt>> orderLogFeignResult = orderLogFeign.search(orderLog);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(orderLogFeignResult.getCode())) {
                throw BusinessException.fail("查询订单日记异常：" + orderLogFeignResult.getMsg());
            }
            List dbOrderLog = orderLogFeignResult.getData();
            orderLogList.addAll(dbOrderLog);
        }
        return orderLogList;
    }

    private List<OrderLogExt> getOrderLogExts(SearchRequest searchRequest) {
        SearchResponse result = null;
        try {
            result = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("ES 查询日记失败：", e.getMessage());
        }
        boolean fullEsFlag = true;

        List<OrderLogExt> orderLogList = new ArrayList<>();
        if (null != result) {
            SearchHit[] searchHitArr = result.getHits().getHits();

            for (SearchHit searchHit : searchHitArr) {
                Map<String, Object> temp = searchHit.getSourceAsMap();

                if (null == temp || temp.isEmpty()) {
                    continue;
                }
                OrderLogExt orderLog = new OrderLogExt();
                try {
                    BeanUtils.populate(orderLog, temp);
                } catch (IllegalAccessException e) {
                    log.error(e.getMessage());
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                    log.error(e.getMessage());
                }

                if (StringUtils.isNotBlank(orderLog.getOperateType()) && EnumUtils.isValidEnum(OrderOperateType.class, orderLog.getOperateType())) {
                    orderLog.setOperateName(EnumUtils.getEnum(OrderOperateType.class, orderLog.getOperateType()).getValue());
                }
                orderLogList.add(orderLog);
            }

        }

        Collections.sort(orderLogList, new Comparator<OrderLogExt>() {
            @Override
            public int compare(OrderLogExt h1, OrderLogExt h2) {
                return h2.getCreateTime().compareTo(h1.getCreateTime());
            }
        });

        //过滤不要的数据，同步需要去掉那些success  的提示语
        orderLogList.stream().forEach(orderLogExt -> {
            String operateContent = orderLogExt.getOperateContent();
            String operateType = orderLogExt.getOperateType();
            if (ToolUtils.isNotEmpty(operateContent)
                && ToolUtils.isNotEmpty(operateType)
                && emptyList.contains(orderLogExt.getOperateType())
                && CommonConstant.STRING_FLAG_YES.equals(orderLogExt.getOperateFlag())) {
                orderLogExt.setOperateContent(null);
            }
            if (null != operateContent && BaseCodeEnum.SUCCESS.getMsg().equalsIgnoreCase(operateContent)) {
                orderLogExt.setOperateContent(null);
            }
            //if(StringUtils.isNotBlank(orderLogExt.getOperateContent()) && OrderOperateType.AUDIT_ITEMS.getKey().equals(orderLogExt.getOperateType())){
            //    orderLogExt.setOperateContent(orderLogExt.getOperateContent().replace("/ by zero", "商品基础数据配置了0或接近0的数值，请检查！"));
            //}
        });
        return orderLogList;

    }


    /**
     * 查询日记
     */
    @Override
    public List<OrderLogExt> searchLogByParentOrderNo(String orderNo) {
//        RestHighLevelClient restHighLevelClient = client;
        //        String index = "oms_order_log_" + DateUtil.format(new Date(), SIMPLE_MONTH_PATTERN);
        String index = "oms_order_log";
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.routing(orderNo);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchQuery("parentOrderNo", orderNo));
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        List<OrderLogExt> orderLogList = getOrderLogExts(searchRequest);

        boolean isAllLog = false;
        if (!orderLogList.isEmpty()) {
            // 判断下有没有订单映射的
            isAllLog = orderLogList.stream().filter(m -> m.getOperateType().equals("ORDER_MAPPING")).findAny().isPresent();
        }
        //如果没有订单映射的日记， 日记列表也是空的，去数据那边查一下
        if (!isAllLog) {
            OrderLog orderLog = new OrderLog();
            orderLog.setParentOrderNo(orderNo);
            JsonResponse<List<OrderLogExt>> orderLogFeignResult = orderLogFeign.search(orderLog);
            if (!BaseCodeEnum.SUCCESS.getCode().equals(orderLogFeignResult.getCode())) {
                throw BusinessException.fail("查询订单日记异常：" + orderLogFeignResult.getMsg());
            }
            List dbOrderLog = orderLogFeignResult.getData();
            orderLogList.addAll(dbOrderLog);
        }

        // 处理关单类型的备注信息
        closeOrderDiffReasonProcess(orderNo, orderLogList);

        Collections.sort(orderLogList, new Comparator<OrderLogExt>() {
            @Override
            public int compare(OrderLogExt h1, OrderLogExt h2) {
                return h2.getCreateTime().compareTo(h1.getCreateTime());
            }
        });
        return orderLogList;
    }

    private static final String DIFF_REASON_FORMAT = "商品编码{%s}%s；";

    @SuppressWarnings({"unchecked"})
    private void closeOrderDiffReasonProcess(String orderNo, List<OrderLogExt> orderLogList) {
        if (StringUtils.isBlank(orderNo)
            || CollectionUtil.isEmpty(orderLogList)) {
            return;
        }
        orderLogList.stream().filter(item -> Objects.equals(CLOSE_ORDER.getKey(), item.getOperateType())).findFirst().ifPresent(item -> {
            List<CustomerOrderItem> customerOrderItems = customerOrderItemFeign.listByOrderNo(orderNo).getData();
            if (CollectionUtil.isNotEmpty(customerOrderItems)) {
                String closeOrderRemark = "";
                try {
                    if (StringUtils.isNotBlank(item.getOperateContent())) {
                        Map map = JSON.parseObject(item.getOperateContent(), HashMap.class);
                        closeOrderRemark = map.getOrDefault("closeOrderRemark", "").toString();
                        if (StringUtils.isNotBlank(closeOrderRemark)) {
                            closeOrderRemark = "【" + closeOrderRemark + "】";
                        }
                    }
                } catch (Exception e) {
                    log.error("operateContent转换成Map异常：", e);
                }
                StringBuilder sb = new StringBuilder(closeOrderRemark);
                customerOrderItems.stream().forEach(customerOrderItem -> {
                    if (StringUtils.isNotBlank(customerOrderItem.getItemCode())) {
                        String diffReason = StringUtils.isNotBlank(customerOrderItem.getDiffReason()) ? customerOrderItem.getDiffReason() : "";
                        // 关单原因，如果非空且包含_，去查一下dc，把code替换成名称
                        if (StringUtils.contains(customerOrderItem.getDiffReason(), "_") ) {
                            String ppSecReblack = dictHelper.getDictVaule("PP_SEC_REBLACK", diffReason);
                            if (StringUtils.isNotBlank(ppSecReblack)) {
                                diffReason = ppSecReblack;
                            }
                        }
                        sb.append(String.format(DIFF_REASON_FORMAT, customerOrderItem.getItemCode(),
                            diffReason));
                    }
                });
                item.setOperateContent(StrUtil.removeSuffix(sb, "；"));
            }
        });
    }


    @Override
    public void saveInformationSecurityOperation(InformationSecurityOperationVo request) {
        String index = EsOperateType.INFORMATION_SECURITY_OPERATION.getIndex() + "_" + DateUtils.getNow("yyyyMM");
        request.setCreateTime(new Date());
        request.setUpdateTime(new Date());
        IndexRequest indexRequest = new IndexRequest(index);
        indexRequest.source(JSON.toJSONString(request), XContentType.JSON);
        indexRequest.routing( EsOperateType.INFORMATION_SECURITY_OPERATION.getRouter());
        bulkProcessor().add(indexRequest);
    }
}
