package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.rule.domain.OptRuleRouters;
import com.midea.logistics.otp.rule.domain.bean.IssueRule;
import com.midea.logistics.otp.rule.domain.request.IssueRuleRequest;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: IssueRuleFeign
 * Author: luoh
 * Date: 2019-6-3 16:30:57
 * Description:下发规则 feign
 */
@FeignClient(value = "logistics-otp-rule-service",url = BaseRouter.LOGISTICS_OTP_RULE_SERVICE)
public interface IssueRuleFeign {

    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_SAVE, method = RequestMethod.POST)
    JsonResponse create(@RequestBody IssueRuleRequest issueRuleRequest);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_DETAIL, method = RequestMethod.GET)
    JsonResponse<IssueRule> queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_UPDATE, method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody IssueRule issueRule);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_DELETE, method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_LIST, method = RequestMethod.GET)
    JsonResponse<PageResponse<IssueRule>> search(@SpringQueryMap IssueRuleRequest issueRuleRequest);

    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_LIST_POST, method = RequestMethod.POST)
    JsonResponse<PageResponse<IssueRule>> searchPost(@RequestBody IssueRuleRequest issueRuleRequest);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_BATCH_CREATE_OR_UPDATE, method = RequestMethod.POST)
    JsonResponse batchCreateOrUpdate(@RequestBody List<IssueRuleRequest> issueRuleRequests);

    /**
     * 批量删除
     *
     * @return
     */
    @RequestMapping(value = OptRuleRouters.ISSUE_RULE_BATCH_DELETE_BY_BUSINESSKEY, method = RequestMethod.DELETE)
    JsonResponse batchDeleteByBusinessKey(@RequestBody List<Long> ids);

}
