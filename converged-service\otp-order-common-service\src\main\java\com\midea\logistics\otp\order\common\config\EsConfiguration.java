package com.midea.logistics.otp.order.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.elasticsearch.client.Node;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ©Copyright ©1968-2020 Midea Group,IT
 *
 * @FileName: EsConfiguration
 * @description:
 * @author: kongly1
 * @date: 2021-8-3 17:25
 */

@Slf4j
@ConditionalOnProperty(prefix = "lc.es", name = "cluster-nodes")
@ConfigurationProperties(prefix = "lc.es")
@Configuration
public class EsConfiguration {
    @Value("${lc.es.cluster-nodes}")
    private String host;

    @Value("${lc.es.connTimeout:}")
    private Integer connTimeout;

    @Value("${lc.es.socketTimeout:}")
    private Integer socketTimeout;

    @Value("${lc.es.scheme:}")
    private String scheme;

    @Value("${lc.es.connectTimeout:}")
    private Integer connectTimeout;

    @Value("${lc.es.username}")
    private String username;

    @Value("${lc.es.password}")
    private String password;

    @Value("${lc.es.thread-count:}")
    private Integer threadCount;

    /**
     * maxConnPerRoute
     */
    @Value("${lc.es.max-conn-per-route:}")
    private Integer maxConnPerRoute;
    /**
     * maxConnPerRoute
     */
    @Value("${lc.es.max-conn-total:}")
    private Integer maxConnTotal;


    private static final String COLON = ":";
    private static final String COMMA = ",";

    /**
     * Creates a new {@link HttpHost} by parsing the given source.
     *
     * @param source must not be {@literal null} or empty.
     */
    private List<HttpHost> clusterNodes(String source) {

        Assert.hasText(source, "Cluster nodes source must not be null or empty!");

        String[] nodes = StringUtils.delimitedListToStringArray(source, COMMA);

        return Arrays.stream(nodes).map(node -> {

            String[] segments = StringUtils.delimitedListToStringArray(node, COLON);

            Assert.isTrue(segments.length == 2,
                () -> String.format("Invalid cluster node %s in %s! Must be in the format host:port!", node, source));

            String host = segments[0].trim();
            String port = segments[1].trim();

            Assert.hasText(host, () -> String.format("No host name given cluster node %s!", node));
            Assert.hasText(port, () -> String.format("No port given in cluster node %s!", node));

            if (StringUtils.isEmpty(scheme)) {
                return new HttpHost(toInetAddress(host).getHostName(), Integer.parseInt(port));
            } else {
                return new HttpHost(toInetAddress(host).getHostName(), Integer.parseInt(port), scheme);
            }

        }).collect(Collectors.toList());
    }

    private static InetAddress toInetAddress(String host) {

        try {
            return InetAddress.getByName(host);
        } catch (UnknownHostException o_O) {
            throw new IllegalArgumentException(o_O);
        }
    }

    @Bean(destroyMethod = "close", name = "client")
    public RestHighLevelClient initRestClient() {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));

        List<HttpHost> clusterNodes;
        clusterNodes = clusterNodes(host);
        List<Node> nodes = clusterNodes.stream().map(Node::new).collect(Collectors.toList());
        Node[] host = new Node[]{};
        RestClientBuilder restClientBuilder = RestClient.builder(nodes.toArray(host));
        restClientBuilder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                                                          @Override
                                                          public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                                                              httpClientBuilder.disableAuthCaching();
                                                              if (Objects.nonNull(threadCount)) {
                                                                  log.info("======== setting defaultIOReactorConfig, threadCount:{}", threadCount);
                                                                  httpClientBuilder.setDefaultIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(threadCount).build());
                                                              }
                                                              if (Objects.nonNull(maxConnPerRoute)) {
                                                                  httpClientBuilder.setMaxConnPerRoute(maxConnPerRoute);
                                                              }
                                                              if (Objects.nonNull(maxConnTotal)) {
                                                                  httpClientBuilder.setMaxConnTotal(maxConnTotal);
                                                              }
                                                              return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                                                          }
                                                      }
        );
        restClientBuilder.setRequestConfigCallback(requestConfigCallback -> {
            if (Objects.nonNull(connectTimeout)) {
                log.info("======== setting connectTimeout: {}ms", connectTimeout);
                requestConfigCallback.setConnectTimeout(connectTimeout);
            }
            if (Objects.nonNull(socketTimeout)) {
                log.info("======== setting socketTimeout: {}ms", socketTimeout);
                requestConfigCallback.setSocketTimeout(socketTimeout);
            }
            return requestConfigCallback;
        });
        RestHighLevelClient client = new RestHighLevelClient(
            restClientBuilder
        );
        return client;
    }

}

