package com.midea.logistics.otplbs.bean;

import lombok.Data;
/**
 * @Title: CopOrderInfosRes
 * @Description: cop子订单信息返回类
 * @author: dongxy31
 * @date: 2024年2月20日
 * @version V1.0
 *
 */
@Data
public class CopOrderInfosRes {

    /**
     * 订单状态 （100代表订单新建状态）
     */
    private Integer orderStatus;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 父单号-IN单号
     */
    private String parentOrderNo;

    /**
     * 运单号子集
     */
    private String waybillNo;

    /**
     * 运单号是否变更
     */
    private String waybillNoChange;

}
