package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EbMaterialGroupManager;
import com.midea.logistics.domain.mdm.domain.EbMaterialGroup;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.helper.ExportExcel;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.helper.bean.ShippingTypeRuleExcelResponse;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule;
import com.midea.logistics.otp.rule.domain.request.LoadingFeeRuleRequest;
import com.midea.logistics.otp.rule.domain.request.ShippingTypeRuleRequest;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.midea.logistics.otplbs.fegin.ShippingTypeRuleFeign;
import com.midea.logistics.otplbs.manager.helper.CheckShippingTypeRuleExcel;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Slf4j
@Component
public class ShippingTypeRuleManager {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    private ShippingTypeRuleFeign shippingTypeRuleFeign;

    @Autowired
    private CdWarehouseManager cdWarehouseManager;

    @Autowired
    private EbCustomerManager ebCustomerManager;
    @Autowired
    private CheckShippingTypeRuleExcel checkShippingTypeRuleExcel;
    @Autowired
    private EbMaterialGroupManager ebMaterialGroupManager;
    @Autowired
    private ReportFeign reportFeign;
    @Autowired
    private UpDownloadHelper upDownloadHelper;

    public void excelExport(ShippingTypeRuleRequest shippingTypeRuleRequest, HttpServletResponse response) throws IOException {
        String fileName = "配送方式规则.xlsx";
        List<ShippingTypeRuleExcelResponse> list = Lists.newArrayList();
        shippingTypeRuleRequest.setPageSize(CommonConstant.PAGE_SIZE);
        JsonResponse<PageResponse<ShippingTypeRule>> search = shippingTypeRuleFeign.search(shippingTypeRuleRequest);
        if (search == null || search.data == null || CollectionUtils.isEmpty(search.data.getList())) {
            new ExportExcel(null, ShippingTypeRuleExcelResponse.class, 1).setDataList(list).write(response, fileName).dispose();
            return;
        }
        search.data.getList().forEach(
                shippingTypeRule -> {
                    ShippingTypeRuleExcelResponse shippingTypeRuleExcelResponse = new ShippingTypeRuleExcelResponse();
                    BeanUtils.copyProperties(shippingTypeRule, shippingTypeRuleExcelResponse);
                    list.add(shippingTypeRuleExcelResponse);
                }
        );
        new ExportExcel(null, ShippingTypeRuleExcelResponse.class,2).setDataList(list).write(response, fileName).dispose();
    }

    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) throws Exception{
        log.info("配送方式导入开始============================");
        ImportExcel ei = new ImportExcel(file, 1, 0,applicationContext);
        int lastCellNum = ei.getLastCellNum();
        if (lastCellNum != 18){
            throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
        }
        List<ShippingTypeRuleExcelResponse> list = ei.getDataList(ShippingTypeRuleExcelResponse.class, null);
        List<ShippingTypeRuleRequest> shippingTypeRules = Lists.newArrayList();
        List<String> fail = Lists.newArrayList();
        final int[] row = {1};
        list.stream().forEach(shippingTypeRuleRequest -> {
            try{
                row[0] ++;
                checkShippingTypeRuleExcel.check(shippingTypeRuleRequest);
                //重新赋值
                ShippingTypeRuleRequest shippingTypeRule = new ShippingTypeRuleRequest();
                BeanUtils.copyProperties(shippingTypeRuleRequest, shippingTypeRule);
                //获取品类名称
                if (ToolUtils.isNotEmpty(shippingTypeRule.getItemClass())) {
                    EbMaterialGroup ebMaterialGroup = ebMaterialGroupManager.getEbMaterialGroupCache(shippingTypeRule.getItemClass());
                    if (ebMaterialGroup != null) {
                        shippingTypeRule.setItemClassName(ebMaterialGroup.getEbmgNameCn());
                    }
                }
                JsonResponse response = shippingTypeRuleFeign.batchCreateOrUpdate(Lists.newArrayList(shippingTypeRule));
                if (null == response){
                    throw BusinessException.fail("保存失败");
                }
                if(!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())){
                    throw BusinessException.fail(response.getMsg());
                }
                shippingTypeRules.add(shippingTypeRule);
            } catch (BusinessException ex){
                fail.add("第"+ row[0] +"行数据异常,"+ex.getMessage());
                log.error(ex.getMessage(), ex);
                return;
            }catch (Exception ep){
                fail.add("第"+ row[0] +"行数据异常,");
                log.error(ep.getMessage() ,ep);
                return;
            } finally {
                log.info("配送方式导入======第"+ row[0] +"行============导入中==========");
            }
        });
        log.info("配送方式导入结束============================");
        JsonResponse jsonResponse = JsonResponse.success(shippingTypeRules);
        if (ToolUtils.isNotEmpty(fail)){
            jsonResponse.setMsg(JSON.toJSONString(fail));
            throw BusinessException.fail(fail.toString());
        }
        return jsonResponse;
    }

    public JsonResponse importShippingTypeRule(@RequestParam("file") MultipartFile file) {
        ImportFileRequest request = upDownloadHelper.uploadFileToOss(file);
        JsonResponse jsonResponse = reportFeign.importShippingTypeRule(request);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            throw BusinessException.fail(jsonResponse.getMsg());
        }
        return jsonResponse;
    }

}


