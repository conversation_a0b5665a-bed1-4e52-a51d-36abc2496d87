package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;

/**
* @description: 入安得仓时，校验是否爆仓：validFlag  存储，是否需要在order_info表中增加字段
* @author: 陈永培
* @createtime: 2020/11/13 15:19
*/
public interface IOutAreaService {

    /**
     * @description: 爆仓
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 刘院民
     * @createtime: 2023/08/14 17:45
     */
    CustomerOrderInfoExt isOutArea(CustomerOrderInfoExt customerOrderInfoExt);

    /** 
    * @description: 爆仓
    * @param: [orderInfo] 
    * @return: com.mideaframework.core.web.JsonResponse 
    * @author: 陈永培
    * @createtime: 2020/11/17 17:51
    */ 
    OrderInfoExt isOutArea(OrderInfoExt orderInfo);
}
