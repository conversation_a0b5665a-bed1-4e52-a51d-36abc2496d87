package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EbPlace;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbCustomerFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EsCompanyFeign;
import com.midea.logistics.otp.common.helper.ImportExcelHelper;
import com.midea.logistics.otp.common.helper.bean.TmsMessageConfigExcelRequest;
import com.midea.logistics.otp.enums.TmsMessageStatus;
import com.midea.logistics.otplbs.fegin.TmsMessageConfigFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TmsMessageConfigManager {

    @Autowired
    private TmsMessageConfigFeign tmsMessageConfigFeign;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private EbCustomerFeign ebCustomerFeign;

    @Autowired
    private EsCompanyFeign esCompanyFeign;

    private static final Logger logger = LoggerFactory.getLogger(TmsMessageConfigManager.class);

    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) throws Exception {
        logger.info("短信配置导入开始============================");
        //可扫描excel中间带空行的导入工具类
        ImportExcelHelper ei = new ImportExcelHelper(file, 1, 0, applicationContext);
        int lastCellNum = ei.getLastCellNum();
        if (lastCellNum != 8) {
            return JsonResponse.fail("当前使用导入模板版本错误,请重新下载最新版本的模板");
        }
        List<TmsMessageConfigExcelRequest> tmsMessageConfigs = ei.getDataList(TmsMessageConfigExcelRequest.class, null);
        if (CollectionUtils.isEmpty(tmsMessageConfigs)) {
            return JsonResponse.fail("导入的模板不能为空");
        }

        List<TmsMessageConfigExcelRequest> requestList = Lists.newArrayList();
        Map<Integer, String> errorMap = Maps.newTreeMap();

        //是否必填项存在
        List<TmsMessageConfigExcelRequest> firstVerify = tmsMessageConfigs.stream().filter(messageConfig ->
            StringUtils.isEmpty(messageConfig.getCustomerName())
                || StringUtils.isEmpty(messageConfig.getSiteName())
                || StringUtils.isEmpty(messageConfig.getMessageStatus())
                || StringUtils.isEmpty(messageConfig.getSendMessageFlag())
                || StringUtils.isEmpty(messageConfig.getSendTencentFlag())
                || StringUtils.isEmpty(messageConfig.getSendSenderFlag())
                || StringUtils.isEmpty(messageConfig.getSendReceiverFlag())
                || StringUtils.isEmpty(messageConfig.getAddDriverFlag())
        ).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(firstVerify)) {
            StringBuilder sb = new StringBuilder();
            sb.append("当前新增记录，第");
            for (TmsMessageConfigExcelRequest exRequest : firstVerify) {
                sb.append(exRequest.getRow() + "行，");
            }
            sb.append("必填项为空，导入失败");
            errorMap.put(1, sb.toString());
            throw BusinessException.fail(JSON.toJSONString(errorMap.values()));
        }

        //校验值是否合规
        tmsMessageConfigs.forEach(data -> {
            Integer row = data.getRow();
            check(row, errorMap, data, requestList);
        });

        if (errorMap.size() != 0) {
            errorMap.put(0, "当前新增记录存在值不合规，导入失败");
            return JsonResponse.fail(JSON.toJSONString(errorMap.values()));
        }

        JsonResponse importResponse = new JsonResponse();
        if (!CollectionUtils.isEmpty(requestList)) {
            importResponse = tmsMessageConfigFeign.importBatchInsert(requestList);
            if (Objects.equals(importResponse.getCode(), BaseCodeEnum.FAILED.getCode())) {
                Map<Integer, String> repeatMap = JSON.parseObject(importResponse.getMsg(), new TypeReference<Map<Integer, String>>() {
                });
                errorMap.putAll(repeatMap);
                throw BusinessException.fail(JSON.toJSONString(errorMap.values()));
            }
        }

        logger.info("短信配置导入结束============================");
        return JsonResponse.success(Optional.ofNullable(importResponse.getData()).orElse(null));
    }

    private void check(Integer row, Map<Integer, String> errorMap, TmsMessageConfigExcelRequest data, List<TmsMessageConfigExcelRequest> requestList) {

        StringBuilder stringBuilder = new StringBuilder();
        try {
            //短信配置合法性校验
            String customerName = data.getCustomerName();
            if (!StringUtils.isBlank(customerName)) {
                EbCustomer ebCustomer = new EbCustomer();
                ebCustomer.setEbcuNameCn(customerName);
                JsonResponse<PageResponse<EbCustomer>> jsonResponse = ebCustomerFeign.searchByEbCustomer(ebCustomer);
                List<EbCustomer> ebCustomers = jsonResponse.getData().list;
                if (CollectionUtils.isEmpty(ebCustomers)) {
                    stringBuilder.append("客户名称不存在。");
                } else {
                    data.setCustomerCode(ebCustomers.get(0).getPmCode());
                }
            }

            String siteName = data.getSiteName();
            if (!StringUtils.isBlank(siteName)) {
                JsonResponse<PageResponse<EsCompany>> jsonResponse = esCompanyFeign.searchByCodeAndNames(siteName);
                List<EsCompany> esCompanies = jsonResponse.getData().list;
                if (CollectionUtils.isEmpty(esCompanies)) {
                    stringBuilder.append("服务平台名称不存在。");
                } else {
                    data.setSiteCode(esCompanies.get(0).getSiteCode());
                }
            }

            String messageStatus = data.getMessageStatus();
            if (!StringUtils.isBlank(messageStatus) && !Objects.equals(messageStatus, TmsMessageStatus.DISPATCHED.getValue()) && !Objects.equals(messageStatus, TmsMessageStatus.SIGNED.getValue())) {
                stringBuilder.append("节点[数据异常,值只能为:'在途'或者'已签收'] ,请检查。");
            }

            String sendMessageFlag = data.getSendMessageFlag();
            if (isComplianceHelper(sendMessageFlag)) {
                stringBuilder.append("是否发送信息[数据异常,值只能为:'是'或者'否'] ,请检查。");
            } else {
                data.setSendMessageFlag("是".equals(data.getSendMessageFlag()) ? "Y" : "N");
            }

            String sendTencentFlag = data.getSendTencentFlag();
            if (isComplianceHelper(sendTencentFlag)) {
                stringBuilder.append("是否发送公众号[数据异常,值只能为:'是'或者'否'] ,请检查。");
            } else {
                data.setSendTencentFlag("是".equals(data.getSendTencentFlag()) ? "Y" : "N");
            }

            String sendSenderFlag = data.getSendSenderFlag();
            if (isComplianceHelper(sendSenderFlag)) {
                stringBuilder.append("是否发送发货人[数据异常,值只能为:'是'或者'否'] ,请检查。");
            } else {
                data.setSendSenderFlag("是".equals(data.getSendSenderFlag()) ? "Y" : "N");
            }

            String sendReceiverFlag = data.getSendReceiverFlag();
            if (isComplianceHelper(sendReceiverFlag)) {
                stringBuilder.append("是否发送收货人[数据异常,值只能为:'是'或者'否'] ,请检查。");
            } else {
                data.setSendReceiverFlag("是".equals(data.getSendReceiverFlag()) ? "Y" : "N");
            }

            String addDriverFlag = data.getAddDriverFlag();
            if (isComplianceHelper(addDriverFlag)) {
                stringBuilder.append("是否加司机信息[数据异常,值只能为:'是'或者'否'] ,请检查。");
            } else {
                data.setAddDriverFlag("是".equals(data.getAddDriverFlag()) ? "Y" : "N");
            }

            if (!StringUtils.isEmpty(stringBuilder.toString())) {
                stringBuilder.insert(0, "在第" + row + "行，");
                throw BusinessException.fail(stringBuilder.toString());
            }
            requestList.add(data);
        } catch (BusinessException ex) {
            errorMap.put(data.getRow(), ex.getMessage());
        } catch (Exception ep) {
            throw BusinessException.fail(ep.getMessage());
        }
    }

    private boolean isComplianceHelper(String field) {
        return !StringUtils.isBlank(field) && !Objects.equals(field, "是") && !Objects.equals(field, "否");
    }
}
