package com.midea.logistics.otp.task.mapper.common;

import com.midea.logistics.otp.task.domain.bean.WarehouseCloseRecord;
import com.midea.logistics.otp.task.domain.request.WarehouseCloseRecordReq;
import com.mideaframework.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ©Copyright ©1968-2019 Midea Group,IT
 * FileName: WarehouseCloseRecordMapper
 * Author: dongxy31
 * Date: 2024-6-11 10:44:53
 * Description:WarehouseCloseRecordMapper服务接口
 */
@Mapper
public interface WarehouseCloseRecordMapper extends BaseMapper<WarehouseCloseRecord> {


    List<WarehouseCloseRecord> queryListByUpdate(WarehouseCloseRecordReq warehouseCloseRecord);

    List<WarehouseCloseRecord> queryList(WarehouseCloseRecordReq warehouseCloseRecord);

}