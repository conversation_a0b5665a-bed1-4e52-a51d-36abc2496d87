package com.midea.logistics.otp.orderverify.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.midea.logistics.otp.common.feign.servicefeign.order.ConvergedOrderFeign;
import com.midea.logistics.otp.enums.OrderOperateType;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.ServiceProductsHelper;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.orderverify.aop.ExpressFlow;
import com.midea.logistics.otp.orderverify.service.ServiceProductService;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;

import lombok.extern.slf4j.Slf4j;
import static com.midea.logistics.otp.order.common.helper.ServiceProductsHelper.SERVICE_PRODUCT_ERROR;

@Service
@Slf4j
public class ServiceProductExplainServiceImpl implements ServiceProductService {

    @Autowired
    private ConvergedOrderFeign convergedOrderFeign;

    @Override
    @ExpressFlow(node = OrderOperateType.SUB_SERVICE_PRODUCT)
    public FlowListenerParam explain(OrderInfoExt orderInfo) {
        JsonResponse<String> jsonResponse = convergedOrderFeign.explainServiceProduct(orderInfo);
        String code = jsonResponse.getCode();
        if (!BaseCodeEnum.SUCCESS.getCode().equals(code)) {
            throw BusinessException.fail(SERVICE_PRODUCT_ERROR);
        }
        return FlowListenerParam.success(jsonResponse.data);
    }
}
