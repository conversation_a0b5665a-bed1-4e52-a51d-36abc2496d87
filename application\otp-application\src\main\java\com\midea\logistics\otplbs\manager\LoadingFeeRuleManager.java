package com.midea.logistics.otplbs.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.domain.mdm.request.CdWarehouseRequest;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.CdWarehouseFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbCustomerFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EsCompanyFeign;
import com.midea.logistics.otp.common.helper.ImportExcel;
import com.midea.logistics.otp.common.helper.UpDownloadHelper;
import com.midea.logistics.otp.common.helper.bean.LoadingFeeRuleExcelRequest;
import com.midea.logistics.otp.common.request.ImportFileRequest;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.FeeType;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.rule.domain.request.LoadingFeeRuleRequest;
import com.midea.logistics.otplbs.fegin.LoadingFeeRuleFeign;
import com.midea.logistics.otplbs.fegin.ReportFeign;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


@Slf4j
@Component
public class LoadingFeeRuleManager {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    private LoadingFeeRuleFeign loadingFeeRuleFeign;

    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private CdWarehouseFeign cdWarehouseFeign;
    @Autowired
    private EsCompanyFeign esCompanyFeign;
    @Autowired
    private EbCustomerFeign ebCustomerFeign;
    @Autowired
    private EbCustomerManager ebCustomerManager;

    @Autowired
    private EsCompanyManager esCompanyManager;
    @Autowired
    private ReportFeign reportFeign;
    @Autowired
    private UpDownloadHelper upDownloadHelper;

    public JsonResponse excelImport(@RequestParam("file") MultipartFile file) throws Exception{
        log.info("装卸费计费规则导入开始============================");
        ImportExcel ei = new ImportExcel(file, 1, 0,applicationContext);
        int lastCellNum = ei.getLastCellNum();
        if (lastCellNum != 11){
            throw BusinessException.fail("当前使用导入模板版本错误,请重新下载最新版本的模板.");
        }
        List<LoadingFeeRuleExcelRequest> list = ei.getDataList(LoadingFeeRuleExcelRequest.class, null);
        List<LoadingFeeRuleRequest> loadingFeeRules = Lists.newArrayList();
        List<String> fail = Lists.newArrayList();
        final int[] row = {1};
        list.stream().forEach(loadingFeeRuleRequest -> {
            try{
                row[0] ++;
                check(loadingFeeRuleRequest);
                //重新赋值
                LoadingFeeRuleRequest loadingFeeRule = new LoadingFeeRuleRequest();
                BeanUtils.copyProperties(loadingFeeRuleRequest, loadingFeeRule);
                JsonResponse response = loadingFeeRuleFeign.batchCreateOrUpdate(Lists.newArrayList(loadingFeeRule));
                if (null == response){
                    throw BusinessException.fail("保存失败");
                }
                if(!BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())){
                    throw BusinessException.fail(response.getMsg());
                }
                loadingFeeRules.add(loadingFeeRule);
            } catch (BusinessException ex){
                fail.add("第"+ row[0] +"行数据异常,"+ex.getMessage());
                log.error(ex.getMessage(), ex);
                return;
            }catch (Exception ep){
                fail.add("第"+ row[0] +"行数据异常,");
                log.error(ep.getMessage() ,ep);
                return;
            } finally {
                log.info("装卸费计费规则导入======第"+ row[0] +"行============导入中==========");
            }
        });
        log.info("装卸费计费规则导入结束============================");
        JsonResponse jsonResponse = JsonResponse.success(loadingFeeRules);
        if (ToolUtils.isNotEmpty(fail)){
        jsonResponse.setMsg(JSON.toJSONString(fail));
        throw BusinessException.fail(fail.toString());
        }
        return jsonResponse;
    }

    public void check(LoadingFeeRuleExcelRequest request) {

        //装卸计费标识导入时，配送方式要能识别汉字、英文，都按字典解析
        String deliveryType = request.getDeliveryType();
        if (ToolUtils.isNotEmpty(deliveryType)){
            String key = DeliveryType.getType(deliveryType);
            if (ToolUtils.isNotEmpty(key)){
                request.setDeliveryType(key);
            }
        }
        //*客户名称
        if (ToolUtils.isEmpty(request.getCustomerName())){
            throw BusinessException.fail("客户不能为空");
        }
        String customerName = request.getCustomerName();
        String customerCode = queryCustomerName(customerName);
        if (ToolUtils.isEmpty(customerCode)){
            throw BusinessException.fail("找不到客户");
        }
        request.setCustomerCode(customerCode);
        // *订单类型
        if (ToolUtils.isEmpty(request.getOrderTypeName())){
            throw BusinessException.fail("订单类型不能为空");
        }
        Optional<OrderType> first = Lists.newArrayList(OrderType.values()).stream().filter(o -> o.getValue().equals(request.getOrderTypeName())).findFirst();
        if (first.isPresent()){
            request.setOrderType(first.get().getKey());
        }else {
            throw BusinessException.fail("找不到订单类型");
        }
        // *服务平台
        String siteName = request.getSiteName();
        String siteCode = request.getSiteCode();
        if (StringUtils.isEmpty(siteCode)){
            String siteCode1 = querySiteCode(siteName);
            request.setSiteCode(siteCode1);
            if (ToolUtils.isEmpty(siteCode1)){
                throw BusinessException.fail("找不到平台");
            }

        }else{
            String esCompanyCache = esCompanyManager.getEsCompanyNameBySiteCode(siteCode);
            request.setSiteName(esCompanyCache);
            if (ToolUtils.isEmpty(esCompanyCache)){
                throw BusinessException.fail("找不到平台");
            }
        }

        // *仓库名称
        if (ToolUtils.isEmpty(request.getWhName()+request.getWhCode())){
            throw BusinessException.fail("仓库不能为空");
        }
        //装卸计费标识页面优化导入仓库解析逻辑:填写了仓库名称则解析仓库名称,未填写则按仓库编码解析
        String whName ="";
        String whCode = "";
        if (ToolUtils.isNotEmpty(request.getWhName())){

            whName = request.getWhName();
            whCode = queryWhCode(whName);
            if (ToolUtils.isEmpty(whCode)){
                throw BusinessException.fail("找不到仓库");
            }
            request.setWhCode(whCode);
        }else{
            whCode = request.getWhCode();
            whName = cdWarehouseManager.getCdWarehouseNameByWhCode(whCode);
            if (ToolUtils.isEmpty(whName)){
                throw BusinessException.fail("找不到仓库");
            }
            request.setWhName(whName);
        }


        // *计费标识
        if (ToolUtils.isEmpty(request.getFeeType())){
            throw BusinessException.fail("计费标识不能为空");
        }
        Optional<FeeType> firstFeeType = Lists.newArrayList(FeeType.values()).stream().filter(o -> o.getValue().equals(request.getFeeType())).findFirst();
        if (firstFeeType.isPresent()){
            request.setFeeType(firstFeeType.get().getKey());
        } else {
            throw BusinessException.fail("计费标识请输入:"+ Arrays.toString(FeeType.values()));
        }
        //FeeType.
        //③	自提字段:是=Y,否=N;
        String pickFlag = request.getPickFlag();
        if (CommonConstant.YES.equals(pickFlag)){
            request.setPickFlag( CommonConstant.STRING_FLAG_YES);
        }
        if (CommonConstant.NO.equals(pickFlag)){
            request.setPickFlag(CommonConstant.STRING_FLAG_NO);
        }

    }

    private String queryWhCode(String whName) {
        if(ToolUtils.isEmpty(whName)) {
            return null;
        }
        CdWarehouseRequest param = new CdWarehouseRequest();
        //cdwhIsStop=0
        param.setCdwhIsStop(0d);
        param.setCdwhName(whName);
        JsonResponse<PageResponse<CdWarehouse>> jsonResponse = cdWarehouseFeign.search(param);
        log.info("cdWarehouseFeign.search==={}",jsonResponse.toString());
        List<CdWarehouse> list = jsonResponse.data.list;
        CdWarehouse base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getWhCode();
    }

    private String querySiteCode(String siteName) {
        if(ToolUtils.isEmpty(siteName)) {
            return null;
        }
        JsonResponse<PageResponse<EsCompany>> jsonResponse = esCompanyFeign.searchByCodeAndNames(siteName);
        log.info("esCompanyFeign.searchByCodeAndNames==={}",jsonResponse.toString());
        List<EsCompany> list = jsonResponse.data.list;
        EsCompany base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getSiteCode();
    }
    private String queryCustomerName(String customerName) {
        if(ToolUtils.isEmpty(customerName)) {
            return null;
        }
        EbCustomer ebCustomer = new EbCustomer();
        ebCustomer.setEbcuNameCn(customerName);
        ebCustomer.setEbcuCustomerStatus("1");
        JsonResponse<PageResponse<EbCustomer>> jsonResponse = ebCustomerFeign.searchByEbCustomer(ebCustomer);
        log.info("ebCustomerFeign.searchByEbCustomer==={}",jsonResponse.toString());
        List<EbCustomer> list = jsonResponse.data.list;
        EbCustomer base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getPmCode();
    }


    public JsonResponse importLoadingFeeRule(@RequestParam("file") MultipartFile file) {
        ImportFileRequest request = upDownloadHelper.uploadFileToOss(file);
        JsonResponse jsonResponse = reportFeign.importLoadingFeeRule(request);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jsonResponse.getCode())) {
            throw BusinessException.fail(jsonResponse.getMsg());
        }
        return jsonResponse;
    }

}


