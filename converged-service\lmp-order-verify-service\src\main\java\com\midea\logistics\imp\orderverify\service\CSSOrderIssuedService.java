package com.midea.logistics.imp.orderverify.service;

import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;

/**
* @description: 下发售后
* @author: 陈永培
* @createtime: 2020/11/14 15:17
*/
public interface CSSOrderIssuedService {

    /**
    * @description: 下发售后
    * @param: [orderInfo]
    * @return: com.mideaframework.core.web.JsonResponse
    * @author: 陈永培
    * @createtime: 2020/11/14 15:17
    */
    OrderInfoExt orderIssued(OrderInfoExt orderInfo);

}
