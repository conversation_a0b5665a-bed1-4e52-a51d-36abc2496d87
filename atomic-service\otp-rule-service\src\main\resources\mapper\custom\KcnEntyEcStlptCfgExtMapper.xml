<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.KcnEntyEcStlptCfgMapper">

    <update id="batchUpdateCanSetEmpty">
        <foreach collection="list" item="item" separator=";">
            UPDATE kcn_enty_ec_stlpt_cfg
            <include refid="setBatchFieldsSqlCanSetEmpty"/>
            where
            `id` =
            #{item.id}
        </foreach>
    </update>

    <sql id="setBatchFieldsSqlCanSetEmpty">
        <set>
            `version` = `version` + 1 ,
            <if test="item.fxShopId !=null">
                `fx_shop_id` = #{item.fxShopId},
            </if>
            <if test="item.buyerMerchantName !=null">
                `buyer_merchant_name` = #{item.buyerMerchantName},
            </if>
            <if test="item.updateUserCode != null">
                `update_user_code` = #{item.updateUserCode},
            </if>
            <if test="item.shopName !=null">
                `shop_name` = #{item.shopName},
            </if>
            <if test="item.remark != null">
                `remark` = #{item.remark},
            </if>
            <if test="item.fxShopName !=null">
                `fx_shop_name` = #{item.fxShopName},
            </if>
            <if test="item.shopFinance !=null">
                `shop_finance` = #{item.shopFinance},
            </if>
            <if test="item.settlePartment !=null">
                `settle_partment` = #{item.settlePartment},
            </if>
            <if test="item.buyerMerchantCode !=null">
                `buyer_merchant_code` = #{item.buyerMerchantCode},
            </if>
            <if test="item.shopId !=null">
                `shop_id` = #{item.shopId},
            </if>
            <if test="item.settleShopName !=null">
                `settle_shop_name` = #{item.settleShopName},
            </if>
        </set>
    </sql>

</mapper>