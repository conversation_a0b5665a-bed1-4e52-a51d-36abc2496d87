package com.midea.logistics.imp.orderverify.helper;

import java.util.List;

import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.ControlParamFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.*;
import com.midea.logistics.otp.common.helper.RedisHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 订单帮助类
 * @author: 陈永培
 * @createtime: 2020/11/14 9:56
 */
@Component
@Slf4j
public class OrderHelper {

    @Autowired
    private CustomerOrderInfoFeign customerOrderInfoFeign;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;
    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private OrderTraceFeign orderTraceFeign;
    @Autowired
    private OrderConfirmFeign orderConfirmFeign;
    @Autowired
    private OrderConfirmItemFeign orderConfirmItemFeign;
    @Autowired
    RedisHelper redisHelper;
    @Autowired
    private ControlParamFeign controlParamFeign;
    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;


    /**
     * 获取 customer_order_info
     *
     * @param orderNo
     * @return
     */
    public CustomerOrderInfo getCustomerOrderInfo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return null;
        }
        CustomerOrderInfo condition = new CustomerOrderInfo();
        condition.setOrderNo(orderNo);
        JsonResponse<CustomerOrderInfo> jr = customerOrderInfoFeign.searchOne(condition);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jr.getCode())) {
            log.error("customerOrderInfoFeign 调用出错 {}", jr.getMsg());
            return null;
        }

        return jr.getData();
    }

    /**
     * 获取 customer_order_info_extend
     *
     * @param orderNo
     * @return
     */
    public CustomerOrderInfoExtend getCustomerOrderInfoExtend(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return null;
        }
        CustomerOrderInfoExtend condition = new CustomerOrderInfoExtend();
        condition.setOrderNo(orderNo);
        JsonResponse<CustomerOrderInfoExtend> jr = customerOrderInfoExtendFeign.selectOne(condition);
        if (!BaseCodeEnum.SUCCESS.getCode().equals(jr.getCode())) {
            log.error("customerOrderInfoExtendFeign 调用出错 {}", jr.getMsg());
            throw BusinessException.fail("查询订单扩展表失败！" + jr.getMsg());
        }

        return jr.getData();
    }

    /**
     * 获取父单地址
     * @param parentOrderNo
     * @return
     */
    public CustomerOrderAddress getCustomerAddress(String parentOrderNo) {
        if (StringUtils.isEmpty(parentOrderNo)) {
            return null;
        }
        CustomerOrderAddress customerOrderAddress = customerOrderAddressFeign.findByOrderNo(parentOrderNo).data();
        return customerOrderAddress;
    }

    /**
     * @description: 是否更新成功，成功返回空字符串
     * @param: [task]
     * @return: java.lang.String
     * @author: 陈永培
     * @createtime: 2020/7/7 10:16
     */
    public String updateCustomerAddress(CustomerOrderAddress customerOrderAddress) {
        JsonResponse<Integer> jr = customerOrderAddressFeign.update(customerOrderAddress.getId(), customerOrderAddress);
        return  CommonConstant.isUpdateSuccess(jr,"父单地址");
    }


    /**
     * @description: 更新父单
     * @param: [customerOrderInfo, msg]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/11/14 13:53
     */
    public void updateCustomerOrderInfo(CustomerOrderInfo customerOrderInfo, String msg) {
        if (ToolUtils.isEmpty(customerOrderInfo)) {
            return;
        }
        JsonResponse<Integer> update = customerOrderInfoFeign.update(customerOrderInfo.getId(), customerOrderInfo);
        if (update == null) {
            throw BusinessException.fail(msg + ",更新订单失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail(msg + ",更新订单失败：" + update.getMsg());
        }
    }


    /**
     * @description: 更新子弹
     * @param: [orderInfo, msg]
     * @return: void
     * @author: 陈永培
     * @createtime: 2020/11/14 13:53
     */
    public void updateOrderInfo(OrderInfo orderInfo, String msg) {
        if (ToolUtils.isEmpty(orderInfo)) {
            return;
        }
        JsonResponse update = orderInfoFeign.update(orderInfo.getId(), orderInfo);
        if (update == null) {
            throw BusinessException.fail(msg + ",更新订单失败");
        }
        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail(msg + ",更新订单失败：" + update.getMsg());
        }
    }


    public void updateCustomerOrderAddress(CustomerOrderAddress customerOrderAddress) {
        if (ToolUtils.isEmpty(customerOrderAddress)) {
            return;
        }

        JsonResponse update = customerOrderAddressFeign.update(customerOrderAddress.getId(), customerOrderAddress);

        if (update == null) {
            throw BusinessException.fail("更新订单地址失败");
        }

        if (!BaseCodeEnum.SUCCESS.getCode().equals(update.getCode())) {
            throw BusinessException.fail("更新订单地址失败：" + update.getMsg());
        }

        Integer i = (Integer) update.getData();
        if (i <= 0) {
            throw BusinessException.fail("更新订单地址失败：" + update.getMsg());
        }

    }
}
