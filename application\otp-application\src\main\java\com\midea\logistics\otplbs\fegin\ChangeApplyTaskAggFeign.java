package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.bean.ChangeApplyLog;
import com.midea.logistics.otp.bean.msgcenter.AbandonDto;
import com.midea.logistics.otp.order.converged.domain.request.ChangeOrderSearchRequest;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.ChangeApply;
import com.midea.logistics.otp.order.domain.bean.ChangeDetail;
import com.midea.logistics.otp.order.domain.bean.custom.ChangeApplyDTO;
import com.midea.logistics.otp.order.domain.bean.custom.OrderBatchRetryPushRequest;
import com.midea.logistics.otp.order.domain.bean.custom.OrderBatchSearchRequest;
import com.midea.logistics.otp.order.domain.response.ChangeOrderListResponse;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ChangeApplyFeign
 * Author: shengzc
 * Date: 2023-2-25 15:53:53
 * Description:更改申请表 feign
 */
@FeignClient(value = "logistics-task-agg-service")
public interface ChangeApplyTaskAggFeign {

    /**
     * !@订单变更 - 查询变更单数据
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.CHANGE_APPLY_BATCH_SEARCH, method = RequestMethod.POST)
    JsonResponse<Object> batchSearchOrder(@RequestBody OrderBatchSearchRequest searchRequest);


    /**
     !@订单变更 - 创建变更单流程流程 Feign
     * @Author: dumg
     * @Date: 2023/3/4
     */
    @PostMapping("/createChangeApplyProcess")
    public JsonResponse createChangeOrderProcess(@RequestBody String reqCreateDraftDto);


    /**
     * 变更单重推
     * 1.重推调用W/T接口，修改W/T数据，响应成功修改变更单状态
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.CHANGE_APPLY_RETRY_PUSH, method = RequestMethod.POST)
    JsonResponse<Object> batchRetryPush(@RequestBody OrderBatchRetryPushRequest pushRequest);

    /**
     * !@订单变更 - 查询ES申请单日志
     *
     * @author: 陈永培
     * @createtime: 2023/3/4 10:44
     */
    @RequestMapping(value = OrderRouters.CHANGE_APPLY_LOGS, method = RequestMethod.POST)
    JsonResponse<List<ChangeApplyLog>> searchChangeApplyLogs(@RequestBody ChangeApplyLog changeApplyLog);

    /**
     * @Description: 申请单直接完成
     * @Author: shengzc
     * @Date: 2023/3/17
     */
    @RequestMapping(value = OrderRouters.CHANGE_APPLY_FINISHED, method = RequestMethod.POST)
    JsonResponse<Object> finished(@Valid @RequestBody ChangeApply changeApply);

    @PostMapping(value = OrderRouters.CHANGE_APPLY_ABANDON)
    public JsonResponse changeApplyAbandon(@RequestBody AbandonDto abandonDto);

    /**
     * 批量查询明细
     *
     * @param
     * @return
     */
    @RequestMapping(value = OrderRouters.CHANGE_BATCH_DETAIL_SEARCH_LIST, method = RequestMethod.POST)
    JsonResponse<PageResponse<ChangeOrderListResponse>> batchSearchList(@RequestBody ChangeOrderSearchRequest searchRequest);

    /**
     * !@订单变更 - 根据applyNo查询详情申请单详情，包含关联的变更单明细
     *
     * @return
     */
    @RequestMapping(value = OrderRouters.CHANGE_APPLY_INFO, method = RequestMethod.POST)
    JsonResponse<ChangeApplyDTO> info(@RequestBody ChangeDetail changeDetail);

    /**
     * 保存或修改变更单
     * @param changeApply
     * @return
     */
    @RequestMapping(value = OrderRouters.SAVE_OR_UPDATE_CHANGE_APPLY, method = RequestMethod.POST)
    JsonResponse<ChangeApply> saveOrUpdateChangeApply(@Valid @RequestBody ChangeApply changeApply);
}

