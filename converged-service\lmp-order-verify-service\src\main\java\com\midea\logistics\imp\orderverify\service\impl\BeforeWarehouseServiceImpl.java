package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.IBeforeWarehouseService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.dc.DcOtpConvergedFeign;
import com.midea.logistics.otp.common.request.WarehouseNetPlanExt;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.order.common.fegin.OrderInfoFeign;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderAddress;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.web.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @author: zhouhl
 * @date: 2022-1-7 9:36
 * @Description:
 */

@Service
@Slf4j
public class BeforeWarehouseServiceImpl implements IBeforeWarehouseService {
    //前置仓平台字典
    private static final String PREPOSE_WAREHOUSE_SITE = "PREPOSE_WAREHOUSE_SITE";

    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private OrderInfoFeign orderInfoFeign;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private DcOtpConvergedFeign dcOtpConvergedFeign;


    /**
     * 前置仓覆盖区域打标
     *
     * @param orderInfo
     */
    @Override
    public void setQzWarehAreaFlag(OrderInfoExt orderInfo) {
        orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(orderInfo.getOrderNo());
        log.info("前置仓覆盖区域打标:{}", JSON.toJSONString(orderInfo));
        if (null == orderInfo || StringUtils.isEmpty(orderInfo.getParentOrderNo()) ||
            StringUtils.isEmpty(orderInfo.getSiteCode()) || StringUtils.isEmpty(orderInfo.getWhCode())){
            return;
        }
        String siteCode = orderInfo.getSiteCode();
        String whCode = orderInfo.getWhCode();
        String parentOrderNo = orderInfo.getParentOrderNo();

        //判断是否是前置仓 不是不打标
        String preposeWarehouseSite = dictHelper.getDictVaule(PREPOSE_WAREHOUSE_SITE, siteCode);
        if (StringUtils.isEmpty(preposeWarehouseSite)){
            return;
        }
        //前置仓 && 配送方式 网点直配|宅配 && 订单类型 销售出库|调拨出库|上门取件 打标
        DeliveryType deliveryType = DeliveryType.getDeliveryType(orderInfo.getDeliveryType());
        String orderType = orderInfo.getOrderType();
        List<DeliveryType> deliveryTypes = Arrays.asList(DeliveryType.NET_MATCHING, DeliveryType.DOT);
        List<String> orderTypes = Arrays.asList(OrderType.PO.getKey(), OrderType.AO.getKey(), OrderType.DP.getKey());
        if (deliveryTypes.contains(deliveryType) && orderTypes.contains(orderType)){
            OrderInfo updateDTO = new OrderInfo();
            //三,四级地址
            CustomerOrderAddress address = orderFlowHelper.getCustomerOrderAddress(parentOrderNo);
            if(null == address){
                log.info("前置仓覆盖区域打标地址查询失败:{}",parentOrderNo);
                updateDTO.setQzWarehAreaFlag(CommonConstant.PREPOSE_WAREHOUSE_ERROR);
                orderInfo.setQzWarehAreaFlag(CommonConstant.PREPOSE_WAREHOUSE_ERROR);
                orderInfoFeign.update(orderInfo.getId(),updateDTO);
                return;
            }
            String receiverDistrictCode = address.getReceiverDistrictCode();
            String receiverTownCode = address.getReceiverTownCode();
            String provinceCode = address.getReceiverProvinceCode();
            String cityCode = address.getReceiverCityCode();

            //调DC接口查询仓网规划区域标志
            WarehouseNetPlanExt warehouseNetPlan = new WarehouseNetPlanExt();
            warehouseNetPlan.setSiteCode(siteCode);
            warehouseNetPlan.setWhCode(whCode);
            warehouseNetPlan.setProvinceCode(provinceCode);
            warehouseNetPlan.setCityCode(cityCode);
//            warehouseNetPlan.setCountyCode(receiverDistrictCode);
//            warehouseNetPlan.setTownCode(receiverTownCode);
            warehouseNetPlan.setDistrictCode(receiverDistrictCode);
            warehouseNetPlan.setStreetCode(receiverTownCode);
            warehouseNetPlan.setTenantCode("annto");
            JsonResponse<String> flagResponse = dcOtpConvergedFeign.queryBySiteCodeAndPlaces(warehouseNetPlan);
            if (null == flagResponse || !BaseCodeEnum.SUCCESS.getCode().equals(flagResponse.getCode())){
                log.info("订单{}前置仓覆盖区域打标DC查询失败:{}",orderInfo.getOrderNo(),(null == flagResponse ? "调接口失败" : flagResponse.getMsg()));
                updateDTO.setQzWarehAreaFlag(CommonConstant.PREPOSE_WAREHOUSE_ERROR);
                orderInfo.setQzWarehAreaFlag(CommonConstant.PREPOSE_WAREHOUSE_ERROR);
                orderInfoFeign.update(orderInfo.getId(),updateDTO);
                return;
            }
            updateDTO.setQzWarehAreaFlag(flagResponse.getData());
            orderInfo.setQzWarehAreaFlag(flagResponse.getData());
            orderInfoFeign.update(orderInfo.getId(),updateDTO);
        }


    }
}
