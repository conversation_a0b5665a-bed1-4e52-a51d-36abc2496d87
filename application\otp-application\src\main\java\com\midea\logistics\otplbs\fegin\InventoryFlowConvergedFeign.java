package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.request.EcmSearchGoodsRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: InventoryFlowFeign
 * Author: lindq2
 * Date: 2019-5-14 11:40:46
 * Description:库存流水 feign
 */
@FeignClient(value = "logistics-otp-order-converged-service",url = BaseRouter.LOGISTICS_OTP_ORDER_CONVERGED_SERVICE)
public interface InventoryFlowConvergedFeign {

    @PostMapping(value = OrderRouters.SEARCH_RECEIVING_AND_DISPATCHING_GOODS)
    JsonResponse searchReceivingAndDispatchingGoods(@RequestBody EcmSearchGoodsRequest ecmSearchGoodsRequest);

    public static void main(String[] args) {
        System.out.println(new StringBuilder("19071521000106").reverse().toString());
        System.out.println(new StringBuilder("19071522000108").reverse().toString());
    }
}

