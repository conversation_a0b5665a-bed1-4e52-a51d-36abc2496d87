package com.midea.logistics.otp.order.common.fegin;

import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ©Copyright ©1968-2024 Midea Group,IT
 *
 * @Description: 时效服务-增值服务时效
 * @FileName: ValueAddedServiceAgingFeign
 * @Author: fengxw26
 * @Date: 2024/7/23 9:45
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "ValueAddedServiceAgingFeign")
public interface ValueAddedServiceAgingFeign {

    @RequestMapping(value = "/valueAddedService/agingParse", method = RequestMethod.POST)
    JsonResponse agingParse(@RequestParam("taskNo") String taskNo);

    @RequestMapping(value = "/valueAddedService/deleteAgingByVdOrderNo", method = RequestMethod.POST)
    JsonResponse deleteAgingByVdOrderNo(@RequestParam("vdOrderNo") String vdOrderNo);

    @RequestMapping(value = "/valueAddedService/deleteAgingByTaskNo", method = RequestMethod.POST)
    JsonResponse deleteAgingByTaskNo(@RequestParam("taskNo") String taskNo);
}
