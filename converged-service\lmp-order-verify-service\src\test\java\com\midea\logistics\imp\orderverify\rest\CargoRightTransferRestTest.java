package com.midea.logistics.imp.orderverify.rest;

import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.junit.Assert.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class CargoRightTransferRestTest extends BaseRestTest {

    @Test
    public void testSearch2() throws Exception {

        String contend = "{\"id\":4828127790,\"createTime\":\"2020-11-30 09:33:56\",\"updateTime\":\"2020-11-30 09:33:56\",\"deleteFlag\":0,\"version\":0,\"createUserCode\":\"system\",\"updateUserCode\":\"system\",\"remark\":\"苏宁经销采购订单号1020441677、1020441648、1020441649\",\"start\":0,\"pageSize\":30,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"asc\":\" asc\",\"dataMap\":{},\"tenantCode\":\"annto\",\"orderNo\":\"IN22020113009331000035215\",\"customerOrderNo\":\"Z202011271-5\",\"orderType\":\"PO\",\"excuteStatus\":100,\"orderStatus\":100,\"companyCode\":\"5021907252\",\"companyName\":\"济南分公司\",\"customerCode\":\"********\",\"customerName\":\"志高空调官方旗舰店\",\"siteCode\":\"***********\",\"siteName\":\"济南电商中心\",\"whCode\":\"W201288\",\"whName\":\"济南电商大宝仓\",\"sourceSystem\":\"CIMS\",\"upperOrderTime\":\"2020-11-14 18:35:00\",\"expectArriveStartTime\":\"2020-11-27 23:59:59\",\"businessMode\":\"B2B\",\"transportType\":\"00\",\"agingProductCode\":\"BZ01\",\"inOutType\":\"OUT\",\"totalGrossWeight\":0,\"totalVolume\":0,\"entityId\":17,\"upperReceiverCode\":\"C0016132\",\"upperSourceCustomerCode\":\"C0016132\",\"upperWhCode\":\"MD7C44F200\",\"upperWhName\":\"烟机广州中心佛山正品-成品\",\"upperCustomerCode\":\"43411\",\"upperOrderType\":\"PO\",\"orderValue\":0.0,\"orderSource\":\"API\",\"exceptionDesc\":\"解析成功,delivery:null,地址：广东省佛山市三水区乐平镇佛山市三水区乐平镇苏宁物流三水基地\",\"upperReferenceId\":\"20070363\",\"upperReceiverName\":\"苏宁易购集团股份有限公司苏宁采购中心*广州\",\"upperSenderName\":\"苏宁易购集团股份有限公司苏宁采购中心*广州\",\"workOrderNo\":\"_\",\"orderTime\":\"2020-11-30 09:33:56\",\"cnDispatch\":0,\"deliveredVerifyFlag\":0,\"customerOrderAddress\":{\"id\":4663467372,\"createTime\":\"2020-11-30 09:33:56\",\"updateTime\":\"2020-11-30 09:33:56\",\"deleteFlag\":0,\"version\":2,\"createUserCode\":\"system\",\"updateUserCode\":\"system\",\"remark\":\"苏宁经销采购订单号1020441677、1020441648、1020441649\",\"start\":0,\"pageSize\":30,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"asc\":\" asc\",\"dataMap\":{},\"orderNo\":\"IN22020113009331000035215\",\"receiverName\":\"龙琪\",\"receiverMobile\":\"13535513809\",\"receiverProvinceCode\":\"144\",\"receiverProvinceName\":\"广东省\",\"receiverCityCode\":\"14406\",\"receiverCityName\":\"佛山市\",\"receiverDistrictCode\":\"1440607\",\"receiverDistrictName\":\"三水区\",\"receiverTownCode\":\"1440607103\",\"receiverTownName\":\"乐平镇\",\"receiverDetailAddr\":\"佛山市三水区乐平镇苏宁物流三水基地\",\"senderDistrictName\":\"狮山镇\",\"senderTownCode\":\"1440605124\",\"endLng\":113.02834639181492,\"endLat\":23.27249520783694,\"originAddr\":\"佛山市三水区乐平镇苏宁物流三水基地\"},\"customerOrderItems\":[{\"id\":55682944642,\"createTime\":\"2020-11-30 09:33:56\",\"updateTime\":\"2020-11-30 09:33:56\",\"deleteFlag\":0,\"version\":0,\"createUserCode\":\"system\",\"updateUserCode\":\"system\",\"start\":0,\"pageSize\":30,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"asc\":\" asc\",\"dataMap\":{},\"itemLineNo\":1,\"itemCode\":\"0006445240\",\"customerItemCode\":\"21073010004319\",\"itemSuiteCode\":\"\",\"itemClass\":\"M111.CRYJ.CRYJ0001\",\"itemStatus\":\"Y\",\"upperItemStatus\":\"MD7C44F200\",\"planQty\":2.0,\"volume\":0.003281,\"grossWeight\":28.0,\"netWeight\":24.5,\"price\":0.0,\"unit\":\"\",\"customerOrderNo\":\"Z202011271-5\",\"upperItemCode\":\"21073010004319\",\"itemName\":\"CXW-280-C62P 侧吸型烟机 （NFC+智能WiFi）触控 玻璃 中国\",\"materialGroup1\":\"G000331\",\"materialGroup5\":\"N000877\",\"upperItemStatusTo\":\"\",\"setFlag\":0,\"upperLineNo\":\"27882206\",\"orderNo\":\"IN22020113009331000035215\",\"singleVolume\":0.331245,\"singleWeight\":28.0}]}";
        mockMvc.perform(MockMvcRequestBuilders.post("/lmp/validate/cargo/right/transfer").content(contend).contentType(MediaType.APPLICATION_JSON).header("tenantCode","annto"))
            .andExpect(status().isOk()).andDo(print()).andExpect(jsonPath("$.code").isString())
            .andExpect(jsonPath("$.code").value("0"));
    }
}