package com.midea.logistics.otp.ttx.service.impl;


import com.midea.logistics.otp.ttx.inventory.domain.bean.InboundOrderHead;
import com.midea.logistics.otp.ttx.mapper.otp.InboundOrderHeadMapper;
import com.midea.logistics.otp.ttx.service.IInboundOrderHeadService;
import com.mideaframework.core.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: InboundOrderHeadServiceImpl
* Author: yaowl
* Date: 2019-9-17 14:15:59
* Description:InboundOrderHeadServiceImpl类
*/
@Service
public class InboundOrderHeadServiceImpl implements IInboundOrderHeadService {

    private static final Logger logger = LoggerFactory.getLogger( InboundOrderHeadServiceImpl.class );

    @Autowired
    private InboundOrderHeadMapper inboundOrderHeadMapper;


    private void swapBizData(InboundOrderHead source,InboundOrderHead target){
        BeanUtils.copyProperties(source,target,"id","updateUserCode","updateTime","version");
    }

    @Override
    public InboundOrderHead save(InboundOrderHead inboundOrderHead) {
        if( null == inboundOrderHead) {
            logger.warn("save inboundOrderHead, but inboundOrderHead is null...");
            return null;
        }
        inboundOrderHeadMapper.save(inboundOrderHead);
        return inboundOrderHead;
    }

    @Override
    public Integer insertBatch(List<InboundOrderHead> list) {
        if( null == list||list.isEmpty()) {
            logger.warn("save inboundOrderHeadList, but inboundOrderHeadList is null...");
            return null;
        }
        return inboundOrderHeadMapper.insertBatch(list);
    }


    @Override
    public InboundOrderHead update(InboundOrderHead inboundOrderHead) {
        if( null == inboundOrderHead || null == inboundOrderHead.getId()) {
            logger.warn("update inboundOrderHead, but inboundOrderHead is null  or inboundOrderHead id is null...");
            return null;
        }
        InboundOrderHead beforChange = selectById(inboundOrderHead);
        if(beforChange!=null){
            InboundOrderHead afterChange=new InboundOrderHead();
            BeanUtils.copyProperties(beforChange,afterChange);
            swapBizData(inboundOrderHead,afterChange);
            afterChange.setUpdateUserCode(inboundOrderHead.getUpdateUserCode());
            afterChange.setDeleteFlag(0);
            afterChange.setUpdateTime(new Date());
            inboundOrderHeadMapper.updateById(afterChange);
            return afterChange;
        }
        throw BusinessException.fail("数据不存在");
    }

    @Override
    public Integer batchUpdate(List<InboundOrderHead> list) {
        if(null==list||list.isEmpty()){
            logger.warn("update inboundOrderHeadList, but inboundOrderHeadList is null...");
            return null;
        }
        return inboundOrderHeadMapper.batchUpdate(list);
    }


    @Override
    public InboundOrderHead selectById(InboundOrderHead inboundOrderHead) {
        if(inboundOrderHead.getId()==null){
            logger.warn("select inboundOrderHead by id, but inboundOrderHead.id is null ...");
            return null;
        }
        InboundOrderHead idQuery=new InboundOrderHead();
        idQuery.setId(inboundOrderHead.getId());
        return inboundOrderHeadMapper.selectOne(idQuery);
    }

    @Override
    public List<InboundOrderHead> select(InboundOrderHead inboundOrderHead) {
        List<InboundOrderHead> inboundOrderHeads = new ArrayList<>();
        if( inboundOrderHead == null) {
            logger.warn("select inboundOrderHead by index, but inboundOrderHead is null ...");
            return inboundOrderHeads;
        }
        return inboundOrderHeadMapper.selectByIndex( inboundOrderHead );
    }


}