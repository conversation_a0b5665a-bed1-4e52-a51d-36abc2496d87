package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.OrderInfoBms;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: OrderInfoBmsFeign
* Author: james
* Date: 2020-4-28 16:00:13
* Description:订单财务信息表 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface OrderInfoBmsFeign {

    @RequestMapping(value = "/orderInfoBms", method = RequestMethod.POST)
    JsonResponse create(@RequestBody OrderInfoBms orderInfoBms);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/orderInfoBms/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/orderInfoBms/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody OrderInfoBms orderInfoBms);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/orderInfoBms/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/orderInfoBmss", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap OrderInfoBms orderInfoBms);

    @PostMapping(value = "/orderInfoBmsOne")
    JsonResponse searchOne(@RequestBody OrderInfoBms orderInfoBms);
}
