package com.midea.logistics.otp.order.common.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.domain.bean.ValueAddedService;
import com.midea.logistics.otp.order.domain.request.ValueAddExtRequest;
import com.midea.logistics.otp.order.domain.request.ValueAddRequest;
import com.midea.logistics.otp.order.domain.bean.custom.ValueAddedServiceExt;
import com.midea.logistics.otp.order.domain.request.ValueAddedServiceQueryPageReq;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;


/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ValueAddedServiceFeign
 * Author: lindq2
 * Date: 2019-5-14 11:40:46
 * Description:增值服务单 feign
 */
@FeignClient(value = "logistics-otp-order-service",url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface ValueAddedServiceFeign {

    @RequestMapping(value = "/valueAddedService", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ValueAddedService valueAddedService);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedService/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedService/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ValueAddedService valueAddedService);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedService/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/valueAddedServices", method = RequestMethod.GET)
    JsonResponse<PageResponse<ValueAddedService>> search(@SpringQueryMap ValueAddedService valueAddedService);

    @RequestMapping(value = "/valueAddedServices/selectOne", method = RequestMethod.GET)
    public JsonResponse<ValueAddedService> selectOne(@SpringQueryMap ValueAddedService valueAddedService);

    @RequestMapping(value = "/valueAddedService/searchWithDetails",method = RequestMethod.GET)
    JsonResponse<ValueAddRequest> searchWithDetailsByVdOrderNo(@RequestParam("vdOrderNo") String vdOrderNo);

    @RequestMapping(value = "/valueAddedServices/insertBatchVdWithTask",method = RequestMethod.POST)
    JsonResponse<Integer> insertBatchVdWithTask(@RequestBody ValueAddExtRequest valueAddExtRequest);

    @RequestMapping(value = "/valueAddedServices/updateBatchVdWithTask",method = RequestMethod.POST)
    JsonResponse<Integer> updateBatchVdWithTask(@RequestBody ValueAddExtRequest valueAddExtRequest);

    @RequestMapping(value = "/valueAddedService/createOrUpdateWithTask",method = RequestMethod.POST)
    JsonResponse createOrUpdateWithTask(@RequestBody ValueAddExtRequest valueAddExtRequest);

    @RequestMapping(value = "/valueAddedService/saveWithDetails",method = RequestMethod.POST)
    JsonResponse saveWithDetails(@RequestBody ValueAddedServiceExt valueAddedServiceExt);

    @RequestMapping(value = "/valueAddedService/updateCanSetEmpty/{id}", method = RequestMethod.PUT)
    JsonResponse<Integer> updateCanSetEmpty(@PathVariable("id") Long id,  @RequestBody ValueAddedService valueAddedService);

    @RequestMapping(value = "/valueAddedService/updateCloseOrder",method = RequestMethod.POST)
    JsonResponse updateCloseOrder(@RequestBody ValueAddRequest valueAddRequest);

    @RequestMapping(value = "/valueAddedService/createOrUpdateWithTaskExt",method = RequestMethod.POST)
    JsonResponse<Integer> createOrUpdateWithTaskExt(@RequestBody ValueAddExtRequest valueAddExtRequest);

    @PostMapping(value = "/valueAddedService/queryPage")
    JsonResponse<PageResponse<ValueAddedService>>valueAddedServicesQueryPage(@RequestBody ValueAddedServiceQueryPageReq valueAddedServiceQueryPageReq);
}
