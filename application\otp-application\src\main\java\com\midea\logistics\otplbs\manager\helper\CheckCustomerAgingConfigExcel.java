package com.midea.logistics.otplbs.manager.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.EbCustomerManager;
import com.midea.logistics.cache.manager.EsCompanyManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.EbCustomer;
import com.midea.logistics.domain.mdm.domain.EbPlace;
import com.midea.logistics.domain.mdm.domain.EsCompany;
import com.midea.logistics.domain.mdm.request.CdWarehouseRequest;
import com.midea.logistics.domain.mdm.request.EbPlaceRequest;
import com.midea.logistics.domain.mdm.request.EsCompanyRequest;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.CdWarehouseFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EbplaceFeign;
import com.midea.logistics.otp.common.feign.servicefeign.mdm.EsCompanyFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.AgingConfigFeign;
import com.midea.logistics.otp.common.helper.Validatorhelper;
import com.midea.logistics.otp.common.helper.bean.CustomerAgingConfigExcelRequest;
import com.midea.logistics.otp.common.helper.bean.CustomerAgingConfigNewExcelRequest;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.domain.bean.AgingConfig;
import com.midea.logistics.otp.order.domain.bean.CustomerAgingConfig;
import com.midea.logistics.otp.order.domain.bean.CustomerAgingType;
import com.midea.logistics.otp.order.domain.bean.TmpAddress;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CheckCustomerAgingConfigExcel implements CheckProperty<CustomerAgingConfigExcelRequest>{

    @Autowired
    private EbplaceFeign ebPlaceFeign;
    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private CdWarehouseFeign cdWarehouseFeign;
    @Autowired
    private EsCompanyFeign esCompanyFeign;

    @Autowired
    private EbCustomerManager ebCustomerManager;

    @Autowired
    private AgingConfigFeign agingConfigFeign;
    @Autowired
    Validatorhelper validatorhelper;
    @Autowired
    private EsCompanyManager esCompanyManager;

    public static void main(String[] args) {
        OrderType[] values = OrderType.values();
        System.out.println(JSON.toJSONString(values));
        //String 上门取件单 = Lists.newArrayList(OrderType.values()).stream().filter(o -> o.getValue().equals("上门取件单")).findFirst().get().getKey();
        //System.out.println(上门取件单);
    }

    @Override
    public void check(CustomerAgingConfigExcelRequest customerAgingConfigExcelRequest) {
        //checkTown(customerAgingConfig.getProvinceName(),customerAgingConfig.getCityName(),customerAgingConfig.getDistrictName(),customerAgingConfig.getTownName(),customerAgingConfig);
        //订单类型
        String orderTypeName = customerAgingConfigExcelRequest.getOrderTypeName();
        if (ToolUtils.isNotEmpty(orderTypeName)) {
            Optional<OrderType> first = Lists.newArrayList(OrderType.values()).stream().filter(o -> o.getValue().equals(orderTypeName)).findFirst();
            if (first.isPresent()){
                customerAgingConfigExcelRequest.setOrderType(first.get().getKey());
            }
        }

        String customerAgingTypeName = ObjectUtils.defaultIfNull(customerAgingConfigExcelRequest.getCustomerAgingTypeName(),CustomerAgingTypeEnum.ANNTO_AGING.getValue());
        Optional<CustomerAgingTypeEnum> customerAgingTypeFirst = Lists.newArrayList(CustomerAgingTypeEnum.values()).stream().filter(o -> o.getValue().equals(customerAgingTypeName)).findFirst();
        if (customerAgingTypeFirst.isPresent()){
            customerAgingConfigExcelRequest.setCustomerAgingType(customerAgingTypeFirst.get().getKey());
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getCustomerAgingType())){
            customerAgingConfigExcelRequest.setCustomerAgingType(CustomerAgingTypeEnum.ANNTO_AGING.getKey());
        }
        //配送方式
        String deliveryTypeName = customerAgingConfigExcelRequest.getDeliveryTypeName();
        if (ToolUtils.isNotEmpty(deliveryTypeName)) {
            Optional<DeliveryType> first = Lists.newArrayList(DeliveryType.values()).stream().filter(o -> o.getValue().equals(deliveryTypeName)).findFirst();
            if (first.isPresent()){
                customerAgingConfigExcelRequest.setDeliveryType(first.get().getKey());
            }
        }
        //始发平台
        String siteName = customerAgingConfigExcelRequest.getSiteName();
        String siteCode = customerAgingConfigExcelRequest.getSiteCode();
        if (StringUtils.isEmpty(siteCode)){
            customerAgingConfigExcelRequest.setSiteCode(querySiteCode(siteName));
        }else{
            String esCompanyCache = esCompanyManager.getEsCompanyNameBySiteCode(siteCode);
            customerAgingConfigExcelRequest.setSiteName(esCompanyCache);
        }

        //始发仓库
        String whName = customerAgingConfigExcelRequest.getWhName();
        String whCode = customerAgingConfigExcelRequest.getWhCode();
        if (StringUtils.isEmpty(whCode)){
            customerAgingConfigExcelRequest.setWhCode(queryWhCode(whName));
        }else{
            String cdWhName = cdWarehouseManager.getCdWarehouseNameByWhCode(whCode);
            customerAgingConfigExcelRequest.setWhName(cdWhName);
        }

        //是否中转
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getTaskType())
            &&(!StringUtils.isEmpty(customerAgingConfigExcelRequest.getTaskTypeName()))){
            if("是".equalsIgnoreCase(customerAgingConfigExcelRequest.getTaskTypeName())){
                customerAgingConfigExcelRequest.setTaskType(TaskType.RDO.getKey());
            }
        }

        //一级分拨仓库编码
        String distributionWhCode = customerAgingConfigExcelRequest.getDistributionWhCode();
        String cdWarehouseNameByWhCode = cdWarehouseManager.getCdWarehouseNameByWhCode(distributionWhCode);
        if (ToolUtils.isNotEmpty(distributionWhCode) && ToolUtils.isEmpty(cdWarehouseNameByWhCode)){
            throw BusinessException.fail("一级分拨仓库编码没找到仓库名称");
        }else {
            customerAgingConfigExcelRequest.setDistributionWhName(cdWarehouseNameByWhCode);
        }
        //二级分拨仓库编码
/*        String nextDistributeWhCode = customerAgingConfigExcelRequest.getNextDistributeWhCode();
        String cdWarehouseNameByWhCode1 = cdWarehouseManager.getCdWarehouseNameByWhCode(nextDistributeWhCode);
        customerAgingConfigExcelRequest.setNextDistributeWhName(cdWarehouseNameByWhCode1);*/
        //目的地
        String receiveProvince = customerAgingConfigExcelRequest.getReceiveProvince();
        String receiveCity = customerAgingConfigExcelRequest.getReceiveCity();
        String receiveDistrict = customerAgingConfigExcelRequest.getReceiveDistrict();
        String receiveTown = customerAgingConfigExcelRequest.getReceiveTown();
        List<String> addressCode = getAddressCode(Lists.newArrayList(receiveProvince, receiveCity, receiveDistrict, receiveTown));
        if (addressCode != null) {
            String receiverProvinceCode = addressCode.get(0);
            String receiverCityCode = addressCode.get(1);
            String receiverDistrictCode = addressCode.get(2);
            String receiverTownCode = addressCode.get(3);
            customerAgingConfigExcelRequest.setReceiveProvinceCode(receiverProvinceCode);
            customerAgingConfigExcelRequest.setReceiveCityCode(receiverCityCode);
            customerAgingConfigExcelRequest.setReceiveDistrictCode(receiverDistrictCode);
            customerAgingConfigExcelRequest.setReceiveTownCode(receiverTownCode);
            if (ToolUtils.isEmpty(receiverTownCode)){
                customerAgingConfigExcelRequest.setReceiveTown("");
            }
            log.info("目的地{}",JSON.toJSONString(addressCode));
            addressCode.clear();
        }
        //始发地
        String sendProvince = customerAgingConfigExcelRequest.getSendProvince();
        String sendCity = customerAgingConfigExcelRequest.getSendCity();
        String sendDistrict = customerAgingConfigExcelRequest.getSendDistrict();
        String sendTown = customerAgingConfigExcelRequest.getSendTown();
        addressCode = getAddressCode(Lists.newArrayList(sendProvince, sendCity, sendDistrict, sendTown));
        if (addressCode != null) {
            String sendProvinceCode = addressCode.get(0);
            String sendCityCode = addressCode.get(1);
            String sendDistrictCode = addressCode.get(2);
            String sendTownCode = addressCode.get(3);
            customerAgingConfigExcelRequest.setSendProvinceCode(sendProvinceCode);
            customerAgingConfigExcelRequest.setSendCityCode(sendCityCode);
            customerAgingConfigExcelRequest.setSendDistrictCode(sendDistrictCode);
            customerAgingConfigExcelRequest.setSendTownCode(sendTownCode);
            if (ToolUtils.isEmpty(sendTownCode)){
                customerAgingConfigExcelRequest.setSendTown("");
            }
            log.info("始发地{}",JSON.toJSONString(addressCode));
        }
        //时效产品
        String agingProductName = customerAgingConfigExcelRequest.getAgingProductName();
        String agingProductCode = customerAgingConfigExcelRequest.getAgingProduct();
        AgingConfig agingConfig = queryAgingProductCode(agingProductName, agingProductCode);
        if (null == agingConfig){
            if (ToolUtils.isNotEmpty(agingProductName) || ToolUtils.isNotEmpty(agingProductCode)){
                throw BusinessException.fail("时效产品未查询到");
            }
            throw BusinessException.fail("时效产品不能为空");
        }
        String productCode = agingConfig.getAgingProductCode();
        String productName = agingConfig.getAgingProductName();
        customerAgingConfigExcelRequest.setAgingProduct(productCode);
        customerAgingConfigExcelRequest.setAgingProductName(productName);
        //是否启用
        String enableFlagName = customerAgingConfigExcelRequest.getEnableFlagName();
        if(StringUtils.isNotEmpty(enableFlagName)){
            customerAgingConfigExcelRequest.setEnableFlag(CommonConstant.ENABLED.equals(enableFlagName)? CommonConstant.FLAG_YES : CommonConstant.FLAG_NO);
        }
        if(customerAgingConfigExcelRequest.getEnableFlag()==null){
            customerAgingConfigExcelRequest.setEnableFlag(CommonConstant.FLAG_NO);
        }
        //客户
        if (ToolUtils.isNotEmpty(customerAgingConfigExcelRequest.getCustomerCode())) {
            EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(customerAgingConfigExcelRequest.getCustomerCode());
            if ( ebCustomerCache != null) {
                customerAgingConfigExcelRequest.setCustomerName(ebCustomerCache.getEbcuNameCn());
            }
        }
        //仓库
        if(ToolUtils.isNotEmpty(customerAgingConfigExcelRequest.getWhCode())){
            CdWarehouse cdWarehouseCache = cdWarehouseManager.getCdWarehouseCache(customerAgingConfigExcelRequest.getWhCode());
            if (ToolUtils.isNotEmpty(cdWarehouseCache)) {
                customerAgingConfigExcelRequest.setWhName(cdWarehouseCache.getCdwhName());
                if (!customerAgingConfigExcelRequest.getSiteCode().equals(cdWarehouseCache.getSiteCode())) {
                    throw BusinessException.fail("仓库需要在对应服务平台下");
                }
            }
        }
        CustomerAgingConfig customerAgingConfig = new CustomerAgingConfig();
        BeanUtils.copyProperties(customerAgingConfigExcelRequest, customerAgingConfig);
        if (ToolUtils.isNotEmpty(customerAgingConfigExcelRequest.getDatail())){
            customerAgingConfig.setCustomerAgingConfigDatailList(Lists.newArrayList());
        }
        //遍历校验参数
        List<String> validateList = validCustomerAgingConfig(customerAgingConfig);
        if (ToolUtils.isNotEmpty(validateList)){
            throw BusinessException.fail(StringUtils.join(validateList,','));
        }

        if (ToolUtils.isEmpty(customerAgingConfigExcelRequest.getDatail())){
            throw BusinessException.fail("未配置时效节点，最少也要配置一个撒");
        }
    }

    /**
     * 端对端时效导入校验，之前的太复杂了
     * @param customerAgingConfigExcelRequest
     */
    public void checkByNew(CustomerAgingConfigNewExcelRequest customerAgingConfigExcelRequest) {
        //checkTown(customerAgingConfig.getProvinceName(),customerAgingConfig.getCityName(),customerAgingConfig.getDistrictName(),customerAgingConfig.getTownName(),customerAgingConfig);
        //订单类型
        String orderTypeName = customerAgingConfigExcelRequest.getOrderTypeName();
        if (ToolUtils.isNotEmpty(orderTypeName)) {
            Optional<OrderType> first = Lists.newArrayList(OrderType.values()).stream().filter(o -> o.getValue().equals(orderTypeName)).findFirst();
            if (first.isPresent()){
                customerAgingConfigExcelRequest.setOrderType(first.get().getKey());
            }
        }
/*        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getWhCode())){
            throw BusinessException.fail("站点仓库编码不能为空");
        }*/
        if (ToolUtils.isEmpty(customerAgingConfigExcelRequest.getOrderTypeName())) {
            if (StringUtils.isEmpty(customerAgingConfigExcelRequest.getOrderType())){
                throw BusinessException.fail("订单类型编码不能为空");
            }
            customerAgingConfigExcelRequest.setOrderTypeName(OrderType.getName(customerAgingConfigExcelRequest.getOrderType()));
            if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getOrderTypeName())){
                throw BusinessException.fail("订单类型编码不存在");
            }
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getSiteCode()) && (
            "1".equals(customerAgingConfigExcelRequest.getWhCodeNum())&&OrderType.YS.getKey().equalsIgnoreCase(customerAgingConfigExcelRequest.getOrderType())
            || "2".equals(customerAgingConfigExcelRequest.getWhCodeNum())&&OrderType.DO.getKey().equalsIgnoreCase(customerAgingConfigExcelRequest.getOrderType())
            )){
            throw BusinessException.fail("站点平台编码不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getAgingProductName())){
            throw BusinessException.fail("时效产品不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getOrderStartTime())){
            throw BusinessException.fail("截单时间开始不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getOrderEndTime())){
            throw BusinessException.fail("截单时间结束不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getAgingStartTime())){
            throw BusinessException.fail("时效起点不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getValidTime())){
            throw BusinessException.fail("生效时间不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getInvalidTime())){
            throw BusinessException.fail("失效时间不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getEnableFlagName())){
            throw BusinessException.fail("是否启用不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getCustomerAgingCode())){
            customerAgingConfigExcelRequest.setCustomerAgingCode("");
            throw BusinessException.fail("时效编码不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getCustomerAgingName())){
            throw BusinessException.fail("时效名称不能为空");
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getBusinessMode())){
            throw BusinessException.fail("业务模式不能为空");
        }else {
            List<String> b = Arrays.asList(BusinessMode.B2B.getName(),BusinessMode.B2C.getName());
            if(!b.contains(customerAgingConfigExcelRequest.getBusinessMode())){
                throw BusinessException.fail("业务模式不存在");
            }
        }
        String customerAgingTypeName = ObjectUtils.defaultIfNull(customerAgingConfigExcelRequest.getCustomerAgingTypeName(),CustomerAgingTypeEnum.ANNTO_AGING.getValue());
        Optional<CustomerAgingTypeEnum> customerAgingTypeFirst = Lists.newArrayList(CustomerAgingTypeEnum.values()).stream().filter(o -> o.getValue().equals(customerAgingTypeName)).findFirst();
        if (customerAgingTypeFirst.isPresent()){
            customerAgingConfigExcelRequest.setCustomerAgingType(customerAgingTypeFirst.get().getKey());
        }
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getCustomerAgingType())){
            customerAgingConfigExcelRequest.setCustomerAgingType(CustomerAgingTypeEnum.ANNTO_AGING.getKey());
        }
        //配送方式
        String deliveryTypeName = customerAgingConfigExcelRequest.getDeliveryTypeName();
        if (ToolUtils.isNotEmpty(deliveryTypeName)) {
            Optional<DeliveryType> first = Lists.newArrayList(DeliveryType.values()).stream().filter(o -> o.getValue().equals(deliveryTypeName)).findFirst();
            if (first.isPresent()){
                customerAgingConfigExcelRequest.setDeliveryType(first.get().getKey());
            }
        }
        //始发平台
        String siteName = customerAgingConfigExcelRequest.getSiteName();
        String siteCode = customerAgingConfigExcelRequest.getSiteCode();
        if (StringUtils.isEmpty(siteCode)){
            customerAgingConfigExcelRequest.setSiteCode(querySiteCode(siteName));
        }else{
            String esCompanyCache = esCompanyManager.getEsCompanyNameBySiteCode(siteCode);
            if(StringUtils.isNotBlank(esCompanyCache)) {
                customerAgingConfigExcelRequest.setSiteName(esCompanyCache);
            }
        }

        //始发仓库
        String whName = customerAgingConfigExcelRequest.getWhName();
        String whCode = customerAgingConfigExcelRequest.getWhCode();
        if (StringUtils.isEmpty(whCode)){
            customerAgingConfigExcelRequest.setWhCode(queryWhCode(whName));
        }else{
            String cdWhName = cdWarehouseManager.getCdWarehouseNameByWhCode(whCode);
            if(StringUtils.isNotBlank(cdWhName)) {
                customerAgingConfigExcelRequest.setWhName(cdWhName);
            }
        }

        //是否中转
        if(StringUtils.isEmpty(customerAgingConfigExcelRequest.getTaskType())
            &&(!StringUtils.isEmpty(customerAgingConfigExcelRequest.getTaskTypeName()))){
            if("是".equalsIgnoreCase(customerAgingConfigExcelRequest.getTaskTypeName())){
                customerAgingConfigExcelRequest.setTaskType(TaskType.RDO.getKey());
            }
        }

        //一级分拨仓库编码
        String distributionWhCode = customerAgingConfigExcelRequest.getDistributionWhCode();
        String cdWarehouseNameByWhCode = cdWarehouseManager.getCdWarehouseNameByWhCode(distributionWhCode);
        if (ToolUtils.isNotEmpty(distributionWhCode) && ToolUtils.isEmpty(cdWarehouseNameByWhCode)){
            throw BusinessException.fail("一级分拨仓库编码没找到仓库名称");
        }else {
            customerAgingConfigExcelRequest.setDistributionWhName(cdWarehouseNameByWhCode);
        }
        //二级分拨仓库编码
/*        String nextDistributeWhCode = customerAgingConfigExcelRequest.getNextDistributeWhCode();
        String cdWarehouseNameByWhCode1 = cdWarehouseManager.getCdWarehouseNameByWhCode(nextDistributeWhCode);
        customerAgingConfigExcelRequest.setNextDistributeWhName(cdWarehouseNameByWhCode1);*/
        //目的地
        String receiveProvince = customerAgingConfigExcelRequest.getReceiveProvince();
        String receiveCity = customerAgingConfigExcelRequest.getReceiveCity();
        String receiveDistrict = customerAgingConfigExcelRequest.getReceiveDistrict();
        String receiveTown = customerAgingConfigExcelRequest.getReceiveTown();
        List<String> addressCode = getAddressCode(Lists.newArrayList(receiveProvince, receiveCity, receiveDistrict, receiveTown));
        if (addressCode != null) {
            String receiverProvinceCode = addressCode.get(0);
            String receiverCityCode = addressCode.get(1);
            String receiverDistrictCode = addressCode.get(2);
            String receiverTownCode = addressCode.get(3);
            customerAgingConfigExcelRequest.setReceiveProvinceCode(receiverProvinceCode);
            customerAgingConfigExcelRequest.setReceiveCityCode(receiverCityCode);
            customerAgingConfigExcelRequest.setReceiveDistrictCode(receiverDistrictCode);
            customerAgingConfigExcelRequest.setReceiveTownCode(receiverTownCode);
            if (ToolUtils.isEmpty(receiverTownCode)){
                customerAgingConfigExcelRequest.setReceiveTown("");
            }
            log.info("目的地{}",JSON.toJSONString(addressCode));
            addressCode.clear();
        }
        //始发地
        String sendProvince = customerAgingConfigExcelRequest.getSendProvince();
        String sendCity = customerAgingConfigExcelRequest.getSendCity();
        String sendDistrict = customerAgingConfigExcelRequest.getSendDistrict();
        String sendTown = customerAgingConfigExcelRequest.getSendTown();
        addressCode = getAddressCode(Lists.newArrayList(sendProvince, sendCity, sendDistrict, sendTown));
        if (addressCode != null) {
            String sendProvinceCode = addressCode.get(0);
            String sendCityCode = addressCode.get(1);
            String sendDistrictCode = addressCode.get(2);
            String sendTownCode = addressCode.get(3);
            customerAgingConfigExcelRequest.setSendProvinceCode(sendProvinceCode);
            customerAgingConfigExcelRequest.setSendCityCode(sendCityCode);
            customerAgingConfigExcelRequest.setSendDistrictCode(sendDistrictCode);
            customerAgingConfigExcelRequest.setSendTownCode(sendTownCode);
            if (ToolUtils.isEmpty(sendTownCode)){
                customerAgingConfigExcelRequest.setSendTown("");
            }
            log.info("始发地{}",JSON.toJSONString(addressCode));
        }
        //时效产品
        String agingProductName = customerAgingConfigExcelRequest.getAgingProductName();
        String agingProductCode = customerAgingConfigExcelRequest.getAgingProduct();
        AgingConfig agingConfig = queryAgingProductCode(agingProductName, agingProductCode);
        if (null == agingConfig){
            if (ToolUtils.isNotEmpty(agingProductName) || ToolUtils.isNotEmpty(agingProductCode)){
                throw BusinessException.fail("时效产品未查询到");
            }
            throw BusinessException.fail("时效产品不能为空");
        }
        String productCode = agingConfig.getAgingProductCode();
        String productName = agingConfig.getAgingProductName();
        customerAgingConfigExcelRequest.setAgingProduct(productCode);
        customerAgingConfigExcelRequest.setAgingProductName(productName);
        //是否启用
        String enableFlagName = customerAgingConfigExcelRequest.getEnableFlagName();
        if(StringUtils.isNotEmpty(enableFlagName)){
            customerAgingConfigExcelRequest.setEnableFlag(CommonConstant.ENABLED.equals(enableFlagName)? CommonConstant.FLAG_YES : CommonConstant.FLAG_NO);
        }
        if(customerAgingConfigExcelRequest.getEnableFlag()==null){
            customerAgingConfigExcelRequest.setEnableFlag(CommonConstant.FLAG_NO);
        }
        //客户
        if (ToolUtils.isNotEmpty(customerAgingConfigExcelRequest.getCustomerCode())) {
            EbCustomer ebCustomerCache = ebCustomerManager.getEbCustomerCache(customerAgingConfigExcelRequest.getCustomerCode());
            if ( ebCustomerCache != null) {
                customerAgingConfigExcelRequest.setCustomerName(ebCustomerCache.getEbcuNameCn());
            }else {
                throw BusinessException.fail("未查到对应客户信息");
            }
        }
        //仓库
        if(ToolUtils.isNotEmpty(customerAgingConfigExcelRequest.getWhCode())){
            CdWarehouse cdWarehouseCache = cdWarehouseManager.getCdWarehouseCache(customerAgingConfigExcelRequest.getWhCode());
            if (ToolUtils.isNotEmpty(cdWarehouseCache)) {
                customerAgingConfigExcelRequest.setWhName(cdWarehouseCache.getCdwhName());
                if (StringUtils.isNotBlank(customerAgingConfigExcelRequest.getSiteCode()) && !customerAgingConfigExcelRequest.getSiteCode().equals(cdWarehouseCache.getSiteCode())) {
                    throw BusinessException.fail("仓库需要在对应服务平台下");
                }
            }
        }
        CustomerAgingConfig customerAgingConfig = new CustomerAgingConfig();
        BeanUtils.copyProperties(customerAgingConfigExcelRequest, customerAgingConfig);
        if (ToolUtils.isNotEmpty(customerAgingConfigExcelRequest.getDatail())){
            customerAgingConfig.setCustomerAgingConfigDatailList(Lists.newArrayList());
        }
        //遍历校验参数
        List<String> validateList = validCustomerAgingConfig(customerAgingConfig);
        if (ToolUtils.isNotEmpty(validateList)){
            throw BusinessException.fail(StringUtils.join(validateList,','));
        }

        if (ToolUtils.isEmpty(customerAgingConfigExcelRequest.getDatails())){
            throw BusinessException.fail("未配置时效节点，最少也要配置一个撒");
        }
    }

    /**
     * @description: 从 getAddressCode 方法优化过来的，之前的方法逻辑太复杂，if else get（0）太多了，不容易理解
     * @param: [addrs]
     * @return: java.util.List<java.lang.String>
     * @author: 陈永培
     * @createtime: 2020/6/11 9:31
     */
    public TmpAddress getAddCode(String province, String city, String district, String town) {

        String tmpProvince = null;
        String tmpCity = null;
        String tmpDistrict = null;
        String tmpTown = null;

        //1.查找省
        EbPlace place = queryPlace(province, "PLACE_PROVINCE", "100000");
        if (place == null) {
            return new TmpAddress(tmpProvince,tmpCity,tmpDistrict,tmpTown);
        }
        tmpProvince = place.getEbplCode();

        //2.查找市
        place = queryPlace(city, "PLACE_CITY", tmpProvince);
        if (place == null) {
            return new TmpAddress(tmpProvince,tmpCity,tmpDistrict,tmpTown);
        }
        tmpCity = place.getEbplCode();

        //3.查找区
        place = queryPlace(district, "PLACE_DISTRICT", tmpCity);
        if (place == null) {
            return new TmpAddress(tmpProvince,tmpCity,tmpDistrict,tmpTown);
        }
        tmpDistrict = place.getEbplCode();

        //4.查找县
        place = queryPlace(town, "PLACE_STREET", tmpDistrict);
        if (place == null) {
            return new TmpAddress(tmpProvince,tmpCity,tmpDistrict,tmpTown);
        }
        tmpTown = place.getEbplCode();

        //5.最后返回
        return new TmpAddress(tmpProvince,tmpCity,tmpDistrict,tmpTown);
    }


    /**
     * 以下为校验用户输入的省市区镇
     *
     * @param
     * @return
     */
    public List<String> getAddressCode(List<String> addrs) {
        if (null == addrs || addrs.size() < 3){
            return null;
        }
        List<String> list = Lists.newArrayList();
        if (ToolUtils.isEmpty(addrs.get(0))) {

            log.warn(BaseCodeEnum.REQUEST_NULL.getCode(), "省不能为空");
            return null;
        }
        EbPlace place = queryPlace(addrs.get(0), "PLACE_PROVINCE", "100000");
        if (place != null) {
            list.add(place.getEbplCode());
            if (ToolUtils.isNotEmpty(addrs.get(1))) {
                place = queryPlace(addrs.get(1), "PLACE_CITY", place.getEbplCode());
            }
        }else{
            list.add(null);
        }

        if (place != null) {
            list.add(place.getEbplCode());
            if (ToolUtils.isNotEmpty(addrs.get(2))) {
                place = queryPlace(addrs.get(2), "PLACE_DISTRICT", place.getEbplCode());
            }
        }else{
            list.add(null);
        }
        if (place != null) {
            list.add(place.getEbplCode());
            if (addrs.size() != 4){
                list.add(null);
                return list;
            }
            if (ToolUtils.isNotEmpty(addrs.get(3))) {
                place = queryPlace(addrs.get(3), "PLACE_STREET", place.getEbplCode());
            } else {
                place = null;
            }
        }else{
            list.add(null);
        }
        if (place == null) {
            if (addrs.size() == 4){
                log.warn(BaseCodeEnum.PARAMETER_INVALID.getCode(), addrs.get(0) + "省" + addrs.get(1) + "市" + addrs.get(2) + "区"   + addrs.get(3) + "不存在或已停用");
            }
            log.warn(BaseCodeEnum.PARAMETER_INVALID.getCode(), addrs.get(0) + "省" + addrs.get(1) + "市" + addrs.get(2) + "区"  + "不存在或已停用");
            list.add("");
        }else{
            list.add(place.getEbplCode());
        }
        return list;
    }
    /**
     * 查询地点
     */
    private EbPlace queryPlace(String name, String type, String parentCode) {

        if (StringUtils.isEmpty(name)) {
            return null;
        }

        EbPlace ebPlaceRequest = new EbPlace();
        ebPlaceRequest.setEbplNameCn(name);
        ebPlaceRequest.setEbplType(type);
        ebPlaceRequest.setEbplParentPmCode(parentCode);
        ebPlaceRequest.setEbplIsAble("ENABLE");
        JsonResponse<List<EbPlace>> jsonResponse = ebPlaceFeign.queryEbPlace(ebPlaceRequest,1);
        List<EbPlace> ebPlaceList = jsonResponse.data();
        EbPlace ebPlace = CollectionUtils.isEmpty(ebPlaceList) ? null : ebPlaceList.get(0);
        if (null !=ebPlace && ToolUtils.isNotEqual(name,ebPlace.getEbplNameCn())){
            ebPlace = null;
        }
        
        if(null == ebPlace){
            throw BusinessException.fail("【"+name+"】地址不存在，请检查地址（长度和名称）是否与基础数据一致");
        }
        return ebPlace;
    }

    private String queryWhCode(String whName) {
        if(ToolUtils.isEmpty(whName)) {
            return null;
        }
        CdWarehouseRequest param = new CdWarehouseRequest();
        //cdwhIsStop=0
        param.setCdwhIsStop(0d);
        param.setCdwhName(whName);
        JsonResponse<PageResponse<CdWarehouse>> jsonResponse = cdWarehouseFeign.search(param);
        List<CdWarehouse> list = jsonResponse.data.list;
        CdWarehouse base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getWhCode();
    }

    private String querySiteCode(String siteName) {
        if(ToolUtils.isEmpty(siteName)) {
            return null;
        }
        JsonResponse<PageResponse<EsCompany>> jsonResponse = esCompanyFeign.searchByCodeAndNames(siteName);
        List<EsCompany> list = jsonResponse.data.list;
        EsCompany base = CollectionUtils.isEmpty(list) ? null : list.get(0);
        return null == base? null : base.getSiteCode();
    }


    private AgingConfig queryAgingProductCode(String agingProductName,String agingProductCode) {
        if(ToolUtils.isEmpty(agingProductName) && ToolUtils.isEmpty(agingProductCode)) {
            return null;
        }
        AgingConfig agingConfig = new AgingConfig();
        agingConfig.setAgingProductName(agingProductName);
        agingConfig.setAgingProductCode(agingProductCode);
        JsonResponse<PageResponse<AgingConfig>> search = agingConfigFeign.search(agingConfig);
        if (null == search.data() || ToolUtils.isEmpty(search.data().getList())){
            return null;
        }
        AgingConfig base = search.data().getList().get(0);
        return null == base? null : base;
    }

    public List<String> validCustomerAgingConfig(CustomerAgingConfig customerAgingConfig) {
        if (customerAgingConfig.getId() != null) {
            return Collections.emptyList();
        }
        //1、客户时效页面改造：收发货地址控制
        List<String> validateList = validatorhelper.validate(customerAgingConfig);
        //1）销售出库、调拨出库、返厂出库上门取件收货地必填
        if (OrderType.PO.getKey().equals(customerAgingConfig.getOrderType())
            || OrderType.AO.getKey().equals(customerAgingConfig.getOrderType())
//            || OrderType.DP.getKey().equals(customerAgingConfig.getOrderType())
        ){
            if (StringUtils.isBlank(customerAgingConfig.getReceiveCityCode())|| StringUtils.isBlank(customerAgingConfig.getReceiveDistrictCode())|| StringUtils.isBlank(customerAgingConfig.getReceiveProvinceCode())) {
                validateList.add("收货地址不能为空!");
            }
        }
        //2）退货入库、上门取件退货，发货地必填
        if (OrderType.RI.getKey().equals(customerAgingConfig.getOrderType())
            || OrderType.DPRI.getKey().equals(customerAgingConfig.getOrderType())
        ){
            if(!TaskType.RDO.getKey().equalsIgnoreCase(customerAgingConfig.getTaskType())) {
                if (StringUtils.isBlank(customerAgingConfig.getSendCityCode()) || StringUtils.isBlank(customerAgingConfig.getSendDistrictCode()) || StringUtils.isBlank(customerAgingConfig.getSendProvinceCode())) {
                    validateList.add("发货地址不能为空!");
                }
            }
        }
        //3）纯运输订单收发货必填
        if (InOutType.YS.getName().equals(customerAgingConfig.getOrderType())) {
            if(!TaskType.RDO.getKey().equalsIgnoreCase(customerAgingConfig.getTaskType())) {
                if (StringUtils.isBlank(customerAgingConfig.getSendCityCode()) || StringUtils.isBlank(customerAgingConfig.getSendDistrictCode()) || StringUtils.isBlank(customerAgingConfig.getSendProvinceCode())) {
                    validateList.add("发货地址不能为空!");
                }
                if (StringUtils.isBlank(customerAgingConfig.getReceiveCityCode()) || StringUtils.isBlank(customerAgingConfig.getReceiveDistrictCode()) || StringUtils.isBlank(customerAgingConfig.getReceiveProvinceCode())) {
                    validateList.add("收货地址不能为空!");
                }
            }
        } else {
            if (StringUtils.isBlank(customerAgingConfig.getSiteCode())) {
                validateList.add("平台编码不能为空!");
            }
        }
        return validateList;
    }
}
