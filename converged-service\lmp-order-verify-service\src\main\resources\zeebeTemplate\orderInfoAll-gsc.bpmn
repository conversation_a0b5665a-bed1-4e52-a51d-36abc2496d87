<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-gsc" name="子订单流程-gsc" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0opo74t</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0opo74t" sourceRef="StartEvent_1" targetRef="Activity_0smupof" />
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_02zzxtp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0p8hiid" sourceRef="Activity_087r4hi" targetRef="Gateway_0b6uii0" />
    <bpmn:exclusiveGateway id="Gateway_0smudh3" name="是否获取运单号" default="Flow_1e8tnvs">
      <bpmn:incoming>Flow_1imkmni</bpmn:incoming>
      <bpmn:incoming>Flow_1did1ew</bpmn:incoming>
      <bpmn:outgoing>Flow_1e8tnvs</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ss94pu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1e8tnvs" name="yes" sourceRef="Gateway_0smudh3" targetRef="Activity_1fv890g" />
    <bpmn:serviceTask id="Activity_0tlqh7q" name="时效产品">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="aging" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/agingParse" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_161nz2d</bpmn:incoming>
      <bpmn:outgoing>Flow_039eniu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0smupof" name="大小电解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="orderOutCollabWh" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/setOrderOutCollabWh" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0opo74t</bpmn:incoming>
      <bpmn:outgoing>Flow_0vyki7j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
          <zeebe:output source="=responseBody.deliveryType" target="orderInfo.deliveryType" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_0p8hiid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e8tnvs</bpmn:incoming>
      <bpmn:outgoing>Flow_14nina3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_195jet3" name="是否自提" default="Flow_161nz2d">
      <bpmn:incoming>Flow_0ltk8av</bpmn:incoming>
      <bpmn:outgoing>Flow_161nz2d</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mwtbnu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161nz2d" name="no" sourceRef="Gateway_195jet3" targetRef="Activity_0tlqh7q" />
    <bpmn:sequenceFlow id="Flow_1mwtbnu" name="yes" sourceRef="Gateway_195jet3" targetRef="Activity_1kkl3ix">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and (orderInfo.orderType="DO" or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="PI") or (orderInfo.deliveryType="EXPRESS" and orderInfo.orderType="AI"))) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0hvdqty" name="计费相关">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/anyBmsInfo" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ss94pu</bpmn:incoming>
      <bpmn:incoming>Flow_14nina3</bpmn:incoming>
      <bpmn:outgoing>Flow_0ltk8av</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ltk8av" sourceRef="Activity_0hvdqty" targetRef="Gateway_195jet3" />
    <bpmn:serviceTask id="Activity_1kkl3ix" name="推送查单系统,bms,net,task">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="lots" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/bmsLotsNetTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1mwtbnu</bpmn:incoming>
      <bpmn:incoming>Flow_039eniu</bpmn:incoming>
      <bpmn:outgoing>Flow_02zzxtp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02zzxtp" sourceRef="Activity_1kkl3ix" targetRef="Event_14d5skw" />
    <bpmn:sequenceFlow id="Flow_0ss94pu" name="no" sourceRef="Gateway_0smudh3" targetRef="Activity_0hvdqty">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=((orderInfo.businessMode="B2B" and orderInfo.deliveryType="ZT") or (orderInfo.businessMode="B2C" and orderInfo.orderType="PO" and orderInfo.deliveryType="ZT") or (orderInfo.orderType="PO" and orderInfo.deliveryType="EXPRESS"))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_039eniu" sourceRef="Activity_0tlqh7q" targetRef="Activity_1kkl3ix" />
    <bpmn:serviceTask id="Activity_03ulbfz" name="项目分类解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="projectType" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/analysisProjectClassify" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vyki7j</bpmn:incoming>
      <bpmn:outgoing>Flow_1a30h1b</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_1a30h1b</bpmn:incoming>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Gateway_0b6uii0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:sequenceFlow id="Flow_0vyki7j" sourceRef="Activity_0smupof" targetRef="Activity_03ulbfz" />
    <bpmn:sequenceFlow id="Flow_1a30h1b" sourceRef="Activity_03ulbfz" targetRef="Gateway_0mzkgmp" />
    <bpmn:sequenceFlow id="Flow_14nina3" sourceRef="Activity_1fv890g" targetRef="Activity_0hvdqty" />
    <bpmn:serviceTask id="Activity_1bt85bl" name="电商分类解析1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/ecommerceCategoriesAnalysisTag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_15lsgau</bpmn:incoming>
      <bpmn:outgoing>Flow_1imkmni</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1imkmni" sourceRef="Activity_1bt85bl" targetRef="Gateway_0smudh3" />
    <bpmn:exclusiveGateway id="Gateway_0b6uii0" default="Flow_1did1ew">
      <bpmn:incoming>Flow_0p8hiid</bpmn:incoming>
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:outgoing>Flow_15lsgau</bpmn:outgoing>
      <bpmn:outgoing>Flow_1did1ew</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15lsgau" name="yes" sourceRef="Gateway_0b6uii0" targetRef="Activity_1bt85bl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!=null and (orderInfo.orderType="DP" or orderInfo.orderType="SO")) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1did1ew" sourceRef="Gateway_0b6uii0" targetRef="Gateway_0smudh3" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-gsc">
      <bpmndi:BPMNEdge id="Flow_1imkmni_di" bpmnElement="Flow_1imkmni">
        <di:waypoint x="1090" y="177" />
        <di:waypoint x="1139" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14nina3_di" bpmnElement="Flow_14nina3">
        <di:waypoint x="1331" y="270" />
        <di:waypoint x="1392" y="270" />
        <di:waypoint x="1392" y="432" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a30h1b_di" bpmnElement="Flow_1a30h1b">
        <di:waypoint x="520" y="177" />
        <di:waypoint x="609" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vyki7j_di" bpmnElement="Flow_0vyki7j">
        <di:waypoint x="320" y="177" />
        <di:waypoint x="420" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="659" y="177" />
        <di:waypoint x="705" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="662" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="634" y="152" />
        <di:waypoint x="634" y="110" />
        <di:waypoint x="910" y="110" />
        <di:waypoint x="910" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="635" y="122" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_039eniu_di" bpmnElement="Flow_039eniu">
        <di:waypoint x="1012" y="472" />
        <di:waypoint x="934" y="472" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ss94pu_di" bpmnElement="Flow_0ss94pu">
        <di:waypoint x="1189" y="177" />
        <di:waypoint x="1392" y="177" />
        <di:waypoint x="1392" y="432" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1397" y="298" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02zzxtp_di" bpmnElement="Flow_02zzxtp">
        <di:waypoint x="834" y="472" />
        <di:waypoint x="752" y="472" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ltk8av_di" bpmnElement="Flow_0ltk8av">
        <di:waypoint x="1342" y="472" />
        <di:waypoint x="1212" y="472" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mwtbnu_di" bpmnElement="Flow_1mwtbnu">
        <di:waypoint x="1187" y="447" />
        <di:waypoint x="1187" y="390" />
        <di:waypoint x="884" y="390" />
        <di:waypoint x="884" y="430" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1027" y="372" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161nz2d_di" bpmnElement="Flow_161nz2d">
        <di:waypoint x="1162" y="472" />
        <di:waypoint x="1112" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1131" y="454" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e8tnvs_di" bpmnElement="Flow_1e8tnvs">
        <di:waypoint x="1164" y="202" />
        <di:waypoint x="1164" y="270" />
        <di:waypoint x="1231" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1165" y="241" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p8hiid_di" bpmnElement="Flow_0p8hiid">
        <di:waypoint x="805" y="177" />
        <di:waypoint x="905" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0opo74t_di" bpmnElement="Flow_0opo74t">
        <di:waypoint x="172" y="177" />
        <di:waypoint x="220" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15lsgau_di" bpmnElement="Flow_15lsgau">
        <di:waypoint x="955" y="177" />
        <di:waypoint x="990" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="964" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1did1ew_di" bpmnElement="Flow_1did1ew">
        <di:waypoint x="930" y="202" />
        <di:waypoint x="930" y="250" />
        <di:waypoint x="1140" y="250" />
        <di:waypoint x="1140" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="136" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="143" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="716" y="454" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="723" y="497" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0smudh3_di" bpmnElement="Gateway_0smudh3" isMarkerVisible="true">
        <dc:Bounds x="1139" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1150" y="127" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tz6c6h_di" bpmnElement="Activity_0tlqh7q">
        <dc:Bounds x="1012" y="432" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_059zzl3_di" bpmnElement="Activity_0smupof">
        <dc:Bounds x="220" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="705" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="1231" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_195jet3_di" bpmnElement="Gateway_195jet3" isMarkerVisible="true">
        <dc:Bounds x="1162" y="447" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1166" y="504" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0hvdqty_di" bpmnElement="Activity_0hvdqty">
        <dc:Bounds x="1342" y="432" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kkl3ix_di" bpmnElement="Activity_1kkl3ix">
        <dc:Bounds x="834" y="432" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03ulbfz_di" bpmnElement="Activity_03ulbfz">
        <dc:Bounds x="420" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="609" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="621" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bt85bl_di" bpmnElement="Activity_1bt85bl">
        <dc:Bounds x="990" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0b6uii0_di" bpmnElement="Gateway_0b6uii0" isMarkerVisible="true">
        <dc:Bounds x="905" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
