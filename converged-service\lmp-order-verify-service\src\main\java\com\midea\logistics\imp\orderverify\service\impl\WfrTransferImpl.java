package com.midea.logistics.imp.orderverify.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.cache.manager.ControlParamManager;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.domain.mdm.domain.ControlParam;
import com.midea.logistics.imp.orderverify.service.LmpSeparateWarehouseService;
import com.midea.logistics.imp.orderverify.service.WfrTransferService;
import com.midea.logistics.otp.bean.ValidateSameWhDto;
import com.midea.logistics.otp.bean.WarehouseClusterConfigDetail;
import com.midea.logistics.otp.bean.WarehouseClusterReq;
import com.midea.logistics.otp.bean.WarehouseClusterRes;
import com.midea.logistics.otp.common.bean.SuContractExt;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.enums.WhCategoryEnum;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.utils.ControlParamHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.common.utils.SUtils;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.fegin.BopServiceFeign;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderItemFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.common.mq.producer.OrderAuditMessageProducer;
import com.midea.logistics.otp.order.common.mq.producer.ZeebeOrderProducer;
import com.midea.logistics.otp.order.common.service.RelationOrderService;
import com.midea.logistics.otp.order.common.service.SeparateWarehouseService;
import com.midea.logistics.otp.order.converged.domain.request.OrderInfoRequest;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfoExtend;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderItem;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import com.midea.logistics.otp.order.domain.request.BatchCenterInvQueryReq;
import com.midea.logistics.otp.order.domain.request.MrpCentralInvRequest;
import com.midea.logistics.otp.order.domain.response.CenterInvQueryItem;
import com.midea.logistics.otp.order.domain.response.CenterInvQueryResponse;
import com.midea.logistics.otp.ordertask.converged.domain.bean.OrderMq;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 项目名称：logistics-otp
 * 功能说明：
 *
 * <AUTHOR>
 * @createtime 2024/2/23 8:44
 */
@Component
@Slf4j
public class WfrTransferImpl implements WfrTransferService {
    
    @Autowired
    private CustomerOrderItemFeign customerOrderItemFeign;
    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private SeparateWarehouseService separateWarehouseService;
    @Autowired
    private RelationOrderService relationOrderService;
    @Autowired
    private CenterInvHelper centerInvHelper;
    @Autowired
    private WfrTransferHelper wfrTransferHelper;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private OrderAuditMessageProducer orderAuditMessageProducer;
    @Autowired
    private LmpSeparateWarehouseService lmpSeparateWarehouseService;
    @Autowired
    private ContractHelper contractHelper;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private ControlParamManager controlParamManager;
    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private ZeebeOrderProducer zeebeOrderProducer;
    @Autowired
    private BopServiceFeign bopServiceFeign;
    @Autowired
    private MDMHelper mdmHelper;
    @Autowired
    private ControlParamHelper controlParamHelper;
    
    //不允许进行货权转移的商品状态
    private static final String EXCLUDE_JOIN_ITEM_STATUS = "EXCLUDE_JOIN_ITEM_STATUS";
    //无忧零售仓库分参数
    public static final String WFR_WH_WHCATEGORY = "WFR_WH_WHCATEGORY";
    /**
     * !@货权转移校验 - 入口（中台） - !@无忧零售 - 借还款流程专用 cf: https://cf.annto.com/pages/viewpage.action?pageId=38958234
     * !@货权转移校验 - 入口（中台） - !@美云销OU  cf： https://cf.annto.com/pages/viewpage.action?pageId=46495812
     * !@货权转移校验 - 入口（中台） - !@云仓OU  cf： https://cf.annto.com/pages/viewpage.action?pageId=50464942
     * !@货权转移校验 - 入口（中台） - !@MSSOU  cf: https://cf.annto.com/pages/viewpage.action?pageId=66652918
     * @param customerOrderInfo
     * @return
     */
    @Override
    @ZeebeFlow("CARGO_RIGHT_TRANSFER")
    public CustomerOrderInfoExt wfrCargoRightTransfer(CustomerOrderInfoExt customerOrderInfo) {
        String sourceSystem = customerOrderInfo.getSourceSystem();
        String projectClassify = customerOrderInfo.getProjectClassify();
        String upperOrderType = customerOrderInfo.getUpperOrderType();
        String siteCode = customerOrderInfo.getSiteCode();
        String targetSiteCode = customerOrderInfo.getTargetSiteCode();
        String orderNo = customerOrderInfo.getOrderNo();
        String customerCode = customerOrderInfo.getCustomerCode();
        String targetCustomerCode = customerOrderInfo.getTargetCustomerCode();
        
        //这里showLog，主要为了排查问题方便，免得经常去服务器看日志
        boolean showLog = customerOrderInfo.isCipherTextSystem();
    
        //1、借还货订单校验（无忧零售）
        boolean wfrFlag = BusinessHelper.isWFROrder(customerOrderInfo);
        //2、美云销OU转移
        boolean shtFlag = BusinessHelper.isSHTOrder(customerOrderInfo);
        //3、MSSOU转移
        boolean mssShtFlag = BusinessHelper.isMssSHTOrder(customerOrderInfo);
        //4、云仓OU转移
        boolean cShtFlag = BusinessHelper.isCloudSHTOrder(customerOrderInfo);
        if (! (wfrFlag || shtFlag || mssShtFlag  || cShtFlag )){
            FlowListenerParam.success("非零售的借还货订单或者美云销、云仓OU转移订单、楼宇OU转移订单，无需解析");
            return customerOrderInfo;
        }
        
        CustomerOrderInfoExtend customerOrderExtend = orderverifyHelper.getCustomerOrderExtend(customerOrderInfo.getOrderNo());
        if (ToolUtils.isEmpty(customerOrderExtend)) {
        	throw BusinessException.fail("父单拓展表信息为空");
        }
        
        
        String flag = wfrFlag? "无忧零售" : shtFlag? "美云销OU": cShtFlag? "云仓OU": mssShtFlag ? "楼宇OU": "";
        
        CommonConstant.check(siteCode,"平台为空");
        CommonConstant.check(targetSiteCode,"目标平台为空");
        CommonConstant.check(customerCode,"客户为空");
        CommonConstant.check(targetCustomerCode,"目标客户为空");
        
        //如果已经分仓完成， 不做货权转移了，防止zeebe流程重跑...
        if(CommonConstant.APARTED.equals(customerOrderInfo.getApartStatus())){
            FlowListenerParam.success("已经分仓完成，不做货权转移");
            return customerOrderInfo;
        }
        
        //校验平台
        boolean notEqual = ToolUtils.isNotEqual(siteCode, targetSiteCode);
        if (notEqual){
            throw BusinessException.fail("收发货平台不一致，不允许执行货权转移");
        }
        
        List<CustomerOrderItem> lists = customerOrderItemFeign.listByOrderNo(orderNo).getData();
        if (ToolUtils.isEmpty(lists) ) {
            throw BusinessException.fail("查询父单明细为空");
        }
    
        //检查商品状态
        checkItemStatus(lists,customerOrderInfo);
    
        //校验目标商品状态和商品状态是否一致
        boolean existDiff = SUtils.anyMatch(lists, a -> ToolUtils.isNotEqual(a.getItemStatus(), a.getItemStatusTo()));
        if (existDiff){
            throw BusinessException.fail("订单商品状态与目标商品状态不一致，不允许执行货权转移");
        }
    
        //取对应的仓库去同仓校验
        List<String> whcodes = wfrTransferHelper.getWhcodes(customerOrderInfo, customerOrderExtend);
    
        //3.同仓校验
        log.info("wfrCargoRightTransfer-> orderNo:{},center whcodes:{}",orderNo, JSON.toJSONString(whcodes));
        ValidateSameWhDto validateSameWhDto = ValidateSameWhDto.builder()
              .customerCode(customerCode)
              .targetCustomerCode(targetCustomerCode)
              .whCodes(whcodes)
              .orderNo(customerOrderInfo.getOrderNo())
              .siteCode(siteCode)
              .build();
        
        //2024年8月31日15:25:01 丽红：统一调用 wms同仓校验，不区分基地标  https://cf.annto.com/pages/viewpage.action?pageId=53263643
        List<String> sameWhs = separateWarehouseService.onlyValidateSameWarehouseByWms(validateSameWhDto);
        
        //List<String> sameWhs = separateWarehouseService.validateSameWarehouseCodeSplit(validateSameWhDto);
        if (CollectionUtils.isEmpty(sameWhs)) {
            throw BusinessException.fail("收发客户未配置协同关系，不允许执行货权转移");
        }
    
        //4、合同校验 2024年3月28日09:32:42 高露新增逻辑：传目标客户+平台+仓储去查询合同 （无忧零售才执行，OU无需要校验）
        contractVerify(customerOrderInfo, siteCode, targetCustomerCode);
    
        //5.执行自动分仓
        
        //，使用订单：客户（customer_code）+订单平台（site_code）+商品编码（item_code）+商品状态（item_status）调用中央库存批量查询查询接口，获取库存信息，将订单商品计划数量与仓库可用库存对比
        BatchCenterInvQueryReq search = new BatchCenterInvQueryReq();
        //客户类型
        search.setInvType("custInv");
        //加上协同仓（算了不加了，返回报错了，不知道是商品报错还是协同仓报错,还是后面判断吧）
        //search.setWhCodes(sameWhs.toArray(new String[0]));
        //客户
        search.setOwnerCode(customerCode);
        //凭条
        search.setSiteCodes(new String[]{siteCode});
        //商品
        search.setItemCodes(lists.stream().map(CustomerOrderItem::getItemCode).toArray(String[]::new));
        //正品
        search.setItemStatus(lists.stream().map(CustomerOrderItem::getItemStatus).findFirst().get());
        MrpCentralInvRequest obj = new MrpCentralInvRequest();
        search.setPageNo(obj.getPageNo());
        search.setPageSize(obj.getPageSize());
        search.setStart(obj.getStart());
        search.setTotalRows(obj.getTotalRows());
        CenterInvQueryResponse centerInvInfo = centerInvHelper.batchQueryCenterInvInfo(search);
        
        List<CenterInvQueryItem> items = centerInvInfo.getItems();
        if (ToolUtils.isEmpty(items)) {
            throw BusinessException.fail("中央库存查询结果为空");
        }
        
        //过滤出同仓校验的配置协同关系的仓库的商品
        List<String> invWhCode = SUtils.toList(items, CenterInvQueryItem::getWhCode);
        List<String> needConfigWhcodeList = invWhCode.stream().filter(item -> !sameWhs.contains(item)).collect(Collectors.toList());
        //对比中央库存返回的仓库和协同仓库，输出中央库存有的，但是协同仓没有的
        
        List<CenterInvQueryItem> filterItem = SUtils.filter(items, a -> { return sameWhs.contains(a.getWhCode()) ? true : false; });
        if (ToolUtils.isEmpty(filterItem)) {
            String tip = "请前往WMS核实，路径：WMS-配置-出库配置-客户";
            if(businessHelper.isBaseOrder(validateSameWhDto.getOrderNo())){
                tip = "请前往大物流核实，路径：仓库基础数据 - 仓储配置 - 协同仓配置";
            }
            throw BusinessException.fail("仓库：【"+SUtils.join(needConfigWhcodeList,",")+"】不存在协同配置，协同仓：【"+SUtils.join(sameWhs,",")+"】库存不足，客户："+customerCode+"， 目标客户："+targetCustomerCode+"，"+tip);
        }
        
        Map<String, List<CenterInvQueryItem>> invItemCodeGroup = SUtils.group(filterItem, CenterInvQueryItem::getItemCode);
        Map<String, List<CustomerOrderItem>> customerItemCodeGroup = SUtils.group(lists, CustomerOrderItem::getItemCode);
        for (String itemCode : customerItemCodeGroup.keySet()) {
            List<CustomerOrderItem> customerOrderItemList = customerItemCodeGroup.get(itemCode);
            if (ToolUtils.isEmpty(customerOrderItemList)) {
                continue;
            }
            
            List<CenterInvQueryItem> centerInvQueryItems = invItemCodeGroup.get(itemCode);
            if (ToolUtils.isEmpty(centerInvQueryItems)) {
                throw BusinessException.fail("商品编码【"+itemCode+"】查询中央库存无可用数量");
            }
            
            BigDecimal sumEffectQtyEa = SUtils.sum(centerInvQueryItems, CenterInvQueryItem::getEffectQtyEa);
            BigDecimal sumCustomerPlanQty = SUtils.sum(customerOrderItemList, CustomerOrderItem::getPlanQty);
            if (sumCustomerPlanQty.compareTo(sumEffectQtyEa) > 0){
                String msg = "（";
                for (CenterInvQueryItem centerInvQueryItem : centerInvQueryItems) {
                    msg = msg + centerInvQueryItem.getWhName() + " : "+ centerInvQueryItem.getEffectQtyEa() + ",";
                }
                msg = msg.substring(0,msg.length()-1) + "）";
                throw BusinessException.fail("商品编码【"+itemCode+"】的总计划数量【"+sumCustomerPlanQty.intValue()+"】大于所有中心仓的总可用数量【"+sumEffectQtyEa+"】"+msg);
            }
        }
    
        try {
            wfrTransferHelper.generateAnOrder(customerOrderInfo, lists, filterItem,showLog);
            wfrTransferHelper.showLog(customerOrderInfo, showLog);
        } catch (Exception e) {
            String log = wfrTransferHelper.showLog(customerOrderInfo.getOrderNo(), showLog);
            throw BusinessException.fail(e.getMessage()  + (ToolUtils.isNotEmpty(log)?log:""));
        }
    
        FlowListenerParam.success(flag+"，货权转移成功");
        return customerOrderInfo;
    }
    
    
    
    
   
    
   
    
    /**
    * @description: 检查明细的状态是否符合转移
    * @param: [lists, customerOrderInfo]
    * @return: void
    * @author: 陈永培
    * @createtime: 2024/7/11 9:35
    */
    private void checkItemStatus(List<CustomerOrderItem> lists,CustomerOrderInfoExt customerOrderInfo) {
    
        List<CustomerOrderItem> emptyStatusList = SUtils.filter(lists, a -> ToolUtils.isEmpty(a.getItemStatus()));
        if (ToolUtils.isNotEmpty(emptyStatusList)) {
            throw BusinessException.fail("商品明细中包含为空的商品状态，不允许进行货权转移");
        }
    
        String status = lists.stream().map(CustomerOrderItem::getItemStatus).findFirst().get();
        boolean different = SUtils.anyMatch(lists, a -> !status.equals(a.getItemStatus()));
        if (different){
            throw BusinessException.fail("商品明细中包含多种商品状态，不允许进行货权转移");
        }
        
        //1、无忧零售校验全部正品
        boolean wfrFlag = BusinessHelper.isWFROrder(customerOrderInfo);
        if (wfrFlag ){
            boolean existNoY = SUtils.anyMatch(lists, a -> !ItemStatus.Y.getKey().equals(a.getItemStatus()));
            if (existNoY){
                throw BusinessException.fail("非正品的订单不允许执行货权转移");
            }
            return;
        }
        
        //2、其余的，判断商品状态是否在系统控制参数EXCLUDE_JOIN_ITEM_STATUS中，若存在，则不允许执行转移，若不存在，则允许执行转移
        lists.stream().forEach(a -> {
            String itemStatus = a.getItemStatus();
            boolean isExcludeJoinItemStatus = checkIsExcludeJoinItemStatus(itemStatus);
            if (isExcludeJoinItemStatus) {
                throw BusinessException.fail("【" + dictHelper.getItemStatusName(itemStatus) + "】不允许进行货权转移");
            }
        });
        return;
    }
    
    
    
    /**
     * 合同校验
     * @param customerOrderInfo
     * @param siteCode
     * @param targetCustomerCode
     */
    private void contractVerify(CustomerOrderInfoExt customerOrderInfo, String siteCode, String targetCustomerCode) {
    
        //1、无忧零售才校验仓库合同
        if (BusinessHelper.isWFROrder(customerOrderInfo) ){
            OrderInfoRequest contractRequest = new OrderInfoRequest();
            contractRequest.setOrderNo(customerOrderInfo.getOrderNo());
            contractRequest.setBusinessType(BusinessType.STORAGE.getKey());
            contractRequest.setSiteCode(siteCode);
            contractRequest.setCustomerCode(targetCustomerCode);
            JsonResponse<SuContractExt> contractInfoResponse = contractHelper.getContractInfo(contractRequest);
            log.info("wfrCargoRightTransfer-> orderNo ：{} , contractRequest:{} , contractInfoResponse:{} ", customerOrderInfo.getOrderNo(), JSONObject.toJSONString(contractRequest), JSONObject.toJSONString(contractInfoResponse));
            SuContractExt data = contractInfoResponse.getData();
            if (ToolUtils.isEmpty(data)) {
                String msg = String.format("客户%s在%s平台无仓储合同", targetCustomerCode, siteCode);
                throw BusinessException.fail(msg);
            }
           return;
        }
        
    }
    
    /**
    * !@无忧零售 - 自动分仓拆单后触发任务生成
    * @param: [customerOrderInfo]
    * @return: com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt
    * @author: 陈永培
    * @createtime: 2024/2/23 9:04
    */
    @Override
    @ZeebeFlow("APART")
    public CustomerOrderInfoExt automatic(CustomerOrderInfoExt customerOrderInfo) {
    
        //1、查询所有的子单
        String parentOrderNo = customerOrderInfo.getOrderNo();
        List<OrderInfo> orderInfoList = orderverifyHelper.getOrderInfoByParentNo(parentOrderNo);
        if (ToolUtils.isEmpty(orderInfoList)) {
            throw BusinessException.fail("查询子单列表失败");
        }
    
        //2、创建子单拓展表
        lmpSeparateWarehouseService.createOrderExtend(parentOrderNo,orderInfoList, customerOrderInfo);
    
        
        //1、借还货订单校验（无忧零售），还需要走子单审核【质押校验】 + 【生成任务】
        if(BusinessHelper.isWFROrder(customerOrderInfo)){
            //触发中台流程
            SUtils.foreach(orderInfoList,a->{
                zeebeOrderProducer.sent(a.getOrderNo());
            });
            return customerOrderInfo;
        }
        
        //3.其余的直接触发任务生成
        SUtils.foreach(orderInfoList,a->{
            OrderMq orderMq = new OrderMq();
            orderMq.setOrderNo(a.getOrderNo());
            orderMq.setNode(OrderStatus.AUDITED.getKey());
            boolean sent = orderAuditMessageProducer.sent(orderMq);
        });
        
        return customerOrderInfo;
    }
    
    /**
     * @description: 判断是否符合不允许进行货权转移的订单类型
     * @return: void
     * @author: 陈永培
     * @createtime: 2019/11/12 10:38
     */
    public boolean checkIsExcludeJoinItemStatus(String itemStatus) {
        boolean isSuccess = false;
        ControlParam controlParam = controlParamManager.getCache(EXCLUDE_JOIN_ITEM_STATUS);
        if (null != controlParam) {
            String targetValue = controlParam.getValue();
            if (StringUtils.isNotEmpty(targetValue)) {
                String[] statusList = targetValue.split(",");
                for (String s : statusList) {
                    if (s.equals(itemStatus)) {
                        isSuccess = true;
                    }
                }
            }
        }
        return isSuccess;
    }
    
}
