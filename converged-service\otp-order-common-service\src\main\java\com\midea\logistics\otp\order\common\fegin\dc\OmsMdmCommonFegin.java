package com.midea.logistics.otp.order.common.fegin.dc;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.converged.domain.request.OrderInfoRequest;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date
 */
@FeignClient(value = "logistics-mdm-oms-service",url = BaseRouter.LOGISTICS_MDM_OMS_SERVICE)
public interface OmsMdmCommonFegin {

    @GetMapping("/select/customer/wh")
    public JsonResponse getCustomer(@SpringQueryMap OrderInfoRequest orderInfoRequest);

}
