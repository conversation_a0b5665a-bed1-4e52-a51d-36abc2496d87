<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.common.ScPosConfigMapper">

    <select id="getScPosConfigList" resultType="com.midea.logistics.otp.rule.domain.bean.ScPosConfig">
        select
        <include refid="searchFieldsSql"/>
        from sc_pos_config t
        where t.delete_flag = 0
        <if test="sourceSystems != null and sourceSystems.size > 0">
            and t.source_system in
            <foreach collection="sourceSystems" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerCodes != null and customerCodes.size > 0">
            and t.customer_code in
            <foreach collection="customerCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="orderTypes != null and orderTypes.size > 0">
            and t.order_type in
            <foreach collection="orderTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="siteCodes != null and siteCodes.size > 0">
            and t.site_code in
            <foreach collection="siteCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailAddrs != null and detailAddrs.size > 0">
            and t.detail_addr in
            <foreach collection="detailAddrs" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>


    <update id="updatePlace">
        update sc_pos_config
        <set>
            `version` = `version` + 1 ,
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `district_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `town_code` = #{busCode}
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>

    <update id="dcReplacePlace">
        update sc_pos_config
        <set>
            `version` = `version` + 1 ,
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `province_code` = #{newBusCode},
                    `province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `city_code` = #{newBusCode},
                    `city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `district_code` = #{newBusCode},
                    `district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `town_code` = #{newBusCode},
                    `town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
                and `district_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `town_code` = #{busCode}
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>
</mapper>