package com.midea.logistics.imp.orderverify.service.impl;

import com.annto.framework.easyjunit.disable.apollo.EasyJunitDisableApollo;
import com.annto.framework.easyjunit.mariadb.EasyJunitMariadb;
import com.annto.framework.easyjunit.redis.EasyJunitRedis;
import com.annto.framework.easyjunit.rocketmq.EasyJunitRocketMQ;
import com.midea.logistics.imp.orderverify.LmpOrderVerifyService;
import com.midea.logistics.imp.orderverify.helper.OrderHelper;
import com.midea.logistics.otp.enums.BusinessType;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.order.domain.bean.CustomerOrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

/**
 * 描 述 : 业务计费类型逻辑修改
 * 作 者 : 刘 院 民
 * 日 期 : 2024/7/22 14:06
 */
@Slf4j
@EasyJunitDisableApollo
@EasyJunitRocketMQ
@EasyJunitMariadb
@EasyJunitRedis
@ActiveProfiles("local")
@TestPropertySource(locations = "classpath:local.properties")
@SpringBootTest(classes = LmpOrderVerifyService.class)
class AnalysisBusineesFeeServiceImplTest {

    @Autowired
    AnalysisBusineesFeeServiceImpl analysisBusineesFeeService;

    @MockBean
    private OrderHelper orderHelper;

    @Test
    void analysisBusineesFeeConfigTest() {
        OrderInfoExt orderInfoExt = new OrderInfoExt();
        orderInfoExt.setId(1L);
        orderInfoExt.setParentOrderNo("IN22407221348497720");
        orderInfoExt.setOrderNo("AN22407221348527721");

        CustomerOrderInfo customerOrderInfo = new CustomerOrderInfo();
        customerOrderInfo.setSourceSystem("OFC");
        customerOrderInfo.setDeliveryType(DeliveryType.EXPRESS.getKey());
        customerOrderInfo.setOrderType("PO");
        Mockito.when(orderHelper.getCustomerOrderInfo(orderInfoExt.getParentOrderNo())).thenReturn(customerOrderInfo);

        try {
            analysisBusineesFeeService.analysisBusineesFeeConfig(orderInfoExt, true);
        } catch (Exception e) {
            log.error("", e);
        }

        Assertions.assertTrue(StringUtils.equals(orderInfoExt.getBusinessType(), BusinessType.EXPRESS.getKey()));

    }

}
