package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.midea.logistics.imp.orderverify.rest.BaseRestTest;
import com.midea.logistics.otp.order.domain.bean.custom.CustomerOrderInfoExt;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class ProductConfigServiceImplTest extends BaseRestTest {


    @Autowired
    ProductConfigServiceImpl productConfigService;
    

    @Test
    public void checkProductConfig() {
        String  str = "{\"id\":16126592,\"createTime\":\"2021-03-09 13:41:38\",\"updateTime\":\"2021-03-09 13:47:37\",\"deleteFlag\":0,\"version\":0,\"createUserCode\":\"system\",\"updateUserCode\":\"system\",\"start\":0,\"pageSize\":30,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"asc\":\" asc\",\"dataMap\":{},\"tenantCode\":\"annto\",\"orderNo\":\"IN22103091330146135\",\"customerOrderNo\":\"TIO161456787556175507-001\",\"relationOrderNo\":\"TOS2021030100000095\",\"orderType\":\"ADI\",\"excuteStatus\":199,\"orderStatus\":150,\"companyCode\":\"7751614555\",\"companyName\":\"佛山分公司\",\"customerCode\":\"E0109501\",\"customerName\":\"E0109501公司-名称\",\"siteCode\":\"7790073156\",\"siteName\":\"佛山小电服务中心\",\"orderSourcePlatform\":\"1\",\"sourceSystem\":\"ECM\",\"upperOrderTime\":\"2021-03-01 11:04:35\",\"businessMode\":\"B2C\",\"deliveryType\":\"ZT\",\"transportType\":\"00\",\"inOutType\":\"IN\",\"totalGrossWeight\":4.0896,\"totalVolume\":0.08142,\"totalQty\":8.0,\"upperWhCode\":\"10968\",\"upperCustomerCode\":\"10968\",\"upperOrderType\":\"OrderType\",\"thirdFlag\":1,\"orderSource\":\"API\",\"orderTime\":\"2021-03-09 13:41:38\",\"cnDispatch\":0,\"deliveredVerifyFlag\":0,\"totalPkgQty\":0,\"installFlag\":0,\"deliverypayType\":1,\"collectionFlag\":\"N\",\"vipFlag\":0,\"customerOrderAddress\":{\"id\":15699192,\"createTime\":\"2021-03-09 13:41:38\",\"updateTime\":\"2021-03-09 13:43:51\",\"deleteFlag\":0,\"version\":1,\"createUserCode\":\"system\",\"updateUserCode\":\"system\",\"start\":0,\"pageSize\":30,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"asc\":\" asc\",\"dataMap\":{},\"orderNo\":\"IN22103091330146135\",\"originAddr\":\"null\"},\"customerOrderItems\":[{\"id\":16398671,\"createTime\":\"2021-03-09 13:41:38\",\"updateTime\":\"2021-03-09 13:47:37\",\"deleteFlag\":0,\"version\":3,\"createUserCode\":\"system\",\"updateUserCode\":\"system\",\"start\":0,\"pageSize\":30,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"asc\":\" asc\",\"dataMap\":{},\"tenantCode\":\"annto\",\"itemLineNo\":1,\"itemCode\":\"0004253122\",\"customerItemCode\":\"X1014300000005\",\"itemClass\":\"OT.XXM.XXM004\",\"itemStatus\":\"Y\",\"upperItemStatus\":\"10968\",\"planQty\":4.0,\"volume\":0.0192,\"grossWeight\":0.6824,\"netWeight\":0.5,\"unit\":\"Ge\",\"customerOrderNo\":\"TIO161456787556175507-001\",\"upperItemCode\":\"X1014300000005\",\"itemName\":\"熊小美趴趴熊\",\"materialGroup1\":\"G000013\",\"setFlag\":0,\"barcode69\":\"6938187308524\",\"upperLineNo\":\"22210301000138\",\"orderNo\":\"IN22103091330146135\",\"singleVolume\":0.0192,\"singleWeight\":0.6824},{\"id\":16398672,\"createTime\":\"2021-03-09 13:41:38\",\"updateTime\":\"2021-03-09 13:47:37\",\"deleteFlag\":0,\"version\":3,\"createUserCode\":\"system\",\"updateUserCode\":\"system\",\"start\":0,\"pageSize\":30,\"orderBy\":\"1\",\"orderByType\":\"asc\",\"asc\":\" asc\",\"dataMap\":{},\"tenantCode\":\"annto\",\"itemLineNo\":2,\"itemCode\":\"0004571318\",\"customerItemCode\":\"X1014101000015\",\"itemClass\":\"OT.XXM.XXM002\",\"itemStatus\":\"Y\",\"upperItemStatus\":\"10968\",\"planQty\":4.0,\"volume\":0.001155,\"grossWeight\":0.34,\"unit\":\"Ba\",\"customerOrderNo\":\"TIO161456787556175507-001\",\"upperItemCode\":\"X1014101000015\",\"itemName\":\"熊小美遮阳伞\",\"materialGroup1\":\"G000013\",\"materialGroup2\":\"G000013\",\"setFlag\":0,\"barcode69\":\"6938187307619\",\"upperLineNo\":\"22210301000139\",\"orderNo\":\"IN22103091330146135\",\"singleVolume\":0.001155,\"singleWeight\":0.34}]}";
        CustomerOrderInfoExt customerOrderInfoExt = JSON.parseObject(str,CustomerOrderInfoExt.class);
        productConfigService.checkProductConfig(customerOrderInfoExt);
    }
}