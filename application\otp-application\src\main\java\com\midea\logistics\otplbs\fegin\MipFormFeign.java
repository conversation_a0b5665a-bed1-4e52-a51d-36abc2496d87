package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.MipForm;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: MipFormFeign
* Author: luoh
* Date: 2020-3-11 12:00:56
* Description:mip流程表单 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface MipFormFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/mipForm", method = RequestMethod.POST)
    JsonResponse create(@RequestBody MipForm mipForm);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipForm/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipForm/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody MipForm mipForm);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipForm/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/mipForms", method = RequestMethod.POST)
    JsonResponse search(@RequestBody MipForm mipForm);

    @RequestMapping(value = "/mipForm/selectOne", method = RequestMethod.GET)
    JsonResponse<MipForm> selectOne(@SpringQueryMap MipForm mipForm);
}
