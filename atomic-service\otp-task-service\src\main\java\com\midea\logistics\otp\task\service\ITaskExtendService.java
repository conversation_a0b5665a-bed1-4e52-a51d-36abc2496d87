package com.midea.logistics.otp.task.service;
import com.mideaframework.core.web.PageResponse;
import com.midea.logistics.otp.task.domain.bean.TaskExtend;

import java.util.List;
/**
* ©Copyright ©1968-2019 Midea Group,IT
* FileName: ITaskExtendService
* Author: 刘院民
* Date: 2023-3-21 11:34:11
* Description:ITaskExtendService服务接口
*/
public interface ITaskExtendService{

    Integer saveTaskExtend(TaskExtend taskExtend);

    Integer deleteTaskExtend(TaskExtend taskExtend);

    Integer updateTaskExtend(TaskExtend taskExtend);

    TaskExtend selectOneTaskExtend(TaskExtend taskExtend);

    PageResponse<TaskExtend> selectTaskExtendPage(TaskExtend taskExtend);

    List<TaskExtend> selectTaskExtendByIndex(TaskExtend taskExtend);

    int countTaskExtendByIndex(TaskExtend taskExtend);

    Integer insertBatch(List<TaskExtend> taskExtends);

    Integer batchUpdate(List<TaskExtend> taskExtends);

    Integer deleteBatch(List<TaskExtend> taskExtends);

    List<TaskExtend> listByTaskNos(List<String> taskNos);

    Integer updateByIdCanSetEmpty(TaskExtend taskExtend);

    Integer updateBatchCanSetEmpty(List<TaskExtend> list);

    Integer deleteByTaskNos(List<String> list);
}