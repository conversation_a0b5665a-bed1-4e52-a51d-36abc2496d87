package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.OrderRouters;
import com.midea.logistics.otp.order.domain.bean.ValueAddedServiceItem;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ValueAddedServiceItemFeign
 * Author: lindq2
 * Date: 2019-5-14 11:40:46
 * Description:增值服务单商品 feign
 */
@FeignClient(value = "logistics-otp-order-service")
public interface ValueAddedServiceItemFeign {

    @RequestMapping(value = "/valueAddedServiceItem", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ValueAddedServiceItem valueAddedServiceItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItem/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItem/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ValueAddedServiceItem valueAddedServiceItem);

    /**
     * @param id
     * @return
     */
    @RequestMapping(value = "/valueAddedServiceItem/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/valueAddedServiceItems", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap ValueAddedServiceItem valueAddedServiceItem);

    /**
     * 批量新增或者保存，根据是否传id来判断
     *
     * @param valueAddedServiceItems
     * @return
     */
    @PostMapping(OrderRouters.VALUE_ADDED_SERVICE_ITEM_BATCH_CREATE_OR_UPDATE)
    JsonResponse batchCreateOrUpdate(@RequestBody List<ValueAddedServiceItem> valueAddedServiceItems);
}
