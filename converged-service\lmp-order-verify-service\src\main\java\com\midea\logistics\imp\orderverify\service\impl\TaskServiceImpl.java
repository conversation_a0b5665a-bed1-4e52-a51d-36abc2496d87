package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.order.common.helper.InstallHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.midea.logistics.imp.orderverify.service.TaskService;
import com.midea.logistics.otp.enums.DeliveryType;
import com.midea.logistics.otp.enums.OrderStatus;
import com.midea.logistics.otp.enums.OrderType;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.OrderStatusCheckHelper;
import com.midea.logistics.otp.order.common.mq.producer.OrderAuditMessageProducer;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.ordertask.converged.domain.bean.OrderMq;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {

    @Autowired
    private OrderAuditMessageProducer orderAuditMessageProducer;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private InstallHelper installHelper;

    @Override
    @ZeebeFlow("ISSUED_TASK")
    public OrderInfoExt sentTask(OrderInfoExt orderInfoExt) {

        String orderNo = orderInfoExt.getOrderNo();
        if (orderNo == null) {
            throw BusinessException.fail("订单号为空");
        }

        OrderInfo orderInfo = orderInfoExt;
        DeliveryType deliveryType = DeliveryType.getDeliveryType(orderInfo.getDeliveryType());
        OrderType orderType = OrderType.valueOf(orderInfo.getOrderType());


        /**
         *
         * 2019-07-30 09:39:38
         *
         * 生成任务：
         * 不下发售后  都生成任务
         *
         *
         * 下发售后：
         * 宅配 + （销售出库 或  上门取件 或 上门取件退货） ||  CVTE & DOT & YS     下发售后
         * 2019-09-30 增加 CVTE & DOT & YS  下发售后 ，未不改变原来的条件，单独判定了 CVTE 场景
         * 2019-12-4 增加 最后一公里 NET && （上门取件 或 上门取件退货）
         * 2021-04-12 配送方式为快递加送装
         */

        // 下发售后条件：
        boolean sssuedService = OrderStatusCheckHelper.isssuedServiceJudge(orderInfo);

        //zbs:如子单为送装协同订单【配送方式=直配 且 子单商品存在offlineOrderConfirmFlag=16】，则增加子单异步下发CSP，不卡控任务下发WMS和TMS
        //2025.5.22 zbs: 加上 直配 && 送装一体
        boolean offLineOrWmInstallOrder = businessHelper.isOffLineOrder(orderInfo) || installHelper.isWmInstallOrderCheckCsp(orderInfo);
        if (offLineOrWmInstallOrder) {
            log.info("送装协同单，异步下发CSP，不卡控任务下发WMS和TMS，orderNo:{}", orderNo);
        }

        if (sssuedService && !offLineOrWmInstallOrder) {
            log.warn("待网点反馈，,orderNo:{}", orderNo);
            FlowListenerParam.success("如果超时未反馈网点名称请联系马达");
            return orderInfoExt;
        }

        // 网点配送 + 退货入库单 手工决定配送方式
        if(DeliveryType.NET == deliveryType && OrderType.RI == orderType){
            FlowListenerParam.success("请手工选择配送方式");
            return orderInfoExt;
        }

        OrderMq orderMq = new OrderMq();
        orderMq.setOrderNo(orderNo);
        orderMq.setNode(OrderStatus.AUDITED.getKey());
        boolean sent = orderAuditMessageProducer.sent(orderMq);

        if (!sent) {
            throw BusinessException.fail("下发调用队列失败:"+orderNo);
        }

        FlowListenerParam.success("");
        return orderInfoExt;

    }
}




