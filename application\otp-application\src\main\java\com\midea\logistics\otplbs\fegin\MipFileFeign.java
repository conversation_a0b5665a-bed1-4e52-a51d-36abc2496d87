package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.MipFile;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: MipFileFeign
* Author: luoh
* Date: 2020-3-11 12:00:56
* Description:mip文件表 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface MipFileFeign{

    /**
    *
    *
    */
    @RequestMapping(value = "/mipFile", method = RequestMethod.POST)
    JsonResponse create(@RequestBody MipFile mipFile);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipFile/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipFile/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody MipFile mipFile);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/mipFile/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    /**
    *
    *
    */
    @RequestMapping(value = "/mipFiles", method = RequestMethod.POST)
    JsonResponse search(@RequestBody MipFile mipFile);

    @RequestMapping(value = "/mipFile/insertBatch", method = RequestMethod.POST)
    JsonResponse insertBatch(@RequestBody List<MipFile> mipFiles);
}
