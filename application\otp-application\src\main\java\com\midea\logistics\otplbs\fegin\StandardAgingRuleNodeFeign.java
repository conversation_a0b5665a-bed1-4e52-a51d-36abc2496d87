package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.StandardAgingNode;
import com.midea.logistics.otp.order.domain.bean.StandardAgingRuleNode;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ©Copyright ©1968-2023 Midea Group,IT
 *
 * @Description:
 * @FileName: StandardAgingRuleNodeFeign
 * @Author: fengxw26
 * @Date: 2023/3/10 10:08
 */
@FeignClient(value = "logistics-aging-agg-service", contextId = "StandardAgingRuleNodeFeign")
public interface StandardAgingRuleNodeFeign {
    /**
     * 分页查询
     *
     * @param entity
     * @return
     */
    @PostMapping("/custom/standardAgingRuleNode/searchPage")
    JsonResponse searchPage(@RequestBody StandardAgingRuleNode entity);

    /**
     * 批量新增或修改
     *
     * @param entityList
     * @return
     */
    @PostMapping("/custom/standardAgingRuleNode/batchCreateOrUpdate")
    JsonResponse batchCreateOrUpdate(@RequestBody List<StandardAgingRuleNode> entityList);
}
