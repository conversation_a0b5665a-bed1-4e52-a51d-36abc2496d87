package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.midea.logistics.cache.manager.CdCommonMaterialManager;
import com.midea.logistics.cache.manager.CdWarehouseManager;
import com.midea.logistics.domain.mdm.domain.CdCommonMaterial;
import com.midea.logistics.domain.mdm.domain.CdWarehouse;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.service.MaterialGroupService;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.OrderInfoItem;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MaterialGroupServiceImpl implements MaterialGroupService {

    @Autowired
    private CdWarehouseManager cdWarehouseManager;
    @Autowired
    private CdCommonMaterialManager cdCommonMaterialManager;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;

    /**
     * 计费组5 效验
     *
     * @param orderInfoExt
     * @return
     */
    @Override
    //@ZeebeFlow("MATERIAL_GROUP5")
    public OrderInfoExt materialGroup5Verify(OrderInfoExt orderInfoExt) {
        FlowListenerParam.success("通过");
        return orderInfoExt;
    }
//
//    @Override
//    @ZeebeFlow("MATERIAL_GROUP5")
//    public OrderInfoExt materialGroup5Verify(OrderInfoExt orderInfoExt) {
//        FlowListenerParam.success("通过");
//        return orderInfoExt;
//
//
//        OrderInfo orderInfo = orderInfoExt;
//
//        if (orderInfo.getDeliveryType() == null) {
//            throw BusinessException.fail("未完成发货方式解析");
//        }
//        //电商B2C 大电仓库 采购入库校验计费组5
//        boolean b2cPi = OrderType.PI.getKey().equals(orderInfo.getOrderType()) && BusinessMode.B2C.getName().equals(orderInfo.getBusinessMode()) && CommonEnum.NO.getValue().equals(orderInfo.getOutsourceFlag());
//
//        if (!DeliveryType.DOT.getKey().equals(orderInfo.getDeliveryType()) && !b2cPi) {
//            FlowListenerParam.success("非"+DeliveryType.DOT.getValue() + "或者非B2C大电采购入库单,无需验证,通过");
//            return orderInfoExt;
//        }
//
//
//        /**
//         * 判断条件：子单，order_type=PI，bussiness_mode = B2C，outsource_flag = 0，用仓库编码查询MDM的cd_warehouse表——is_2c_wh=1
//         */
//        boolean isVerifyPI = false ;
//        //b2c采购入库单
//        if(b2cPi && DeliveryType.DOT.getKey().equals(orderInfo.getDeliveryType())){
//            CdWarehouse cdWarehouse = cdWarehouseManager.getCdWarehouseCache(orderInfo.getWhCode());
//            if (cdWarehouse == null) {
//                throw BusinessException.fail("通基础数据获取不到对应仓库信息,仓库编码:" + orderInfo.getWhCode());
//            }
//            Integer is2Cwh = cdWarehouse.getIs2cWh() == null ? 0 : cdWarehouse.getIs2cWh().intValue();
//            //B2C采购入库大电商仓
//            isVerifyPI = b2cPi &&  CommonEnum.YES.getValue().equals(is2Cwh);
//        }
//
//        if(!isVerifyPI && !DeliveryType.DOT.getKey().equals(orderInfo.getDeliveryType())){
//            FlowListenerParam.success("无需校验计费组5,通过");
//            return orderInfoExt;
//        }
//
//        List<OrderInfoItem> infoItems = orderInfoExt.getOrderInfoItems();
//        if (infoItems == null) {
//            throw BusinessException.fail("查询订单明细失败");
//        }
//        boolean isPlanOrder = SourceSystem.HANDLE.getKey().equals(orderInfo.getSourceSystem()) && CommonEnum.YES.getValue().equals(orderInfo.getPlanOrderFlag());
//        //模糊订单允许明细为空
//        if(isPlanOrder && CollectionUtils.isEmpty(infoItems)){
//            FlowListenerParam.success("模糊订单明细为空无需校验计费组5");
//            return orderInfoExt;
//        }
//        if (CollectionUtils.isEmpty(infoItems)) {
//            throw BusinessException.fail("订单明细不存在");
//        }
//
//        List<String> errMsgList = new ArrayList<String>();
//
//        infoItems.forEach(i -> {
//            if (StringUtils.isBlank(i.getMaterialGroup5())) {
//
//                // 如果仍然为空去基础数据查
//                if (StringUtils.isBlank(i.getItemCode())) {
//                    throw BusinessException.fail("商品code为空");
//                }
//                CdCommonMaterial cdCommonMaterialCache = cdCommonMaterialManager.getCdCommonMaterialCache(i.getItemCode());
//                if (cdCommonMaterialCache == null) {
//                    throw BusinessException.fail("商品不存在");
//                }
//                if (StringUtils.isBlank(cdCommonMaterialCache.getCdcmMaterialGroup5())) {
//                    errMsgList.add(i.getItemCode());
//                    return;
//                }
//                i.setMaterialGroup5(cdCommonMaterialCache.getCdcmMaterialGroup5());
//            }
//        });
//
//        lmpOrderFlowHelper.updateOrderInfoItem(infoItems);
//        orderInfoExt.setOrderInfoItems(infoItems);
//
//        if(!CollectionUtils.isEmpty(errMsgList)){
//            String errMsg =  String.join(",",errMsgList);
//            throw BusinessException.fail("提示：宅配或者采购入库订单商品:"+errMsg+"计费组5为空，请联系核算主管维护或电商中心联系张明辉、配送中心联系邓刚");
//        }
//
//        Map<String, String> result = new HashMap<>();
//        infoItems.forEach(item -> {
//            result.put(item.getItemCode(), item.getMaterialGroup5());
//        });
//        String s = JSONObject.toJSONString(result);
//        FlowListenerParam.success(s.substring(1,s.length()-1));
//        return orderInfoExt;
//    }
}
