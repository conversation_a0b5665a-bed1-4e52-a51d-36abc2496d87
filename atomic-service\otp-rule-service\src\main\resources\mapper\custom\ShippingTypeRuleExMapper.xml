<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.logistics.otp.rule.mapper.custom.ShippingTypeRuleExMapper">

    <sql id="searchFieldsSql">
        `business_mode` AS businessMode,
        `net_delivery_type` AS netDeliveryType,
        `town_name` AS townName,
        `district_code` AS districtCode,
        `district_name` AS districtName,
        `town_code` AS townCode,
        `province_code` AS provinceCode,
        `city_code` AS cityCode,
        `create_user_code` AS createUserCode,
        `delivery_type` AS deliveryType,
        `order_type` AS orderType,
        `customer_code` AS customerCode,
        `update_time` AS updateTime,
        `remark` AS remark,
        `version` AS version,
        `delete_flag` AS deleteFlag,
        `update_user_code` AS updateUserCode,
        `wh_name` AS whName,
        `wh_code` AS whCode,
        `site_code` AS siteCode,
        `site_name` AS siteName,
        `city_name` AS cityName,
        `create_time` AS createTime,
        `id` AS id,
        `province_name` AS provinceName,
        `item_class` AS itemClass,
        `item_class_name` AS itemClassName,
        `volume` AS volume,
        `weight` AS weight,
        `install_flag` AS installFlag,
        `weight_symbol` AS weightSymbol,
        `delivery_route_type` AS deliveryRouteType,
        `vip_flag` AS vipFlag
    </sql>

    <select id="getShippingTypeRules" resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select
        <include refid="searchFieldsSql"/>
        from shipping_type_rule t
        <include refid="whereFieldsSql"/>
        <if test="orderBy !=null">
            order by ${orderBy}
            <if test="orderByType !=null">
                ${orderByType}
            </if>
        </if>
        limit ${start},${pageSize}
    </select>

    <sql id="whereFieldsSql">
        where delete_flag=0
        <if test="businessMode !=null and businessMode != ''">
            and `business_mode` =#{businessMode}
        </if>
        <if test="townName !=null and townName != ''">
            and `town_name` =#{townName}
        </if>
        <if test="districtCode !=null and districtCode != ''">
            and `district_code` =#{districtCode}
        </if>
        <if test="districtCode == ''">
            and (`district_code` ='' or `district_code` is null)
        </if>
        <if test="districtName !=null and districtName != ''">
            and `district_name` =#{districtName}
        </if>
        <if test="townCode !=null and townCode != ''">
            and `town_code` =#{townCode}
        </if>
        <if test="townCode == ''">
            and (`town_code` ='' or `town_code` is null)
        </if>
        <if test="provinceCode !=null and provinceCode != ''">
            and `province_code` =#{provinceCode}
        </if>
        <if test="provinceCode == ''">
            and (`province_code` ='' or `province_code` is null)
        </if>
        <if test="cityCode !=null and cityCode != ''">
            and `city_code` =#{cityCode}
        </if>
        <if test="cityCode == ''">
            and (`city_code` ='' or `city_code` is null)
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="customerCode == ''">
            and (`customer_code` ='' or `customer_code` is null)
        </if>
        <if test="whName !=null and whName != ''">
            and `wh_name` =#{whName}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="volume !=null and volume != 0">
            and `volume` = #{volume}
        </if>
        <if test="volume !=null and volume == 0">
            and (`volume` is null)
        </if>
        <if test=" volume == null">
            and (`volume` is null)
        </if>
        <if test="weight !=null and weight > 0">
            and (`weight` is not null)
        </if>
        <if test="weight !=null and weight == 0">
            and (`weight` is null)
        </if>
        <if test=" weight == null">
            and (`weight` is null)
        </if>
        <if test="installFlag != null">
            and `install_flag` = #{installFlag}
        </if>
        <if test="installFlag == null">
            and (`install_flag` is null)
        </if>
        <if test="weightSymbol != null and weightSymbol != ''">
            and `weight_symbol` is not null
        </if>
        <if test="weightSymbol == null">
            and (`weight_symbol` is null or `weight_symbol` = '')
        </if>
        <!--<if test="weightSymbol != null and weightSymbol == ''">
            and (`weight_symbol` is null or `weight_symbol` = '')
        </if>-->
        <if test="deliveryRouteType != null and deliveryRouteType != ''">
            and delivery_route_type = #{deliveryRouteType}
        </if>
        <if test="deliveryRouteType == null">
            and (`delivery_route_type` is null or `delivery_route_type` = '')
        </if>
        <if test="vipFlag != null">
            and vip_flag = #{vipFlag}
        </if>
        <if test="vipFlag == null">
            and (`vip_flag` is null)
        </if>
        <choose>
            <when test="whCode !=null and whCode != ''">
                and `wh_code` =#{whCode}
            </when>
            <otherwise>
                <if test="orderType == 'YS'">
                    and ifnull(`wh_code`, '')=''
                </if>
            </otherwise>
        </choose>
        <if test="startTime != null and endTime != null">
            <![CDATA[
                        and `create_time` >= #{startTime} and `create_time` <= #{endTime}
                        ]]>
        </if>
        <if test="id !=null">
            and `id` =#{id}
        </if>
    </sql>

    <sql id="whereFieldsSqlOther">
        <if test="batchSiteCode !=null and batchSiteCode.size()>0">
            and site_code in
            <foreach collection="batchSiteCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="batchWhCode !=null and batchWhCode.size()>0">
            and wh_code in
            <foreach collection="batchWhCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getDeliveryRule" resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select
        <include refid="searchFieldsSql"/>
        from shipping_type_rule
        where delete_flag=0
        <if test="businessMode !=null and businessMode != ''">
            and `business_mode` =#{businessMode}
        </if>
        <if test="districtCode !=null and districtCode != ''">
            and `district_code` =#{districtCode}
        </if>
        <if test="districtCode == ''">
            and (`district_code` ='' or `district_code` is null)
        </if>
        <if test="townCode !=null and townCode != ''">
            and `town_code` =#{townCode}
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="customerCode == ''">
            and (`customer_code` ='' or `customer_code` is null)
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>
        <if test="orderType != 'YS' and whCode == ''">
            and ifnull(`wh_code`, '')=''
        </if>
        <if test="itemClass !=null and itemClass != ''">
            and `item_class` =#{itemClass}
        </if>
        <if test="itemClass == ''">
            and (`item_class` ='' or `item_class` is null)
        </if>
    </select>


    <select id="addressExplainCheckIsExpress" resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select
        <include refid="searchFieldsSql"/>
        from shipping_type_rule
        where delete_flag=0
        and `business_mode` =#{businessMode}
        and `site_code` =#{siteCode}
        and `order_type` =#{orderType}
        and `wh_code` =#{whCode}
    </select>


    <select id="getNewDeliveryRule" resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select
        <include refid="searchFieldsSql"/>
        from shipping_type_rule
        where delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="businessMode !=null and businessMode != ''">
            and `business_mode` =#{businessMode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>
        <if test="whCode == '' or whCode == null">
            and (`wh_code` ='' or `wh_code` is null)
        </if>
        <if test="districtCode !=null and districtCode != ''">
            and `district_code` =#{districtCode}
        </if>
        <if test="districtCode == '' or districtCode == null">
            and (`district_code` ='' or `district_code` is null)
        </if>
        <if test="townCode !=null and townCode != ''">
            and `town_code` =#{townCode}
        </if>
        <if test="townCode == '' or townCode == null ">
            and (`town_code` ='' or `town_code` is null)
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="customerCode == '' or customerCode == null">
            and (`customer_code` ='' or `customer_code` is null)
        </if>
        <if test="itemClass !=null and itemClass != ''">
            and `item_class` =#{itemClass}
        </if>
        <if test="itemClass == '' or itemClass == null">
            and (`item_class` ='' or `item_class` is null)
        </if>
    </select>

    <select id="geRulesByOrderTypeBusinessModeOrderType" resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select
        <include refid="searchFieldsSql"/>
        from shipping_type_rule
        where delete_flag=0
        and `business_mode` =#{businessMode}
        and `site_code` =#{siteCode}
        and `order_type` =#{orderType}
    </select>

    <select id="getNewDeliveryRuleV2" resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select
        <include refid="searchFieldsSql"/>
        from shipping_type_rule
        where delete_flag=0
        <if test="orderType !=null and orderType != ''">
            and `order_type` =#{orderType}
        </if>
        <if test="businessMode !=null and businessMode != ''">
            and `business_mode` =#{businessMode}
        </if>
        <if test="siteCode !=null and siteCode != ''">
            and `site_code` =#{siteCode}
        </if>
        <if test="whCode !=null and whCode != ''">
            and `wh_code` =#{whCode}
        </if>
        <if test="whCode == '' or whCode == null">
            and (`wh_code` ='' or `wh_code` is null)
        </if>
        <if test="provinceCode !=null and provinceCode != ''">
            and `province_code` =#{provinceCode}
        </if>
        <if test="provinceCode == '' or provinceCode == null">
            and (`province_code` ='' or `province_code` is null)
        </if>
        <if test="cityCode !=null and cityCode != ''">
            and `city_code` =#{cityCode}
        </if>
        <if test="cityCode == '' or cityCode == null">
            and (`city_code` ='' or `city_code` is null)
        </if>
        <if test="districtCode !=null and districtCode != ''">
            and `district_code` =#{districtCode}
        </if>
        <if test="districtCode == '' or districtCode == null">
            and (`district_code` ='' or `district_code` is null)
        </if>
        <if test="townCode !=null and townCode != ''">
            and `town_code` =#{townCode}
        </if>
        <if test="townCode == '' or townCode == null ">
            and (`town_code` ='' or `town_code` is null)
        </if>
        <if test="customerCode !=null and customerCode != ''">
            and `customer_code` =#{customerCode}
        </if>
        <if test="customerCode == '' or customerCode == null">
            and (`customer_code` ='' or `customer_code` is null)
        </if>
    </select>
    
     <select id="getNewDeliveryRuleV3" resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select * 
         from (
            select
                if(#{volume} is not null and volume >= #{volume}, volume, null) volume_rank,
              concat(
                <!--1、订单类型+业务模式+平台+仓库+省市区镇+客户+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code 
                    and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code 
                    and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--2、订单类型+业务模式+平台+仓库+省市区+客户+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                    and (town_code is null or town_code = '')
             and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--3、订单类型+业务模式+平台+仓库+省市+客户+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--4、订单类型+业务模式+平台+仓库+省市区镇+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code 
                    and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
                ,
             <!--5、订单类型+业务模式+平台+仓库+省市区+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                    and (town_code is null or town_code = '')
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
                ,
             <!--6、订单类型+业务模式+平台+仓库+省市+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
                ,
             <!--7、订单类型+业务模式+平台+仓库+客户+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and (province_code is null or province_code = '')
                    and (city_code is null or city_code = '')
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--8、订单类型+业务模式+平台+仓库+方量-->
                case when #{volume} is not null and #{volume} &lt;= volume 
                    and (province_code is null or province_code = '')
                    and (city_code is null or city_code = '')
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
                ,
             <!--9、订单类型+业务模式+平台+仓库+省市区镇+客户-->
                case when  (volume is null or volume = '')
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code 
                    and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code 
                    and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--10、订单类型+业务模式+平台+仓库+省市区+客户-->
                case when  (volume is null or volume = '')
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                    and (town_code is null or town_code = '')
                    and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--11、订单类型+业务模式+平台+仓库+省市+客户-->
                case when  (volume is null or volume = '')
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--12、订单类型+业务模式+平台+仓库+客户-->
                case when  (volume is null or volume = '')
                    and (province_code is null or province_code = '')
                    and (city_code is null or city_code = '')
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and #{customerCode} is not null and #{customerCode} != '' and #{customerCode} = customer_code
                then 1 else 0 end
                ,
             <!--13、订单类型+业务模式+平台+仓库+省市区镇-->
                case when  (volume is null or volume = '')
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code 
                    and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
                ,
             <!--14、订单类型+业务模式+平台+仓库+省市区-->
                case when  (volume is null or volume = '')
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code 
                    and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                    and (town_code is null or town_code = '')
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
                ,
             <!--15、订单类型+业务模式+平台+仓库+省市-->
                case when  (volume is null or volume = '')
                    and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code 
                    and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
             ,
             <!--16、订单类型+业务模式+平台+仓库-->
                case when  (volume is null or volume = '')
                    and (province_code is null or province_code = '')
                    and (city_code is null or city_code = '')
                    and (district_code is null or district_code = '')
                    and (town_code is null or town_code = '')
                    and (customer_code is null or customer_code = '')
                then 1 else 0 end
              ) as ranking,
                <include refid="searchFieldsSql"/>
            from
              logistics_otp_rule.shipping_type_rule
            where
              delete_flag = 0
                and (`install_flag` is null or `install_flag` = '')
                <if test="orderType !=null and orderType != ''">
                    and `order_type` =#{orderType}
                </if>
                <if test="businessMode !=null and businessMode != ''">
                    and `business_mode` =#{businessMode}
                </if>
                <if test="siteCode !=null and siteCode != ''">
                    and `site_code` =#{siteCode}
                </if>
                <if test="whCode !=null and whCode != ''">
                    and `wh_code` =#{whCode}
                </if>
                <if test="whCode == '' or whCode == null">
                    and (`wh_code` ='' or `wh_code` is null)
                </if>
            order by ranking desc,if((#{volume} is null or #{volume} = '') and (volume_rank is null or volume_rank = ''),1,0), volume_rank
            limit 1
         ) t
         where ranking > 0
    </select>
    
    <select id="getDeliveryRuleByInstallFlag"
            resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select *
        from (
            select
                <choose>
                    <when test="installFlag != null and installFlag == 1">
                        <!--送装标识为是的场景-->
                        concat(
                            <!--1、是否高端机+四级地址-->
                            case when #{vipFlag} is not null and #{vipFlag} = vip_flag
                            and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                            then 1 else 0 end
                            ,
                            <!--2、是否高端机+三级地址-->
                            case when #{vipFlag} is not null and  #{vipFlag} = vip_flag
                            and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and (town_code is null or town_code = '')
                            then 1 else 0 end
                            ,
                            <!--3、是否高端机为空+四级地址-->
                            case
                            when vip_flag is null
                            and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                            then 1 else 0 end
                            ,
                            <!--4、是否高端机为空+三级地址-->
                            case 
                            when vip_flag is null
                            and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and (town_code is null or town_code = '')
                            then 1 else 0 end
                        )
                    </when>
                    <otherwise>
                        <!--送装标识为否的场景-->
                        concat(
                            <!--1、订单类型+仓库+是否送装服务标识=否+四级地址+客户-->
                            case when #{customerCode} is not null and #{customerCode} != '' and  #{customerCode} = customer_code
                            and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                            then 1 else 0 end
                            ,
                            <!--2、订单类型+仓库+是否送装服务标识=否+三级地址+客户-->
                            case when #{customerCode} is not null and #{customerCode} != '' and  #{customerCode} = customer_code
                            and #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and (town_code is null or town_code = '')
                            then 1 else 0 end
                            ,
                            <!--3、订单类型+仓库+是否送装服务标识=否+地址为空+客户-->
                            case when #{customerCode} is not null and #{customerCode} != '' and  #{customerCode} = customer_code
                            and (province_code is null or province_code = '')
                            and (city_code is null or city_code = '')
                            and (district_code is null or district_code = '')
                            and (town_code is null or town_code = '')
                            then 1 else 0 end
                            ,
                            <!--4、订单类型+仓库+送装服务标识=否+4级地址+客户为空+是否外网=外网+重量+符号-->
                            case when #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'THIRD'
                            and case 
                                    when weight_symbol = '&lt;=' then #{weight} &lt;= weight
                                    when weight_symbol = '&lt;' then #{weight} &lt; weight
                                    when weight_symbol = '>=' then #{weight} >= weight
                                    when weight_symbol = '>' then #{weight} > weight
                                    when weight_symbol = '=' then #{weight} = weight
                                    else false end
                            then 1 else 0 end
                            ,
                            <!--5、订单类型+仓库+送装服务标识=否+3级地址+客户为空+是否外网=外网+重量+符号-->
                            case when #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and (town_code is null or town_code = '')
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'THIRD'
                            and case 
                                    when weight_symbol = '&lt;=' then #{weight} &lt;= weight
                                    when weight_symbol = '&lt;' then #{weight} &lt; weight
                                    when weight_symbol = '>=' then #{weight} >= weight
                                    when weight_symbol = '>' then #{weight} > weight
                                    when weight_symbol = '=' then #{weight} = weight
                                    else false end
                            then 1 else 0 end
                            ,
                            <!--6.订单类型+仓库+送装服务标识=否+4级地址+客户为空+是否外网=内网+重量为空-->
                            case when #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'ANNTO'
                            and (weight is null or weight = '')
                            then 1 else 0 end
                            ,
                            <!--7.订单类型+仓库+送装服务标识=否+4级地址+客户为空+是否外网=外网+重量为空-->
                            case when #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and #{townCode} is not null and #{townCode} != '' and #{townCode} = town_code
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'THIRD'
                            and (weight is null or weight = '')
                            then 1 else 0 end
                            ,
                            <!--8.订单类型+仓库+送装服务标识=否+3级地址+客户为空+是否外网=内网+重量为空-->
                            case when #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and (town_code is null or town_code = '')
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'ANNTO'
                            and (weight is null or weight = '')
                            then 1 else 0 end
                            ,
                            <!--9.订单类型+仓库+送装服务标识=否+3级地址+客户为空+是否外网=外网+重量为空-->
                            case when #{provinceCode} is not null and #{provinceCode} != '' and  #{provinceCode} = province_code
                            and #{cityCode} is not null and #{cityCode} != '' and  #{cityCode} = city_code
                            and #{districtCode} is not null and #{districtCode} != '' and  #{districtCode} = district_code
                            and (town_code is null or town_code = '')
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'THIRD'
                            and (weight is null or weight = '')
                            then 1 else 0 end
                            ,
                            <!--10、订单类型+仓库+送装服务标识=否+地址为空+客户为空+是否外网=内网+重量为空-->
                            case when (province_code is null or province_code = '')
                            and (city_code is null or city_code = '')
                            and (district_code is null or district_code = '')
                            and (town_code is null or town_code = '')
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'ANNTO'
                            and (weight is null or weight = '')
                            then 1 else 0 end
                            ,
                            <!--11.订单类型+仓库+送装服务标识=否+地址为空+客户为空+是否外网=外网+重量为空-->
                            case when (province_code is null or province_code = '')
                            and (city_code is null or city_code = '')
                            and (district_code is null or district_code = '')
                            and (town_code is null or town_code = '')
                            and (customer_code is null or customer_code = '')
                            and delivery_route_type = 'THIRD'
                            and (weight is null or weight = '')
                            then 1 else 0 end
                        )
                    </otherwise>
                </choose> as ranking,
                <include refid="searchFieldsSql"/>
                from
                logistics_otp_rule.shipping_type_rule
                where
                delete_flag = 0
                <if test="orderType !=null and orderType != ''">
                    and `order_type` =#{orderType}
                </if>
                <if test="businessMode !=null and businessMode != ''">
                    and `business_mode` =#{businessMode}
                </if>
                <if test="siteCode !=null and siteCode != ''">
                    and `site_code` =#{siteCode}
                </if>
                <if test="whCode !=null and whCode != ''">
                    and `wh_code` =#{whCode}
                </if>
                <if test="whCode == '' or whCode == null">
                    and (`wh_code` ='' or `wh_code` is null)
                </if>
                <if test="installFlag !=null and installFlag == 1">
                    and `install_flag` = 1
                </if>
                <if test="installFlag !=null and installFlag != 1">
                    and `install_flag` in (0,2)
                </if>
                <if test="customerCode == '' or customerCode == null">
                    and (`customer_code` ='' or `customer_code` is null)
                </if>
                order by ranking desc
                limit 1
        ) t
        where ranking > 0
    </select>

    <select id="selectShippingTypeRuleByIndexCount" resultType="java.lang.Integer">
        select count(*)
        from shipping_type_rule t
        <include refid="com.midea.logistics.otp.rule.mapper.common.ShippingTypeRuleMapper.whereFieldsSql"/>
        <include refid="whereFieldsSqlOther"/>
    </select>

    <select id="selectShippingTypeRuleByIndex"
            resultType="com.midea.logistics.otp.rule.domain.bean.ShippingTypeRule">
        select
        <include refid="com.midea.logistics.otp.rule.mapper.common.ShippingTypeRuleMapper.searchFieldsSql"/>
        from shipping_type_rule t
        <include refid="com.midea.logistics.otp.rule.mapper.common.ShippingTypeRuleMapper.whereFieldsSql"/>
        <include refid="whereFieldsSqlOther"/>
        order by t.update_time desc
        limit ${start},${pageSize}
    </select>

    <update id="updateSiteNameByCode">
        update shipping_type_rule
        set site_name = #{siteName}
        where delete_flag = 0 and site_code = #{siteCode}
    </update>

    <update id="updateWhNameByCode">
        update shipping_type_rule
        set wh_name = #{whName}
        where delete_flag = 0 and wh_code = #{whCode}
    </update>
    
    <update id="updatePlace">
        update shipping_type_rule
        <set>
            `version` = `version` + 1 ,
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `district_code` = #{busCode}
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `town_code` = #{busCode}
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>
    
    <update id="dcReplacePlace">
        update shipping_type_rule
        <set>
            `version` = `version` + 1 ,
            <choose>
                <when test='changeType !=null and changeType == "PROVINCE"'>
                    `province_code` = #{newBusCode},
                    `province_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "CITY"'>
                    `city_code` = #{newBusCode},
                    `city_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "DISTRICT"'>
                    `district_code` = #{newBusCode},
                    `district_name` = #{newBusName},
                </when>
                <when test='changeType !=null and changeType == "TOWN"'>
                    `town_code` = #{newBusCode},
                    `town_name` = #{newBusName},
                </when>
            </choose>
        </set>
        where delete_flag = 0
        <choose>
            <when test='changeType !=null and changeType == "PROVINCE"'>
                and `province_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "CITY"'>
                and `city_code` = #{busCode}
            </when>
            <when test='changeType !=null and changeType == "DISTRICT"'>
                and `district_code` = #{busCode}
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
            </when>
            <when test='changeType !=null and changeType == "TOWN"'>
                and `town_code` = #{busCode}
                and `city_code` = SUBSTRING(#{busCode}, 1, 5)
            </when>
            <otherwise>
                /* 其他情况 */
                and #{busCode} = -1
            </otherwise>
        </choose>
    </update>

</mapper>