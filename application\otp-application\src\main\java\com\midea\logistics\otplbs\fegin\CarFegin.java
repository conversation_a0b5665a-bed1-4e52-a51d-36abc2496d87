package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.ordertask.converged.domain.TaskConvergedRouters;
import com.midea.logistics.otp.task.domain.bean.custom.CarArrivedPostInfo;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 到车登记
 */

@FeignClient(value = "logistics-otp-ordertask-converged-service",url = BaseRouter.LOGISTICS_OTP_ORDERTASK_CONVERGED_SERVICE)
public interface CarFegin {

    /**
     * 保存到车登记信息
     *
     * @return
     */
    @PostMapping(TaskConvergedRouters.CAR_ARRIVED_INFO)
    JsonResponse saveCarArrivedInfo(@RequestBody CarArrivedPostInfo arrivedInfo) ;

}