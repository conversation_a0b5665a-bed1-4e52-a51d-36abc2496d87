package com.midea.logistics.otp.order.common.fegin;

import com.midea.logistics.otp.common.constants.BaseRouter;
import com.midea.logistics.otp.order.common.fegin.fallback.WorkflowFeignFallBack;
import com.midea.logistics.otp.order.domain.bean.WorkflowInstance;
import com.midea.logistics.otp.order.domain.bean.WorkflowNodeInstance;
import com.midea.logistics.otp.order.domain.bean.WorkflowNodeTemplate;
import com.midea.logistics.otp.order.domain.bean.WorkflowTemplate;
import com.midea.logistics.otp.order.domain.dto.WorkflowJobParam;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @description: Demo
 */
@FeignClient(value = "logistics-otp-order-service", fallback = WorkflowFeignFallBack.class, url = BaseRouter.LOGISTICS_OTP_ORDER_SERVICE)
public interface WorkflowFeign {


    /**
     * 查询流程记录
     */
    @GetMapping("/getWorkflowInstance")
    JsonResponse<WorkflowInstance> getWorkflowInstance(@SpringQueryMap WorkflowInstance instance);

    /**
     * 查询流程节点记录
     */
    @GetMapping("/getWorkflowNodeInstance")
    JsonResponse<WorkflowNodeInstance> getWorkflowNodeInstance(@SpringQueryMap WorkflowNodeInstance nodeInstance);


    /**
     * 查询流程节点记录
     */
    @GetMapping("/getWorkflowNodeInstances")
    JsonResponse<List<WorkflowNodeInstance>> getWorkflowNodeInstances(@SpringQueryMap WorkflowNodeInstance nodeInstance);


    @GetMapping("/getEnableWorkflowTemplate")
    JsonResponse<List<WorkflowTemplate>> getEnableWorkflowTemplate();


    @GetMapping("/getEnableWorkflowNodeTemplate")
    JsonResponse<List<WorkflowNodeTemplate>> getEnableWorkflowNodeTemplate();


    @PostMapping("/updateFlowNode")
    JsonResponse<Integer> updateFlowNode(@RequestBody WorkflowNodeInstance instance);



    /**
     * 获取未启动审核的订单
     */
    @GetMapping("/getNotStartVerifyOrders")
    JsonResponse<List<String>> getNotStartVerifyOrders();
    @GetMapping("/getNotContinueVerifyCustomerOrders")
    JsonResponse<List<String>> getNotContinueVerifyCustomerOrders();
    @GetMapping("/getNotContinueVerifyOrders")
    JsonResponse<List<String>> getNotContinueVerifyOrders();


    /**
     * 读取待执行的流程
     */
    @PostMapping("/pickTimeoutFlows")
    JsonResponse<List<WorkflowInstance>> pickTimeoutFlows(@RequestBody WorkflowJobParam param);


}