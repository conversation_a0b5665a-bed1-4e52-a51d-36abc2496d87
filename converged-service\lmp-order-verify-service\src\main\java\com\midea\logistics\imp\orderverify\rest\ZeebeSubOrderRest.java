package com.midea.logistics.imp.orderverify.rest;

import com.google.common.collect.Maps;
import com.midea.logistics.imp.orderverify.helper.LmpOrderFlowHelper;
import com.midea.logistics.imp.orderverify.mq.producer.OrderAgingPushProducer;
import com.midea.logistics.imp.orderverify.service.*;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.order.common.flow.action.EndNodeAction;
import com.midea.logistics.otp.order.common.helper.OrderFlowHelper;
import com.midea.logistics.otp.order.converged.domain.OrderConvergedRouters;
import com.midea.logistics.otp.order.domain.bean.OrderInfo;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeServerRequestDto;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@RestController
public class ZeebeSubOrderRest {
    private Logger logger = LoggerFactory.getLogger(ZeebeSubOrderRest.class);
    @Autowired
    private DeliveryModeAnalysisService deliveryModeAnalysisService;
    @Autowired
    private  ContractVerifyService contractVerifyService;
    @Autowired
    private  AgingParseService agingParseService;
    @Autowired
    private AnalysisOfReceivingUnitService analysisOfReceivingUnitService;
    @Autowired
    private ThirdPartyService thirdPartyService;
    @Autowired
    private CSSOrderIssuedService cssOrderIssuedService;
    @Autowired
    private IOutAreaService iOutAreaService;
    @Autowired
    private ZeebeLmpService zeebeLmpService;
    @Autowired
    private MaterialGroupService materialGroupService;
    @Autowired
    private SyncBmsService syncBmsService;
    @Autowired
    private OrderOutCollabWhService orderOutCollabWhService;
    @Autowired
    private LoadingFeeAnalyzeService loadingFeeAnalyzeService;
    @Autowired
    private AnalysisBusineesFeeService analysisBusineesFee;
    @Autowired
    private LogisticModeService logisticModeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private AnalysisNetRangeService analysisNetRangeService;
    @Autowired
    private  IWaybillNoGenService  iWaybillNoGenService;
    @Autowired
    private  PledgeFlowService pledgeFlowService;
    @Autowired
    private LmpOrderFlowHelper lmpOrderFlowHelper;
    @Autowired
    private IBeforeWarehouseService iBeforeWarehouseService;
    @Autowired
    private ServiceProductService serviceProductService;
    @Autowired
    private PrivacyService privacyService;
    @Autowired
    private PreposeTaskService preposeTaskService;
    @Autowired
    private OrderAgingPushProducer orderAgingPushProducer;
    @Autowired
    private EndNodeAction endNodeAction;
    @Autowired
    private OrderFlowHelper orderFlowHelper;
    @Autowired
    private EcommerceCategoriesAnalysisTag ecommerceCategoriesAnalysisTag;

    /**
     * 触发订单流程 zeebe
     * @param orderNo
     * @return
     */
    @GetMapping("/orderVerifyZeebeTest")
    public JsonResponse orderVerifyZeebe(@RequestParam("orderNo") String orderNo) {
        return zeebeLmpService.orderVerifyZeebe(orderNo);
    }


    /**
     * 触发订单流程带模板id zeebe
     * @param zeebeRequestDto
     * @return
     */
    @GetMapping("/orderVerifyZeebe")
    public JsonResponse orderVerifyZeebe(@RequestBody ZeebeServerRequestDto zeebeRequestDto) {
        return zeebeLmpService.orderVerifyZeebe(zeebeRequestDto);
    }


    @PostMapping("/pledgeCheck")
    public JsonResponse pledgeCheck(@RequestBody OrderInfoExt orderInfo){
        try {
        return JsonResponse.success(pledgeFlowService.pledgeCheck(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("质押，失败原因:"+e.getMessage());
        }
    }

    @PostMapping("/pledgeCheck/byOrderNo")
    public JsonResponse pledgeCheckByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoExt(order.getOrderNo());
        pledgeFlowService.pledgeCheck(orderInfo);
        return JsonResponse.success(null);
    }
    /**
     * @description: 大小电设置
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author:
     * @createtime: 2020/11/18 9:44
     */
    @PostMapping("/setOrderOutCollabWh")
    public JsonResponse setOrderOutCollabWh(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(orderOutCollabWhService.setOrderOutCollabWh(orderInfo));
        }catch (Exception e) {
            throw BusinessException.fail("大小电设置，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/setOrderOutCollabWh/byOrderNo")
    public JsonResponse setOrderOutCollabWhByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(order.getOrderNo());
        orderInfo = orderOutCollabWhService.setOrderOutCollabWh(orderInfo);
        return JsonResponse.success(new HashMap<>());
    }

    /**
     * @description: 检验爆仓
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 9:44
     */
    @PostMapping("/isOutArea")
    public JsonResponse isOutArea(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(iOutAreaService.isOutArea(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("爆仓失败，失败原因:"+e.getMessage());
        }
    }

    @PostMapping("/isOutArea/byOrderNo")
    public JsonResponse isOutAreaByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoExt(order.getOrderNo());
        OrderInfoExt outArea = iOutAreaService.isOutArea(orderInfo);
        return JsonResponse.success(null);
    }
    /**
     * 项目分类解析
     * @param entity
     * @return
     */
    @PostMapping(OrderConvergedRouters.ANALYSIS_PROJECT_CLASSIFY)
    public JsonResponse analysisProjectClassify(@RequestBody OrderInfoExt entity) {
        try {
            return JsonResponse.success(analysisOfReceivingUnitService.analysisProjectClassify(entity));
        } catch (Exception e) {
            throw BusinessException.fail("项目分类解析失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping(OrderConvergedRouters.ANALYSIS_PROJECT_CLASSIFY_BY_ORDERNO)
    public JsonResponse analysisProjectClassifyByOrderNo(@RequestBody OrderInfoExt order) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(order.getOrderNo());
        orderInfo=analysisOfReceivingUnitService.analysisProjectClassify(orderInfo);
        return JsonResponse.success(lmpOrderFlowHelper.getZeebeResponseParam(orderInfo));
    }
    /**
     * 配送方式解析
     * @param entity
     * @return
     */
    @PostMapping("/delivery/mode/analysis")
    public JsonResponse updateDeliveryModeAnalysis(@RequestBody OrderInfoExt entity) {
        OrderInfoExt orderInfoExt = deliveryModeAnalysisService.updateDeliveryModeAnalysis(entity);
        try {
            //2022年1月7日10:34:54 索超 前置仓覆盖区域打标
            iBeforeWarehouseService.setQzWarehAreaFlag(orderInfoExt);
        } catch (Exception e) {
            logger.error("前置仓覆盖区域打标异常:{}",orderInfoExt.getOrderNo(),e);
        }
        try {
            //2024.3.20:zhs:BC一体业务打标
            orderFlowHelper.ecBusinessMarkingAsync(orderInfoExt);
        } catch (Exception e) {
            logger.error("BC一体业务打标异常:{}", orderInfoExt.getOrderNo(), e);
        }
        return JsonResponse.success(orderInfoExt);
    }

    @PostMapping("/delivery/mode/analysis/byOrderNo")
    public JsonResponse updateDeliveryModeAnalysisByOrderNo(@RequestBody OrderInfoExt order) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoExt(order.getOrderNo());
        OrderInfoExt orderInfoExt = deliveryModeAnalysisService.updateDeliveryModeAnalysis(orderInfo);
        try {
            //2022年1月7日10:34:54 索超 前置仓覆盖区域打标
            iBeforeWarehouseService.setQzWarehAreaFlag(orderInfoExt);
        } catch (Exception e) {
            logger.error("前置仓覆盖区域打标异常:{}",orderInfoExt.getOrderNo(),e);
        }
        try {
            //2024.3.20:zhs:配送方式解析完后，BC一体业务打标
            orderFlowHelper.ecBusinessMarkingAsync(orderInfoExt);
        } catch (Exception e) {
            logger.error("BC一体业务打标异常:{}", orderInfoExt.getOrderNo(), e);
        }
        return JsonResponse.success(lmpOrderFlowHelper.getZeebeResponseParam(orderInfoExt));
    }
    /**
     * 网点解析
     * @param entity
     * @return
     */
    @PostMapping("/converged/analysisNetRange")
    public JsonResponse analysisNetRange(@RequestBody OrderInfoExt entity) {
        return JsonResponse.success(analysisNetRangeService.analysisNetRange(entity));
    }

    @PostMapping("/converged/analysisNetRange/byOrderNo")
    public JsonResponse analysisNetRangeByOrderNo(@RequestBody OrderInfoExt order) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoExt(order.getOrderNo());
        return JsonResponse.success(analysisNetRangeService.analysisNetRange(orderInfo));
    }

    /**
     * @description: 计费组5
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author:
     * @createtime: 2020/11/18 9:44
     */
    @PostMapping("/materialGroup5Verify")
    public JsonResponse materialGroup5Verify(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(materialGroupService.materialGroup5Verify(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("计费组5，失败原因:"+e.getMessage());
        }
    }

    @PostMapping("/materialGroup5Verify/byOrderNo")
    public JsonResponse materialGroup5VerifyByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoExt(order.getOrderNo());
        Map<String, Map> result = new HashMap<>();
        result.put("orderInfo",new HashMap());
        return JsonResponse.success(result);
    }
    /**
     * 第三方解析
     * @param entity
     * @return
     */
    @PostMapping("third/party/analysis")
    public JsonResponse updateThirdParty(@RequestBody OrderInfoExt entity) {
        try {
            return JsonResponse.success(thirdPartyService.setThirdParty(entity));
        } catch (Exception e) {
            throw BusinessException.fail("第三方解析失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("third/party/analysis/byOrderNo")
    public JsonResponse updateThirdPartyByOrderNo(@RequestBody OrderInfoExt order) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(order.getOrderNo());
        orderInfo=thirdPartyService.setThirdParty(orderInfo);
        return JsonResponse.success(lmpOrderFlowHelper.getZeebeResponseParam(orderInfo));
    }
    /**
     * @description: 装卸费规则
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 15:13
     */
    @PostMapping("/loadingFeeAnalyze")
    public JsonResponse loadingFeeAnalyze(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(loadingFeeAnalyzeService.loadingFeeAnalyze(orderInfo, true));
        } catch (Exception e) {
            throw BusinessException.fail("仓间调拨失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/loadingFeeAnalyze/byOrderNo")
    public JsonResponse loadingFeeAnalyzeByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(order.getOrderNo());
        loadingFeeAnalyzeService.loadingFeeAnalyze(orderInfo, true);
        return JsonResponse.success(null);
    }
    /**
     * @description: 计费业务类型解析
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 15:13
     */
    @PostMapping("/analysisBusineesFee")
    public JsonResponse analysisBusineesFee(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(analysisBusineesFee.analysisBusineesFeeConfig(orderInfo, true));
        } catch (Exception e) {
            throw BusinessException.fail("计费业务类型解析失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/analysisBusineesFee/byOrderNo")
    public JsonResponse analysisBusineesFeeByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(order.getOrderNo());
        analysisBusineesFee.analysisBusineesFeeConfig(orderInfo, true);
        return JsonResponse.success(null);
    }
    /**
     * 合同校验
     * @param orderInfoExt
     * @return
     */
    @PostMapping(OrderConvergedRouters.CONTRACT_VERIFICATION)
    public JsonResponse contractVerify(@RequestBody OrderInfoExt orderInfoExt) {
        try {
            return JsonResponse.success(contractVerifyService.contractVerify(orderInfoExt));
        }catch (Exception e){
            throw BusinessException.fail("合同校验失败，失败原因:"+e.getMessage());
        }
    }

    /**
     * 计费相关
     * @param orderInfoExt
     * @return
     */
    @PostMapping("/anyBmsInfo")
    public JsonResponse anyBmsInfo(@RequestBody OrderInfoExt orderInfoExt) {
        try {
            String deliveryType = orderInfoExt.getDeliveryType();
            boolean isMrpJspsOrder = orderFlowHelper.isMrpJspsOrder(orderInfoExt);

            // 计费组解析
            if(StringUtils.isNotBlank(deliveryType) && DeliveryType.isDOTType(deliveryType)) {
                materialGroupService.materialGroup5Verify(orderInfoExt);
            }

            // 装卸费规则 : 1. 配送方式非运输  2. 配送方式是运输时，出入库类型（IN_OUT_TYPE）是 IN || OUT
            String inOutType = orderInfoExt.getInOutType();
            if(!Objects.equals(deliveryType, DeliveryType.YS.getKey())
                || (InOutType.isOnlyIn(inOutType) || InOutType.isOnlyOut(inOutType))) {
                orderInfoExt = loadingFeeAnalyzeService.loadingFeeAnalyze(orderInfoExt, false);
            }

            // 计费业务类型解析
            if(StringUtils.isNotBlank(deliveryType) && !DeliveryType.isZT(deliveryType) || isMrpJspsOrder) {
                orderInfoExt = analysisBusineesFee.analysisBusineesFeeConfig(orderInfoExt, false);
            }

            return JsonResponse.success(contractVerifyService.contractVerify(orderInfoExt));
        }catch (Exception e){
            throw BusinessException.fail("计费相关失败，失败原因:"+e.getMessage());
        }
    }

    @PostMapping(OrderConvergedRouters.CONTRACT_VERIFICATION_BY_ORDERNO)
    public JsonResponse contractVerifyByOrderNo(@RequestBody OrderInfoExt order) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoExt(order.getOrderNo());
        OrderInfoExt orderInfoExt = contractVerifyService.contractVerify(orderInfo);
        return JsonResponse.success(null);
    }
    /**
     * 时效解析
     * @param orderInfoExt
     * @return
     */
    @PostMapping("/agingParse")
    public JsonResponse agingParse(@RequestBody OrderInfoExt orderInfoExt) {
        OrderInfoExt newOrderInfoExt = agingParseService.agingParse(orderInfoExt);
        //时效推送
        if (null != newOrderInfoExt && SourceSystem.isOFC(newOrderInfoExt.getSourceSystem())
            && OrderType.isRIOrder(newOrderInfoExt.getOrderType())
            && ProjectClassifyEnum.isXbShopRi(newOrderInfoExt.getProjectClassify())) {
            orderAgingPushProducer.send(newOrderInfoExt);
        }
        return JsonResponse.success(newOrderInfoExt);
    }

    @PostMapping("/agingParse/byOrderNo")
    public JsonResponse agingParseByOrderNo(@RequestBody OrderInfoExt order) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoExt(order.getOrderNo());
        val map = Maps.newHashMap();
        OrderInfoExt orderInfoExt = agingParseService.agingParse(orderInfo);
        map.put("orderInfo",null);

        return JsonResponse.success(map);
    }
    /**
     * @description: 建单同步计费bms
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author:
     * @createtime: 2020/11/18 9:44
     */
    @PostMapping("/syncBms")
    public JsonResponse syncBms(@RequestBody OrderInfoExt orderInfo){
        return JsonResponse.success(syncBmsService.syncBms(orderInfo));

    }
    @PostMapping("/syncBms/byOrderNo")
    public JsonResponse syncBmsByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(order.getOrderNo());
        syncBmsService.syncBms(orderInfo);
        return JsonResponse.success(new HashMap<>());

    }
    /**
     * 推送查单系统
     * @param orderInfoExt
     * @return
     */
    @PostMapping("/pushLots")
    public JsonResponse pushLots(@RequestBody OrderInfoExt orderInfoExt) {
        OrderInfoExt synclots = syncBmsService.synclotp(orderInfoExt);
        return JsonResponse.success(synclots);
    }


    @PostMapping("/pushLots/byOrderNo")
    public JsonResponse pushLotsByOrderNo(@RequestBody OrderInfoExt orderInfoExt) {
        orderInfoExt =  lmpOrderFlowHelper.getOrderInfoOnly(orderInfoExt.getOrderNo());
        syncBmsService.synclotp(orderInfoExt);
        return JsonResponse.success(null);
    }

    /**
     * ofc异节点：推送查单系统,bms,net,task
     * @param orderInfoExt
     * @return
     */
    @PostMapping("/bmsLotsNetTask")
    public JsonResponse bmsLotsNetTask(@RequestBody OrderInfoExt orderInfoExt) {

        // zeebe流程重审，存在缓存业务类型为空，数据库业务类型有值的情况，为空时查数据库补充
        if(StringUtils.isBlank(orderInfoExt.getBusinessType())){
            OrderInfo orderInfo = lmpOrderFlowHelper.getOrderInfo(orderInfoExt.getOrderNo());
            orderInfoExt.setBusinessType(orderInfo.getBusinessType());
            orderInfoExt.setContractCustomerCode(orderInfo.getContractCustomerCode());
            orderInfoExt.setContractCustomerName(orderInfo.getContractCustomerName());
        }

        // 弘铄 ： 20230506 自提订单可以推 BMS
        if(StringUtils.isNotBlank(orderInfoExt.getDeliveryType()) && StringUtils.isNotEmpty(orderInfoExt.getInvoiceUnitCode())) {
            syncBmsService.syncBms(orderInfoExt);
        }
        syncBmsService.synclotp(orderInfoExt);
        try {
            JsonResponse.success(cssOrderIssuedService.orderIssued(orderInfoExt));
        }catch (Exception e) {
            throw BusinessException.fail("下发末端配送失败，失败原因:" + e.getMessage());
        }
        try {
            return JsonResponse.success(taskService.sentTask(orderInfoExt));
        }catch (Exception e) {
            throw BusinessException.fail("下发mq生成任务，失败原因:" + e.getMessage());
        }
    }


    /**
     * 运作模式
     * @param orderInfoExt
     * @return
     */
    @PostMapping("/ansyLogisticMode")
    public JsonResponse ansyLogisticMode(@RequestBody OrderInfoExt orderInfoExt) {
        try {
            return JsonResponse.success(logisticModeService.ansyLogisticMode(orderInfoExt));
        }catch (Exception e){
            throw BusinessException.fail("更新运作模式，失败原因:"+e.getMessage());
        }
    }

    @PostMapping("/ansyLogisticMode/byOrderNo")
    public JsonResponse ansyLogisticModeByOrderNo(@RequestBody OrderInfoExt orderInfoExt) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(orderInfoExt.getOrderNo());
        logisticModeService.ansyLogisticMode(orderInfo);
        return JsonResponse.success(null);
    }
    /**
     * 下发mq生成任务
     * @param orderInfoExt
     * @return
     */
    @PostMapping("/sentTask")
    public JsonResponse sentTask(@RequestBody OrderInfoExt orderInfoExt) {
        OrderInfoExt infoExt;
        try {
            infoExt = taskService.sentTask(orderInfoExt);
        }catch (Exception e) {
            throw BusinessException.fail("下发mq生成任务，失败原因:" + e.getMessage());
        }
        try {
            if(SourceSystem.isCAINIAO(orderInfoExt.getSourceSystem())){
                endNodeAction.dpSynCN(orderInfoExt.getOrderNo());
            }
        }catch (Exception e){
            throw BusinessException.fail("接单的物流详情同步菜鸟，失败原因:" + e.getMessage());
        }
        return JsonResponse.success(infoExt);
    }

    @PostMapping("/sentTask/byOrderNo")
    public JsonResponse sentTaskByOrderNo(@RequestBody OrderInfoExt orderInfoExt) {
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(orderInfoExt.getOrderNo());
        taskService.sentTask(orderInfo);
        return JsonResponse.success(null);
    }

    /**
     * @description: 下发末端配送
     * @param: [orderInfo]
     * @return: com.mideaframework.core.web.JsonResponse
     * @author: 陈永培
     * @createtime: 2020/11/14 18:14
     */
    @PostMapping("/orderIssued")
    public JsonResponse orderIssued(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(cssOrderIssuedService.orderIssued(orderInfo));
        }catch (Exception e) {
            throw BusinessException.fail("下发末端配送失败，失败原因:" + e.getMessage());
        }
    }

    @PostMapping("/orderIssued/byOrderNo")
    public JsonResponse orderIssuedByOrderNo(@RequestBody OrderInfoExt order){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(order.getOrderNo());
        cssOrderIssuedService.orderIssued(orderInfo);
        return JsonResponse.success(null);

    }

    /**
     * 生成运单号
     * @param orderInfo
     * @return
     */
    @PostMapping("/waybillno/gen")
    public JsonResponse waybillNoGen(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(iWaybillNoGenService.genWaybillNo(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("生成运单号，失败原因:"+e.getMessage());
        }

    }

    @PostMapping("/waybillno/gen/byOrderNo")
    public JsonResponse waybillNoGenByOrderNo(@RequestBody OrderInfoExt orderInfoExt){
        OrderInfoExt orderInfo =  lmpOrderFlowHelper.getOrderInfoOnly(orderInfoExt.getOrderNo());
        iWaybillNoGenService.genWaybillNo(orderInfo);
        return JsonResponse.success(null);

    }

    /**
     * 解析服务产品
     * @param orderInfo
     * @return
         */
    @PostMapping("/explainServiceProduct")
    public JsonResponse explainServiceProduct(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(serviceProductService.explainServiceProduct(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("解析服务产品，失败原因:"+e.getMessage());
        }

    }

    /**
     * 绑定虚拟号
     * @param orderInfo
     * @return
     */
    @PostMapping("/bindingVirtualPhone")
    public JsonResponse bindingVirtualPhone(@RequestBody OrderInfoExt orderInfo){
        try {
            return JsonResponse.success(privacyService.bindingVirtualPhone(orderInfo));
        }catch (Exception e){
            throw BusinessException.fail("绑定虚拟号，失败原因:"+e.getMessage());
        }
    }

    /**
     * 前分仓任务推送集拼池
     * @param orderInfoExt
     * @return
     */
    @Deprecated
    @PostMapping("/pushAssamPreposeTask")
    public JsonResponse pushAssamPreposeTask(@RequestBody OrderInfoExt orderInfoExt) {
        try {
            return JsonResponse.success(preposeTaskService.generatePreposeTaskByTask(orderInfoExt));
        }catch (Exception e){
            throw BusinessException.fail("前分仓任务创建集拼任务失败，失败原因:"+e.getMessage());
        }
    }

    /**
     * 电商分类解析
     */
    @PostMapping("/ecommerceCategoriesAnalysisTag")
    public JsonResponse ecommerceCategoriesAnalysis(@RequestBody OrderInfoExt orderInfoExt){
        OrderInfoExt orderInfo =  ecommerceCategoriesAnalysisTag.analysis(orderInfoExt);
        return JsonResponse.success(orderInfo);

    }
}
