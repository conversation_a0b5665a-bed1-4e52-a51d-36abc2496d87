<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1nmzx6j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Zeebe Modeler" exporterVersion="0.10.0">
  <bpmn:process id="Process_0ul4aqd-plan" name="子订单流程-模糊订单" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:extensionElements>
        <zeebe:ioMapping>
          <zeebe:output source="=orderInfo" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0s6iwcj</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_14d5skw" name="结束">
      <bpmn:incoming>Flow_13j2a01</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0mzkgmp" name="是否自提" default="Flow_1o3p3mz">
      <bpmn:incoming>Flow_0s6iwcj</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3p3mz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12lfdrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o3p3mz" name="no" sourceRef="Gateway_0mzkgmp" targetRef="Activity_087r4hi" />
    <bpmn:exclusiveGateway id="Gateway_0smudh3" name="是否获取运单号" default="Flow_1e8tnvs">
      <bpmn:incoming>Flow_1c7yf2h</bpmn:incoming>
      <bpmn:incoming>Flow_0og8wf0</bpmn:incoming>
      <bpmn:outgoing>Flow_1e8tnvs</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ss94pu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12lfdrh" name="yes" sourceRef="Gateway_0mzkgmp" targetRef="Gateway_01l4ri7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!=null and orderInfo.deliveryType="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e8tnvs" name="yes" sourceRef="Gateway_0smudh3" targetRef="Activity_1fv890g" />
    <bpmn:sequenceFlow id="Flow_1g901df" sourceRef="Activity_1fv890g" targetRef="Gateway_02vnb2c" />
    <bpmn:exclusiveGateway id="Gateway_02vnb2c" name="非自提" default="Flow_15pwtiu">
      <bpmn:incoming>Flow_1g901df</bpmn:incoming>
      <bpmn:incoming>Flow_0ss94pu</bpmn:incoming>
      <bpmn:outgoing>Flow_1hgo4ci</bpmn:outgoing>
      <bpmn:outgoing>Flow_15pwtiu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hgo4ci" name="yes" sourceRef="Gateway_02vnb2c" targetRef="Activity_0qzipw6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.deliveryType!="ZT") = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15pwtiu" name="no" sourceRef="Gateway_02vnb2c" targetRef="Activity_0f3fs9t" />
    <bpmn:sequenceFlow id="Flow_0n0vsfo" sourceRef="Activity_0qzipw6" targetRef="Activity_0f3fs9t" />
    <bpmn:sequenceFlow id="Flow_01mqa55" sourceRef="Activity_0f3fs9t" targetRef="Activity_195th7r" />
    <bpmn:sequenceFlow id="Flow_13j2a01" sourceRef="Activity_195th7r" targetRef="Event_14d5skw" />
    <bpmn:serviceTask id="Activity_087r4hi" name="配送方式">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="mode" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/delivery/mode/analysis" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3p3mz</bpmn:incoming>
      <bpmn:outgoing>Flow_1gko5jx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1fv890g" name="获取运单号">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="waybill" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/waybillno/gen" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1e8tnvs</bpmn:incoming>
      <bpmn:outgoing>Flow_1g901df</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0qzipw6" name="计费业务类型解析">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="busineesFee" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/analysisBusineesFee" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hgo4ci</bpmn:incoming>
      <bpmn:outgoing>Flow_0n0vsfo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0f3fs9t" name="合同校验">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="verification" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/converged/contractVerification" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_15pwtiu</bpmn:incoming>
      <bpmn:incoming>Flow_0n0vsfo</bpmn:incoming>
      <bpmn:outgoing>Flow_01mqa55</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_195th7r" name="生成任务">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="task" retries="1" />
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/sentTask" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_01mqa55</bpmn:incoming>
      <bpmn:outgoing>Flow_13j2a01</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0s6iwcj" sourceRef="StartEvent_1" targetRef="Gateway_0mzkgmp" />
    <bpmn:sequenceFlow id="Flow_1gko5jx" sourceRef="Activity_087r4hi" targetRef="Gateway_01l4ri7" />
    <bpmn:sequenceFlow id="Flow_0ss94pu" name="no" sourceRef="Gateway_0smudh3" targetRef="Gateway_02vnb2c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=((orderInfo.businessMode="B2B" and orderInfo.deliveryType="ZT") or (orderInfo.businessMode="B2C" and orderInfo.orderType="PO" and orderInfo.deliveryType="ZT") or (orderInfo.orderType="PO" and orderInfo.deliveryType="EXPRESS"))=true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0tvvsvw" name="电商分类解析1">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="http" retries="1" />
        <zeebe:taskHeaders>
          <zeebe:header key="url" value="http://logistics-lmp-order-verify-service/ecommerceCategoriesAnalysisTag" />
          <zeebe:header key="method" value="POST" />
        </zeebe:taskHeaders>
        <zeebe:ioMapping>
          <zeebe:input source="=orderInfo" target="parameterBody" />
          <zeebe:output source="=responseBody" target="orderInfo" />
        </zeebe:ioMapping>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0onl1co</bpmn:incoming>
      <bpmn:outgoing>Flow_1c7yf2h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1c7yf2h" sourceRef="Activity_0tvvsvw" targetRef="Gateway_0smudh3" />
    <bpmn:exclusiveGateway id="Gateway_01l4ri7" default="Flow_0og8wf0">
      <bpmn:incoming>Flow_1gko5jx</bpmn:incoming>
      <bpmn:incoming>Flow_12lfdrh</bpmn:incoming>
      <bpmn:outgoing>Flow_0onl1co</bpmn:outgoing>
      <bpmn:outgoing>Flow_0og8wf0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0onl1co" name="yes" sourceRef="Gateway_01l4ri7" targetRef="Activity_0tvvsvw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=(orderInfo!=null and orderInfo.orderType!=null and (orderInfo.orderType="DP" or orderInfo.orderType="SO")) = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0og8wf0" sourceRef="Gateway_01l4ri7" targetRef="Gateway_0smudh3" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0ul4aqd-plan">
      <bpmndi:BPMNEdge id="Flow_1c7yf2h_di" bpmnElement="Flow_1c7yf2h">
        <di:waypoint x="680" y="177" />
        <di:waypoint x="715" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ss94pu_di" bpmnElement="Flow_0ss94pu">
        <di:waypoint x="765" y="177" />
        <di:waypoint x="915" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="834" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gko5jx_di" bpmnElement="Flow_1gko5jx">
        <di:waypoint x="450" y="177" />
        <di:waypoint x="495" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13j2a01_di" bpmnElement="Flow_13j2a01">
        <di:waypoint x="1380" y="177" />
        <di:waypoint x="1442" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01mqa55_di" bpmnElement="Flow_01mqa55">
        <di:waypoint x="1230" y="177" />
        <di:waypoint x="1280" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n0vsfo_di" bpmnElement="Flow_0n0vsfo">
        <di:waypoint x="1090" y="270" />
        <di:waypoint x="1180" y="270" />
        <di:waypoint x="1180" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15pwtiu_di" bpmnElement="Flow_15pwtiu">
        <di:waypoint x="965" y="177" />
        <di:waypoint x="1130" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="945" y="352" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hgo4ci_di" bpmnElement="Flow_1hgo4ci">
        <di:waypoint x="940" y="202" />
        <di:waypoint x="940" y="270" />
        <di:waypoint x="990" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="947" y="233" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g901df_di" bpmnElement="Flow_1g901df">
        <di:waypoint x="880" y="270" />
        <di:waypoint x="930" y="270" />
        <di:waypoint x="930" y="192" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e8tnvs_di" bpmnElement="Flow_1e8tnvs">
        <di:waypoint x="740" y="202" />
        <di:waypoint x="740" y="270" />
        <di:waypoint x="780" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="747" y="232" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12lfdrh_di" bpmnElement="Flow_12lfdrh">
        <di:waypoint x="280" y="152" />
        <di:waypoint x="280" y="110" />
        <di:waypoint x="500" y="110" />
        <di:waypoint x="500" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="281" y="122" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s6iwcj_di" bpmnElement="Flow_0s6iwcj">
        <di:waypoint x="188" y="177" />
        <di:waypoint x="255" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3p3mz_di" bpmnElement="Flow_1o3p3mz">
        <di:waypoint x="305" y="177" />
        <di:waypoint x="350" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="308" y="159" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0onl1co_di" bpmnElement="Flow_0onl1co">
        <di:waypoint x="545" y="177" />
        <di:waypoint x="580" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="554" y="159" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0og8wf0_di" bpmnElement="Flow_0og8wf0">
        <di:waypoint x="520" y="202" />
        <di:waypoint x="520" y="260" />
        <di:waypoint x="720" y="260" />
        <di:waypoint x="720" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_14d5skw_di" bpmnElement="Event_14d5skw">
        <dc:Bounds x="1442" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1449" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0smudh3_di" bpmnElement="Gateway_0smudh3" isMarkerVisible="true">
        <dc:Bounds x="715" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="726" y="127" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02vnb2c_di" bpmnElement="Gateway_02vnb2c" isMarkerVisible="true">
        <dc:Bounds x="915" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="925" y="122" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13kqxwy_di" bpmnElement="Activity_1fv890g">
        <dc:Bounds x="780" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_023kpgh_di" bpmnElement="Activity_0qzipw6">
        <dc:Bounds x="990" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0h86k46_di" bpmnElement="Activity_0f3fs9t">
        <dc:Bounds x="1130" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1aomcsq_di" bpmnElement="Activity_195th7r">
        <dc:Bounds x="1280" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0tvvsvw_di" bpmnElement="Activity_0tvvsvw">
        <dc:Bounds x="580" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mzkgmp_di" bpmnElement="Gateway_0mzkgmp" isMarkerVisible="true">
        <dc:Bounds x="255" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="267" y="210" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s5tz71_di" bpmnElement="Activity_087r4hi">
        <dc:Bounds x="350" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01l4ri7_di" bpmnElement="Gateway_01l4ri7" isMarkerVisible="true">
        <dc:Bounds x="495" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
