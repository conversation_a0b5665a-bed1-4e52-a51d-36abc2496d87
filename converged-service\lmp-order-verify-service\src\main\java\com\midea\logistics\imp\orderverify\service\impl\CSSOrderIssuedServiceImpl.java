package com.midea.logistics.imp.orderverify.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.midea.logistics.domain.mdm.domain.MdmDataDictionaryDetail;
import com.midea.logistics.imp.orderverify.service.CSSOrderIssuedService;
import com.midea.logistics.otp.common.constants.CommonConstant;
import com.midea.logistics.otp.common.feign.convergedfeign.bop.BopNetFeign;
import com.midea.logistics.otp.common.feign.servicefeign.dispatch.BopFeign;
import com.midea.logistics.otp.common.feign.servicefeign.order.*;
import com.midea.logistics.otp.common.helper.BusinessHelper;
import com.midea.logistics.otp.common.utils.DictHelper;
import com.midea.logistics.otp.common.utils.TransferUtil;
import com.midea.logistics.otp.enums.*;
import com.midea.logistics.otp.lastmile.domain.NetTask;
import com.midea.logistics.otp.lastmile.domain.NetTaskAddress;
import com.midea.logistics.otp.lastmile.domain.NetTaskItem;
import com.midea.logistics.otp.lastmile.domain.NetworkReceipt;
import com.midea.logistics.otp.order.common.fegin.CustomerOrderAddressFeign;
import com.midea.logistics.otp.order.common.fegin.OrderInfoItemFeign;
import com.midea.logistics.otp.order.common.flow.dto.FlowListenerParam;
import com.midea.logistics.otp.order.common.helper.*;
import com.midea.logistics.otp.order.domain.bean.*;
import com.midea.logistics.otp.order.domain.bean.custom.OrderInfoExt;
import com.midea.logistics.otp.order.domain.dto.CustomerOrderInfoExtendConfDto;
import com.midea.logistics.otp.order.domain.dto.IssuedItemExt;
import com.midea.logistics.otp.order.domain.dto.OrderExtendConfDto;
import com.midea.logistics.zeebe.sdk.domain.annotation.ZeebeFlow;
import com.mideaframework.core.constants.BaseCodeEnum;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.utils.ToolUtils;
import com.mideaframework.core.web.JsonResponse;
import com.mideaframework.core.web.OpenJsonResponse;
import com.mideaframework.core.web.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.midea.logistics.otp.enums.OrderStatus.DELIVERED;

/**
 * @Program: logistics-otp
 * @ClassName: CSSOrderIssuedServiceImpl
 * @Author： JiaJun
 * @Date： 2019-06-05 10:22
 */
@Slf4j
@Service
public class CSSOrderIssuedServiceImpl implements CSSOrderIssuedService {
    private static final Set<String> SEARCH_EXTEND_SYS = Sets.newHashSet(SourceSystem.OFC.getKey(), SourceSystem.COP_DIO.getKey(), SourceSystem.MRP.getKey());

    @Autowired
    private CustomerOrderAddressFeign customerOrderAddressFeign;
    @Autowired
    private OrderExtendFeign orderExtendFeign;
    @Autowired
    private OrderInfoItemFeign orderInfoItemFeign;

    @Autowired
    private OrderExceptionFeign orderExceptionFeign;

    @Autowired
    private OrderAgingStationFeign orderAgingStationFeign;

    @Autowired
    private DictHelper dictHelper;
    @Autowired
    private OrderStatusCheckHelper orderStatusCheckHelper;
    @Autowired
    private BopFeign bopFeign;
    @Autowired
    private OrderverifyHelper orderverifyHelper;
    @Autowired
    private CustomerOrderInfoExtendFeign customerOrderInfoExtendFeign;
    @Autowired
    private OrderCodeRelationFeign orderCodeRelationFeign;
    @Autowired
    private CustomerOrderInfoHelper customerOrderInfoHelper;
    @Autowired
    private PrivacyHelper privacyHelper;
    @Autowired
    private BusinessHelper businessHelper;
    @Autowired
    private WithDrawRedisLockHelper withDrawRedisLockHelper;
    @Autowired
    private BopNetFeign bopNetFeign;
    @Autowired
    private OrderLogHelper orderLogHelper;
    @Autowired
    private BusinessControlParamHelper businessControlParamHelper;
    @Autowired
    private CspIssuedHelper cspIssuedHelper;
    @Autowired
    private InstallHelper installHelper;
    /**
     * !@下发网点CSP（中台）
     * 下发售后
     *
     * @param orderInfo
     */
    @Override
    @ZeebeFlow("ISSUED_SERVICE")
    public OrderInfoExt orderIssued(OrderInfoExt orderInfo) {



        CommonConstant.checkOrderInfo(orderInfo);
        String deliveryTypeOld = orderInfo.getDeliveryType();
        String whCodeOld = orderInfo.getWhCode();
//        this.setNetMatching(orderInfo);
        String orderNo = orderInfo.getOrderNo();

        String errorMsg = "";
        boolean sssuedService = OrderStatusCheckHelper.isssuedServiceJudge(orderInfo);

        //zbs:如子单为送装协同订单【配送方式=直配 且 子单商品存在offlineOrderConfirmFlag=16】，则增加子单异步下发CSP，不卡控任务下发WMS和TMS
        //2025.5.22 zbs: 加上 直配 && 送装一体
        boolean offLineOrWmInstallOrder = businessHelper.isOffLineOrder(orderInfo) || installHelper.isWmInstallOrderCheckCsp(orderInfo);
        if (offLineOrWmInstallOrder) {
            log.info("送装协同单，异步下发CSP，不卡控任务下发WMS和TMS，orderNo:{}", orderNo);
        }

        if (!sssuedService && !offLineOrWmInstallOrder) {
            log.warn("订单不需要下发末端配送,orderNo:{}", orderNo);
            FlowListenerParam.success("订单不需要下发末端配送");
            orderInfo.setDeliveryType(deliveryTypeOld);
            return orderInfo;
        }

        //查询客户地址表
        CustomerOrderAddress customerOrderAddress = new CustomerOrderAddress();
        customerOrderAddress.setOrderNo(orderInfo.getParentOrderNo());
        JsonResponse<PageResponse<CustomerOrderAddress>> jsonResponse1 = customerOrderAddressFeign.search(customerOrderAddress);
        if (jsonResponse1 == null || !"0".equals(jsonResponse1.getCode())) {
            log.warn("查询订单地址失败,orderNo:{}", orderNo);
            throw BusinessException.fail("查询订单地址失败");
        }
        if (CollectionUtils.isEmpty(jsonResponse1.data.list)) {
            log.warn("订单地址不存在,orderNo:{}", orderNo);
            throw BusinessException.fail("订单地址不存在");
        }
        customerOrderAddress = jsonResponse1.data.list.get(0);


        //查询订单明细表
        OrderInfoItem orderInfoItem = new OrderInfoItem();
        orderInfoItem.setOrderNo(orderNo);
        JsonResponse<List<OrderInfoItem>> jsonResponse2 = orderInfoItemFeign.list(orderInfoItem);
        if (jsonResponse2 == null) {
            log.warn("查询订单明细失败,orderNo:{}", orderNo);
            throw BusinessException.fail("查询订单明细失败");
        }
        if (jsonResponse2.data == null || !"0".equals(jsonResponse2.getCode())) {
            log.warn("查询订单明细失败,orderNo:{}", orderNo);
            throw BusinessException.fail("查询订单明细失败");
        }
        List<OrderInfoItem> orderInfoItems = jsonResponse2.getData();

        //zhs:项目分类project_classify=JHLP时，增加子单明细表的express_no字段，转化为subWaybillNo放在明细中
        Map<String, CustomerOrderItem> custItemsMapByUplineNo = null;
        if (ProjectClassifyEnum.isJHLP(orderInfo.getProjectClassify())) {
            custItemsMapByUplineNo = customerOrderInfoHelper.getCustomerOrderItemsMapGroupbyUplineNo(orderInfo.getParentOrderNo());
        }
        Map<String, CustomerOrderItem> custItemsMapByItemCodeAndLineNo = null;
        if (SourceSystem.isOFC(orderInfo.getSourceSystem()) || SourceSystem.isMRP(orderInfo.getSourceSystem())) {
            List<CustomerOrderItem> customerOrderItems = customerOrderInfoHelper.getCustomerOrderItemsByOrderNo(orderInfo.getParentOrderNo());
            custItemsMapByItemCodeAndLineNo = customerOrderInfoHelper.getCustItemsMapGroupByItemCodeAndLineNo(customerOrderItems);
        }

        // 处理异常重推下发售后失败
        OrderException orderException = new OrderException();
        BeanUtils.copyProperties(orderInfo, orderException);
        orderException.setExceptionStatus(OrderExceptionStatus.NEW.getKey());
        orderException.setExceptionDesc("下发末端配送失败");
        orderException.setExceptionType(ExceptionType.CCS_ISSUED_FAILED.getKey());
        orderException.setOperateContent(OrderOperateType.ISSUED_SERVICE.getValue());
        orderException.setOperateType(OrderOperateType.ISSUED_SERVICE.getKey());

        NetworkReceipt networkReceipt = new NetworkReceipt();

        JsonResponse<String> response = new JsonResponse<>();
        String code = response.getCode();
        String desc = "CSP:";

        JsonResponse<String> jsonResponseNet = new JsonResponse();
        // 异常重推
        orderException.setOperateType(OrderOperateType.ISSUED_TASK_LASTMILE.getKey());
        orderException.setOperateContent(OrderOperateType.ISSUED_TASK_LASTMILE.getValue());
        orderException.setExceptionDesc(ExceptionType.LASTMILE_ISSUED_FAILED.getValue());
        orderException.setExceptionType(ExceptionType.LASTMILE_ISSUED_FAILED.getKey());

        try {
            BeanUtils.copyProperties(orderInfo, networkReceipt);
            networkReceipt.setOriginalLogisticNo(orderInfo.getWorkOrderNo());
            List<NetTaskItem> netTaskItems = Lists.newArrayList();
            boolean allZeroInstallFlag = businessHelper.judgeAllZeroInstallFlag(orderInfoItems,orderInfo.getSourceSystem());
            for (OrderInfoItem ti : orderInfoItems) {
                NetTaskItem n = new NetTaskItem();
                BeanUtils.copyProperties(ti, n);
                n.setItemLineNo(ti.getItemLineNo().longValue());
                if (allZeroInstallFlag) {
                    n.setIfInstall(CommonConstant.FLAG_NO);
                } else {
                    n.setIfInstall(CommonConstant.trimInt(ti.getIfInstall()));
                }
                if (CommonConstant.INSTALL_NULL_FLAG.equals(ti.getInstallFlag())) {
                    n.setInstallFlag(CommonConstant.FLAG_NO);
                }
                if (null != custItemsMapByUplineNo && custItemsMapByUplineNo.containsKey(n.getUpperLineNo())) {
                    n.setSubWaybillNo(custItemsMapByUplineNo.get(n.getUpperLineNo()).getSubOrderNo());
                }
                if (null != custItemsMapByItemCodeAndLineNo && custItemsMapByItemCodeAndLineNo.containsKey(n.getItemCode() + "_" + n.getItemLineNo())) {
                    CustomerOrderItem custItem = custItemsMapByItemCodeAndLineNo.get(n.getItemCode() + "_" + n.getItemLineNo());
                    if (custItem != null) {
                        if (StringUtils.isNotBlank(custItem.getExtendInfoFields())) {
                            IssuedItemExt issuedItemExt = JSON.parseObject(custItem.getExtendInfoFields(), IssuedItemExt.class);
                            if (issuedItemExt != null) {
                                n.setPlatformSkuId(issuedItemExt.getPlatformSkuId());
                                n.setO2oFlag(issuedItemExt.getO2oFlag());
                                if (issuedItemExt.getOfflineOrderConfirmFlag() != null && issuedItemExt.getOfflineOrderConfirmFlag() == 16) {
                                    networkReceipt.setOfflineOrderConfirmFlag(issuedItemExt.getOfflineOrderConfirmFlag());
                                }
                            }
                        }
                    }
                }
                netTaskItems.add(n);
            }
            networkReceipt.setNetTaskItems(netTaskItems);

            NetTaskAddress netTaskAddress = new NetTaskAddress();
            BeanUtils.copyProperties(customerOrderAddress, netTaskAddress);
            BeanUtils.copyProperties(orderInfo, netTaskAddress);

            OrderExtend orderExtend = new OrderExtend();
            orderExtend.setOrderNo(orderNo);
            JsonResponse<OrderExtend> orderExtendResponse = orderExtendFeign.selectOne(orderExtend);
            orderExtend = orderExtendResponse.getData();
            if (null != orderExtend){
                networkReceipt.setOrderStartTime(orderExtend.getOrderStartTime());
                networkReceipt.setOrderEndTime(orderExtend.getOrderEndTime());
                //菜鸟电商分类
                networkReceipt.setCommerceCategories(orderExtend.getCommerceCategories());
                // 子单扩展表拓展下发字段
                if(StringUtils.isNotBlank(orderExtend.getConfObj())) {
                    OrderExtendConfDto ceDto = JSON.parseObject(orderExtend.getConfObj(), OrderExtendConfDto.class);
                    if (ceDto != null) {
                        networkReceipt.setVipFlag(ceDto.getVipFlag());
                    }
                }
            }



            //2023年8月4日15:31:34 李庐： 判断是否将收货地址下发给CSP,如果为空或者为0的时候，清空townCode和TownName
            Integer issueCspReceiverFlag = customerOrderAddress.getIssueCspReceiverFlag();
            if (null == issueCspReceiverFlag || 0 == issueCspReceiverFlag){
                netTaskAddress.setReceiverTownCode(null);
                netTaskAddress.setReceiverTownName(null);
            }

            networkReceipt.setNetTaskAddress(netTaskAddress);
            networkReceipt.setTonetSiteCode(orderInfo.getSiteCode());
            networkReceipt.setTonetSiteName(orderInfo.getSiteName());
            // 网点分中心 2023年10月31日09:16:13 zhs:不用给了
//                if (StringUtils.isNotBlank(orderInfo.getNetworkCode())) {
//                    NetInfo netInfo = netInfoManager.getNetInfoCache(orderInfo.getNetworkCode());
//                    if (null != netInfo) {
//                        networkReceipt.setNetworkCenterCode(netInfo.getNetCenterCode());
//                        networkReceipt.setNetworkCenterName(netInfo.getNetCenterName());
//                    }
//                }

            //查询仓运服务平台转换参数，匹配是否存在配置, 重置客户信息
            businessControlParamHelper.setTwTransCspCustomer(networkReceipt);

            networkReceipt.setDeliveredVerifyFlag(orderInfo.getDeliveredVerifyFlag());

            //1、下发lastmile时，如果deliveryVerifyFlag=2时，就设置为2；
            // 为1时，要判断是否国美（order_source_platform=10） ，是国美给1，否则给 0；
            if (null != networkReceipt.getDeliveredVerifyFlag() && 1 == networkReceipt.getDeliveredVerifyFlag() && !"10".equals(orderInfo.getOrderSourcePlatform())){
                networkReceipt.setDeliveredVerifyFlag(0);
            }
            //2021-04-12 xzz：配送方式快递加送装转换成宅配下发网点
//                if (isEAD) {
//                    networkReceipt.setDeliveryType(DeliveryType.DOT.getKey());
//                }

            networkReceipt.setDataMap(null);

            // 手工来源 + 服务单
            boolean handleSoFlag = SourceSystem.isHandleSo(orderInfo.getSourceSystem(), orderInfo.getOrderType());
            boolean cnPO = SourceSystem.isCAINIAO(orderInfo.getSourceSystem()) && OrderType.isPOOrder(orderInfo.getOrderType());
            CustomerOrderInfoExtend customerOrderInfoExtend = null;
            CustomerOrderInfoExtendConfDto confDto = null;
            CustomerOrderInfo customerOrderInfo = null;
            if (SEARCH_EXTEND_SYS.contains(orderInfo.getSourceSystem()) || handleSoFlag || cnPO) {
                CustomerOrderInfoExtend extendCondition = new CustomerOrderInfoExtend();
                extendCondition.setOrderNo(orderInfo.getParentOrderNo());
                JsonResponse<List<CustomerOrderInfoExtend>> listJsonResponse = customerOrderInfoExtendFeign.selectByIndex(extendCondition);
                if (null != listJsonResponse && BaseCodeEnum.SUCCESS.getCode().equals(listJsonResponse.getCode())) {
                    List<CustomerOrderInfoExtend> list = listJsonResponse.getData();
                    if (!CollectionUtils.isEmpty(list)) {
                        customerOrderInfoExtend = list.get(0);
                        if (customerOrderInfoExtend != null) {
                            confDto = customerOrderInfoExtend.confObjJsonToDtoNotThrow();
                        }
                    }
                }

                // COP 并且是 SO 查询父单表
                if (SourceSystem.isCOPDIO(orderInfo.getSourceSystem()) && OrderType.isSOOrder(orderInfo.getOrderType())) {
                    customerOrderInfo = customerOrderInfoHelper.getCustomerOrderInfo(orderInfo.getParentOrderNo());
                }
            }

            if(confDto != null){
                //套送编码
                networkReceipt.setCompleteSetNo(confDto.getCompleteSetNo());
            }

            //zhs:OFC下发Net传css鉴定单号
            if (SourceSystem.isOFC(orderInfo.getSourceSystem())) {
                if (customerOrderInfoExtend != null) {
                    networkReceipt.setCssJdOrderNo(customerOrderInfoExtend.getCssJdOrderNo());
                    networkReceipt.setOnOffLineFlag(customerOrderInfoExtend.getOnOffLineFlag());
                    if(ToolUtils.isNotEmpty(confDto)){
                        //2023.10.31：glh：OFC下发传退市机标识、备注拼退市机编码
                        if(ToolUtils.isNotEmpty(confDto.getDelistItemCode())){
                            String remark = networkReceipt.getRemark() != null ? networkReceipt.getRemark() : "";
                            networkReceipt.setRemark(confDto.getDelistItemCode() + "##" + remark);
                            networkReceipt.setDelistFlag(confDto.getDelistFlag());
                        }
                        // 零售通路
                        networkReceipt.setCooperateClassCode(confDto.getCooperateClassCode());
                    }
                }

            }
            //菜鸟预约天数下发csp
            if(cnPO && ToolUtils.isNotEmpty(confDto)){
                networkReceipt.setAppointLimitDays(confDto.getAppointLimitDays());
            }

            if (SourceSystem.isCOPDIO(orderInfo.getSourceSystem()) || handleSoFlag) {
                if (customerOrderInfoExtend != null && OrderDistinctionFlag.isJJ(customerOrderInfoExtend.getOrderDistinctionFlag())) {
                    //gl：家居订单：当上游订单类型为安装服务单（二次上门）时，即：upper_order_type =SECONDVISIT，给CSP的service_type 传110；
                    if (UpperOrderType.isSecondVisit(orderInfo.getUpperOrderType())) {
                        networkReceipt.setServiceType(110);
                    }
                    //家居订单下发CSP时，商品明细生成子运单号：subWaybillNo，生成逻辑：按照上游商品行号+商品上的包件数进行生成，从上游商品行号为1的开始，号码生成规则：originOrderNo_商品包件数
                    createSubWaybillNo(networkReceipt);
                }
                if (confDto != null) {
                    networkReceipt.setInvoiceUnitCode(confDto.getUpperInvoiceUnitCode());
                    networkReceipt.setInvoiceUnitName(confDto.getUpperInvoiceUnitName());
                    networkReceipt.setOldEvcSn(confDto.getOldEvcSn());
                    networkReceipt.setDeviceSpec(confDto.getDeviceSpec());
                    networkReceipt.setComboCode(confDto.getComboCode());
                    networkReceipt.setComboName(confDto.getComboName());
                    networkReceipt.setNewEvcSn(confDto.getNewEvcSn());
                    networkReceipt.setDeviceSn(confDto.getDeviceSn());
                    networkReceipt.setCarType(confDto.getUpperCarType());
                    networkReceipt.setCarCategory(confDto.getUpperCarCategory());
                    networkReceipt.setServiceMethod(confDto.getServiceMethod());
                    networkReceipt.setServiceTimes(confDto.getServiceTimes());
                    networkReceipt.setIsNeedInstall(confDto.getIsNeedInstall());
                    if (StringUtils.isNotBlank(confDto.getWarrantyType())) {
                        networkReceipt.setWarrantyType(Integer.valueOf(confDto.getWarrantyType()));
                    }
                    networkReceipt.setDisclaimers(confDto.getDisclaimers());
                    networkReceipt.setServicePurchasePriceUnit(confDto.getServicePurchasePriceUnit());

                    // 设置提货人信息
                    netTaskAddress.setTakerName(confDto.getTakerName());
                    netTaskAddress.setTakerMobile(confDto.getTakerMobile());
                    netTaskAddress.setTakerAddress(confDto.getTakerAddress());
                    // 将提货人省、市、区、镇、详细地址拼接作为新的详细地址给到CSP
                    String takerDetailAddr = String.join("", confDto.getTakerProvinceName(), confDto.getTakerCityName(),
                        confDto.getTakerDistrictName(), confDto.getTakerTownName(), confDto.getTakerDetailAddr());
                    netTaskAddress.setTakerDetailAddr(takerDetailAddr);
                }
                networkReceipt.setDistributionFlag(CommonEnum.NO.getValue());
                networkReceipt.setThirdFlag(CommonEnum.YES.getValue());
                networkReceipt.setBrand(null);
                networkReceipt.setOrderType(orderInfo.getUpperOrderType());
                networkReceipt.setBusinessType(orderInfo.getBusinessType());
                networkReceipt.setBusinessMode(BusinessMode.B2C.getName());
                networkReceipt.setProjectType(16);
                netTaskAddress.setReceiverTownCode(customerOrderAddress.getReceiverTownCode());
                netTaskAddress.setReceiverTownName(customerOrderAddress.getReceiverTownName());
                netTaskAddress.setReceiverLat(customerOrderAddress.getEndLat());
                netTaskAddress.setReceiverLng(customerOrderAddress.getEndLng());

                // 把COP 给的快递订单号传给 CSP
                if (customerOrderInfo != null) {
                    networkReceipt.setExpressNo(customerOrderInfo.getExpressNo());
                }
            }

            //OrderAging aging = new OrderAging();
            //aging.setOrderNo(orderInfo.getOrderNo());
            //aging.setOrderStatus(OrderStatus.DELIVERED.getKey());
            //List<OrderAging> agings = orderAgingFeign.list(aging).data;
            //if (ToolUtils.isNotEmpty(agings)){
            //    networkReceipt.setConSignMentTime(agings.get(0).getPlanTime());
            //}
    
            // 网点应签收、终端应签收时间
            List<OrderAging> data = orderverifyHelper.getOrderAgingNotThrow(orderNo);
            if (ToolUtils.isNotEmpty(data)) {
                data.stream().forEach(oA -> {
                    if (DELIVERED.getKey().equals(oA.getOrderStatus())) {
                        networkReceipt.setConSignMentTime(oA.getPlanTime());
                    }
                    //2024年9月19日18:27:45 泓铄：新增 最终签收时间给csp https://cf.annto.com/pages/resumedraft.action?draftId=56332769&draftShareId=f16afc3c-6e39-439c-a212-b4c15ceadee2&
                    if (OrderStatus.SIGN.getKey().equals(oA.getOrderStatus())) {
                        networkReceipt.setFinalSignPlanTime(oA.getPlanTime());
                    }
                    if (OrderStatus.NET_SIGN.getKey().equals(oA.getOrderStatus())) {
                        networkReceipt.setNetworkSignPlanTime(oA.getPlanTime());
                    }
                    if (OrderStatus.FEEDER_ARRIVED.getKey().equals(oA.getOrderStatus())) {
                        networkReceipt.setNetworkArrivePlanTime(oA.getPlanTime());
                    }
                    //202411 泓铄：新增取件出库节点
                    if (OrderStatus.RJ_PICK_OUT.getKey().equals(oA.getOrderStatus())) {
                        networkReceipt.setNetworkOutPlanTime(oA.getPlanTime());
                    }
                    //202503 泓铄：新增取件考核时间
                    if (OrderStatus.RJ_PIECKED.getKey().equals(oA.getOrderStatus())) {
                        networkReceipt.setRequiredPickTime(oA.getPlanTime());
                    }
                    //202504 泓铄：新增揽收考核时间 https://cf.annto.com/x/zHmQB
                    if (OrderStatus.COLLECT.getKey().equals(oA.getOrderStatus())) {
                        networkReceipt.setCollectionPlanTime(oA.getPlanTime());
                    }
                });
            }
            

            //零售系统特殊处理
            if (SourceSystem.isMRP(orderInfo.getSourceSystem())) {
                OrderCodeRelation orderCodeRelation = new OrderCodeRelation();
                orderCodeRelation.setOrderNo(orderInfo.getParentOrderNo());
                JsonResponse<OrderCodeRelation> orderCodeRelationJsonResponse = orderCodeRelationFeign.selectOne(orderCodeRelation);
                if (null == orderCodeRelationJsonResponse || !BaseCodeEnum.SUCCESS.getCode().equals(orderCodeRelationJsonResponse.getCode())) {
                    log.error("下发网点时,查询关联表失败" + (null == orderCodeRelationJsonResponse ? "" : orderCodeRelationJsonResponse.getMsg()));
                }
                orderCodeRelation = orderCodeRelationJsonResponse.getData();
                if (null != orderCodeRelation) {
                    networkReceipt.setTenantCode(orderCodeRelation.getShardingKey());
                }
            }

            //虚拟号
            VirtualPhoneInfo virtualPhoneInfo = privacyHelper.getVirtualPhoneInfoByOrderNo(orderInfo.getParentOrderNo());
            if(virtualPhoneInfo != null && businessHelper.isPrivacyPhone(networkReceipt)){
                networkReceipt.setSecretNoX(virtualPhoneInfo.getVirtualNum());
                networkReceipt.setSubsId(virtualPhoneInfo.getSubsId());
                networkReceipt.setPhoneEndTime(virtualPhoneInfo.getValidTime());
            }

            //同步产品信息
            CustomerOrderProduct customerOrderProduct = new CustomerOrderProduct();
            customerOrderProduct.setParentOrderNo(orderInfo.getParentOrderNo());
            customerOrderProduct = customerOrderInfoHelper.getCustomerOrderProduct(customerOrderProduct);
            if (null != customerOrderProduct) {
                networkReceipt.setProductCode(customerOrderProduct.getProductCode());
                networkReceipt.setProductName(customerOrderProduct.getProductName());
                networkReceipt.setIsStandardProduct(customerOrderProduct.getIsStandardProduct());
                networkReceipt.setBillSystem(customerOrderProduct.getBillSystem());
            }
//gl:来源平台为唯品会自营的订单，在下发TMS、CSP时，校验任务的仓库是否在业务控制参数唯品会计费转换（VIPTRANSWH）中，若在，则下发TMS、CSP的客户名称和编码，需转换为数据字典：唯品会客户（VIPCUSTOMER）中配置的客户
            if (businessHelper.isVipzyTransWh(orderInfo)) {
                List<MdmDataDictionaryDetail> vipCustomerDcList = dictHelper.getDict(CommonConstant.VIP_CUSTOMER);
                if (!CollectionUtils.isEmpty(vipCustomerDcList)) {
                    MdmDataDictionaryDetail vipCustomerDc = vipCustomerDcList.get(0);
                    networkReceipt.setCustomerCode(vipCustomerDc.getCode());
                    networkReceipt.setCustomerName(vipCustomerDc.getName());
                    networkReceipt.setContractCustomerCode(vipCustomerDc.getCode());
                    networkReceipt.setContractCustomerName(vipCustomerDc.getName());
                }
            }
            //高端品牌标识
            networkReceipt.setVipService(businessHelper.getVipService(orderInfo.getParentOrderNo(), customerOrderInfoExtend));

            //构建仓库信息
            cspIssuedHelper.buildWarehouseInfo(networkReceipt, orderInfo, customerOrderAddress);

            //下发前检测锁
            checkLock(orderNo, orderInfo);

            jsonResponseNet = bopFeign.networkReceipt(networkReceipt);
            log.info("中台——》下发最后一公里网点结果:{},{},{}", orderNo, jsonResponseNet.getCode(), JSON.toJSONStringWithDateFormat(networkReceipt, JSON.DEFFAULT_DATE_FORMAT));
            if (null == jsonResponseNet || !BaseCodeEnum.SUCCESS.getCode().equals(jsonResponseNet.getCode()) || null == jsonResponseNet.getData()) {
                jsonResponseNet =  new JsonResponse().build(BaseCodeEnum.FAILED.getCode(),"调用下发网点鹊桥接口失败:" + (null == jsonResponseNet ? "" : jsonResponseNet.getMsg()));
            } else {
                jsonResponseNet = JSON.parseObject(jsonResponseNet.getData(), JsonResponse.class);
            }



        } catch (Exception e) {
            jsonResponseNet.setCode(BaseCodeEnum.FAILED.getCode());
            jsonResponseNet.setErrMsg(e.getMessage());
            jsonResponseNet.setMsg(OrderOperateType.ISSUED_TASK_LASTMILE.getValue());
            orderException.setMessage(JSON.toJSONString(networkReceipt));
            errorMsg = " ： " + e.getMessage();
            //异常插入
            log.error("{}下发最后一公里网点异常：{}", orderNo, e);
        }

        BeanUtils.copyProperties(jsonResponseNet, response);
        code = response.getCode();

        //更新父单状态
        Integer newOrderStatus = OrderStatus.NET_RECEIVED.getKey();
        //若网点接单失败，则变更父单【订单状态】为【网点接单失败-210】
        if (null == response || !BaseCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            newOrderStatus = OrderStatus.TO_NET_FAILE.getKey();
        }
        //offLineOrder、wmInstallOrder 下发网点成功，不用更新成待网点派单
        boolean offLineOrWmInstallOrderSuccess = offLineOrWmInstallOrder && OrderStatus.NET_RECEIVED.getKey().equals(newOrderStatus);
        if(!offLineOrWmInstallOrderSuccess){
            orderStatusCheckHelper.changeCustomerOrderStatus(orderInfo, newOrderStatus ,true);
        }

        if (!BaseCodeEnum.SUCCESS.getCode().equals(code)) {
            String error  = JSON.toJSONString(orderInfo);
            int length = error.length();
            orderException.setExceptionDesc(desc + "下发末端配送失败，" + error.substring(0, length < 100 ? length : 100)  + errorMsg);
            orderException.setMessage(JSON.toJSONString(networkReceipt));
            orderExceptionFeign.create(orderException);

            throw BusinessException.fail(desc + "下发末端配送失败:" + response.getMsg() + "," + response.getData() + errorMsg);
        }

        OpenJsonResponse openJsonResponse = JSON.parseObject(response.data(), OpenJsonResponse.class);
        if (null != openJsonResponse && !"200".equals(openJsonResponse.getCode())) {
            String error = JSON.toJSONString(orderInfo);
            int length = error.length();
            orderException.setExceptionDesc(desc + "下发末端配送失败，" + error.substring(0, length < 100 ? length : 100) + errorMsg);
            orderExceptionFeign.create(orderException);

            log.info("下发末端配送失败,orderNo:{}，openJsonResponse:{}", orderNo, response.data());
            throw BusinessException.fail(desc + "下发末端配送失败:" + response.data() + errorMsg);
        }

        //下发cps，加一个redis锁，取消分仓的时候判断，如果有的话，要去调CSP取消
        withDrawRedisLockHelper.issueToCspLock(orderInfo.getOrderNo());

        //2021年8月18日15:21:58 王涛： 刚好取消和下发并发了（先下发了，后取消），这里需要重新判断一下，如果取消了后，再掉网点取消
        this.reCheckOrderCancelAndDelNet(orderInfo);

        log.info(desc + "下发末端配送成功,orderNo:{}", orderNo);
        FlowListenerParam.success("派工中，请联系送装助理派单");
        orderInfo.setDeliveryType(deliveryTypeOld);
        orderInfo.setWhCode(whCodeOld);
        return orderInfo;
    }

    /**
     * @description: 检查锁
     * @param: [orderNo, orderInfo]
     * @return: void
     * @author: 陈永培
     * @createtime: 2023/3/22 11:01
     */
    private void checkLock(String orderNo, OrderInfo orderInfo) {
        //2021年10月14日15:54:04 加上校验，取消分仓的时候禁止下发
        boolean cancelWareHouseLock = withDrawRedisLockHelper.checkCancelWareHouseLock(orderNo);
        if (cancelWareHouseLock) {
            throw BusinessException.fail("正在取消分仓，下发失败");
        }

        //!@取消订单 - 下发CSP前检测取消
        try {
            if (orderStatusCheckHelper.checkOrderCancelLock(orderInfo,null,OrderOperateType.ISSUED_SERVICE,IssueSystem.CSP.getKey())){
                throw BusinessException.fail("订单已经取消，不需要下发网点");
            }
        } catch (Exception e){
            log.error("ISSUED_SERVICE-> ：orderNo:{},e:{}",orderNo,e);
            throw BusinessException.fail(e.getMessage());
        }

        /**
         * 2021年8月9日11:47:51 王涛 加上取消锁的判断，防止取消了还继续下发
         */
        //boolean orderLock = orderStatusCheckHelper.checkMultipyOrderLock(orderInfo.getParentOrderNo(), orderInfo.getOrderNo());
        //if (orderLock) {
        //    log.warn("订单已经取消，不需要下发售后/最后一公里,orderNo:{}", orderNo);
        //    throw BusinessException.fail("订单已经取消，不需要下发售后/最后一公里");
        //}
    }

    /**
     * @description: 重新检测订单有没有订单锁，有则直接掉网点取消
     * @param: [orderInfo, error, isOk, info]
     * @return: boolean
     * @author: 陈永培
     * @createtime: 2020/8/31 11:13
     */
    private void reCheckOrderCancelAndDelNet(OrderInfo info) {
        /**
         * 2021年8月9日11:47:51 王涛 加上取消锁的判断，防止取消了还继续下发
         */
        boolean orderLock = orderStatusCheckHelper.checkMultipyOrderLock(info.getParentOrderNo(), info.getOrderNo());
        /**
         * 取消分仓了，防止取消了还继续下发
         */
        boolean cancelWareHouseLock = withDrawRedisLockHelper.checkCancelWareHouseLock(info.getOrderNo());

        if (orderLock || cancelWareHouseLock) {
            //这里没关系，等个2s（只有检测到锁才等，为了网点那边顺利的接到单子）
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            //1.调用鹊桥net取消接口
            NetTask t = new NetTask();
            t.setOrderNo(info.getOrderNo());
            t.setOrderStatus(OrderStatus.CANCEL.getKey());
            JsonResponse bopJr = bopNetFeign.netEditStatus(t);
            JsonResponse<String> response = TransferUtil.getJsonResponse(bopJr);
            log.info("reCheckOrderCancelAndDelNet-> customerOrderNo:{},request:{},jsonResponse:{}", info.getCustomerOrderNo(), JSON.toJSONString(t), JSON.toJSONString(response));
            String code = response.getCode();
            String responseData = response.getData();
            //不是返回 SUCCESS
            if (BaseCodeEnum.FAILED.getCode().equals(code)) {
                orderLogHelper.saveLog(info, OrderOperateType.WITHDRAW_NET, CommonConstant.STRING_FLAG_NO, "，" + response.getMsg());
            } else {
                orderLogHelper.saveLog(info, OrderOperateType.WITHDRAW_NET, null, "检测到订单已经取消，取消送装任务");
            }
        }
    }



    public void setNetMatching(OrderInfoExt orderInfo){
        //下发网点，配送（delivery_type='DELIVERY'）
        // 订单判断最后一段order_aging_station.transfer_flag=3
        // 需要下发网点，下发网点的配送方式为网点直配
//        if (!DeliveryType.DELIVERY.getKey().equals(orderInfo.getDeliveryType())){
//            return;
//        }
        OrderAgingStation orderAgingStation = new OrderAgingStation();
        orderAgingStation.setOrderNo(orderInfo.getOrderNo());
        orderAgingStation.setDeleteFlag(0);
        orderAgingStation.setTransferFlag(3);
        JsonResponse<List<OrderAgingStation>> listJsonResponse = orderAgingStationFeign.selectOrderAgingStationByIndex(orderAgingStation);
        log.info("最后一段order_aging_station.transfer_flag===>{}",listJsonResponse);
        List<OrderAgingStation> data = listJsonResponse.data();
        if (ToolUtils.isEmpty(data)){
            return;
        }
        orderAgingStation = data.get(0);
        orderInfo.setDeliveryType(DeliveryType.NET_MATCHING.getKey());
        orderInfo.setWhCode(orderAgingStation.getWhCode());
    }

    /**
     * 生成子运单号
     *
     */
    public void createSubWaybillNo(NetworkReceipt networkReceipt) {
        List<NetTaskItem> netTaskItems = networkReceipt.getNetTaskItems();
        String orderNo = networkReceipt.getOriginOrderNo();
        //根据行号排序
        List<NetTaskItem> list = netTaskItems.stream()
            .sorted((Comparator.comparing(NetTaskItem :: getItemLineNo))).collect(Collectors.toList());

        String str = "";
        int line = 1;
        int num = 1;
        int packageQty;
        BigDecimal pkgQty;
        //生成子订单号
        for (int i = 0; i < list.size(); i++){
            pkgQty = list.get(i).getPkgQty();
            if (pkgQty == null || BigDecimal.ZERO.compareTo(pkgQty) >= 0) {
                packageQty = 1;
            } else {
                packageQty = pkgQty.intValue();
            }
            if(line == list.get(i).getItemLineNo()) {
                for(int j = 0;j < packageQty;j++) {
                    if(StringUtils.isBlank(str)) {
                        str = String.format("%s%s%s",orderNo,"-",num);
                    }else {
                        str = String.format("%s%s%s%s%s",str,",",orderNo,"-",num);
                    }

                    num++;

                }
            }

            list.get(i).setSubWaybillNo(str);

            line++;

            str = "";
        }


    }
}
