package com.midea.logistics.otp.order.common.fegin.fallback;

import com.midea.logistics.otp.order.common.fegin.WorkflowFeign;
import com.mideaframework.core.exception.BusinessException;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2019年10月10日 上午15:28
 */
@Component
@Slf4j
public class WorkflowFeignFallBack implements FallbackFactory<WorkflowFeign> {
    @Override
    public WorkflowFeign create(Throwable throwable) {
        log.error(throwable.getMessage());
        throw new BusinessException(throwable.getMessage());
//        return new WorkflowFeign() {
//            @Override
//            public JsonResponse<WorkflowInstance> getWorkflowInstance(WorkflowInstance instance) {
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
//            }
//
//            @Override
//            public JsonResponse<WorkflowNodeInstance> getWorkflowNodeInstance(WorkflowNodeInstance nodeInstance) {
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
//            }
//
//            @Override
//            public JsonResponse<List<WorkflowNodeInstance>> getWorkflowNodeInstances(WorkflowNodeInstance nodeInstance) {
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
//            }
//
//            @Override
//            public JsonResponse<List<WorkflowTemplate>> getEnableWorkflowTemplate() {
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
//            }
//
//            @Override
//            public JsonResponse<List<WorkflowNodeTemplate>> getEnableWorkflowNodeTemplate() {
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
//            }
//
//            @Override
//            public JsonResponse<Integer> checkMorkflowHistory(String workflowTicket) {
//                JsonResponse jsonResponse = new JsonResponse();
//                jsonResponse.setCode(BaseCodeEnum.FAILED.getCode());
//                jsonResponse.setMsg(throwable.getMessage());
//                return jsonResponse;
//            }
//        };
    }
}
