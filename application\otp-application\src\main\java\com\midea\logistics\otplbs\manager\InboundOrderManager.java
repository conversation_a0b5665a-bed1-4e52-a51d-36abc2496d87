package com.midea.logistics.otplbs.manager;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2019-9-3 19:10
 */
@Component
public class InboundOrderManager {

    private static final Logger logger = LoggerFactory.getLogger(InboundOrderManager.class);

    public void inboundOrderTemplateDownload(HttpServletRequest request, HttpServletResponse response) {
        InputStream fis = null;
        XSSFWorkbook workbook = null;
        ServletOutputStream out = null;
        try {
            String file = "inbound_order_template.xlsx";
            fis = Thread.currentThread().getContextClassLoader().getResourceAsStream("/printTempaltes/inbound_order_template.xlsx");
            workbook = new XSSFWorkbook(fis);
            response.setContentType("application/binary;charset=ISO8859-1");
            String fileName = java.net.URLEncoder.encode(file, "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
        } catch (
            IOException e) {
            logger.info(e.getMessage());
        } finally {
            //关闭文件输出流
            try {
                if(out!=null) {
                    out.close();
                }
                if(fis!=null) {
                    fis.close();
                }
                if(workbook!=null) {
                    workbook.close();
                }
            } catch (IOException e) {
                logger.error("文件流关闭失败"+e.getMessage());
            }

        }
    }
}
