spring.logistics-log.path=./logs
sms.businessDepart=default
lc.metadata.env=sit

spring.rocketmq.messageModel=CLUSTERING
spring.rocketmq.messageListener=CONCURRENTLY
spring.rocketmq.retryAnotherBrokerWhenNotStoreOK=true
spring.rocketmq.producerGroup=LOGISTICS-LMP-ORDER-SERVICE-PRODUCER-GROUP-UAT
spring.rocketmq.consumerGroup=LOGISTICS-LMP-ORDER-SERVICE-CONSUMER-GROUP-UAT
spring.rocketmq.pullBatchSize=2
spring.rocketmq.pullInterval=500

ocr.channel.baidu.appId=24370766
ocr.channel.baidu.apiKey=zYG1d5iQQTXHa6b1ur9LWUEh
ocr.channel.baidu.secretKey=mhneqMN6iln6Gq5bQyTEIbsXOSztGpFO
ocr.channel.porxy.host=*************
ocr.channel.porxy.port=9011