package com.midea.logistics.otp.ttx.mapper.custom;

import com.midea.logistics.otp.ttx.inventory.domain.bean.ConfigDetail;
import com.mideaframework.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ©Copyright ©1968-2018 Midea Group,IT
 * FileName: ConfigDetailMapper
 * Author: caizt
 * Date: 2019-8-23 10:26:21
 * Description:TTX-数据字典
 */
@Mapper
public interface ConfigDetailMapper extends BaseMapper<ConfigDetail> {
    List<ConfigDetail> queryConfigDetailBy(@Param("warehouseCode") String warehouseCode);
    List<String> queryLocationCode(@Param("warehouseCode") String warehouseCode,@Param("codes") List<String> codes);
    Integer queryWarehouseCount(@Param("code") String warehouseCode);
    Integer queryCustomerCount(@Param("code") String code,@Param("warehouseCode") String warehouseCode);
    Integer queryDiffLocationInventoryCount(@Param("warehouseCode") String warehouseCode,@Param("locationCode") String locationCode,@Param("customerCode") String customerCode);
    Integer queryInventoryCustomerCount(@Param("warehouseCode") String warehouseCode,@Param("companyCode") String companyCode,@Param("customerCode") String customerCode,@Param("inventorySts") String inventorySts);
}
