package com.midea.logistics.imp.orderverify.service.impl;

import com.midea.logistics.imp.orderverify.service.OrderZeebeService;
import com.midea.logistics.otp.order.common.fegin.OrderZeebeRelationFeign;
import com.midea.logistics.otp.order.domain.bean.OrderZeebeRelation;
import com.midea.logistics.zeebe.sdk.domain.dto.ZeebeResponeDto;
import com.mideaframework.core.exception.BusinessException;
import com.mideaframework.core.web.JsonResponse;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class OrderZeebeServiceImpl implements OrderZeebeService {

    private static final Logger logger = LoggerFactory.getLogger(OrderZeebeServiceImpl.class);

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OrderZeebeRelationFeign orderZeebeRelationFeign;

    private static final String WORK_FLOW_INSTANCE_KEY = "workflowInstanceKey";
    private static final String ZEEBE_REDIS_PRE = "zeebe:flow:";

    @Override
    public Long saveWorkflowInstanceKey(String orderNo, ZeebeResponeDto event) {
        Long workflowInstanceKey = event.getProcessInstanceKey();
        stringRedisTemplate.opsForValue().set(ZEEBE_REDIS_PRE + orderNo, workflowInstanceKey.toString(),30, TimeUnit.DAYS);
        //数据入库
        OrderZeebeRelation orderZeebeRelation = new OrderZeebeRelation();
        orderZeebeRelation.setOrderNo(orderNo);
        orderZeebeRelation.setWorkflowInstanceKey(workflowInstanceKey);
        //2024年2月20日22:58:20 永培 ： 设置是那个流程的，后面容易查找
        orderZeebeRelation.setRemark(event.getBpmnProcessId());
        JsonResponse jsonResponse = orderZeebeRelationFeign.create(orderZeebeRelation);
        if (jsonResponse == null || !jsonResponse.judgeSuccess()) {
            logger.error("保存订单流程关系失败,orderNo:{},workflowInstanceKey:{}", orderNo, workflowInstanceKey);
        }
        return workflowInstanceKey;
    }

    @Override
    public Long getWorkflowInstanceKey(String orderNo) {
        String workflowInstanceKey = stringRedisTemplate.opsForValue().get(ZEEBE_REDIS_PRE +orderNo);
        if (StringUtils.isBlank(workflowInstanceKey)) {
            OrderZeebeRelation select = new OrderZeebeRelation();
            select.setOrderNo(orderNo);
            select.setOrderBy("create_time");
            select.setOrderByType("desc");
            JsonResponse<List<OrderZeebeRelation>> jsonResponse = orderZeebeRelationFeign.list(select);
            if (jsonResponse != null && jsonResponse.judgeSuccess()) {
                List<OrderZeebeRelation> list = jsonResponse.getData();
                if (!list.isEmpty()) {
                    return list.get(0).getWorkflowInstanceKey();
                }
            }
            throw BusinessException.fail("不存在" + orderNo + "的zeebe流程");
        }
        return Long.valueOf(workflowInstanceKey);
    }
}
