package com.midea.logistics.otplbs.fegin;

import com.midea.logistics.otp.order.domain.bean.ExceptionOrderInfo;
import com.mideaframework.core.web.JsonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* ©Copyright ©1968-2018 Midea Group,IT
* FileName: ExceptionOrderInfoFeign
* Author: lindq2
* Date: 2022-8-31 15:01:11
* Description:异常订单信息表 feign
*/
@FeignClient(value = "logistics-otp-order-service")
public interface ExceptionOrderInfoFeign{

    @RequestMapping(value = "/exceptionOrderInfo", method = RequestMethod.POST)
    JsonResponse create(@RequestBody ExceptionOrderInfo exceptionOrderInfo);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/exceptionOrderInfo/{id}", method = RequestMethod.GET)
    JsonResponse queryByBusinessKey(@PathVariable("id") Long id);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/exceptionOrderInfo/{id}", method = RequestMethod.PUT)
    JsonResponse update(@PathVariable("id") Long id, @RequestBody ExceptionOrderInfo exceptionOrderInfo);

    /**
    * @param id
    * @return
    */
    @RequestMapping(value = "/exceptionOrderInfo/{id}", method = RequestMethod.DELETE)
    JsonResponse deleteByBusinessKey(@PathVariable("id") Long id);

    @RequestMapping(value = "/exceptionOrderInfos", method = RequestMethod.GET)
    JsonResponse search(@SpringQueryMap ExceptionOrderInfo exceptionOrderInfo);

    @PostMapping("/exceptionOrderInfos")
    JsonResponse createBatch(List<ExceptionOrderInfo> exceptionOrderInfos);
}
